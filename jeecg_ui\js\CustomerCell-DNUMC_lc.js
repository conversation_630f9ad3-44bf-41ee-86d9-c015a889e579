import{d as g,ag as r,aq as u,ar as n,k as o,aD as e,G as a,au as m,aB as _,ah as T}from"./vue-vendor-dy9k-Yad.js";import{T as f,u as I}from"./index-BkGZ5fiW.js";import{a3 as x,X as b}from"./antd-vue-vendor-me9YkNVC.js";import{d as h}from"./table-BDFKJhHv.js";import v from"./BasicTable-xCEZpGLb.js";import{a as w}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const R=[{title:"ID",dataIndex:"id",slots:{customRender:"id"}},{title:"头像",dataIndex:"avatar",width:100,slots:{customRender:"avatar"}},{title:"分类",dataIndex:"category",width:80,align:"center",defaultHidden:!0,slots:{customRender:"category"}},{title:"姓名",dataIndex:"name",width:120},{title:"图片列表1",dataIndex:"imgArr",helpMessage:["这是简单模式的图片列表","只会显示一张在表格中","但点击可预览多张图片"],width:140,slots:{customRender:"img"}},{title:"照片列表2",dataIndex:"imgs",width:160,slots:{customRender:"imgs"}},{title:"地址",dataIndex:"address"},{title:"编号",dataIndex:"no",slots:{customRender:"no"}},{title:"开始时间",dataIndex:"beginTime"},{title:"结束时间",dataIndex:"endTime"}],k=g({components:{BasicTable:v,TableImg:f,Tag:b,Avatar:x},setup(){const[i]=I({title:"自定义列内容",titleHelpMessage:"表格中所有头像、图片均为mock生成，仅用于演示图片占位",api:h,columns:R,bordered:!0,showTableSetting:!0});return{registerTable:i}}}),y={class:"p-4"};function B(i,C,A,L,D,$){const s=r("Tag"),d=r("Avatar"),p=r("TableImg"),l=r("BasicTable");return n(),u("div",y,[o(l,{onRegister:i.registerTable},{id:e(({record:t})=>[a(" ID: "+m(t.id),1)]),no:e(({record:t})=>[o(s,{color:"green"},{default:e(()=>[a(m(t.no),1)]),_:2},1024)]),bodyCell:e(({column:t,record:c})=>[t.key==="avatar"?(n(),_(d,{key:0,size:60,src:c.avatar},null,8,["src"])):T("",!0)]),img:e(({text:t})=>[o(p,{size:60,simpleShow:!0,imgList:t},null,8,["imgList"])]),imgs:e(({text:t})=>[o(p,{size:60,imgList:t},null,8,["imgList"])]),category:e(({record:t})=>[o(s,{color:"green"},{default:e(()=>[a(m(t.no),1)]),_:2},1024)]),_:1},8,["onRegister"])])}const Ht=w(k,[["render",B]]);export{Ht as default};
