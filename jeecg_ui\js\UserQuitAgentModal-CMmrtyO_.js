var N=Object.defineProperty,k=Object.defineProperties;var A=Object.getOwnPropertyDescriptors;var Y=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var _=(e,o,r)=>o in e?N(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,h=(e,o)=>{for(var r in o||(o={}))I.call(o,r)&&_(e,r,o[r]);if(Y)for(var r of Y(o))R.call(o,r)&&_(e,r,o[r]);return e},B=(e,o)=>k(e,A(o));var w=(e,o,r)=>new Promise((d,a)=>{var g=i=>{try{p(r.next(i))}catch(m){a(m)}},M=i=>{try{p(r.throw(i))}catch(m){a(m)}},p=i=>i.done?d(i.value):Promise.resolve(i.value).then(g,M);p((r=r.apply(e,o)).next())});import{d as D,aB as T,ar as x,aD as P,k as S,u as f,aE as U}from"./vue-vendor-dy9k-Yad.js";import{B as V}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{b}from"./user.data-CLRTqTDz.js";import{f as v,h as C}from"./user.api-mLAlJze4.js";import{i as s}from"./antd-vue-vendor-me9YkNVC.js";import{u as Q}from"./useForm-CgkFTrrO.js";import{ac as j}from"./index-CCWaWN5g.js";import{B as q}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";const E=D({name:"user-quit-agent-modal"}),Kt=D(B(h({},E),{emits:["success","register"],setup(e,{emit:o}){const r=o,[d,{resetFields:a,setFieldsValue:g,validate:M,clearValidate:p,updateSchema:i}]=Q({schemas:b,showActionButtonGroup:!1}),[m,{setModalProps:n,closeModal:y}]=j(t=>w(null,null,function*(){yield a(),n({confirmLoading:!0});let l=t.userId;const u=yield v({userName:t.userName});t=u.result?u.result:t;let c=new Date;t.startTime||(t.startTime=s(c).format("YYYY-MM-DD HH:mm:ss")),t.endTime||(t.endTime=H(c)),yield i([{field:"agentUserName",componentProps:{excludeUserIdList:[l]}}]),yield g(h({},t)),n({confirmLoading:!1})}));function F(){return w(this,null,function*(){try{const t=yield M();n({confirmLoading:!0}),yield C(t),y(),r("success",t.userName)}finally{n({confirmLoading:!1})}})}function H(t){let l=t.getFullYear()+30,u=s(t).format("MM"),c=s(t).format("DD"),L=s(t).format("HH:mm:ss");return s(l+"-"+u+"-"+c+" "+L).format("YYYY-MM-DD HH:mm:ss")}return(t,l)=>(x(),T(f(V),U(t.$attrs,{onRegister:f(m),width:800,title:"离职交接",onOk:F}),{default:P(()=>[S(f(q),{onRegister:f(d)},null,8,["onRegister"])]),_:1},16,["onRegister"]))}}));export{Kt as default};
