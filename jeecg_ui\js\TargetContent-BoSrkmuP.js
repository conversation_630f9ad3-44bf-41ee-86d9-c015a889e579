import{d as s,ag as e,aB as p,ar as c,aD as a,k as d,at as m}from"./vue-vendor-dy9k-Yad.js";import{J as o}from"./antd-vue-vendor-me9YkNVC.js";import{a as f}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const i=s({components:{CardMeta:o.Meta,Card:o},setup(){return{}}});function l(_,t,C,u,x,g){const r=e("CardMeta"),n=e("Card");return c(),p(n,{hoverable:"",style:{width:"240px",background:"#fff"}},{cover:a(()=>t[0]||(t[0]=[m("img",{alt:"example",src:"https://os.alipayobjects.com/rmsportal/QBnOOoLaAfKPirc.png"},null,-1)])),default:a(()=>[d(r,{title:"懒加载组件"})]),_:1})}const b=f(i,[["render",l]]);export{b as default};
