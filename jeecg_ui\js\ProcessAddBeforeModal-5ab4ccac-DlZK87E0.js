import{f as u,ag as v,aq as P,ar as w,k as h,aD as k}from"./vue-vendor-dy9k-Yad.js";import{I as C}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{B as x}from"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import{f as B,h as M,l as F,o as j,d as _,c as I}from"./api-0389a176-CuvsoQ7H.js";import{s as R}from"./_plugin-vue_export-helper-dad06003-lGy7RumW.js";import{u as S}from"./useForm-CgkFTrrO.js";import{ac as A}from"./index-CCWaWN5g.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./_commonjsHelpers-ce4d82cc-RqGMvybJ.js";var D=Object.defineProperty,E=Object.defineProperties,L=Object.getOwnPropertyDescriptors,y=Object.getOwnPropertySymbols,U=Object.prototype.hasOwnProperty,q=Object.prototype.propertyIsEnumerable,g=(e,o,t)=>o in e?D(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,G=(e,o)=>{for(var t in o||(o={}))U.call(o,t)&&g(e,t,o[t]);if(y)for(var t of y(o))q.call(o,t)&&g(e,t,o[t]);return e},H=(e,o)=>E(e,L(o)),f=(e,o,t)=>new Promise((r,l)=>{var s=i=>{try{n(t.next(i))}catch(m){l(m)}},p=i=>{try{n(t.throw(i))}catch(m){l(m)}},n=i=>i.done?r(i.value):Promise.resolve(i.value).then(s,p);n((t=t.apply(e,o)).next())});const Ye={list:e=>j(e),delete:e=>F(e),batchDelete:e=>M(e)},J={updateProcess:e=>B(e),saveOrUpdate(e,o=!1,t){return f(this,null,function*(){if(o)return yield _(e,t),e;{const r=yield I(e,t);return H(G({},e),{id:r})}})}},N=[{label:"流程名称",field:"name",required:!0,component:"Input",componentProps:{showCount:!0,maxlength:30,placeholder:"请输入流程名称"}},{label:"流程图标",field:"icon",component:"JImageUpload"},{label:"描述",field:"descr",component:"InputTextArea",componentProps:{maxlength:255,showCount:!0,placeholder:"请输入工作流描述"}}],T={name:"ProcessAddBeforeModal",components:{BasicForm:x,BasicModal:C},emits:["ok","register"],setup(e,{emit:o}){const t=u("创建流程"),r=u(!1),l=u({}),[s,{resetFields:p,setFieldsValue:n,validate:i}]=S({schemas:N,showActionButtonGroup:!1,layout:"vertical",wrapperCol:{span:24}}),[m,{closeModal:d,setModalProps:c}]=A(a=>f(this,null,function*(){yield p(),c({confirmLoading:!1}),r.value=!!(a!=null&&a.isUpdate),r.value?t.value="修改工作流":t.value="创建工作流",c({minHeight:400,bodyStyle:{padding:"10px"}}),l.value=(a==null?void 0:a.record)||{},n(l.value)}));function b(){return f(this,null,function*(){try{c({confirmLoading:!0});let a=yield i();r.value?(a.id=l.value.id,yield J.updateProcess(a),d(),o("ok",a)):(d(),o("ok",a))}finally{c({confirmLoading:!1})}})}function O(){d()}return{registerModal:m,registerForm:s,title:t,handleOk:b,handleCancel:O}}},V={class:"p-2"};function z(e,o,t,r,l,s){const p=v("BasicForm"),n=v("BasicModal");return w(),P("div",V,[h(n,{destroyOnClose:"",onRegister:r.registerModal,canFullscreen:!1,width:"600px",title:r.title,onOk:r.handleOk,onCancel:r.handleCancel},{default:k(()=>[h(p,{onRegister:r.registerForm},null,8,["onRegister"])]),_:1},8,["onRegister","title","onOk","onCancel"])])}const K=R(T,[["render",z],["__scopeId","data-v-591ef727"]]),Ze=Object.freeze(Object.defineProperty({__proto__:null,default:K},Symbol.toStringTag,{value:"Module"}));export{K as P,Ze as a,J as f,Ye as p};
