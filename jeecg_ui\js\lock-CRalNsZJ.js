var k=(t,e,s)=>new Promise((l,i)=>{var a=o=>{try{c(s.next(o))}catch(n){i(n)}},I=o=>{try{c(s.throw(o))}catch(n){i(n)}},c=o=>o.done?l(o.value):Promise.resolve(o.value).then(a,I);c((s=s.apply(t,e)).next())});import{b7 as L,b8 as r,b9 as f}from"./index-CCWaWN5g.js";const p=L({id:"app-lock",state:()=>({lockInfo:r.getLocal(f)}),getters:{getLockInfo(){return this.lockInfo}},actions:{setLockInfo(t){this.lockInfo=Object.assign({},this.lockInfo,t),r.setLocal(f,this.lockInfo,!0)},resetLockInfo(){r.removeLocal(f,!0),this.lockInfo=null},unLock(t){return k(this,null,function*(){var e;if(((e=this.lockInfo)==null?void 0:e.pwd)===t)return this.resetLockInfo(),!0})}}});export{p as u};
