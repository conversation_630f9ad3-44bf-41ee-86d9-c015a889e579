import{d as P,ag as r,aB as X,ar as S,aD as e,at as m,k as o,G as l}from"./vue-vendor-dy9k-Yad.js";import{u as D}from"./index-BkGZ5fiW.js";import{P as N}from"./index-CtJ0w2CP.js";import{a4 as s,a0 as _,K as d,Y as V,J as g,V as y}from"./antd-vue-vendor-me9YkNVC.js";import{refundTimeTableData as Y,refundTimeTableSchema as $}from"./data-BGUZ7pat.js";import w from"./BasicTable-xCEZpGLb.js";import{a as E}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const I=P({components:{BasicTable:w,PageWrapper:N,[y.name]:y,[g.name]:g,Empty:V,[d.name]:d,[d.Item.name]:d.Item,[_.name]:_,[_.Step.name]:_.Step,[s.name]:s,[s.TabPane.name]:s.TabPane},setup(){const[u]=D({title:"退货进度",columns:$,pagination:!1,dataSource:Y,showIndexColumn:!1,scroll:{y:300}});return{registerTimeTable:u}}}),W={class:"pt-4 m-4 desc-wrap"};function z(u,t,J,K,R,G){const f=r("a-button"),b=r("a-tab-pane"),k=r("a-tabs"),a=r("a-descriptions-item"),i=r("a-descriptions"),p=r("a-step"),B=r("a-steps"),n=r("a-card"),T=r("a-divider"),v=r("Empty"),x=r("BasicTable"),C=r("PageWrapper");return S(),X(C,{title:"单号：234231029431",contentBackground:""},{extra:e(()=>[o(f,null,{default:e(()=>t[0]||(t[0]=[l(" 操作一 ")])),_:1,__:[0]}),o(f,null,{default:e(()=>t[1]||(t[1]=[l(" 操作二 ")])),_:1,__:[1]}),o(f,{type:"primary"},{default:e(()=>t[2]||(t[2]=[l(" 主操作 ")])),_:1,__:[2]})]),footer:e(()=>[o(k,{"default-active-key":"1"},{default:e(()=>[o(b,{key:"1",tab:"详情"}),o(b,{key:"2",tab:"规则"})]),_:1})]),default:e(()=>[m("div",W,[o(i,{size:"small",column:2},{default:e(()=>[o(a,{label:"创建人"},{default:e(()=>t[3]||(t[3]=[l(" 曲丽丽 ")])),_:1,__:[3]}),o(a,{label:"订购产品"},{default:e(()=>t[4]||(t[4]=[l(" XX 服务 ")])),_:1,__:[4]}),o(a,{label:"创建时间"},{default:e(()=>t[5]||(t[5]=[l(" 2017-01-10 ")])),_:1,__:[5]}),o(a,{label:"关联单据"},{default:e(()=>t[6]||(t[6]=[m("a",null,"12421",-1)])),_:1,__:[6]}),o(a,{label:"生效日期"},{default:e(()=>t[7]||(t[7]=[l(" 2017-07-07 ~ 2017-08-08 ")])),_:1,__:[7]}),o(a,{label:"备注"},{default:e(()=>t[8]||(t[8]=[l(" 请于两个工作日内确认 ")])),_:1,__:[8]})]),_:1}),o(n,{title:"流程进度",bordered:!1},{default:e(()=>[o(B,{current:1,"progress-dot":"",size:"small"},{default:e(()=>[o(p,{title:"创建项目"},{description:e(()=>t[9]||(t[9]=[m("div",null,"Jeecg",-1),l(),m("p",null,"2016-12-12 12:32",-1)])),_:1}),o(p,{title:"部门初审"},{description:e(()=>t[10]||(t[10]=[m("p",null,"Chad",-1)])),_:1}),o(p,{title:"财务复核"}),o(p,{title:"完成"})]),_:1})]),_:1}),o(n,{title:"用户信息",bordered:!1,class:"mt-5"},{default:e(()=>[o(i,{column:3},{default:e(()=>[o(a,{label:"用户姓名"},{default:e(()=>t[11]||(t[11]=[l(" 付小小 ")])),_:1,__:[11]}),o(a,{label:"会员卡号"},{default:e(()=>t[12]||(t[12]=[l(" XX 32943898021309809423 ")])),_:1,__:[12]}),o(a,{label:"身份证"},{default:e(()=>t[13]||(t[13]=[l(" 3321944288191034921 ")])),_:1,__:[13]}),o(a,{label:"联系方式"},{default:e(()=>t[14]||(t[14]=[l(" 18100000000 ")])),_:1,__:[14]}),o(a,{label:"联系地址",span:2},{default:e(()=>t[15]||(t[15]=[l(" 曲丽丽 18100000000 浙江省杭州市西湖区黄姑山路工专路交叉路口 ")])),_:1,__:[15]})]),_:1}),o(i,{title:"信息组",column:3},{default:e(()=>[o(a,{label:"某某数据"},{default:e(()=>t[16]||(t[16]=[l(" 111 ")])),_:1,__:[16]}),o(a,{label:"该数据更新时间"},{default:e(()=>t[17]||(t[17]=[l(" 2017-08-08 ")])),_:1,__:[17]}),o(a,{label:"某某数据"},{default:e(()=>t[18]||(t[18]=[l(" 725 ")])),_:1,__:[18]}),o(a,{label:"该数据更新时间"},{default:e(()=>t[19]||(t[19]=[l(" 2017-08-08 ")])),_:1,__:[19]})]),_:1}),t[28]||(t[28]=m("h4",null,"信息组",-1)),o(n,{title:"多层级信息组"},{default:e(()=>[o(i,{title:"组名称",column:3},{default:e(()=>[o(a,{label:"负责人"},{default:e(()=>t[20]||(t[20]=[l(" 林东东 ")])),_:1,__:[20]}),o(a,{label:"角色码"},{default:e(()=>t[21]||(t[21]=[l(" 1234567 ")])),_:1,__:[21]}),o(a,{label:"所属部门"},{default:e(()=>t[22]||(t[22]=[l(" XX公司 - YY部 ")])),_:1,__:[22]}),o(a,{label:"过期时间"},{default:e(()=>t[23]||(t[23]=[l(" 2017-08-08 ")])),_:1,__:[23]}),o(a,{label:"描述",span:2},{default:e(()=>t[24]||(t[24]=[l(" 这段描述很长很长很长很长很长很长很长很长很长很长很长很长很长很长... ")])),_:1,__:[24]})]),_:1}),o(T),o(i,{title:"组名称",column:1},{default:e(()=>[o(a,{label:"学名"},{default:e(()=>t[25]||(t[25]=[l(" Citrullus lanatus (Thunb.) Matsum. et Nakai一年生蔓生藤本；茎、枝粗壮，具明显的棱。卷须较粗.. ")])),_:1,__:[25]})]),_:1}),o(T),o(i,{title:"组名称",column:1},{default:e(()=>[o(a,{label:"负责人"},{default:e(()=>t[26]||(t[26]=[l(" 付小小 ")])),_:1,__:[26]}),o(a,{label:"角色码"},{default:e(()=>t[27]||(t[27]=[l(" 1234568 ")])),_:1,__:[27]})]),_:1})]),_:1})]),_:1,__:[28]}),o(n,{title:"用户近半年来电记录",class:"my-5"},{default:e(()=>[o(v)]),_:1}),o(x,{onRegister:u.registerTimeTable},null,8,["onRegister"])])]),_:1})}const Qt=E(I,[["render",z]]);export{Qt as default};
