var F=(q,D,V)=>new Promise((K,O)=>{var M=v=>{try{x(V.next(v))}catch(p){O(p)}},B=v=>{try{x(V.throw(v))}catch(p){O(p)}},x=v=>v.done?K(v.value):Promise.resolve(v.value).then(M,B);x((V=V.apply(q,D)).next())});import{d as we,r as $,K as Ve,f as h,e as xe,u as l,ag as c,aB as C,ar as s,aD as r,k as n,aq as d,at as z,F as _,aC as b,ah as U,G as y,au as w,aO as te,aE as Re,n as ae}from"./vue-vendor-dy9k-Yad.js";import{u as Ue,B as Be}from"./index-JbqXEynz.js";import{s as Oe}from"./route.api-DBvzaHKw.js";import{bz as L,aO as Ie,aW as T,ao as $e}from"./antd-vue-vendor-me9YkNVC.js";import"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";const ze={key:0},De={key:1},Ke={key:2},Pe={key:3},Ae={class:"btn",style:{"padding-top":"10px"}},He={class:"btn",style:{"padding-top":"10px"}},Ye=we({__name:"RouteModal",emits:["register","success"],setup(q,{emit:D}){const V=D,K=$({xs:{span:24},sm:{span:5}}),O=$({xs:{span:24},sm:{span:16}}),M=Ve(),B=h(!0),x=h(),v=h();let p=$({inputVisible:!1,inputValue:""});const R=h(0),I=h(-1),ne={routerId:[{required:!0,message:"routerId不能为空",trigger:"blur"}],name:[{required:!0,message:"路由名称不能为空",trigger:"blur"}],uri:[{required:!0,message:"uri不能为空",trigger:"blur"}]},S=["Path","Host","Method","After","Before","Between","RemoteAddr"],P=[{key:1,name:"限流过滤器"}],le=h([{name:"Path",args:[]},{name:"Header",args:{header:"",regexp:""}},{name:"Query",args:{param:"",regexp:""}},{name:"Method",args:[]},{name:"Host",args:[]},{name:"Cookie",args:{name:"",regexp:""}},{name:"After",args:[]},{name:"Before",args:[]},{name:"Between",args:[]},{name:"RemoteAddr",args:[]}]),j=h();let u=$({});const[re,{setDrawerProps:A,closeDrawer:ue}]=Ue(e=>F(null,null,function*(){B.value=!!(e!=null&&e.isUpdate),A({confirmLoading:!1}),ie(),l(B)&&(u=Object.assign(u,e.record))})),se=xe(()=>l(B)?"编辑路由":"新增路由");function oe(e,t){let i=e.args.filter(f=>f!==t);e.args=i}function ie(){u=Object.assign(u,{id:"",routerId:"",name:"",uri:"",status:1,predicates:[],filters:[]})}function pe(e){u.predicates.push({args:e.args,name:e.name})}function de(e,t,i){t[i]=e.target.value}function ce(e,t,i,f){R.value=f,I.value=i,p.inputValue=t,ae(()=>{v.value[0].focus()})}function me(e,t){p.inputValue="",p.inputVisible=!0,R.value=t,ae(()=>{x.value[0].focus()})}function E(e){}function J(e,t){e.predicates.splice(t,1)}function fe(e,t){e.args.splice(t,1)}function ge(e,t){e.filters.splice(t,1)}function ve(e){e.args.push({key:"key"+e.args.length+1,value:""})}function _e(e){e.key==0&&u.filters.push({args:[{key:"name",value:"default"},{key:"fallbackUri",value:"forward:/fallback"}],name:"Hystrix",title:P[0].name}),e.key==1&&u.filters.push({args:[{key:"key-resolver",value:"#{@ipKeyResolver}"},{key:"redis-rate-limiter.replenishRate",value:20},{key:"redis-rate-limiter.burstCapacity",value:20}],name:"RequestRateLimiter",title:P[0].name})}function G(e){let t=e.args;const i=p.inputValue;i&&t.indexOf(i)===-1&&(e.args=[...t,p.inputValue]),p.inputVisible=!1,p.inputValue="",I.value=-1,R.value=-1}function Q(e,t,i){p.inputValue&&(e.args[i]=p.inputValue),I.value=-1,R.value=-1}function Ne(){}function ye(){return F(this,null,function*(){yield j.value.validate().then(()=>{try{A({confirmLoading:!0});let e=Object.assign({},u,{predicates:JSON.stringify(u.predicates),filters:JSON.stringify(u.filters)});Oe({router:e}).then(()=>{ue(),V("success")})}finally{A({confirmLoading:!1})}})})}return(e,t)=>{const i=c("a-input"),f=c("a-form-item"),ke=c("a-switch"),H=c("a-divider"),W=c("a-tag"),X=c("a-col"),he=c("a-row"),Y=c("a-menu-item"),Z=c("a-menu"),N=c("a-button"),ee=c("a-dropdown"),Ce=c("a-form-item-rest"),be=c("a-form");return s(),C(l(Be),Re(e.$attrs,{onRegister:l(re),title:se.value,width:"30%",onOk:ye,destroyOnClose:"",showFooter:""}),{default:r(()=>[n(be,{ref_key:"formRef",ref:j,"label-col":K,"wrapper-col":O,model:l(u),rules:ne},{default:r(()=>[n(f,{label:"路由ID",name:"routerId"},{default:r(()=>[n(i,{value:l(u).routerId,"onUpdate:value":t[0]||(t[0]=a=>l(u).routerId=a),placeholder:"路由唯一ID"},null,8,["value"])]),_:1}),n(f,{label:"路由名称",name:"name"},{default:r(()=>[n(i,{value:l(u).name,"onUpdate:value":t[1]||(t[1]=a=>l(u).name=a),placeholder:"路由名称"},null,8,["value"])]),_:1}),n(f,{label:"路由URI",name:"uri"},{default:r(()=>[n(i,{value:l(u).uri,"onUpdate:value":t[2]||(t[2]=a=>l(u).uri=a),placeholder:"路由URL"},null,8,["value"])]),_:1}),n(f,{label:"路由状态",name:"status"},{default:r(()=>[n(ke,{"default-checked":"","checked-value":1,"un-checked-value":0,checked:l(u).status,"onUpdate:checked":t[3]||(t[3]=a=>l(u).status=a)},null,8,["checked"])]),_:1}),n(f,{name:"predicates",label:"路由条件"},{default:r(()=>[(s(!0),d(_,null,b(l(u).predicates,(a,k)=>(s(),d("div",null,[S.includes(a.name)?(s(),d(_,{key:0},[n(H,null,{default:r(()=>[y(w(a.name)+" ",1),n(l(L),{size:"22",onClick:o=>J(l(u),k)},null,8,["onClick"])]),_:2},1024),z("div",null,[(s(!0),d(_,null,b(a.args,(o,m)=>(s(),d(_,null,[m==I.value&&k==R.value?(s(),C(i,{key:0,ref_for:!0,ref_key:"inputRef2",ref:v,type:"text",size:"small",style:{width:"190px"},value:l(p).inputValue,"onUpdate:value":t[4]||(t[4]=g=>l(p).inputValue=g),onChange:E,onBlur:g=>Q(a,o,m),onKeyup:te(g=>Q(a,o,m),["enter"])},null,8,["value","onBlur","onKeyup"])):(s(),C(W,{key:o,style:{"margin-bottom":"2px"},closable:!0,onClose:()=>oe(a,o),onClick:g=>ce(a,o,m,k)},{default:r(()=>[y(w(o),1)]),_:2},1032,["onClose","onClick"]))],64))),256)),l(p).inputVisible&&k==R.value?(s(),C(i,{key:0,ref_for:!0,ref_key:"inputRef",ref:x,type:"text",size:"small",style:{width:"100px"},value:l(p).inputValue,"onUpdate:value":t[5]||(t[5]=o=>l(p).inputValue=o),onChange:E,onBlur:o=>G(a),onKeyup:te(o=>G(a),["enter"])},null,8,["value","onBlur","onKeyup"])):(s(),C(W,{key:1,style:{background:"#fff",borderstyle:"dashed","margin-bottom":"2px"},onClick:o=>me(a,k)},{default:r(()=>[n(l(Ie),{size:"22"}),y(" 新建"+w(a.name),1)]),_:2},1032,["onClick"]))])],64)):U("",!0),S.includes(a.name)?U("",!0):(s(),d(_,{key:1},[n(H,null,{default:r(()=>[y(w(a.name)+" ",1),n(l(L),{size:"22",onClick:o=>J(l(u),k)},null,8,["onClick"])]),_:2},1024),z("div",null,[(s(!0),d(_,null,b(a.args,(o,m)=>(s(),C(he,null,{default:r(()=>[n(X,{span:5,style:{"margin-top":"8px"}},{default:r(()=>[m=="header"?(s(),d("span",ze,"Header名称")):U("",!0),m=="regexp"?(s(),d("span",De,"参数值")):U("",!0),m=="param"?(s(),d("span",Ke,"参数名")):U("",!0),m=="name"?(s(),d("span",Pe,"参数名")):U("",!0)]),_:2},1024),n(X,{span:18},{default:r(()=>[n(i,{defaultValue:o,placeholder:"参数值",style:{width:"70%","margin-right":"8px","margin-top":"3px"},onChange:g=>de(g,a.args,m)},null,8,["defaultValue","onChange"])]),_:2},1024)]),_:2},1024))),256))])],64))]))),256)),z("p",Ae,[n(ee,{trigger:"click"},{overlay:r(()=>[n(Z,null,{default:r(()=>[(s(!0),d(_,null,b(le.value,a=>(s(),C(Y,{key:a.name,onClick:k=>pe(a)},{default:r(()=>[y(w(a.name),1)]),_:2},1032,["onClick"]))),128))]),_:1})]),default:r(()=>[n(N,{type:"dashed",style:{"margin-left":"8px",width:"100%"}},{default:r(()=>[t[6]||(t[6]=y(" 添加路由条件 ")),n(l(T),{size:22})]),_:1,__:[6]})]),_:1})])]),_:1}),n(f,{name:"predicates",label:"过滤器"},{default:r(()=>[(s(!0),d(_,null,b(l(u).filters,(a,k)=>(s(),d("div",null,[n(H,null,{default:r(()=>[y(w(a.name)+" ",1),n(l(L),{size:"22",onClick:o=>ge(l(u),k)},null,8,["onClick"])]),_:2},1024),(s(!0),d(_,null,b(a.args,(o,m)=>(s(),d("div",{key:o.key},[n(Ce,null,{default:r(()=>[n(i,{value:o.key,"onUpdate:value":g=>o.key=g,placeholder:"参数键",style:{width:"45%","margin-right":"8px"}},null,8,["value","onUpdate:value"]),n(i,{value:o.value,"onUpdate:value":g=>o.value=g,placeholder:"参数值",style:{width:"40%","margin-right":"8px","margin-top":"3px"}},null,8,["value","onUpdate:value"])]),_:2},1024),n(l($e),{size:22,onClick:g=>fe(a,m)},null,8,["onClick"])]))),128)),n(N,{type:"dashed",style:{"margin-left":"28%",width:"37%","margin-top":"5px"},size:"small",onClick:o=>ve(a)},{default:r(()=>[n(l(T),{size:22}),t[7]||(t[7]=y(" 添加参数 "))]),_:2,__:[7]},1032,["onClick"])]))),256)),z("p",He,[n(ee,{trigger:"click"},{overlay:r(()=>[n(Z,{onClick:_e},{default:r(()=>[(s(),d(_,null,b(P,a=>n(Y,{key:a.key,name:a.name},{default:r(()=>[y(w(a.name),1)]),_:2},1032,["name"])),64))]),_:1})]),default:r(()=>[n(N,{type:"dashed",style:{"margin-left":"8px",width:"100%"}},{default:r(()=>[t[8]||(t[8]=y(" 添加过滤器 ")),n(l(T))]),_:1,__:[8]})]),_:1})])]),_:1})]),_:1},8,["label-col","wrapper-col","model"])]),_:1},16,["onRegister","title"])}}});export{Ye as default};
