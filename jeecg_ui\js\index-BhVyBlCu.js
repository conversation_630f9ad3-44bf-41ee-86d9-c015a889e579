var et=Object.defineProperty,nt=Object.defineProperties;var it=Object.getOwnPropertyDescriptors;var x=Object.getOwnPropertySymbols;var ot=Object.prototype.hasOwnProperty,at=Object.prototype.propertyIsEnumerable;var k=(a,n,o)=>n in a?et(a,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[n]=o,l=(a,n)=>{for(var o in n||(n={}))ot.call(n,o)&&k(a,o,n[o]);if(x)for(var o of x(n))at.call(n,o)&&k(a,o,n[o]);return a},I=(a,n)=>nt(a,it(n));var h=(a,n,o)=>new Promise((w,g)=>{var v=r=>{try{d(o.next(r))}catch(s){g(s)}},R=r=>{try{d(o.throw(r))}catch(s){g(s)}},d=r=>r.done?w(r.value):Promise.resolve(r.value).then(v,R);d((o=o.apply(a,n)).next())});import{d as D,ap as rt,f as b,ag as st,aq as m,ar as p,k as f,aD as u,au as T,G as S,u as y}from"./vue-vendor-dy9k-Yad.js";import{u as lt}from"./index-BkGZ5fiW.js";import{E as ut,u as dt,aX as P,a as ct}from"./index-CCWaWN5g.js";import{g as mt,d as pt,a as ft}from"./entrustBidding-7diNZrW8.js";import{D as gt}from"./index-CyU3vcHV.js";import{Q as ht}from"./componentMap-Bkie1n3v.js";import bt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFilePreview-CazplhRu.js";import"./SupplyDemand-DK00S9Ao.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const Tt={class:"p-4"},yt={key:0},wt={key:1},vt={key:0},Rt={key:1},Ct=D({name:"EntrustBidding"}),_t=D(I(l({},Ct),{setup(a){const{createMessage:n,createConfirm:o}=dt(),{handleExportXls:w}=ut(),g=rt(),v=[{title:"序号",dataIndex:"index",width:60,customRender:({index:t})=>t+1},{title:"委托单号",dataIndex:"entrustOrderId",width:160},{title:"标的名称",dataIndex:"itemName",width:200,ellipsis:!0},{title:"标的类型",dataIndex:"itemType",width:100},{title:"标的地址",dataIndex:"address",width:200,ellipsis:!0},{title:"委托单位",dataIndex:"entrustCompanyName",width:150},{title:"委托时间",dataIndex:"createTime",width:150,customRender:({text:t})=>t?P(t):"-"},{title:"竞价时间",dataIndex:"biddingTime",width:150,customRender:({text:t})=>t?P(t):"-"},{title:"标的数量",dataIndex:"quantity",width:100},{title:"计量单位",dataIndex:"unit",width:80},{title:"保留价",dataIndex:"reservePrice",width:120,slots:{customRender:"amount"}},{title:"起拍价",dataIndex:"startPrice",width:120,slots:{customRender:"amount"}},{title:"成交价",dataIndex:"dealPrice",width:120,slots:{customRender:"amount"}},{title:"审核状态",dataIndex:"status",width:100,slots:{customRender:"status"}}],R=[{key:"all",label:"全部委托",icon:""},{key:"draft",label:"未审核",icon:""},{key:"pending",label:"审核中",icon:""},{key:"approved",label:"已通过",icon:""},{key:"rejected",label:"未通过",icon:""}],d=b("all"),r=b({}),s=b(!1),C=b(null);function N(t){d.value=t;let e={};switch(t){case"draft":e={status:1};break;case"pending":e={status:2};break;case"approved":e={status:3};break;case"rejected":e={status:4};break;default:e={}}r.value=e,_()}function M(){return h(this,null,function*(){try{let t={};try{t=yield Y().validate()}catch(c){}const e=l(l({},r.value),t);e.entrustTimeRange&&Array.isArray(e.entrustTimeRange)&&e.entrustTimeRange.length===2&&(e.entrustTimeStart=e.entrustTimeRange[0],e.entrustTimeEnd=e.entrustTimeRange[1],delete e.entrustTimeRange),e.biddingTimeRange&&Array.isArray(e.biddingTimeRange)&&e.biddingTimeRange.length===2&&(e.biddingTimeStart=e.biddingTimeRange[0],e.biddingTimeEnd=e.biddingTimeRange[1],delete e.biddingTimeRange),yield w("委托竞价列表",mt,e),n.success("导出成功")}catch(t){n.error("导出失败")}})}function A(t){return h(this,null,function*(){t!=null&&t.entrustTimeRange&&(t.entrustTimeStart=t.entrustTimeRange.split(",")[0],t.entrustTimeEnd=t.entrustTimeRange.split(",")[1],delete t.entrustTimeRange);const e={entrustType:1,serviceType:1},c=l(l(l({},t),e),r.value);return ft(c)})}const[E,{reload:_,getForm:Y}]=lt({api:A,columns:v,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,showNavigation:!0,navigationItems:R,activeNavigationKey:d.value,showExportButton:!0,inset:!0,maxHeight:458,actionColumn:{width:220,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"},formConfig:{labelWidth:64,size:"large",actionColOptions:{span:6,style:{textAlign:"right"}},schemas:[{field:"itemName",label:"标的名称",component:"Input",componentProps:{placeholder:"请输入标的名称"},colProps:{span:6}},{field:"status",label:"标的状态",component:"Select",componentProps:{placeholder:"请选择标的状态",options:[{label:"未开始",value:1},{label:"竞价中",value:2},{label:"已成交",value:3},{label:"已流拍",value:4}]},colProps:{span:6}},{field:"entrustTimeRange",label:"时间区间",component:"RangePicker",componentProps:{placeholder:["开始时间","结束时间"],showTime:!0,format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss"},colProps:{span:6}}]}});function B(t){return[{label:"编辑",onClick:j.bind(null,t)},{label:"查看详情",onClick:F.bind(null,t)},{label:"删除",color:"error",popConfirm:{title:"确认删除该委托吗？",confirm:V.bind(null,t)}}]}function U(t){return[{label:"撤拍",popConfirm:{title:"确认撤拍该委托吗？",confirm:O.bind(null,t)},ifShow:!1},{label:"报名管理",onClick:q.bind(null,t),ifShow:!1},{label:"数企详情",onClick:z.bind(null,t),ifShow:!1},{label:"竞价记录",onClick:K.bind(null,t),ifShow:!1},{label:"结算信息",onClick:W.bind(null,t),ifShow:!1},{label:"成交确认书",onClick:Q.bind(null,t),ifShow:!1},{label:"竞买人列表",onClick:X.bind(null,t),ifShow:!1},{label:"工作报告书",onClick:G.bind(null,t),ifShow:!1}]}function j(t){const e=t.entrustOrderId;g.push({path:"/entrust/appreciationEntrust",query:{id:e,serviceType:1}})}function F(t){const e={id:t.entrustOrderId,entrustType:1,serviceType:1,status:t.status||2,projectName:t.itemName||"-",relationUser:t.relationUser||"-",relationPhone:t.relationPhone||"-",applicantUser:t.entrustCompanyName||"-",auditUser:t.auditUser||"-",submitTime:t.createTime||"-",auditTime:t.auditTime||"-"};C.value=e,s.value=!0}function H(){s.value=!1,C.value=null}function O(t){n.info("撤拍功能开发中...")}function V(t){return h(this,null,function*(){try{yield pt(t.entrustOrderId),_()}catch(e){}})}function q(t){n.info("报名管理功能开发中...")}function z(t){n.info("数企详情功能开发中...")}function K(t){n.info("竞价记录功能开发中...")}function W(t){n.info("结算信息功能开发中...")}function Q(t){n.info("成交确认书功能开发中...")}function X(t){n.info("竞买人列表功能开发中...")}function G(t){n.info("工作报告书功能开发中...")}function L(t){return{0:"草稿",1:"待审核",2:"审核通过",3:"审核拒绝"}[t]||"未知"}function J(t){return{0:"default",1:"processing",2:"success",3:"error"}[t]||"default"}function Z(t){return{1:"待发布",2:"报名中",3:"竞价中",4:"已流拍",5:"已发布",6:"已成交",7:"已完成",8:"已撤拍"}[t]||"未知"}function $(t){return{1:"default",2:"processing",3:"warning",4:"error",5:"cyan",6:"success",7:"green",8:"red"}[t]||"default"}function tt(t){return!t&&t!==0?"-":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(Number(t))}return(t,e)=>{const c=st("a-tag");return p(),m("div",Tt,[f(y(bt),{onRegister:y(E),onNavigationChange:N,onExport:M},{action:u(({record:i})=>[f(y(ht),{actions:B(i),dropDownActions:U(i)},null,8,["actions","dropDownActions"])]),auditStatus:u(({text:i})=>[f(c,{color:J(i)},{default:u(()=>[S(T(L(i)),1)]),_:2},1032,["color"])]),subjectStatus:u(({text:i})=>[f(c,{color:$(i)},{default:u(()=>[S(T(Z(i)),1)]),_:2},1032,["color"])]),amount:u(({text:i})=>[i?(p(),m("span",yt,T(tt(i)),1)):(p(),m("span",wt,"-"))]),premiumRate:u(({text:i})=>[i!=null?(p(),m("span",vt,T(i)+"%",1)):(p(),m("span",Rt,"-"))]),_:1},8,["onRegister"]),f(y(gt),{open:s.value,"onUpdate:open":e[0]||(e[0]=i=>s.value=i),record:C.value,"entrust-type":1,"service-type":1,onClose:H},null,8,["open","record"])])}}})),Ae=ct(_t,[["__scopeId","data-v-b864e352"]]);export{Ae as default};
