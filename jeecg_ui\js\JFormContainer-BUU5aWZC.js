import{u as r,j as t,a as p}from"./index-CCWaWN5g.js";import{d,f as l,w as u,aq as c,ar as m,as as f,at as i,aG as o}from"./vue-vendor-dy9k-Yad.js";const{createConfirm:h}=r();const T="/openapi/auth/exportXls",$="/openapi/auth/importExcel",j=e=>t.get({url:"/openapi/auth/list",params:e}),E=(e,a)=>t.delete({url:"/openapi/auth/delete",params:e},{joinParamsToUrl:!0}).then(()=>{a()}),L=(e,a)=>{h({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>t.delete({url:"/openapi/auth/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{a()})})},U=(e,a)=>{let s=a?"/openapi/auth/edit":"/openapi/auth/add";return t.post({url:s,params:e},{isTransformResponse:!1})},k=e=>t.get({url:"/openapi/list",params:e}),C=e=>t.get({url:"/openapi/permission/list",params:e}),D=e=>t.post({url:"/openapi/permission/add",params:e},{isTransformResponse:!1}),O=e=>t.get({url:"/openapi/auth/genAKSK",params:e}),g=d({name:"JForm",props:{disabled:{type:Boolean,default:!1,required:!1}},setup(e,{emit:a}){const s=l(e.disabled);return u(()=>e.disabled,n=>{s.value=n}),{formDisabled:s}}}),b=["disabled"],K={disabled:""};function v(e,a,s,n,_,x){return m(),c("div",{class:f(e.formDisabled?"jeecg-form-container-disabled jeecg-form-detail-effect":"jeecg-and-modal-form")},[i("fieldset",{disabled:e.formDisabled},[o(e.$slots,"detail",{},void 0,!0)],8,b),o(e.$slots,"edit",{},void 0,!0),i("fieldset",K,[o(e.$slots,"default",{},void 0,!0)])],2)}const w=p(g,[["render",v],["__scopeId","data-v-b9a419e6"]]);export{w as J,T as a,L as b,O as c,E as d,k as e,C as f,$ as g,j as l,D as p,U as s};
