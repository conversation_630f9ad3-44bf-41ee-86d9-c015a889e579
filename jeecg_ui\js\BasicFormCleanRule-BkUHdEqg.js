var c=(v,u,r)=>new Promise((m,p)=>{var d=o=>{try{i(r.next(o))}catch(t){p(t)}},a=o=>{try{i(r.throw(o))}catch(t){p(t)}},i=o=>o.done?m(o.value):Promise.resolve(o.value).then(d,a);i((r=r.apply(v,u)).next())});import{d as F,ag as g,aq as x,ar as C,at as k,k as e,aD as n,G as l,u as _,F as B}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as b}from"./useForm-CgkFTrrO.js";import{B as w}from"./BasicForm-DBcXiHk0.js";import{a as R}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const q={style:{margin:"20px auto","text-align":"center"}},y=F({__name:"BasicFormCleanRule",setup(v){const u=[{field:"visitor",label:"来访人员",component:"Input",required:!0},{field:"accessed",label:"来访日期",component:"DatePicker",required:!0},{field:"phone",label:"来访人手机号",component:"Input",required:!0}],[r,{clearValidate:m,validateFields:p,validate:d}]=b({schemas:u,labelWidth:"150px",showActionButtonGroup:!1,autoFocusFirstItem:!0});function a(o){return c(this,null,function*(){o=="all"?yield d():yield p(["visitor"])})}function i(o){return c(this,null,function*(){o=="all"?yield m():yield m(["visitor"])})}return(o,t)=>{const s=g("a-button");return C(),x(B,null,[k("div",q,[e(s,{onClick:t[0]||(t[0]=f=>a("all")),class:"mr-2"},{default:n(()=>t[4]||(t[4]=[l(" 触发表单验证 ")])),_:1,__:[4]}),e(s,{onClick:t[1]||(t[1]=f=>i("all")),class:"mr-2"},{default:n(()=>t[5]||(t[5]=[l(" 清空表单验证 ")])),_:1,__:[5]}),e(s,{onClick:t[2]||(t[2]=f=>a("visitor")),class:"mr-2"},{default:n(()=>t[6]||(t[6]=[l(" 只验证来访人员 ")])),_:1,__:[6]}),e(s,{onClick:t[3]||(t[3]=f=>i("visitor")),class:"mr-2"},{default:n(()=>t[7]||(t[7]=[l(" 只清空来访人员验证 ")])),_:1,__:[7]})]),e(_(w),{onRegister:_(r),style:{"margin-top":"20px"}},null,8,["onRegister"])],64)}}}),Vt=R(y,[["__scopeId","data-v-f0494f76"]]);export{Vt as default};
