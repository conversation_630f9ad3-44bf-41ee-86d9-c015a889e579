var y=(T,N,g)=>new Promise((f,C)=>{var c=s=>{try{m(g.next(s))}catch(x){C(x)}},b=s=>{try{m(g.throw(s))}catch(x){C(x)}},m=s=>s.done?f(s.value):Promise.resolve(s.value).then(c,b);m((g=g.apply(T,N)).next())});import{d as W,e as X,f as u,w as Z,ag as _,aq as ee,ar as K,k as l,aD as o,aB as E,at as O,u as p,ah as te,au as k,as as ae,G as d,F as ne,n as le}from"./vue-vendor-dy9k-Yad.js";import{a as oe}from"./index-JbqXEynz.js";import{_ as se}from"./index-BtIdS_Qz.js";import re from"./DepartDataRuleDrawer-DIyoAvaX.js";import{c as ce,e as ie,f as ue}from"./depart.api-BoGnt_ZX.js";import{F as pe,c6 as de,N as fe,c7 as B,a as me}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";const ve={class:"j-box-bottom-button offset-20",style:{"margin-top":"30px"}},ye=W({__name:"DepartRuleTab",props:{data:{type:Object,default:()=>({})}},setup(T){const{prefixCls:N}=pe("j-depart-form-content"),g=T,f=X(()=>{var e;return(e=g.data)==null?void 0:e.id}),C=u(),c=u(!1),b=u([]),m=u([]),s=u([]),x=u([]),r=u([]),F=u([]),R=u(!1),{t:D}=fe(),[q,G]=oe();L({success:e=>{const t=localStorage.getItem(B);if(t){const a=JSON.parse(t);a.level&&j(a.level!="relation"),a.expand&&$(a.expand=="openAll")}}}),Z(f,()=>P(),{immediate:!0});function L(){return y(this,arguments,function*(e={}){var t;try{c.value=!0;let{treeList:a,ids:n}=yield ce();m.value=de(a),b.value=n,(t=e.success)==null||t.call(e,n)}finally{c.value=!1}})}function P(){return y(this,null,function*(){if(f.value)try{c.value=!0;let e=yield ie({departId:f.value});r.value=e,F.value=[...e]}finally{c.value=!1}})}function J(){return y(this,null,function*(){try{c.value=!0,yield ue({departId:f.value,permissionIds:r.value.join(","),lastpermissionIds:F.value.join(",")}),yield L(),yield P()}finally{c.value=!1}})}function M(e,t){if(R.value)r.value=e.checked?e.checked:e;else{const a=z(t.node,"children","key");if(t.checked)r.value=[...new Set([...r.value,...a])];else{const n=U(r.value,a);r.value=n}}}function U(e,t){const a={};for(const n of t)a[n]=!0;return e.filter(n=>!a[n])}function z(e,t,a){var S;const n=[];n.push(e[a]);const i=A=>{A.forEach(w=>{var h;n.push(w[a]),(h=w[t])!=null&&h.length&&i(w[t])})};return(S=e[t])!=null&&S.length&&i(e[t]),n}function H(e){s.value=e}function Y(e,{selectedNodes:t}){var a;if((a=t[0])!=null&&a.ruleFlag){let n=e[0];G.openDrawer(!0,{departId:f,functionId:n})}x.value=[]}function _e(e){return y(this,null,function*(){R.value=e,yield le(),r.value=C.value.getCheckedKeys()})}function $(e){return y(this,null,function*(){e?(s.value=b.value,I("expand","openAll")):(s.value=[],I("expand","closeAll"))})}function V(e){return y(this,null,function*(){e?r.value=b.value:r.value=[]})}const j=e=>{R.value=e,e?I("level","standAlone"):I("level","relation")},I=(e,t)=>{const a=localStorage.getItem(B),n=a?JSON.parse(a):{};n[e]=t,localStorage.setItem(B,JSON.stringify(n))};return(e,t)=>{const a=_("Icon"),n=_("a-empty"),i=_("a-menu-item"),S=_("a-menu"),A=_("a-button"),w=_("a-dropdown"),h=_("a-spin");return K(),ee(ne,null,[l(h,{spinning:c.value},{default:o(()=>[m.value.length>0?(K(),E(p(se),{key:0,ref_key:"basicTree",ref:C,class:"depart-rule-tree",checkable:"",treeData:m.value,checkedKeys:r.value,selectedKeys:x.value,expandedKeys:s.value,checkStrictly:!0,style:{height:"500px",overflow:"auto"},onCheck:M,onExpand:H,onSelect:Y},{title:o(({slotTitle:v,ruleFlag:Q})=>[O("span",null,k(v),1),Q?(K(),E(a,{key:0,icon:"ant-design:align-left-outlined",style:{"margin-left":"5px",color:"red"}})):te("",!0)]),_:1},8,["treeData","checkedKeys","selectedKeys","expandedKeys"])):(K(),E(n,{key:1,description:"无可配置部门权限"})),O("div",ve,[O("div",{class:ae(["j-box-bottom-button-float",[`${p(N)}`]])},[l(w,{trigger:["click"],placement:"top"},{overlay:o(()=>[l(S,null,{default:o(()=>[l(i,{key:"3",onClick:t[0]||(t[0]=v=>V(!0))},{default:o(()=>[d(k(p(D)("component.tree.selectAll")),1)]),_:1}),l(i,{key:"4",onClick:t[1]||(t[1]=v=>V(!1))},{default:o(()=>[d(k(p(D)("component.tree.unSelectAll")),1)]),_:1}),l(i,{key:"5",onClick:t[2]||(t[2]=v=>$(!0))},{default:o(()=>[d(k(p(D)("component.tree.expandAll")),1)]),_:1}),l(i,{key:"6",onClick:t[3]||(t[3]=v=>$(!1))},{default:o(()=>[d(k(p(D)("component.tree.unExpandAll")),1)]),_:1}),l(i,{key:"7",onClick:t[4]||(t[4]=v=>j(!1))},{default:o(()=>[d(k(p(D)("component.tree.checkStrictly")),1)]),_:1}),l(i,{key:"8",onClick:t[5]||(t[5]=v=>j(!0))},{default:o(()=>[d(k(p(D)("component.tree.checkUnStrictly")),1)]),_:1})]),_:1})]),default:o(()=>[l(A,{style:{float:"left"}},{default:o(()=>[t[6]||(t[6]=d(" 树操作 ")),l(a,{icon:"ant-design:up-outlined"})]),_:1,__:[6]})]),_:1}),l(A,{type:"primary",preIcon:"ant-design:save-filled",onClick:J},{default:o(()=>t[7]||(t[7]=[d("保存")])),_:1,__:[7]})],2)])]),_:1},8,["spinning"]),l(re,{onRegister:p(q)},null,8,["onRegister"])],64)}}}),Oe=me(ye,[["__scopeId","data-v-f5440a95"]]);export{Oe as default};
