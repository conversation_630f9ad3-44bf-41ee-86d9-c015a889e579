import{d as i,ag as n,aq as p,ar as t,A as d,aB as s}from"./vue-vendor-dy9k-Yad.js";import{cp as u,bn as g}from"./antd-vue-vendor-me9YkNVC.js";import{C as c,a as m}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const f=i({name:"SiderTrigger",components:{DoubleRightOutlined:g,DoubleLeftOutlined:u},setup(){const{getCollapsed:e,toggleCollapsed:o}=c();return{getCollapsed:e,toggleCollapsed:o}}});function C(e,o,b,_,k,D){const l=n("DoubleRightOutlined"),r=n("DoubleLeftOutlined");return t(),p("div",{onClick:o[0]||(o[0]=d((...a)=>e.toggleCollapsed&&e.toggleCollapsed(...a),["stop"]))},[e.getCollapsed?(t(),s(l,{key:0})):(t(),s(r,{key:1}))])}const L=m(f,[["render",C]]);export{L as default};
