import{d as e,ag as a,aB as s,ar as p,aE as i,aD as n,G as m}from"./vue-vendor-dy9k-Yad.js";import{B as c}from"./index-JbqXEynz.js";import{a as f}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";const _=e({components:{BasicDrawer:c},setup(){return{}}});function d(t,r,w,l,u,B){const o=a("BasicDrawer");return p(),s(o,i(t.$attrs,{title:"Drawer Title",width:"50%"}),{default:n(()=>r[0]||(r[0]=[m(" Drawer Info. ")])),_:1,__:[0]},16)}const I=f(_,[["render",d]]);export{I as default};
