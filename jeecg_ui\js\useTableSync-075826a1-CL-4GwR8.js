import{c as g,f as F,e as h,n as f}from"./vue-vendor-dy9k-Yad.js";import{r as I}from"./cgform.data-0ca62d09-CBB13rBO.js";import{p as N}from"./antd-vue-vendor-me9YkNVC.js";var x=(b,m,a)=>new Promise((i,v)=>{var r=l=>{try{u(a.next(l))}catch(c){v(c)}},y=l=>{try{u(a.throw(l))}catch(c){v(c)}},u=l=>l.done?i(l.value):Promise.resolve(l.value).then(r,y);u((a=a.apply(b,m)).next())});function V(b){const m=g("tables"),a=g("fullScreenRef"),i=F(),v=F(!1),r=F([]),y=h(()=>({normal:a!=null&&a.value?430:260,noToolbar:a!=null&&a.value?480:320})),u=h(()=>["id"].concat(b.value.map(t=>t.key))),l=h(()=>({scrollY:{enabled:!0,gt:15},scrollX:{enabled:!0,gt:20}}));function c(t){return x(this,null,function*(){let e=i.value;if(yield e.fullValidateTable())throw{code:I,activeKey:t};let T=e.getTableData().map(s=>N(s,u.value)),p=e.getDeleteData().map(s=>s.id);return{tableData:T,deleteIds:p}})}function w(t,e=!1){return x(this,null,function*(){e?(r.value=[],yield f(),yield i.value.addOrInsert(t,0,null,{setActive:!1}),yield f(),i.value.recalcDisableRows()):(r.value=t,yield f(),i.value.recalcDisableRows())})}function R(t){let e=i.value,T=t.value.tableRef,p=t.value.getRemoveIds(),s=T.getXTable().internalData.tableFullData,k=e.getXTable().internalData.tableFullData;return s.forEach(o=>{let D=!1;if(k.forEach(n=>{if(o.id===n.id){let d=n.dbFieldName,E=n.dbFieldTxt;(o.dbFieldName!==d||o.dbFieldTxt!==E)&&e.setValues([{rowKey:n.id,values:{dbFieldName:o.dbFieldName,dbFieldTxt:o.dbFieldTxt}}]),D=!0}else p.forEach(d=>{d===n.id&&(setTimeout(()=>{e.removeRowsById(d)},0),D=!0)})}),!D){let n=Object.assign({},o);b.value.forEach(d=>{d.key!=="dbFieldName"&&d.key!=="dbFieldTxt"&&(n[d.key]=d.defaultValue)}),e.addRows(n)}}),f()}return{tables:m,tableRef:i,loading:v,dataSource:r,columnKeys:u,tableHeight:y,tableProps:l,syncTable:R,validateData:c,setDataSource:w}}export{V as L};
