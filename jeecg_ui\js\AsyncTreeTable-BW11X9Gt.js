var b=Object.defineProperty,v=Object.defineProperties;var y=Object.getOwnPropertyDescriptors;var c=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var f=(o,t,r)=>t in o?b(o,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[t]=r,_=(o,t)=>{for(var r in t||(t={}))C.call(t,r)&&f(o,r,t[r]);if(c)for(var r of c(t))k.call(t,r)&&f(o,r,t[r]);return o},g=(o,t)=>v(o,y(t));var n=(o,t,r)=>new Promise((s,l)=>{var p=i=>{try{a(r.next(i))}catch(e){l(e)}},d=i=>{try{a(r.throw(i))}catch(e){l(e)}},a=i=>i.done?s(i.value):Promise.resolve(i.value).then(p,d);a((r=r.apply(o,t)).next())});import{d as I,f as h,ag as R,aB as w,ar as B,aD as x,k as T,u}from"./vue-vendor-dy9k-Yad.js";import{j as S}from"./index-CCWaWN5g.js";import{P as K}from"./index-CtJ0w2CP.js";import{u as L}from"./index-BkGZ5fiW.js";import N from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const P="/mock/api/asynTreeList",Dt=I({__name:"AsyncTreeTable",setup(o){const t=h(!1),r=h([]),[s,{setLoading:l}]=L({rowKey:"id",bordered:!0,canResize:!1,isTreeTable:!0,showIndexColumn:!0,columns:[{title:"名称",dataIndex:"name"},{title:"组件",dataIndex:"component"},{title:"排序",dataIndex:"orderNum"}]});function p(i){return n(this,null,function*(){t.value=!0;let e=yield S.get({url:P,params:i});return t.value=!1,e.map(m=>m.hasChildren?g(_({},m),{children:[]}):m)})}function d(){return n(this,null,function*(){r.value=yield p({id:"0"})})}d();function a(i,e){return n(this,null,function*(){i&&e.hasChildren&&e.children.length===0&&(e.children=yield p({id:e.id}))})}return(i,e)=>{const m=R("a-card");return B(),w(u(K),null,{default:x(()=>[T(m,{bordered:!1},{default:x(()=>[T(u(N),{loading:t.value,dataSource:r.value,onRegister:u(s),onExpand:a},null,8,["loading","dataSource","onRegister"])]),_:1})]),_:1})}}});export{Dt as default};
