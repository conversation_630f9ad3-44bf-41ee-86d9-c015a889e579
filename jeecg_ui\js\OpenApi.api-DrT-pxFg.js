import{j as t,u as o}from"./index-CCWaWN5g.js";const{createConfirm:a}=o();const p="/openapi/exportXls",r="/openapi/importExcel";const i=e=>t.get({url:"/openapi/list",params:e}),l=(e,n)=>t.delete({url:"/openapi/delete",params:e},{joinParamsToUrl:!0}).then(()=>{n()}),c=(e,n)=>{a({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>t.delete({url:"/openapi/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{n()})})},d=(e,n)=>n?t.put({url:"/openapi/edit",params:e}):t.post({url:"/openapi/add",params:e}),u=e=>t.get({url:"/openapi/genPath",params:e},{isTransformResponse:!1}),g=e=>t.get({url:"/openapi/list",params:e},{isTransformResponse:!1}),m=e=>t.get({url:"/openapi/list",params:e},{isTransformResponse:!1}),f=e=>t.get({url:"/openapi/json",params:e},{isTransformResponse:!1});export{p as a,c as b,u as c,l as d,f as e,m as f,r as g,i as l,g as o,d as s};
