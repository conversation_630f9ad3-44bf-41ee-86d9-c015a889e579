import{d as u,aI as f,e as t,ag as d,aq as m,ar as y,aA as b,as as v,aG as g,k as C}from"./vue-vendor-dy9k-Yad.js";import{F as k}from"./index-CCWaWN5g.js";var N=Object.defineProperty,s=Object.getOwnPropertySymbols,S=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable,n=(r,e,o)=>e in r?N(r,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[e]=o,O=(r,e)=>{for(var o in e||(e={}))S.call(e,o)&&n(r,o,e[o]);if(s)for(var o of s(e))h.call(e,o)&&n(r,o,e[o]);return r};const W=u({__name:"NodeIcon",props:{icon:{type:String,required:!0},color:{type:String,default:"#66ccff"},rotate:{type:Number,default:0},strokeWidth:{type:Number,default:0},iconColor:{type:String,default:"#fff"},className:{type:String,default:""}},setup(r){f(a=>({"8b4e5c24":r.iconColor,"4a17fe99":i.value}));const e=r,{prefixCls:o}=k("airag-base-node-icon"),l=t(()=>{const a=[o];return e.className&&a.push(e.className),a}),c=t(()=>{const a=e.rotate>0?{transform:`rotate(${e.rotate}deg)`}:{};return O({backgroundColor:e.color},a)}),i=t(()=>e.strokeWidth>0?e.strokeWidth+"px":e.strokeWidth);return(a,j)=>{const p=d("Icon");return y(),m("div",{class:v(l.value),style:b(c.value)},[g(a.$slots,"icon",{},()=>[C(p,{icon:r.icon,color:r.iconColor},null,8,["icon","color"])])],6)}}});export{W as B};
