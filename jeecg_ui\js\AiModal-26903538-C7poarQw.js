import{f as i,ag as l,aB as _,ar as k,u as c,aE as x,aD as p,at as e,k as n,G as N}from"./vue-vendor-dy9k-Yad.js";import{B as $}from"./index-Diw57m_E.js";import{cs as z,ac as A,u as I,j as R}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const V={class:"aiWrap"},b={class:"content"},y={__name:"AiModal",emits:["register","success"],setup(U,{emit:u}){const m={aigc:"/online/cgform/api/aigc"},v=u,[d,{closeModal:g}]=A(),{createMessage:h}=I(),t=i(""),o=i(!1),f=i("ai创建表"),r=()=>{g()},C=()=>{t.value.trim()===""?h.warning("请输入修饰词~"):(o.value=!0,R.post({url:`${m.aigc}?prompt=${t.value}`,timeout:1e3*60*5}).then(s=>{o.value=!1,r(),v("success"),t.value=""}).catch(s=>{o.value=!1}))};return(s,a)=>{const w=l("a-input"),M=l("a-button");return k(),_(c($),x({height:180,title:f.value,width:600,maskClosable:!1},s.$attrs,{onRegister:c(d),footer:null,onCancel:r}),{default:p(()=>[e("div",V,[a[2]||(a[2]=e("div",{class:"titleArea"},[e("svg",{t:"1707100353985",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"4235",width:"26",height:"26"},[e("path",{d:"M512 64C264.8 64 64 264.8 64 512s200.8 448 448 448 448-200.8 448-448S759.2 64 512 64z m32 704h-64v-64h64v64z m11.2-203.2l-5.6 4.8c-3.2 2.4-5.6 8-5.6 12.8v58.4h-64v-58.4c0-24.8 11.2-48 29.6-63.2l5.6-4.8c56-44.8 83.2-68 83.2-108C598.4 358.4 560 320 512 320c-49.6 0-86.4 36.8-86.4 86.4h-64C361.6 322.4 428 256 512 256c83.2 0 150.4 67.2 150.4 150.4 0 72.8-49.6 112.8-107.2 158.4z","p-id":"4236",fill:"currentColor"})]),e("h3",null,"创建工作表字段需要专业建议?试试AI智能推荐吧")],-1)),a[3]||(a[3]=e("p",{class:"tip"},"可尝试添加修饰词，如:智能家居行业的生产计划表",-1)),e("div",b,[n(w,{value:t.value,"onUpdate:value":a[0]||(a[0]=B=>t.value=B),valueModifiers:{trim:!0},placeholder:"请输入修饰词"},null,8,["value"]),n(M,{loading:o.value,type:"primary",onClick:C},{default:p(()=>a[1]||(a[1]=[N("生成表")])),_:1},8,["loading"])])])]),_:1},16,["title","onRegister"])}}},Q=z(y,[["__scopeId","data-v-a85052cc"]]);export{Q as default};
