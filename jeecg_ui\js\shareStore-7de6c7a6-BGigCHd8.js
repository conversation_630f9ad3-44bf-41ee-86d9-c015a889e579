import{cL as h,b7 as u,cM as m,cN as f,cO as g}from"./index-CCWaWN5g.js";var R=(t,o,e)=>new Promise((l,a)=>{var i=r=>{try{n(e.next(r))}catch(c){a(c)}},d=r=>{try{n(e.throw(r))}catch(c){a(c)}},n=r=>r.done?l(r.value):Promise.resolve(r.value).then(i,d);n((e=e.apply(t,o)).next())});const s=h(),w=u({id:"online-cgform-share",state:()=>({cgformRecord:null,dataRecord:null}),getters:{getCgformRecord(){return this.cgformRecord},getDataRecord(){return this.dataRecord}},actions:{checkUrlToken(){return R(this,null,function*(){const t=new URLSearchParams(window.location.search).get("token"),o=t!=null&&t.length>0;if(o){s.setToken(t);try{const e=yield m();if(e!=null&&e.userInfo)s.setUserInfo(e.userInfo),e.sysAllDictItems&&s.setAllDictItems(e.sysAllDictItems);else throw new Error("token无效");return!0}catch(e){return s.setToken(""),f(g),!1}}return o})},setCgformRecord(t){this.cgformRecord=t},setDataRecord(t){this.dataRecord=t}}});export{w as S};
