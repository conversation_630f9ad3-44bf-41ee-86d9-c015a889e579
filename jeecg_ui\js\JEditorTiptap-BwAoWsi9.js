var c=Object.defineProperty;var d=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable;var p=(e,o,a)=>o in e?c(e,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[o]=a,l=(e,o)=>{for(var a in o||(o={}))u.call(o,a)&&p(e,a,o[a]);if(d)for(var a of d(o))h.call(o,a)&&p(e,a,o[a]);return e};import{d as f,e as m,n as g,ag as b,aB as T,ar as _,aE as C}from"./vue-vendor-dy9k-Yad.js";import{T as E}from"./index-ByPySmGo.js";import{H as t,a as F}from"./index-CCWaWN5g.js";import{a5 as v}from"./antd-vue-vendor-me9YkNVC.js";const $=f({name:"JEditorTiptap",inheritAttrs:!1,components:{TiptapEditor:E},props:{value:t.string.def(""),disabled:t.bool.def(!1),autoFocus:t.bool.def(!0),height:t.string.def("300px"),placeholder:t.string.def("请输入内容..."),hideToolbar:t.bool.def(!1)},emits:["change","update:value"],setup(e,{emit:o,attrs:a}){const s=m(()=>l({modelValue:e.value,disabled:e.disabled,autoFocus:e.autoFocus,height:e.height,placeholder:e.placeholder,hideToolbar:e.hideToolbar},a)),n=v.useInjectFormItemContext();function i(r){o("change",r),o("update:value",r),g(()=>{n==null||n.onFieldChange()})}return{bindProps:s,onChange:i}}});function k(e,o,a,s,n,i){const r=b("TiptapEditor");return _(),T(r,C(e.bindProps,{onChange:e.onChange}),null,16,["onChange"])}const y=F($,[["render",k]]);export{y as J};
