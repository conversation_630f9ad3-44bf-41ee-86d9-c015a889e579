var u=(e,n,a)=>new Promise((r,p)=>{var s=i=>{try{m(a.next(i))}catch(t){p(t)}},l=i=>{try{m(a.throw(i))}catch(t){p(t)}},m=i=>i.done?r(i.value):Promise.resolve(i.value).then(s,l);m((a=a.apply(e,n)).next())});import{d as b,f as C,ag as f,aq as g,ar as R,k as w,aD as v}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{o as x}from"./select-nVA4yav1.js";import{d as I}from"./table-BDFKJhHv.js";import{t as k}from"./tree-C9AW4Mg9.js";import{h as T}from"./antd-vue-vendor-me9YkNVC.js";import{u as _,a as A}from"./index-CCWaWN5g.js";import{useListPage as E}from"./useListPage-Soxgnx9a.js";import{Q as y}from"./componentMap-Bkie1n3v.js";import P from"./BasicTable-xCEZpGLb.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const S=[{title:"输入框",dataIndex:"name",editRow:!0,editComponentProps:{prefix:"$"},width:150},{title:"默认输入状态",dataIndex:"name7",editRow:!0,width:150},{title:"输入框校验",dataIndex:"name1",editRow:!0,align:"left",editRule:!0,width:150},{title:"输入框函数校验",dataIndex:"name2",editRow:!0,align:"right",editRule:e=>u(null,null,function*(){return e==="2"?"不能输入该值":""})},{title:"数字输入框",dataIndex:"id",editRow:!0,editRule:!0,editComponent:"InputNumber",width:150},{title:"下拉框",dataIndex:"name3",editRow:!0,editComponent:"Select",editComponentProps:{options:[{label:"Option1",value:"1"},{label:"Option2",value:"2"},{label:"Option3",value:"3"}]},width:200},{title:"远程下拉",dataIndex:"name4",editRow:!0,editComponent:"ApiSelect",editComponentProps:{api:x,resultField:"list",labelField:"name",valueField:"id"},width:200},{title:"远程下拉树",dataIndex:"name8",editRow:!0,editComponent:"ApiTreeSelect",editRule:!1,editComponentProps:{api:k,resultField:"list"},width:200},{title:"日期选择",dataIndex:"date",editRow:!0,editComponent:"DatePicker",editComponentProps:{valueFormat:"YYYY-MM-DD",format:"YYYY-MM-DD"},width:150},{title:"时间选择",dataIndex:"time",editRow:!0,editComponent:"TimePicker",editComponentProps:{valueFormat:"HH:mm",format:"HH:mm"},width:100},{title:"勾选框",dataIndex:"name5",editRow:!0,editComponent:"Checkbox",editValueMap:e=>e?"是":"否",width:100},{title:"开关",dataIndex:"name6",editRow:!0,editComponent:"Switch",editValueMap:e=>e?"开":"关",width:100}],M=b({components:{BasicTable:P,TableAction:y},setup(){const{createMessage:e}=_(),n=C(""),{tableContext:a}=E({designScope:"basic-table-demo",tableProps:{title:"可编辑行示例",titleHelpMessage:["本例中修改[数字输入框]这一列时，同一行的[远程下拉]列的当前编辑数据也会同步发生改变"],api:I,columns:S,showIndexColumn:!1,showTableSetting:!0,tableSetting:{fullScreen:!0},actionColumn:{width:160,title:"Action",dataIndex:"action",slots:{customRender:"action"}},useSearchForm:!1}}),[r]=a;function p(t){var o;n.value=t.key,(o=t.onEdit)==null||o.call(t,!0)}function s(t){var o;n.value="",(o=t.onEdit)==null||o.call(t,!1,!1)}function l(t){return u(this,null,function*(){var d,c;if(e.loading({content:"正在保存...",duration:0,key:"saving"}),yield(d=t.onValid)==null?void 0:d.call(t))try{const h=T(t.editValueRefs);(yield(c=t.onEdit)==null?void 0:c.call(t,!1,!0))&&(n.value=""),e.success({content:"数据已保存",key:"saving"})}catch(h){e.error({content:"保存失败",key:"saving"})}else e.error({content:"请填写正确的数据",key:"saving"})})}function m(t,o){return t.editable?[{label:"保存",onClick:l.bind(null,t,o)},{label:"取消",popConfirm:{title:"是否取消编辑",confirm:s.bind(null,t,o)}}]:[{label:"编辑",disabled:n.value?n.value!==t.key:!1,onClick:p.bind(null,t)}]}function i({column:t,value:o,record:d}){t.dataIndex==="id"&&(d.editValueRefs.name4.value=`${o}`)}return{registerTable:r,handleEdit:p,createActions:m,onEditChange:i}}}),D={class:"p-4"};function Y(e,n,a,r,p,s){const l=f("TableAction"),m=f("BasicTable");return R(),g("div",D,[w(m,{onRegister:e.registerTable,onEditChange:e.onEditChange},{action:v(({record:i,column:t})=>[w(l,{actions:e.createActions(i,t)},null,8,["actions"])]),_:1},8,["onRegister","onEditChange"])])}const qt=A(M,[["render",Y]]);export{qt as default};
