var x=Object.defineProperty;var g=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable;var h=(i,t,o)=>t in i?x(i,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):i[t]=o,_=(i,t)=>{for(var o in t||(t={}))C.call(t,o)&&h(i,o,t[o]);if(g)for(var o of g(t))F.call(t,o)&&h(i,o,t[o]);return i};var d=(i,t,o)=>new Promise((r,p)=>{var n=a=>{try{m(o.next(a))}catch(l){p(l)}},s=a=>{try{m(o.throw(a))}catch(l){p(l)}},m=a=>a.done?r(a.value):Promise.resolve(a.value).then(n,s);m((o=o.apply(i,t)).next())});import{f,u as w,ag as v,aq as O,ar as P,k as y,aD as I}from"./vue-vendor-dy9k-Yad.js";import{I as S}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{B as D}from"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import{d as R}from"./AiKnowledgeBase.data-BCjuIj-H.js";import{f as A}from"./AiKnowledgeBase.api-Dgaf5KfS.js";import{u as L}from"./useForm-CgkFTrrO.js";import{ac as N,a as V}from"./index-CCWaWN5g.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const b={name:"AiragKnowledgeDocModal",components:{BasicForm:D,BasicModal:S},emits:["success","register"],setup(i,{emit:t}){const o=f("创建知识库"),r=f(!1),p=f(),[n,{resetFields:s,setFieldsValue:m,validate:a,clearValidate:l,updateSchema:T}]=L({schemas:R,showActionButtonGroup:!1,layout:"vertical",wrapperCol:{span:24}}),[k,{closeModal:u,setModalProps:c}]=N(e=>d(null,null,function*(){yield s(),c({confirmLoading:!1}),r.value=!!(e!=null&&e.isUpdate),o.value=r.value?"编辑文档":"创建文档",w(r)?(e.record.type==="file"&&e.record.metadata&&(e.record.filePath=JSON.parse(e.record.metadata).filePath),yield m(_({},e.record))):(p.value=e.knowledgeId,yield m({type:e.type})),c({bodyStyle:{padding:"10px"}})}));function B(){return d(this,null,function*(){try{c({confirmLoading:!0});let e=yield a();w(r)||(e.knowledgeId=p.value),e.filePath&&(e.metadata=JSON.stringify({filePath:e.filePath}),delete e.filePath),yield A(e),u(),t("success")}finally{c({confirmLoading:!1})}})}function M(){u()}return{registerModal:k,registerForm:n,title:o,handleOk:B,handleCancel:M}}},J={class:"p-2"};function K(i,t,o,r,p,n){const s=v("BasicForm"),m=v("BasicModal");return P(),O("div",J,[y(m,{destroyOnClose:"",onRegister:r.registerModal,width:"600px",title:r.title,onOk:r.handleOk,onCancel:r.handleCancel},{default:I(()=>[y(s,{onRegister:r.registerForm},null,8,["onRegister"])]),_:1},8,["onRegister","title","onOk","onCancel"])])}const jo=V(b,[["render",K],["__scopeId","data-v-fb5176f6"]]);export{jo as default};
