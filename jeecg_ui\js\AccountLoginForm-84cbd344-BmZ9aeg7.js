import{d as J,f as m,r as U,e as I,u as o,J as Q,o as Z,ag as f,aB as $,ar as K,aO as W,aD as d,at as a,k as c,as as F,au as g,aq as q,G as L}from"./vue-vendor-dy9k-Yad.js";import{cs as X,N as Y,bP as ee,bQ as te,ah as ae,u as oe,bZ as se}from"./index-CCWaWN5g.js";import{i as ne}from"./checkcode-DLY3GIII.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var ie=(M,C,v)=>new Promise((w,h)=>{var s=l=>{try{p(v.next(l))}catch(n){h(n)}},r=l=>{try{p(v.throw(l))}catch(n){h(n)}},p=l=>l.done?w(l.value):Promise.resolve(l.value).then(s,r);p((v=v.apply(M,C)).next())});const ce={class:"content-item current-active"},re={class:"code-image"},le=["src"],ue=["src"],de={class:"forget-pwd"},pe={style:{float:"left"},class:"pointer forget-login-pwd"},me={class:"remember-password pointer",style:{float:"right"}},fe=J({__name:"AccountLoginForm",emits:["login","forget-pwd","login-success"],setup(M,{expose:C,emit:v}){const w=m(),h=m();m();const{t:s}=Y(),r=m(""),p=m(!1),l=m(),n=U({randCodeImage:"",requestCodeSuccess:!1,checkKey:-1}),t=U({account:"",password:"",inputCode:""}),{getFormRules:P}=ee(t),{validForm:T}=te(l),V=ae(),R=m(!1);I(()=>t.account!=""||o(r)==="account"?"current-active":""),I(()=>t.password!=""||o(r)==="password"?"current-active":""),I(()=>t.inputCode!=""||o(r)==="inputCode"?"current-active":"");const{notification:D,createErrorModal:ge}=oe(),k=v;function x(i){r.value=i}function b(){r.value=""}function S(){return ie(this,null,function*(){let i=yield T();if(i)try{p.value=!0;const{userInfo:e}=yield V.login(Q({password:i.password,username:i.account,captcha:i.inputCode,checkKey:n.checkKey,mode:"none",goHome:!1}));e&&k("login-success",e.realname)}catch(e){D.error({message:s("sys.api.errorTip"),description:e.message||s("sys.api.networkExceptionMsg"),duration:3}),y()}finally{p.value=!1}})}function y(){t.inputCode="",n.checkKey=new Date().getTime()+Math.random().toString(36).slice(-4),se(n.checkKey).then(i=>{n.randCodeImage=i,n.requestCodeSuccess=!0})}function E(){k("login","phoneLogin")}function O(){k("forget-pwd")}function j(i){Object.assign(t,i),y()}return Z(()=>{y()}),C({setAccountData:j}),(i,e)=>{const _=f("a-input"),B=f("a-form-item"),N=f("a-col"),z=f("a-row"),A=f("a-checkbox"),G=f("a-button"),H=f("a-form");return K(),$(H,{id:"loginContentForm",ref_key:"loginRef",ref:l,model:t,rules:o(P),onKeyup:W(S,["enter","native"])},{default:d(()=>[a("div",{class:"content-item current-active",onClick:e[1]||(e[1]=u=>x("account"))},[c(B,{name:"account"},{default:d(()=>[c(_,{ref_key:"accountRef",ref:w,id:"accountLogin",value:t.account,"onUpdate:value":e[0]||(e[0]=u=>t.account=u),style:{height:"40px"},onBlur:b},null,8,["value"]),a("div",{class:F(["form-title",r.value==="account"?"active-title":""])},g(o(s)("sys.login.userName")),3)]),_:1})]),a("div",{class:"content-item current-active",onClick:e[3]||(e[3]=u=>x("password"))},[c(B,{name:"password"},{default:d(()=>[c(_,{id:"pwdLogin",ref:"",type:"password",value:t.password,"onUpdate:value":e[2]||(e[2]=u=>t.password=u),style:{height:"40px"},onBlur:b},null,8,["value"]),a("div",{class:F(["form-title",r.value==="password"?"active-title":""])},g(o(s)("sys.login.password")),3)]),_:1})]),a("div",ce,[c(z,{span:24},{default:d(()=>[c(N,{span:15},{default:d(()=>[c(B,{name:"inputCode",onClick:e[5]||(e[5]=u=>x("inputCode"))},{default:d(()=>[c(_,{ref_key:"codeRef",ref:h,value:t.inputCode,"onUpdate:value":e[4]||(e[4]=u=>t.inputCode=u),style:{height:"40px"},onBlur:b},null,8,["value"]),a("div",{class:F(["form-title",r.value==="inputCode"?"active-title":""])},g(o(s)("sys.login.inputCode")),3)]),_:1})]),_:1}),c(N,{span:8},{default:d(()=>[a("div",re,[n.requestCodeSuccess?(K(),q("img",{key:0,class:"pointer",style:{"margin-top":"2px","max-width":"initial"},src:n.randCodeImage,onClick:y},null,8,le)):(K(),q("img",{key:1,style:{"margin-top":"2px","max-width":"initial"},src:o(ne),onClick:y},null,8,ue))])]),_:1})]),_:1})]),a("div",de,[a("div",pe,[a("span",{style:{color:"#757575"},onClick:O},g(o(s)("sys.login.forgetPassword")),1)]),a("div",me,[c(A,{checked:R.value,"onUpdate:checked":e[6]||(e[6]=u=>R.value=u)},{default:d(()=>[L(g(o(s)("sys.login.rememberMe")),1)]),_:1},8,["checked"])])]),a("div",null,[c(G,{type:"primary",onClick:S,loading:p.value,class:"login-btn"},{default:d(()=>[L(g(o(s)("sys.login.loginButton")),1)]),_:1},8,["loading"])]),a("div",{class:"phone-login-btn pointer",onClick:E},g(o(s)("sys.login.mobileSignInFormTitle")),1)]),_:1},8,["model","rules"])}}}),ke=X(fe,[["__scopeId","data-v-ffc0558b"]]);export{ke as default};
