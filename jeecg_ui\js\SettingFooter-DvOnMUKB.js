import{d as _,u as g,ag as l,aq as b,ar as f,as as R,aB as B,ah as D,k as a,aD as i,G as u,au as d}from"./vue-vendor-dy9k-Yad.js";import{aG as O,cq as v}from"./antd-vue-vendor-me9YkNVC.js";import{bD as M,F as N,c5 as T,ah as A,B as $,N as F,br as G,u as P,d7 as S,dh as V,di as j,a as q}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const w=_({name:"SettingFooter",components:{CopyOutlined:v,RedoOutlined:O},setup(){const e=M(),{prefixCls:p}=N("setting-footer"),{t:o}=F(),{createSuccessModal:m,createMessage:r}=P(),C=T(),c=A(),t=$();function n(){const{isSuccessRef:s}=G(JSON.stringify(g(t.getProjectConfig),null,2));g(s)&&m({title:o("layout.setting.operatingTitle"),content:o("layout.setting.operatingContent")})}function y(){try{t.setProjectConfig(S);const{colorWeak:s,grayMode:h}=S;V(s),j(h),r.success(o("layout.setting.resetSuccess"))}catch(s){r.error(s)}}function k(){localStorage.clear(),t.resetAllState(),e.resetState(),C.resetState(),c.resetState(),location.reload()}return{prefixCls:p,t:o,handleCopy:n,handleResetSetting:y,handleClearAndRedo:k,isDev:!1}}});function I(e,p,o,m,r,C){const c=l("CopyOutlined"),t=l("a-button"),n=l("RedoOutlined");return f(),b("div",{class:R(e.prefixCls)},[e.isDev?(f(),B(t,{key:0,type:"primary",block:"",onClick:e.handleCopy},{default:i(()=>[a(c,{class:"mr-2"}),u(" "+d(e.t("layout.setting.copyBtn")),1)]),_:1},8,["onClick"])):D("",!0),a(t,{color:"warning",block:"",onClick:e.handleResetSetting,class:"my-3"},{default:i(()=>[a(n,{class:"mr-2"}),u(" "+d(e.t("common.resetText")),1)]),_:1},8,["onClick"]),a(t,{color:"error",block:"",onClick:e.handleClearAndRedo},{default:i(()=>[a(n,{class:"mr-2"}),u(" "+d(e.t("layout.setting.clearBtn")),1)]),_:1},8,["onClick"])],2)}const x=q(w,[["render",I],["__scopeId","data-v-3d60515b"]]);export{x as default};
