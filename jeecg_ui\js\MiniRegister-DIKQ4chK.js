var se=Object.defineProperty,oe=Object.defineProperties;var te=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,ae=Object.prototype.propertyIsEnumerable;var F=(u,r,c)=>r in u?se(u,r,{enumerable:!0,configurable:!0,writable:!0,value:c}):u[r]=c,H=(u,r)=>{for(var c in r||(r={}))ne.call(r,c)&&F(u,c,r[c]);if(S)for(var c of S(r))ae.call(r,c)&&F(u,c,r[c]);return u},j=(u,r)=>oe(u,te(r));var b=(u,r,c)=>new Promise((n,y)=>{var d=e=>{try{h(c.next(e))}catch(w){y(w)}},M=e=>{try{h(c.throw(e))}catch(w){y(w)}},h=e=>e.done?n(e.value):Promise.resolve(e.value).then(d,M);h((c=c.apply(u,r)).next())});import{d as Q,f as g,r as ie,o as le,ag as C,aq as _,ar as v,at as o,k as i,u as a,aD as p,au as P,ah as $,F as ce}from"./vue-vendor-dy9k-Yad.js";import{ad as re,N as ue,j as B,u as de,aV as pe,a as me}from"./index-CCWaWN5g.js";import fe from"./CaptchaModal-CFhSDiLW.js";import"./index-Diw57m_E.js";const ge="/assets/jeecg_ad_text-Bzr-nQGK.png",A="/assets/icon-eye-k-QpZYuxBH.png",V="/assets/icon-eye-g-w6YbkYNQ.png",_e={class:"aui-content"},ve={class:"aui-container"},ye={class:"aui-form"},he={class:"aui-image"},we={class:"aui-image-text"},xe=["src"],ke={class:"aui-formBox"},be={class:"aui-formWell"},Ce={class:"aui-flex aui-form-nav aui-clear-left",style:{"padding-bottom":"21px"}},Pe={class:"aui-flex-box activeNav on"},Ie={class:"aui-form-box"},Me={class:"aui-account aui-account-line"},Re={class:"aui-input-line"},Ue={class:"aui-input-line"},Te={class:"aui-input-line"},Ne={class:"aui-input-line"},Be={key:1,class:"aui-code-line"},Ee={class:"aui-input-line"},Oe={class:"aui-eye"},De=["src"],Se=["src"],Fe={class:"aui-input-line"},He={class:"aui-eye"},je=["src"],$e=["src"],Ae={class:"aui-flex"},Ve={class:"aui-flex-box"},Qe={class:"aui-choice"},Ye={style:{color:"#1b90ff","margin-left":"4px"}},ze={class:"aui-formButton"},Ge={class:"aui-flex"},Ke={class:"aui-flex"},Le=Q({name:"mini-register"}),qe=Q(j(H({},Le),{emits:["go-back","success","register"],setup(u,{expose:r,emit:c}){const{t:n}=ue(),{notification:y,createMessage:d}=de(),M=c,h=g(),e=ie({username:"",mobile:"",smscode:"",password:"",confirmPassword:"",policy:!1,roleCode:void 0}),w=g([]),R=g(!0),x=g(60),m=g(null),U=g("close"),T=g("close"),[Y,{openModal:z}]=re();function G(){return b(this,null,function*(){try{const s=yield B.get({url:"/sys/dict/getRegisterRoleTypeDictItems/registerRoleCodeDict"});s&&s.length>0&&(w.value=s)}catch(s){}})}function K(s){return b(this,null,function*(){try{return yield B.post({url:"/hgyUser/hgysms",params:{mobile:s}})}catch(t){throw t}})}function L(s){return b(this,null,function*(){try{return yield B.post({url:"/hgyUser/hgyAdminUserRegister",params:s},{isTransformResponse:!1})}catch(t){throw t}})}function q(){M("go-back"),N()}le(()=>{G()});function E(){return b(this,null,function*(){if(!e.mobile){d.warn(n("sys.login.mobilePlaceholder"));return}try{yield K(e.mobile);const s=60;a(m)||(x.value=s,R.value=!1,m.value=setInterval(()=>{a(x)>0&&a(x)<=s?x.value=x.value-1:(R.value=!0,clearInterval(a(m)),m.value=null)},1e3))}catch(s){s.code===pe.PHONE_SMS_FAIL_CODE?z(!0,{}):d.error((s==null?void 0:s.message)||"获取验证码失败")}})}function W(){if(!e.username){d.warn(n("sys.login.accountPlaceholder"));return}if(!e.mobile){d.warn(n("sys.login.mobilePlaceholder"));return}if(!e.roleCode){d.warn("请选择角色类型");return}if(!e.smscode){d.warn(n("sys.login.smsPlaceholder"));return}if(!e.password){d.warn(n("sys.login.passwordPlaceholder"));return}if(!e.confirmPassword){d.warn(n("sys.login.confirmPassword"));return}if(e.password!==e.confirmPassword){d.warn(n("sys.login.diffPwd"));return}if(!e.policy){d.warn(n("sys.login.policyPlaceholder"));return}Z()}function Z(){return b(this,null,function*(){try{const s=yield L({username:e.username,password:e.password,phone:e.mobile,smsCode:e.smscode,roleCode:e.roleCode});s&&s.success===!0?(y.success({message:"注册成功",description:s.message||n("sys.api.registerMsg"),duration:3}),M("success",{username:e.username,password:e.password}),N()):y.warning({message:n("sys.api.errorTip"),description:(s==null?void 0:s.message)||n("sys.api.networkExceptionMsg"),duration:3})}catch(s){y.error({message:n("sys.api.errorTip"),description:(s==null?void 0:s.message)||n("sys.api.networkExceptionMsg"),duration:3})}})}function N(){Object.assign(e,{username:"",mobile:"",smscode:"",password:"",confirmPassword:"",policy:!1,roleCode:void 0}),a(m)||(R.value=!0,clearInterval(a(m)),m.value=null),h.value.resetFields()}function O(s){U.value=s}function D(s){T.value=s}return r({initForm:N}),(s,t)=>{const k=C("Icon"),I=C("a-input"),f=C("a-form-item"),J=C("a-select"),X=C("a-checkbox"),ee=C("a-form");return v(),_(ce,null,[o("div",_e,[o("div",ve,[o("div",ye,[o("div",he,[o("div",we,[o("img",{src:a(ge),alt:""},null,8,xe)])]),o("div",ke,[o("div",be,[i(ee,{ref_key:"formRef",ref:h,model:e},{default:p(()=>[o("div",Ce,[o("div",Pe,P(a(n)("sys.login.signUpFormTitle")),1)]),o("div",Ie,[o("div",Me,[i(f,null,{default:p(()=>[o("div",Re,[i(k,{class:"aui-icon",icon:"ant-design:user-outlined"}),i(I,{class:"fix-auto-fill",type:"text",placeholder:a(n)("sys.login.userName"),value:e.username,"onUpdate:value":t[0]||(t[0]=l=>e.username=l)},null,8,["placeholder","value"])])]),_:1}),i(f,null,{default:p(()=>[o("div",Ue,[i(k,{class:"aui-icon",icon:"ant-design:mobile-outlined"}),i(I,{class:"fix-auto-fill",type:"text",placeholder:a(n)("sys.login.mobile"),value:e.mobile,"onUpdate:value":t[1]||(t[1]=l=>e.mobile=l)},null,8,["placeholder","value"])])]),_:1}),i(f,null,{default:p(()=>[o("div",Te,[i(k,{class:"aui-icon",icon:"ant-design:team-outlined"}),i(J,{class:"fix-auto-fill aui-select",placeholder:"请选择角色类型",value:e.roleCode,"onUpdate:value":t[2]||(t[2]=l=>e.roleCode=l),options:w.value,fieldNames:{label:"label",value:"value"},getPopupContainer:l=>l.parentNode},null,8,["value","options","getPopupContainer"])])]),_:1}),i(f,null,{default:p(()=>[o("div",Ne,[i(k,{class:"aui-icon",icon:"ant-design:mail-outlined"}),i(I,{class:"fix-auto-fill",type:"text",placeholder:a(n)("sys.login.smsCode"),value:e.smscode,"onUpdate:value":t[3]||(t[3]=l=>e.smscode=l)},null,8,["placeholder","value"]),R.value?(v(),_("div",{key:0,class:"aui-code-line",onClick:E},P(a(n)("component.countdown.normalText")),1)):(v(),_("div",Be,P(a(n)("component.countdown.sendText",[a(x.value)])),1))])]),_:1}),i(f,null,{default:p(()=>[o("div",Ee,[i(k,{class:"aui-icon",icon:"ant-design:lock-outlined"}),i(I,{class:"fix-auto-fill",type:U.value==="close"?"password":"text",placeholder:a(n)("sys.login.password"),value:e.password,"onUpdate:value":t[4]||(t[4]=l=>e.password=l)},null,8,["type","placeholder","value"]),o("div",Oe,[U.value==="open"?(v(),_("img",{key:0,src:a(A),alt:"开启",onClick:t[5]||(t[5]=l=>O("close"))},null,8,De)):U.value==="close"?(v(),_("img",{key:1,src:a(V),alt:"关闭",onClick:t[6]||(t[6]=l=>O("open"))},null,8,Se)):$("",!0)])])]),_:1}),i(f,null,{default:p(()=>[o("div",Fe,[i(k,{class:"aui-icon",icon:"ant-design:lock-outlined"}),i(I,{class:"fix-auto-fill",type:T.value==="close"?"password":"text",placeholder:a(n)("sys.login.confirmPassword"),value:e.confirmPassword,"onUpdate:value":t[7]||(t[7]=l=>e.confirmPassword=l)},null,8,["type","placeholder","value"]),o("div",He,[T.value==="open"?(v(),_("img",{key:0,src:a(A),alt:"开启",onClick:t[8]||(t[8]=l=>D("close"))},null,8,je)):T.value==="close"?(v(),_("img",{key:1,src:a(V),alt:"关闭",onClick:t[9]||(t[9]=l=>D("open"))},null,8,$e)):$("",!0)])])]),_:1}),i(f,{name:"policy"},{default:p(()=>[o("div",Ae,[o("div",Ve,[o("div",Qe,[i(X,{checked:e.policy,"onUpdate:checked":t[10]||(t[10]=l=>e.policy=l)},null,8,["checked"]),o("span",Ye,P(a(n)("sys.login.policy")),1)])])])]),_:1})])]),o("div",ze,[o("div",Ge,[o("a",{class:"aui-link-login aui-flex-box btn",onClick:W},P(a(n)("sys.login.registerButton")),1)]),o("div",Ke,[o("a",{class:"aui-linek-code aui-flex-box",onClick:q},P(a(n)("sys.login.backSignIn")),1)])])]),_:1},8,["model"])])])])])]),i(fe,{onRegister:a(Y),onOk:E},null,8,["onRegister"])],64)}}})),We=me(qe,[["__scopeId","data-v-29a0bbcf"]]),os=Object.freeze(Object.defineProperty({__proto__:null,default:We},Symbol.toStringTag,{value:"Module"}));export{We as M,ge as a,os as b};
