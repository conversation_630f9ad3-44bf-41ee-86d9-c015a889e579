var c=(x,p,n)=>new Promise((s,r)=>{var u=e=>{try{a(n.next(e))}catch(t){r(t)}},o=e=>{try{a(n.throw(e))}catch(t){r(t)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(u,o);a((n=n.apply(x,p)).next())});import{d,f as i,ag as y,aB as k,ar as f}from"./vue-vendor-dy9k-Yad.js";import{bx as l,j as m}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const S=d({__name:"JVxeDemo4",setup(x){const p=i([{requestData:u,key:"s1"},{requestData:o,key:"menu1"}]),n=i([{title:"性别",key:"sex",type:l.select,dictCode:"sex",width:"180px",placeholder:"请选择${title}"},{title:"省/直辖市/自治区",key:"s1",type:l.select,width:"180px",placeholder:"请选择${title}",linkageKey:"s2"},{title:"市",key:"s2",type:l.select,width:"180px",placeholder:"请选择${title}",linkageKey:"s3"},{title:"县/区",key:"s3",type:l.select,width:"180px",options:[],placeholder:"请选择${title}"},{title:"一级菜单",key:"menu1",type:l.select,width:"180px",placeholder:"请选择${title}",linkageKey:"menu2"},{title:"二级菜单",key:"menu2",type:l.select,width:"180px",placeholder:"请选择${title}",linkageKey:"menu3"},{title:"三级菜单",key:"menu3",type:l.select,width:"180px",placeholder:"请选择${title}"}]),s=i([{sex:"1",s1:"110000",s2:"110100",s3:"110101"},{sex:"2",s1:"130000",s2:"130300",s3:"130303"}]),r=[{text:"北京市",value:"110000",parent:""},{text:"天津市",value:"120000",parent:""},{text:"河北省",value:"130000",parent:""},{text:"上海市",value:"310000",parent:""},{text:"北京市",value:"110100",parent:"110000"},{text:"天津市市",value:"120100",parent:"120000"},{text:"石家庄市",value:"130100",parent:"130000"},{text:"唐山市",value:"130200",parent:"130000"},{text:"秦皇岛市",value:"130300",parent:"130000"},{text:"上海市",value:"310100",parent:"310000"},{text:"东城区",value:"110101",parent:"110100"},{text:"西城区",value:"110102",parent:"110100"},{text:"朝阳区",value:"110105",parent:"110100"},{text:"和平区",value:"120101",parent:"120100"},{text:"河东区",value:"120102",parent:"120100"},{text:"河西区",value:"120103",parent:"120100"},{text:"黄浦区",value:"310101",parent:"310100"},{text:"徐汇区",value:"310104",parent:"310100"},{text:"长宁区",value:"310105",parent:"310100"},{text:"长安区",value:"130102",parent:"130100"},{text:"桥西区",value:"130104",parent:"130100"},{text:"新华区",value:"130105",parent:"130100"},{text:"路南区",value:"130202",parent:"130200"},{text:"路北区",value:"130203",parent:"130200"},{text:"古冶区",value:"130204",parent:"130200"},{text:"海港区",value:"130302",parent:"130300"},{text:"山海关区",value:"130303",parent:"130300"},{text:"北戴河区",value:"130304",parent:"130300"}];function u(a){return new Promise(e=>{let t=r.filter(v=>v.parent===a);setTimeout(()=>e(t),500)})}function o(a){return c(this,null,function*(){let e;return a===""?e=yield m.get({url:"/sys/permission/getSystemMenuList",params:{}}):e=yield m.get({url:"/sys/permission/getSystemSubmenu",params:{parentId:a}}),e.map(t=>({value:t.id,text:t.name}))})}return(a,e)=>{const t=y("JVxeTable");return f(),k(t,{ref:"vTable",toolbar:"",rowNumber:"",rowSelection:"",maxHeight:580,dataSource:s.value,columns:n.value,linkageConfig:p.value},null,8,["dataSource","columns","linkageConfig"])}}});export{S as default};
