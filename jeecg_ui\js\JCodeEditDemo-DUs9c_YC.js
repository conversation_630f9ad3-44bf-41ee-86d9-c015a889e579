import{d,f as a,e as h,u as f,ag as n,aB as C,ar as B,aD as _,k as S}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{x as b,u as v,a as g}from"./index-CCWaWN5g.js";import{o as k}from"./select-nVA4yav1.js";import E from"./JCodeEditor-B-WXz11X.js";import{B as J}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */const w=[{field:"field1",component:"JCodeEditor",label:"代码编辑器",required:!0,slot:"jCodeEdit",colProps:{span:15},defaultValue:"Hello JeecgBoot"}],R=d({components:{BasicForm:J,JCodeEditor:E},setup(){const o=a(null),{createMessage:r}=v(),t=a(""),i=h(()=>({keyword:f(t)}));function m(e){t.value=e}return{schemas:w,optionsListApi:k,onSearch:b(m,300),searchParams:i,handleReset:()=>{t.value=""},handleSubmit:e=>{r.success("click search,values:"+JSON.stringify(e))},check:o}}});function x(o,r,t,i,m,e){const c=n("JCodeEditor"),u=n("BasicForm");return B(),C(u,{labelWidth:200,schemas:o.schemas,showResetButton:!1,showSubmitButton:!1,actionColOptions:{span:24},onSubmit:o.handleSubmit,onReset:o.handleReset,style:{height:"800px"}},{jCodeEdit:_(({model:p,field:s})=>[S(c,{value:p[s],"onUpdate:value":l=>p[s]=l,mode:"js",height:"300px",fullScreen:!0},null,8,["value","onUpdate:value"])]),_:1},8,["schemas","onSubmit","onReset"])}const $o=g(R,[["render",x]]);export{$o as default};
