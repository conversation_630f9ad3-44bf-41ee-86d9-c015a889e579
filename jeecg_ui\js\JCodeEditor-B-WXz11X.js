var $e=Object.defineProperty,qe=Object.defineProperties;var We=Object.getOwnPropertyDescriptors;var ve=Object.getOwnPropertySymbols;var Ve=Object.prototype.hasOwnProperty,Ue=Object.prototype.propertyIsEnumerable;var me=(y,H,i)=>H in y?$e(y,H,{enumerable:!0,configurable:!0,writable:!0,value:i}):y[H]=i,be=(y,H)=>{for(var i in H||(H={}))Ve.call(H,i)&&me(y,i,H[i]);if(ve)for(var i of ve(H))Ue.call(H,i)&&me(y,i,H[i]);return y},ye=(y,H)=>qe(y,We(H));import{d as De,f as $,r as Ge,e as xe,w as ae,o as ze,u as ke,aI as Ke,ag as Me,aq as Je,ar as we,aE as Ae,aB as Xe,ah as Ye,at as Qe}from"./vue-vendor-dy9k-Yad.js";import{D as Ze,F as et,a9 as tt,ay as nt,az as it,H as B,a as rt}from"./index-CCWaWN5g.js";import{useRuleFormItem as ot}from"./useFormItem-CHvpjy4o.js";import{r as I,C as st}from"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import{a as Tt}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var q={exports:{}},St=q.exports,Le;function Be(){return Le||(Le=1,function(y,H){(function(i){i(I())})(function(i){"use strict";function A(f,c,r,l){if(r&&r.call){var p=r;r=null}else var p=g(f,r,"rangeFinder");typeof c=="number"&&(c=i.Pos(c,0));var s=g(f,r,"minFoldSize");function d(e){var n=p(f,c);if(!n||n.to.line-n.from.line<s)return null;if(l==="fold")return n;for(var t=f.findMarksAt(n.from),a=0;a<t.length;++a)if(t[a].__isFold){if(!e)return null;n.cleared=!0,t[a].clear()}return n}var h=d(!0);if(g(f,r,"scanUp"))for(;!h&&c.line>f.firstLine();)c=i.Pos(c.line-1,0),h=d(!1);if(!(!h||h.cleared||l==="unfold")){var x=b(f,r,h);i.on(x,"mousedown",function(e){o.clear(),i.e_preventDefault(e)});var o=f.markText(h.from,h.to,{replacedWith:x,clearOnEnter:g(f,r,"clearOnEnter"),__isFold:!0});o.on("clear",function(e,n){i.signal(f,"unfold",f,e,n)}),i.signal(f,"fold",f,h.from,h.to)}}function b(f,c,r){var l=g(f,c,"widget");if(typeof l=="function"&&(l=l(r.from,r.to)),typeof l=="string"){var p=document.createTextNode(l);l=document.createElement("span"),l.appendChild(p),l.className="CodeMirror-foldmarker"}else l&&(l=l.cloneNode(!0));return l}i.newFoldFunction=function(f,c){return function(r,l){A(r,l,{rangeFinder:f,widget:c})}},i.defineExtension("foldCode",function(f,c,r){A(this,f,c,r)}),i.defineExtension("isFolded",function(f){for(var c=this.findMarksAt(f),r=0;r<c.length;++r)if(c[r].__isFold)return!0}),i.commands.toggleFold=function(f){f.foldCode(f.getCursor())},i.commands.fold=function(f){f.foldCode(f.getCursor(),null,"fold")},i.commands.unfold=function(f){f.foldCode(f.getCursor(),{scanUp:!1},"unfold")},i.commands.foldAll=function(f){f.operation(function(){for(var c=f.firstLine(),r=f.lastLine();c<=r;c++)f.foldCode(i.Pos(c,0),{scanUp:!1},"fold")})},i.commands.unfoldAll=function(f){f.operation(function(){for(var c=f.firstLine(),r=f.lastLine();c<=r;c++)f.foldCode(i.Pos(c,0),{scanUp:!1},"unfold")})},i.registerHelper("fold","combine",function(){var f=Array.prototype.slice.call(arguments,0);return function(c,r){for(var l=0;l<f.length;++l){var p=f[l](c,r);if(p)return p}}}),i.registerHelper("fold","auto",function(f,c){for(var r=f.getHelpers(c,"fold"),l=0;l<r.length;l++){var p=r[l](f,c);if(p)return p}});var w={rangeFinder:i.fold.auto,widget:"↔",minFoldSize:0,scanUp:!1,clearOnEnter:!0};i.defineOption("foldOptions",null);function g(f,c,r){if(c&&c[r]!==void 0)return c[r];var l=f.options.foldOptions;return l&&l[r]!==void 0?l[r]:w[r]}i.defineExtension("foldOption",function(f,c){return g(this,f,c)})})}(q,q.exports)),q.exports}var Ot=Be();var W={exports:{}},Ct=W.exports,He;function at(){return He||(He=1,function(y,H){(function(i){i(I())})(function(i){"use strict";function A(b){return function(w,g){var f=g.line,c=w.getLine(f);function r(x){for(var o,e=g.ch,n=0;;){var t=e<=0?-1:c.lastIndexOf(x[0],e-1);if(t==-1){if(n==1)break;n=1,e=c.length;continue}if(n==1&&t<g.ch)break;if(o=w.getTokenTypeAt(i.Pos(f,t+1)),!/^(comment|string)/.test(o))return{ch:t+1,tokenType:o,pair:x};e=t-1}}function l(x){var o=1,e=w.lastLine(),n,t=x.ch,a;e:for(var u=f;u<=e;++u)for(var m=w.getLine(u),v=u==f?t:0;;){var L=m.indexOf(x.pair[0],v),F=m.indexOf(x.pair[1],v);if(L<0&&(L=m.length),F<0&&(F=m.length),v=Math.min(L,F),v==m.length)break;if(w.getTokenTypeAt(i.Pos(u,v+1))==x.tokenType){if(v==L)++o;else if(!--o){n=u,a=v;break e}}++v}return n==null||f==n?null:{from:i.Pos(f,t),to:i.Pos(n,a)}}for(var p=[],s=0;s<b.length;s++){var d=r(b[s]);d&&p.push(d)}p.sort(function(x,o){return x.ch-o.ch});for(var s=0;s<p.length;s++){var h=l(p[s]);if(h)return h}return null}}i.registerHelper("fold","brace",A([["{","}"],["[","]"]])),i.registerHelper("fold","brace-paren",A([["{","}"],["[","]"],["(",")"]])),i.registerHelper("fold","import",function(b,w){function g(s){if(s<b.firstLine()||s>b.lastLine())return null;var d=b.getTokenAt(i.Pos(s,1));if(/\S/.test(d.string)||(d=b.getTokenAt(i.Pos(s,d.end+1))),d.type!="keyword"||d.string!="import")return null;for(var h=s,x=Math.min(b.lastLine(),s+10);h<=x;++h){var o=b.getLine(h),e=o.indexOf(";");if(e!=-1)return{startCh:d.end,end:i.Pos(h,e)}}}var f=w.line,c=g(f),r;if(!c||g(f-1)||(r=g(f-2))&&r.end.line==f-1)return null;for(var l=c.end;;){var p=g(l.line+1);if(p==null)break;l=p.end}return{from:b.clipPos(i.Pos(f,c.startCh+1)),to:l}}),i.registerHelper("fold","include",function(b,w){function g(p){if(p<b.firstLine()||p>b.lastLine())return null;var s=b.getTokenAt(i.Pos(p,1));if(/\S/.test(s.string)||(s=b.getTokenAt(i.Pos(p,s.end+1))),s.type=="meta"&&s.string.slice(0,8)=="#include")return s.start+8}var f=w.line,c=g(f);if(c==null||g(f-1)!=null)return null;for(var r=f;;){var l=g(r+1);if(l==null)break;++r}return{from:i.Pos(f,c+1),to:b.clipPos(i.Pos(r))}})})}(W,W.exports)),W.exports}var Et=at();var V={exports:{}},Pt=V.exports,Te;function lt(){return Te||(Te=1,function(y,H){(function(i){i(I())})(function(i){"use strict";i.registerGlobalHelper("fold","comment",function(A){return A.blockCommentStart&&A.blockCommentEnd},function(A,b){var w=A.getModeAt(b),g=w.blockCommentStart,f=w.blockCommentEnd;if(!(!g||!f)){for(var c=b.line,r=A.getLine(c),l,p=b.ch,s=0;;){var d=p<=0?-1:r.lastIndexOf(g,p-1);if(d==-1){if(s==1)return;s=1,p=r.length;continue}if(s==1&&d<b.ch)return;if(/comment/.test(A.getTokenTypeAt(i.Pos(c,d+1)))&&(d==0||r.slice(d-f.length,d)==f||!/comment/.test(A.getTokenTypeAt(i.Pos(c,d))))){l=d+g.length;break}p=d-1}var h=1,x=A.lastLine(),o,e;e:for(var n=c;n<=x;++n)for(var t=A.getLine(n),a=n==c?l:0;;){var u=t.indexOf(g,a),m=t.indexOf(f,a);if(u<0&&(u=t.length),m<0&&(m=t.length),a=Math.min(u,m),a==t.length)break;if(a==u)++h;else if(!--h){o=n,e=a;break e}++a}if(!(o==null||c==o&&e==l))return{from:i.Pos(c,l),to:i.Pos(o,e)}}})})}(V,V.exports)),V.exports}var It=lt();var U={exports:{}},jt=U.exports,Fe;function ft(){return Fe||(Fe=1,function(y,H){(function(i){i(I())})(function(i){"use strict";function A(b,w){var g=b.getLine(w),f=g.search(/\S/);return f==-1||/\bcomment\b/.test(b.getTokenTypeAt(i.Pos(w,f+1)))?-1:i.countColumn(g,null,b.getOption("tabSize"))}i.registerHelper("fold","indent",function(b,w){var g=A(b,w.line);if(!(g<0)){for(var f=null,c=w.line+1,r=b.lastLine();c<=r;++c){var l=A(b,c);if(l!=-1)if(l>g)f=c;else break}if(f)return{from:i.Pos(w.line,b.getLine(w.line).length),to:i.Pos(f,b.getLine(f).length)}}})})}(U,U.exports)),U.exports}var Rt=ft();var D={exports:{}},Bt=D.exports,Se;function ut(){return Se||(Se=1,function(y,H){(function(i){i(I(),Be())})(function(i){"use strict";i.defineOption("foldGutter",!1,function(o,e,n){n&&n!=i.Init&&(o.clearGutter(o.state.foldGutter.options.gutter),o.state.foldGutter=null,o.off("gutterClick",p),o.off("changes",d),o.off("viewportChange",h),o.off("fold",x),o.off("unfold",x),o.off("swapDoc",d),o.off("optionChange",s)),e&&(o.state.foldGutter=new b(w(e)),l(o),o.on("gutterClick",p),o.on("changes",d),o.on("viewportChange",h),o.on("fold",x),o.on("unfold",x),o.on("swapDoc",d),o.on("optionChange",s))});var A=i.Pos;function b(o){this.options=o,this.from=this.to=0}function w(o){return o===!0&&(o={}),o.gutter==null&&(o.gutter="CodeMirror-foldgutter"),o.indicatorOpen==null&&(o.indicatorOpen="CodeMirror-foldgutter-open"),o.indicatorFolded==null&&(o.indicatorFolded="CodeMirror-foldgutter-folded"),o}function g(o,e){for(var n=o.findMarks(A(e,0),A(e+1,0)),t=0;t<n.length;++t)if(n[t].__isFold){var a=n[t].find(-1);if(a&&a.line===e)return n[t]}}function f(o){if(typeof o=="string"){var e=document.createElement("div");return e.className=o+" CodeMirror-guttermarker-subtle",e}else return o.cloneNode(!0)}function c(o,e,n){var t=o.state.foldGutter.options,a=e-1,u=o.foldOption(t,"minFoldSize"),m=o.foldOption(t,"rangeFinder"),v=typeof t.indicatorFolded=="string"&&r(t.indicatorFolded),L=typeof t.indicatorOpen=="string"&&r(t.indicatorOpen);o.eachLine(e,n,function(F){++a;var S=null,T=F.gutterMarkers;if(T&&(T=T[t.gutter]),g(o,a)){if(v&&T&&v.test(T.className))return;S=f(t.indicatorFolded)}else{var k=A(a,0),O=m&&m(o,k);if(O&&O.to.line-O.from.line>=u){if(L&&T&&L.test(T.className))return;S=f(t.indicatorOpen)}}!S&&!T||o.setGutterMarker(F,t.gutter,S)})}function r(o){return new RegExp("(^|\\s)"+o+"(?:$|\\s)\\s*")}function l(o){var e=o.getViewport(),n=o.state.foldGutter;n&&(o.operation(function(){c(o,e.from,e.to)}),n.from=e.from,n.to=e.to)}function p(o,e,n){var t=o.state.foldGutter;if(t){var a=t.options;if(n==a.gutter){var u=g(o,e);u?u.clear():o.foldCode(A(e,0),a)}}}function s(o,e){e=="mode"&&d(o)}function d(o){var e=o.state.foldGutter;if(e){var n=e.options;e.from=e.to=0,clearTimeout(e.changeUpdate),e.changeUpdate=setTimeout(function(){l(o)},n.foldOnChangeTimeSpan||600)}}function h(o){var e=o.state.foldGutter;if(e){var n=e.options;clearTimeout(e.changeUpdate),e.changeUpdate=setTimeout(function(){var t=o.getViewport();e.from==e.to||t.from-e.to>20||e.from-t.to>20?l(o):o.operation(function(){t.from<e.from&&(c(o,t.from,e.from),e.from=t.from),t.to>e.to&&(c(o,e.to,t.to),e.to=t.to)})},n.updateViewportTimeSpan||400)}}function x(o,e){var n=o.state.foldGutter;if(n){var t=e.line;t>=n.from&&t<n.to&&c(o,t,t+1)}}})}(D,D.exports)),D.exports}var Nt=ut();var G={exports:{}},_t=G.exports,Oe;function ct(){return Oe||(Oe=1,function(y,H){(function(i){i(I())})(function(i){"use strict";var A="CodeMirror-activeline",b="CodeMirror-activeline-background",w="CodeMirror-activeline-gutter";i.defineOption("styleActiveLine",!1,function(l,p,s){var d=s==i.Init?!1:s;p!=d&&(d&&(l.off("beforeSelectionChange",r),g(l),delete l.state.activeLines),p&&(l.state.activeLines=[],c(l,l.listSelections()),l.on("beforeSelectionChange",r)))});function g(l){for(var p=0;p<l.state.activeLines.length;p++)l.removeLineClass(l.state.activeLines[p],"wrap",A),l.removeLineClass(l.state.activeLines[p],"background",b),l.removeLineClass(l.state.activeLines[p],"gutter",w)}function f(l,p){if(l.length!=p.length)return!1;for(var s=0;s<l.length;s++)if(l[s]!=p[s])return!1;return!0}function c(l,p){for(var s=[],d=0;d<p.length;d++){var h=p[d],x=l.getOption("styleActiveLine");if(!(typeof x=="object"&&x.nonEmpty?h.anchor.line!=h.head.line:!h.empty())){var o=l.getLineHandleVisualStart(h.head.line);s[s.length-1]!=o&&s.push(o)}}f(l.state.activeLines,s)||l.operation(function(){g(l);for(var e=0;e<s.length;e++)l.addLineClass(s[e],"wrap",A),l.addLineClass(s[e],"background",b),l.addLineClass(s[e],"gutter",w);l.state.activeLines=s})}function r(l,p){c(l,p.ranges)}})}(G,G.exports)),G.exports}var $t=ct();var z={exports:{}},qt=z.exports,Ce;function dt(){return Ce||(Ce=1,function(y,H){(function(i){i(I())})(function(i){"use strict";var A="CodeMirror-hint",b="CodeMirror-hint-active";i.showHint=function(e,n,t){if(!n)return e.showHint(t);t&&t.async&&(n.async=!0);var a={hint:n};if(t)for(var u in t)a[u]=t[u];return e.showHint(a)},i.defineExtension("showHint",function(e){e=c(this,this.getCursor("start"),e);var n=this.listSelections();if(!(n.length>1)){if(this.somethingSelected()){if(!e.hint.supportsSelection)return;for(var t=0;t<n.length;t++)if(n[t].head.line!=n[t].anchor.line)return}this.state.completionActive&&this.state.completionActive.close();var a=this.state.completionActive=new w(this,e);a.options.hint&&(i.signal(this,"startCompletion",this),a.update(!0))}}),i.defineExtension("closeHint",function(){this.state.completionActive&&this.state.completionActive.close()});function w(e,n){if(this.cm=e,this.options=n,this.widget=null,this.debounce=0,this.tick=0,this.startPos=this.cm.getCursor("start"),this.startLen=this.cm.getLine(this.startPos.line).length-this.cm.getSelection().length,this.options.updateOnCursorActivity){var t=this;e.on("cursorActivity",this.activityFunc=function(){t.cursorActivity()})}}var g=window.requestAnimationFrame||function(e){return setTimeout(e,1e3/60)},f=window.cancelAnimationFrame||clearTimeout;w.prototype={close:function(){this.active()&&(this.cm.state.completionActive=null,this.tick=null,this.options.updateOnCursorActivity&&this.cm.off("cursorActivity",this.activityFunc),this.widget&&this.data&&i.signal(this.data,"close"),this.widget&&this.widget.close(),i.signal(this.cm,"endCompletion",this.cm))},active:function(){return this.cm.state.completionActive==this},pick:function(e,n){var t=e.list[n],a=this;this.cm.operation(function(){t.hint?t.hint(a.cm,e,t):a.cm.replaceRange(r(t),t.from||e.from,t.to||e.to,"complete"),i.signal(e,"pick",t),a.cm.scrollIntoView()}),this.options.closeOnPick&&this.close()},cursorActivity:function(){this.debounce&&(f(this.debounce),this.debounce=0);var e=this.startPos;this.data&&(e=this.data.from);var n=this.cm.getCursor(),t=this.cm.getLine(n.line);if(n.line!=this.startPos.line||t.length-n.ch!=this.startLen-this.startPos.ch||n.ch<e.ch||this.cm.somethingSelected()||!n.ch||this.options.closeCharacters.test(t.charAt(n.ch-1)))this.close();else{var a=this;this.debounce=g(function(){a.update()}),this.widget&&this.widget.disable()}},update:function(e){if(this.tick!=null){var n=this,t=++this.tick;h(this.options.hint,this.cm,this.options,function(a){n.tick==t&&n.finishUpdate(a,e)})}},finishUpdate:function(e,n){this.data&&i.signal(this.data,"update");var t=this.widget&&this.widget.picked||n&&this.options.completeSingle;this.widget&&this.widget.close(),this.data=e,e&&e.list.length&&(t&&e.list.length==1?this.pick(e,0):(this.widget=new s(this,e),i.signal(e,"shown")))}};function c(e,n,t){var a=e.options.hintOptions,u={};for(var m in o)u[m]=o[m];if(a)for(var m in a)a[m]!==void 0&&(u[m]=a[m]);if(t)for(var m in t)t[m]!==void 0&&(u[m]=t[m]);return u.hint.resolve&&(u.hint=u.hint.resolve(e,n)),u}function r(e){return typeof e=="string"?e:e.text}function l(e,n){var t={Up:function(){n.moveFocus(-1)},Down:function(){n.moveFocus(1)},PageUp:function(){n.moveFocus(-n.menuSize()+1,!0)},PageDown:function(){n.moveFocus(n.menuSize()-1,!0)},Home:function(){n.setFocus(0)},End:function(){n.setFocus(n.length-1)},Enter:n.pick,Tab:n.pick,Esc:n.close},a=/Mac/.test(navigator.platform);a&&(t["Ctrl-P"]=function(){n.moveFocus(-1)},t["Ctrl-N"]=function(){n.moveFocus(1)});var u=e.options.customKeys,m=u?{}:t;function v(S,T){var k;typeof T!="string"?k=function(O){return T(O,n)}:t.hasOwnProperty(T)?k=t[T]:k=T,m[S]=k}if(u)for(var L in u)u.hasOwnProperty(L)&&v(L,u[L]);var F=e.options.extraKeys;if(F)for(var L in F)F.hasOwnProperty(L)&&v(L,F[L]);return m}function p(e,n){for(;n&&n!=e;){if(n.nodeName.toUpperCase()==="LI"&&n.parentNode==e)return n;n=n.parentNode}}function s(e,n){this.id="cm-complete-"+Math.floor(Math.random(1e6)),this.completion=e,this.data=n,this.picked=!1;var t=this,a=e.cm,u=a.getInputField().ownerDocument,m=u.defaultView||u.parentWindow,v=this.hints=u.createElement("ul");v.setAttribute("role","listbox"),v.setAttribute("aria-expanded","true"),v.id=this.id;var L=e.cm.options.theme;v.className="CodeMirror-hints "+L,this.selectedHint=n.selectedHint||0;for(var F=n.list,S=0;S<F.length;++S){var T=v.appendChild(u.createElement("li")),k=F[S],O=A+(S!=this.selectedHint?"":" "+b);k.className!=null&&(O=k.className+" "+O),T.className=O,S==this.selectedHint&&T.setAttribute("aria-selected","true"),T.id=this.id+"-"+S,T.setAttribute("role","option"),k.render?k.render(T,n,k):T.appendChild(u.createTextNode(k.displayText||r(k))),T.hintId=S}var R=e.options.container||u.body,j=a.cursorCoords(e.options.alignWithWord?n.from:null),N=j.left,Z=j.bottom,fe=!0,ee=0,te=0;if(R!==u.body){var Ne=["absolute","relative","fixed"].indexOf(m.getComputedStyle(R).position)!==-1,ne=Ne?R:R.offsetParent,ue=ne.getBoundingClientRect(),ce=u.body.getBoundingClientRect();ee=ue.left-ce.left-ne.scrollLeft,te=ue.top-ce.top-ne.scrollTop}v.style.left=N-ee+"px",v.style.top=Z-te+"px";var X=m.innerWidth||Math.max(u.body.offsetWidth,u.documentElement.offsetWidth),ie=m.innerHeight||Math.max(u.body.offsetHeight,u.documentElement.offsetHeight);R.appendChild(v),a.getInputField().setAttribute("aria-autocomplete","list"),a.getInputField().setAttribute("aria-owns",this.id),a.getInputField().setAttribute("aria-activedescendant",this.id+"-"+this.selectedHint);var P=e.options.moveOnOverlap?v.getBoundingClientRect():new DOMRect,de=e.options.paddingForScrollbar?v.scrollHeight>v.clientHeight+1:!1,_;setTimeout(function(){_=a.getScrollInfo()});var _e=P.bottom-ie;if(_e>0){var re=P.bottom-P.top,oe=P.top-(j.bottom-j.top)-2;ie-P.top<oe?(re>oe&&(v.style.height=(re=oe)+"px"),v.style.top=(Z=j.top-re)+te+"px",fe=!1):v.style.height=ie-P.top-2+"px"}var Y=P.right-X;if(de&&(Y+=a.display.nativeBarWidth),Y>0&&(P.right-P.left>X&&(v.style.width=X-5+"px",Y-=P.right-P.left-X),v.style.left=(N=Math.max(j.left-Y-ee,0))+"px"),de)for(var Q=v.firstChild;Q;Q=Q.nextSibling)Q.style.paddingRight=a.display.nativeBarWidth+"px";if(a.addKeyMap(this.keyMap=l(e,{moveFocus:function(C,E){t.changeActive(t.selectedHint+C,E)},setFocus:function(C){t.changeActive(C)},menuSize:function(){return t.screenAmount()},length:F.length,close:function(){e.close()},pick:function(){t.pick()},data:n})),e.options.closeOnUnfocus){var he;a.on("blur",this.onBlur=function(){he=setTimeout(function(){e.close()},100)}),a.on("focus",this.onFocus=function(){clearTimeout(he)})}a.on("scroll",this.onScroll=function(){var C=a.getScrollInfo(),E=a.getWrapperElement().getBoundingClientRect();_||(_=a.getScrollInfo());var ge=Z+_.top-C.top,se=ge-(m.pageYOffset||(u.documentElement||u.body).scrollTop);if(fe||(se+=v.offsetHeight),se<=E.top||se>=E.bottom)return e.close();v.style.top=ge+"px",v.style.left=N+_.left-C.left+"px"}),i.on(v,"dblclick",function(C){var E=p(v,C.target||C.srcElement);E&&E.hintId!=null&&(t.changeActive(E.hintId),t.pick())}),i.on(v,"click",function(C){var E=p(v,C.target||C.srcElement);E&&E.hintId!=null&&(t.changeActive(E.hintId),e.options.completeOnSingleClick&&t.pick())}),i.on(v,"mousedown",function(){setTimeout(function(){a.focus()},20)});var pe=this.getSelectedHintRange();return(pe.from!==0||pe.to!==0)&&this.scrollToActive(),i.signal(n,"select",F[this.selectedHint],v.childNodes[this.selectedHint]),!0}s.prototype={close:function(){if(this.completion.widget==this){this.completion.widget=null,this.hints.parentNode&&this.hints.parentNode.removeChild(this.hints),this.completion.cm.removeKeyMap(this.keyMap);var e=this.completion.cm.getInputField();e.removeAttribute("aria-activedescendant"),e.removeAttribute("aria-owns");var n=this.completion.cm;this.completion.options.closeOnUnfocus&&(n.off("blur",this.onBlur),n.off("focus",this.onFocus)),n.off("scroll",this.onScroll)}},disable:function(){this.completion.cm.removeKeyMap(this.keyMap);var e=this;this.keyMap={Enter:function(){e.picked=!0}},this.completion.cm.addKeyMap(this.keyMap)},pick:function(){this.completion.pick(this.data,this.selectedHint)},changeActive:function(e,n){if(e>=this.data.list.length?e=n?this.data.list.length-1:0:e<0&&(e=n?0:this.data.list.length-1),this.selectedHint!=e){var t=this.hints.childNodes[this.selectedHint];t&&(t.className=t.className.replace(" "+b,""),t.removeAttribute("aria-selected")),t=this.hints.childNodes[this.selectedHint=e],t.className+=" "+b,t.setAttribute("aria-selected","true"),this.completion.cm.getInputField().setAttribute("aria-activedescendant",t.id),this.scrollToActive(),i.signal(this.data,"select",this.data.list[this.selectedHint],t)}},scrollToActive:function(){var e=this.getSelectedHintRange(),n=this.hints.childNodes[e.from],t=this.hints.childNodes[e.to],a=this.hints.firstChild;n.offsetTop<this.hints.scrollTop?this.hints.scrollTop=n.offsetTop-a.offsetTop:t.offsetTop+t.offsetHeight>this.hints.scrollTop+this.hints.clientHeight&&(this.hints.scrollTop=t.offsetTop+t.offsetHeight-this.hints.clientHeight+a.offsetTop)},screenAmount:function(){return Math.floor(this.hints.clientHeight/this.hints.firstChild.offsetHeight)||1},getSelectedHintRange:function(){var e=this.completion.options.scrollMargin||0;return{from:Math.max(0,this.selectedHint-e),to:Math.min(this.data.list.length-1,this.selectedHint+e)}}};function d(e,n){if(!e.somethingSelected())return n;for(var t=[],a=0;a<n.length;a++)n[a].supportsSelection&&t.push(n[a]);return t}function h(e,n,t,a){if(e.async)e(n,a,t);else{var u=e(n,t);u&&u.then?u.then(a):a(u)}}function x(e,n){var t=e.getHelpers(n,"hint"),a;if(t.length){var u=function(m,v,L){var F=d(m,t);function S(T){if(T==F.length)return v(null);h(F[T],m,L,function(k){k&&k.list.length>0?v(k):S(T+1)})}S(0)};return u.async=!0,u.supportsSelection=!0,u}else return(a=e.getHelper(e.getCursor(),"hintWords"))?function(m){return i.hint.fromList(m,{words:a})}:i.hint.anyword?function(m,v){return i.hint.anyword(m,v)}:function(){}}i.registerHelper("hint","auto",{resolve:x}),i.registerHelper("hint","fromList",function(e,n){var t=e.getCursor(),a=e.getTokenAt(t),u,m=i.Pos(t.line,a.start),v=t;a.start<t.ch&&/\w/.test(a.string.charAt(t.ch-a.start-1))?u=a.string.substr(0,t.ch-a.start):(u="",m=t);for(var L=[],F=0;F<n.words.length;F++){var S=n.words[F];S.slice(0,u.length)==u&&L.push(S)}if(L.length)return{list:L,from:m,to:v}}),i.commands.autocomplete=i.showHint;var o={hint:i.hint.auto,completeSingle:!0,alignWithWord:!0,closeCharacters:/[\s()\[\]{};:>,]/,closeOnPick:!0,closeOnUnfocus:!0,updateOnCursorActivity:!0,completeOnSingleClick:!0,container:null,customKeys:null,extraKeys:null,paddingForScrollbar:!0,moveOnOverlap:!0};i.defineOption("hintOptions",null)})}(z,z.exports)),z.exports}var Wt=dt();var K={exports:{}},Vt=K.exports,Ee;function ht(){return Ee||(Ee=1,function(y,H){(function(i){i(I())})(function(i){"use strict";var A=/[\w$]+/,b=500;i.registerHelper("hint","anyword",function(w,g){for(var f=g&&g.word||A,c=g&&g.range||b,r=w.getCursor(),l=w.getLine(r.line),p=r.ch,s=p;s&&f.test(l.charAt(s-1));)--s;for(var d=s!=p&&l.slice(s,p),h=g&&g.list||[],x={},o=new RegExp(f.source,"g"),e=-1;e<=1;e+=2)for(var n=r.line,t=Math.min(Math.max(n+e*c,w.firstLine()),w.lastLine())+e;n!=t;n+=e)for(var a=w.getLine(n),u;u=o.exec(a);)n==r.line&&u[0]===d||(!d||u[0].lastIndexOf(d,0)==0)&&!Object.prototype.hasOwnProperty.call(x,u[0])&&(x[u[0]]=!0,h.push(u[0]));return{list:h,from:i.Pos(r.line,s),to:i.Pos(r.line,p)}})})}(K,K.exports)),K.exports}var Ut=ht();var M={exports:{}},Dt=M.exports,Pe;function pt(){return Pe||(Pe=1,function(y,H){(function(i){i(I())})(function(i){var A=/MSIE \d/.test(navigator.userAgent)&&(document.documentMode==null||document.documentMode<8),b=i.Pos,w={"(":")>",")":"(<","[":"]>","]":"[<","{":"}>","}":"{<","<":">>",">":"<<"};function g(s){return s&&s.bracketRegex||/[(){}[\]]/}function f(s,d,h){var x=s.getLineHandle(d.line),o=d.ch-1,e=h&&h.afterCursor;e==null&&(e=/(^| )cm-fat-cursor($| )/.test(s.getWrapperElement().className));var n=g(h),t=!e&&o>=0&&n.test(x.text.charAt(o))&&w[x.text.charAt(o)]||n.test(x.text.charAt(o+1))&&w[x.text.charAt(++o)];if(!t)return null;var a=t.charAt(1)==">"?1:-1;if(h&&h.strict&&a>0!=(o==d.ch))return null;var u=s.getTokenTypeAt(b(d.line,o+1)),m=c(s,b(d.line,o+(a>0?1:0)),a,u,h);return m==null?null:{from:b(d.line,o),to:m&&m.pos,match:m&&m.ch==t.charAt(0),forward:a>0}}function c(s,d,h,x,o){for(var e=o&&o.maxScanLineLength||1e4,n=o&&o.maxScanLines||1e3,t=[],a=g(o),u=h>0?Math.min(d.line+n,s.lastLine()+1):Math.max(s.firstLine()-1,d.line-n),m=d.line;m!=u;m+=h){var v=s.getLine(m);if(v){var L=h>0?0:v.length-1,F=h>0?v.length:-1;if(!(v.length>e))for(m==d.line&&(L=d.ch-(h<0?1:0));L!=F;L+=h){var S=v.charAt(L);if(a.test(S)&&(x===void 0||(s.getTokenTypeAt(b(m,L+1))||"")==(x||""))){var T=w[S];if(T&&T.charAt(1)==">"==h>0)t.push(S);else if(t.length)t.pop();else return{pos:b(m,L),ch:S}}}}}return m-h==(h>0?s.lastLine():s.firstLine())?!1:null}function r(s,d,h){for(var x=s.state.matchBrackets.maxHighlightLineLength||1e3,o=h&&h.highlightNonMatching,e=[],n=s.listSelections(),t=0;t<n.length;t++){var a=n[t].empty()&&f(s,n[t].head,h);if(a&&(a.match||o!==!1)&&s.getLine(a.from.line).length<=x){var u=a.match?"CodeMirror-matchingbracket":"CodeMirror-nonmatchingbracket";e.push(s.markText(a.from,b(a.from.line,a.from.ch+1),{className:u})),a.to&&s.getLine(a.to.line).length<=x&&e.push(s.markText(a.to,b(a.to.line,a.to.ch+1),{className:u}))}}if(e.length){A&&s.state.focused&&s.focus();var m=function(){s.operation(function(){for(var v=0;v<e.length;v++)e[v].clear()})};if(d)setTimeout(m,800);else return m}}function l(s){s.operation(function(){s.state.matchBrackets.currentlyHighlighted&&(s.state.matchBrackets.currentlyHighlighted(),s.state.matchBrackets.currentlyHighlighted=null),s.state.matchBrackets.currentlyHighlighted=r(s,!1,s.state.matchBrackets)})}function p(s){s.state.matchBrackets&&s.state.matchBrackets.currentlyHighlighted&&(s.state.matchBrackets.currentlyHighlighted(),s.state.matchBrackets.currentlyHighlighted=null)}i.defineOption("matchBrackets",!1,function(s,d,h){h&&h!=i.Init&&(s.off("cursorActivity",l),s.off("focus",l),s.off("blur",p),p(s)),d&&(s.state.matchBrackets=typeof d=="object"?d:{},s.on("cursorActivity",l),s.on("focus",l),s.on("blur",p))}),i.defineExtension("matchBrackets",function(){r(this,!0)}),i.defineExtension("findMatchingBracket",function(s,d,h){return(h||typeof d=="boolean")&&(h?(h.strict=d,d=h):d=d?{strict:!0}:null),f(this,s,d)}),i.defineExtension("scanForBracket",function(s,d,h,x){return c(this,s,d,h,x)})})}(M,M.exports)),M.exports}var Gt=pt();var J={exports:{}},zt=J.exports,Ie;function gt(){return Ie||(Ie=1,function(y,H){(function(i){i(I())})(function(i){i.defineOption("placeholder","",function(r,l,p){var s=p&&p!=i.Init;if(l&&!s)r.on("blur",g),r.on("change",f),r.on("swapDoc",f),i.on(r.getInputField(),"compositionupdate",r.state.placeholderCompose=function(){w(r)}),f(r);else if(!l&&s){r.off("blur",g),r.off("change",f),r.off("swapDoc",f),i.off(r.getInputField(),"compositionupdate",r.state.placeholderCompose),A(r);var d=r.getWrapperElement();d.className=d.className.replace(" CodeMirror-empty","")}l&&!r.hasFocus()&&g(r)});function A(r){r.state.placeholder&&(r.state.placeholder.parentNode.removeChild(r.state.placeholder),r.state.placeholder=null)}function b(r){A(r);var l=r.state.placeholder=document.createElement("pre");l.style.cssText="height: 0; overflow: visible",l.style.direction=r.getOption("direction"),l.className="CodeMirror-placeholder CodeMirror-line-like";var p=r.getOption("placeholder");typeof p=="string"&&(p=document.createTextNode(p)),l.appendChild(p),r.display.lineSpace.insertBefore(l,r.display.lineSpace.firstChild)}function w(r){setTimeout(function(){var l=!1;if(r.lineCount()==1){var p=r.getInputField();l=p.nodeName=="TEXTAREA"?!r.getLine(0).length:!/[^\u200b]/.test(p.querySelector(".CodeMirror-line").textContent)}l?b(r):A(r)},20)}function g(r){c(r)&&b(r)}function f(r){var l=r.getWrapperElement(),p=c(r);l.className=l.className.replace(" CodeMirror-empty","")+(p?" CodeMirror-empty":""),p?b(r):A(r)}function c(r){return r.lineCount()===1&&r.getLine(0)===""}})}(J,J.exports)),J.exports}var Kt=gt();const vt=(y,H,i)=>{const A=[...H];return{codeHintingRegistry:()=>{const g=[];A.forEach(c=>{c.superiors&&g.push(c.superiors)});const f=(c,r)=>{const l=c.getCursor(),p=c.getTokenAt(l),s=p.start,d=l.ch,h=p.string;let x=null;if(h.length){if(h==="."){const n=c.getLine(l.line);for(let t=0,a=g.length;t<a;t++){const u=n.slice(-(g[t].length+1),-1);if(g.includes(u)){x=u;break}}}else{const n=c.getLine(l.line);for(let t=0,a=g.length;t<a;t++){const u=n.slice(s-(g[t].length+1),s);if(u.substr(-1)==="."&&g.includes(u.replace(".",""))){x=u.replace(".","");break}}}const o=(n,t)=>n.toLowerCase().indexOf(t.toLowerCase());let e=A.filter(n=>x?n.superiors===x:n.superiors==null);if(h==="."?x==null&&(e=[]):e=e.filter(n=>{const{text:t}=n,a=o(t,h);return t.startsWith(".")?a===1:a===0}).sort((n,t)=>o(n.text,h)<o(t.text,h)?-1:1),e.length===1){const n=e[0];(n.text===h||n.text.substring(1)===h)&&(e=[])}if(e.length){h!="."&&(e=e.map(t=>t.text.indexOf(".")===0?ye(be({},t),{text:t.text.substring(1)}):t)),r({list:e,from:y.Pos(l.line,s),to:y.Pos(l.line,d)});const n=A[0];n!=null&&n.desc&&setTimeout(()=>{const t=document.querySelector(".CodeMirror-hints");if(t){const a=t.children;Array.from(a).forEach(u=>{const m=u.textContent,v=A.find(L=>L.displayText===m);v&&u.setAttribute("title",v.desc)})}},0)}}};f.async=!0,f.supportsSelection=!0,H.length&&y.registerHelper("hint",i,f)},codeHintingMount:g=>{H.length&&(g.setOption("mode",i),setTimeout(()=>{g.on("cursorActivity",function(){g==null||g.showHint({completeSingle:!1})})},1e3))}}},le=De({name:"JCodeEditor",inheritAttrs:!1,components:{},props:{value:B.string.def(""),height:B.string.def("auto"),disabled:B.bool.def(!1),fullScreen:B.bool.def(!1),zIndex:B.any.def(1500),theme:B.string.def("idea"),language:B.string.def(""),keywords:B.array.def([])},emits:["change","update:value"],setup(y,{emit:H}){var T;const{getDarkMode:i}=Ze(),A=$(null),{prefixCls:b}=et("code-editer"),w=window.CodeMirror||st,g=$(),[f]=ot(y,"value","change",g),c=$();let r=null;const l=tt(),p=$(y.height),s=Ge({tabSize:2,theme:i.value==nt.DARK?"monokai":y.theme,smartIndent:!0,lineNumbers:!0,line:!0,foldGutter:!0,lineWrapping:!0,gutters:["CodeMirror-linenumbers","CodeMirror-foldgutter","CodeMirror-lint-markers"],styleActiveLine:!0,mode:y.language,readOnly:y.disabled,matchBrackets:!0,extraKeys:{"Cmd-/":k=>S(k),"Ctrl-/":k=>S(k)}});let d=(T=y.value)!=null?T:"";const h=$(!1),x=xe(()=>h.value?"fullscreen-exit":"fullscreen"),o=xe(()=>{let k={class:[b,"full-screen-parent","auto-height",{"full-screen":h.value}],style:{}};return h.value&&(k.style["z-index"]=y.zIndex),k}),{codeHintingMount:e,codeHintingRegistry:n}=vt(w,y.keywords,y.language);n(),ae(()=>y.value,()=>{d!=y.value&&t(y.value,!1)}),ze(()=>{m(),setTimeout(()=>{F()},150)});function t(k,O=!0){k&&it(k)&&(k=JSON.stringify(JSON.parse(k),null,2)),r==null||r.setValue(k!=null?k:""),d=k,O&&u(d),setTimeout(()=>{F(),setTimeout(()=>{F()},600)},400)}function a(k){d=k.getValue()||"",y.value!=d&&u(d)}function u(k){H("change",k),H("update:value",k)}function m(){r=w.fromTextArea(c.value,s),r.on("change",a),t(d,!1),e(r)}function v(){h.value=!h.value}ae(()=>y.disabled,k=>{r&&r.setOption("readOnly",k)}),ae(()=>y.language,k=>{k&&r&&r.setOption("mode",k)});const L=Object.assign({},ke(y),ke(l));function F(){r&&r.refresh()}function S(k){var O=k.getSelection(),R=k.getCursor("start"),j=k.getCursor("end"),N=O.startsWith("//");N?k.replaceRange(O.replace(/\n\/\/\s/g,`
`).replace(/^\/\/\s/,""),R,j):k.replaceRange("// "+O.replace(/\n(?=.)/g,`
// `),R,j)}return{state:f,textarea:c,boxBindProps:o,getBindValue:L,setValue:t,isFullScreen:h,fullScreenIcon:x,onToggleFullScreen:v,refresh:F,containerRef:A}}}),je=()=>{Ke(y=>({"7d6a9170":y.height}))},Re=le.setup;le.setup=Re?(y,H)=>(je(),Re(y,H)):je;function mt(y,H,i,A,b,w){const g=Me("a-icon");return we(),Je("div",Ae({ref:"containerRef"},y.boxBindProps),[y.fullScreen?(we(),Xe(g,{key:0,class:"full-screen-icon",type:y.fullScreenIcon,onClick:y.onToggleFullScreen},null,8,["type","onClick"])):Ye("",!0),Qe("textarea",Ae({ref:"textarea"},y.getBindValue),null,16)],16)}const Mt=rt(le,[["render",mt]]);export{Mt as default};
