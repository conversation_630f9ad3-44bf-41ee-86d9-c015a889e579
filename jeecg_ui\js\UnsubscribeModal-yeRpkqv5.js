var C=(_,g,l)=>new Promise((c,p)=>{var b=t=>{try{r(l.next(t))}catch(a){p(a)}},i=t=>{try{r(l.throw(t))}catch(a){p(a)}},r=t=>t.done?c(t.value):Promise.resolve(t.value).then(b,i);r((l=l.apply(_,g)).next())});import{d as $,ap as q,f as v,r as T,w as U,ag as m,aB as V,ar as A,aD as f,at as k,k as s,G,au as j,u as z}from"./vue-vendor-dy9k-Yad.js";import{f as h}from"./antd-vue-vendor-me9YkNVC.js";import{C as E}from"./index-Diw57m_E.js";import{g as H,u as J}from"./user-Cjsg8yWg.js";import{ah as K,a as L}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const O={class:"unsubscribe-form"},P={class:"code-input-wrapper"},Q=$({__name:"UnsubscribeModal",props:{open:{type:Boolean}},emits:["update:open","close","success"],setup(_,{emit:g}){const l=_,c=g,p=K(),b=q(),i=v(),r=v(!1),t=v(!1),a=v(0);let u=null;const n=T({phone:"",code:""}),B={phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"},{pattern:/^\d{6}$/,message:"验证码为6位数字",trigger:"blur"}]};U(()=>l.open,o=>{if(r.value=o,o){const e=p.getUserInfo;e!=null&&e.phone&&(n.phone=e.phone)}else D()},{immediate:!0}),U(r,o=>{c("update:open",o)});function S(){return C(this,null,function*(){var o;try{yield(o=i.value)==null?void 0:o.validateFields(["phone"]),t.value=!0,yield H({phone:n.phone}),h.success("验证码发送成功"),I()}catch(e){e!=="validation failed"&&h.error("发送验证码失败")}finally{t.value=!1}})}function I(){a.value=60,u=setInterval(()=>{a.value--,a.value<=0&&(clearInterval(u),u=null)},1e3)}function R(){return C(this,null,function*(){var o;try{yield(o=i.value)==null?void 0:o.validate(),yield J({phone:n.phone,code:n.code}),h.success("账号注销成功"),yield p.logout(!0),b.push("/login"),c("success"),w()}catch(e){e!=="validation failed"&&h.error("注销账号失败")}})}function w(){r.value=!1,c("close")}function D(){var o;(o=i.value)==null||o.resetFields(),n.phone="",n.code="",u&&(clearInterval(u),u=null),a.value=0}return(o,e)=>{const F=m("a-alert"),x=m("a-input"),y=m("a-form-item"),M=m("a-button"),N=m("a-form");return A(),V(z(E),{open:r.value,"onUpdate:open":e[2]||(e[2]=d=>r.value=d),title:"注销账号",width:500,"show-footer":!0,"show-cancel-button":!0,"show-confirm-button":!0,"cancel-text":"取消","confirm-text":"确认注销",onClose:w,onCancel:w,onConfirm:R},{default:f(()=>[k("div",O,[s(F,{message:"注意",description:"注销账号后，您的所有数据将被永久删除且无法恢复，请谨慎操作！",type:"warning","show-icon":"",style:{"margin-bottom":"24px"}}),s(N,{ref_key:"formRef",ref:i,model:n,rules:B,layout:"vertical"},{default:f(()=>[s(y,{label:"手机号",name:"phone"},{default:f(()=>[s(x,{value:n.phone,"onUpdate:value":e[0]||(e[0]=d=>n.phone=d),placeholder:"请输入手机号",maxlength:11},null,8,["value"])]),_:1}),s(y,{label:"验证码",name:"code"},{default:f(()=>[k("div",P,[s(x,{value:n.code,"onUpdate:value":e[1]||(e[1]=d=>n.code=d),placeholder:"请输入验证码",maxlength:6,style:{flex:"1","margin-right":"12px"}},null,8,["value"]),s(M,{disabled:a.value>0,loading:t.value,onClick:S,style:{width:"120px"}},{default:f(()=>[G(j(a.value>0?`${a.value}s后重发`:"获取验证码"),1)]),_:1},8,["disabled","loading"])])]),_:1})]),_:1},8,["model"])])]),_:1},8,["open"])}}}),me=L(Q,[["__scopeId","data-v-91c928b1"]]);export{me as default};
