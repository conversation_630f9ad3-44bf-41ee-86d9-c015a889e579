import{d as l,ag as r,aB as d,ar as u,aD as f,k as t}from"./vue-vendor-dy9k-Yad.js";import{B as g}from"./componentMap-Bkie1n3v.js";import{u as _,a as B}from"./index-CCWaWN5g.js";import"./index-L3cSIXth.js";import{P as h}from"./index-CtJ0w2CP.js";import{A as e}from"./antd-vue-vendor-me9YkNVC.js";import{u as a}from"./upload-Dd1Qafin.js";import{B as C}from"./BasicForm-DBcXiHk0.js";import{u as P}from"./useForm-CgkFTrrO.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const $=[{field:"field1",component:"Upload",label:"字段1",colProps:{span:8},rules:[{required:!0,message:"请选择上传文件"}],componentProps:{api:a}}],x=l({components:{BasicUpload:g,BasicForm:C,PageWrapper:h,[e.name]:e},setup(){const{createMessage:o}=_(),[p]=P({labelWidth:120,schemas:$,actionColOptions:{span:16}});return{handleChange:i=>{o.info(`已上传文件${JSON.stringify(i)}`)},uploadApi:a,register:p}}});function A(o,p,i,F,U,W){const m=r("a-alert"),s=r("BasicUpload"),n=r("BasicForm"),c=r("PageWrapper");return u(),d(c,{title:"上传组件示例"},{default:f(()=>[t(m,{message:"基础示例"}),t(s,{maxSize:20,maxNumber:10,onChange:o.handleChange,api:o.uploadApi,class:"my-5",accept:["image/*"]},null,8,["onChange","api"]),t(m,{message:"嵌入表单,加入表单校验"}),t(n,{onRegister:o.register,class:"my-5"},null,8,["onRegister"])]),_:1})}const So=B(x,[["render",A]]);export{So as default};
