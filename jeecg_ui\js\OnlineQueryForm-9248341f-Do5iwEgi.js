import"./index-L3cSIXth.js";import{f as P,r as ke,w as U,J as Q,ag as M,aq as ee,ah as T,ar as R,k as _,aD as F,at as H,aB as te,au as G,G as we,aE as oe}from"./vue-vendor-dy9k-Yad.js";import{cs as Ye,u as Se,j as De}from"./index-CCWaWN5g.js";import{i as je,n as re,o as Pe,F as A,p as Fe,q as xe,r as ne,g as Be,j as x}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{o as Ce}from"./constant-fa63bd66-Ddbq-fz2.js";import{E as Me}from"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import{u as _e}from"./useForm-CgkFTrrO.js";import{B as Ee}from"./BasicForm-DBcXiHk0.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./vxe-table-vendor-B22HppNm.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";var Ne=Object.defineProperty,qe=Object.defineProperties,Je=Object.getOwnPropertyDescriptors,ae=Object.getOwnPropertySymbols,Ie=Object.prototype.hasOwnProperty,$e=Object.prototype.propertyIsEnumerable,ie=(h,c,m)=>c in h?Ne(h,c,{enumerable:!0,configurable:!0,writable:!0,value:m}):h[c]=m,E=(h,c)=>{for(var m in c||(c={}))Ie.call(c,m)&&ie(h,m,c[m]);if(ae)for(var m of ae(c))$e.call(c,m)&&ie(h,m,c[m]);return h},z=(h,c)=>qe(h,Je(c)),N=(h,c,m)=>new Promise((d,y)=>{var J=v=>{try{j(m.next(v))}catch(Y){y(Y)}},B=v=>{try{j(m.throw(v))}catch(Y){y(Y)}},j=v=>v.done?d(v.value):Promise.resolve(v.value).then(J,B);j((m=m.apply(h,c)).next())});const Te={name:"OnlineQueryForm",components:{BasicForm:Ee,JRangeNumber:Me},props:{id:{type:String,default:""},queryBtnCfg:{type:Object,default:()=>({enabled:!0,buttonName:"查询",buttonIcon:"ant-design:search"})},resetBtnCfg:{type:Object,default:()=>({enabled:!0,buttonName:"重置",buttonIcon:"ant-design:reload"})}},emits:["search","loaded"],setup(h,{emit:c}){const m="/online/cgform/api/getQueryInfoVue3/",d=P(null),y=P([]),J=P({xs:24,sm:24,md:12,lg:6,xl:6}),B=P(!1),j=P(!1),v=P({}),Y=P([]),{createMessage:I}=Se(),{linkTableCard2Select:V}=je(),$=P(80),p=ke({config:{},cache:{},param:{},status:!1});U(()=>p.status,e=>N(this,null,function*(){const{config:t,cache:r,param:n}=Q(p);let a=Object.assign({},t,r,n);yield se(a)}),{immediate:!0,deep:!0});function b(e,t){return N(this,null,function*(){p.cache=E({},e),p.param=E({},t),p.status=!p.status})}U(()=>h.id,e=>{e?le():y.value=[]},{immediate:!0});function O(e){return N(this,null,function*(){var t,r,n,a;let o=[],g={},i=Object.keys(e),s=-1;for(let f of i){const l=e[f];f==="sys_org_code"&&(l.fieldExtendJson||(l.fieldExtendJson='{"store":"orgCode"}'));let u=l.view;if(l.originView=l.view,re[u]&&(l.view=re[u]),yield Pe(f,l,g),l.mode=="group"&&(u=="date"||u=="datetime"||u=="number"||u=="time")){let D=A.createSlotFormSchema(f,l);o.push(D)}else if(l.view===Fe){let D=xe(l,f);for(let C of D){let Z=A.createFormSchema(C.key,C),K=ne(o,C.key);K==-1?o.push(Z):o[K]=Z}}else if(ne(o,f)==-1){let D=A.createFormSchema(f,l);o.push(D)}let w=l.fieldExtendJson;w&&(w=JSON.parse(w),w.labelLength&&(s>-1?s=w.labelLength>s?w.labelLength:s:s=w.labelLength))}s==-1?s=Ce:o.forEach(f=>{f.labelLength=s}),o.sort(function(f,l){return f.order-l.order});let k=[];o.length>2&&(B.value=!0);let S=[];for(let f=0;f<o.length;f++){let l=o[f];l.setFormRef(d),l.noChange(),l.asSearchForm(),f>1&&(S.push(l.field),l.isHidden());let u=l.getFormItemSchema();if(l.slot=="groupDatetime"&&o.length<=3&&(u.colProps={xs:24,sm:24,md:12,lg:8,xl:8}),u.component==="JSwitch"){const C=(t=u.componentProps)!=null?t:{};u.componentProps=z(E({},C),{query:!0})}if(V(u),u.component==="LinkTableSelect"){let C=(r=u.componentProps)!=null?r:{};u.componentProps=z(E({},C),{editBtnShow:!1})}const w=(n=u.componentProps)!=null?n:{};w.getPopupContainer||(u.componentProps=z(E({},w),{getPopupContainer:()=>document.body}));const D=(a=e[u.field])!=null?a:{};D.mode=="like"&&D.view==="text"&&D.originView==="text"&&(u.component="JInput"),k.push(u)}Y.value=S,y.value=k,p.config=E({},g),p.status=!p.status,setTimeout(()=>{const f=s*14+s+24;$.value=f},0)})}const q=e=>{const t=e.properties;t&&Object.entries(t).forEach(([r,n])=>{const a=n;if(["date_year","date_month","date_week","date_quarter"].includes(a.view)){const o=a.fieldExtendJson?JSON.parse(a.fieldExtendJson):{};o.picker=a.view.split("_")[1],a.fieldExtendJson=JSON.stringify(o),a.view="date"}})};function le(){return N(this,null,function*(){let e=yield ce();q(e);let t=pe(e);c("loaded",e);let{formProperties:r,hasField:n}=me(t,e);if(n==!1){y.value=[];return}yield O(r)})}function se(e){return N(this,null,function*(){yield Be(d);const t=he(e);yield W(t),Object.keys(t).length>0&&L()})}function me(e,t){const{searchFieldList:r,joinQuery:n,table:a}=t;let o=!1,g={};return e&&Object.keys(e).map(i=>{r.indexOf(i)>=0&&(n==!0?i.indexOf("@")<0?(g[a+"@"+i]=e[i],o=!0):(g[i]=e[i],o=!0):i.indexOf("@")<0&&(g[i]=e[i],o=!0))}),{formProperties:g,hasField:o}}function pe(e){const{properties:t,searchFieldList:r,joinQuery:n,table:a}=e;let o={},g=1;return Object.keys(t).map(i=>{let s=t[i];if(s.view=="table"){let k=s.properties,S=g*100;Object.keys(k).map(f=>{let l=k[f];l.order=S+Number(l.order);let u=i+"@"+f;o[u]=l}),g++}else s.order=Number(s.order),o[i]=s}),o}function ce(){let e=`${m}${h.id}`;return new Promise(t=>{De.get({url:e},{isTransformResponse:!1}).then(r=>{r.success?t(r.result):(t(!1),I.warning(r.message))}).catch(()=>{I.warning("获取查询条件失败!"),t(!1)})})}const[ue,{resetFields:fe,setFieldsValue:W,updateSchema:de,getFieldsValue:ge}]=_e({name:"online-query-form",schemas:y,showActionButtonGroup:!1,baseColProps:J,autoSubmitOnEnter:!0,labelWidth:$,wrapperCol:null,submitFunc(){L()}});function L(){let e=ge();ye(e),be(e);let t=Object.assign({},Q(p.param),Oe(e));c("search",t,!0)}const he=e=>{const t=E({},e),r=y.value.filter(n=>["groupTime","groupDatetime","groupNumber","groupDate"].includes(n.slot));return r.length&&Object.keys(t).forEach(n=>{let a;if(r.find(o=>o.field===n?(a=n,!0):!1)){const o=t[a];if(typeof o=="string"){const g=o.split(",");t[a]=[...g]}}}),t},be=e=>{if(e){const t=y.value.filter(r=>["groupTime","groupDatetime","groupDate","groupNumber"].includes(r.slot));t.length&&Object.keys(e).forEach(r=>{let n;if(t.find(a=>a.field===r?(n=r,!0):!1)){const a=e[n];if(typeof a=="string"){const o=a.split(",");e[`${n}_begin`]=o[0],e[`${n}_end`]=o[1],delete e[n]}}})}},ye=e=>{const t=y.value.filter(r=>{var n;return((n=r.componentProps)==null?void 0:n.picker)&&r.componentProps.picker!="default"});t.length&&Object.keys(e).forEach(r=>{let n;const a=t.find(o=>o.field===r||`${o.field}_begin`===r||`${o.field}_end`===r?(n=r,!0):!1);if(a){const o=e[n];if(o){const g=(i,s,k)=>{const S=a.componentProps.picker;S==="year"?k?e[s]=x(i).endOf("year").format("YYYY-MM-DD"):e[s]=x(i).startOf("year").format("YYYY-MM-DD"):S==="month"?k?e[s]=x(i).endOf("month").format("YYYY-MM-DD"):e[s]=x(i).startOf("month").format("YYYY-MM-DD"):S==="week"?k?e[s]=x(i).endOf("week").format("YYYY-MM-DD"):e[s]=x(i).startOf("week").format("YYYY-MM-DD"):S==="quarter"&&(k?e[s]=x(i).endOf("quarter").format("YYYY-MM-DD"):e[s]=x(i).startOf("quarter").format("YYYY-MM-DD"))};if((a==null?void 0:a.slot)==="groupDate"){const i=o.split(",");g(i[0],`${n}_begin`,!1),g(i[1],`${n}_end`,!0),delete e[n]}else g(o,n,!1)}}})};function X(){return N(this,null,function*(){yield fe();const{config:e,param:t}=Q(p);let r=Object.assign({},e,t);return Object.keys(r).length>0&&(yield W(r)),r})}function ve(){return N(this,null,function*(){const e=yield X();c("search",e,!1)})}function Oe(e){return Object.keys(e).map(t=>{e[t]&&e[t]instanceof Array&&(e[t]=e[t].join(","))}),e}return U(()=>j.value,e=>{let t=Y.value;if(t&&t.length>0){let r=[];for(let n of t)r.push({field:n,show:e});de(r)}},{immediate:!1}),{onlineQueryFormRef:d,registerForm:ue,initDefaultValues:b,toggleButtonShow:B,toggleSearchStatus:j,doSearch:L,resetSearch:ve,queryParams:v,formSchemas:y,clearSearch:X,getGroupDatePlaceholder:e=>{let t=["开始日期","结束日期"];if(e!=null&&e.picker)switch(e==null?void 0:e.picker){case"year":t=["开始年份","结束年份"];break;case"month":t=["开始月份","结束月份"];break;case"week":t=["开始周","结束周"];break;case"quarter":t=["开始季度","结束季度"];break;default:t=["开始日期","结束日期"]}return t}}}},Re={key:0,class:"jeecg-basic-table-form-container online-query-form p-0"},Ve={style:{float:"left",overflow:"hidden","margin-left":"10px"},class:"table-page-search-submitButtons"};function Le(h,c,m,d,y,J){const B=M("a-range-picker"),j=M("a-time-range-picker"),v=M("JRangeNumber"),Y=M("a-button"),I=M("a-icon"),V=M("a-col"),$=M("BasicForm");return d.formSchemas&&d.formSchemas.length>0?(R(),ee("div",Re,[_($,{ref:"onlineQueryFormRef",onRegister:d.registerForm},{groupDate:F(({model:p,field:b,schema:O})=>[_(B,oe({style:{width:"100%"},value:p[b],"onUpdate:value":q=>p[b]=q},O.componentProps,{placeholder:d.getGroupDatePlaceholder(O.componentProps),valueFormat:"YYYY-MM-DD"}),null,16,["value","onUpdate:value","placeholder"])]),groupDatetime:F(({model:p,field:b})=>[_(B,{style:{width:"100%"},value:p[b],"onUpdate:value":O=>p[b]=O,"show-time":!0,valueFormat:"YYYY-MM-DD HH:mm:ss"},null,8,["value","onUpdate:value"])]),groupTime:F(({model:p,field:b})=>[_(j,{style:{width:"100%"},value:p[b],"onUpdate:value":O=>p[b]=O,"value-format":"HH:mm:ss"},null,8,["value","onUpdate:value"])]),groupNumber:F(({model:p,field:b,schema:O})=>[_(v,oe({value:p[b],"onUpdate:value":q=>p[b]=q},O.componentProps),null,16,["value","onUpdate:value"])]),formFooter:F(()=>[_(V,{md:6,sm:8},{default:F(()=>[H("span",Ve,[m.queryBtnCfg.enabled?(R(),te(Y,{key:0,type:"primary",preIcon:m.queryBtnCfg.buttonIcon,onClick:d.doSearch},{default:F(()=>[H("span",null,G(m.queryBtnCfg.buttonName),1)]),_:1},8,["preIcon","onClick"])):T("",!0),m.resetBtnCfg.enabled?(R(),te(Y,{key:1,type:"primary",preIcon:m.resetBtnCfg.buttonIcon,style:{"margin-left":"8px"},onClick:d.resetSearch},{default:F(()=>[H("span",null,G(m.resetBtnCfg.buttonName),1)]),_:1},8,["preIcon","onClick"])):T("",!0),d.toggleButtonShow?(R(),ee("a",{key:2,onClick:c[0]||(c[0]=p=>d.toggleSearchStatus=!d.toggleSearchStatus),style:{"margin-left":"8px"}},[we(G(d.toggleSearchStatus?"收起":"展开")+" ",1),_(I,{type:d.toggleSearchStatus?"up":"down"},null,8,["type"])])):T("",!0)])]),_:1})]),_:1},8,["onRegister"])])):T("",!0)}const oo=Ye(Te,[["render",Le],["__scopeId","data-v-04f16c27"]]);export{oo as default};
