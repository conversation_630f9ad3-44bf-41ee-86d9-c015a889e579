var K=Object.defineProperty,Q=Object.defineProperties;var W=Object.getOwnPropertyDescriptors;var H=Object.getOwnPropertySymbols;var Z=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable;var V=(g,p,v)=>p in g?K(g,p,{enumerable:!0,configurable:!0,writable:!0,value:v}):g[p]=v,x=(g,p)=>{for(var v in p||(p={}))Z.call(p,v)&&V(g,v,p[v]);if(H)for(var v of H(p))h.call(p,v)&&V(g,v,p[v]);return g},D=(g,p)=>Q(g,W(p));var F=(g,p,v)=>new Promise((C,w)=>{var N=y=>{try{I(v.next(y))}catch(U){w(U)}},a=y=>{try{I(v.throw(y))}catch(U){w(U)}},I=y=>y.done?C(y.value):Promise.resolve(y.value).then(N,a);I((v=v.apply(g,p)).next())});import{d as ee,f as P,z as ae,w as z,e as te,ag as b,aq as m,ar as f,k as t,aD as l,at as o,ah as _,G as i,au as le,u as se}from"./vue-vendor-dy9k-Yad.js";import{bB as J,i as L}from"./antd-vue-vendor-me9YkNVC.js";import{_ as Y}from"./JUpload-CRos0F1P.js";import{i as M}from"./JAreaSelect-Db7Nhhc_.js";import{J as S}from"./JEditorTiptap-BwAoWsi9.js";import{a as oe}from"./index-CCWaWN5g.js";import"./useFormItem-CHvpjy4o.js";import"./areaDataUtil-BXVjRArW.js";import"./index-ByPySmGo.js";import"./vxe-table-vendor-B22HppNm.js";const ie={class:"step1"},ue={class:"form-section"},ne={class:"form-row"},re={key:0,class:"edit-mode-tip"},de={key:0,class:"form-section"},ve={class:"section-content"},me={class:"form-row basic-three-row"},fe={class:"form-row basic-three-row"},pe={class:"form-row auction-type-row"},ce={key:1,class:"form-section"},ge={class:"section-content"},_e={class:"form-row"},be={class:"upload-container"},ye={key:2,class:"form-section"},Ie={class:"section-content"},Te={key:3,class:"form-section"},qe={class:"section-content"},we={key:4,class:"form-section"},Ue={class:"section-content"},xe={key:5,class:"form-section"},De={class:"section-content"},Ye={key:6,class:"form-section"},Ce={class:"section-content"},Ne={class:"form-row asset-row"},ke={class:"form-row asset-row"},Se={class:"form-row asset-row"},ze={key:7,class:"form-section"},Me={class:"section-content"},Ee={class:"form-row asset-detail-row"},He={class:"form-row asset-detail-row"},Ve={class:"form-row asset-detail-row"},Fe={key:8,class:"form-section"},Pe={class:"section-title"},Le={key:0,class:"form-row"},Je={class:"location-selects"},Re={key:1,class:"form-row location-row"},Be={class:"location-selects"},Xe={class:"detail-address"},Oe={key:9,class:"form-section"},Ae={key:0},Ge={class:"form-row"},je={class:"upload-container"},$e={key:1},Ke={class:"form-row"},Qe={class:"upload-container"},We={class:"form-row"},Ze={class:"upload-container"},he={key:10,class:"form-section"},ea={class:"form-row"},aa={name:"Step1",components:{JUpload:Y,JAreaSelect:M,JEditorTiptap:S,EnvironmentOutlined:J}},ta=ee(D(x({},aa),{props:{modelValue:{},locationLoading:{type:Boolean},isEditMode:{type:Boolean}},emits:["update:modelValue","area-change","get-current-location","service-type-change"],setup(g,{expose:p,emit:v}){const C=P(),w=g,N=v,a=ae(w,"modelValue"),I=P(null),y=()=>{a.value.basicInfo.disposalStartTime&&a.value.basicInfo.disposalEndTime?I.value=[L(a.value.basicInfo.disposalStartTime),L(a.value.basicInfo.disposalEndTime)]:I.value=null};z(()=>[a.value.basicInfo.disposalStartTime,a.value.basicInfo.disposalEndTime],()=>{y()},{immediate:!0}),z(()=>a.value.serviceType,(r,e)=>{});const U=r=>{const e=[],n=[];if(r.forEach(c=>{const u={fileName:c.fileName,filePath:c.filePath,fileSize:c.fileSize,fileType:c.fileType};c.fileType==="image"?e.push(u):n.push(u)}),e.length>0){const c=JSON.stringify(e);a.value.materials.images=c,a.value.other.images=c}if(n.length>0){const c=JSON.stringify(n);a.value.materials.attachments=c,a.value.other.attachments=c}};z(()=>a.value.hgyAttachmentList,r=>{r&&Array.isArray(r)&&r.length>0&&U(r)},{immediate:!0,deep:!0});const R=r=>{r?(a.value.basicInfo.disposalStartTime=r[0].format("YYYY-MM-DD HH:mm:ss"),a.value.basicInfo.disposalEndTime=r[1].format("YYYY-MM-DD HH:mm:ss")):(a.value.basicInfo.disposalStartTime="",a.value.basicInfo.disposalEndTime="")},B=()=>{N("get-current-location")},X=r=>{N("service-type-change",r)},O=te(()=>{const r={serviceType:[{required:!0,message:"请选择服务类型",trigger:"change"}],location:{province:[{required:!0,message:"请选择省份",trigger:"change"}],city:[{required:!0,message:"请选择城市",trigger:"change"}],area:[{required:!0,message:"请选择区域",trigger:"change"}],detailAddress:[{required:!0,message:"请输入详细地址",trigger:"blur"}]}};return a.value.serviceType===1?D(x({},r),{auctionInfo:{auctionName:[{required:!0,message:"请输入拍卖会名称",trigger:"blur"}],entrustCompany:[{required:!0,message:"委托单位不能为空",trigger:"blur"}],auctionForm:[{required:!0,message:"请选择拍卖形式",trigger:"change"}],registerEndTime:[{required:!0,message:"请选择报名截止时间",trigger:"change"}],startTime:[{required:!0,message:"请选择开拍时间",trigger:"change"}],endType:[{required:!0,message:"请选择结束方式",trigger:"change"}],auctionType:[{required:!0,message:"请选择拍卖方式",trigger:"change"}]}}):a.value.serviceType===3?D(x({},r),{entrustInfo:{noticeName:[{required:!0,message:"请输入公告名称",trigger:"blur"}]}}):a.value.serviceType===2?D(x({},r),{entrustInfo:{title:[{required:!0,message:"处置单位不能为空",trigger:"blur"}]},basicInfo:{assetName:[{required:!0,message:"请输入资产名称",trigger:"blur"}],assetType:[{required:!0,message:"请选择资产类型",trigger:"change"}],quantity:[{required:!0,message:"请输入资产数量",trigger:"blur"}],unit:[{required:!0,message:"请选择计量单位",trigger:"change"}],quantityFlag:[{required:!0,message:"请选择是否展示实际数量",trigger:"change"}],serviceLife:[{required:!0,message:"请输入使用年限",trigger:"blur"}],depreciationDegree:[{required:!0,message:"请选择新旧程度",trigger:"change"}],currentStatus:[{required:!0,message:"请选择当前状态",trigger:"change"}],appraisalValue:[{required:!0,message:"请输入评估价值",trigger:"blur"}],disposalPrice:[{required:!0,message:"请输入处置底价",trigger:"blur"}],disposalStartTime:[{required:!0,message:"请选择处置开始时间",trigger:"change"}],disposalEndTime:[{required:!0,message:"请选择处置结束时间",trigger:"change"}],paymentMethod:[{required:!0,message:"请选择付款方式",trigger:"change"}],isTaxIncluded:[{required:!0,message:"请选择是否含税",trigger:"change"}]}}):r});return p({validateForm:()=>F(null,null,function*(){var r;try{return yield new Promise(e=>setTimeout(e,100)),yield(r=C.value)==null?void 0:r.validate(),!0}catch(e){const n=document.querySelector(".ant-form-item-has-error");return n&&n.scrollIntoView({behavior:"smooth",block:"center"}),!1}})}),(r,e)=>{const n=b("a-select-option"),c=b("a-select"),u=b("a-form-item"),T=b("a-input"),d=b("a-radio"),q=b("a-radio-group"),E=b("a-date-picker"),k=b("a-input-number"),A=b("a-range-picker"),G=b("a-button"),j=b("a-textarea"),$=b("a-form");return f(),m("div",ie,[t($,{model:a.value,rules:O.value,ref_key:"formRef",ref:C,"scroll-to-first-error":!0},{default:l(()=>[o("div",ue,[e[44]||(e[44]=o("h3",{class:"section-title"},"服务类型",-1)),o("div",ne,[t(u,{name:"serviceType",class:"service-type-item"},{default:l(()=>[t(c,{value:a.value.serviceType,"onUpdate:value":e[0]||(e[0]=s=>a.value.serviceType=s),onChange:X,placeholder:"请选择服务类型",size:"large",class:"service-type-select",disabled:w.isEditMode},{default:l(()=>[t(n,{value:1},{default:l(()=>e[40]||(e[40]=[i("发布竞价标的")])),_:1,__:[40]}),t(n,{value:2},{default:l(()=>e[41]||(e[41]=[i("发布资产处置")])),_:1,__:[41]}),t(n,{value:3},{default:l(()=>e[42]||(e[42]=[i("发布采购信息")])),_:1,__:[42]})]),_:1},8,["value","disabled"]),w.isEditMode?(f(),m("div",re,e[43]||(e[43]=[o("span",{class:"tip-text"},"编辑模式下不允许修改服务类型",-1)]))):_("",!0)]),_:1})])]),a.value.serviceType===1?(f(),m("div",de,[e[54]||(e[54]=o("div",{class:"section-title"},"基本信息",-1)),o("div",ve,[o("div",me,[t(u,{label:"拍卖会名称",name:["auctionInfo","auctionName"],required:"",class:"basic-three-item"},{default:l(()=>[t(T,{value:a.value.auctionInfo.auctionName,"onUpdate:value":e[1]||(e[1]=s=>a.value.auctionInfo.auctionName=s),placeholder:"请输入拍卖会名称",size:"large"},null,8,["value"])]),_:1}),t(u,{label:"委托单位",name:["auctionInfo","entrustCompany"],required:"",class:"basic-three-item"},{default:l(()=>[t(T,{value:a.value.auctionInfo.entrustCompany,"onUpdate:value":e[2]||(e[2]=s=>a.value.auctionInfo.entrustCompany=s),placeholder:"委托单位",size:"large",disabled:"",readonly:""},null,8,["value"])]),_:1}),t(u,{label:"拍卖形式",name:["auctionInfo","auctionForm"],required:"",class:"basic-three-item"},{default:l(()=>[t(q,{value:a.value.auctionInfo.auctionForm,"onUpdate:value":e[3]||(e[3]=s=>a.value.auctionInfo.auctionForm=s)},{default:l(()=>[t(d,{value:1},{default:l(()=>e[45]||(e[45]=[i("同步")])),_:1,__:[45]}),t(d,{value:2},{default:l(()=>e[46]||(e[46]=[i("线上")])),_:1,__:[46]})]),_:1},8,["value"])]),_:1})]),o("div",fe,[t(u,{label:"报名截止",name:["auctionInfo","registerEndTime"],required:"",class:"basic-three-item"},{default:l(()=>[t(E,{value:a.value.auctionInfo.registerEndTime,"onUpdate:value":e[4]||(e[4]=s=>a.value.auctionInfo.registerEndTime=s),"show-time":"",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择报名截止时间",size:"large",style:{width:"100%"}},null,8,["value"])]),_:1}),t(u,{label:"开拍时间",name:["auctionInfo","startTime"],required:"",class:"basic-three-item"},{default:l(()=>[t(E,{value:a.value.auctionInfo.startTime,"onUpdate:value":e[5]||(e[5]=s=>a.value.auctionInfo.startTime=s),"show-time":"",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择开拍时间",size:"large",style:{width:"100%"}},null,8,["value"])]),_:1}),t(u,{label:"结束方式",name:["auctionInfo","endType"],required:"",class:"basic-three-item"},{default:l(()=>[t(q,{value:a.value.auctionInfo.endType,"onUpdate:value":e[6]||(e[6]=s=>a.value.auctionInfo.endType=s)},{default:l(()=>[t(d,{value:1},{default:l(()=>e[47]||(e[47]=[i("手动")])),_:1,__:[47]}),t(d,{value:2},{default:l(()=>e[48]||(e[48]=[i("自动")])),_:1,__:[48]})]),_:1},8,["value"])]),_:1})]),o("div",pe,[t(u,{label:"拍卖方式",name:["auctionInfo","auctionType"],required:"",class:"auction-type-item"},{default:l(()=>[t(q,{value:a.value.auctionInfo.auctionType,"onUpdate:value":e[7]||(e[7]=s=>a.value.auctionInfo.auctionType=s)},{default:l(()=>[t(d,{value:1},{default:l(()=>e[49]||(e[49]=[i("正常")])),_:1,__:[49]}),t(d,{value:2},{default:l(()=>e[50]||(e[50]=[i("减价")])),_:1,__:[50]}),t(d,{value:3},{default:l(()=>e[51]||(e[51]=[i("盲拍")])),_:1,__:[51]}),t(d,{value:4},{default:l(()=>e[52]||(e[52]=[i("混合式报价")])),_:1,__:[52]}),t(d,{value:5},{default:l(()=>e[53]||(e[53]=[i("盲拍指定版")])),_:1,__:[53]})]),_:1},8,["value"])]),_:1})])])])):_("",!0),a.value.serviceType===1?(f(),m("div",ce,[e[56]||(e[56]=o("div",{class:"section-title"},"资料上传",-1)),o("div",ge,[o("div",_e,[t(u,{label:"封面图片",name:["other","coverImage"],class:"upload-item"},{default:l(()=>[o("div",be,[t(Y,{value:a.value.other.coverImage,"onUpdate:value":e[8]||(e[8]=s=>a.value.other.coverImage=s),multiple:!1,"max-count":1,accept:"image/*","list-type":"picture-card","file-type":"image",class:"upload-component upload-normal"},null,8,["value"]),e[55]||(e[55]=o("div",{class:"upload-tip"},"建议尺寸800*800 像素，不超过10MB",-1))])]),_:1})])])])):_("",!0),a.value.serviceType===1?(f(),m("div",ye,[e[57]||(e[57]=o("div",{class:"section-title"},"拍卖公告",-1)),o("div",Ie,[t(u,{label:"拍卖公告",name:["auctionInfo","noticeContent"],class:"form-item-full"},{default:l(()=>[t(S,{value:a.value.auctionInfo.noticeContent,"onUpdate:value":e[9]||(e[9]=s=>a.value.auctionInfo.noticeContent=s),placeholder:"请输入拍卖公告内容","auto-focus":!1},null,8,["value"])]),_:1})])])):_("",!0),a.value.serviceType===1?(f(),m("div",Te,[e[58]||(e[58]=o("div",{class:"section-title"},"拍卖须知",-1)),o("div",qe,[t(u,{label:"拍卖须知",name:["auctionInfo","rulesContent"],class:"form-item-full"},{default:l(()=>[t(S,{value:a.value.auctionInfo.rulesContent,"onUpdate:value":e[10]||(e[10]=s=>a.value.auctionInfo.rulesContent=s),placeholder:"请输入拍卖须知内容","auto-focus":!1},null,8,["value"])]),_:1})])])):_("",!0),a.value.serviceType===1?(f(),m("div",we,[e[59]||(e[59]=o("div",{class:"section-title"},"重要声明",-1)),o("div",Ue,[t(u,{label:"重要声明",name:["auctionInfo","statementContent"],class:"form-item-full"},{default:l(()=>[t(S,{value:a.value.auctionInfo.statementContent,"onUpdate:value":e[11]||(e[11]=s=>a.value.auctionInfo.statementContent=s),placeholder:"请输入重要声明内容","auto-focus":!1},null,8,["value"])]),_:1})])])):_("",!0),a.value.serviceType===3?(f(),m("div",xe,[e[60]||(e[60]=o("div",{class:"section-title"},"委托信息",-1)),o("div",De,[t(u,{label:"公告名称",name:["entrustInfo","noticeName"],required:"",class:"form-item-full"},{default:l(()=>[t(T,{value:a.value.entrustInfo.noticeName,"onUpdate:value":e[12]||(e[12]=s=>a.value.entrustInfo.noticeName=s),placeholder:"请输入公告名称",size:"large"},null,8,["value"])]),_:1})])])):_("",!0),a.value.serviceType===2?(f(),m("div",Ye,[e[74]||(e[74]=o("div",{class:"section-title"},"基本信息",-1)),o("div",Ce,[o("div",Ne,[t(u,{label:"处置单位",name:["entrustInfo","title"],required:"",class:"asset-item"},{default:l(()=>[t(T,{value:a.value.entrustInfo.title,"onUpdate:value":e[13]||(e[13]=s=>a.value.entrustInfo.title=s),placeholder:"处置单位",size:"large",disabled:"",readonly:""},null,8,["value"])]),_:1}),t(u,{label:"资产名称",name:["basicInfo","assetName"],required:"",class:"asset-item"},{default:l(()=>[t(T,{value:a.value.basicInfo.assetName,"onUpdate:value":e[14]||(e[14]=s=>a.value.basicInfo.assetName=s),placeholder:"请输入资产名称",size:"large"},null,8,["value"])]),_:1}),t(u,{label:"资产编号",name:["basicInfo","assetNo"],class:"asset-item"},{default:l(()=>[t(T,{value:a.value.basicInfo.assetNo,"onUpdate:value":e[15]||(e[15]=s=>a.value.basicInfo.assetNo=s),placeholder:"资产编号",size:"large",disabled:"",readonly:""},null,8,["value"])]),_:1})]),o("div",ke,[t(u,{label:"资产类型",name:["basicInfo","assetType"],required:"",class:"asset-item"},{default:l(()=>[t(c,{value:a.value.basicInfo.assetType,"onUpdate:value":e[16]||(e[16]=s=>a.value.basicInfo.assetType=s),placeholder:"请选择资产类型",size:"large"},{default:l(()=>[t(n,{value:1},{default:l(()=>e[61]||(e[61]=[i("设备类")])),_:1,__:[61]}),t(n,{value:2},{default:l(()=>e[62]||(e[62]=[i("车辆类")])),_:1,__:[62]}),t(n,{value:3},{default:l(()=>e[63]||(e[63]=[i("房产类")])),_:1,__:[63]}),t(n,{value:4},{default:l(()=>e[64]||(e[64]=[i("其他")])),_:1,__:[64]})]),_:1},8,["value"])]),_:1}),t(u,{label:"资产数量",name:["basicInfo","quantity"],required:"",class:"asset-item"},{default:l(()=>[t(T,{value:a.value.basicInfo.quantity,"onUpdate:value":e[17]||(e[17]=s=>a.value.basicInfo.quantity=s),placeholder:"请输入资产数量",size:"large"},null,8,["value"])]),_:1}),t(u,{label:"计量单位",name:["basicInfo","unit"],required:"",class:"asset-item"},{default:l(()=>[t(c,{value:a.value.basicInfo.unit,"onUpdate:value":e[18]||(e[18]=s=>a.value.basicInfo.unit=s),placeholder:"请选择计量单位",size:"large"},{default:l(()=>[t(n,{value:"台"},{default:l(()=>e[65]||(e[65]=[i("台")])),_:1,__:[65]}),t(n,{value:"辆"},{default:l(()=>e[66]||(e[66]=[i("辆")])),_:1,__:[66]}),t(n,{value:"套"},{default:l(()=>e[67]||(e[67]=[i("套")])),_:1,__:[67]}),t(n,{value:"个"},{default:l(()=>e[68]||(e[68]=[i("个")])),_:1,__:[68]}),t(n,{value:"件"},{default:l(()=>e[69]||(e[69]=[i("件")])),_:1,__:[69]}),t(n,{value:"批"},{default:l(()=>e[70]||(e[70]=[i("批")])),_:1,__:[70]}),t(n,{value:"米"},{default:l(()=>e[71]||(e[71]=[i("米")])),_:1,__:[71]})]),_:1},8,["value"])]),_:1})]),o("div",Se,[t(u,{label:"是否展示实际数量",name:["basicInfo","quantityFlag"],required:"",class:"asset-detail-item"},{default:l(()=>[t(q,{value:a.value.basicInfo.quantityFlag,"onUpdate:value":e[19]||(e[19]=s=>a.value.basicInfo.quantityFlag=s)},{default:l(()=>[t(d,{value:1},{default:l(()=>e[72]||(e[72]=[i("是")])),_:1,__:[72]}),t(d,{value:0},{default:l(()=>e[73]||(e[73]=[i("否")])),_:1,__:[73]})]),_:1},8,["value"])]),_:1})])])])):_("",!0),a.value.serviceType===2?(f(),m("div",ze,[e[91]||(e[91]=o("div",{class:"section-title"},"资产详情",-1)),o("div",Me,[o("div",Ee,[t(u,{label:"使用年限",name:["basicInfo","serviceLife"],required:"",class:"asset-detail-item"},{default:l(()=>[t(k,{value:a.value.basicInfo.serviceLife,"onUpdate:value":e[20]||(e[20]=s=>a.value.basicInfo.serviceLife=s),placeholder:"请输入使用年限",size:"large",style:{width:"100%"},min:0,"addon-after":"年"},null,8,["value"])]),_:1}),t(u,{label:"新旧程度",name:["basicInfo","depreciationDegree"],required:"",class:"asset-detail-item"},{default:l(()=>[t(c,{value:a.value.basicInfo.depreciationDegree,"onUpdate:value":e[21]||(e[21]=s=>a.value.basicInfo.depreciationDegree=s),placeholder:"请选择新旧程度",size:"large"},{default:l(()=>[t(n,{value:1},{default:l(()=>e[75]||(e[75]=[i("九成新")])),_:1,__:[75]}),t(n,{value:2},{default:l(()=>e[76]||(e[76]=[i("八成新")])),_:1,__:[76]}),t(n,{value:3},{default:l(()=>e[77]||(e[77]=[i("七成新")])),_:1,__:[77]}),t(n,{value:4},{default:l(()=>e[78]||(e[78]=[i("六成新")])),_:1,__:[78]}),t(n,{value:5},{default:l(()=>e[79]||(e[79]=[i("五成新")])),_:1,__:[79]}),t(n,{value:6},{default:l(()=>e[80]||(e[80]=[i("四成新")])),_:1,__:[80]}),t(n,{value:7},{default:l(()=>e[81]||(e[81]=[i("三成新")])),_:1,__:[81]}),t(n,{value:8},{default:l(()=>e[82]||(e[82]=[i("二成新")])),_:1,__:[82]}),t(n,{value:9},{default:l(()=>e[83]||(e[83]=[i("一成新")])),_:1,__:[83]})]),_:1},8,["value"])]),_:1}),t(u,{label:"当前状态",name:["basicInfo","currentStatus"],required:"",class:"asset-detail-item"},{default:l(()=>[t(q,{value:a.value.basicInfo.currentStatus,"onUpdate:value":e[22]||(e[22]=s=>a.value.basicInfo.currentStatus=s)},{default:l(()=>[t(d,{value:1},{default:l(()=>e[84]||(e[84]=[i("在用")])),_:1,__:[84]}),t(d,{value:2},{default:l(()=>e[85]||(e[85]=[i("闲置")])),_:1,__:[85]}),t(d,{value:3},{default:l(()=>e[86]||(e[86]=[i("报废")])),_:1,__:[86]})]),_:1},8,["value"])]),_:1})]),o("div",He,[t(u,{label:"评估价值",name:["basicInfo","appraisalValue"],required:"",class:"asset-detail-item"},{default:l(()=>[t(k,{value:a.value.basicInfo.appraisalValue,"onUpdate:value":e[23]||(e[23]=s=>a.value.basicInfo.appraisalValue=s),placeholder:"请输入评估价值",size:"large",style:{width:"100%"},min:0,precision:2,"addon-after":"元"},null,8,["value"])]),_:1}),t(u,{label:"处置底价",name:["basicInfo","disposalPrice"],required:"",class:"asset-detail-item"},{default:l(()=>[t(k,{value:a.value.basicInfo.disposalPrice,"onUpdate:value":e[24]||(e[24]=s=>a.value.basicInfo.disposalPrice=s),placeholder:"请输入处置底价",size:"large",style:{width:"100%"},min:0,precision:2,"addon-after":"元"},null,8,["value"])]),_:1}),t(u,{label:"处置时间",name:["basicInfo","disposalStartTime"],required:"",class:"asset-detail-item"},{default:l(()=>[t(A,{value:I.value,"onUpdate:value":e[25]||(e[25]=s=>I.value=s),"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:["开始时间","结束时间"],size:"large",style:{width:"100%"},onChange:R},null,8,["value"])]),_:1})]),o("div",Ve,[t(u,{label:"付款方式",name:["basicInfo","paymentMethod"],required:"",class:"asset-detail-item"},{default:l(()=>[t(q,{value:a.value.basicInfo.paymentMethod,"onUpdate:value":e[26]||(e[26]=s=>a.value.basicInfo.paymentMethod=s)},{default:l(()=>[t(d,{value:1},{default:l(()=>e[87]||(e[87]=[i("全款")])),_:1,__:[87]}),t(d,{value:2},{default:l(()=>e[88]||(e[88]=[i("分期")])),_:1,__:[88]})]),_:1},8,["value"])]),_:1}),t(u,{label:"是否含税",name:["basicInfo","isTaxIncluded"],required:"",class:"asset-detail-item"},{default:l(()=>[t(q,{value:a.value.basicInfo.isTaxIncluded,"onUpdate:value":e[27]||(e[27]=s=>a.value.basicInfo.isTaxIncluded=s)},{default:l(()=>[t(d,{value:"0"},{default:l(()=>e[89]||(e[89]=[i("否")])),_:1,__:[89]}),t(d,{value:"1"},{default:l(()=>e[90]||(e[90]=[i("是")])),_:1,__:[90]})]),_:1},8,["value"])]),_:1}),t(u,{label:"税点",name:["basicInfo","taxRate"],required:a.value.basicInfo.isTaxIncluded!=="0",class:"asset-detail-item"},{default:l(()=>[t(k,{value:a.value.basicInfo.taxRate,"onUpdate:value":e[28]||(e[28]=s=>a.value.basicInfo.taxRate=s),placeholder:"请输入税点",size:"large",style:{width:"100%"},min:0,max:100,precision:2,"addon-after":"%",disabled:a.value.basicInfo.isTaxIncluded==="0"},null,8,["value","disabled"])]),_:1},8,["required"])])])])):_("",!0),a.value.serviceType!==1?(f(),m("div",Fe,[o("h3",Pe,le(a.value.serviceType===3?"所属地区":"存放位置"),1),a.value.serviceType===3?(f(),m("div",Le,[o("div",Je,[t(u,{name:["location","province"],class:"location-area-item"},{default:l(()=>[t(M,{province:a.value.location.province,"onUpdate:province":e[29]||(e[29]=s=>a.value.location.province=s),city:a.value.location.city,"onUpdate:city":e[30]||(e[30]=s=>a.value.location.city=s),area:a.value.location.area,"onUpdate:area":e[31]||(e[31]=s=>a.value.location.area=s),placeholder:"请选择所属地区",level:3},null,8,["province","city","area"])]),_:1})])])):(f(),m("div",Re,[o("div",Be,[t(u,{name:["location","province"],class:"location-area-item"},{default:l(()=>[t(M,{province:a.value.location.province,"onUpdate:province":e[32]||(e[32]=s=>a.value.location.province=s),city:a.value.location.city,"onUpdate:city":e[33]||(e[33]=s=>a.value.location.city=s),area:a.value.location.area,"onUpdate:area":e[34]||(e[34]=s=>a.value.location.area=s),placeholder:"请选择存放位置",level:3},null,8,["province","city","area"])]),_:1})]),o("div",Xe,[t(u,{label:"详细地址",name:["location","detailAddress"],required:"",class:"detail-address-item"},{default:l(()=>[t(T,{value:a.value.location.detailAddress,"onUpdate:value":e[35]||(e[35]=s=>a.value.location.detailAddress=s),placeholder:"请输入详细地址",size:"large"},{suffix:l(()=>[t(G,{type:"text",onClick:B,loading:r.locationLoading,class:"location-btn",size:"small"},{icon:l(()=>[t(se(J))]),_:1},8,["loading"])]),_:1},8,["value"])]),_:1})])]))])):_("",!0),a.value.serviceType!==1?(f(),m("div",Oe,[e[95]||(e[95]=o("h3",{class:"section-title"},"资料上传",-1)),a.value.serviceType===3?(f(),m("div",Ae,[o("div",Ge,[t(u,{label:"采购附件",name:["materials","attachments"],required:"",class:"upload-item"},{default:l(()=>[o("div",je,[t(Y,{value:a.value.materials.attachments,"onUpdate:value":e[36]||(e[36]=s=>a.value.materials.attachments=s),multiple:!0,"max-count":5,accept:".pdf,.doc,.docx,.xls,.xlsx","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[92]||(e[92]=o("div",{class:"upload-tip"},"支持PDF、DOC、DOCX、XLS、XLSX格式",-1))])]),_:1})])])):a.value.serviceType===2?(f(),m("div",$e,[o("div",Ke,[t(u,{label:"标的图片",name:["materials","images"],class:"upload-item"},{default:l(()=>[o("div",Qe,[t(Y,{value:a.value.materials.images,"onUpdate:value":e[37]||(e[37]=s=>a.value.materials.images=s),multiple:!0,"max-count":10,accept:"image/*","list-type":"picture-card","file-type":"image","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[93]||(e[93]=o("div",{class:"upload-tip"},"最多可上传10张图片，支持JPG、PNG格式，单个文件不超过5MB",-1))])]),_:1})]),o("div",We,[t(u,{label:"附件上传",name:["materials","attachments"],class:"upload-item"},{default:l(()=>[o("div",Ze,[t(Y,{value:a.value.materials.attachments,"onUpdate:value":e[38]||(e[38]=s=>a.value.materials.attachments=s),multiple:!0,"max-count":5,accept:".pdf,.doc,.docx,.xls,.xlsx","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[94]||(e[94]=o("div",{class:"upload-tip"},"支持PDF、DOC、DOCX、XLS、XLSX格式",-1))])]),_:1})])])):_("",!0)])):_("",!0),a.value.serviceType===2?(f(),m("div",he,[e[96]||(e[96]=o("h3",{class:"section-title"},"特殊说明",-1)),o("div",ea,[t(u,{label:"特殊说明",name:["materials","specialNote"],class:"upload-item"},{default:l(()=>[t(j,{value:a.value.materials.specialNote,"onUpdate:value":e[39]||(e[39]=s=>a.value.materials.specialNote=s),placeholder:"请输入特殊说明（如有）",rows:3,size:"large"},null,8,["value"])]),_:1})])])):_("",!0)]),_:1},8,["model","rules"])])}}})),ca=oe(ta,[["__scopeId","data-v-2057e47f"]]);export{ca as default};
