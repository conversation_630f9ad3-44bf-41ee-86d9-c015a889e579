var k=Object.defineProperty;var w=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var q=(e,t,r)=>t in e?k(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,x=(e,t)=>{for(var r in t||(t={}))E.call(t,r)&&q(e,r,t[r]);if(w)for(var r of w(t))L.call(t,r)&&q(e,r,t[r]);return e};var g=(e,t,r)=>new Promise((f,s)=>{var D=a=>{try{b(r.next(a))}catch(m){s(m)}},y=a=>{try{b(r.throw(a))}catch(m){s(m)}},b=a=>a.done?f(a.value):Promise.resolve(a.value).then(D,y);b((r=r.apply(e,t)).next())});import{d as O,f as R,e as N,u as l,ag as v,aB as V,ar as J,aD as n,k as i,G as z,aE as X}from"./vue-vendor-dy9k-Yad.js";import{B as G}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{j as u,ac as H,u as K}from"./index-CCWaWN5g.js";import{M as Q}from"./antd-vue-vendor-me9YkNVC.js";import{u as $}from"./useForm-CgkFTrrO.js";import{B as W}from"./BasicForm-DBcXiHk0.js";const Y={1:{dbDriver:"com.mysql.jdbc.Driver"},4:{dbDriver:"com.mysql.cj.jdbc.Driver"},2:{dbDriver:"oracle.jdbc.OracleDriver"},3:{dbDriver:"com.microsoft.sqlserver.jdbc.SQLServerDriver"},5:{dbDriver:"org.mariadb.jdbc.Driver"},6:{dbDriver:"org.postgresql.Driver"},7:{dbDriver:"dm.jdbc.driver.DmDriver"},8:{dbDriver:"com.kingbase8.Driver"},9:{dbDriver:"com.oscar.Driver"},10:{dbDriver:"org.sqlite.JDBC"},11:{dbDriver:"com.ibm.db2.jcc.DB2Driver"},12:{dbDriver:"org.hsqldb.jdbc.JDBCDriver"},13:{dbDriver:"org.apache.derby.jdbc.ClientDriver"},14:{dbDriver:"org.h2.Driver"},15:{dbDriver:""}},Z={1:{dbUrl:"*******************************************************************************************"},4:{dbUrl:"*******************************************************************************************&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai"},2:{dbUrl:"*************************************"},3:{dbUrl:"**************************************************************************"},5:{dbUrl:"*****************************************************************************"},6:{dbUrl:"*******************************************"},7:{dbUrl:"jdbc:dm://127.0.0.1:5236/?jeecg-boot&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8"},8:{dbUrl:"*******************************************"},9:{dbUrl:"******************************************"},10:{dbUrl:"*************************"},11:{dbUrl:"*************************************"},12:{dbUrl:"***************************************"},13:{dbUrl:"**************************************"},14:{dbUrl:"jdbc:h2:tcp://127.0.0.1:8082/jeecg-boot"},15:{dbUrl:""}},be=[{title:"数据源名称",dataIndex:"name",width:200,align:"left"},{title:"数据库类型",dataIndex:"dbType_dictText",width:200},{title:"驱动类",dataIndex:"dbDriver",width:200},{title:"数据源地址",dataIndex:"dbUrl"},{title:"用户名",dataIndex:"dbUsername",width:200}],me=[{field:"name",label:"数据源名称",component:"Input",colProps:{span:8}},{field:"dbType",label:"数据库类型",component:"JDictSelectTag",colProps:{span:8},componentProps:()=>({dictCode:"database_type"})}],A=[{field:"id",label:"id",component:"Input",show:!1},{field:"code",label:"数据源编码",component:"Input",required:!0,dynamicDisabled:({values:e})=>!!e.id},{field:"name",label:"数据源名称",component:"Input",required:!0},{field:"dbType",label:"数据库类型",component:"JDictSelectTag",required:!0,componentProps:({formModel:e})=>({dictCode:"database_type",onChange:t=>{e=Object.assign(e,Y[t],Z[t])}})},{field:"dbDriver",label:"驱动类",required:!0,component:"Input"},{field:"dbUrl",label:"数据源地址",required:!0,component:"Input"},{field:"dbUsername",label:"用户名",required:!0,component:"Input"},{field:"dbPassword",label:"密码",required:!0,component:"InputPassword",slot:"pwd"},{field:"remark",label:"备注",component:"InputTextArea"}];const pe="sys/dataSource/exportXls",ge="sys/dataSource/importExcel",ve=e=>u.get({url:"/sys/dataSource/list",params:e}),ee=(e,t)=>{let r=t?"/sys/dataSource/edit":"/sys/dataSource/add";return u.post({url:r,params:e})},te=e=>u.get({url:"/sys/dataSource/queryById",params:e}),fe=(e,t)=>u.delete({url:"/sys/dataSource/delete",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),re=e=>u.post({url:"/online/cgreport/api/testConnection",params:e}),De=(e,t)=>{Q.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>u.delete({url:"/sys/dataSource/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},oe=O({__name:"DataSourceModal",emits:["register","success"],setup(e,{emit:t}){const{createMessage:r}=K(),f=t,s=R(!0),[D,{getFieldsValue:y,resetFields:b,validateFields:a,setFieldsValue:m,validate:T}]=$({schemas:A,showActionButtonGroup:!1}),[I,{setModalProps:j,closeModal:B}]=H(o=>g(null,null,function*(){yield b(),j({confirmLoading:!1}),s.value=!!(o!=null&&o.isUpdate),l(s)&&(o.record=yield te({id:o.record.id}),yield m(x({},o.record)))})),C=N(()=>l(s)?"编辑数据源":"新增数据源");function M(){return g(this,null,function*(){let o=["dbType","dbDriver","dbUrl","dbName","dbUsername","dbPassword"],c=y(o),h={};o.forEach(d=>h[d]={value:c[d],errors:null}),yield a(o).then(d=>{let S=r.loading("连接中....",0);re(d).then(p=>{p.success&&r.success("连接成功")}).catch(p=>{}).finally(()=>S())})})}function P(o){return g(this,null,function*(){try{let c=yield T();j({confirmLoading:!0}),yield ee(c,s.value),B(),f("success")}finally{j({confirmLoading:!1})}})}return(o,c)=>{const h=v("a-input-password"),d=v("a-col"),S=v("a-button"),p=v("a-row");return J(),V(l(G),X(o.$attrs,{onRegister:l(I),title:C.value,onOk:P,width:"40%"}),{default:n(()=>[i(l(W),{onRegister:l(D)},{pwd:n(({model:U,field:_})=>[i(p,{gutter:8},{default:n(()=>[i(d,{sm:15,md:16,lg:17,xl:19},{default:n(()=>[i(h,{value:U[_],"onUpdate:value":F=>U[_]=F,placeholder:"请输入密码"},null,8,["value","onUpdate:value"])]),_:2},1024),i(d,{sm:9,md:7,lg:7,xl:5},{default:n(()=>[i(S,{type:"primary",style:{width:"100%"},onClick:M},{default:n(()=>c[0]||(c[0]=[z("测试")])),_:1,__:[0]})]),_:1})]),_:2},1024)]),_:1},8,["onRegister"])]),_:1},16,["onRegister","title"])}}}),ye=Object.freeze(Object.defineProperty({__proto__:null,default:oe},Symbol.toStringTag,{value:"Module"}));export{ye as D,oe as _,pe as a,De as b,be as c,fe as d,ve as e,ge as g,me as s};
