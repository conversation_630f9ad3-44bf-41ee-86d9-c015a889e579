import{bx as e}from"./index-CCWaWN5g.js";const i=[{title:"接口名称",align:"center",dataIndex:"name"},{title:"请求方法",align:"center",dataIndex:"requestMethod"},{title:"接口地址",align:"center",dataIndex:"requestUrl"},{title:"IP 黑名单",align:"center",dataIndex:"blackList"},{title:"状态",align:"center",dataIndex:"status"},{title:"创建人",align:"center",dataIndex:"createBy"},{title:"创建时间",align:"center",dataIndex:"createTime"}],n=[{label:"接口名称",field:"name",component:"JInput"},{label:"创建人",field:"createBy",component:"JInput"}],r=[{label:"接口名称",field:"name",component:"Input",dynamicRules:({model:t,schema:l})=>[{required:!0,message:"请输入接口名称!"}]},{label:"请求方法",field:"requestMethod",component:"JSearchSelect",componentProps:{dictOptions:[{text:"POST",value:"POST"},{text:"GET",value:"GET"},{text:"HEAD",value:"HEAD"},{text:"PUT",value:"PUT"},{text:"PATCH",value:"PATCH"},{text:"DELETE",value:"DELETE"},{text:"OPTIONS",value:"OPTIONS"},{text:"TRACE",value:"TRACE"}]},dynamicRules:({model:t,schema:l})=>[{required:!0,message:"请输入请求方法!"}]},{label:"接口地址",field:"requestUrl",component:"Input",dynamicDisabled:!0},{label:"IP 黑名单",field:"blackList",component:"Input"},{label:"请求体内容",component:"Input",field:"body"},{label:"原始地址",field:"originUrl",component:"Input"},{label:"删除标识",field:"delFlag",component:"Input",defaultValue:0,show:!1},{label:"状态",field:"status",component:"Input",defaultValue:"1",show:!1},{label:"",field:"id",component:"Input",show:!1}],d=[{title:"请求头Key",align:"center",dataIndex:"headerKey"},{title:"是否必填",align:"center",dataIndex:"required_dictText"},{title:"默认值",align:"center",dataIndex:"defaultValue"},{title:"备注",align:"center",dataIndex:"note"}],o=[{title:"参数Key",align:"center",dataIndex:"paramKey"},{title:"是否必填",align:"center",dataIndex:"required_dictText"},{title:"默认值",align:"center",dataIndex:"defaultValue"},{title:"备注",align:"center",dataIndex:"note"}],u=[{title:"请求头Key",key:"headerKey",type:e.input,width:"200px",placeholder:"请输入${title}",defaultValue:""},{title:"是否必填",key:"required",type:e.checkbox,options:[],width:"100px",placeholder:"请输入${title}",defaultValue:"",customValue:["1","0"]},{title:"默认值",key:"defaultValue",type:e.input,width:"200px",placeholder:"请输入${title}",defaultValue:""},{title:"备注",key:"note",type:e.input,width:"200px",placeholder:"请输入${title}",defaultValue:""}],p=[{title:"参数Key",key:"paramKey",type:e.input,width:"200px",placeholder:"请输入${title}",defaultValue:""},{title:"是否必填",key:"required",type:e.checkbox,options:[],width:"100px",placeholder:"请输入${title}",defaultValue:"",customValue:["1","0"]},{title:"默认值",key:"defaultValue",type:e.input,width:"200px",placeholder:"请输入${title}",defaultValue:""},{title:"备注",key:"note",type:e.input,width:"200px",placeholder:"请输入${title}",defaultValue:""}],c={name:{title:"接口名称",order:0,view:"text",type:"string"},requestMethod:{title:"请求方法",order:1,view:"list",type:"string",dictCode:""},requestUrl:{title:"接口地址",order:2,view:"text",type:"string"},blackList:{title:"IP 黑名单",order:3,view:"text",type:"string"},status:{title:"状态",order:5,view:"number",type:"number"},createBy:{title:"创建人",order:6,view:"text",type:"string"},createTime:{title:"创建时间",order:7,view:"datetime",type:"string"},openApiHeader:{title:"请求头表",view:"table",fields:{headerKey:{title:"请求头Key",order:1,view:"text",type:"string"},required:{title:"是否必填",order:2,view:"number",type:"number",dictCode:"yn"},defaultValue:{title:"默认值",order:3,view:"text",type:"string"},note:{title:"备注",order:4,view:"text",type:"string"}}},openApiParam:{title:"请求参数部分",view:"table",fields:{paramKey:{title:"参数Key",order:1,view:"text",type:"string"},required:{title:"是否必填",order:2,view:"number",type:"number",dictCode:"yn"},defaultValue:{title:"默认值",order:3,view:"text",type:"string"},note:{title:"备注",order:4,view:"text",type:"string"}}}};export{n as a,p as b,i as c,d,o as e,r as f,u as o,c as s};
