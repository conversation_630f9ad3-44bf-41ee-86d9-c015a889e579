import{B as _}from"./index-Diw57m_E.js";import{f as b,e as j,ag as F,aB as L,ar as A,aE as J,aD as P,k as S}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{cs as q,u as G,ac as E,j as H}from"./index-CCWaWN5g.js";import{o as K}from"./antd-vue-vendor-me9YkNVC.js";import{u as z}from"./useForm-CgkFTrrO.js";import{B as D}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";var X=Object.defineProperty,T=Object.getOwnPropertySymbols,Y=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable,k=(a,o,t)=>o in a?X(a,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[o]=t,$=(a,o)=>{for(var t in o||(o={}))Y.call(o,t)&&k(a,t,o[t]);if(T)for(var t of T(o))Z.call(o,t)&&k(a,t,o[t]);return a},h=(a,o,t)=>new Promise((n,u)=>{var r=l=>{try{p(t.next(l))}catch(s){u(s)}},g=l=>{try{p(t.throw(l))}catch(s){u(s)}},p=l=>l.done?n(l.value):Promise.resolve(l.value).then(r,g);p((t=t.apply(a,o)).next())});const Q={name:"LinkTableConfigModal",emits:["success","register"],components:{BasicModal:_,BasicForm:D},setup(a,{emit:o}){const t=b(!1),{createMessage:n}=G(),u=b("");let r={};const[g,{closeModal:p}]=E(e=>h(this,null,function*(){r=$({},e.record),yield w({dictTable:e.record.dictTable}),setTimeout(()=>h(this,null,function*(){let m=K(e.record,"dictTable");yield w(m),yield I()}),200),u.value=e.fieldName})),l=b(""),s=b(""),v=b([]),y=b([]);function x(e){return h(this,null,function*(){if(e){const m="/online/cgform/field/listByHeadCode",d=yield H.get({url:m,params:{headCode:e}});if(d&&d.length>0){let c=d.filter(i=>i.dbFieldName!="id"&&i.dbIsPersist==1&&i.isShowList==1);c.length>0?v.value=c.map(i=>({text:i.dbFieldTxt,value:i.dbFieldName})):v.value=[];let f=d.filter(i=>i.dbFieldName!="id"&&i.fieldShowType=="image"&&i.dbIsPersist==1);f.length>0?y.value=f.map(i=>({text:i.dbFieldTxt,value:i.dbFieldName})):y.value=[{text:"无图片字段可以选择",value:"",key:"",disabled:!0}]}else v.value=[],y.value=[{text:"无图片字段可以选择",value:"",key:"",disabled:!0}]}})}function B(e){return h(this,null,function*(){l.value="",s.value="",yield x(e)})}const M=j(()=>{let e=v.value,m=l.value,d=s.value;return e.filter(c=>c.value!=m&&c.value!=d)}),O=[{label:"rowKey",field:"rowKey",component:"Input",show:!1},{label:"dictField",field:"dictField",component:"Input",defaultValue:"id",show:!1},{label:"字段描述",field:"dbFieldTxt",component:"Input",required:!0},{label:"关联表",field:"dictTable",component:"JSearchSelect",required:!0,componentProps:({formActionType:e})=>({dict:"onl_cgform_head where copy_type = 0,table_txt,table_name",pageSize:10,async:!0,immediateChange:!0,popContainer:".link-table-config-modal",params:{order:"desc",column:"create_time"},onChange:m=>h(this,null,function*(){(r.titleField||r.otherFields)&&(yield e.setFieldsValue({titleField:"",otherFields:"",imageField:""}),yield e.clearValidate()),yield B(m)})})},{label:"标题字段",field:"titleField",component:"JSearchSelect",required:!0,componentProps:{async:!1,popContainer:".link-table-config-modal",dictOptions:v,immediateChange:!0,onChange:e=>{l.value=e,r.titleField=e}}},{label:"封面图片",field:"imageField",component:"JSearchSelect",componentProps:{async:!1,popContainer:".link-table-config-modal",dictOptions:y,immediateChange:!0,onChange:e=>{s.value=e,r.imageFieldName=e}}},{label:"其他字段",field:"otherFields",component:"JSelectMultiple",componentProps:({schema:e,tableAction:m,formActionType:d,formModel:c})=>({popContainer:".link-table-config-modal",options:M.value,onChange:f=>{if(f.split(",").length>6){const i=f.split(",");i.pop();const C=i.join(",");setTimeout(()=>{c.otherFields=C,r.otherFields=C},0),n.warning("最多选择6个字段~")}else r.otherFields=f}})},{label:"显示方式",field:"showType",component:"Select",defaultValue:"card",componentProps:{options:[{label:"卡片",value:"card"},{label:"下拉框",value:"select"}]}},{label:"是否多选",field:"multiSelect",component:"RadioGroup",defaultValue:!1,componentProps:{options:[{label:"否",value:!1},{label:"是",value:!0}]}},{label:"列表只读",field:"isListReadOnly",component:"RadioGroup",defaultValue:!1,componentProps:{options:[{label:"否",value:!1},{label:"是",value:!0}]}}],[N,{validate:V,setFieldsValue:w,clearValidate:I,resetFields:W}]=z({schemas:O,showActionButtonGroup:!1,labelAlign:"right"});function R(){return h(this,null,function*(){let e=yield V();e.fieldName=u.value,o("success",e),p()})}return{registerModal:g,spinningLoading:t,registerForm:N,handleSubmit:R}}};function U(a,o,t,n,u,r){const g=F("BasicForm"),p=F("a-spin"),l=F("BasicModal");return A(),L(l,J({wrapClassName:"link-table-config-modal"},a.$attrs,{title:"关联记录配置",onRegister:n.registerModal,keyboard:"",canFullscreen:!1,cancelText:"关闭",onOk:n.handleSubmit}),{default:P(()=>[S(p,{spinning:n.spinningLoading},{default:P(()=>[S(g,{onRegister:n.registerForm},null,8,["onRegister"])]),_:1},8,["spinning"])]),_:1},16,["onRegister","onOk"])}const et=q(Q,[["render",U]]);export{et as default};
