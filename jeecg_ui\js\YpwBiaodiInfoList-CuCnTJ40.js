var J=Object.defineProperty,W=Object.defineProperties;var Z=Object.getOwnPropertyDescriptors;var I=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,oo=Object.prototype.propertyIsEnumerable;var j=(n,o,t)=>o in n?J(n,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[o]=t,T=(n,o)=>{for(var t in o||(o={}))$.call(o,t)&&j(n,t,o[t]);if(I)for(var t of I(o))oo.call(o,t)&&j(n,t,o[t]);return n},A=(n,o)=>W(n,Z(o));var k=(n,o,t)=>new Promise((B,b)=>{var u=a=>{try{d(t.next(a))}catch(c){b(c)}},v=a=>{try{d(t.throw(a))}catch(c){b(c)}},d=a=>a.done?B(a.value):Promise.resolve(a.value).then(u,v);d((t=t.apply(n,o)).next())});import{d as M,r as E,f as to,ag as l,v as eo,aq as io,ar as m,k as p,aD as r,u as s,q as g,aB as _,ah as no,G as f}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{useListPage as ro}from"./useListPage-Soxgnx9a.js";import ao from"./YpwBiaodiInfoModal-Bpj9mHw7.js";import{s as po,b as so,g as lo,a as mo,c as uo,d as co,l as _o,e as fo}from"./YpwBiaodiInfo.api-Bht6QChW.js";import{ah as bo,ad as wo,a as yo}from"./index-CCWaWN5g.js";import{Q as go}from"./componentMap-Bkie1n3v.js";import ho from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./renderUtils-D7XVOFwj.js";const Co=M({name:"ypw-ypwBiaodiInfo"}),xo=M(A(T({},Co),{setup(n){const o=E({}),t=to([]),B=bo(),[b,{openModal:u}]=wo(),{prefixCls:v,tableContext:d,onExportXls:a,onImportXls:c}=ro({tableProps:{title:"标的详情",api:_o,columns:co,canResize:!1,formConfig:{schemas:uo,autoSubmitOnEnter:!0,showAdvancedButton:!0,fieldMapToNumber:[["bdBaoliujia",["bdBaoliujia_begin","bdBaoliujia_end"]],["bdBaozhengjin",["bdBaozhengjin_begin","bdBaozhengjin_end"]]],fieldMapToTime:[]},actionColumn:{width:120,fixed:"right"},beforeFetch:e=>Object.assign(e,o)},exportConfig:{name:"标的详情",url:mo,params:o},importConfig:{url:lo,success:w}}),[R,{reload:S},{rowSelection:U,selectedRowKeys:h}]=d,q=E(po);function F(e){Object.keys(e).map(i=>{o[i]=e[i]}),S()}function z(){u(!0,{isUpdate:!1,showFooter:!0})}function N(e){u(!0,{record:e,isUpdate:!0,showFooter:!0})}function O(e){u(!0,{record:e,isUpdate:!0,showFooter:!1})}function Q(e){return k(this,null,function*(){yield fo({id:e.id},w)})}function K(){return k(this,null,function*(){yield so({ids:h.value},w)})}function w(){(h.value=[])&&S()}function L(e){return[{label:"编辑",onClick:N.bind(null,e),auth:"ypw:ypw_biaodi_info:edit"}]}function P(e){return[{label:"详情",onClick:O.bind(null,e)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:Q.bind(null,e),placement:"topLeft"},auth:"ypw:ypw_biaodi_info:delete"}]}return(e,i)=>{const C=l("a-button"),V=l("j-upload-button"),D=l("Icon"),X=l("a-menu-item"),Y=l("a-menu"),G=l("a-dropdown"),H=l("super-query"),y=eo("auth");return m(),io("div",null,[p(s(ho),{onRegister:s(R),rowSelection:s(U)},{tableTitle:r(()=>[g((m(),_(C,{type:"primary",onClick:z,preIcon:"ant-design:plus-outlined"},{default:r(()=>i[0]||(i[0]=[f(" 新增")])),_:1,__:[0]})),[[y,"ypw:ypw_biaodi_info:add"]]),g((m(),_(C,{type:"primary",preIcon:"ant-design:export-outlined",onClick:s(a)},{default:r(()=>i[1]||(i[1]=[f(" 导出")])),_:1,__:[1]},8,["onClick"])),[[y,"ypw:ypw_biaodi_info:exportXls"]]),g((m(),_(V,{type:"primary",preIcon:"ant-design:import-outlined",onClick:s(c)},{default:r(()=>i[2]||(i[2]=[f("导入")])),_:1,__:[2]},8,["onClick"])),[[y,"ypw:ypw_biaodi_info:importExcel"]]),s(h).length>0?(m(),_(G,{key:0},{overlay:r(()=>[p(Y,null,{default:r(()=>[p(X,{key:"1",onClick:K},{default:r(()=>[p(D,{icon:"ant-design:delete-outlined"}),i[3]||(i[3]=f(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:r(()=>[g((m(),_(C,null,{default:r(()=>[i[4]||(i[4]=f("批量操作 ")),p(D,{icon:"mdi:chevron-down"})]),_:1,__:[4]})),[[y,"ypw:ypw_biaodi_info:deleteBatch"]])]),_:1})):no("",!0),p(H,{config:q,onSearch:F},null,8,["config"])]),action:r(({record:x})=>[p(s(go),{actions:L(x),dropDownActions:P(x)},null,8,["actions","dropDownActions"])]),bodyCell:r(({column:x,record:ko,index:Bo,text:vo})=>i[5]||(i[5]=[])),_:1},8,["onRegister","rowSelection"]),p(ao,{onRegister:s(b),onSuccess:w},null,8,["onRegister"])])}}})),Rt=yo(xo,[["__scopeId","data-v-9284e439"]]);export{Rt as default};
