import{f as r,e as m,u,d as l,ag as p,v as f,q as h,ar as v,aq as w,as as C,k as L}from"./vue-vendor-dy9k-Yad.js";import{cs as P,F as y,cQ as x,D as H}from"./index-CCWaWN5g.js";import{createPageContext as D}from"./usePageContext-CxiNGbPs.js";import{useWindowSizeFn as k}from"./useWindowSizeFn-DDbrQbks.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var S=(e,n,t)=>new Promise((a,i)=>{var d=o=>{try{s(t.next(o))}catch(c){i(c)}},g=o=>{try{s(t.throw(o))}catch(c){i(c)}},s=o=>o.done?a(o.value):Promise.resolve(o.value).then(d,g);s((t=t.apply(e,n)).next())});const z=r(0),M=r(0);function O(){const e=r(window.innerHeight),n=r(window.innerHeight),t=m(()=>u(e)-u(z)-u(M)||0);k(()=>{e.value=window.innerHeight},100,{immediate:!0});function a(i){return S(this,null,function*(){n.value=i})}D({contentHeight:t,setPageHeight:a,pageHeight:n})}const q=l({name:"LayoutContent",components:{},setup(){const{prefixCls:e}=y("layout-content"),{getOpenPageLoading:n}=x(),{getLayoutContentMode:t,getPageLoading:a}=H();return O(),{prefixCls:e,getOpenPageLoading:n,getLayoutContentMode:t,getPageLoading:a}}});function B(e,n,t,a,i,d){const g=p("RouterView"),s=f("loading");return h((v(),w("div",{class:C([e.prefixCls,e.getLayoutContentMode])},[L(g)],2)),[[s,e.getOpenPageLoading&&e.getPageLoading]])}const Q=P(q,[["render",B]]);export{Q as default};
