var l=Object.defineProperty;var i=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var s=(o,t,e)=>t in o?l(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,n=(o,t)=>{for(var e in t||(t={}))m.call(t,e)&&s(o,e,t[e]);if(i)for(var e of i(t))c.call(t,e)&&s(o,e,t[e]);return o};import{d as f,f as h,h as u,aq as d,ar as y,aA as g}from"./vue-vendor-dy9k-Yad.js";import{useECharts as b}from"./useECharts-BU6FzBZi.js";import{b as x}from"./props-BGjQktHt.js";import{D as _}from"./index-CCWaWN5g.js";import"./useTimeout-CeTdFD_D.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const z=f({__name:"VisitAnalysisBar",props:n({},x),setup(o){const t=h(null),{setOptions:e}=b(t),{getThemeColor:r}=_(),p=()=>{e({tooltip:{trigger:"axis",axisPointer:{lineStyle:{width:1,color:r.value}}},grid:{left:"1%",right:"1%",top:"2  %",bottom:0,containLabel:!0},xAxis:{type:"category",data:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"]},yAxis:{type:"value",max:8e3,splitNumber:4},series:[{data:[3e3,2e3,3333,5e3,3200,4200,3200,2100,3e3,5100,6e3,3200,4800],type:"bar",barMaxWidth:80,color:r.value}]})};return u(()=>{p()}),(a,v)=>(y(),d("div",{ref_key:"chartRef",ref:t,style:g({height:a.height,width:a.width})},null,4))}});export{z as default};
