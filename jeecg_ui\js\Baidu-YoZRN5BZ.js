var m=(t,i,e)=>new Promise((o,n)=>{var r=a=>{try{p(e.next(a))}catch(c){n(c)}},s=a=>{try{p(e.throw(a))}catch(c){n(c)}},p=a=>a.done?o(a.value):Promise.resolve(a.value).then(r,s);p((e=e.apply(t,i)).next())});import{d as u,f as d,o as f,n as l,u as h,aq as w,ar as B,aA as _}from"./vue-vendor-dy9k-Yad.js";import{useScript as M}from"./useScript-C3Sw6r4a.js";import{a as g}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const y="https://api.map.baidu.com/getscript?v=3.0&ak=OaBvYmKX3pjF7YFUFeeBCeGdy9Zp7xB2&services=&t=20210201100830&s=1",v=u({name:"BaiduMap",props:{width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"}},setup(){const t=d(null),{toPromise:i}=M({src:y});function e(){return m(this,null,function*(){yield i(),yield l();const o=h(t);if(!o)return;const n=window.BMap,r=new n.Map(o),s=new n.Point(116.404,39.915);r.centerAndZoom(s,15),r.enableScrollWheelZoom(!0)})}return f(()=>{e()}),{wrapRef:t}}});function S(t,i,e,o,n,r){return B(),w("div",{ref:"wrapRef",style:_({height:t.height,width:t.width})},null,4)}const R=g(v,[["render",S]]);export{R as default};
