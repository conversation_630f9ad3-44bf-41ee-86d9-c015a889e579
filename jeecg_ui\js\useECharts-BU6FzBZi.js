var x=Object.defineProperty;var k=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,b=Object.prototype.propertyIsEnumerable;var z=(n,t,r)=>t in n?x(n,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[t]=r,D=(n,t)=>{for(var r in t||(t={}))C.call(t,r)&&z(n,r,t[r]);if(k)for(var r of k(t))b.call(t,r)&&z(n,r,t[r]);return n};import{useTimeoutFn as d}from"./useTimeout-CeTdFD_D.js";import{D as y,x as H,z as R,at as S,q as T}from"./index-CCWaWN5g.js";import{e as w,f as h,u as i,n as q,w as B}from"./vue-vendor-dy9k-Yad.js";import O from"./echarts-D8q0NfgS.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./renderers-CGMjx3X9.js";function P(n,t="default"){const{getDarkMode:r}=y(),u=w(()=>t==="default"?r.value:t);let e=null,l=c;const a=h({});let v=()=>{};l=H(c,200);const g=w(()=>u.value!=="dark"?a.value:D({backgroundColor:"transparent"},a.value));function p(s=t){const o=i(n);if(!o||!i(o))return;e=O.init(o,s);const{removeEvent:f}=R({el:window,name:"resize",listener:l});v=f;const{widthRef:F,screenEnum:M}=S();(i(F)<=M.MD||o.offsetHeight===0)&&d(()=>{l()},30)}function m(s,o=!0){var f;if(a.value=s,((f=i(n))==null?void 0:f.offsetHeight)===0){d(()=>{m(i(g))},30);return}q(()=>{d(()=>{!e&&(p(u.value),!e)||(o&&(e==null||e.clear()),e==null||e.setOption(i(g)))},30)})}function c(){e==null||e.resize()}B(()=>u.value,s=>{e&&(e.dispose(),p(s),m(a.value))}),T(()=>{e&&(v(),e.dispose(),e=null)});function E(){return e||p(u.value),e}return{setOptions:m,resize:c,echarts:O,getInstance:E}}export{P as useECharts};
