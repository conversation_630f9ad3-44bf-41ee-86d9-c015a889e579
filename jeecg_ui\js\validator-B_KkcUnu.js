var f=(e,r,t)=>new Promise((s,n)=>{var a=i=>{try{c(t.next(i))}catch(o){n(o)}},m=i=>{try{c(t.throw(i))}catch(o){n(o)}},c=i=>i.done?s(i.value):Promise.resolve(i.value).then(a,m);c((t=t.apply(e,r)).next())});import{h as l}from"./index-CCWaWN5g.js";import{d as u}from"./user.api-mLAlJze4.js";import"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const _={rule(e,r){if(e==="email")return this.email(r);if(e==="phone")return this.phone(r)},email(e){return[{required:e||!1,validator:(r,t)=>f(null,null,function*(){return e==!0&&!t?Promise.reject("请输入邮箱!"):t&&!new RegExp(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/).test(t)?Promise.reject("请输入正确邮箱格式!"):Promise.resolve()}),trigger:"change"}]},phone(e){return[{required:e,validator:(r,t)=>f(null,null,function*(){return e&&!t?Promise.reject("请输入手机号码!"):/^1[3456789]\d{9}$/.test(t)?Promise.resolve():Promise.reject("手机号码格式有误")}),trigger:"change"}]},startTime(e,r){return[{required:r||!1,validator:(t,s)=>r&&!s?Promise.reject("请选择开始时间"):e&&s&&l(e).isBefore(s)?Promise.reject("开始时间需小于结束时间"):Promise.resolve(),trigger:"change"}]},endTime(e,r){return[{required:r||!1,validator:(t,s)=>r&&!s?Promise.reject("请选择结束时间"):e&&s&&l(s).isBefore(e)?Promise.reject("结束时间需大于开始时间"):Promise.resolve(),trigger:"change"}]},confirmPassword(e,r){return[{required:r||!1,validator:(t,s)=>s?s!==e.password?Promise.reject("两次输入的密码不一致!"):Promise.resolve():Promise.reject("密码不能为空")}]},duplicateCheckRule(e,r,t,s,n){return[{validator:(a,m)=>!m&&n?Promise.reject(`请输入${s.label}`):new Promise((c,i)=>{u({tableName:e,fieldName:r,fieldVal:m,dataId:t.id}).then(o=>{o.success?c():i(o.message||"校验失败")}).catch(o=>{i(o.message||"验证失败")})})}]}};function w(e,r,t,s){return f(this,null,function*(){try{const a=yield u({tableName:e,fieldName:r,fieldVal:t,dataId:s});return a.success?Promise.resolve():Promise.reject(a.message||"校验失败")}catch(n){return Promise.reject("校验失败,可能是断网等问题导致的校验失败")}})}export{w as duplicateValidate,_ as rules};
