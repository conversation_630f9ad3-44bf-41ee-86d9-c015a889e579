import{d as r,ag as s,aB as p,ar as t,aE as i,aD as m,aq as n,F as c,aC as l,at as d}from"./vue-vendor-dy9k-Yad.js";import{B as f}from"./index-Diw57m_E.js";import{a as _}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const u=r({components:{BasicModal:f},setup(){return{}}});function B(o,M,h,$,g,k){const a=s("BasicModal");return t(),p(a,i(o.$attrs,{title:"Modal Title",helpMessage:["提示1","提示2"],width:"700px"}),{default:m(()=>[(t(),n(c,null,l(20,e=>d("p",{class:"h-20",key:e}," 根据屏幕高度自适应 ")),64))]),_:1},16)}const j=_(u,[["render",B]]);export{j as default};
