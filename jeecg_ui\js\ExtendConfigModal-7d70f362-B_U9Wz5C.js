import{d as v,n as b,ag as h,aB as j,ar as x,aD as L,k as P}from"./vue-vendor-dy9k-Yad.js";import{B as R}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{cs as V,u as I,ac as O}from"./index-CCWaWN5g.js";import{R as Q}from"./useSchemas-b074f3a1-CF0HohIK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./validator-B_KkcUnu.js";import{u as U}from"./useForm-CgkFTrrO.js";import{B as _}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./user.api-mLAlJze4.js";var u=(i,l,r)=>new Promise((s,a)=>{var c=o=>{try{n(r.next(o))}catch(p){a(p)}},e=o=>{try{n(r.throw(o))}catch(p){a(p)}},n=o=>o.done?s(o.value):Promise.resolve(o.value).then(c,e);n((r=r.apply(i,l)).next())});const D=v({name:"CgformExtConfigModel",components:{BasicModal:R,BasicForm:_},props:{parentForm:{type:Object,required:!0}},emits:["register","ok"],setup(i,{emit:l}){const{createMessage:r}=I(),{formSchemas:s}=Q(i,{onIsDesformChange:C,onJoinQueryChange:k,onReportPrintShowChange:B,onFormLabelLengthShow:T}),[a,{resetFields:c,setFieldsValue:e,getFieldsValue:n,clearValidate:o,validate:p}]=U({schemas:s,showActionButtonGroup:!1,labelAlign:"right"}),[y,{closeModal:d}]=O(t=>u(this,null,function*(){yield c(),yield e(t.extConfigJson)}));function F(){return u(this,null,function*(){yield o(),yield b();try{const t=yield p();l("ok",t),d()}catch(t){}})}function w(){d()}function C(t){if(t==="Y"){let{themeTemplate:m}=i.parentForm.getFieldsValue(["themeTemplate"]);m==="erp"&&(i.parentForm.setFieldsValue({themeTemplate:"normal"}),r.warning("请注意：erp风格不支持对接表单设计，已自动改为默认风格！"))}else o("desFormCode")}const f="{{ window._CONFIG['domianURL'] }}/jmreport/view/{积木报表ID}";function B(t){return u(this,null,function*(){let m=n().reportPrintUrl;t===0?m===f&&(yield e({reportPrintUrl:""})):t===1&&m===""&&(yield e({reportPrintUrl:f})),o("reportPrintUrl")})}function T(t){return u(this,null,function*(){t==0&&(yield e({formLabelLength:null})),yield o("formLabelLength")})}function k(t){if(t===1){let{themeTemplate:m,isTree:M,tableType:g}=i.parentForm.getFieldsValue(["themeTemplate","isTree","tableType"]);m==="erp"&&(r.warning("请注意：erp风格不支持联合查询，配置无效!"),e({joinQuery:0})),m==="innerTable"&&(r.warning("请注意：内嵌风格不支持联合查询，配置无效!"),e({joinQuery:0})),g===1?(r.warning("请注意：单表不支持联合查询，配置无效!"),e({joinQuery:0})):g===3?(r.warning("请注意：当前表为附表，请在对应主表配置!"),e({joinQuery:0})):M==="Y"&&(r.warning("请注意：树形列表不支持联合查询，配置无效!"),e({joinQuery:0}))}}return{handleOk:F,handleCancel:w,registerModal:y,registerForm:a}}});function S(i,l,r,s,a,c){const e=h("BasicForm"),n=h("BasicModal");return x(),j(n,{onRegister:i.registerModal,title:"表单扩展配置项",width:1e3,onOk:i.handleOk,onCancel:i.handleCancel},{default:L(()=>[P(e,{onRegister:i.registerForm},null,8,["onRegister"])]),_:1},8,["onRegister","onOk","onCancel"])}const Ne=V(D,[["render",S],["__scopeId","data-v-f3d939e5"]]);export{Ne as default};
