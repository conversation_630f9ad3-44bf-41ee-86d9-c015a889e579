import{B as A}from"./index-Diw57m_E.js";import{f as b,ag as h,aB as I,ar as R,aE as q,aD as T,k as F}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{cs as L,u as N,ac as _,j as E}from"./index-CCWaWN5g.js";import{o as H}from"./antd-vue-vendor-me9YkNVC.js";import{u as J}from"./useForm-CgkFTrrO.js";import{B as K}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";var D=Object.defineProperty,x=Object.getOwnPropertySymbols,G=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable,B=(o,i,t)=>i in o?D(o,i,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[i]=t,U=(o,i)=>{for(var t in i||(i={}))G.call(i,t)&&B(o,t,i[t]);if(x)for(var t of x(i))Q.call(i,t)&&B(o,t,i[t]);return o},c=(o,i,t)=>new Promise((l,s)=>{var p=r=>{try{m(t.next(r))}catch(d){s(d)}},n=r=>{try{m(t.throw(r))}catch(d){s(d)}},m=r=>r.done?l(r.value):Promise.resolve(r.value).then(p,n);m((t=t.apply(o,i)).next())});const $={name:"LinkTableFieldConfigModal",emits:["success","register"],components:{BasicModal:A,BasicForm:K},setup(o,{emit:i}){const t=b(!1),l=b([]),s=b([]);let p={},n={};const{createMessage:m}=N(),[r,{closeModal:d}]=_(e=>c(this,null,function*(){n=U({},e.record),p=e.tableAndFieldsMap,yield w(),yield v({dictTable:e.record.dictTable}),n.dictTable&&y(n.dictTable),setTimeout(()=>c(this,null,function*(){let a=H(e.record,"dictTable");yield v(a),yield C()}),200)}));function w(){return c(this,null,function*(){let e=Object.keys(p);if(!e||e.length==0)l.value=[];else{let a=[];for(let u of e)a.push({text:p[u].title,value:u});l.value=a}})}function y(e){return c(this,null,function*(){if(e){const{table:a,fields:u}=p[e];if(!a){m.warning("请先完善字段["+e+"]关联记录的配置");return}const S="/online/cgform/field/listByHeadCode",g=yield E.get({url:S,params:{headCode:a}});if(g&&g.length>0){let j=g.map(f=>({text:f.dbFieldTxt,value:f.dbFieldName})),V=u.split(",");s.value=j.filter(f=>V.includes(f.value))}else s.value=[]}})}const k=[{label:"rowKey",field:"rowKey",component:"Input",show:!1},{label:"字段描述",field:"dbFieldTxt",component:"Input",required:!0},{label:"关联记录",field:"dictTable",component:"JSearchSelect",required:!0,componentProps:({formActionType:e})=>({async:!1,popContainer:".link-table-field-config-modal",dictOptions:l.value,immediateChange:!0,onChange:a=>c(this,null,function*(){n.dictText&&(yield e.setFieldsValue({dictText:""}),yield e.clearValidate()),y(a)})})},{label:"显示字段",field:"dictText",component:"JSearchSelect",required:!0,componentProps:{async:!1,popContainer:".link-table-field-config-modal",dictOptions:s,onChange:e=>{n.dictText=e}}}],[M,{validate:O,setFieldsValue:v,clearValidate:C}]=J({schemas:k,showActionButtonGroup:!1,labelAlign:"right"});function P(){return c(this,null,function*(){const e=yield O();i("success",e),d()})}return{registerModal:r,spinningLoading:t,registerForm:M,handleSubmit:P}}};function z(o,i,t,l,s,p){const n=h("BasicForm"),m=h("a-spin"),r=h("BasicModal");return R(),I(r,q({wrapClassName:"link-table-field-config-modal"},o.$attrs,{title:"他表字段配置",onRegister:l.registerModal,keyboard:"",canFullscreen:!1,cancelText:"关闭",onOk:l.handleSubmit}),{default:T(()=>[F(m,{spinning:l.spinningLoading},{default:T(()=>[F(n,{onRegister:l.registerForm},null,8,["onRegister"])]),_:1},8,["spinning"])]),_:1},16,["onRegister","onOk"])}const We=L($,[["render",z]]);export{We as default};
