import{d as k,f as A,r as M,h as L,aq as C,ar as j,aA as B,ag as v,k as a,aD as l,u as m}from"./vue-vendor-dy9k-Yad.js";import{B as $}from"./Bar-Tb_Evzfu.js";import{B as E,G as K}from"./Gauge-DelrgGV_.js";import{S,T as F}from"./SingleLine-kPL5Z-yi.js";import{L as P}from"./LineMulti-Bb6oGGI0.js";import{P as Y}from"./Pie-BKQXlPsX.js";import{useECharts as R}from"./useECharts-BU6FzBZi.js";import{h as O}from"./antd-vue-vendor-me9YkNVC.js";import{a as T}from"./index-CCWaWN5g.js";import{R as G}from"./RankList-gPMCy9y-.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";const N=["#4db6ac","#ffb74d","#64b5f6","#e57373","#9575cd","#a1887f","#90a4ae","#4dd0e1","#81c784","#ff8a65"],q=(()=>{let t=+new Date;const c=[],s=[],h=[],u=[];for(let r=0;r<20;r++){let n={name:"",value:0};const e=new Date(t+=1e3*3600*24);n.name=[e.getFullYear(),e.getMonth()+1,e.getDate()].join("-"),n.value=Math.random()*200,c.push(n)}for(let r=0;r<2;r++)for(let n=0;n<20;n++){let e={name:"",value:0,type:2010+r+""};const o=new Date(t+1e3*3600*24*n);e.name=[o.getFullYear(),o.getMonth()+1,o.getDate()].join("-"),e.value=Math.random()*200,s.push(e)}const f=[{value:335,name:"客服电话"},{value:310,name:"奥迪官网"},{value:234,name:"媒体曝光"},{value:135,name:"质检总局"},{value:105,name:"其他"}],y=[{value:75,name:"政治",type:"文综",max:100},{value:65,name:"历史",type:"文综",max:100},{value:55,name:"地理",type:"文综",max:100},{value:74,name:"化学",type:"文综",max:100},{value:38,name:"物理",type:"文综",max:100},{value:88,name:"生物",type:"文综",max:100}];for(let r=0;r<2;r++){for(let n=0;n<15;n++){let e={name:"",value:0,type:2010+r+"",seriesType:r>=1?"line":"bar"};const o=new Date(t+1e3*3600*24*n);e.name=[o.getFullYear(),o.getMonth()+1,o.getDate()].join("-"),e.value=Math.random()*200,h.push(e)}u.push(N[r])}return{barDataSource:c,barMultiData:s,pieData:f,barLineData:h,barLineColors:u,radarData:y}})(),z=k({name:"Radar",props:{chartData:{type:Array,default:()=>[]},option:{type:Object,default:()=>({})},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"}},setup(t){const c=A(null),{setOptions:s}=R(c),h=M({title:{text:"基础雷达图"},legend:{data:["文综"]},radar:{indicator:[{name:"历史"},{name:"地理"},{name:"生物"},{name:"化学"},{name:"物理"},{name:"政治"}]},series:[{type:"radar",data:[{value:[82,70,60,55,90,66],name:"文综"}]}]});L(()=>{t.chartData&&u()});function u(){t.option&&Object.assign(h,O(t.option));let f=Array.from(new Set(t.chartData.map(n=>n.type))),y=Array.from(new Set(t.chartData.map(n=>{let{name:e,max:o}=n;return{name:e,max:o}}))),r=[];f.forEach(n=>{let e={name:n},o=t.chartData.filter(d=>n===d.type);e.value=o.map(d=>d.value),r.push(e)}),h.radar.axisName=y,h.series[0].data=r,s(h)}return{chartRef:c}}});function U(t,c,s,h,u,f){return j(),C("div",{ref:"chartRef",style:B({height:t.height,width:t.width})},null,4)}const V=T(z,[["render",U]]),H=k({name:"barAndLine",props:{chartData:{type:Array,default:()=>[]},option:{type:Object,default:()=>({})},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"},customColor:{type:Array,default:()=>[]}},setup(t){const c=A(null),{setOptions:s,echarts:h}=R(c),u=M({tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0,backgroundColor:"#333"}}},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"bar",type:"bar",data:[]}]});L(()=>{t.chartData&&f()});function f(){t.option&&Object.assign(u,O(t.option));let y=Array.from(new Set(t.chartData.map(e=>e.type))),r=Array.from(new Set(t.chartData.map(e=>e.name))),n=[];y.forEach((e,o)=>{let d={name:e},D=t.chartData.filter(p=>e===p.type);d.data=D.map(p=>p.value),d.type=D[0].seriesType,t!=null&&t.customColor&&(t!=null&&t.customColor[o])&&(d.color=t.customColor[o]),n.push(d)}),u.series=n,u.xAxis.data=r,s(u)}return{chartRef:c}}});function I(t,c,s,h,u,f){return j(),C("div",{ref:"chartRef",style:B({height:t.height,width:t.width})},null,4)}const J=T(H,[["render",I]]),Q={class:"p-4"},ut=k({__name:"index",setup(t){const c=A("1"),{barDataSource:s,barMultiData:h,pieData:u,barLineData:f,radarData:y,barLineColors:r}=q,n={title:{text:"多列柱状图",left:"center"}},e=d("name","total",2e3,100,"北京朝阳 "," 号店");function o(D){}function d(D,p,b,x,i="",w="月"){let g=[];for(let _=0;_<12;_+=1)g.push({[D]:`${i}${_+1}${w}`,[p]:Math.floor(Math.random()*b)+x});return g}return(D,p)=>{const b=v("a-col"),x=v("a-row"),i=v("a-tab-pane"),w=v("a-tabs"),g=v("a-card");return j(),C("div",Q,[a(g,{bordered:!1,style:{height:"100%"}},{default:l(()=>[a(w,{activeKey:c.value,"onUpdate:activeKey":p[0]||(p[0]=_=>c.value=_),animated:"",onChange:o},{default:l(()=>[a(i,{key:"1",tab:"柱状图"},{default:l(()=>[a(x,null,{default:l(()=>[a(b,{span:24},{default:l(()=>[a($,{chartData:m(s),height:"50vh",option:{title:{text:"销售额排行",left:"center"}}},null,8,["chartData"])]),_:1})]),_:1})]),_:1}),a(i,{key:"2",tab:"多列柱状图","force-render":""},{default:l(()=>[a(E,{chartData:m(h),option:n,height:"50vh"},null,8,["chartData"])]),_:1}),a(i,{key:"3",tab:"迷你柱状图",style:{display:"flex","justify-content":"center"}},{default:l(()=>[a($,{chartData:m(s),width:"30%",height:"50vh"},null,8,["chartData"])]),_:1}),a(i,{key:"4",tab:"面积图"},{default:l(()=>[a(S,{chartData:m(s),height:"50vh",option:{title:{text:"销售额排行",left:"center"}}},null,8,["chartData"])]),_:1}),a(i,{key:"5",tab:"迷你面积图",style:{display:"flex","justify-content":"center"}},{default:l(()=>[a(S,{chartData:m(s),width:"30%",height:"50vh"},null,8,["chartData"])]),_:1}),a(i,{key:"6",tab:"多行折线图"},{default:l(()=>[a(P,{chartData:m(h),height:"50vh",option:n,type:"line"},null,8,["chartData"])]),_:1}),a(i,{key:"7",tab:"饼图"},{default:l(()=>[a(Y,{chartData:m(u),height:"40vh",option:{title:{text:"基础饼状图",left:"center"}}},null,8,["chartData"])]),_:1}),a(i,{key:"8",tab:"雷达图"},{default:l(()=>[a(V,{chartData:m(y),height:"50vh"},null,8,["chartData"])]),_:1}),a(i,{key:"9",tab:"仪表盘"},{default:l(()=>[a(K,{chartData:{name:"出勤率",value:70},height:"50vh"})]),_:1}),a(i,{key:"10",tab:"折柱图"},{default:l(()=>[a(J,{chartData:m(f),customColor:m(r),height:"50vh"},null,8,["chartData","customColor"])]),_:1}),a(i,{key:"11",tab:"排名列表"},{default:l(()=>[a(G,{title:"门店销售排行榜",list:m(e),style:{width:"600px",margin:"0 auto"}},null,8,["list"])]),_:1}),a(i,{key:"13",tab:"趋势"},{default:l(()=>[a(F,{title:"Trend",term:"Trend：",percentage:30})]),_:1})]),_:1},8,["activeKey"])]),_:1})])}}});export{ut as default};
