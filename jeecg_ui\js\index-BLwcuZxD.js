var ne=Object.defineProperty,ae=Object.defineProperties;var le=Object.getOwnPropertyDescriptors;var G=Object.getOwnPropertySymbols;var he=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable;var J=(i,e,t)=>e in i?ne(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,M=(i,e)=>{for(var t in e||(e={}))he.call(e,t)&&J(i,t,e[t]);if(G)for(var t of G(e))ce.call(e,t)&&J(i,t,e[t]);return i},z=(i,e)=>ae(i,le(e));import{aq as c,ar as h,F as j,aC as k,aA as A,as as v,at as m,aB as N,ah as p,aQ as me,aD as ue,au as g,ag as w,k as C,q as Q,aO as x,aR as de,aG as q,B as fe}from"./vue-vendor-dy9k-Yad.js";import{a as _}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var U="emoji-mart";const je=JSON;var Y=typeof window!="undefined"&&"localStorage"in window;let B,R;function ge(i){i||(i={}),B=i.getter,R=i.setter}function ve(i){U=i}function pe(i){for(let e in i){let t=i[e];$(e,t)}}function $(i,e){if(R)R(i,e);else{if(!Y)return;try{window.localStorage[`${U}.${i}`]=je.stringify(e)}catch(t){}}}function we(i){if(B)return B(i);if(!Y)return;try{var e=window.localStorage[`${U}.${i}`]}catch(t){return}if(e)return JSON.parse(e)}const E={update:pe,set:$,get:we,setNamespace:ve,setHandlers:ge},I={name:"a",unified:"b",non_qualified:"c",has_img_apple:"d",has_img_google:"e",has_img_twitter:"f",has_img_facebook:"h",keywords:"j",sheet:"k",emoticons:"l",text:"m",short_names:"n",added_in:"o"},ee=i=>{const e=[];var t=(r,o)=>{r&&(Array.isArray(r)?r:[r]).forEach(s=>{(o?s.split(/[-|_|\s]+/):[s]).forEach(n=>{n=n.toLowerCase(),e.indexOf(n)==-1&&e.push(n)})})};return t(i.short_names,!0),t(i.name,!0),t(i.keywords,!1),t(i.emoticons,!1),e.join(",")};function te(i){var e=Object.getOwnPropertyNames(i);for(let t of e){let r=i[t];i[t]=r&&typeof r=="object"?te(r):r}return Object.freeze(i)}const _e=i=>{if(!i.compressed)return i;i.compressed=!1;for(let e in i.emojis){let t=i.emojis[e];for(let r in I)t[r]=t[I[r]],delete t[I[r]];t.short_names||(t.short_names=[]),t.short_names.unshift(e),t.sheet_x=t.sheet[0],t.sheet_y=t.sheet[1],delete t.sheet,t.text||(t.text=""),t.added_in||(t.added_in=6),t.added_in=t.added_in.toFixed(1),t.search=ee(t)}return i=te(i),i},T=["+1","grinning","kissing_heart","heart_eyes","laughing","stuck_out_tongue_winking_eye","sweat_smile","joy","scream","disappointed","unamused","weary","sob","sunglasses","heart","hankey"];let d,V,H={};function ie(){V=!0,d=E.get("frequently")}function ye(i){V||ie();var{id:e}=i;d||(d=H),d[e]||(d[e]=0),d[e]+=1,E.set("last",e),E.set("frequently",d)}function Ce(i){if(V||ie(),!d){H={};const n=[];let l=Math.min(i,T.length);for(let a=0;a<l;a++)H[T[a]]=parseInt((l-a)/4,10)+1,n.push(T[a]);return n}const e=i,t=[];for(let n in d)d.hasOwnProperty(n)&&t.push(n);const o=t.sort((n,l)=>d[n]-d[l]).reverse().slice(0,e),s=E.get("last");return s&&o.indexOf(s)==-1&&(o.pop(),o.push(s)),o}const D={add:ye,get:Ce},ke={activity:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113"/></svg>',custom:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><g transform="translate(2.000000, 1.000000)"><rect id="Rectangle" x="8" y="0" width="3" height="21" rx="1.5"></rect><rect id="Rectangle" transform="translate(9.843, 10.549) rotate(60) translate(-9.843, -10.549) " x="8.343" y="0.049" width="3" height="21" rx="1.5"></rect><rect id="Rectangle" transform="translate(9.843, 10.549) rotate(-60) translate(-9.843, -10.549) " x="8.343" y="0.049" width="3" height="21" rx="1.5"></rect></g></svg>',flags:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z"/></svg>',foods:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9"/></svg>',nature:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8"/><path d="M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235"/></svg>',objects:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z"/><path d="M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789"/></svg>',smileys:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"/><path d="M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0"/></svg>',people:'<svg xmlns:svg="http://www.w3.org/2000/svg" height="24" width="24" viewBox="0 0 24 24"> <path id="path3814" d="m 3.3591089,21.17726 c 0.172036,0.09385 4.265994,2.29837 8.8144451,2.29837 4.927767,0 8.670894,-2.211883 8.82782,-2.306019 0.113079,-0.06785 0.182268,-0.190051 0.182267,-0.321923 0,-3.03119 -0.929494,-5.804936 -2.617196,-7.810712 -1.180603,-1.403134 -2.661918,-2.359516 -4.295699,-2.799791 4.699118,-2.236258 3.102306,-9.28617162 -2.097191,-9.28617162 -5.1994978,0 -6.7963103,7.04991362 -2.097192,9.28617162 -1.6337821,0.440275 -3.1150971,1.396798 -4.2956991,2.799791 -1.687703,2.005776 -2.617196,4.779522 -2.617196,7.810712 1.2e-6,0.137378 0.075039,0.263785 0.195641,0.329572 z M 8.0439319,5.8308783 C 8.0439309,2.151521 12.492107,0.30955811 15.093491,2.9109411 17.694874,5.5123241 15.852911,9.9605006 12.173554,9.9605 9.8938991,9.9579135 8.0465186,8.1105332 8.0439319,5.8308783 Z m -1.688782,7.6894977 c 1.524535,-1.811449 3.5906601,-2.809035 5.8184041,-2.809035 2.227744,0 4.293869,0.997586 5.818404,2.809035 1.533639,1.822571 2.395932,4.339858 2.439152,7.108301 -0.803352,0.434877 -4.141636,2.096112 -8.257556,2.096112 -3.8062921,0 -7.3910861,-1.671043 -8.2573681,-2.104981 0.04505,-2.765017 0.906968,-5.278785 2.438964,-7.099432 z" /> <path id="path3816" d="M 12.173828 0.38867188 C 9.3198513 0.38867187 7.3770988 2.3672285 6.8652344 4.6308594 C 6.4218608 6.5916015 7.1153562 8.7676117 8.9648438 10.126953 C 7.6141249 10.677376 6.3550511 11.480944 5.3496094 12.675781 C 3.5629317 14.799185 2.6015625 17.701475 2.6015625 20.847656 C 2.6015654 21.189861 2.7894276 21.508002 3.0898438 21.671875 C 3.3044068 21.788925 7.4436239 24.039062 12.173828 24.039062 C 17.269918 24.039062 21.083568 21.776786 21.291016 21.652344 C 21.57281 21.483266 21.746097 21.176282 21.746094 20.847656 C 21.746094 17.701475 20.78277 14.799185 18.996094 12.675781 C 17.990455 11.480591 16.733818 10.675362 15.382812 10.125 C 17.231132 8.7655552 17.925675 6.5910701 17.482422 4.6308594 C 16.970557 2.3672285 15.027805 0.38867188 12.173828 0.38867188 z M 12.792969 2.3007812 C 13.466253 2.4161792 14.125113 2.7383941 14.695312 3.3085938 C 15.835712 4.4489931 15.985604 5.9473549 15.46875 7.1953125 C 14.951896 8.4432701 13.786828 9.3984378 12.173828 9.3984375 C 10.197719 9.3961954 8.607711 7.806187 8.6054688 5.8300781 C 8.6054683 4.2170785 9.5606362 3.0520102 10.808594 2.5351562 C 11.432573 2.2767293 12.119685 2.1853833 12.792969 2.3007812 z M 12.173828 11.273438 C 14.233647 11.273438 16.133674 12.185084 17.5625 13.882812 C 18.93069 15.508765 19.698347 17.776969 19.808594 20.283203 C 18.807395 20.800235 15.886157 22.162109 12.173828 22.162109 C 8.7614632 22.162109 5.6245754 20.787069 4.5390625 20.265625 C 4.6525896 17.766717 5.4203315 15.504791 6.7851562 13.882812 C 8.2139827 12.185084 10.11401 11.273438 12.173828 11.273438 z " /> </svg>',places:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5"/><path d="M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z"/></svg>',recent:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z"/><path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"/></svg>',symbols:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76"/></svg>'},Ee={props:{i18n:{type:Object,required:!0},color:{type:String},categories:{type:Array,required:!0},activeCategory:{type:Object,default(){return{}}}},emits:["click"],created(){this.svgs=ke}},Se={role:"tablist",class:"emoji-mart-anchors"},be=["aria-label","aria-selected","data-title","onClick"],xe=["innerHTML"];function Ae(i,e,t,r,o,s){return h(),c("div",Se,[(h(!0),c(j,null,k(t.categories,n=>(h(),c("button",{role:"tab",type:"button","aria-label":n.name,"aria-selected":n.id==t.activeCategory.id,key:n.id,class:v({"emoji-mart-anchor":!0,"emoji-mart-anchor-selected":n.id==t.activeCategory.id}),style:A({color:n.id==t.activeCategory.id?t.color:""}),"data-title":t.i18n.categories[n.id],onClick:l=>i.$emit("click",n)},[m("div",{"aria-hidden":"true",innerHTML:i.svgs[n.id]},null,8,xe),m("span",{"aria-hidden":"true",class:"emoji-mart-anchor-bar",style:A({backgroundColor:t.color})},null,4)],14,be))),128))])}const Oe=_(Ee,[["render",Ae]]),Me=String,Pe=Me.fromCodePoint||function(){var e=16384,t=[],r,o,s=-1,n=arguments.length;if(!n)return"";for(var l="";++s<n;){var a=Number(arguments[s]);if(!isFinite(a)||a<0||a>1114111||Math.floor(a)!=a)throw RangeError("Invalid code point: "+a);a<=65535?t.push(a):(a-=65536,r=(a>>10)+55296,o=a%1024+56320,t.push(r,o)),(s+1===n||t.length>e)&&(l+=String.fromCharCode.apply(null,t),t.length=0)}return l};function Fe(i){var e=i.split("-"),t=e.map(r=>`0x${r}`);return Pe.apply(null,t)}function X(i){return i.reduce((e,t)=>(e.indexOf(t)===-1&&e.push(t),e),[])}function Le(i,e){const t=X(i),r=X(e);return t.filter(o=>r.indexOf(o)>=0)}function se(i,e){var t={};for(let r in i){let o=i[r],s=o;Object.prototype.hasOwnProperty.call(e,r)&&(s=e[r]),typeof s=="object"&&(s=se(o,s)),t[r]=s}return t}function ze(){if(typeof document=="undefined")return 0;const i=document.createElement("div");i.style.width="100px",i.style.height="100px",i.style.overflow="scroll",i.style.position="absolute",i.style.top="-9999px",document.body.appendChild(i);const e=i.offsetWidth-i.clientWidth;return document.body.removeChild(i),e}const qe=61,Ie=/^(?:\:([^\:]+)\:)(?:\:skin-tone-(\d)\:)?$/,Z=["1F3FA","1F3FB","1F3FC","1F3FD","1F3FE","1F3FF"];class Ft{constructor(e,{emojisToShowFilter:t,include:r,exclude:o,custom:s,recent:n,recentLength:l=20}={}){this._data=_e(e),this._emojisFilter=t||null,this._include=r||null,this._exclude=o||null,this._custom=s||[],this._recent=n||D.get(l),this._emojis={},this._nativeEmojis={},this._emoticons={},this._categories=[],this._recentCategory={id:"recent",name:"Recent",emojis:[]},this._customCategory={id:"custom",name:"Custom",emojis:[]},this._searchIndex={},this.buildIndex(),Object.freeze(this)}buildIndex(){let e=this._data.categories;if(this._include&&(e=e.filter(t=>this._include.includes(t.id)),e=e.sort((t,r)=>{const o=this._include.indexOf(t.id),s=this._include.indexOf(r.id);return o<s?-1:o>s?1:0})),e.forEach(t=>{if(!this.isCategoryNeeded(t.id))return;let r={id:t.id,name:t.name,emojis:[]};t.emojis.forEach(o=>{let s=this.addEmoji(o);s&&r.emojis.push(s)}),r.emojis.length&&this._categories.push(r)}),this.isCategoryNeeded("custom")){if(this._custom.length>0)for(let t of this._custom)this.addCustomEmoji(t);this._customCategory.emojis.length&&this._categories.push(this._customCategory)}this.isCategoryNeeded("recent")&&(this._recent.length&&this._recent.map(t=>{for(let r of this._customCategory.emojis)if(r.id===t){this._recentCategory.emojis.push(r);return}this.hasEmoji(t)&&this._recentCategory.emojis.push(this.emoji(t))}),this._recentCategory.emojis.length&&this._categories.unshift(this._recentCategory))}findEmoji(e,t){let r=e.match(Ie);if(r&&(e=r[1],r[2]&&(t=parseInt(r[2],10))),this._data.aliases.hasOwnProperty(e)&&(e=this._data.aliases[e]),this._emojis.hasOwnProperty(e)){let o=this._emojis[e];return t?o.getSkin(t):o}return this._nativeEmojis.hasOwnProperty(e)?this._nativeEmojis[e]:null}categories(){return this._categories}emoji(e){this._data.aliases.hasOwnProperty(e)&&(e=this._data.aliases[e]);let t=this._emojis[e];if(!t)throw new Error("Can not find emoji by id: "+e);return t}firstEmoji(){let e=this._emojis[Object.keys(this._emojis)[0]];if(!e)throw new Error("Can not get first emoji");return e}hasEmoji(e){return this._data.aliases.hasOwnProperty(e)&&(e=this._data.aliases[e]),!!this._emojis[e]}nativeEmoji(e){return this._nativeEmojis.hasOwnProperty(e)?this._nativeEmojis[e]:null}search(e,t){if(t||(t=75),!e.length)return null;if(e=="-"||e=="-1")return[this.emoji("-1")];let r=e.toLowerCase().split(/[\s|,|\-|_]+/),o=[];r.length>2&&(r=[r[0],r[1]]),o=r.map(n=>{let l=this._emojis,a=this._searchIndex,f=0;for(let u=0;u<n.length;u++){const S=n[u];if(f++,a[S]||(a[S]={}),a=a[S],!a.results){let F={};a.results=[],a.emojis={};for(let y in l){let b=l[y],L=b._data.search,O=n.substr(0,f),W=L.indexOf(O);if(W!=-1){let K=W+1;O==y&&(K=0),a.results.push(b),a.emojis[y]=b,F[y]=K}}a.results.sort((y,b)=>{var L=F[y.id],O=F[b.id];return L-O})}l=a.emojis}return a.results}).filter(n=>n);var s=null;return o.length>1?s=Le.apply(null,o):o.length?s=o[0]:s=[],s&&s.length>t&&(s=s.slice(0,t)),s}addCustomEmoji(e){let t=Object.assign({},e,{id:e.short_names[0],custom:!0});t.search||(t.search=ee(t));let r=new P(t);return this._emojis[r.id]=r,this._customCategory.emojis.push(r),r}addEmoji(e){let t=this._data.emojis[e];if(!this.isEmojiNeeded(t))return!1;let r=new P(t);if(this._emojis[e]=r,r.native&&(this._nativeEmojis[r.native]=r),r._skins)for(let o in r._skins){let s=r._skins[o];s.native&&(this._nativeEmojis[s.native]=s)}return r.emoticons&&r.emoticons.forEach(o=>{this._emoticons[o]||(this._emoticons[o]=e)}),r}isCategoryNeeded(e){let t=this._include&&this._include.length?this._include.indexOf(e)>-1:!0,r=this._exclude&&this._exclude.length?this._exclude.indexOf(e)>-1:!1;return!(!t||r)}isEmojiNeeded(e){return this._emojisFilter?this._emojisFilter(e):!0}}class P{constructor(e){if(this._data=Object.assign({},e),this._skins=null,this._data.skin_variations){this._skins=[];for(var t in Z){let r=Z[t],o=this._data.skin_variations[r],s=Object.assign({},e);for(let n in o)s[n]=o[n];delete s.skin_variations,s.skin_tone=parseInt(t)+1,this._skins.push(new P(s))}}this._sanitized=Te(this._data);for(let r in this._sanitized)this[r]=this._sanitized[r];this.short_names=this._data.short_names,this.short_name=this._data.short_names[0],Object.freeze(this)}getSkin(e){return e&&e!="native"&&this._skins?this._skins[e-1]:this}getPosition(){let e=qe-1,t=+(100/e*this._data.sheet_x).toFixed(2),r=+(100/e*this._data.sheet_y).toFixed(2);return`${t}% ${r}%`}ariaLabel(){return[this.native].concat(this.short_names).filter(Boolean).join(", ")}}class re{constructor(e,t,r,o,s,n,l){this._emoji=e,this._native=o,this._skin=t,this._set=r,this._fallback=s,this.canRender=this._canRender(),this.cssClass=this._cssClass(),this.cssStyle=this._cssStyle(l),this.content=this._content(),this.title=n===!0?e.short_name:null,this.ariaLabel=e.ariaLabel(),Object.freeze(this)}getEmoji(){return this._emoji.getSkin(this._skin)}_canRender(){return this._isCustom()||this._isNative()||this._hasEmoji()||this._fallback}_cssClass(){return["emoji-set-"+this._set,"emoji-type-"+this._emojiType()]}_cssStyle(e){let t={};return this._isCustom()?t={backgroundImage:"url("+this.getEmoji()._data.imageUrl+")",backgroundSize:"100%",width:e+"px",height:e+"px"}:this._hasEmoji()&&!this._isNative()&&(t={backgroundPosition:this.getEmoji().getPosition()}),e&&(this._isNative()?t=Object.assign(t,{fontSize:Math.round(e*.95*10)/10+"px"}):t=Object.assign(t,{width:e+"px",height:e+"px"})),t}_content(){return this._isCustom()?"":this._isNative()?this.getEmoji().native:this._hasEmoji()?"":this._fallback?this._fallback(this.getEmoji()):null}_isNative(){return this._native}_isCustom(){return this.getEmoji().custom}_hasEmoji(){if(!this.getEmoji()._data)return!1;const e=this.getEmoji()._data["has_img_"+this._set];return e===void 0?!0:e}_emojiType(){return this._isCustom()?"custom":this._isNative()?"native":this._hasEmoji()?"image":"fallback"}}function Te(i){var{name:e,short_names:t,skin_tone:r,skin_variations:o,emoticons:s,unified:n,custom:l,imageUrl:a}=i,f=i.id||t[0],u=`:${f}:`;return l?{id:f,name:e,colons:u,emoticons:s,custom:l,imageUrl:a}:(r&&(u+=`:skin-tone-${r}:`),{id:f,name:e,colons:u,emoticons:s,unified:n.toLowerCase(),skin:r||(o?1:null),native:Fe(n)})}const Ne={native:{type:Boolean,default:!1},tooltip:{type:Boolean,default:!1},fallback:{type:Function},skin:{type:Number,default:1},set:{type:String,default:"apple"},emoji:{type:[String,Object],required:!0},size:{type:Number,default:null},tag:{type:String,default:"span"}},Be={perLine:{type:Number,default:9},maxSearchResults:{type:Number,default:75},emojiSize:{type:Number,default:24},title:{type:String,default:"Emoji Mart™"},emoji:{type:String,default:"department_store"},color:{type:String,default:"#ae65c5"},set:{type:String,default:"apple"},skin:{type:Number,default:null},defaultSkin:{type:Number,default:1},native:{type:Boolean,default:!1},emojiTooltip:{type:Boolean,default:!1},autoFocus:{type:Boolean,default:!1},i18n:{type:Object,default(){return{}}},showPreview:{type:Boolean,default:!0},showSearch:{type:Boolean,default:!0},showCategories:{type:Boolean,default:!0},showSkinTones:{type:Boolean,default:!0},infiniteScroll:{type:Boolean,default:!0},pickerStyles:{type:Object,default(){return{}}}},Re={props:z(M({},Ne),{data:{type:Object,required:!0}}),emits:["click","mouseenter","mouseleave"],computed:{view(){return new re(this.emojiObject,this.skin,this.set,this.native,this.fallback,this.tooltip,this.size)},sanitizedData(){return this.emojiObject._sanitized},title(){return this.tooltip?this.emojiObject.short_name:null},emojiObject(){return typeof this.emoji=="string"?this.data.findEmoji(this.emoji):this.emoji}},created(){},methods:{onClick(){this.$emit("click",this.emojiObject)},onMouseEnter(){this.$emit("mouseenter",this.emojiObject)},onMouseLeave(){this.$emit("mouseleave",this.emojiObject)}}};function He(i,e,t,r,o,s){return s.view.canRender?(h(),N(me(i.tag),{key:0,title:s.view.title,"aria-label":s.view.ariaLabel,"data-title":s.title,class:"emoji-mart-emoji",onMouseenter:s.onMouseEnter,onMouseleave:s.onMouseLeave,onClick:s.onClick},{default:ue(()=>[m("span",{class:v(s.view.cssClass),style:A(s.view.cssStyle)},g(s.view.content),7)]),_:1},40,["title","aria-label","data-title","onMouseenter","onMouseleave","onClick"])):p("",!0)}const oe=_(Re,[["render",He]]),De={props:{data:{type:Object,required:!0},i18n:{type:Object,required:!0},id:{type:String,required:!0},name:{type:String,required:!0},emojis:{type:Array},emojiProps:{type:Object,required:!0}},methods:{activeClass:function(i){return!this.emojiProps.selectedEmoji||!this.emojiProps.selectedEmojiCategory?"":this.emojiProps.selectedEmoji.id==i.id&&this.emojiProps.selectedEmojiCategory.id==this.id?"emoji-mart-emoji-selected":""}},computed:{isVisible(){return!!this.emojis},isSearch(){return this.name=="Search"},hasResults(){return this.emojis.length>0},emojiObjects(){return this.emojis.map(i=>{let e=i,t=new re(i,this.emojiProps.skin,this.emojiProps.set,this.emojiProps.native,this.emojiProps.fallback,this.emojiProps.emojiTooltip,this.emojiProps.emojiSize);return{emojiObject:e,emojiView:t}})}},components:{Emoji:oe}},Ue=["aria-label"],Ve={class:"emoji-mart-category-label"},We={class:"emoji-mart-category-label"},Ke=["aria-label","data-title","title","onMouseenter","onMouseleave","onClick"],Ge={key:0},Je={class:"emoji-mart-no-results-label"};function Xe(i,e,t,r,o,s){const n=w("emoji");return s.isVisible&&(s.isSearch||s.hasResults)?(h(),c("section",{key:0,class:v({"emoji-mart-category":!0,"emoji-mart-no-results":!s.hasResults}),"aria-label":t.i18n.categories[t.id]},[m("div",Ve,[m("h3",We,g(t.i18n.categories[t.id]),1)]),(h(!0),c(j,null,k(s.emojiObjects,({emojiObject:l,emojiView:a})=>(h(),c(j,null,[a.canRender?(h(),c("button",{"aria-label":a.ariaLabel,role:"option","aria-selected":"false","aria-posinset":"1","aria-setsize":"1812",type:"button","data-title":l.short_name,key:l.id,title:a.title,class:v(["emoji-mart-emoji",s.activeClass(l)]),onMouseenter:f=>t.emojiProps.onEnter(a.getEmoji()),onMouseleave:f=>t.emojiProps.onLeave(a.getEmoji()),onClick:f=>t.emojiProps.onClick(a.getEmoji())},[m("span",{class:v(a.cssClass),style:A(a.cssStyle)},g(a.content),7)],42,Ke)):p("",!0)],64))),256)),s.hasResults?p("",!0):(h(),c("div",Ge,[C(n,{data:t.data,emoji:"sleuth_or_spy",native:t.emojiProps.native,skin:t.emojiProps.skin,set:t.emojiProps.set},null,8,["data","native","skin","set"]),m("div",Je,g(t.i18n.notfound),1)]))],10,Ue)):p("",!0)}const Ze=_(De,[["render",Xe]]),Qe={props:{skin:{type:Number,required:!0}},emits:["change"],data(){return{opened:!1}},methods:{onClick(i){this.opened&&i!=this.skin&&this.$emit("change",i),this.opened=!this.opened}}},Ye=["onClick"];function $e(i,e,t,r,o,s){return h(),c("div",{class:v({"emoji-mart-skin-swatches":!0,"emoji-mart-skin-swatches-opened":o.opened})},[(h(),c(j,null,k(6,n=>m("span",{key:n,class:v({"emoji-mart-skin-swatch":!0,"emoji-mart-skin-swatch-selected":t.skin==n})},[m("span",{class:v("emoji-mart-skin emoji-mart-skin-tone-"+n),onClick:l=>s.onClick(n)},null,10,Ye)],2)),64))],2)}const et=_(Qe,[["render",$e]]),tt={props:{data:{type:Object,required:!0},title:{type:String,required:!0},emoji:{type:[String,Object]},idleEmoji:{type:[String,Object],required:!0},showSkinTones:{type:Boolean,default:!0},emojiProps:{type:Object,required:!0},skinProps:{type:Object,required:!0},onSkinChange:{type:Function,required:!0}},computed:{emojiData(){return this.emoji?this.emoji:{}},emojiShortNames(){return this.emojiData.short_names},emojiEmoticons(){return this.emojiData.emoticons}},components:{Emoji:oe,Skins:et}},it={class:"emoji-mart-preview"},st={class:"emoji-mart-preview-emoji"},rt={class:"emoji-mart-preview-data"},ot={class:"emoji-mart-preview-name"},nt={class:"emoji-mart-preview-shortnames"},at={class:"emoji-mart-preview-emoticons"},lt={class:"emoji-mart-preview-emoji"},ht={class:"emoji-mart-preview-data"},ct={class:"emoji-mart-title-label"},mt={key:0,class:"emoji-mart-preview-skins"};function ut(i,e,t,r,o,s){const n=w("emoji"),l=w("skins");return h(),c("div",it,[t.emoji?(h(),c(j,{key:0},[m("div",st,[C(n,{data:t.data,emoji:t.emoji,native:t.emojiProps.native,skin:t.emojiProps.skin,set:t.emojiProps.set},null,8,["data","emoji","native","skin","set"])]),m("div",rt,[m("div",ot,g(t.emoji.name),1),m("div",nt,[(h(!0),c(j,null,k(s.emojiShortNames,a=>(h(),c("span",{key:a,class:"emoji-mart-preview-shortname"},":"+g(a)+":",1))),128))]),m("div",at,[(h(!0),c(j,null,k(s.emojiEmoticons,a=>(h(),c("span",{key:a,class:"emoji-mart-preview-emoticon"},g(a),1))),128))])])],64)):(h(),c(j,{key:1},[m("div",lt,[C(n,{data:t.data,emoji:t.idleEmoji,native:t.emojiProps.native,skin:t.emojiProps.skin,set:t.emojiProps.set},null,8,["data","emoji","native","skin","set"])]),m("div",ht,[m("span",ct,g(t.title),1)]),t.showSkinTones?(h(),c("div",mt,[C(l,{skin:t.skinProps.skin,onChange:e[0]||(e[0]=a=>t.onSkinChange(a))},null,8,["skin"])])):p("",!0)],64))])}const dt=_(tt,[["render",ut]]),ft={props:{data:{type:Object,required:!0},i18n:{type:Object,required:!0},autoFocus:{type:Boolean,default:!1},onSearch:{type:Function,required:!0},onArrowLeft:{type:Function,required:!1},onArrowRight:{type:Function,required:!1},onArrowDown:{type:Function,required:!1},onArrowUp:{type:Function,required:!1},onEnter:{type:Function,required:!1}},emits:["search","enter","arrowUp","arrowDown","arrowRight","arrowLeft"],data(){return{value:""}},computed:{emojiIndex(){return this.data}},watch:{value(){this.$emit("search",this.value)}},methods:{clear(){this.value=""}},mounted(){let i=this.$el.querySelector("input");this.autoFocus&&i.focus()}},jt={class:"emoji-mart-search"},gt=["placeholder"];function vt(i,e,t,r,o,s){return h(),c("div",jt,[Q(m("input",{type:"text",placeholder:t.i18n.search,role:"textbox","aria-autocomplete":"list","aria-owns":"emoji-mart-list","aria-label":"Search for an emoji","aria-describedby":"emoji-mart-search-description",onKeydown:[e[0]||(e[0]=x(n=>i.$emit("arrowLeft",n),["left"])),e[1]||(e[1]=x(()=>i.$emit("arrowRight"),["right"])),e[2]||(e[2]=x(()=>i.$emit("arrowDown"),["down"])),e[3]||(e[3]=x(n=>i.$emit("arrowUp",n),["up"])),e[4]||(e[4]=x(()=>i.$emit("enter"),["enter"]))],"onUpdate:modelValue":e[5]||(e[5]=n=>o.value=n)},null,40,gt),[[de,o.value]]),e[6]||(e[6]=m("span",{class:"hidden",id:"emoji-picker-search-description"},"Use the left, right, up and down arrow keys to navigate the emoji search results.",-1))])}const pt=_(ft,[["render",vt]]);var wt=typeof window!="undefined";wt&&function(){for(var i=0,e=["ms","moz","webkit","o"],t=0;t<e.length&&!window.requestAnimationFrame;++t)window.requestAnimationFrame=window[e[t]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[e[t]+"CancelAnimationFrame"]||window[e[t]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(r,o){var s=new Date().getTime(),n=Math.max(0,16-(s-i)),l=window.setTimeout(function(){r(s+n)},n);return i=s+n,l}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(r){clearTimeout(r)})}();class _t{constructor(e){this._vm=e,this._data=e.data,this._perLine=e.perLine,this._categories=[],this._categories.push(...this._data.categories()),this._categories=this._categories.filter(t=>t.emojis.length>0),this._categories[0].first=!0,Object.freeze(this._categories),this.activeCategory=this._categories[0],this.searchEmojis=null,this.previewEmoji=null,this.previewEmojiCategoryIdx=0,this.previewEmojiIdx=-1}onScroll(){const t=this._vm.$refs.scroll.scrollTop;let r=this.filteredCategories[0];for(let o=0,s=this.filteredCategories.length;o<s;o++){let n=this.filteredCategories[o],l=this._vm.getCategoryComponent(o);if(l&&l.$el.offsetTop-50>t)break;r=n}this.activeCategory=r}get allCategories(){return this._categories}get filteredCategories(){return this.searchEmojis?[{id:"search",name:"Search",emojis:this.searchEmojis}]:this._categories.filter(e=>e.emojis.length>0)}get previewEmojiCategory(){return this.previewEmojiCategoryIdx>=0?this.filteredCategories[this.previewEmojiCategoryIdx]:null}onAnchorClick(e){if(this.searchEmojis)return;let t=this.filteredCategories.indexOf(e),r=this._vm.getCategoryComponent(t),o=()=>{if(r){let s=r.$el.offsetTop;e.first&&(s=0),this._vm.$refs.scroll.scrollTop=s}};this._vm.infiniteScroll?o():this.activeCategory=this.filteredCategories[t]}onSearch(e){let t=this._data.search(e,this.maxSearchResults);this.searchEmojis=t,this.previewEmojiCategoryIdx=0,this.previewEmojiIdx=0,this.updatePreviewEmoji()}onEmojiEnter(e){this.previewEmoji=e,this.previewEmojiIdx=-1,this.previewEmojiCategoryIdx=-1}onEmojiLeave(e){this.previewEmoji=null}onArrowLeft(){this.previewEmojiIdx>0?this.previewEmojiIdx-=1:(this.previewEmojiCategoryIdx-=1,this.previewEmojiCategoryIdx<0?this.previewEmojiCategoryIdx=0:this.previewEmojiIdx=this.filteredCategories[this.previewEmojiCategoryIdx].emojis.length-1),this.updatePreviewEmoji()}onArrowRight(){this.previewEmojiIdx<this.emojisLength(this.previewEmojiCategoryIdx)-1?this.previewEmojiIdx+=1:(this.previewEmojiCategoryIdx+=1,this.previewEmojiCategoryIdx>=this.filteredCategories.length?this.previewEmojiCategoryIdx=this.filteredCategories.length-1:this.previewEmojiIdx=0),this.updatePreviewEmoji()}onArrowDown(){if(this.previewEmojiIdx==-1)return this.onArrowRight();const e=this.filteredCategories[this.previewEmojiCategoryIdx].emojis.length;let t=this._perLine;this.previewEmojiIdx+t>e&&(t=e%this._perLine);for(let r=0;r<t;r++)this.onArrowRight();this.updatePreviewEmoji()}onArrowUp(){let e=this._perLine;this.previewEmojiIdx-e<0&&(this.previewEmojiCategoryIdx>0?e=this.filteredCategories[this.previewEmojiCategoryIdx-1].emojis.length%this._perLine:e=0);for(let t=0;t<e;t++)this.onArrowLeft();this.updatePreviewEmoji()}updatePreviewEmoji(){this.previewEmoji=this.filteredCategories[this.previewEmojiCategoryIdx].emojis[this.previewEmojiIdx],this._vm.$nextTick(()=>{const e=this._vm.$refs.scroll,t=e.querySelector(".emoji-mart-emoji-selected"),r=e.offsetTop-e.offsetHeight;t&&t.offsetTop+t.offsetHeight>r+e.scrollTop&&(e.scrollTop+=t.offsetHeight),t&&t.offsetTop<e.scrollTop&&(e.scrollTop-=t.offsetHeight)})}emojisLength(e){return e==-1?0:this.filteredCategories[e].emojis.length}}const yt={search:"Search",notfound:"No Emoji Found",categories:{search:"Search Results",recent:"Frequently Used",smileys:"Smileys & Emotion",people:"People & Body",nature:"Animals & Nature",foods:"Food & Drink",activity:"Activity",places:"Travel & Places",objects:"Objects",symbols:"Symbols",flags:"Flags",custom:"Custom"}},Ct={props:z(M({},Be),{data:{type:Object,required:!0}}),emits:["select","skin-change"],data(){return{activeSkin:this.skin||E.get("skin")||this.defaultSkin,view:new _t(this)}},computed:{customStyles(){return M({width:this.calculateWidth+"px"},this.pickerStyles)},emojiProps(){return{native:this.native,skin:this.activeSkin,set:this.set,emojiTooltip:this.emojiTooltip,emojiSize:this.emojiSize,selectedEmoji:this.view.previewEmoji,selectedEmojiCategory:this.view.previewEmojiCategory,onEnter:this.onEmojiEnter.bind(this),onLeave:this.onEmojiLeave.bind(this),onClick:this.onEmojiClick.bind(this)}},skinProps(){return{skin:this.activeSkin}},calculateWidth(){return this.perLine*(this.emojiSize+12)+12+2+ze()},filteredCategories(){return this.view.filteredCategories},mergedI18n(){return Object.freeze(se(yt,this.i18n))},idleEmoji(){try{return this.data.emoji(this.emoji)}catch(i){return this.data.firstEmoji()}},isSearching(){return this.view.searchEmojis!=null}},watch:{skin(){this.onSkinChange(this.skin)}},methods:{onScroll(){this.infiniteScroll&&!this.waitingForPaint&&(this.waitingForPaint=!0,window.requestAnimationFrame(this.onScrollPaint.bind(this)))},onScrollPaint(){this.waitingForPaint=!1,this.view.onScroll()},onAnchorClick(i){this.view.onAnchorClick(i)},onSearch(i){this.view.onSearch(i)},onEmojiEnter(i){this.view.onEmojiEnter(i)},onEmojiLeave(i){this.view.onEmojiLeave(i)},onArrowLeft(i){const e=this.view.previewEmojiIdx;this.view.onArrowLeft(),i&&this.view.previewEmojiIdx!==e&&i.preventDefault()},onArrowRight(){this.view.onArrowRight()},onArrowDown(){this.view.onArrowDown()},onArrowUp(i){this.view.onArrowUp(),i.preventDefault()},onEnter(){this.view.previewEmoji&&(this.$emit("select",this.view.previewEmoji),D.add(this.view.previewEmoji))},onEmojiClick(i){this.$emit("select",i),D.add(i)},onTextSelect(i){i.stopPropagation()},onSkinChange(i){this.activeSkin=i,E.update({skin:i}),this.$emit("skin-change",i)},getCategoryComponent(i){let e=this.$refs["categories_"+i];return e&&"0"in e?e[0]:e}},components:{Anchors:Oe,Category:Ze,Preview:dt,Search:pt}},kt={key:0,class:"emoji-mart-bar emoji-mart-bar-anchors"},Et={id:"emoji-mart-list",ref:"scrollContent",role:"listbox","aria-expanded":"true"},St={key:0,class:"emoji-mart-bar emoji-mart-bar-preview"};function bt(i,e,t,r,o,s){const n=w("anchors"),l=w("search"),a=w("category"),f=w("preview");return h(),c("section",{class:"emoji-mart emoji-mart-static",style:A(s.customStyles)},[i.showCategories?(h(),c("div",kt,[C(n,{data:t.data,i18n:s.mergedI18n,color:i.color,categories:o.view.allCategories,"active-category":o.view.activeCategory,onClick:s.onAnchorClick},null,8,["data","i18n","color","categories","active-category","onClick"])])):p("",!0),q(i.$slots,"searchTemplate",{data:t.data,i18n:i.i18n,autoFocus:i.autoFocus,onSearch:s.onSearch},()=>[i.showSearch?(h(),N(l,{key:0,ref:"search",data:t.data,i18n:s.mergedI18n,"auto-focus":i.autoFocus,"on-search":s.onSearch,onSearch:s.onSearch,onArrowLeft:s.onArrowLeft,onArrowRight:s.onArrowRight,onArrowDown:s.onArrowDown,onArrowUp:s.onArrowUp,onEnter:s.onEnter,onSelect:s.onTextSelect},null,8,["data","i18n","auto-focus","on-search","onSearch","onArrowLeft","onArrowRight","onArrowDown","onArrowUp","onEnter","onSelect"])):p("",!0)]),m("div",{role:"tabpanel",class:"emoji-mart-scroll",ref:"scroll",onScroll:e[0]||(e[0]=(...u)=>s.onScroll&&s.onScroll(...u))},[m("div",Et,[q(i.$slots,"customCategory"),(h(!0),c(j,null,k(o.view.filteredCategories,(u,S)=>Q((h(),N(a,{ref_for:!0,ref:"categories_"+S,key:u.id,data:t.data,i18n:s.mergedI18n,id:u.id,name:u.name,emojis:u.emojis,"emoji-props":s.emojiProps},null,8,["data","i18n","id","name","emojis","emoji-props"])),[[fe,i.infiniteScroll||u==o.view.activeCategory||s.isSearching]])),128))],512)],544),q(i.$slots,"previewTemplate",{data:t.data,title:i.title,emoji:o.view.previewEmoji,idleEmoji:s.idleEmoji,showSkinTones:i.showSkinTones,emojiProps:s.emojiProps,skinProps:s.skinProps,onSkinChange:s.onSkinChange},()=>[i.showPreview?(h(),c("div",St,[C(f,{data:t.data,title:i.title,emoji:o.view.previewEmoji,"idle-emoji":s.idleEmoji,"show-skin-tones":i.showSkinTones,"emoji-props":s.emojiProps,"skin-props":s.skinProps,"on-skin-change":s.onSkinChange},null,8,["data","title","emoji","idle-emoji","show-skin-tones","emoji-props","skin-props","on-skin-change"])])):p("",!0)])],4)}const Lt=_(Ct,[["render",bt]]);export{Oe as Anchors,Ze as Category,oe as Emoji,P as EmojiData,Ft as EmojiIndex,re as EmojiView,Lt as Picker,dt as Preview,pt as Search,et as Skins,D as frequently,Te as sanitize,E as store,_e as uncompress};
