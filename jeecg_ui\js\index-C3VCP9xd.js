import{d as v,f as u,ag as c,aB as f,ar as d,aD as o,at as p,k as t,G as a,aq as S}from"./vue-vendor-dy9k-Yad.js";import{B as _}from"./index-Xq00qzUB.js";import{u as b,a as $}from"./index-CCWaWN5g.js";import{aq as O,bo as V}from"./antd-vue-vendor-me9YkNVC.js";import{P as j}from"./index-CtJ0w2CP.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const D=v({components:{BasicDragVerify:_,BugOutlined:V,RightOutlined:O,PageWrapper:j},setup(){const{createMessage:s}=b(),e=u(null),y=u(null),g=u(null),k=u(null),B=u(null);function l(i){const{time:m}=i;s.success(`校验成功,耗时${m}秒`)}function r(i){i&&i.resume()}return{handleSuccess:l,el1:e,el2:y,el3:g,el4:k,el5:B,handleBtnClick:r}}}),P={class:"flex justify-center p-4 items-center bg-gray-700"},N={class:"flex justify-center p-4 items-center bg-gray-700"},W={class:"flex justify-center p-4 items-center bg-gray-700"},q={class:"flex justify-center p-4 items-center bg-gray-700"},I={class:"flex justify-center p-4 items-center bg-gray-700"},M={key:0},R={key:1};function T(s,e,y,g,k,B){const l=c("BasicDragVerify"),r=c("a-button"),i=c("BugOutlined"),m=c("RightOutlined"),C=c("PageWrapper");return d(),f(C,{title:"拖动校验示例"},{default:o(()=>[p("div",P,[t(l,{ref:"el1",onSuccess:s.handleSuccess},null,8,["onSuccess"]),t(r,{type:"primary",class:"ml-2",onClick:e[0]||(e[0]=n=>s.handleBtnClick(s.el1))},{default:o(()=>e[5]||(e[5]=[a(" 还原 ")])),_:1,__:[5]})]),p("div",N,[t(l,{ref:"el2",onSuccess:s.handleSuccess,circle:""},null,8,["onSuccess"]),t(r,{type:"primary",class:"ml-2",onClick:e[1]||(e[1]=n=>s.handleBtnClick(s.el2))},{default:o(()=>e[6]||(e[6]=[a(" 还原 ")])),_:1,__:[6]})]),p("div",W,[t(l,{ref:"el3",onSuccess:s.handleSuccess,text:"拖动以进行校验",successText:"校验成功",barStyle:{backgroundColor:"#018ffb"}},null,8,["onSuccess"]),t(r,{type:"primary",class:"ml-2",onClick:e[2]||(e[2]=n=>s.handleBtnClick(s.el3))},{default:o(()=>e[7]||(e[7]=[a(" 还原 ")])),_:1,__:[7]})]),p("div",q,[t(l,{ref:"el4",onSuccess:s.handleSuccess},{actionIcon:o(n=>[n?(d(),f(i,{key:0})):(d(),f(m,{key:1}))]),_:1},8,["onSuccess"]),t(r,{type:"primary",class:"ml-2",onClick:e[3]||(e[3]=n=>s.handleBtnClick(s.el4))},{default:o(()=>e[8]||(e[8]=[a(" 还原 ")])),_:1,__:[8]})]),p("div",I,[t(l,{ref:"el5",onSuccess:s.handleSuccess},{text:o(n=>[n?(d(),S("div",M,[t(i),e[9]||(e[9]=a(" 成功 "))])):(d(),S("div",R,[e[10]||(e[10]=a(" 拖动 ")),t(m)]))]),_:1},8,["onSuccess"]),t(r,{type:"primary",class:"ml-2",onClick:e[4]||(e[4]=n=>s.handleBtnClick(s.el5))},{default:o(()=>e[11]||(e[11]=[a(" 还原 ")])),_:1,__:[11]})])]),_:1})}const U=$(D,[["render",T],["__scopeId","data-v-cc79afa1"]]);export{U as default};
