var m=Object.defineProperty,i=Object.defineProperties;var c=Object.getOwnPropertyDescriptors;var l=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;var r=(e,t,a)=>t in e?m(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,s=(e,t)=>{for(var a in t||(t={}))p.call(t,a)&&r(e,a,t[a]);if(l)for(var a of l(t))u.call(t,a)&&r(e,a,t[a]);return e},d=(e,t)=>i(e,c(t));import{c as f}from"./user.api-mLAlJze4.js";import{rules as n}from"./validator-B_KkcUnu.js";import{render as o}from"./renderUtils-D7XVOFwj.js";const I=[{title:"用户账号",dataIndex:"username",width:120},{title:"用户姓名",dataIndex:"realname",width:100},{title:"头像",dataIndex:"avatar",width:120,customRender:o.renderAvatar},{title:"手机号",dataIndex:"phone",width:100},{title:"状态",dataIndex:"status_dictText",width:80}],g=[{title:"用户账号",dataIndex:"username",width:100},{title:"用户姓名",dataIndex:"realname",width:100},{title:"头像",dataIndex:"avatar",width:80,customRender:o.renderAvatar},{title:"性别",dataIndex:"sex",width:80,sorter:!0,customRender:({text:e})=>o.renderDict(e,"sex")}],y=[{label:"账号",labelWidth:64,field:"username",component:"JInput"},{label:"名字",labelWidth:64,field:"realname",component:"JInput"},{label:"用户状态",labelWidth:64,field:"status",component:"JDictSelectTag",componentProps:{dictCode:"user_status",placeholder:"请选择状态",stringToNumber:!0}}],x=[{label:"",field:"id",component:"Input",show:!1},{label:"用户账号",field:"username",component:"Input",required:!0,dynamicDisabled:({values:e})=>!!e.id,dynamicRules:({model:e,schema:t})=>n.duplicateCheckRule("sys_user","username",e,t,!0)},{label:"登录密码",field:"password",component:"StrengthMeter",componentProps:{autocomplete:"new-password"},rules:[{required:!0,message:"请输入登录密码"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,message:"密码由8位数字、大小写字母和特殊符号组成!"}]},{label:"确认密码",field:"confirmPassword",component:"InputPassword",dynamicRules:({values:e})=>n.confirmPassword(e,!0)},{label:"用户姓名",field:"realname",required:!0,component:"Input"},{label:"角色",field:"selectedroles",component:"ApiSelect",componentProps:{mode:"multiple",api:f,labelField:"roleName",valueField:"id",immediate:!1}},{label:"企业",field:"relTenantIds",component:"JDictSelectTag",componentProps:{dictCode:"sys_tenant,name,id",mode:"multiple"}},{label:"头像",field:"avatar",component:"JImageUpload",componentProps:{fileMax:1}},{label:"手机号码",field:"phone",component:"Input",required:!0,dynamicRules:({model:e,schema:t})=>[d(s({},n.duplicateCheckRule("sys_user","phone",e,t,!0)[0]),{trigger:"blur"}),{pattern:/^1[3456789]\d{9}$/,message:"手机号码格式有误",trigger:"blur"}]}],T=[{label:"用户账号",field:"username",component:"Input",componentProps:{readOnly:!0}},{label:"登录密码",field:"password",component:"StrengthMeter",componentProps:{placeholder:"请输入登录密码"},rules:[{required:!0,message:"请输入登录密码"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,message:"密码由8位数字、大小写字母和特殊符号组成!"}]},{label:"确认密码",field:"confirmPassword",component:"InputPassword",dynamicRules:({values:e})=>n.confirmPassword(e,!0)}],D=[{label:"",field:"id",component:"Input",show:!1},{field:"userName",label:"用户名",component:"Input",componentProps:{readOnly:!0,allowClear:!1}},{field:"agentUserName",label:"代理人用户名",required:!0,component:"JSelectUser",componentProps:{rowKey:"username",labelKey:"realname",maxSelectCount:10}},{field:"startTime",label:"代理开始时间",component:"DatePicker",required:!0,componentProps:{showTime:!0,valueFormat:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择代理开始时间",getPopupContainer:()=>document.body}},{field:"endTime",label:"代理结束时间",component:"DatePicker",required:!0,componentProps:{showTime:!0,valueFormat:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择代理结束时间",getPopupContainer:()=>document.body}},{field:"status",label:"状态",component:"JDictSelectTag",defaultValue:"1",componentProps:{dictCode:"valid_status",type:"radioButton"}}],S=[{label:"",field:"id",component:"Input",show:!1},{field:"userName",label:"用户名",component:"Input",componentProps:{readOnly:!0,allowClear:!1}},{field:"agentUserName",label:"交接人员",component:"JSelectUser",componentProps:{rowKey:"username",labelKey:"realname",maxSelectCount:1}},{field:"startTime",label:"交接开始时间",component:"DatePicker",componentProps:{showTime:!0,valueFormat:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择交接开始时间",getPopupContainer:()=>document.body}},{field:"endTime",label:"交接结束时间",component:"DatePicker",componentProps:{showTime:!0,valueFormat:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择交接结束时间",getPopupContainer:()=>document.body}},{field:"status",label:"状态",component:"JDictSelectTag",defaultValue:"1",componentProps:{dictCode:"valid_status",type:"radioButton"}}],C=[{title:"用户账号",dataIndex:"username",width:120},{title:"用户姓名",dataIndex:"realname",width:100},{title:"头像",dataIndex:"avatar",width:120,customRender:o.renderAvatar},{title:"手机号",dataIndex:"phone",width:100},{title:"状态",dataIndex:"status",width:80,customRender:({text:e})=>e==="1"?"正常":e==="3"?"审批中":"已拒绝"}],Y=[{label:"账号",field:"username",component:"Input",colProps:{span:6}},{label:"名字",field:"realname",component:"Input",colProps:{span:6}},{label:"性别",field:"sex",component:"JDictSelectTag",componentProps:{dictCode:"sex",placeholder:"请选择性别",stringToNumber:!0},colProps:{span:6}}];export{C as a,S as b,T as c,D as d,I as e,x as f,g as r,y as s,Y as u};
