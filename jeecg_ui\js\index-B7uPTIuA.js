import{f as o,o as f,ag as m,aq as d,ah as _,ar as v,k as h,aD as C,at as g,u as k}from"./vue-vendor-dy9k-Yad.js";import{a as x,bf as b,bg as c,aZ as r,bh as A,r as E}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const I="/assets/ai-CmEpHu56.png",V=["src"],B={__name:"index",setup(D){const e=o(1),s=o(null),a=o(!1);f(()=>{const t=b(c);t&&t==r()?e.value=!1:e.value=!0,e.value&&s.value.addEventListener("contextmenu",n=>{a.value=!0,n.preventDefault()})});const l=()=>{a.value=!1},i=()=>{a.value=!1,e.value=!1,A(c,r())},p=t=>{E.push({path:"/ai"})};return(t,n)=>{const u=m("a-popconfirm");return e.value?(v(),d("div",{key:0,ref_key:"aideWrapRef",ref:s,class:"aide-wrap",onClick:p},[h(u,{open:a.value,placement:"topRight",title:"确定AI助手退出吗？","ok-text":"确定","cancel-text":"取消",onCancel:l,onConfirm:i},{default:C(()=>[g("img",{src:k(I),alt:"ai助手"},null,8,V)]),_:1},8,["open"])],512)):_("",!0)}}},F=x(B,[["__scopeId","data-v-4b63abcc"]]);export{F as default};
