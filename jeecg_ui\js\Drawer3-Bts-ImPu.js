import{d as m,ag as s,aB as d,ar as p,aE as u,aD as t,aq as _,F as c,aC as f,at as k,k as r,G as a}from"./vue-vendor-dy9k-Yad.js";import{B}from"./index-JbqXEynz.js";import{a as w}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";const F=m({components:{BasicDrawer:B},setup(){return{handleOk:()=>{}}}});function b(n,e,D,$,C,O){const o=s("a-button"),i=s("BasicDrawer");return p(),d(i,u(n.$attrs,{title:"Modal Title",width:"50%",showFooter:"",onOk:n.handleOk}),{insertFooter:t(()=>[r(o,null,{default:t(()=>e[0]||(e[0]=[a(" btn")])),_:1,__:[0]})]),centerFooter:t(()=>[r(o,null,{default:t(()=>e[1]||(e[1]=[a(" btn2")])),_:1,__:[1]})]),appendFooter:t(()=>[r(o,null,{default:t(()=>e[2]||(e[2]=[a(" btn3")])),_:1,__:[2]})]),default:t(()=>[(p(),_(c,null,f(40,l=>k("p",{class:"h-20",key:l}," 根据屏幕高度自适应 ")),64))]),_:1},16,["onOk"])}const L=w(F,[["render",b]]);export{L as default};
