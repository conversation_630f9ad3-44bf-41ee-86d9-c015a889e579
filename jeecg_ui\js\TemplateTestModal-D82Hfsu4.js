var h=Object.defineProperty;var u=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var f=(r,o,t)=>o in r?h(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,g=(r,o)=>{for(var t in o||(o={}))w.call(o,t)&&f(r,t,o[t]);if(u)for(var t of u(o))M.call(o,t)&&f(r,t,o[t]);return r};var d=(r,o,t)=>new Promise((n,a)=>{var c=i=>{try{s(t.next(i))}catch(m){a(m)}},l=i=>{try{s(t.throw(i))}catch(m){a(m)}},s=i=>i.done?n(i.value):Promise.resolve(i.value).then(c,l);s((t=t.apply(r,o)).next())});import{d as F,aB as k,ar as y,aD as R,k as T,u as p,aE as S}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{B as b}from"./index-Diw57m_E.js";import{a as v,b as x}from"./template.api-Bv33NMH9.js";import{u as C}from"./useForm-CgkFTrrO.js";import{ac as L}from"./index-CCWaWN5g.js";import{B as P}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";const It=F({__name:"TemplateTestModal",emits:["register"],setup(r,{emit:o}){const t=o,[n,{resetFields:a,setFieldsValue:c,validate:l,updateSchema:s}]=C({schemas:v,showActionButtonGroup:!1}),[i,{setModalProps:m,closeModal:B}]=L(e=>d(null,null,function*(){yield a(),yield c(g({},p(e.record)))}));function _(){return d(this,null,function*(){try{const e=yield l();m({confirmLoading:!0}),yield x(e),B()}finally{m({confirmLoading:!1})}})}return(e,V)=>(y(),k(p(b),S({onRegister:p(i),title:"发送测试",width:800},e.$attrs,{onOk:_}),{default:R(()=>[T(p(P),{onRegister:p(n)},null,8,["onRegister"])]),_:1},16,["onRegister"]))}});export{It as default};
