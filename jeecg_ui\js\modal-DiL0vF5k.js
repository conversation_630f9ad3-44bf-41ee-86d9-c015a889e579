var L=(r,o,f)=>new Promise((k,_)=>{var C=t=>{try{n(f.next(t))}catch(m){_(m)}},s=t=>{try{n(f.throw(t))}catch(m){_(m)}},n=t=>t.done?k(t.value):Promise.resolve(t.value).then(C,s);n((f=f.apply(r,o)).next())});import{d as F,f as R,r as D,e as N,u as B,ag as i,aB as U,ar as M,aE as V,aD as l,k as e,G as u,at as z,aq as Y,F as $,aC as I}from"./vue-vendor-dy9k-Yad.js";import{B as E}from"./index-Diw57m_E.js";import{s as H}from"./jvxetable.api-C3sIybRa.js";import{o as P,a as S}from"./api-drClL0vf.js";import{ac as A,a as G}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const K=F({name:"tableModal",components:{BasicModal:E},emits:["success","register"],setup(r,{emit:o}){const f=R(!0),k=R(""),_=R(),C=D({xs:{span:24},sm:{span:5}}),s=D({xs:{span:24},sm:{span:16}}),n={orderCode:[{required:!0,message:"订单号不能为空",trigger:"blur"}]},t=D({id:null,orderCode:"",orderMoney:"",ctype:"",content:"",jeecgOrderCustomerList:[],jeecgOrderTicketList:[]}),[m,{setModalProps:v,closeModal:y}]=A(a=>L(null,null,function*(){if(v({confirmLoading:!1}),f.value=!!(a!=null&&a.isUpdate),w(),B(f)){k.value=a.record.id,Object.assign(t,a.record);let p={id:t.id};const d=yield P(p);t.jeecgOrderCustomerList=d;const q=yield S(p);t.jeecgOrderTicketList=q}})),c=N(()=>B(f)?"编辑":"新增");function g(){t.jeecgOrderCustomerList.push({})}function b(a){t.jeecgOrderCustomerList.splice(a,1),t.jeecgOrderCustomerList.splice(a,1)}function w(){t.id=null,t.orderCode="",t.orderMoney="",t.orderDate=null,t.ctype="",t.content="",t.jeecgOrderCustomerList=[],t.jeecgOrderTicketList=[]}function O(){t.jeecgOrderTicketList.push({})}function j(a){t.jeecgOrderTicketList.splice(a,1),t.jeecgOrderTicketList.splice(a,1)}function T(){return L(this,null,function*(){_.value.validate().then(()=>L(null,null,function*(){try{v({confirmLoading:!0}),yield H(t,B(f)),y(),o("success")}finally{v({confirmLoading:!1})}})).catch(a=>{})})}return{formRef:_,validatorRules:n,orderMainModel:t,registerModal:m,getTitle:c,labelCol:C,wrapperCol:s,addRowCustom:g,delRowCustom:b,addRowTicket:O,delRowTicket:j,handleSubmit:T}}});function J(r,o,f,k,_,C){const s=i("a-input"),n=i("a-form-item"),t=i("a-col"),m=i("a-select-option"),v=i("a-select"),y=i("a-date-picker"),c=i("a-row"),g=i("Icon"),b=i("a-button"),w=i("a-tab-pane"),O=i("a-tabs"),j=i("a-form"),T=i("BasicModal");return M(),U(T,V(r.$attrs,{onRegister:r.registerModal,title:r.getTitle,onOk:r.handleSubmit,width:"70%"}),{default:l(()=>[e(j,{ref:"formRef",model:r.orderMainModel,"label-col":r.labelCol,"wrapper-col":r.wrapperCol,rules:r.validatorRules},{default:l(()=>[e(c,{class:"form-row",gutter:16},{default:l(()=>[e(t,{lg:8},{default:l(()=>[e(n,{label:"订单号",name:"orderCode"},{default:l(()=>[e(s,{value:r.orderMainModel.orderCode,"onUpdate:value":o[0]||(o[0]=a=>r.orderMainModel.orderCode=a),placeholder:"请输入订单号"},null,8,["value"])]),_:1})]),_:1}),e(t,{lg:8},{default:l(()=>[e(n,{label:"订单类型"},{default:l(()=>[e(v,{placeholder:"请选择订单类型",value:r.orderMainModel.ctype,"onUpdate:value":o[1]||(o[1]=a=>r.orderMainModel.ctype=a)},{default:l(()=>[e(m,{value:"1"},{default:l(()=>o[5]||(o[5]=[u("国内订单")])),_:1,__:[5]}),e(m,{value:"2"},{default:l(()=>o[6]||(o[6]=[u("国际订单")])),_:1,__:[6]})]),_:1},8,["value"])]),_:1})]),_:1}),e(t,{lg:8},{default:l(()=>[e(n,{label:"订单日期"},{default:l(()=>[e(y,{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss",value:r.orderMainModel.orderDate,"onUpdate:value":o[2]||(o[2]=a=>r.orderMainModel.orderDate=a)},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(c,{class:"form-row",gutter:16},{default:l(()=>[e(t,{lg:8},{default:l(()=>[e(n,{label:"订单金额"},{default:l(()=>[e(s,{value:r.orderMainModel.orderMoney,"onUpdate:value":o[3]||(o[3]=a=>r.orderMainModel.orderMoney=a),placeholder:"请输入订单金额"},null,8,["value"])]),_:1})]),_:1}),e(t,{lg:8},{default:l(()=>[e(n,{label:"订单备注"},{default:l(()=>[e(s,{value:r.orderMainModel.content,"onUpdate:value":o[4]||(o[4]=a=>r.orderMainModel.content=a),placeholder:"请输入订单备注"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(O,{defaultActiveKey:"1"},{default:l(()=>[e(w,{tab:"客户信息",key:"1"},{default:l(()=>[z("div",null,[e(c,{type:"flex",style:{"margin-bottom":"10px"},gutter:16},{default:l(()=>[e(t,{span:5},{default:l(()=>o[7]||(o[7]=[u("客户名")])),_:1,__:[7]}),e(t,{span:5},{default:l(()=>o[8]||(o[8]=[u("性别")])),_:1,__:[8]}),e(t,{span:6},{default:l(()=>o[9]||(o[9]=[u("身份证号码")])),_:1,__:[9]}),e(t,{span:6},{default:l(()=>o[10]||(o[10]=[u("手机号")])),_:1,__:[10]}),e(t,{span:2},{default:l(()=>o[11]||(o[11]=[u("操作")])),_:1,__:[11]})]),_:1}),(M(!0),Y($,null,I(r.orderMainModel.jeecgOrderCustomerList,(a,p)=>(M(),U(c,{type:"flex",style:{"margin-bottom":"10px"},gutter:16,key:p},{default:l(()=>[e(t,{span:6,style:{display:"none"}},{default:l(()=>[e(n,null,{default:l(()=>[e(s,{placeholder:"id",value:a.id,"onUpdate:value":d=>a.id=d},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(t,{span:5},{default:l(()=>[e(n,null,{default:l(()=>[e(s,{placeholder:"客户名",value:a.name,"onUpdate:value":d=>a.name=d},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(t,{span:5},{default:l(()=>[e(n,null,{default:l(()=>[e(v,{placeholder:"性别",value:a.sex,"onUpdate:value":d=>a.sex=d},{default:l(()=>[e(m,{value:"1"},{default:l(()=>o[12]||(o[12]=[u("男")])),_:1,__:[12]}),e(m,{value:"2"},{default:l(()=>o[13]||(o[13]=[u("女")])),_:1,__:[13]})]),_:2},1032,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(t,{span:6},{default:l(()=>[(M(),U(n,{name:["jeecgOrderCustomerList",p,"idcard"],rules:[{required:!0,message:"请输入身份证号",trigger:"blur"}],key:p},{default:l(()=>[e(s,{placeholder:"身份证号",value:a.idcard,"onUpdate:value":d=>a.idcard=d},null,8,["value","onUpdate:value"])]),_:2},1032,["name"]))]),_:2},1024),e(t,{span:6},{default:l(()=>[e(n,{name:["jeecgOrderCustomerList",p,"telphone"]},{default:l(()=>[e(s,{placeholder:"手机号",value:a.telphone,"onUpdate:value":d=>a.telphone=d},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])]),_:2},1024),e(t,{span:2},{default:l(()=>[e(n,null,{default:l(()=>[e(g,{icon:"ant-design:minus-outlined",onClick:d=>r.delRowCustom(p),style:{fontsize:"20px"}},null,8,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128)),e(b,{type:"dashed",style:{width:"98%","margin-top":"10px"},onClick:r.addRowCustom},{default:l(()=>[e(g,{icon:"ph:plus-bold"}),o[14]||(o[14]=u(" 添加客户信息 "))]),_:1,__:[14]},8,["onClick"])])]),_:1}),e(w,{tab:"机票信息",key:"2",forceRender:""},{default:l(()=>[e(c,{type:"flex",style:{"margin-bottom":"10px"},gutter:16},{default:l(()=>[e(t,{span:6},{default:l(()=>o[15]||(o[15]=[u("航班号")])),_:1,__:[15]}),e(t,{span:6},{default:l(()=>o[16]||(o[16]=[u("航班时间")])),_:1,__:[16]}),e(t,{span:6},{default:l(()=>o[17]||(o[17]=[u("操作")])),_:1,__:[17]})]),_:1}),(M(!0),Y($,null,I(r.orderMainModel.jeecgOrderTicketList,(a,p)=>(M(),U(c,{type:"flex",style:{"margin-bottom":"10px"},gutter:16,key:p},{default:l(()=>[e(t,{span:6,style:{display:"none"}},{default:l(()=>[e(n,null,{default:l(()=>[e(s,{placeholder:"id",value:a.id,"onUpdate:value":d=>a.id=d},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(t,{span:6},{default:l(()=>[e(n,{name:["jeecgOrderTicketList",p,"ticketCode"],rules:{required:!0,message:"请输入航班号",trigger:"blur"}},{default:l(()=>[e(s,{placeholder:"航班号",value:a.ticketCode,"onUpdate:value":d=>a.ticketCode=d},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])]),_:2},1024),e(t,{span:6},{default:l(()=>[e(n,null,{default:l(()=>[e(y,{placeholder:"航班时间",valueFormat:"YYYY-MM-DD",value:a.tickectDate,"onUpdate:value":d=>a.tickectDate=d},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(t,{span:6},{default:l(()=>[e(n,null,{default:l(()=>[e(g,{icon:"ant-design:minus-outlined",onClick:d=>r.delRowTicket(p),style:{fontsize:"20px"}},null,8,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128)),e(b,{type:"dashed",style:{width:"98%","margin-top":"10px"},onClick:r.addRowTicket},{default:l(()=>[e(g,{icon:"ph:plus-bold"}),o[18]||(o[18]=u(" 添加机票信息 "))]),_:1,__:[18]},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["model","label-col","wrapper-col","rules"])]),_:1},16,["onRegister","title","onOk"])}const fe=G(K,[["render",J],["__scopeId","data-v-ffab824d"]]);export{fe as default};
