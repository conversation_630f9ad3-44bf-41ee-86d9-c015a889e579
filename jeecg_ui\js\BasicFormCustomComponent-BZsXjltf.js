import{d as i,aB as n,ar as r,u as l}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{j as t,a}from"./index-CCWaWN5g.js";import{u as m}from"./useForm-CgkFTrrO.js";import{B as s}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const c=[{label:"验证码",field:"code",component:"InputCountDown",componentProps:{size:"default",count:120}},{label:"Api下拉选择",field:"apiSelect",component:"ApiSelect",componentProps:{mode:"multiple",api:()=>t.get({url:"/test/jeecgDemo/list"}),numberToString:!1,labelField:"name",valueField:"id",params:{},resultField:"records"}},{label:"Api树选择",field:"apiSelect",component:"ApiTreeSelect",componentProps:{api:()=>t.get({url:"/mock/tree/getDemoOptions"}),params:{},resultField:"list"}},{label:"校验密码强度",field:"pwd",component:"StrengthMeter",componentProps:{showInput:!0,disabled:!1}},{label:"省市县联动",field:"province",component:"JAreaLinkage",componentProps:{showArea:!0,showAll:!0}},{label:"岗位选择",field:"post",component:"JSelectPosition",componentProps:{showSelected:!0,maxSelectCount:1,modalTitle:"岗位"}},{label:"角色选择",field:"role",component:"JSelectRole",componentProps:{params:{},isRadioSelection:!0,modalTitle:"角色"}},{label:"用户选择",field:"user",component:"JSelectUser",componentProps:{rowKey:"username",labelKey:"realname",showButton:!1,modalTitle:"用户"}},{label:"图片上传",field:"uploadImage",component:"JImageUpload",componentProps:{text:"图片上传",listType:"picture-card",bizPath:"temp",disabled:!1,fileMax:1}},{label:"字典标签",field:"dictTags",component:"JDictSelectTag",componentProps:{dictCode:"sex",type:"radioButton"}},{label:"部门选择",field:"dept",component:"JSelectDept",componentProps:{sync:!1,checkable:!0,showButton:!1,checkStrictly:!0,modalTitle:"部门选择"}},{label:"省市县级联动",field:"provinceArea",component:"JAreaSelect",componentProps:{level:3}},{label:"富文本",field:"editor",component:"JEditor",componentProps:{disabled:!1}},{label:"markdown",field:"markdown",component:"JMarkdownEditor",componentProps:{disabled:!1}},{label:"可输入下拉框",field:"inputSelect",component:"JSelectInput",componentProps:{options:[{label:"Default",value:"default"},{label:"IFrame",value:"iframe"}],showSearch:!0,disabled:!1}},{label:"代码编辑器组件",field:"jCode",component:"JCodeEditor",componentProps:{height:"150px",disabled:!1,fullScreen:!1,zIndex:999,theme:"idea",keywords:["console"],language:"javascript"}},{label:"分类字典树",field:"dictTree",component:"JCategorySelect",componentProps:{placeholder:"请选择分类字典树",condition:"",multiple:!1,pcode:"A04",pid:"",back:"id"}},{label:"下拉多选",field:"selectMultiple",component:"JSelectMultiple",componentProps:{dictCode:"company_rank",readOnly:!1}},{label:"popup",field:"popup",component:"JPopup",componentProps:({formActionType:o})=>{const{setFieldsValue:e}=o;return{setFieldsValue:e,code:"demo",multi:!1,fieldConfig:[{source:"name",target:"popup"}]}}},{label:"开关自定义",field:"switch",component:"JSwitch",componentProps:{options:["Y","N"],labelOptions:["是","否"],query:!1,disabled:!1}},{label:"定时表达式选择",field:"timing",component:"JEasyCron",componentProps:{hideSecond:!1,hideYear:!1,disabled:!1,remote:(o,e,p)=>{}}},{label:"分类字典树",field:"treeDict",component:"JTreeDict",componentProps:{field:"id",async:!0,disabled:!1,parentCode:"A04"}},{label:"多行输入窗口",field:"inputPop",component:"JInputPop",componentProps:{title:"多行输入窗口",position:"bottom"}},{label:"多选",field:"multipleChoice",component:"JCheckbox",componentProps:{dictCode:"company_rank",disabled:!1}},{label:"下拉树选择",field:"treeCusSelect",component:"JTreeSelect",componentProps:{dict:"sys_permission,name,id",pidField:"parent_id"}},{label:"根据部门选择用户组件",field:"userByDept",component:"JSelectUserByDept",componentProps:{showButton:!0,modalTitle:"部门用户选择"}},{label:"文件上传",field:"uploadFile",component:"JUpload",componentProps:{text:"文件上传",maxCount:2,download:!0}},{label:"字典表搜索",field:"dictSearchSelect",component:"JSearchSelect",componentProps:{dict:"demo,name,id",async:!0,pageSize:3}},{label:"动态创建input框",field:"jAddInput",component:"JAddInput",componentProps:{min:1}},{label:"用户选择组件",field:"userCusSelect",component:"UserSelect",componentProps:{multi:!0,store:"id",izExcludeMy:!1,disabled:!1}},{label:"选择角色组件",field:"roleSelect",component:"RoleSelect",componentProps:{maxSelectCount:4,multi:!0}},{label:"数值范围输入框",field:"rangeNumber",component:"JRangeNumber"},{label:"远程Api单选框组",field:"apiRadioGroup",component:"ApiRadioGroup",componentProps:{api:()=>t.get({url:"/mock/select/getDemoOptions"}),params:{},isBtn:!1,resultField:"list",labelField:"name",valueField:"id"}}],d=i({__name:"BasicFormCustomComponent",setup(o){const[e,{getFieldsValue:p,setFieldsValue:u,resetFields:f,validate:b}]=m({schemas:c,labelWidth:"150px",showActionButtonGroup:!1});return(P,S)=>(r(),n(l(s),{onRegister:l(e),style:{"margin-top":"20px"}},null,8,["onRegister"]))}}),he=a(d,[["__scopeId","data-v-c4dea78f"]]);export{he as default};
