var v=Object.defineProperty;var m=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var u=(t,o,e)=>o in t?v(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,f=(t,o)=>{for(var e in o||(o={}))F.call(o,e)&&u(t,e,o[e]);if(m)for(var e of m(o))I.call(o,e)&&u(t,e,o[e]);return t};var p=(t,o,e)=>new Promise((l,s)=>{var d=a=>{try{r(e.next(a))}catch(n){s(n)}},i=a=>{try{r(e.throw(a))}catch(n){s(n)}},r=a=>a.done?l(a.value):Promise.resolve(a.value).then(d,i);r((e=e.apply(t,o)).next())});import{l as P,d as D,f as S,e as k,u as g,ag as b,aB as w,ar as y,aE as N,aD as T,k as x}from"./vue-vendor-dy9k-Yad.js";import{B as R}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{X as $}from"./antd-vue-vendor-me9YkNVC.js";import{a as q}from"./system-bqUZCbh5.js";import{B as C}from"./BasicForm-DBcXiHk0.js";import{u as L}from"./useForm-CgkFTrrO.js";import{ac as O,a as V}from"./index-CCWaWN5g.js";const Z=[{title:"部门名称",dataIndex:"deptName",width:160,align:"left"},{title:"排序",dataIndex:"orderNo",width:50},{title:"状态",dataIndex:"status",width:80,customRender:({record:t})=>{const e=~~t.status===0,l=e?"green":"red",s=e?"启用":"停用";return P($,{color:l},()=>s)}},{title:"创建时间",dataIndex:"createTime",width:180},{title:"备注",dataIndex:"remark"}],ee=[{field:"deptName",label:"部门名称",component:"Input",colProps:{span:8}},{field:"status",label:"状态",component:"Select",componentProps:{options:[{label:"启用",value:"0"},{label:"停用",value:"1"}]},colProps:{span:8}}],j=[{field:"deptName",label:"部门名称",component:"Input",required:!0},{field:"parentDept",label:"上级部门",component:"TreeSelect",componentProps:{replaceFields:{title:"deptName",key:"id",value:"id"},getPopupContainer:()=>document.body},required:!0},{field:"orderNo",label:"排序",component:"InputNumber",required:!0},{field:"status",label:"状态",component:"RadioButtonGroup",defaultValue:"0",componentProps:{options:[{label:"启用",value:"0"},{label:"停用",value:"1"}]},required:!0},{label:"备注",field:"remark",component:"InputTextArea"}],A=D({name:"DeptModal",components:{BasicModal:R,BasicForm:C},emits:["success","register"],setup(t,{emit:o}){const e=S(!0),[l,{resetFields:s,setFieldsValue:d,updateSchema:i,validate:r}]=L({labelWidth:100,schemas:j,showActionButtonGroup:!1}),[a,{setModalProps:n,closeModal:h}]=O(c=>p(null,null,function*(){s(),n({confirmLoading:!1}),e.value=!!(c!=null&&c.isUpdate),g(e)&&d(f({},c.record));const M=yield q();i({field:"parentDept",componentProps:{treeData:M}})})),B=k(()=>g(e)?"编辑部门":"新增部门");function _(){return p(this,null,function*(){try{const c=yield r();n({confirmLoading:!0}),h(),o("success")}finally{n({confirmLoading:!1})}})}return{registerModal:a,registerForm:l,getTitle:B,handleSubmit:_}}});function G(t,o,e,l,s,d){const i=b("BasicForm"),r=b("BasicModal");return y(),w(r,N(t.$attrs,{onRegister:t.registerModal,title:t.getTitle,onOk:t.handleSubmit}),{default:T(()=>[x(i,{onRegister:t.registerForm},null,8,["onRegister"])]),_:1},16,["onRegister","title","onOk"])}const U=V(A,[["render",G]]),te=Object.freeze(Object.defineProperty({__proto__:null,default:U},Symbol.toStringTag,{value:"Module"}));export{U as D,te as a,Z as c,ee as s};
