import{f,u as l}from"./vue-vendor-dy9k-Yad.js";import{q as c}from"./index-CCWaWN5g.js";function d(r){const t=f(r),n=f(!1);let e;function o(){e&&window.clearInterval(e)}function u(){n.value=!1,o(),e=null}function s(){l(n)||e||(n.value=!0,e=setInterval(()=>{l(t)===1?(u(),t.value=r):t.value-=1},1e3))}function a(){t.value=r,u()}function i(){a(),s()}return c(()=>{a()}),{start:s,reset:a,restart:i,clear:o,stop:u,currentCount:t,isStart:n}}export{d as u};
