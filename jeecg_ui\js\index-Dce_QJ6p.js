var z=Object.defineProperty,F=Object.defineProperties;var K=Object.getOwnPropertyDescriptors;var w=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var E=(e,t,s)=>t in e?z(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,o=(e,t)=>{for(var s in t||(t={}))L.call(t,s)&&E(e,s,t[s]);if(w)for(var s of w(t))R.call(t,s)&&E(e,s,t[s]);return e},D=(e,t)=>F(e,K(t));import{F as q,a9 as G,n as I,a7 as H,I as J,w as Q}from"./index-CCWaWN5g.js";import{i as T,d as U,f as x,e as m,u as n,k as p,aE as X,g as Y}from"./vue-vendor-dy9k-Yad.js";import{aC as Z,K as S}from"./antd-vue-vendor-me9YkNVC.js";import{C as ee}from"./index-LCGLvkB3.js";function te(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!T(e)}const ne={useCollapse:{type:Boolean,default:!0},title:{type:String,default:""},size:{type:String,validator:e=>["small","default","middle",void 0].includes(e),default:"small"},bordered:{type:Boolean,default:!0},column:{type:[Number,Object],default:()=>({xxl:4,xl:3,lg:3,md:3,sm:2,xs:1})},collapseOptions:{type:Object,default:null},schema:{type:Array,default:()=>[]},data:{type:Object}},se=U({name:"Description",props:ne,emits:["register"],setup(e,{slots:t,emit:s}){const d=x(null),{prefixCls:b}=q("description"),i=G(),l=m(()=>o(o({},e),n(d))),g=m(()=>D(o({},n(l)),{title:void 0})),_=m(()=>!!n(l).title),M=m(()=>o({canExpand:!1},n(g).collapseOptions)),N=m(()=>o(o({},n(i)),n(g)));function W(r){d.value=o(o({},n(d)),r)}function $({label:r,labelMinWidth:c,labelStyle:a}){if(!a&&!c)return r;const u=D(o({},a),{minWidth:`${c}px `});return p("div",{style:u},[r])}function k(){const{schema:r,data:c}=n(g);return n(r).map(a=>{const{render:u,field:P,span:B,show:C,contentMinWidth:O}=a;if(C&&I(C)&&!C(c))return null;const v=()=>{var j;const f=(j=n(g))==null?void 0:j.data;if(!f)return null;const y=Z(f,P);return I(u)?u(y,f):y!=null?y:""},V=O;return p(S.Item,{label:$(a),key:P,span:B},{default:()=>{if(!O)return v();const f={minWidth:`${V}px`};return p("div",{style:f},[v()])}})}).filter(a=>!!a)}const h=()=>{let r;return p(S,X({class:`${b}`},n(N)),te(r=k())?r:{default:()=>[r]})},A=()=>{const r=e.useCollapse?h():p("div",null,[h()]);if(!e.useCollapse)return r;const{canExpand:c,helpMessage:a}=n(M),{title:u}=n(l);return p(ee,{title:u,canExpan:c,helpMessage:a},{default:()=>r,action:()=>H(t,"action")})};return s("register",{setDescProps:W}),()=>n(_)?A():h()}});function ue(e){if(!Y())throw new Error("useDescription() can only be used inside setup() or functional components!");const t=x(null),s=x(!1);function d(i){n(s)&&J()||(t.value=i,e&&i.setDescProps(e),s.value=!0)}return[d,{setDescProps:i=>{var l;(l=n(t))==null||l.setDescProps(i)}}]}const pe=Q(se);export{pe as D,ue as u};
