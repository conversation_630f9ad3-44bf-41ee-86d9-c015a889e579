import{d as f,aB as h,w as v,n as g,ar as a,u as t,aE as k,aD as y,aq as s,ah as B,F as L,aC as w,at as n,au as l}from"./vue-vendor-dy9k-Yad.js";import{v as x}from"./useNode-08a107c9-1Tvy8vWu.js";import"./NodeStyle-59980363-DzErCCzZ.js";import"./index-9c51646a-BviQbLw-.js";import"./antd-vue-vendor-me9YkNVC.js";import{x as C}from"./NodeContainer.vue_vue_type_style_index_0_lang-60bcf3b8-DFhy0o3x.js";import"./NodeKV.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const _={key:0,class:"switch-node-content"},b={class:"case-item"},N={class:"case-header"},P=f({__name:"SwitchNode",props:{node:{type:Object,required:!0},graph:{type:Object,required:!0}},setup(d){const o=d,{$node:r,updateHeight:p,containerRef:m,containerProps:u}=x(o,{onUpdateNode(e){e.caseList=o.node.$caseList}});return v(()=>{var e;return(e=r.value.caseList)==null?void 0:e.length},()=>g(()=>p())),(e,q)=>(a(),h(t(C),k({ref_key:"containerRef",ref:m},t(u)),{default:y(()=>{var i;return[(i=t(r).caseList)!=null&&i.length?(a(),s("div",_,[(a(!0),s(L,null,w(t(r).caseList,c=>(a(),s("div",b,[n("div",N,[n("div",null,l(c.label),1),n("div",null,l(c.type),1)])]))),256))])):B("",!0)]}),_:1},16))}});export{P as V};
