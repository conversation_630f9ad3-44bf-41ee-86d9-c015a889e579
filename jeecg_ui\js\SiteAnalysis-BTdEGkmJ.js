import{d as n,f as c,aB as p,ar as e,u as _,aE as f,aD as u,aq as o,ah as r,k as s}from"./vue-vendor-dy9k-Yad.js";import{J as l}from"./antd-vue-vendor-me9YkNVC.js";import b from"./VisitAnalysis-DX-4-4QD.js";import k from"./VisitAnalysisBar-EwDB5Y_K.js";import"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";import"./props-BGjQktHt.js";const d={key:0},y={key:1},A=n({__name:"SiteAnalysis",setup(v){const t=c("tab1"),i=[{key:"tab1",tab:"流量趋势"},{key:"tab2",tab:"访问量"}];function m(a){t.value=a}return(a,h)=>(e(),p(_(l),f({"tab-list":i},a.$attrs,{"active-tab-key":t.value,onTabChange:m}),{default:u(()=>[t.value==="tab1"?(e(),o("p",d,[s(b)])):r("",!0),t.value==="tab2"?(e(),o("p",y,[s(k)])):r("",!0)]),_:1},16,["active-tab-key"]))}});export{A as default};
