var K=(h,S,y)=>new Promise((k,m)=>{var i=s=>{try{d(y.next(s))}catch(g){m(g)}},v=s=>{try{d(y.throw(s))}catch(g){m(g)}},d=s=>s.done?k(s.value):Promise.resolve(s.value).then(i,v);d((y=y.apply(h,S)).next())});import{d as Q,f as c,ag as D,aB as P,ar as $,aD as n,k as a,u as r,G as u,ah as W,au as X,at as L,aE as Z}from"./vue-vendor-dy9k-Yad.js";import{a as ee,u as te,B as le}from"./index-JbqXEynz.js";import{_ as oe}from"./index-BtIdS_Qz.js";import ne from"./RoleDataRuleDrawer-C7vwg56A.js";import{m as se,n as j,o as ae}from"./role.api-BvRyEQIC.js";import{cf as N,N as re,a as ie}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";const ue=Q({__name:"RolePermissionDrawer",emits:["register"],setup(h,{emit:S}){const y=S,k=c([]),m=c([]),i=c([]),v=c([]),d=c([]),s=c(""),g=c(null),_=c(!1),I=c([]),T=c(!1),[F,{openDrawer:V}]=ee(),[J,{setDrawerProps:b,closeDrawer:E}]=te(t=>K(null,null,function*(){yield U(),b({confirmLoading:!1,loading:!0}),s.value=t.roleId;const e=yield se();k.value=O(e.treeList),m.value=e.ids;const o=localStorage.getItem(N);if(o){const f=JSON.parse(o);f.level&&A({key:f.level}),f.expand&&A({key:f.expand})}else I.value=e.ids;const l=yield j({roleId:r(s)});i.value=l,v.value=l,b({loading:!1})}));function O(t){return t!=null&&t.length&&t.forEach(e=>{var o;if(e.slotTitle){const{t:l}=re();e.slotTitle.includes("t('")&&l&&(e.slotTitle=new Function("t",`return ${e.slotTitle}`)(l))}(o=e.children)!=null&&o.length&&O(e.children)}),t}function q(t,e){if(T.value)i.value=t.checked?t.checked:t;else{const o=M(e.node,"children","key");if(e.checked)i.value=[...new Set([...i.value,...o])];else{const l=G(i.value,o);i.value=l}}}function G(t,e){const o={};for(const l of e)o[l]=!0;return t.filter(l=>!o[l])}function M(t,e,o){var R;const l=[];l.push(t[o]);const f=w=>{w.forEach(p=>{var x;l.push(p[o]),(x=p[e])!=null&&x.length&&f(p[e])})};return(R=t[e])!=null&&R.length&&f(t[e]),l}function H(t){t&&t.length>0&&(d.value=t),V(!0,{functionId:r(d)[0],roleId:r(s)})}function U(){k.value=[],m.value=[],i.value=[],v.value=[],d.value=[],s.value=""}function Y(){const t=r(g);if(!t)throw new Error("tree is null!");return t}function B(t){return K(this,null,function*(){let e={roleId:r(s),permissionIds:r(Y().getCheckedKeys()).join(","),lastpermissionIds:r(v).join(",")};if(_.value===!1&&(yield z(e)),t)E();else{const o=yield j({roleId:r(s)});v.value=o}})}function z(t){return K(this,null,function*(){_.value=!0;try{yield ae(t)}catch(e){_.value=!1}setTimeout(()=>{_.value=!1},500)})}function A({key:t}){t==="checkAll"?i.value=m.value:t==="cancelCheck"?i.value=[]:t==="openAll"?(I.value=m.value,C("expand","openAll")):t==="closeAll"?(I.value=[],C("expand","closeAll")):t==="relation"?(T.value=!1,C("level","relation")):(T.value=!0,C("level","standAlone"))}const C=(t,e)=>{const o=localStorage.getItem(N),l=o?JSON.parse(o):{};l[t]=e,localStorage.setItem(N,JSON.stringify(l))};return(t,e)=>{const o=D("Icon"),l=D("a-menu-item"),f=D("a-menu"),R=D("a-dropdown"),w=D("a-button");return $(),P(r(le),Z(t.$attrs,{onRegister:r(J),width:"650px",destroyOnClose:"",showFooter:""}),{title:n(()=>[e[10]||(e[10]=u(" 角色权限配置 ")),a(R,null,{overlay:n(()=>[a(f,{onClick:A},{default:n(()=>[a(l,{key:"checkAll"},{default:n(()=>e[2]||(e[2]=[u("选择全部")])),_:1,__:[2]}),a(l,{key:"cancelCheck"},{default:n(()=>e[3]||(e[3]=[u("取消选择")])),_:1,__:[3]}),e[8]||(e[8]=L("div",{class:"line"},null,-1)),a(l,{key:"openAll"},{default:n(()=>e[4]||(e[4]=[u("展开全部")])),_:1,__:[4]}),a(l,{key:"closeAll"},{default:n(()=>e[5]||(e[5]=[u("折叠全部")])),_:1,__:[5]}),e[9]||(e[9]=L("div",{class:"line"},null,-1)),a(l,{key:"relation"},{default:n(()=>e[6]||(e[6]=[u("层级关联")])),_:1,__:[6]}),a(l,{key:"standAlone"},{default:n(()=>e[7]||(e[7]=[u("层级独立")])),_:1,__:[7]})]),_:1,__:[8,9]})]),default:n(()=>[a(o,{icon:"ant-design:more-outlined",class:"more-icon"})]),_:1})]),footer:n(()=>[a(w,{onClick:r(E)},{default:n(()=>e[11]||(e[11]=[u("取消")])),_:1,__:[11]},8,["onClick"]),a(w,{onClick:e[0]||(e[0]=p=>B(!1)),type:"primary",loading:_.value,ghost:"",style:{"margin-right":"0.8rem"}},{default:n(()=>e[12]||(e[12]=[u("仅保存")])),_:1,__:[12]},8,["loading"]),a(w,{onClick:e[1]||(e[1]=p=>B(!0)),type:"primary",loading:_.value},{default:n(()=>e[13]||(e[13]=[u("保存并关闭")])),_:1,__:[13]},8,["loading"])]),default:n(()=>[a(r(oe),{ref_key:"treeRef",ref:g,checkable:"",treeData:k.value,checkedKeys:i.value,expandedKeys:I.value,selectedKeys:d.value,clickRowToExpand:!1,checkStrictly:!0,title:"所拥有的的权限",onCheck:q,onSelect:H},{title:n(({slotTitle:p,ruleFlag:x})=>[u(X(p)+" ",1),x?($(),P(o,{key:0,icon:"ant-design:align-left-outlined",style:{"margin-left":"5px",color:"red"}})):W("",!0)]),_:1},8,["treeData","checkedKeys","expandedKeys","selectedKeys"]),a(ne,{onRegister:r(F)},null,8,["onRegister"])]),_:1},16,["onRegister"])}}}),Te=ie(ue,[["__scopeId","data-v-8ccc97a9"]]);export{Te as default};
