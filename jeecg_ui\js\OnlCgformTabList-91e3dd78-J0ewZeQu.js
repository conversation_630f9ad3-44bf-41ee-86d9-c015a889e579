import{d as Oe,f as Qe,w as ze,ag as D,aq as f,ar as o,aB as l,ah as s,q as N,k as c,u as e,B as O,as as Je,aD as i,at as b,au as y,G as P,F as Q,aC as Ae,aE as Ke,aP as Ve,aQ as je,aJ as $e,aK as Ue}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{u as Ge}from"./index-CCWaWN5g.js";import We from"./OnlineCustomModal-c8b1e780-DLSsFULK.js";import Xe from"./OnlineTabAutoModal-c9ca3c24-C383VmEl.js";import z from"./OnlineTabDetailModal-fd4c9a72-Bxs35kTz.js";import{a as Ye}from"./JImportModal-BnQ3nPZC.js";import{d as Ze,m as et}from"./useListButton-98908683-Bu4g4Tt-.js";import{u as tt,a as ot,g as nt,O as rt}from"./useExtendComponent-bb98e568-B7LlULaY.js";import it from"./OnlineQueryForm-9248341f-Do5iwEgi.js";import at from"./SuperQuery-46032e66-Dg-mJkkb.js";import{u as lt}from"./useOnlinePopEvent-687070b7-B8lc-COq.js";import{t as pt}from"./constant-fa63bd66-Ddbq-fz2.js";import"./index-Diw57m_E.js";import"./OnlineForm-58282699-DaLYL1I2.js";import"./index-L3cSIXth.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useCustomHook-acb00837-B7NPzH0H.js";import"./OnlineForm.vue_vue_type_style_index_0_scoped_3f26e7bd_lang-4ed993c7-l0sNRNKZ.js";import"./OnlineTabForm-1940e88b-DH6ovygw.js";import"./CommentPanel-HxTGfoA9.js";import"./OnlineTabFormDetail-2aa67564-BBsdyGC6.js";import"./DetailForm-c592b8d8-BIx7KBmJ.js";import"./index-mbACBRQ9.js";import"./OnlineSubFormDetail-8be879b9-LBHMpLKz.js";import"./cgformState-d9f8ec42-C8rx7JjX.js";import{Q as mt}from"./componentMap-Bkie1n3v.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./SuperQueryValComponent.vue_vue_type_script_lang-8fe34917-DN8CXDRQ.js";import st from"./BasicTable-xCEZpGLb.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./JUpload-CRos0F1P.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./injectionKey-DPVn4AgL.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-BLwcuZxD.js";var q=(F,x,a)=>new Promise((u,w)=>{var k=m=>{try{I(a.next(m))}catch(S){w(S)}},d=m=>{try{I(a.throw(m))}catch(S){w(S)}},I=m=>m.done?u(m.value):Promise.resolve(m.value).then(k,d);I((a=a.apply(F,x)).next())});const ct={class:"p-2"},ut={key:0,style:{"font-size":"12px","font-style":"italic"}},dt={key:0,style:{"font-size":"12px","font-style":"italic"}},gt=["src","onClick"],ft=["innerHTML","onClick"],yt=["innerHTML"],Ct=["title"],Yo=Oe({__name:"OnlCgformTabList",setup(F){const{createMessage:x}=Ge(),{ID:a,onlineTableContext:u,onlineQueryFormOuter:w,loading:k,reload:d,dataSource:I,pagination:m,handleSpecialConfig:S,getColumnList:J,handleChangeInTable:A,loadData:K,superQueryButtonRef:_,superQueryStatus:V,handleSuperQuery:j,onlineExtConfigJson:E,handleFormConfig:$,registerCustomModal:U,tableReloading:v}=Ze();if(!a.value)throw x.warning("地址错误, 配置ID不存在!"),new Error("地址错误, 配置ID不存在!");let{initCgEnhanceJs:G}=tt(u);const{buttonSwitch:h,cgBIBtnMap:r,getQueryButtonCfg:W,getResetButtonCfg:X,getFormConfirmButtonCfg:Y,cgTopButtonList:T,importUrl:Z,registerModal:ee,handleAdd:te,handleBatchDelete:oe,registerImportModal:ne,onImportExcel:re,onExportExcel:ie,cgButtonJsHandler:ae,cgButtonActionHandler:le,getDropDownActions:pe,getActions:me,initButtonList:se,initButtonSwitch:ce,registerDetailModal:ue,registerBpmModal:ht}=et(u,E),M=Qe(!1);function de(){return q(this,null,function*(){try{M.value=!0,yield ie()}finally{setTimeout(()=>M.value=!1,1500)}})}const{columns:ge,actionColumn:fe,selectedKeys:ye,rowSelection:Ce,enableScrollBar:he,tableScroll:be,downloadRowFile:ke,getImgView:Ie,getPcaText:H,getFormatDate:Se,handleColumnResult:Be,hrefComponent:R,viewOnlineCellImage:we,hrefMainTableId:Re,registerOnlineHrefModal:xe,registerPopModal:_e,openPopModal:ve,onlinePopModalRef:Te,popTableId:L,handleClickFieldHref:Me}=ot(u,E);ze(a,()=>{De()},{immediate:!0});function De(){return q(this,null,function*(){k.value=!0;let n=yield J(pt);Pe(n),yield K(),k.value=!1,u.execButtonEnhance("setup")})}function Pe(n){let g=G(n.enhanceJs);u.EnhanceJS=g,se(n.cgButtonList),ce(n.hideColumns),Be(n),S(n)}function qe(n){u.queryParam=n,d({mode:"search"})}function Fe(n){return q(this,null,function*(){yield nt(_),_.value.init(n)})}function Ee(n){L.value=n.id;let g={title:n.describe};n.record&&n.record.id&&(g.record=n.record,g.isUpdate=!0),ve(!0,g)}return lt(Ee),(n,g)=>{const He=D("a-skeleton"),C=D("a-button"),Le=D("a-modal");return o(),f("div",ct,[e(v)?(o(),l(He,{key:0,active:""})):s("",!0),N(c(it,{ref_key:"onlineQueryFormOuter",ref:w,id:e(a),queryBtnCfg:e(W),resetBtnCfg:e(X),onSearch:qe,onLoaded:Fe},null,8,["id","queryBtnCfg","resetBtnCfg"]),[[O,!e(v)]]),e(v)?s("",!0):(o(),l(e(st),{key:1,ref:"onlineTable",rowKey:"jeecg_row_key",canResize:!0,bordered:!0,showIndexColumn:!1,loading:e(k),columns:e(ge),dataSource:e(I),pagination:e(m),rowSelection:e(Ce),actionColumn:e(fe),showTableSetting:!0,clickToRowSelect:!1,scroll:e(be),onTableRedo:e(d),class:Je({"j-table-force-nowrap":e(he)}),onChange:e(A)},{tableTitle:i(()=>[e(h).add&&e(r).add.enabled?(o(),l(C,{key:0,type:"primary",preIcon:e(r).add.buttonIcon,onClick:e(te)},{default:i(()=>[b("span",null,y(e(r).add.buttonName),1)]),_:1},8,["preIcon","onClick"])):s("",!0),e(h).import&&e(r).import.enabled?(o(),l(C,{key:1,type:"primary",preIcon:e(r).import.buttonIcon,onClick:e(re)},{default:i(()=>[b("span",null,y(e(r).import.buttonName),1)]),_:1},8,["preIcon","onClick"])):s("",!0),e(h).export&&e(r).export.enabled?(o(),l(C,{key:2,type:"primary",preIcon:e(r).export.buttonIcon,loading:M.value,onClick:de},{default:i(()=>[b("span",null,y(e(r).export.buttonName),1)]),_:1},8,["preIcon","loading"])):s("",!0),e(T)&&e(T).length>0?(o(!0),f(Q,{key:3},Ae(e(T),(t,p)=>(o(),f(Q,null,[t.optType=="js"?(o(),l(C,{key:"cgbtn"+p,onClick:B=>e(ae)(t.buttonCode),type:"primary",preIcon:t.buttonIcon?"ant-design:"+t.buttonIcon:""},{default:i(()=>[P(y(t.buttonName),1)]),_:2},1032,["onClick","preIcon"])):t.optType=="action"?(o(),l(C,{key:"cgbtn"+p,onClick:B=>e(le)(t.buttonCode),type:"primary",preIcon:t.buttonIcon?"ant-design:"+t.buttonIcon:""},{default:i(()=>[P(y(t.buttonName),1)]),_:2},1032,["onClick","preIcon"])):s("",!0)],64))),256)):s("",!0),e(h).batch_delete&&e(r).batch_delete.enabled?N((o(),l(C,{key:4,preIcon:e(r).batch_delete.buttonIcon,onClick:e(oe)},{default:i(()=>[b("span",null,y(e(r).batch_delete.buttonName),1)]),_:1},8,["preIcon","onClick"])),[[O,e(ye).length>0]]):s("",!0),e(h).super_query&&e(r).super_query.enabled?(o(),l(at,{key:5,ref_key:"superQueryButtonRef",ref:_,online:"",status:e(V),queryBtnCfg:e(r).super_query,onSearch:e(j)},null,8,["status","queryBtnCfg","onSearch"])):s("",!0)]),fileSlot:i(({text:t,record:p,column:B})=>[t?(o(),l(C,{key:1,ghost:!0,type:"primary",preIcon:"ant-design:download",size:"small",onClick:Ne=>e(ke)(t,p,B,e(a))},{default:i(()=>g[0]||(g[0]=[P(" 下载 ")])),_:2},1032,["onClick"])):(o(),f("span",ut,"无文件"))]),imgSlot:i(({text:t})=>[t?(o(),f("img",{key:1,src:e(Ie)(t),alt:"图片不存在",class:"online-cell-image",onClick:p=>e(we)(t)},null,8,gt)):(o(),f("span",dt,"无图片"))]),htmlSlot:i(({text:t,column:p,record:B})=>[p.fieldHref?(o(),f("a",{key:0,innerHTML:t,onClick:Ne=>e(Me)(p.fieldHref,B)},null,8,ft)):(o(),f("div",{key:1,innerHTML:t},null,8,yt))]),pcaSlot:i(({text:t})=>[b("div",{title:e(H)(t)},y(e(H)(t)),9,Ct)]),dateSlot:i(({text:t,column:p})=>[b("span",null,y(e(Se)(t,p)),1)]),action:i(({record:t})=>[c(e(mt),{actions:e(me)(t),dropDownActions:e(pe)(t)},null,8,["actions","dropDownActions"])]),_:1},8,["loading","columns","dataSource","pagination","rowSelection","actionColumn","scroll","onTableRedo","class","onChange"])),c(Xe,{onRegister:e(ee),id:e(a),cgBIBtnMap:e(r),buttonSwitch:e(h),confirmBtnCfg:e(Y),onSuccess:e(d),onFormConfig:e($)},null,8,["onRegister","id","cgBIBtnMap","buttonSwitch","confirmBtnCfg","onSuccess","onFormConfig"]),c(z,{id:e(a),onRegister:e(ue)},null,8,["id","onRegister"]),c(Ye,{onRegister:e(ne),url:e(Z)(),onOk:e(d),online:""},null,8,["onRegister","url","onOk"]),c(Le,Ke(e(R).model,Ve(e(R).on)),{default:i(()=>[(o(),l(je(e(R).is),$e(Ue(e(R).params)),null,16))]),_:1},16),c(We,{onRegister:e(U),onSuccess:e(d)},null,8,["onRegister","onSuccess"]),c(z,{id:e(Re),onRegister:e(xe),defaultFullscreen:!1},null,8,["id","onRegister"]),c(rt,{ref_key:"onlinePopModalRef",ref:Te,id:e(L),onRegister:e(_e),onSuccess:e(d),request:"",topTip:""},null,8,["id","onRegister","onSuccess"])])}}});export{Yo as default};
