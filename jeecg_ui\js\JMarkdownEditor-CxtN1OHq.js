import{d as l,e as u,w as f,n as g,ag as h,aB as b,ar as k,aE as w}from"./vue-vendor-dy9k-Yad.js";import{M as C}from"./index-mbACBRQ9.js";import{H as p,a as _}from"./index-CCWaWN5g.js";import{a5 as M}from"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const v=l({name:"JMarkdownEditor",inheritAttrs:!1,components:{MarkDown:C},props:{value:p.string.def(""),disabled:p.bool.def(!1)},emits:["change","update:value"],setup(o,{emit:r,attrs:s}){let a=null,t=null;const i=u(()=>Object.assign({},o,s)),e=M.useInjectFormItemContext();function m(n){a=n,t=a.getVditor(),f(()=>o.disabled,c=>c?t.disabled():t.enable(),{immediate:!0})}function d(n){r("change",n),r("update:value",n),g(()=>{e==null||e.onFieldChange()})}return{bindProps:i,onChange:d,onGetVditor:m}}});function G(o,r,s,a,t,i){const e=h("MarkDown");return k(),b(e,w(o.bindProps,{onChange:o.onChange,onGet:o.onGetVditor}),null,16,["onChange","onGet"])}const O=_(v,[["render",G]]);export{O as default};
