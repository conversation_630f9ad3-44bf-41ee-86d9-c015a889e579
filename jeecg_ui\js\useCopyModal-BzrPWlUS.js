var C=Object.defineProperty,k=Object.defineProperties;var T=Object.getOwnPropertyDescriptors;var i=Object.getOwnPropertySymbols;var x=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var p=(e,t,r)=>t in e?C(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,a=(e,t)=>{for(var r in t||(t={}))x.call(t,r)&&p(e,r,t[r]);if(i)for(var r of i(t))P.call(t,r)&&p(e,r,t[r]);return e},c=(e,t)=>k(e,T(t));import{u as w,H as b,w as B}from"./vue-vendor-dy9k-Yad.js";import{C as h}from"./clipboard-Bi9yotgY.js";import{u as M}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const u="copy-this-text",f="data-clipboard-text";function E(){return{createCopyModal:g}}const{createMessage:y,createConfirm:O}=M();function g(e){var r,n,l,m,d;let t=O(c(a({},e),{iconType:(r=e.iconType)!=null?r:"info",width:(n=e.width)!=null?n:500,title:(l=e.title)!=null?l:"复制",maskClosable:(m=e.maskClosable)!=null?m:!0,okText:(d=e.okText)!=null?d:"复制",okButtonProps:c(a({},e.okButtonProps),{class:u,[f]:w(e.copyText)}),onOk(){return new Promise(o=>{const s=new h("."+u);s.on("success",()=>{s.destroy(),y.success("复制成功"),o()}),s.on("error",()=>{y.error("该浏览器不支持自动复制"),s.destroy(),o()})})}}));return b(e.copyText)&&B(e.copyText,o=>{t.update({okButtonProps:c(a({},e.okButtonProps),{class:u,[f]:o})})}),t}export{E as useCopyModal};
