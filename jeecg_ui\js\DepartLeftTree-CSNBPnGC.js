var u=(r,m,c)=>new Promise((x,y)=>{var o=s=>{try{a(c.next(s))}catch(l){y(l)}},n=s=>{try{a(c.throw(s))}catch(l){y(l)}},a=s=>s.done?x(s.value):Promise.resolve(s.value).then(o,n);a((c=c.apply(r,m)).next())});import{d as E,c as P,f as i,ag as d,aB as h,ar as v,aD as w,k as C,aq as N,ah as V,F as z,n as K}from"./vue-vendor-dy9k-Yad.js";import{j as D}from"./index-CCWaWN5g.js";import{s as H}from"./depart.user.api-D_abnxSU.js";const L=r=>D.get({url:"/sys/sysDepart/queryDepartTreeSync",params:r}),J=r=>D.get({url:"/sys/user/queryByOrgCodeForAddressList",params:r}),Q=r=>D.get({url:"/sys/position/list",params:r}),I=E({__name:"DepartLeftTree",emits:["select","rootTreeData"],setup(r,{expose:m,emit:c}){const x=P("prefixCls"),y=c,o=i(!1),n=i([]),a=i([]),s=i([]),l=i(!1),S=i(null),q=i("");function _(){return u(this,null,function*(){try{o.value=!0,n.value=[];const e=yield L();Array.isArray(e)&&(n.value=e),a.value.length===0&&T()}finally{o.value=!1}})}_();function B(e){return u(this,null,function*(){try{const t=yield L({pid:e.dataRef.id});if(t.length==0)e.dataRef.isLeaf=!0;else if(e.dataRef.children=t,a.value.length>0){let f=[];for(let p of a.value)t.findIndex(g=>g.id===p)!==-1&&f.push(p);f.length>0&&(a.value=[...a.value])}n.value=[...n.value]}catch(t){}return Promise.resolve()})}function T(){let e=n.value[0];e&&(e.isLeaf||(a.value=[e.key]),b())}function b(){return u(this,null,function*(){yield K(),l.value=!0,yield K(),l.value=!1})}function k(e,t){s.value=[e],t&&(S.value=t,y("select",t))}function R(e){return u(this,null,function*(){if(e)try{o.value=!0,n.value=[];let t=yield H({keyWord:e});Array.isArray(t)&&(n.value=t),T()}finally{o.value=!1}else _();q.value=e})}function j(e,t){e.length>0&&s.value[0]!==e[0]?k(e[0],t.selectedNodes[0]):k(s.value[0])}return m({loadRootTreeData:_}),(e,t)=>{const f=d("a-input-search"),p=d("a-tree"),g=d("a-empty"),A=d("a-spin"),F=d("a-card");return v(),h(F,{bordered:!1,style:{height:"100%"}},{default:w(()=>[C(A,{spinning:o.value},{default:w(()=>[C(f,{placeholder:"按部门名称搜索…",style:{"margin-bottom":"10px"},onSearch:R,allowClear:""}),n.value.length>0?(v(),N(z,{key:0},[l.value?V("",!0):(v(),h(p,{key:0,showLine:"",clickRowToExpand:!1,treeData:n.value,selectedKeys:s.value,"load-data":B,expandedKeys:a.value,"onUpdate:expandedKeys":t[0]||(t[0]=O=>a.value=O),onSelect:j},null,8,["treeData","selectedKeys","expandedKeys"]))],64)):(v(),h(g,{key:1,description:"暂无数据"}))]),_:1},8,["spinning"])]),_:1})}}}),X=Object.freeze(Object.defineProperty({__proto__:null,default:I},Symbol.toStringTag,{value:"Module"}));export{X as D,I as _,J as l,Q as p};
