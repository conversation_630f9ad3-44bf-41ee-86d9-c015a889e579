var g=(t,o,r)=>new Promise((d,u)=>{var M=a=>{try{i(r.next(a))}catch(s){u(s)}},l=a=>{try{i(r.throw(a))}catch(s){u(s)}},i=a=>a.done?d(a.value):Promise.resolve(a.value).then(M,l);i((r=r.apply(t,o)).next())});import{d as j,e as D,r as F,f as $,ag as c,aB as q,ar as b,aD as m,k,G as I,aq as _,ah as B,F as U,au as C}from"./vue-vendor-dy9k-Yad.js";import{B as L}from"./index-Diw57m_E.js";import"./index-BkGZ5fiW.js";import{useListPage as V}from"./useListPage-Soxgnx9a.js";import{g as v}from"./tenant.data-BEsk-IZ-.js";import{q as K,t as z,u as E}from"./tenant.api-CTNrRQ_d.js";import G from"./TenantUserSelectModal-l6S-j4jy.js";import{ac as Q,ad as H,u as J,a as W}from"./index-CCWaWN5g.js";import{Q as X}from"./componentMap-Bkie1n3v.js";import Y from"./BasicTable-xCEZpGLb.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./renderUtils-D7XVOFwj.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";const Z=j({name:"TenantPackUserModal",components:{BasicModal:L,BasicTable:Y,TableAction:X,TenantUserSelectModal:G},setup(){const t=D(()=>o.tenantId),o=F({}),[r,{setModalProps:d,closeModal:u}]=Q(e=>g(null,null,function*(){d({confirmLoading:!1,showCancelBtn:!0,showOkBtn:!1}),Object.assign(o,e.record),yield p()})),{createMessage:M}=J(),l=$("用户"),{tableContext:i}=V({tableProps:{api:E,immediate:!1,columns:v,canResize:!1,useSearchForm:!1,beforeFetch:e=>(e.tenantId=o.tenantId,e.packId=o.id,e.status=1,e),actionColumn:{width:120,fixed:"right"}}}),[a,{openModal:s,closeModal:T}]=H(),[n,{reload:p},{rowSelection:N,selectedRowKeys:ee}]=i;function y(e){return e.join(",")}function S(e){return[{label:"移除",popConfirm:{title:"是否确认移除",confirm:P.bind(null,e)}}]}function P(e){return g(this,null,function*(){let f={packId:e.packId,packName:e.packName,tenantId:o.tenantId,userId:e.id,realname:e.realname};yield K(f),yield p()})}function R(){s(!0,{list:[]})}function A(e){return g(this,null,function*(){if(e&&e.length>0){let f=[],w=[];for(let h of e)f.push(h.realname),w.push(h.id);let O={packId:o.id,packName:o.packName,tenantId:o.tenantId,userId:w.join(","),realname:f.join(",")};yield z(O),yield p()}T()})}return{title:l,registerModal:r,registerTable:n,rowSelection:N,getName:y,getTableAction:S,registerUserModal:a,addUser:R,onSelected:A,getTenantId:t}}});function x(t,o,r,d,u,M){const l=c("a-button"),i=c("TableAction"),a=c("BasicTable"),s=c("tenant-user-select-modal"),T=c("BasicModal");return b(),q(T,{onRegister:t.registerModal,destroyOnClose:"",title:t.title,width:1e3,footer:null},{default:m(()=>[k(a,{onRegister:t.registerTable,rowSelection:t.rowSelection},{departNames:m(({text:n,record:p})=>[n&&n.length>0?(b(),_(U,{key:0},[I(C(t.getName(n)),1)],64)):B("",!0)]),positionNames:m(({text:n,record:p})=>[n&&n.length>0?(b(),_(U,{key:0},[I(C(t.getName(n)),1)],64)):B("",!0)]),tableTitle:m(()=>[k(l,{preIcon:"ant-design:usergroup-add-outlined",type:"primary",onClick:t.addUser},{default:m(()=>o[0]||(o[0]=[I("邀请成员")])),_:1,__:[0]},8,["onClick"])]),action:m(({record:n})=>[k(i,{actions:t.getTableAction(n)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"]),k(s,{multi:!0,onRegister:t.registerUserModal,onOnSelect:t.onSelected,tenantId:t.getTenantId},null,8,["onRegister","onOnSelect","tenantId"])]),_:1},8,["onRegister","title"])}const dt=W(Z,[["render",x]]);export{dt as default};
