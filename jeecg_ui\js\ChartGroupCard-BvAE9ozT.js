import{d as w,ag as G,aB as a,ar as t,aD as c,at as o,au as i,aG as _,f as k,e as D,aq as h,F as V,aC as x,as as z,aH as A,ah as s,G as $,u as y,k as b}from"./vue-vendor-dy9k-Yad.js";import{a as F,D as q,c as E}from"./index-CCWaWN5g.js";import{ah as B}from"./antd-vue-vendor-me9YkNVC.js";import{T as m,S as g}from"./SingleLine-kPL5Z-yi.js";import{B as S}from"./Bar-Tb_Evzfu.js";import{b as H,c as I}from"./data-BpJ37qIE.js";import"./vxe-table-vendor-B22HppNm.js";import"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const P={class:"chart-card-header"},R={class:"meta"},j={class:"chart-card-title"},J={class:"chart-card-action"},K={class:"total"},M={class:"chart-card-content"},O={class:"content-fix"},Q={class:"chart-card-footer"},U={class:"field"},W=w({__name:"ChartCard",props:{title:{type:String,default:""},total:{type:[Number,String],default:""},loading:{type:Boolean,default:!1}},setup(r){return(u,C)=>{const n=G("a-card");return t(),a(n,{loading:r.loading,"body-style":{padding:"20px 24px 8px"},bordered:!1},{default:c(()=>[o("div",P,[o("div",R,[o("span",j,i(r.title),1),o("span",J,[_(u.$slots,"action",{},void 0,!0)])]),o("div",K,[o("span",null,i(r.total),1)])]),o("div",M,[o("div",O,[_(u.$slots,"default",{},void 0,!0)])]),o("div",Q,[o("div",U,[_(u.$slots,"footer",{},void 0,!0)])])]),_:3},8,["loading"])}}}),X=F(W,[["__scopeId","data-v-e7e56aab"]]),Y={class:"md:flex"},Z={key:0},tt={key:1},et={key:0},ht=w({__name:"ChartGroupCard",props:{loading:{type:Boolean},type:{type:String,default:"chart"}},setup(r){const{getThemeColor:u}=q(),C=r,n=k({xAxis:{show:!1,boundaryGap:!1},yAxis:{show:!1,boundaryGap:!1,max:220}}),d=k([{name:"1月",value:50},{name:"2月",value:100},{name:"3月",value:150},{name:"4月",value:40},{name:"5月",value:110},{name:"6月",value:120}]),p=D(()=>u.value),L=D(()=>C.type==="dbc"?H:I);function N(f,v){return v===0?`￥${f}`:v===3?`${f}%`:f}return(f,v)=>{const T=G("a-tooltip");return t(),h("div",Y,[(t(!0),h(V,null,x(L.value,(l,e)=>(t(),a(X,{key:l.title,loading:r.loading,title:l.title,total:N(l.total,e),class:z(["md:w-1/4 w-full !md:mt-0 !mt-4",[e+1<4&&"!md:mr-4"]])},A({action:c(()=>[b(T,{title:"指标说明"},{default:c(()=>[b(y(E),{icon:l.icon,size:20},null,8,["icon"])]),_:2},1024)]),default:c(()=>[r.type==="chart"?(t(),h("div",Z,[e===0?(t(),a(m,{key:0,term:"周同比",percentage:12})):s("",!0),e===0?(t(),a(m,{key:1,term:"日同比",percentage:11,type:!1})):s("",!0),e===1?(t(),a(g,{key:2,option:n.value,chartData:d.value,seriesColor:p.value,height:"50px"},null,8,["option","chartData","seriesColor"])):s("",!0),e===2?(t(),a(S,{key:3,option:n.value,chartData:d.value,seriesColor:p.value,height:"50px"},null,8,["option","chartData","seriesColor"])):s("",!0),e===3?(t(),a(y(B),{key:4,percent:78,"show-info":!1})):s("",!0)])):(t(),h("div",tt,[e===0?(t(),a(g,{key:0,seriesColor:p.value,option:n.value,chartData:d.value,height:"50px"},null,8,["seriesColor","option","chartData"])):s("",!0),e===1?(t(),a(g,{key:1,seriesColor:p.value,option:n.value,chartData:d.value,height:"50px"},null,8,["seriesColor","option","chartData"])):s("",!0),e===2?(t(),a(S,{key:2,seriesColor:p.value,option:n.value,chartData:d.value,height:"50px"},null,8,["seriesColor","option","chartData"])):s("",!0),e===3?(t(),a(y(B),{key:3,percent:78,"show-info":!1})):s("",!0)]))]),_:2},[r.type==="chart"?{name:"footer",fn:c(()=>[e!==3?(t(),h("span",et,[$(i(l.footer),1),o("span",null,i(l.value),1)])):s("",!0),e===3?(t(),a(m,{key:1,term:"周同比",percentage:12})):s("",!0),e===3?(t(),a(m,{key:2,term:"日同比",percentage:11,type:!1})):s("",!0)]),key:"0"}:{name:"footer",fn:c(()=>[o("span",null,[$(i(l.footer),1),o("span",null,i(l.value),1)])]),key:"1"}]),1032,["loading","title","total","class"]))),128))])}}});export{ht as default};
