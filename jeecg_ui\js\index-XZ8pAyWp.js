import{d as s,e as n,aB as a,ar as r,aD as c,ah as i,m as p}from"./vue-vendor-dy9k-Yad.js";import u from"./LockPage-DpLmyeWL.js";import{u as f}from"./lock-CRalNsZJ.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./header-OZa5fSDc.js";const B=s({__name:"index",setup(d){const o=f(),m=n(()=>{var e,t;return(t=(e=o==null?void 0:o.getLockInfo)==null?void 0:e.isLock)!=null?t:!1});return(e,t)=>(r(),a(p,{name:"fade-bottom",mode:"out-in"},{default:c(()=>[m.value?(r(),a(u,{key:0})):i("",!0)]),_:1}))}});export{B as default};
