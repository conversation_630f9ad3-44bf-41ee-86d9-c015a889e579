import{d as u,aB as f,w as v,n as h,ar as a,u as t,aE as g,aD as k,aq as s,ah as B,F as L,aC as x,at as i,au as y}from"./vue-vendor-dy9k-Yad.js";import{v as C}from"./useNode-08a107c9-1Tvy8vWu.js";import"./NodeStyle-59980363-DzErCCzZ.js";import"./index-9c51646a-BviQbLw-.js";import"./antd-vue-vendor-me9YkNVC.js";import{x as _}from"./NodeContainer.vue_vue_type_style_index_0_lang-60bcf3b8-DFhy0o3x.js";import"./NodeKV.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const b={key:0,class:"classifier-node-content"},N={class:"case-item"},j={class:"case-header"},R=u({__name:"ClassifierNode",props:{node:{type:Object,required:!0},graph:{type:Object,required:!0}},setup(c){const o=c,{$node:r,updateHeight:l,containerRef:d,containerProps:p}=C(o,{onUpdateNode(e){e.caseList=o.node.$caseList}});return v(()=>{var e;return(e=r.value.caseList)==null?void 0:e.length},()=>h(()=>l())),(e,q)=>(a(),f(t(_),g({ref_key:"containerRef",ref:d},t(p)),{default:k(()=>{var n;return[(n=t(r).caseList)!=null&&n.length?(a(),s("div",b,[(a(!0),s(L,null,x(t(r).caseList,m=>(a(),s("div",N,[i("div",j,[i("div",null,y(m.label),1)])]))),256))])):B("",!0)]}),_:1},16))}});export{R as D};
