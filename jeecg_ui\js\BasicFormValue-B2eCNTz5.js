var p=(F,n,o)=>new Promise((l,m)=>{var u=t=>{try{i(o.next(t))}catch(e){m(e)}},a=t=>{try{i(o.throw(t))}catch(e){m(e)}},i=t=>t.done?l(t.value):Promise.resolve(t.value).then(u,a);i((o=o.apply(F,n)).next())});import{d as g,ag as x,aq as B,ar as b,at as k,k as s,aD as f,G as _,u as V,F as v}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as w}from"./useForm-CgkFTrrO.js";import{B as C}from"./BasicForm-DBcXiHk0.js";import{a as y}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const q={style:{margin:"20px auto","text-align":"center"}},I=g({__name:"BasicFormValue",setup(F){const n=[{field:"visitor",label:"来访人员",component:"Input",required:!0},{field:"accessed",label:"来访日期",component:"DatePicker",required:!0},{field:"phone",label:"来访人手机号",component:"Input",required:!0}],[o,{getFieldsValue:l,setFieldsValue:m,resetFields:u,validate:a}]=w({schemas:n,showActionButtonGroup:!1,labelWidth:"150px",autoFocusFirstItem:!0});function i(){return p(this,null,function*(){let c=yield l();c=yield a(),c=yield a(["visitor"])})}function t(){return p(this,null,function*(){yield u()})}function e(){return p(this,null,function*(){yield m({visitor:"李四"})})}return(c,r)=>{const d=x("a-button");return b(),B(v,null,[k("div",q,[s(d,{onClick:i,class:"mr-2"},{default:f(()=>r[0]||(r[0]=[_(" 获取表单值 ")])),_:1,__:[0]}),s(d,{onClick:t,class:"mr-2"},{default:f(()=>r[1]||(r[1]=[_(" 清空表单值 ")])),_:1,__:[1]}),s(d,{onClick:e,class:"mr-2"},{default:f(()=>r[2]||(r[2]=[_(" 更新表单值 ")])),_:1,__:[2]})]),s(V(C),{onRegister:V(o),style:{"margin-top":"20px"}},null,8,["onRegister"])],64)}}}),ht=y(I,[["__scopeId","data-v-1e685b17"]]);export{ht as default};
