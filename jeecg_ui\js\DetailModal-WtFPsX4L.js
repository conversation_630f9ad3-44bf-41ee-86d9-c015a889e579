import{d as o,aB as s,ar as i,aD as m,at as p,aE as c,u as a}from"./vue-vendor-dy9k-Yad.js";import{B as n}from"./index-Diw57m_E.js";import{H as l,ac as f,a as d}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const _=["src"],u=o({__name:"DetailModal",props:{frameSrc:l.string.def("")},setup(t){const[r]=f();return(e,B)=>(i(),s(a(n),c(e.$attrs,{onRegister:a(r),title:"查看详情",showCancelBtn:!1,showOkBtn:!1,maxHeight:500}),{default:m(()=>[p("iframe",{src:t.frameSrc,class:"detail-iframe"},null,8,_)]),_:1},16,["onRegister"]))}}),E=d(u,[["__scopeId","data-v-67b80ca1"]]);export{E as default};
