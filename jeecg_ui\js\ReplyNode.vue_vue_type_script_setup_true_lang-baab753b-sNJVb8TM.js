import{d as r,aB as o,ar as p,u as a,aJ as t,aK as s}from"./vue-vendor-dy9k-Yad.js";import"./NodeStyle-59980363-DzErCCzZ.js";import"./index-9c51646a-BviQbLw-.js";import"./antd-vue-vendor-me9YkNVC.js";import{x as i}from"./NodeContainer.vue_vue_type_style_index_0_lang-60bcf3b8-DFhy0o3x.js";import"./NodeKV.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const x=r({__name:"ReplyNode",props:{node:{type:Object,required:!0},graph:{type:Object,required:!0}},setup(m){return(e,n)=>(p(),o(a(i),t(s(e.$props)),null,16))}});export{x as y};
