import{d as Pt,ag as pt,aB as qt,ar as Bt,aD as ht,k as Ht,G as Ot}from"./vue-vendor-dy9k-Yad.js";import{P as Rt}from"./index-CtJ0w2CP.js";import{F as Mt,a as Dt}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";/*!
 * Intro.js v7.2.0
 * https://introjs.com
 *
 * Copyright (C) 2012-2023 A<PERSON><PERSON> (@afshinmeh).
 * https://introjs.com
 *
 * Date: Mon, 14 Aug 2023 19:47:14 GMT
 */function U(t){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},U(t)}function f(t,e,n,o){return new(n||(n=Promise))(function(i,r){function s(p){try{c(o.next(p))}catch(l){r(l)}}function a(p){try{c(o.throw(p))}catch(l){r(l)}}function c(p){var l;p.done?i(p.value):(l=p.value,l instanceof n?l:new n(function(u){u(l)})).then(s,a)}c((o=o.apply(t,e||[])).next())})}function m(t,e){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(r[Symbol.iterator]=function(){return this}),r;function a(c){return function(p){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;r&&(r=0,l[0]&&(s=0)),s;)try{if(n=1,o&&(i=2&l[0]?o.return:l[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,l[1])).done)return i;switch(o=0,i&&(l=[2&l[0],i.value]),l[0]){case 0:case 1:i=l;break;case 4:return s.label++,{value:l[1],done:!1};case 5:s.label++,o=l[1],l=[0];continue;case 7:l=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!((i=i.length>0&&i[i.length-1])||l[0]!==6&&l[0]!==2)){s=0;continue}if(l[0]===3&&(!i||l[1]>i[0]&&l[1]<i[3])){s.label=l[1];break}if(l[0]===6&&s.label<i[1]){s.label=i[1],i=l;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(l);break}i[2]&&s.ops.pop(),s.trys.pop();continue}l=e.call(t,s)}catch(u){l=[6,u],o=0}finally{n=i=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([c,p])}}}function dt(t,e,n){var o,i=((o={})[t]=e,o.path="/",o.expires=void 0,o);if(n){var r=new Date;r.setTime(r.getTime()+24*n*60*60*1e3),i.expires=r.toUTCString()}var s=[];for(var a in i)s.push("".concat(a,"=").concat(i[a]));return document.cookie=s.join("; "),yt(t)}function yt(t){return(e={},document.cookie.split(";").forEach(function(n){var o=n.split("="),i=o[0],r=o[1];e[i.trim()]=r}),e)[t];var e}function Vt(t,e){e?dt(t._options.dontShowAgainCookie,"true",t._options.dontShowAgainCookieDays):dt(t._options.dontShowAgainCookie,"",-1)}var $,et=($={},function(t,e){return e===void 0&&(e="introjs-stamp"),$[e]=$[e]||0,t[e]===void 0&&(t[e]=$[e]++),t[e]}),x=new(function(){function t(){this.events_key="introjs_event"}return t.prototype._id=function(e,n,o){return e+et(n)+(o?"_".concat(et(o)):"")},t.prototype.on=function(e,n,o,i,r){var s=this._id(n,o,i),a=function(c){return o(i||e,c||window.event)};"addEventListener"in e?e.addEventListener(n,a,r):"attachEvent"in e&&e.attachEvent("on".concat(n),a),e[this.events_key]=e[this.events_key]||{},e[this.events_key][s]=a},t.prototype.off=function(e,n,o,i,r){var s=this._id(n,o,i),a=e[this.events_key]&&e[this.events_key][s];a&&("removeEventListener"in e?e.removeEventListener(n,a,r):"detachEvent"in e&&e.detachEvent("on".concat(n),a),e[this.events_key][s]=null)},t}()),g=function(t){return typeof t=="function"};function A(t,e){if(t instanceof SVGElement){var n=t.getAttribute("class")||"";n.match(e)||t.setAttribute("class","".concat(n," ").concat(e))}else if(t.classList!==void 0)for(var o=0,i=e.split(" ");o<i.length;o++){var r=i[o];t.classList.add(r)}else t.className.match(e)||(t.className+=" ".concat(e))}function ot(t,e){var n="";return"currentStyle"in t?n=t.currentStyle[e]:document.defaultView&&document.defaultView.getComputedStyle&&(n=document.defaultView.getComputedStyle(t,null).getPropertyValue(e)),n&&n.toLowerCase?n.toLowerCase():n}function ft(t,e){if(t){var n=function(o){var i=window.getComputedStyle(o),r=i.position==="absolute",s=/(auto|scroll)/;if(i.position==="fixed")return document.body;for(var a=o;a=a.parentElement;)if(i=window.getComputedStyle(a),(!r||i.position!=="static")&&s.test(i.overflow+i.overflowY+i.overflowX))return a;return document.body}(e);n!==document.body&&(n.scrollTop=e.offsetTop-n.offsetTop)}}function it(){if(window.innerWidth!==void 0)return{width:window.innerWidth,height:window.innerHeight};var t=document.documentElement;return{width:t.clientWidth,height:t.clientHeight}}function mt(t,e,n,o,i){var r;if(e!=="off"&&t&&(r=e==="tooltip"?i.getBoundingClientRect():o.getBoundingClientRect(),!function(a){var c=a.getBoundingClientRect();return c.top>=0&&c.left>=0&&c.bottom+80<=window.innerHeight&&c.right<=window.innerWidth}(o))){var s=it().height;r.bottom-(r.bottom-r.top)<0||o.clientHeight>s?window.scrollBy(0,r.top-(s/2-r.height/2)-n):window.scrollBy(0,r.top-(s/2-r.height/2)+n)}}function D(t){t.setAttribute("role","button"),t.tabIndex=0}function Z(t){var e=t.parentElement;return!(!e||e.nodeName==="HTML")&&(ot(t,"position")==="fixed"||Z(e))}function q(t,e){var n=document.body,o=document.documentElement,i=window.pageYOffset||o.scrollTop||n.scrollTop,r=window.pageXOffset||o.scrollLeft||n.scrollLeft;e=e||n;var s=t.getBoundingClientRect(),a=e.getBoundingClientRect(),c=ot(e,"position"),p={width:s.width,height:s.height};return e.tagName.toLowerCase()!=="body"&&c==="relative"||c==="sticky"?Object.assign(p,{top:s.top-a.top,left:s.left-a.left}):Z(t)?Object.assign(p,{top:s.top,left:s.left}):Object.assign(p,{top:s.top+i,left:s.left+r})}function rt(t,e){if(t instanceof SVGElement){var n=t.getAttribute("class")||"";t.setAttribute("class",n.replace(e,"").replace(/^\s+|\s+$/g,""))}else t.className=t.className.replace(e,"").replace(/^\s+|\s+$/g,"")}function E(t,e){var n="";if(t.style.cssText&&(n+=t.style.cssText),typeof e=="string")n+=e;else for(var o in e)n+="".concat(o,":").concat(e[o],";");t.style.cssText=n}function N(t,e,n){if(n&&e){var o=q(e.element,t._targetElement),i=t._options.helperElementPadding;e.element instanceof Element&&Z(e.element)?A(n,"introjs-fixedTooltip"):rt(n,"introjs-fixedTooltip"),e.position==="floating"&&(i=0),E(n,{width:"".concat(o.width+i,"px"),height:"".concat(o.height+i,"px"),top:"".concat(o.top-i/2,"px"),left:"".concat(o.left-i/2,"px")})}}function z(t,e,n,o,i){return t.left+e+n.width>o.width?(i.style.left="".concat(o.width-n.width-t.left,"px"),!1):(i.style.left="".concat(e,"px"),!0)}function G(t,e,n,o){return t.left+t.width-e-n.width<0?(o.style.left="".concat(-t.left,"px"),!1):(o.style.right="".concat(e,"px"),!0)}function j(t,e){t.includes(e)&&t.splice(t.indexOf(e),1)}function Ft(t,e,n,o){var i=t.slice(),r=it(),s=q(n).height+10,a=q(n).width+20,c=e.getBoundingClientRect(),p="floating";if(c.bottom+s>r.height&&j(i,"bottom"),c.top-s<0&&j(i,"top"),c.right+a>r.width&&j(i,"right"),c.left-a<0&&j(i,"left"),o&&(o=o.split("-")[0]),i.length&&(p=i[0],i.includes(o)&&(p=o)),p==="top"||p==="bottom"){var l=void 0,u=[];p==="top"?(l="top-middle-aligned",u=["top-left-aligned","top-middle-aligned","top-right-aligned"]):(l="bottom-middle-aligned",u=["bottom-left-aligned","bottom-middle-aligned","bottom-right-aligned"]),p=function(b,v,y,w){var _=v/2,k=Math.min(y,window.screen.width);return k-b<v&&(j(w,"top-left-aligned"),j(w,"bottom-left-aligned")),(b<_||k-b<_)&&(j(w,"top-middle-aligned"),j(w,"bottom-middle-aligned")),b<v&&(j(w,"top-right-aligned"),j(w,"bottom-right-aligned")),w.length?w[0]:null}(c.left,a,r.width,u)||l}return p}function X(t,e,n,o,i){if(i===void 0&&(i=!1),e){var r,s,a,c,p="";n.style.top="",n.style.right="",n.style.bottom="",n.style.left="",n.style.marginLeft="",n.style.marginTop="",o.style.display="inherit",p=typeof e.tooltipClass=="string"?e.tooltipClass:t._options.tooltipClass,n.className=["introjs-tooltip",p].filter(Boolean).join(" "),n.setAttribute("role","dialog"),(c=e.position)!=="floating"&&t._options.autoPosition&&(c=Ft(t._options.positionPrecedence,e.element,n,c)),s=q(e.element),r=q(n),a=it(),A(n,"introjs-".concat(c));var l=s.width/2-r.width/2;switch(c){case"top-right-aligned":o.className="introjs-arrow bottom-right";var u=0;G(s,u,r,n),n.style.bottom="".concat(s.height+20,"px");break;case"top-middle-aligned":o.className="introjs-arrow bottom-middle",i&&(l+=5),G(s,l,r,n)&&(n.style.right="",z(s,l,r,a,n)),n.style.bottom="".concat(s.height+20,"px");break;case"top-left-aligned":case"top":o.className="introjs-arrow bottom",z(s,i?0:15,r,a,n),n.style.bottom="".concat(s.height+20,"px");break;case"right":n.style.left="".concat(s.width+20,"px"),s.top+r.height>a.height?(o.className="introjs-arrow left-bottom",n.style.top="-".concat(r.height-s.height-20,"px")):o.className="introjs-arrow left";break;case"left":i||t._options.showStepNumbers!==!0||(n.style.top="15px"),s.top+r.height>a.height?(n.style.top="-".concat(r.height-s.height-20,"px"),o.className="introjs-arrow right-bottom"):o.className="introjs-arrow right",n.style.right="".concat(s.width+20,"px");break;case"floating":o.style.display="none",n.style.left="50%",n.style.top="50%",n.style.marginLeft="-".concat(r.width/2,"px"),n.style.marginTop="-".concat(r.height/2,"px");break;case"bottom-right-aligned":o.className="introjs-arrow top-right",G(s,u=0,r,n),n.style.top="".concat(s.height+20,"px");break;case"bottom-middle-aligned":o.className="introjs-arrow top-middle",i&&(l+=5),G(s,l,r,n)&&(n.style.right="",z(s,l,r,a,n)),n.style.top="".concat(s.height+20,"px");break;default:o.className="introjs-arrow top",z(s,0,r,a,n),n.style.top="".concat(s.height+20,"px")}}}function _t(){for(var t=0,e=Array.from(document.querySelectorAll(".introjs-showElement"));t<e.length;t++)rt(e[t],/introjs-[a-zA-Z]+/g)}function d(t,e){var n=document.createElement(t);e=e||{};var o=/^(?:role|data-|aria-)/;for(var i in e){var r=e[i];i==="style"&&typeof r!="function"?E(n,r):typeof r=="string"&&i.match(o)?n.setAttribute(i,r):n[i]=r}return n}function bt(t,e,n){if(n===void 0&&(n=!1),n){var o=e.style.opacity||"1";E(e,{opacity:"0"}),window.setTimeout(function(){E(e,{opacity:o})},10)}t.appendChild(e)}function wt(t,e){return(t+1)/e*100}function Ct(t,e){var n=d("div",{className:"introjs-bullets"});t._options.showBullets===!1&&(n.style.display="none");var o=d("ul");o.setAttribute("role","tablist");for(var i=function(){var p=this.getAttribute("data-step-number");p!=null&&t.goToStep(parseInt(p,10))},r=0;r<t._introItems.length;r++){var s=t._introItems[r].step,a=d("li"),c=d("a");a.setAttribute("role","presentation"),c.setAttribute("role","tab"),c.onclick=i,r===e.step-1&&(c.className="active"),D(c),c.innerHTML="&nbsp;",c.setAttribute("data-step-number",s.toString()),a.appendChild(c),o.appendChild(a)}return n.appendChild(o),n}function St(t,e,n){var o=t.querySelector(".introjs-progress .introjs-progressbar");if(o){var i=wt(e,n);o.style.cssText="width:".concat(i,"%;"),o.setAttribute("aria-valuenow",i.toString())}}function kt(t,e){return f(this,void 0,void 0,function(){var n,o,i,r,s,a,c,p,l,u,b,v,y,w,_,k,L,H,P,V,lt,ct,F,W,Q=this;return m(this,function(O){switch(O.label){case 0:return g(t._introChangeCallback)?[4,t._introChangeCallback.call(t,e.element)]:[3,2];case 1:O.sent(),O.label=2;case 2:return n=document.querySelector(".introjs-helperLayer"),o=document.querySelector(".introjs-tooltipReferenceLayer"),i="introjs-helperLayer",typeof e.highlightClass=="string"&&(i+=" ".concat(e.highlightClass)),typeof t._options.highlightClass=="string"&&(i+=" ".concat(t._options.highlightClass)),n!==null&&o!==null?(c=o.querySelector(".introjs-helperNumberLayer"),p=o.querySelector(".introjs-tooltiptext"),l=o.querySelector(".introjs-tooltip-title"),u=o.querySelector(".introjs-arrow"),b=o.querySelector(".introjs-tooltip"),a=o.querySelector(".introjs-skipbutton"),s=o.querySelector(".introjs-prevbutton"),r=o.querySelector(".introjs-nextbutton"),n.className=i,b.style.opacity="0",b.style.display="none",ft(t._options.scrollToElement,e.element),N(t,e,n),N(t,e,o),_t(),t._lastShowElementTimer&&window.clearTimeout(t._lastShowElementTimer),t._lastShowElementTimer=window.setTimeout(function(){c!==null&&(c.innerHTML="".concat(e.step," ").concat(t._options.stepNumbersOfLabel," ").concat(t._introItems.length)),p.innerHTML=e.intro||"",l.innerHTML=e.title||"",b.style.display="block",X(t,e,b,u),function(h,C,S){if(h){var R=C.querySelector(".introjs-bullets li > a.active"),ut=C.querySelector('.introjs-bullets li > a[data-step-number="'.concat(S.step,'"]'));R&&ut&&(R.className="",ut.className="active")}}(t._options.showBullets,o,e),St(o,t._currentStep,t._introItems.length),b.style.opacity="1",(r!=null&&/introjs-donebutton/gi.test(r.className)||r!=null)&&r.focus(),mt(t._options.scrollToElement,e.scrollTo,t._options.scrollPadding,e.element,p)},350)):(v=d("div",{className:i}),y=d("div",{className:"introjs-tooltipReferenceLayer"}),w=d("div",{className:"introjs-arrow"}),_=d("div",{className:"introjs-tooltip"}),k=d("div",{className:"introjs-tooltiptext"}),L=d("div",{className:"introjs-tooltip-header"}),H=d("h1",{className:"introjs-tooltip-title"}),P=d("div"),E(v,{"box-shadow":"0 0 1px 2px rgba(33, 33, 33, 0.8), rgba(33, 33, 33, ".concat(t._options.overlayOpacity.toString(),") 0 0 0 5000px")}),ft(t._options.scrollToElement,e.element),N(t,e,v),N(t,e,y),bt(t._targetElement,v,!0),bt(t._targetElement,y),k.innerHTML=e.intro,H.innerHTML=e.title,P.className="introjs-tooltipbuttons",t._options.showButtons===!1&&(P.style.display="none"),L.appendChild(H),_.appendChild(L),_.appendChild(k),t._options.dontShowAgain&&(V=d("div",{className:"introjs-dontShowAgain"}),(lt=d("input",{type:"checkbox",id:"introjs-dontShowAgain",name:"introjs-dontShowAgain"})).onchange=function(h){t.setDontShowAgain(h.target.checked)},(ct=d("label",{htmlFor:"introjs-dontShowAgain"})).innerText=t._options.dontShowAgainLabel,V.appendChild(lt),V.appendChild(ct),_.appendChild(V)),_.appendChild(Ct(t,e)),_.appendChild(function(h){var C=d("div");C.className="introjs-progress",h._options.showProgress===!1&&(C.style.display="none");var S=d("div",{className:"introjs-progressbar"});h._options.progressBarAdditionalClass&&(S.className+=" "+h._options.progressBarAdditionalClass);var R=wt(h._currentStep,h._introItems.length);return S.setAttribute("role","progress"),S.setAttribute("aria-valuemin","0"),S.setAttribute("aria-valuemax","100"),S.setAttribute("aria-valuenow",R.toString()),S.style.cssText="width:".concat(R,"%;"),C.appendChild(S),C}(t)),F=d("div"),t._options.showStepNumbers===!0&&(F.className="introjs-helperNumberLayer",F.innerHTML="".concat(e.step," ").concat(t._options.stepNumbersOfLabel," ").concat(t._introItems.length),_.appendChild(F)),_.appendChild(w),y.appendChild(_),(r=d("a")).onclick=function(){return f(Q,void 0,void 0,function(){return m(this,function(h){switch(h.label){case 0:return t._introItems.length-1===t._currentStep?[3,2]:[4,T(t)];case 1:return h.sent(),[3,6];case 2:return/introjs-donebutton/gi.test(r.className)?g(t._introCompleteCallback)?[4,t._introCompleteCallback.call(t,t._currentStep,"done")]:[3,4]:[3,6];case 3:h.sent(),h.label=4;case 4:return[4,I(t,t._targetElement)];case 5:h.sent(),h.label=6;case 6:return[2]}})})},D(r),r.innerHTML=t._options.nextLabel,(s=d("a")).onclick=function(){return f(Q,void 0,void 0,function(){return m(this,function(h){switch(h.label){case 0:return t._currentStep>0?[4,Y(t)]:[3,2];case 1:h.sent(),h.label=2;case 2:return[2]}})})},D(s),s.innerHTML=t._options.prevLabel,D(a=d("a",{className:"introjs-skipbutton"})),a.innerHTML=t._options.skipLabel,a.onclick=function(){return f(Q,void 0,void 0,function(){return m(this,function(h){switch(h.label){case 0:return t._introItems.length-1===t._currentStep&&g(t._introCompleteCallback)?[4,t._introCompleteCallback.call(t,t._currentStep,"skip")]:[3,2];case 1:h.sent(),h.label=2;case 2:return g(t._introSkipCallback)?[4,t._introSkipCallback.call(t,t._currentStep)]:[3,4];case 3:h.sent(),h.label=4;case 4:return[4,I(t,t._targetElement)];case 5:return h.sent(),[2]}})})},L.appendChild(a),t._introItems.length>1&&P.appendChild(s),P.appendChild(r),_.appendChild(P),X(t,e,_,w),mt(t._options.scrollToElement,e.scrollTo,t._options.scrollPadding,e.element,_)),(W=t._targetElement.querySelector(".introjs-disableInteraction"))&&W.parentNode&&W.parentNode.removeChild(W),e.disableInteraction&&function(h,C){var S=document.querySelector(".introjs-disableInteraction");S===null&&(S=d("div",{className:"introjs-disableInteraction"}),h._targetElement.appendChild(S)),N(h,C,S)}(t,e),t._currentStep===0&&t._introItems.length>1?(r!=null&&(r.className="".concat(t._options.buttonClass," introjs-nextbutton"),r.innerHTML=t._options.nextLabel),t._options.hidePrev===!0?(s!=null&&(s.className="".concat(t._options.buttonClass," introjs-prevbutton introjs-hidden")),r!=null&&A(r,"introjs-fullbutton")):s!=null&&(s.className="".concat(t._options.buttonClass," introjs-prevbutton introjs-disabled"))):t._introItems.length-1===t._currentStep||t._introItems.length===1?(s!=null&&(s.className="".concat(t._options.buttonClass," introjs-prevbutton")),t._options.hideNext===!0?(r!=null&&(r.className="".concat(t._options.buttonClass," introjs-nextbutton introjs-hidden")),s!=null&&A(s,"introjs-fullbutton")):r!=null&&(t._options.nextToDone===!0?(r.innerHTML=t._options.doneLabel,A(r,"".concat(t._options.buttonClass," introjs-nextbutton introjs-donebutton"))):r.className="".concat(t._options.buttonClass," introjs-nextbutton introjs-disabled"))):(s!=null&&(s.className="".concat(t._options.buttonClass," introjs-prevbutton")),r!=null&&(r.className="".concat(t._options.buttonClass," introjs-nextbutton"),r.innerHTML=t._options.nextLabel)),s!=null&&s.setAttribute("role","button"),r!=null&&r.setAttribute("role","button"),a!=null&&a.setAttribute("role","button"),r!=null&&r.focus(),function(h){A(h,"introjs-showElement");var C=ot(h,"position");C!=="absolute"&&C!=="relative"&&C!=="sticky"&&C!=="fixed"&&A(h,"introjs-relativePosition")}(e.element),g(t._introAfterChangeCallback)?[4,t._introAfterChangeCallback.call(t,e.element)]:[3,4];case 3:O.sent(),O.label=4;case 4:return[2]}})})}function Wt(t,e){return f(this,void 0,void 0,function(){return m(this,function(n){switch(n.label){case 0:return t._currentStep=e-2,t._introItems===void 0?[3,2]:[4,T(t)];case 1:n.sent(),n.label=2;case 2:return[2]}})})}function $t(t,e){return f(this,void 0,void 0,function(){return m(this,function(n){switch(n.label){case 0:return t._currentStepNumber=e,t._introItems===void 0?[3,2]:[4,T(t)];case 1:n.sent(),n.label=2;case 2:return[2]}})})}function T(t){return f(this,void 0,void 0,function(){var e,n,o;return m(this,function(i){switch(i.label){case 0:if(t._direction="forward",t._currentStepNumber!==void 0)for(e=0;e<t._introItems.length;e++)t._introItems[e].step===t._currentStepNumber&&(t._currentStep=e-1,t._currentStepNumber=void 0);return t._currentStep===-1?t._currentStep=0:++t._currentStep,n=t._introItems[t._currentStep],o=!0,g(t._introBeforeChangeCallback)?[4,t._introBeforeChangeCallback.call(t,n&&n.element,t._currentStep,t._direction)]:[3,2];case 1:o=i.sent(),i.label=2;case 2:return o===!1?(--t._currentStep,[2,!1]):t._introItems.length<=t._currentStep?g(t._introCompleteCallback)?[4,t._introCompleteCallback.call(t,t._currentStep,"end")]:[3,4]:[3,6];case 3:i.sent(),i.label=4;case 4:return[4,I(t,t._targetElement)];case 5:return i.sent(),[2,!1];case 6:return[4,kt(t,n)];case 7:return i.sent(),[2,!0]}})})}function Y(t){return f(this,void 0,void 0,function(){var e,n;return m(this,function(o){switch(o.label){case 0:return t._direction="backward",t._currentStep<=0?[2,!1]:(--t._currentStep,e=t._introItems[t._currentStep],n=!0,g(t._introBeforeChangeCallback)?[4,t._introBeforeChangeCallback.call(t,e&&e.element,t._currentStep,t._direction)]:[3,2]);case 1:n=o.sent(),o.label=2;case 2:return n===!1?(++t._currentStep,[2,!1]):[4,kt(t,e)];case 3:return o.sent(),[2,!0]}})})}function jt(t,e){return f(this,void 0,void 0,function(){var n,o;return m(this,function(i){switch(i.label){case 0:return(n=e.code===void 0?e.which:e.code)===null&&(n=e.charCode===null?e.keyCode:e.charCode),n!=="Escape"&&n!==27||t._options.exitOnEsc!==!0?[3,2]:[4,I(t,t._targetElement)];case 1:return i.sent(),[3,16];case 2:return n!=="ArrowLeft"&&n!==37?[3,4]:[4,Y(t)];case 3:return i.sent(),[3,16];case 4:return n!=="ArrowRight"&&n!==39?[3,6]:[4,T(t)];case 5:return i.sent(),[3,16];case 6:return n!=="Enter"&&n!=="NumpadEnter"&&n!==13?[3,16]:(o=e.target||e.srcElement)&&o.className.match("introjs-prevbutton")?[4,Y(t)]:[3,8];case 7:return i.sent(),[3,15];case 8:return o&&o.className.match("introjs-skipbutton")?t._introItems.length-1===t._currentStep&&g(t._introCompleteCallback)?[4,t._introCompleteCallback.call(t,t._currentStep,"skip")]:[3,10]:[3,12];case 9:i.sent(),i.label=10;case 10:return[4,I(t,t._targetElement)];case 11:return i.sent(),[3,15];case 12:return o&&o.getAttribute("data-step-number")?(o.click(),[3,15]):[3,13];case 13:return[4,T(t)];case 14:i.sent(),i.label=15;case 15:e.preventDefault?e.preventDefault():e.returnValue=!1,i.label=16;case 16:return[2]}})})}function st(t){if(t===null||U(t)!=="object"||"nodeType"in t)return t;var e={};for(var n in t)"jQuery"in window&&t[n]instanceof window.jQuery?e[n]=t[n]:e[n]=st(t[n]);return e}function B(t){var e=document.querySelector(".introjs-hints");return e?Array.from(e.querySelectorAll(t)):[]}function at(t,e){return f(this,void 0,void 0,function(){var n;return m(this,function(o){switch(o.label){case 0:return n=B('.introjs-hint[data-step="'.concat(e,'"]'))[0],J(),n&&A(n,"introjs-hidehint"),g(t._hintCloseCallback)?[4,t._hintCloseCallback.call(t,e)]:[3,2];case 1:o.sent(),o.label=2;case 2:return[2]}})})}function zt(t){return f(this,void 0,void 0,function(){var e,n,o,i,r;return m(this,function(s){switch(s.label){case 0:e=B(".introjs-hint"),n=0,o=e,s.label=1;case 1:return n<o.length?(i=o[n],(r=i.getAttribute("data-step"))?[4,at(t,parseInt(r,10))]:[3,3]):[3,4];case 2:s.sent(),s.label=3;case 3:return n++,[3,1];case 4:return[2]}})})}function Gt(t){return f(this,void 0,void 0,function(){var e,n,o,i,r;return m(this,function(s){switch(s.label){case 0:if(!(e=B(".introjs-hint"))||!e.length)return[3,1];for(n=0,o=e;n<o.length;n++)i=o[n],(r=i.getAttribute("data-step"))&&At(parseInt(r,10));return[3,3];case 1:return[4,Et(t,t._targetElement)];case 2:s.sent(),s.label=3;case 3:return[2]}})})}function At(t){var e=B('.introjs-hint[data-step="'.concat(t,'"]'))[0];e&&rt(e,/introjs-hidehint/g)}function gt(t){var e=B('.introjs-hint[data-step="'.concat(t,'"]'))[0];e&&e.parentNode&&e.parentNode.removeChild(e)}function Ut(t){return f(this,void 0,void 0,function(){var e,n,o,i,r,s,a;return m(this,function(c){switch(c.label){case 0:for((e=document.querySelector(".introjs-hints"))===null&&(e=d("div",{className:"introjs-hints"})),n=function(b){return function(v){var y=v||window.event;y&&y.stopPropagation&&y.stopPropagation(),y&&y.cancelBubble!==null&&(y.cancelBubble=!0),Nt(t,b)}},o=0;o<t._hintItems.length;o++){if(i=t._hintItems[o],document.querySelector('.introjs-hint[data-step="'.concat(o,'"]')))return[2];D(r=d("a",{className:"introjs-hint"})),r.onclick=n(o),i.hintAnimation||A(r,"introjs-hint-no-anim"),Z(i.element)&&A(r,"introjs-fixedhint"),s=d("div",{className:"introjs-hint-dot"}),a=d("div",{className:"introjs-hint-pulse"}),r.appendChild(s),r.appendChild(a),r.setAttribute("data-step",o.toString()),i.hintTargetElement=i.element,i.element=r,xt(i.hintPosition,r,i.hintTargetElement),e.appendChild(r)}return document.body.appendChild(e),g(t._hintsAddedCallback)?[4,t._hintsAddedCallback.call(t)]:[3,2];case 1:c.sent(),c.label=2;case 2:return t._options.hintAutoRefreshInterval>=0&&(t._hintsAutoRefreshFunction=(p=function(){return K(t)},l=t._options.hintAutoRefreshInterval,function(){for(var b=[],v=0;v<arguments.length;v++)b[v]=arguments[v];window.clearTimeout(u),u=window.setTimeout(function(){p(b)},l)}),x.on(window,"scroll",t._hintsAutoRefreshFunction,t,!0)),[2]}var p,l,u})})}function xt(t,e,n){if(n!==void 0){var o=q(n),i=20,r=20;switch(t){default:case"top-left":e.style.left="".concat(o.left,"px"),e.style.top="".concat(o.top,"px");break;case"top-right":e.style.left="".concat(o.left+o.width-i,"px"),e.style.top="".concat(o.top,"px");break;case"bottom-left":e.style.left="".concat(o.left,"px"),e.style.top="".concat(o.top+o.height-r,"px");break;case"bottom-right":e.style.left="".concat(o.left+o.width-i,"px"),e.style.top="".concat(o.top+o.height-r,"px");break;case"middle-left":e.style.left="".concat(o.left,"px"),e.style.top="".concat(o.top+(o.height-r)/2,"px");break;case"middle-right":e.style.left="".concat(o.left+o.width-i,"px"),e.style.top="".concat(o.top+(o.height-r)/2,"px");break;case"middle-middle":e.style.left="".concat(o.left+(o.width-i)/2,"px"),e.style.top="".concat(o.top+(o.height-r)/2,"px");break;case"bottom-middle":e.style.left="".concat(o.left+(o.width-i)/2,"px"),e.style.top="".concat(o.top+o.height-r,"px");break;case"top-middle":e.style.left="".concat(o.left+(o.width-i)/2,"px"),e.style.top="".concat(o.top,"px")}}}function Nt(t,e){return f(this,void 0,void 0,function(){var n,o,i,r,s,a,c,p,l,u,b;return m(this,function(v){switch(v.label){case 0:return n=document.querySelector('.introjs-hint[data-step="'.concat(e,'"]')),o=t._hintItems[e],g(t._hintClickCallback)?[4,t._hintClickCallback.call(t,n,o,e)]:[3,2];case 1:v.sent(),v.label=2;case 2:return(i=J())!==void 0&&parseInt(i,10)===e||(r=d("div",{className:"introjs-tooltip"}),s=d("div"),a=d("div"),c=d("div"),r.onclick=function(y){y.stopPropagation?y.stopPropagation():y.cancelBubble=!0},s.className="introjs-tooltiptext",(p=d("p")).innerHTML=o.hint||"",s.appendChild(p),t._options.hintShowButton&&((l=d("a")).className=t._options.buttonClass,l.setAttribute("role","button"),l.innerHTML=t._options.hintButtonLabel,l.onclick=function(){return at(t,e)},s.appendChild(l)),a.className="introjs-arrow",r.appendChild(a),r.appendChild(s),u=n.getAttribute("data-step")||"",t._currentStep=parseInt(u,10),b=t._hintItems[t._currentStep],c.className="introjs-tooltipReferenceLayer introjs-hintReference",c.setAttribute("data-step",u),N(t,b,c),c.appendChild(r),document.body.appendChild(c),X(t,b,r,a,!0)),[2]}})})}function J(){var t=document.querySelector(".introjs-hintReference");if(t&&t.parentNode){var e=t.getAttribute("data-step");return e?(t.parentNode.removeChild(t),e):void 0}}function Et(t,e){return f(this,void 0,void 0,function(){var n,o,i,r,s,a,c,p,l,u;return m(this,function(b){switch(b.label){case 0:if(t._hintItems=[],t._options.hints&&t._options.hints.length>0)for(n=0,o=t._options.hints;n<o.length;n++)i=o[n],typeof(r=st(i)).element=="string"&&(r.element=document.querySelector(r.element)),r.hintPosition=r.hintPosition||t._options.hintPosition,r.hintAnimation=r.hintAnimation||t._options.hintAnimation,r.element!==null&&t._hintItems.push(r);else{if(!(s=Array.from(e.querySelectorAll("*[data-hint]")))||!s.length)return[2,!1];for(a=0,c=s;a<c.length;a++)p=c[a],l=p.getAttribute("data-hint-animation"),u=t._options.hintAnimation,l&&(u=l==="true"),t._hintItems.push({element:p,hint:p.getAttribute("data-hint")||"",hintPosition:p.getAttribute("data-hint-position")||t._options.hintPosition,hintAnimation:u,tooltipClass:p.getAttribute("data-tooltip-class")||void 0,position:p.getAttribute("data-position")||t._options.tooltipPosition})}return[4,Ut(t)];case 1:return b.sent(),x.on(document,"click",J,t,!1),x.on(window,"resize",K,t,!0),[2,!0]}})})}function K(t){for(var e=0,n=t._hintItems;e<n.length;e++){var o=n[e],i=o.hintTargetElement;xt(o.hintPosition,o.element,i)}}function Tt(t,e){var n=Array.from(e.querySelectorAll("*[data-intro]")),o=[];if(t._options.steps&&t._options.steps.length)for(var i=0,r=t._options.steps;i<r.length;i++){var s=st(b=r[i]);if(s.step=o.length+1,s.title=s.title||"",typeof s.element=="string"&&(s.element=document.querySelector(s.element)||void 0),s.element===void 0||s.element===null){var a=document.querySelector(".introjsFloatingElement");a===null&&(a=d("div",{className:"introjsFloatingElement"}),document.body.appendChild(a)),s.element=a,s.position="floating"}s.position=s.position||t._options.tooltipPosition,s.scrollTo=s.scrollTo||t._options.scrollTo,s.disableInteraction===void 0&&(s.disableInteraction=t._options.disableInteraction),s.element!==null&&o.push(s)}else{var c=void 0;if(n.length<1)return[];for(var p=0,l=n;p<l.length;p++){var u=l[p];if((!t._options.group||u.getAttribute("data-intro-group")===t._options.group)&&u.style.display!=="none"){var b=parseInt(u.getAttribute("data-step")||"",10);c=t._options.disableInteraction,u.hasAttribute("data-disable-interaction")&&(c=!!u.getAttribute("data-disable-interaction")),b>0&&(o[b-1]={step:b,element:u,title:u.getAttribute("data-title")||"",intro:u.getAttribute("data-intro")||"",tooltipClass:u.getAttribute("data-tooltip-class")||void 0,highlightClass:u.getAttribute("data-highlight-class")||void 0,position:u.getAttribute("data-position")||t._options.tooltipPosition,scrollTo:u.getAttribute("data-scroll-to")||t._options.scrollTo,disableInteraction:c})}}for(var v=0,y=0,w=n;y<w.length;y++)if(u=w[y],(!t._options.group||u.getAttribute("data-intro-group")===t._options.group)&&u.getAttribute("data-step")===null){for(;o[v]!==void 0;)v++;c=u.hasAttribute("data-disable-interaction")?!!u.getAttribute("data-disable-interaction"):t._options.disableInteraction,o[v]={element:u,title:u.getAttribute("data-title")||"",intro:u.getAttribute("data-intro")||"",step:v+1,tooltipClass:u.getAttribute("data-tooltip-class")||void 0,highlightClass:u.getAttribute("data-highlight-class")||void 0,position:u.getAttribute("data-position")||t._options.tooltipPosition,scrollTo:u.getAttribute("data-scroll-to")||t._options.scrollTo,disableInteraction:c}}}for(var _=[],k=0;k<o.length;k++)o[k]&&_.push(o[k]);return(o=_).sort(function(L,H){return L.step-H.step}),o}function It(t,e){var n=t._currentStep;if(n!=null&&n!=-1){var o=t._introItems[n],i=document.querySelector(".introjs-tooltipReferenceLayer"),r=document.querySelector(".introjs-helperLayer"),s=document.querySelector(".introjs-disableInteraction");N(t,o,r),N(t,o,i),N(t,o,s),e&&(t._introItems=Tt(t,t._targetElement),function(p,l){if(p._options.showBullets){var u=document.querySelector(".introjs-bullets");u&&u.parentNode&&u.parentNode.replaceChild(Ct(p,l),u)}}(t,o),St(i,n,t._introItems.length));var a=document.querySelector(".introjs-arrow"),c=document.querySelector(".introjs-tooltip");return c&&a&&X(t,t._introItems[n],c,a),K(t),t}}function Lt(t){It(t)}function M(t,e){if(e===void 0&&(e=!1),t&&t.parentElement){var n=t.parentElement;e?(E(t,{opacity:"0"}),window.setTimeout(function(){try{n.removeChild(t)}catch(o){}},500)):n.removeChild(t)}}function I(t,e,n){return n===void 0&&(n=!1),f(this,void 0,void 0,function(){var o,i,r,s;return m(this,function(a){switch(a.label){case 0:return o=!0,t._introBeforeExitCallback===void 0?[3,2]:[4,t._introBeforeExitCallback.call(t,e)];case 1:o=a.sent(),a.label=2;case 2:if(!n&&o===!1)return[2];if((i=Array.from(e.querySelectorAll(".introjs-overlay")))&&i.length)for(r=0,s=i;r<s.length;r++)M(s[r]);return M(e.querySelector(".introjs-helperLayer"),!0),M(e.querySelector(".introjs-tooltipReferenceLayer")),M(e.querySelector(".introjs-disableInteraction")),M(document.querySelector(".introjsFloatingElement")),_t(),x.off(window,"keydown",jt,t,!0),x.off(window,"resize",Lt,t,!0),g(t._introExitCallback)?[4,t._introExitCallback.call(t)]:[3,4];case 3:a.sent(),a.label=4;case 4:return t._currentStep=-1,[2]}})})}function Xt(t,e){return f(this,void 0,void 0,function(){var n;return m(this,function(o){switch(o.label){case 0:return t.isActive()?g(t._introStartCallback)?[4,t._introStartCallback.call(t,e)]:[3,2]:[2,!1];case 1:o.sent(),o.label=2;case 2:return(n=Tt(t,e)).length===0?[2,!1]:(t._introItems=n,function(i,r){var s=this,a=d("div",{className:"introjs-overlay"});E(a,{top:0,bottom:0,left:0,right:0,position:"fixed"}),r.appendChild(a),i._options.exitOnOverlayClick===!0&&(E(a,{cursor:"pointer"}),a.onclick=function(){return f(s,void 0,void 0,function(){return m(this,function(c){switch(c.label){case 0:return[4,I(i,r)];case 1:return c.sent(),[2]}})})})}(t,e),[4,T(t)]);case 3:o.sent(),e.addEventListener,t._options.keyboardNavigation&&x.on(window,"keydown",jt,t,!0),x.on(window,"resize",Lt,t,!0),o.label=4;case 4:return[2,!1]}})})}function vt(t,e,n){return t[e]=n,t}var tt=function(){function t(e){this._currentStep=-1,this._introItems=[],this._hintItems=[],this._targetElement=e,this._options={steps:[],hints:[],isActive:!0,nextLabel:"Next",prevLabel:"Back",skipLabel:"×",doneLabel:"Done",hidePrev:!1,hideNext:!1,nextToDone:!0,tooltipPosition:"bottom",tooltipClass:"",group:"",highlightClass:"",exitOnEsc:!0,exitOnOverlayClick:!0,showStepNumbers:!1,stepNumbersOfLabel:"of",keyboardNavigation:!0,showButtons:!0,showBullets:!0,showProgress:!1,scrollToElement:!0,scrollTo:"element",scrollPadding:30,overlayOpacity:.5,autoPosition:!0,positionPrecedence:["bottom","top","right","left"],disableInteraction:!1,dontShowAgain:!1,dontShowAgainLabel:"Don't show this again",dontShowAgainCookie:"introjs-dontShowAgain",dontShowAgainCookieDays:365,helperElementPadding:10,hintPosition:"top-middle",hintButtonLabel:"Got it",hintShowButton:!0,hintAutoRefreshInterval:10,hintAnimation:!0,buttonClass:"introjs-button",progressBarAdditionalClass:!1}}return t.prototype.isActive=function(){return(!this._options.dontShowAgain||(e=yt(this._options.dontShowAgainCookie))===""||e!=="true")&&this._options.isActive;var e},t.prototype.clone=function(){return new t(this._targetElement)},t.prototype.setOption=function(e,n){return this._options=vt(this._options,e,n),this},t.prototype.setOptions=function(e){return this._options=function(n,o){for(var i=0,r=Object.entries(o);i<r.length;i++){var s=r[i];n=vt(n,s[0],s[1])}return n}(this._options,e),this},t.prototype.start=function(){return f(this,void 0,void 0,function(){return m(this,function(e){switch(e.label){case 0:return[4,Xt(this,this._targetElement)];case 1:return e.sent(),[2,this]}})})},t.prototype.goToStep=function(e){return f(this,void 0,void 0,function(){return m(this,function(n){switch(n.label){case 0:return[4,Wt(this,e)];case 1:return n.sent(),[2,this]}})})},t.prototype.addStep=function(e){return this._options.steps||(this._options.steps=[]),this._options.steps.push(e),this},t.prototype.addSteps=function(e){if(!e.length)return this;for(var n=0;n<e.length;n++)this.addStep(e[n]);return this},t.prototype.goToStepNumber=function(e){return f(this,void 0,void 0,function(){return m(this,function(n){switch(n.label){case 0:return[4,$t(this,e)];case 1:return n.sent(),[2,this]}})})},t.prototype.nextStep=function(){return f(this,void 0,void 0,function(){return m(this,function(e){switch(e.label){case 0:return[4,T(this)];case 1:return e.sent(),[2,this]}})})},t.prototype.previousStep=function(){return f(this,void 0,void 0,function(){return m(this,function(e){switch(e.label){case 0:return[4,Y(this)];case 1:return e.sent(),[2,this]}})})},t.prototype.currentStep=function(){return this._currentStep},t.prototype.exit=function(e){return f(this,void 0,void 0,function(){return m(this,function(n){switch(n.label){case 0:return[4,I(this,this._targetElement,e)];case 1:return n.sent(),[2,this]}})})},t.prototype.refresh=function(e){return It(this,e),this},t.prototype.setDontShowAgain=function(e){return Vt(this,e),this},t.prototype.onbeforechange=function(e){if(!g(e))throw new Error("Provided callback for onbeforechange was not a function");return this._introBeforeChangeCallback=e,this},t.prototype.onchange=function(e){if(!g(e))throw new Error("Provided callback for onchange was not a function.");return this._introChangeCallback=e,this},t.prototype.onafterchange=function(e){if(!g(e))throw new Error("Provided callback for onafterchange was not a function");return this._introAfterChangeCallback=e,this},t.prototype.oncomplete=function(e){if(!g(e))throw new Error("Provided callback for oncomplete was not a function.");return this._introCompleteCallback=e,this},t.prototype.onhintsadded=function(e){if(!g(e))throw new Error("Provided callback for onhintsadded was not a function.");return this._hintsAddedCallback=e,this},t.prototype.onhintclick=function(e){if(!g(e))throw new Error("Provided callback for onhintclick was not a function.");return this._hintClickCallback=e,this},t.prototype.onhintclose=function(e){if(!g(e))throw new Error("Provided callback for onhintclose was not a function.");return this._hintCloseCallback=e,this},t.prototype.onstart=function(e){if(!g(e))throw new Error("Provided callback for onstart was not a function.");return this._introStartCallback=e,this},t.prototype.onexit=function(e){if(!g(e))throw new Error("Provided callback for onexit was not a function.");return this._introExitCallback=e,this},t.prototype.onskip=function(e){if(!g(e))throw new Error("Provided callback for onskip was not a function.");return this._introSkipCallback=e,this},t.prototype.onbeforeexit=function(e){if(!g(e))throw new Error("Provided callback for onbeforeexit was not a function.");return this._introBeforeExitCallback=e,this},t.prototype.addHints=function(){return f(this,void 0,void 0,function(){return m(this,function(e){switch(e.label){case 0:return[4,Et(this,this._targetElement)];case 1:return e.sent(),[2,this]}})})},t.prototype.hideHint=function(e){return f(this,void 0,void 0,function(){return m(this,function(n){switch(n.label){case 0:return[4,at(this,e)];case 1:return n.sent(),[2,this]}})})},t.prototype.hideHints=function(){return f(this,void 0,void 0,function(){return m(this,function(e){switch(e.label){case 0:return[4,zt(this)];case 1:return e.sent(),[2,this]}})})},t.prototype.showHint=function(e){return At(e),this},t.prototype.showHints=function(){return f(this,void 0,void 0,function(){return m(this,function(e){switch(e.label){case 0:return[4,Gt(this)];case 1:return e.sent(),[2,this]}})})},t.prototype.removeHints=function(){return function(e){for(var n=0,o=B(".introjs-hint");n<o.length;n++){var i=o[n].getAttribute("data-step");i&&gt(parseInt(i,10))}x.off(document,"click",J,e,!1),x.off(window,"resize",K,e,!0),e._hintsAutoRefreshFunction&&x.off(window,"scroll",e._hintsAutoRefreshFunction,e,!0)}(this),this},t.prototype.removeHint=function(e){return gt(e),this},t.prototype.showHintDialog=function(e){return f(this,void 0,void 0,function(){return m(this,function(n){switch(n.label){case 0:return[4,Nt(this,e)];case 1:return n.sent(),[2,this]}})})},t}(),nt=function t(e){var n;if(U(e)==="object")n=new tt(e);else if(typeof e=="string"){var o=document.querySelector(e);if(!o)throw new Error("There is no element with given selector.");n=new tt(o)}else n=new tt(document.body);return t.instances[et(n,"introjs-instance")]=n,n};nt.version="7.2.0",nt.instances={};const Yt=Pt({components:{PageWrapper:Rt},setup(){const{prefixVar:t}=Mt("");function e(){nt().setOptions({steps:[{title:"Welcome",intro:"Hello World! 👋"},{title:"Collapse Button",element:document.querySelector(`.${t}-layout-header-trigger`),intro:"This is the menu collapse button."},{title:"User Action",element:document.querySelector(`.${t}-layout-header-action`),intro:"This is the user function area."}]}).start()}return{handleStart:e}}});function Zt(t,e,n,o,i,r){const s=pt("a-button"),a=pt("PageWrapper");return Bt(),qt(a,{title:"引导页",content:"用于给用户的指引操作"},{default:ht(()=>[Ht(s,{type:"primary",onClick:t.handleStart},{default:ht(()=>e[0]||(e[0]=[Ot("开始")])),_:1,__:[0]},8,["onClick"])]),_:1})}const ae=Dt(Yt,[["render",Zt]]);export{ae as default};
