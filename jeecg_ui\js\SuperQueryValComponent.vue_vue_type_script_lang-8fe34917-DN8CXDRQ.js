import{d as E,e as b,u as f,k as h}from"./vue-vendor-dy9k-Yad.js";import{u as S}from"./antd-vue-vendor-me9YkNVC.js";import{j as V}from"./componentMap-Bkie1n3v.js";import{n as x}from"./index-CCWaWN5g.js";var N=Object.defineProperty,A=Object.defineProperties,I=Object.getOwnPropertyDescriptors,y=Object.getOwnPropertySymbols,D=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable,O=(e,t,r)=>t in e?N(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,s=(e,t)=>{for(var r in t||(t={}))D.call(t,r)&&O(e,r,t[r]);if(y)for(var r of y(t))G.call(t,r)&&O(e,r,t[r]);return e},Q=(e,t)=>A(e,I(t));const z=E({name:"SuperQueryValComponent",inheritAttrs:!1,props:{schema:{type:Object,default:()=>({})},formModel:{type:Object,default:()=>({})},setFormModel:{type:Function,default:null}},emits:["submit"],setup(e,{emit:t}){const r=b(()=>{var n;const{schema:o,formModel:u}=e;let{componentProps:a={}}=o;return x(a)&&(a=(n=a({schema:o,formModel:u}))!=null?n:{}),a}),j=b(()=>{const{formModel:n,schema:o}=e;return{field:o.field,model:n,values:s({},n),schema:o}});function g(){var n;const{component:o,changeEvent:u="change",valueField:a}=e.schema,i="val",v=o&&["Switch","Checkbox"].includes(o),p=`on${S(u)}`,P={[p]:(...c)=>{const[m]=c;l[p]&&l[p](...c);const d=m?m.target:null,k=d?v?d.checked:d.value:m;e.setFormModel(i,k)}},C=V.get(o),l=s({allowClear:!0,getPopupContainer:c=>c==null?void 0:c.parentNode},f(r));!l.disabled&&o!=="RangePicker"&&o&&(l.placeholder=((n=f(r))==null?void 0:n.placeholder)||w(o)+e.schema.label),l.codeField=i,l.formValues=f(j);const F={[a||(v?"checked":"value")]:e.formModel[i]},M=Q(s(s(s({},l),P),F),{allowClear:!0,onPressEnter(){t("submit")}});return h(C,M,null)}return()=>h("div",{style:"width:100%"},[g()]);function w(n){return n.includes("Input")||n.includes("Complete")?"请输入":"请选择"}}});export{z as G};
