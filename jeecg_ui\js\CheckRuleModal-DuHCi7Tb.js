var W=Object.defineProperty;var N=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var q=(i,o,t)=>o in i?W(i,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[o]=t,L=(i,o)=>{for(var t in o||(o={}))j.call(o,t)&&q(i,t,o[t]);if(N)for(var t of N(o))z.call(o,t)&&q(i,t,o[t]);return i};var R=(i,o,t)=>new Promise((V,d)=>{var x=n=>{try{y(t.next(n))}catch(b){d(b)}},T=n=>{try{y(t.throw(n))}catch(b){d(b)}},y=n=>n.done?V(n.value):Promise.resolve(n.value).then(x,T);y((t=t.apply(i,o)).next())});import{d as D,e as Q,u as p,f as u,ag as S,aB as X,ar as Y,aD as g,k as c,aE as Z}from"./vue-vendor-dy9k-Yad.js";import{B as ee}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{f as te,u as re,s as oe}from"./check.rule.data-CeB7uPQL.js";import{ac as ae,bx as v}from"./index-CCWaWN5g.js";import{p as ie}from"./antd-vue-vendor-me9YkNVC.js";import{u as se}from"./useForm-CgkFTrrO.js";import{B as le}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./renderUtils-D7XVOFwj.js";import"./user.api-mLAlJze4.js";const ft=D({__name:"CheckRuleModal",emits:["register","success"],setup(i,{emit:o}){const t=Q(()=>p(d)?"编辑":"新增"),V=o,d=u(!0),[x,{resetFields:T,setFieldsValue:y,validate:n,getFieldsValue:b}]=se({schemas:te,showActionButtonGroup:!1}),_=u("1");let B=u([]),J=u([]);const[$,{setModalProps:k,closeModal:A}]=ae(e=>R(null,null,function*(){if(yield T(),k({confirmLoading:!1}),d.value=!!(e!=null&&e.isUpdate),_.value="1",B.value=[],J.value=[],p(d)){yield y(L({},e.record));let a=e.record.ruleJson;if(a){let s=JSON.parse(a),r=[],l=[],m="1";s.forEach(f=>{f.digits==="*"?r.push(Object.assign(f,{priority:m})):(m="0",l.push(f))}),B.value=l,J.value=r}}})),C=u(),F=u();function M(e,a){return new Promise((s,r)=>{e.value.validateTable().then(l=>{if(l)_.value=a,r();else{const m=e.value.getTableData();s(m)}})})}function K(){return R(this,null,function*(){let e,a=[],s=[];n().then(r=>(e=r,M(C,"1"))).then(r=>(r&&r.length>0&&(s=r),M(F,"2"))).then(r=>{r&&r.length>0&&(a=r);let l=[],m=[];for(let h=0;h<a.length;h++){let w=a[h];w.digits="*",w.priority==="1"?l.push(w):m.push(w)}let G=l.concat(s).concat(m).map(h=>ie(h,"digits","pattern","message")),H=JSON.stringify(G),I=Object.assign({},e,{ruleJson:H});P(I)}).catch(()=>{k({confirmLoading:!1})})})}function P(e){return R(this,null,function*(){try{k({confirmLoading:!0}),d.value?yield re(e):yield oe(e),A(),V("success")}finally{k({confirmLoading:!1})}})}const O=({cellValue:e},a)=>{try{new RegExp(e),a(!0)}catch(s){a(!1,"请输入正确的正则表达式")}},U=u([{title:"位数",key:"digits",type:v.inputNumber,minWidth:180,validateRules:[{required:!0,message:"${title}不能为空"},{pattern:/^[1-9]\d*$/,message:"请输入零以上的正整数"}]},{title:"规则（正则表达式）",key:"pattern",minWidth:320,type:v.input,validateRules:[{required:!0,message:"规则不能为空"},{handler:O}]},{title:"提示文本",key:"message",minWidth:180,type:v.input,validateRules:[{required:!0,message:"${title}不能为空"}]}]),E=u([{title:"优先级",key:"priority",type:v.select,defaultValue:"1",options:[{title:"优先运行",value:"1"},{title:"最后运行",value:"0"}],validateRules:[]},{title:"规则（正则表达式）",key:"pattern",width:"40%",type:v.input,validateRules:[{required:!0,message:"规则不能为空"},{handler:O}]},{title:"提示文本",key:"message",width:"20%",type:v.input,validateRules:[{required:!0,message:"${title}不能为空"}]}]);return(e,a)=>{const s=S("a-alert"),r=S("JVxeTable"),l=S("a-tab-pane"),m=S("a-tabs");return Y(),X(p(ee),Z(e.$attrs,{onRegister:p($),onOk:K,title:t.value,width:1200,destroyOnClose:""}),{default:g(()=>[c(p(le),{onRegister:p(x)},null,8,["onRegister"]),c(m,{activeKey:_.value,"onUpdate:activeKey":a[0]||(a[0]=f=>_.value=f),animated:""},{default:g(()=>[c(l,{tab:"局部规则",key:"1",forceRender:!0},{default:g(()=>[c(r,{ref_key:"vTable1",ref:C,toolbar:"",rowNumber:"",dragSort:"",rowSelection:"",maxHeight:580,dataSource:p(B),columns:U.value},{toolbarAfter:g(()=>[c(s,{type:"info",showIcon:"",message:"局部规则按照你输入的位数有序的校验",style:{"margin-bottom":"8px"}})]),_:1},8,["dataSource","columns"])]),_:1}),c(l,{tab:"全局规则",key:"2",forceRender:!0},{default:g(()=>[c(r,{ref_key:"vTable2",ref:F,toolbar:"",rowNumber:"",dragSort:"",rowSelection:"",maxHeight:580,dataSource:p(J),addSetActive:!1,columns:E.value},{toolbarAfter:g(()=>[c(s,{type:"info",showIcon:"",message:"全局规则可校验用户输入的所有字符；全局规则的优先级比局部规则的要高。",style:{"margin-bottom":"8px"}})]),_:1},8,["dataSource","columns"])]),_:1})]),_:1},8,["activeKey"])]),_:1},16,["onRegister","title"])}}});export{ft as default};
