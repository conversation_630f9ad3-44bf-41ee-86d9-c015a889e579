var H=Object.defineProperty,J=Object.defineProperties;var X=Object.getOwnPropertyDescriptors;var h=Object.getOwnPropertySymbols;var Y=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable;var v=(r,o,e)=>o in r?H(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,k=(r,o)=>{for(var e in o||(o={}))Y.call(o,e)&&v(r,e,o[e]);if(h)for(var e of h(o))Z.call(o,e)&&v(r,e,o[e]);return r},S=(r,o)=>J(r,X(o));var w=(r,o,e)=>new Promise((l,c)=>{var _=a=>{try{d(e.next(a))}catch(u){c(u)}},g=a=>{try{d(e.throw(a))}catch(u){c(u)}},d=a=>a.done?l(a.value):Promise.resolve(a.value).then(_,g);d((e=e.apply(r,o)).next())});import{d as tt,c as et,e as ot,w as rt,o as it,ag as s,aq as A,ar as M,k as i,u as p,aD as m,ah as at,G as nt,F as B,at as I}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{a as lt}from"./index-JbqXEynz.js";import{useListPage as pt}from"./useListPage-Soxgnx9a.js";import mt from"./DepartRoleModal-DuErG4qT.js";import st from"./DepartRoleAuthDrawer-Ckr2bhUa.js";import{f as ct,g as dt}from"./depart.user.api-D_abnxSU.js";import{d as ut,a as ft}from"./depart.user.data-BFBNnnrj.js";import{ad as _t}from"./index-CCWaWN5g.js";import gt from"./BasicTable-xCEZpGLb.js";import{Q as bt}from"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./DepartRoleDataRuleDrawer-Qn1FPJB7.js";import"./user.api-mLAlJze4.js";const Be=tt({__name:"DepartRoleInfoTab",props:{data:{require:!0,type:Object}},emits:["register"],setup(r){const o=et("prefixCls"),e=r,l=ot(()=>{var t;return(t=e.data)==null?void 0:t.id}),c={xs:24,sm:24,md:24,lg:12,xl:12,xxl:8},{tableContext:_,createMessage:g}=pt({tableProps:{api:ct,columns:ft,canResize:!1,formConfig:{labelWidth:100,schemas:ut,baseColProps:c,labelAlign:"left",labelCol:{xs:24,sm:24,md:24,lg:9,xl:7,xxl:6},wrapperCol:{},actionColOptions:S(k({},c),{style:{textAlign:"left"}}),showResetButton:!!l.value,showSubmitButton:!!l.value},tableSetting:{cacheKey:"depart_user_departInfo"},beforeFetch(t){t.deptId=l.value},immediate:!!l.value}}),[d,{reload:a,setProps:u,setLoading:R,updateTableDataRecord:F},{rowSelection:P,selectedRowKeys:b}]=_,[T,D]=_t(),[j,N]=lt();rt(()=>e.data,()=>a()),it(()=>{});function V(){b.value=[]}function K(){D.openModal(!0,{isUpdate:!1,record:{}})}function L(t){D.openModal(!0,{isUpdate:!0,record:t})}function q(t){N.openDrawer(!0,{record:t})}function C(t,n){return w(this,null,function*(){if(!l.value)g.warning("请先选择一个部门");else{R(!0);let f=p(t).join(",");try{return yield dt({ids:f},n),a()}finally{R(!1)}}return Promise.reject()})}function O(){return w(this,null,function*(){try{yield C(b,!0),V()}catch(t){}})}function U({isUpdate:t,values:n}){t?F(n.id,n):a()}function z(t){return[{label:"编辑",onClick:L.bind(null,t)}]}function E(t){return[{label:"授权",onClick:q.bind(null,t)},{label:"删除",color:"error",popConfirm:{title:"确认要删除吗？",confirm:C.bind(null,[t.id],!1)}}]}return(t,n)=>{const f=s("a-button"),G=s("a-divider"),x=s("icon"),Q=s("a-menu-item"),W=s("a-menu"),$=s("a-dropdown");return M(),A(B,null,[i(p(gt),{onRegister:p(d),rowSelection:p(P)},{tableTitle:m(()=>[i(f,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:K,disabled:!l.value},{default:m(()=>n[0]||(n[0]=[nt("添加部门角色")])),_:1,__:[0]},8,["disabled"]),p(b).length>0?(M(),A(B,{key:0},[i(G,{type:"vertical"}),i($,null,{overlay:m(()=>[i(W,null,{default:m(()=>[i(Q,{key:"1",onClick:O},{default:m(()=>[i(x,{icon:"ant-design:delete-outlined"}),n[1]||(n[1]=I("span",null,"删除",-1))]),_:1,__:[1]})]),_:1})]),default:m(()=>[i(f,null,{default:m(()=>[n[2]||(n[2]=I("span",null,"批量操作 ",-1)),i(x,{icon:"akar-icons:chevron-down"})]),_:1,__:[2]})]),_:1})],64)):at("",!0)]),action:m(({record:y})=>[i(p(bt),{actions:z(y),dropDownActions:E(y)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),i(mt,{departId:l.value,onRegister:p(T),onSuccess:U},null,8,["departId","onRegister"]),i(st,{onRegister:p(j)},null,8,["onRegister"])],64)}}});export{Be as default};
