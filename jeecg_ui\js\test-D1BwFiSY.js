import{d as u,f as _,ag as c,aq as v,ar as f,at as r,k as p,aD as m,G as e,u as g}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{J as h}from"./JEditorTiptap-BwAoWsi9.js";import{a as C}from"./index-CCWaWN5g.js";import"./BasicForm-DBcXiHk0.js";import"./antd-vue-vendor-me9YkNVC.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-ByPySmGo.js";const x={class:"p-4"},b={class:"mb-4"},k={class:"mt-4"},T=["innerHTML"],V=u({__name:"test",setup(B){const o=_("<p>这是初始内容</p>"),a=()=>{alert(`内容长度: ${o.value.length}`)},n=()=>{o.value=`<h2>测试标题</h2><p>这是通过代码设置的内容，时间: ${new Date().toLocaleString()}</p>`},s=l=>{};return(l,t)=>{const i=c("a-button");return f(),v("div",x,[t[4]||(t[4]=r("h2",null,"Tiptap富文本编辑器测试",-1)),r("div",b,[p(i,{onClick:a},{default:m(()=>t[1]||(t[1]=[e("获取内容")])),_:1,__:[1]}),p(i,{onClick:n,class:"ml-2"},{default:m(()=>t[2]||(t[2]=[e("设置内容")])),_:1,__:[2]})]),p(g(h),{value:o.value,"onUpdate:value":t[0]||(t[0]=d=>o.value=d),height:"400px",placeholder:"请输入内容...",onChange:s},null,8,["value"]),r("div",k,[t[3]||(t[3]=r("h3",null,"内容预览：",-1)),r("div",{class:"border p-4 rounded",innerHTML:o.value},null,8,T)])])}}}),Lt=C(V,[["__scopeId","data-v-f8383cdb"]]);export{Lt as default};
