import{d as s,f as i,aB as n,ar as m,aD as r,at as a,k as t,u as l}from"./vue-vendor-dy9k-Yad.js";import{J as p}from"./antd-vue-vendor-me9YkNVC.js";import{P as d}from"./index-CtJ0w2CP.js";import c from"./WorkbenchHeader-B1KrshrO.js";import u from"./ProjectCard-Cp1LTgHg.js";import _ from"./QuickNav-Bo8xTZiD.js";import f from"./DynamicInfo-DZMWw6G1.js";import g from"./SaleRadar-DN3_t69e.js";import"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./header-OZa5fSDc.js";import"./data-BJoU8O08.js";import"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const v="/assets/illustration-jTCqTCdW.svg",y={class:"lg:flex"},x={class:"lg:w-7/10 w-full !mr-4 enter-y"},C={class:"lg:w-3/10 w-full enter-y"},L=s({__name:"index",setup(h){const o=i(!0);return setTimeout(()=>{o.value=!1},500),(w,e)=>(m(),n(l(d),null,{headerContent:r(()=>[t(c)]),default:r(()=>[a("div",y,[a("div",x,[t(u,{loading:o.value,class:"enter-y"},null,8,["loading"]),t(f,{loading:o.value,class:"!my-4 enter-y"},null,8,["loading"])]),a("div",C,[t(_,{loading:o.value,class:"enter-y"},null,8,["loading"]),t(l(p),{class:"!my-4 enter-y",loading:o.value},{default:r(()=>e[0]||(e[0]=[a("img",{class:"xl:h-50 h-30 mx-auto",src:v},null,-1)])),_:1,__:[0]},8,["loading"]),t(g,{loading:o.value,class:"enter-y"},null,8,["loading"])])])]),_:1}))}});export{L as default};
