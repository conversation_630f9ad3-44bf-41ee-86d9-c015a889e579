const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/BasicTable-xCEZpGLb.js","js/vue-vendor-dy9k-Yad.js","js/antd-vue-vendor-me9YkNVC.js","js/index-L3cSIXth.js","js/BasicForm-DBcXiHk0.js","js/componentMap-Bkie1n3v.js","js/index-CCWaWN5g.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/useFormItem-CHvpjy4o.js","js/index-Diw57m_E.js","js/BasicModal-BLFvpBuk.js","js/ModalHeader-BJG9dHtK.js","js/useTimeout-CeTdFD_D.js","js/index-CImCetrx.js","assets/index-BObJM2Lc.css","assets/ModalHeader-HwQKX-UU.css","js/useWindowSizeFn-DDbrQbks.js","js/index-LCGLvkB3.js","js/index-De_W6s5g.js","js/index-D6l0IxOU.js","js/useIntersectionObserver-C4LVxQJW.js","assets/index-zj-Vfn3Q.css","assets/BasicModal-ByeTDAzn.css","js/CustomModal-BakuIxQv.js","assets/CustomModal-DWxHZmza.css","assets/index-yRxe3SQ1.css","js/download-CZ-9H9a3.js","js/base64Conver-24EVOS6V.js","js/index-CBCjSSNZ.js","assets/index-NmxXH94f.css","js/index-DFrpKMGa.js","js/useCountdown-CCWNeb_r.js","js/useFormItemSingle-Cw668yj5.js","assets/index-BB9COjV3.css","js/JSelectUser-COkExGbu.js","js/props-CCT78mKr.js","js/JSelectBiz-jOYRdMJf.js","assets/JSelectBiz-CYw1rOZ6.css","assets/JSelectUser-CQvjZTEr.css","js/JSelectDept-I-NqkbOH.js","js/index-BtIdS_Qz.js","js/bem-sRx7x0Ii.js","js/props-qAqCef5R.js","js/useContextMenu-BU2ycxls.js","assets/useContextMenu-DRJLeHo9.css","assets/index-D8VMPii6.css","assets/JSelectDept-WHP406xL.css","js/JAreaSelect-Db7Nhhc_.js","js/areaDataUtil-BXVjRArW.js","assets/JAreaSelect-Pwl_5U28.css","js/JEditorTiptap-BwAoWsi9.js","js/index-ByPySmGo.js","assets/index-BrdQT4ew.css","js/JPopup-CeU6ry6r.js","assets/JPopup-Dn0_YeSX.css","js/JEllipsis-BsXuWNHJ.js","js/JUpload-CRos0F1P.js","assets/JUpload-CsrjJkIs.css","js/JSearchSelect-c_lfTydU.js","js/index-CXHeQyuE.js","js/index-Dyko68ZT.js","assets/index-CTbO_Zqi.css","assets/componentMap-Degzw4_e.css","assets/BasicForm-DTEnYz8c.css","js/useForm-CgkFTrrO.js","js/JAreaLinkage-DFCdF3cr.js","js/JCodeEditor-B-WXz11X.js","js/htmlmixed-CmvhkW5V.js","js/vue-CAKGUkuE.js","assets/vue-DyVx2_Fd.css","assets/JCodeEditor-DaPRKM4Q.css","assets/idea-C3eFBO7g.css","js/EasyCronInput-BuvtO5dv.js","assets/EasyCronInput-BLbXuoBB.css","js/JUploadModal-C-iKhVFc.js","js/injectionKey-DPVn4AgL.js","js/index-QxsVJqiT.js","js/depart.api-BoGnt_ZX.js","assets/BasicTable-DcVosJye.css"])))=>i.map(i=>d[i]);
var Fe=Object.defineProperty,Ue=Object.defineProperties;var Ke=Object.getOwnPropertyDescriptors;var we=Object.getOwnPropertySymbols;var Ae=Object.prototype.hasOwnProperty,Ne=Object.prototype.propertyIsEnumerable;var $e=(e,l,a)=>l in e?Fe(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,W=(e,l)=>{for(var a in l||(l={}))Ae.call(l,a)&&$e(e,a,l[a]);if(we)for(var a of we(l))Ne.call(l,a)&&$e(e,a,l[a]);return e},ue=(e,l)=>Ue(e,Ke(l));var A=(e,l,a)=>new Promise((i,p)=>{var u=t=>{try{o(a.next(t))}catch(n){p(n)}},s=t=>{try{o(a.throw(t))}catch(n){p(n)}},o=t=>t.done?i(t.value):Promise.resolve(t.value).then(u,s);o((a=a.apply(e,l)).next())});import{aj as ve,ac as me,a9 as N,aA as Te,_ as ye,a as E,H as c,ad as Ce,aB as ke,aC as Ee,d as Ie,aD as qe,aE as ze,u as fe,aF as He,aG as De,aH as Ge,aI as Ve,aJ as We,F as Me,j as x,R as ge,k as Re,N as Ye,ag as Qe,i as Pe}from"./index-CCWaWN5g.js";import{d as K,u as D,f as _,ag as S,aq as U,ar as T,k as P,aE as j,aD as V,at as X,r as pe,p as Y,w as J,h as se,e as le,n as ie,ah as te,aB as L,au as ae,G as de,F as ee,aC as oe,aA as ce,as as re,o as Xe,H as Ze,aM as xe,aH as et,aG as tt,aJ as at,aK as lt}from"./vue-vendor-dy9k-Yad.js";import{B as be}from"./index-Diw57m_E.js";import{u as Se}from"./JSelectUser-COkExGbu.js";import{s as _e}from"./props-CCT78mKr.js";import{J as Oe}from"./JSelectBiz-jOYRdMJf.js";import{useRuleFormItem as Z}from"./useFormItem-CHvpjy4o.js";import{aN as nt,ai as Le,o as ot,a5 as st,O as it,aO as rt,aP as ut}from"./antd-vue-vendor-me9YkNVC.js";import{T as dt}from"./index-QxsVJqiT.js";import{_ as ct}from"./index-BtIdS_Qz.js";import{q as ft}from"./depart.api-BoGnt_ZX.js";const pt=K({name:"PositionSelectModal",components:{BasicModal:be,BasicTable:ve(()=>ye(()=>import("./BasicTable-xCEZpGLb.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79])),{loading:!0})},props:ue(W({},_e),{modalTitle:{type:String,default:"职务选择"}}),emits:["register","getSelectResult"],setup(e,{emit:l,refs:a}){const[i,{closeModal:p}]=me(),u=N(),s={canResize:!1,bordered:!0,size:"small",rowKey:e.rowKey},o=Object.assign({},D(e),D(u),s),[{rowSelection:t,visibleChange:n,indexColumnProps:g,getSelectResult:C,handleDeleteSelected:y,selectRows:r}]=Se(Te,o),B=_(e.params),d={labelCol:{span:4},baseColProps:{xs:24,sm:10,md:10,lg:10,xl:10,xxl:10},actionColOptions:{xs:24,sm:8,md:8,lg:8,xl:8,xxl:8},schemas:[{label:"职务名称",field:"name",component:"JInput",colProps:{span:10}}]},h=[{title:"职务编码",dataIndex:"code",width:180,align:"left"},{title:"职务名称",dataIndex:"name"},{title:"职务等级",dataIndex:"postRank_dictText",width:180}],f={pagination:!1,showIndexColumn:!1,scroll:{y:390},size:"small",canResize:!1,bordered:!0,rowKey:"id",columns:[{title:"职务名称",dataIndex:"name",width:40},{title:"操作",dataIndex:"action",align:"center",width:40,slots:{customRender:"action"}}]};function v(){C((O,k)=>{l("getSelectResult",O,k),p()})}return{handleOk:v,getPositionList:Te,register:i,visibleChange:n,getBindValue:o,formConfig:d,indexColumnProps:g,columns:h,rowSelection:t,selectedTable:f,selectRows:r,handleDeleteSelected:y,searchInfo:B}}}),gt=["onClick"];function ht(e,l,a,i,p,u){const s=S("BasicTable"),o=S("a-col"),t=S("Icon"),n=S("a-row"),g=S("BasicModal");return T(),U("div",null,[P(g,j(e.$attrs,{onRegister:e.register,title:e.modalTitle,width:"1100px",height:600,wrapClassName:"j-user-select-modal",onOk:e.handleOk,destroyOnClose:"",onVisibleChange:e.visibleChange}),{default:V(()=>[P(n,null,{default:V(()=>[P(o,{span:e.showSelected?18:24},{default:V(()=>[P(s,j({columns:e.columns,bordered:!0,useSearchForm:!0,formConfig:e.formConfig,api:e.getPositionList,searchInfo:e.searchInfo,rowSelection:e.rowSelection,indexColumnProps:e.indexColumnProps},e.getBindValue),null,16,["columns","formConfig","api","searchInfo","rowSelection","indexColumnProps"])]),_:1},8,["span"]),P(o,{span:e.showSelected?6:0},{default:V(()=>[P(s,j(e.selectedTable,{dataSource:e.selectRows,useSearchForm:!0,formConfig:{showActionButtonGroup:!1,baseRowStyle:{minHeight:"40px"}}}),{action:V(({record:C})=>[X("a",{href:"javascript:void(0)",onClick:y=>e.handleDeleteSelected(C)},[P(t,{icon:"ant-design:delete-outlined"})],8,gt)]),_:1},16,["dataSource"])]),_:1},8,["span"])]),_:1})]),_:1},16,["onRegister","title","onOk","onVisibleChange"])])}const vt=E(pt,[["render",ht]]),mt=K({name:"JSelectPosition",components:{PositionSelectModal:vt,JSelectBiz:Oe},inheritAttrs:!1,props:{value:c.oneOfType([c.string,c.array]),labelKey:{type:String,default:"name"},rowKey:{type:String,default:"id"},params:{type:Object,default:()=>{}}},emits:["options-change","change","update:value"],setup(e,{emit:l,refs:a}){const i=_(),[p,{openModal:u}]=Ce(),[s]=Z(e,"value","change",i),o=_([]);let t=pe({value:[],change:!1});const n=_(!1);Y("selectOptions",o),Y("selectValues",t),Y("loadingEcho",n);const g=_(!1),C=N();J(()=>e.value,()=>{e.value&&r()},{deep:!0,immediate:!0}),J(t,()=>{t&&(s.value=t.value)});function y(){g.value=!0,u(!0,{isUpdate:!1})}function r(){let h=e.value?e.value:[];h&&typeof h=="string"&&h!="null"&&h!="undefined"&&(s.value=h.split(","),t.value=h.split(","))}function B(h,f){o.value=h,s.value=f,t.value=f,l("update:value",f.join(","))}const d=Object.assign({},D(e),D(C));return{state:s,getBindValue:d,attrs:C,selectOptions:o,selectValues:t,loadingEcho:n,tag:g,regModal:p,setValue:B,handleOpen:y}}}),yt={class:"JSelectPosition"};function Ct(e,l,a,i,p,u){const s=S("JSelectBiz"),o=S("PositionSelectModal"),t=S("a-form-item");return T(),U("div",yt,[P(s,j({onHandleOpen:e.handleOpen,loading:e.loadingEcho},e.attrs,{onChange:l[0]||(l[0]=n=>e.$emit("update:value",n))}),null,16,["onHandleOpen","loading"]),P(t,null,{default:V(()=>[P(o,j({onRegister:e.regModal,onGetSelectResult:e.setValue},e.getBindValue),null,16,["onRegister","onGetSelectResult"])]),_:1})])}const ca=E(mt,[["render",Ct],["__scopeId","data-v-2d8b1795"]]),bt=K({name:"UserSelectModal",components:{BasicModal:be,BasicTable:ve(()=>ye(()=>import("./BasicTable-xCEZpGLb.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79])),{loading:!0})},props:ue(W({},_e),{modalTitle:{type:String,default:"角色选择"}}),emits:["register","getSelectResult"],setup(e,{emit:l,refs:a}){const[i,{closeModal:p}]=me(),u=N(),s={canResize:!1,bordered:!0,size:"small",rowKey:D(e).rowKey},o=Object.assign({},D(e),D(u),s),[{rowSelection:t,indexColumnProps:n,visibleChange:g,getSelectResult:C}]=Se(ke,o),y=_(e.params),r={baseColProps:{xs:24,sm:24,md:24,lg:14,xl:14,xxl:14},actionColOptions:{xs:24,sm:8,md:8,lg:8,xl:8,xxl:8},schemas:[{label:"角色名称",field:"roleName",component:"Input"}]},B=[{title:"角色名称",dataIndex:"roleName",width:240,align:"left"},{title:"角色编码",dataIndex:"roleCode"}];function d(){C((h,f)=>{l("getSelectResult",h,f),p()})}return{config:s,handleOk:d,searchInfo:y,register:i,indexColumnProps:n,visibleChange:g,getRoleList:ke,formConfig:r,getBindValue:o,columns:B,rowSelection:t}}});function St(e,l,a,i,p,u){const s=S("BasicTable"),o=S("BasicModal");return T(),U("div",null,[P(o,j(e.$attrs,{onRegister:e.register,title:e.modalTitle,width:"800px",onOk:e.handleOk,destroyOnClose:"",onVisibleChange:e.visibleChange}),{default:V(()=>[P(s,j({columns:e.columns},e.config,{useSearchForm:!0,formConfig:e.formConfig,api:e.getRoleList,searchInfo:e.searchInfo,rowSelection:e.rowSelection,indexColumnProps:e.indexColumnProps}),null,16,["columns","formConfig","api","searchInfo","rowSelection","indexColumnProps"])]),_:1},16,["onRegister","title","onOk","onVisibleChange"])])}const _t=E(bt,[["render",St]]),Ot=K({name:"JSelectRole",components:{RoleSelectModal:_t,JSelectBiz:Oe},inheritAttrs:!1,props:{value:c.oneOfType([c.string,c.array]),labelKey:{type:String,default:"roleName"},rowKey:{type:String,default:"id"},params:{type:Object,default:()=>{}}},emits:["options-change","change","update:value"],setup(e,{emit:l,refs:a}){const i=_(),[p,{openModal:u}]=Ce(),[s]=Z(e,"value","change",i),o=_([]);let t=pe({value:[],change:!1});const n=_(!1);Y("selectOptions",o),Y("selectValues",t),Y("loadingEcho",n);const g=_(!1),C=N();se(()=>{e.value&&r(),e.value||(t.value=[])}),J(t,()=>{t&&(s.value=t.value)});function y(){g.value=!0,u(!0,{isUpdate:!1})}function r(){let h=e.value?e.value:[];h&&typeof h=="string"&&h!="null"&&h!="undefined"?(s.value=h.split(","),t.value=h.split(",")):t.value=h}function B(h,f){o.value=h,s.value=f,t.value=f,l("update:value",f)}const d=Object.assign({},D(e),D(C));return{state:s,attrs:C,getBindValue:d,selectOptions:o,selectValues:t,loadingEcho:n,tag:g,regModal:p,setValue:B,handleOpen:y}}}),wt={class:"JSelectRole"};function $t(e,l,a,i,p,u){const s=S("JSelectBiz"),o=S("RoleSelectModal"),t=S("a-form-item");return T(),U("div",wt,[P(s,j({onHandleOpen:e.handleOpen,loading:e.loadingEcho},e.attrs),null,16,["onHandleOpen","loading"]),P(t,null,{default:V(()=>[P(o,j({onRegister:e.regModal,onGetSelectResult:e.setValue},e.getBindValue),null,16,["onRegister","onGetSelectResult"])]),_:1})])}const fa=E(Ot,[["render",$t],["__scopeId","data-v-acec8598"]]),{createMessage:he,createErrorModal:pa}=fe(),Tt=K({name:"JImageUpload",components:{LoadingOutlined:Le,UploadOutlined:nt},inheritAttrs:!1,props:{value:c.oneOfType([c.string,c.array]),listType:{type:String,required:!1,default:"picture-card"},text:{type:String,required:!1,default:"上传"},bizPath:{type:String,required:!1,default:"temp"},disabled:{type:Boolean,required:!1,default:!1},fileMax:{type:Number,required:!1,default:1}},emits:["options-change","change","update:value"],setup(e,{emit:l,refs:a}){const i=_([]),p=N(),[u]=Z(e,"value","change",i),s=w=>{if(w.lastIndexOf("\\")>=0){let F=new RegExp("\\\\","g");w=w.replace(F,"/")}return w.substring(w.lastIndexOf("/")+1)},o=Ee(),t=_(!1),n=_(!0);let g=_([]);const C=_(""),y=_(!1),r=le(()=>e.fileMax>1||e.fileMax===0),B=le(()=>e.fileMax===0?!0:g.value.length<e.fileMax);J(()=>e.value,(w,F)=>{w&&w instanceof Array&&(w=w.join(",")),n.value==!0&&d(w)},{immediate:!0});function d(w){if(!w||w.length==0){g.value=[];return}let F=[];w.split(",").forEach(H=>{let Q=Ie(H);F.push({uid:qe(10),name:s(H),status:"done",url:Q,response:{status:"history",message:H}})}),g.value=F}function h(w){if(w.type.indexOf("image")<0)return he.info("请上传图片"),!1}function f({file:w,fileList:F,event:z}){if(n.value=!1,w.status==="error"&&he.error(`${w.name} 上传失败.`),w.status==="done"&&w.response.success===!1){const R=g.value.findIndex(G=>G.uid===w.uid);R!=-1&&g.value.splice(R,1),he.warning(w.response.message);return}let H=[],Q=0;w.status!="uploading"&&(F.forEach(R=>{R.status==="done"&&H.push(R.response.message),R.status!="uploading"&&Q++}),w.status==="removed"&&void 0,Q==F.length&&(u.value=H.join(","),l("update:value",H.join(",")),ie(()=>{n.value=!0})))}function v(w){}function O(w){C.value=w.url||w.thumbUrl,y.value=!0}function k(){if(g.length>0){let w=g[0].url;return Ie(w,null)}}function M(){y.value=!1}return{state:u,attrs:p,previewImage:C,previewVisible:y,uploadFileList:g,multiple:r,headers:o,loading:t,uploadUrl:ze,beforeUpload:h,uploadVisible:B,handlePreview:O,handleCancel:M,handleChange:f}}}),kt={class:"clearfix"},It={key:0},Dt={key:0},Rt={class:"ant-upload-text"},Pt=["src"];function Bt(e,l,a,i,p,u){const s=S("LoadingOutlined"),o=S("UploadOutlined"),t=S("a-button"),n=S("a-upload"),g=S("a-modal");return T(),U("div",kt,[P(n,{listType:e.listType,accept:"image/*",multiple:e.multiple,action:e.uploadUrl,headers:e.headers,data:{biz:e.bizPath},fileList:e.uploadFileList,"onUpdate:fileList":l[0]||(l[0]=C=>e.uploadFileList=C),beforeUpload:e.beforeUpload,disabled:e.disabled,onChange:e.handleChange,onPreview:e.handlePreview},{default:V(()=>[e.uploadVisible?(T(),U("div",It,[e.listType=="picture-card"?(T(),U("div",Dt,[e.loading?(T(),L(s,{key:0})):(T(),L(o,{key:1})),X("div",Rt,ae(e.text),1)])):te("",!0),e.listType=="picture"?(T(),L(t,{key:1,disabled:e.disabled},{default:V(()=>[P(o),de(" "+ae(e.text),1)]),_:1},8,["disabled"])):te("",!0)])):te("",!0)]),_:1},8,["listType","multiple","action","headers","data","fileList","beforeUpload","disabled","onChange","onPreview"]),P(g,{open:e.previewVisible,footer:null,onCancel:l[1]||(l[1]=C=>e.handleCancel())},{default:V(()=>[X("img",{alt:"example",style:{width:"100%"},src:e.previewImage},null,8,Pt)]),_:1},8,["open"])])}const ga=E(Tt,[["render",Bt],["__scopeId","data-v-5c8af726"]]);var je=(e=>(e.Radio="radio",e.RadioButton="radioButton",e.Select="select",e.List="list",e.Switch="switch",e.SelTree="sel_tree",e.CatTree="cat_tree",e.SelSearch="search",e.SelUser="sel_user",e.Checkbox="checkbox",e.ListMulti="list_multi",e.Pca="pca",e.Popup="popup",e.SelDepart="sel_depart",e))(je||{});const Vt=K({name:"JDictSelectTag",inheritAttrs:!1,components:{LoadingOutlined:Le},props:{value:c.oneOfType([c.string,c.number,c.array]),dictCode:c.string,type:c.string,placeholder:c.string,stringToNumber:c.bool,useDicColor:c.bool.def(!1),getPopupContainer:{type:Function,default:e=>e==null?void 0:e.parentNode},showChooseOption:c.bool.def(!0),options:{type:Array,default:[],required:!1},style:c.any},emits:["options-change","change","update:value"],setup(e,{emit:l,refs:a}){const i=_([]),p=N(),[u,,,s]=Z(e,"value","change"),o=Object.assign({},D(e),D(p)),t=_(!1);let n=!0;const g=le(()=>!e.type||e.type==="list"?"select":e.type);se(()=>{e.dictCode&&(t.value=n,n=!1,C().finally(()=>{t.value=n})),e.dictCode||(i.value=e.options)}),J(()=>e.value,()=>{e.value===""&&(l("change",""),ie(()=>s.onFieldChange()))});function C(){return A(this,null,function*(){let{dictCode:d,stringToNumber:h}=e;const f=yield He(d);i.value=f.reduce((v,O)=>{if(O){const k=O.value;v.push(W({label:O.text||O.label,value:h?+k:k,color:O.color},ot(O,["text","value","color"])))}return v},[])})}function y(d){var v,O,k,M;const{mode:h}=D(o);let f;h==="multiple"?(f=(O=(v=d==null?void 0:d.target)==null?void 0:v.value)!=null?O:d,(f==null||f==="")&&(f=[]),Array.isArray(f)&&(f=f.filter(w=>w!=null&&w!==""))):f=(M=(k=d==null?void 0:d.target)==null?void 0:k.value)!=null?M:d,u.value=f,l("update:value",f)}function r(d){var h,f,v,O;u.value=(f=(h=d==null?void 0:d.target)==null?void 0:h.value)!=null?f:d,l("update:value",(O=(v=d==null?void 0:d.target)==null?void 0:v.value)!=null?O:d)}function B(d,h){var f;return typeof h.children=="function"&&((f=h.children()[0])==null?void 0:f.children.toLowerCase().indexOf(d.toLowerCase()))>=0?!0:(h.value||"").toString().toLowerCase().indexOf(d.toLowerCase())>=0}return{state:u,compType:g,attrs:p,loadingEcho:t,getBindValue:o,dictOptions:i,CompTypeEnum:je,handleChange:y,handleChangeRadio:r,handleFilterOption:B}}}),Mt=["title"];function Lt(e,l,a,i,p,u){const s=S("a-radio"),o=S("a-radio-group"),t=S("a-radio-button"),n=S("LoadingOutlined"),g=S("a-input"),C=S("a-select-option"),y=S("a-select");return e.compType===e.CompTypeEnum.Radio?(T(),L(o,j({key:0},e.attrs,{value:e.state,"onUpdate:value":l[0]||(l[0]=r=>e.state=r),onChange:e.handleChangeRadio}),{default:V(()=>[(T(!0),U(ee,null,oe(e.dictOptions,r=>(T(),L(s,{key:`${r.value}`,value:r.value},{default:V(()=>[X("span",{class:re([e.useDicColor&&r.color?"colorText":""]),style:ce({backgroundColor:`${e.useDicColor&&r.color}`})},ae(r.label),7)]),_:2},1032,["value"]))),128))]),_:1},16,["value","onChange"])):e.compType===e.CompTypeEnum.RadioButton?(T(),L(o,j({key:1},e.attrs,{value:e.state,"onUpdate:value":l[1]||(l[1]=r=>e.state=r),buttonStyle:"solid",onChange:e.handleChangeRadio}),{default:V(()=>[(T(!0),U(ee,null,oe(e.dictOptions,r=>(T(),L(t,{key:`${r.value}`,value:r.value},{default:V(()=>[de(ae(r.label),1)]),_:2},1032,["value"]))),128))]),_:1},16,["value","onChange"])):e.compType===e.CompTypeEnum.Select?(T(),U(ee,{key:2},[e.loadingEcho?(T(),L(g,{key:0,readOnly:"",placeholder:"加载中…"},{prefix:V(()=>[P(n)]),_:1})):(T(),L(y,j({key:1,placeholder:e.placeholder},e.attrs,{value:e.state,"onUpdate:value":l[2]||(l[2]=r=>e.state=r),filterOption:e.handleFilterOption,getPopupContainer:e.getPopupContainer,style:e.style,onChange:e.handleChange}),{default:V(()=>[e.showChooseOption?(T(),L(C,{key:0,value:null},{default:V(()=>l[3]||(l[3]=[de("请选择…")])),_:1,__:[3]})):te("",!0),(T(!0),U(ee,null,oe(e.dictOptions,r=>(T(),L(C,{key:`${r.value}`,value:r.value},{default:V(()=>[X("span",{class:re([e.useDicColor&&r.color?"colorText":""]),style:ce({backgroundColor:`${e.useDicColor&&r.color}`}),title:r.label},ae(r.label),15,Mt)]),_:2},1032,["value"]))),128))]),_:1},16,["placeholder","value","filterOption","getPopupContainer","style","onChange"]))],64)):te("",!0)}const ha=E(Vt,[["render",Lt],["__scopeId","data-v-a0d047e5"]]),jt=K({name:"JEditor",inheritAttrs:!1,components:{Tinymce:dt},props:{value:c.string.def(""),disabled:c.bool.def(!1),autoFocus:c.bool.def(!0)},emits:["change","update:value"],setup(e,{emit:l,attrs:a}){const i=le(()=>Object.assign({},e,a)),p=st.useInjectFormItemContext();function u(s){l("change",s),l("update:value",s),ie(()=>{p==null||p.onFieldChange()})}return{bindProps:i,onChange:u}}});function Jt(e,l,a,i,p,u){const s=S("Tinymce");return T(),L(s,j(e.bindProps,{onChange:e.onChange}),null,16,["onChange"])}const va=E(jt,[["render",Jt]]),Ft=K({name:"JSelectInput",props:{options:c.array.def(()=>[])},emits:["change","update:value"],setup(e,{emit:l,attrs:a}){const i=_([]);J(()=>e.options,()=>{i.value=[...e.options]},{deep:!0,immediate:!0});const p=le(()=>Object.assign({showSearch:!0},e,a,{options:i.value}));function u(...t){o(t[0]),l("change",...t),l("update:value",t[0])}function s(t){let n=i.value.findIndex(g=>g.value.toString()===t.toString())!==-1;!n&&t?(o(t),i.value.push({value:t,searchAdd:!0})):n&&u(t)}function o(t=""){let n=[];i.value.forEach((g,C)=>{var y;g.searchAdd&&((y=g.value)!=null?y:"").toString()!==t.toString()&&n.push(C)});for(let g of n.reverse())i.value.splice(g,1)}return{bindProps:p,onChange:u,onSearch:s}}});function Ut(e,l,a,i,p,u){const s=S("a-select");return T(),L(s,j(e.bindProps,{onChange:e.onChange,onSearch:e.onSearch}),null,16,["onChange","onSearch"])}const ma=E(Ft,[["render",Ut]]),{createMessage:Be,createErrorModal:ya}=fe(),Kt=K({name:"JCategorySelect",components:{},inheritAttrs:!1,props:{value:c.oneOfType([c.string,c.array]),placeholder:{type:String,default:"请选择",required:!1},disabled:{type:Boolean,default:!1,required:!1},condition:{type:String,default:"",required:!1},multiple:{type:[Boolean,String],default:!1},loadTriggleChange:{type:Boolean,default:!1,required:!1},pid:{type:String,default:"",required:!1},pcode:{type:String,default:"",required:!1},back:{type:String,default:"",required:!1}},emits:["options-change","change","update:value"],setup(e,{emit:l,refs:a}){const i=_([]),p=_([]),u=_(),s=N(),[o,,,t]=Z(e,"value","change",i);J(()=>e.value,()=>{g()},{deep:!0,immediate:!0}),J(()=>e.pcode,()=>{n()},{deep:!0,immediate:!0});function n(){let v={pid:e.pid,pcode:e.pcode?e.pcode:"0",condition:e.condition};De(v).then(O=>{if(O&&O.length>0){for(let k of O)k.value=k.key,k.leaf==!1?k.isLeaf=!1:k.leaf==!0&&(k.isLeaf=!0);p.value=O}})}function g(){!e.value||e.value=="0"?e.multiple?u.value=[]:u.value={value:null,label:null}:Ge({ids:e.value}).then(v=>{let O=e.value.split(",");u.value=v.map((k,M)=>({key:O[M],value:O[M],label:k})),e.multiple||(u.value=u.value[0]),C(v[0])})}function C(v){!e.multiple&&e.loadTriggleChange&&y(e.value,v)}function y(v,O){let k={};e.back&&(k[e.back]=O),l("change",v,k),l("update:value",v)}function r(v){let O=v.dataRef;return new Promise(k=>{if(v.children&&v.children.length>0){k();return}let M=O.key,w={pid:M,condition:e.condition};De(w).then(F=>{if(F){for(let z of F)z.value=z.key,z.leaf==!1?z.isLeaf=!1:z.leaf==!0&&(z.isLeaf=!0);B(M,F,p.value),k()}})})}function B(v,O,k){if(k&&k.length>0)for(let M of k)if(M.key==v){!O||O.length==0?M.isLeaf=!0:M.children=O;break}else B(v,O,M.children)}function d(v){if(!v)l("change",""),u.value="",l("update:value","");else if(Array.isArray(v)){let O=[],k=v.map(M=>(O.push(M.label),M.value));y(k.join(","),O.join(",")),u.value=v}else y(v.value,v.label),u.value=v;ie(()=>{t==null||t.onFieldChange()})}function h(){return p}function f(){let v=e.condition;return new Promise((O,k)=>{if(!v)O();else try{let M=JSON.parse(v);typeof M=="object"&&M?O():(Be.error("组件JTreeSelect-condition传值有误，需要一个json字符串!"),k())}catch(M){Be.error("组件JTreeSelect-condition传值有误，需要一个json字符串!"),k()}})}return{state:o,attrs:s,onChange:d,treeData:p,treeValue:u,asyncLoadTreeData:r}}});function At(e,l,a,i,p,u){const s=S("a-tree-select");return T(),L(s,{allowClear:"",labelInValue:"",style:{width:"100%"},disabled:e.disabled,dropdownStyle:{maxHeight:"400px",overflow:"auto"},showCheckedStrategy:"SHOW_ALL",placeholder:e.placeholder,loadData:e.asyncLoadTreeData,value:e.treeValue,treeData:e.treeData,multiple:e.multiple,onChange:e.onChange},null,8,["disabled","placeholder","loadData","value","treeData","multiple","onChange"])}const Ca=E(Kt,[["render",At]]),{createMessage:ba,createErrorModal:Sa}=fe(),Nt=K({name:"JSelectMultiple",components:{},inheritAttrs:!1,props:{value:c.oneOfType([c.string,c.array]),placeholder:{type:String,default:"请选择",required:!1},readOnly:{type:Boolean,required:!1,default:!1},options:{type:Array,default:()=>[],required:!1},triggerChange:{type:Boolean,required:!1,default:!0},spliter:{type:String,required:!1,default:","},popContainer:{type:String,default:"",required:!1},dictCode:{type:String,required:!1},disabled:{type:Boolean,default:!1},useDicColor:{type:Boolean,default:!1}},emits:["options-change","change","input","update:value"],setup(e,{emit:l,refs:a}){const i=_([]),p=_(e.value?e.value.split(e.spliter):[]),u=_([]),s=N(),[o,,,t]=Z(e,"value","change",i);Xe(()=>{e.dictCode?C():u.value=e.options}),J(()=>e.dictCode,()=>{e.dictCode?C():u.value=e.options}),J(()=>e.value,r=>{r?p.value=e.value.split(e.spliter):p.value=[]}),J(()=>e.options,()=>{e.dictCode||(u.value=e.options)});function n(r){e.triggerChange?(l("change",r.join(e.spliter)),l("update:value",r.join(e.spliter))):(l("input",r.join(e.spliter)),l("update:value",r.join(e.spliter))),ie(()=>{t==null||t.onFieldChange()})}function g(r){return e.popContainer?We(r,e.popContainer):r==null?void 0:r.parentNode}function C(){let r=e.dictCode||"";r.indexOf(",")>0&&r.indexOf(" ")>0&&(r=encodeURI(r)),Ve(r).then(B=>{B?u.value=B.map(d=>({value:d.value,label:d.text,color:d.color})):u.value=[]})}function y(r,B){return B.children()[0].children.toLowerCase().indexOf(r.toLowerCase())>=0}return{state:o,attrs:s,dictOptions:u,onChange:n,arrayValue:p,getParentContainer:g,filterOption:y}}});function Et(e,l,a,i,p,u){const s=S("a-select-option"),o=S("a-select");return T(),L(o,{value:e.arrayValue,onChange:e.onChange,mode:"multiple","filter-option":e.filterOption,disabled:e.disabled,placeholder:e.placeholder,allowClear:"",getPopupContainer:e.getParentContainer},{default:V(()=>[(T(!0),U(ee,null,oe(e.dictOptions,(t,n)=>(T(),L(s,{key:n,getPopupContainer:e.getParentContainer,value:t.value},{default:V(()=>[X("span",{class:re([e.useDicColor&&t.color?"colorText":""]),style:ce({backgroundColor:`${e.useDicColor&&t.color}`})},ae(t.text||t.label),7)]),_:2},1032,["getPopupContainer","value"]))),128))]),_:1},8,["value","onChange","filter-option","disabled","placeholder","getPopupContainer"])}const _a=E(Nt,[["render",Et],["__scopeId","data-v-20e3622c"]]),Oa=K({__name:"JSwitch",props:{value:c.oneOfType([c.string,c.number]),options:c.array.def(()=>["Y","N"]),labelOptions:c.array.def(()=>["是","否"]),query:c.bool.def(!1),disabled:c.bool.def(!1)},emits:["change","update:value"],setup(e,{emit:l}){const{prefixCls:a}=Me("j-switch"),i=e,p=N(),u=l,s=_(!1),[o]=Z(i,"value","change");J(()=>i.value,y=>{i.query||(!y&&!i.options.includes(y)?(s.value=!1,C(i.options[1])):s.value=i.options[0]==y)},{immediate:!0});const t=le(()=>{let y=[];return y.push({value:i.options[0],label:i.labelOptions[0]}),y.push({value:i.options[1],label:i.labelOptions[1]}),y});function n(y){let r=y===!1?i.options[1]:i.options[0];C(r)}function g(y){C(y)}function C(y){u("change",y),u("update:value",y)}return(y,r)=>{const B=S("a-select"),d=S("a-switch");return T(),U("div",{class:re(D(a))},[e.query?(T(),L(B,j({key:0,value:D(o),"onUpdate:value":r[0]||(r[0]=h=>Ze(o)?o.value=h:null),options:t.value,disabled:e.disabled,style:{width:"100%"}},D(p),{onChange:g}),null,16,["value","options","disabled"])):(T(),L(d,j({key:1,checked:s.value,"onUpdate:checked":r[1]||(r[1]=h=>s.value=h),disabled:e.disabled},D(p),{onChange:n}),null,16,["checked","disabled"]))],2)}}});const wa=K({__name:"JTreeDict",props:{value:c.string.def(""),field:c.string.def("id"),parentCode:c.string.def(""),async:c.bool.def(!1)},emits:["change","update:value"],setup(e,{emit:l}){const{prefixCls:a}=Me("j-tree-dict"),i=e,p=N(),u=l,s=_([]),o=_(null);J(()=>i.value,()=>t(),{deep:!0,immediate:!0}),J(()=>i.parentCode,()=>n(),{deep:!0,immediate:!0});function t(){return A(this,null,function*(){if(!i.value||i.value=="0")o.value={value:null,label:null};else{let d={field:i.field,val:i.value},h=yield x.get({url:"/sys/category/loadOne",params:d});o.value={value:i.value,label:h.name}}})}function n(){return A(this,null,function*(){let d={async:i.async,pcode:i.parentCode},h=yield x.get({url:"/sys/category/loadTreeRoot",params:d});s.value=[...h],y(h)})}function g(d){return A(this,null,function*(){if(!i.async||d.dataRef.children)return Promise.resolve();let h=d.dataRef.key,f={pid:h},v=yield x.get({url:"/sys/category/loadTreeChildren",params:f});return y(v),C(h,v,s.value),s.value=[...s.value],Promise.resolve()})}function C(d,h,f){if(f&&f.length>0)for(let v of f)if(v.key==d){!h||h.length==0?v.leaf=!0:v.children=h;break}else C(d,h,v.children)}function y(d){let h=i.field=="code"?"code":"key";for(let f of d)f.value=f[h],f.isLeaf=f.leaf,f.children&&f.children.length>0&&y(f.children)}function r(d){B(d?d.value:""),o.value=d}function B(d){u("change",d),u("update:value",d)}return(d,h)=>(T(),L(D(it),j({class:D(a),value:o.value,treeData:s.value,loadData:g,allowClear:"",labelInValue:"",dropdownStyle:{maxHeight:"400px",overflow:"auto"},style:{width:"100%"}},D(p),{onChange:r}),null,16,["class","value","treeData"]))}}),qt=K({name:"JCheckbox",props:{value:c.oneOfType([c.string,c.number]),dictCode:c.string,useDicColor:c.bool.def(!1),options:{type:Array,default:()=>[]}},emits:["change","update:value"],setup(e,{emit:l}){const a=N(),i=_([]),p=_([]);se(()=>{let n=e.value;!n&&n!==0?p.value=[]:(n=n+"",p.value=n.split(",")),(e.value===""||e.value===void 0)&&(p.value=[])}),se(()=>{e&&u()});function u(){return A(this,null,function*(){if(e.options&&e.options.length>0){i.value=e.options;return}e.dictCode&&s()})}function s(){let n=e.dictCode||"";n.indexOf(",")>0&&n.indexOf(" ")>0&&(n=encodeURI(n)),Ve(n).then(g=>{g?i.value=g.map(C=>({value:C.value,label:C.text,color:C.color})):i.value=[]})}function o(n){l("update:value",n.join(",")),l("change",n.join(","))}return{checkboxArray:p,checkOptions:i,attrs:a,handleChange:o,getDicColor:n=>{if(e.useDicColor){const g=i.value.find(C=>C.value==n);if(g)return g.color}return null}}}});function zt(e,l,a,i,p,u){const s=S("a-checkbox-group");return T(),L(s,j(e.attrs,{value:e.checkboxArray,"onUpdate:value":l[0]||(l[0]=o=>e.checkboxArray=o),options:e.checkOptions,onChange:e.handleChange}),{label:V(({label:o,value:t})=>[X("span",{class:re([e.useDicColor&&e.getDicColor(t)?"colorText":""]),style:ce({backgroundColor:`${e.getDicColor(t)}`})},ae(o),7)]),_:1},16,["value","options","onChange"])}const $a=E(qt,[["render",zt],["__scopeId","data-v-6461f313"]]);const Ta=K({__name:"JTreeSelect",props:{value:c.string.def(""),placeholder:c.string.def("请选择"),dict:c.string.def("id"),parentCode:c.string.def(""),pidField:c.string.def("pid"),pidValue:c.string.def(""),hasChildField:c.string.def(""),converIsLeafVal:c.integer.def(1),condition:c.string.def(""),multiple:c.bool.def(!1),loadTriggleChange:c.bool.def(!1),reload:c.number.def(1),url:c.string.def(""),params:c.object.def({}),treeCheckAble:c.bool.def(!1),hiddenNodeKey:c.string.def("")},emits:["change","update:value"],setup(e,{emit:l}){const a=e,i=N(),{t:p}=Ye(),u=l,s=xe(),{createMessage:o}=fe(),t=_([]),n=_(null),g=_(""),C=_(""),y=_(""),r=_(!0);J(()=>a.value,()=>B(),{deep:!0,immediate:!0}),J(()=>a.dict,()=>{h(),f()}),J(()=>a.hiddenNodeKey,()=>{var m;(m=t.value)!=null&&m.length&&a.hiddenNodeKey&&(G(t.value),t.value=[...t.value])}),J(()=>a.reload,()=>A(null,null,function*(){t.value=[],r.value=!1,ie(()=>{r.value=!0}),yield f()}),{immediate:!1});function B(){return A(this,null,function*(){if(!a.value||a.value=="0")a.multiple?n.value=[]:n.value={label:null,value:null};else if(a.url)Q();else{if(a.value){if(ge(n.value)){let I=!1;if(I=(ge(a.value)?a.value:a.value.split(",")).every(q=>!!n.value.find(Je=>Je.value===q)),I)return}else if(Re(n.value)&&D(n).label!=null&&a.value==D(n).value)return}let m={key:a.value},b=yield x.get({url:`/sys/dict/loadDictItem/${a.dict}`,params:m},{isTransformResponse:!1});if(b.success){if(a.multiple){let I=a.value.split(",");n.value=b.result.map(($,q)=>({key:I[q],value:I[q],label:v($)}))}else n.value={key:a.value,value:a.value,label:v(b.result[0])};d(b.result[0])}}})}function d(m){!a.multiple&&a.loadTriggleChange&&u("change",a.value,m)}function h(){var b;let m=(b=a.dict)==null?void 0:b.split(",");g.value=m[0],C.value=m[1],y.value=m[2]}function f(){return A(this,null,function*(){let m={pid:a.pidValue,pidField:a.pidField,hasChildField:a.hasChildField,converIsLeafVal:a.converIsLeafVal,condition:a.condition,tableName:D(g),text:D(C),code:D(y)},b=yield x.get({url:"/sys/dict/loadTreeData",params:m},{isTransformResponse:!1});if(b.success&&b.result){for(let I of b.result)I.title=v(I.title),I.value=I.key,I.isLeaf=!!I.leaf;G(b.result),t.value=[...b.result]}})}function v(m){return m.includes("t('")&&p?new Function("t",`return ${m}`)(p):m}function O(m){return A(this,null,function*(){if(m.dataRef.children||a.url)return Promise.resolve();let b=m.dataRef.key,I={pid:b,pidField:a.pidField,hasChildField:a.hasChildField,converIsLeafVal:a.converIsLeafVal,condition:a.condition,tableName:D(g),text:D(C),code:D(y)},$=yield x.get({url:"/sys/dict/loadTreeData",params:I},{isTransformResponse:!1});if($.success){for(let q of $.result)q.title=v(q.title),q.value=q.key,q.isLeaf=!!q.leaf;G($.result),k(b,$.result,t.value),t.value=[...t.value]}return Promise.resolve()})}function k(m,b,I){if(I&&I.length>0)for(let $ of I)if($.key==m){!b||b.length==0?$.isLeaf=!0:$.children=b;break}else k(m,b,$.children)}function M(m){m?m instanceof Array?w(m.map(b=>b.value).join(",")):w(m.value):w(""),ge(m)&&m.forEach(b=>{if(b.label===void 0&&b.value!=null){const I=n.value.find($=>$.value===b.value);I&&(b.label=I.label)}}),n.value=m}function w(m){u("change",m),u("update:value",m)}function F(m){}function z(){let m=a.condition;return new Promise((b,I)=>{if(!m)b();else try{let $=JSON.parse(m);typeof $=="object"&&$?b():(o.error("组件JTreeSelect-condition传值有误，需要一个json字符串!"),I())}catch($){o.error("组件JTreeSelect-condition传值有误，需要一个json字符串!"),I()}})}J(()=>a.url,m=>A(null,null,function*(){m&&(yield H())}));function H(){return A(this,null,function*(){let m=a.url,b=a.params,I=yield x.get({url:m,params:b},{isTransformResponse:!1});if(I.success&&I.result){for(let $ of I.result)$.title=v($.title),$.key=$.value,$.isLeaf=!!$.leaf;G(I.result),t.value=[...I.result]}})}function Q(){let m=t.value,b=[];R(m,b),b.length>0&&(n.value=b,d(b[0]))}function R(m,b){let I=a.value;if(m&&m.length)for(let $ of m)I===$.value?b.push({key:$.key,value:$.value,label:$.label||$.title}):R($.children,b)}function G(m){if(a.hiddenNodeKey&&(m!=null&&m.length)){for(let b=0,I=m.length;b<I;b++)if(m[b].key==a.hiddenNodeKey){m.splice(b,1),b--,I--;return}}}const ne=le(()=>{const m=[];if(Re(s))for(const b of Object.keys(s))m.push(b);return m});return z().then(()=>{h(),f(),B()}),(m,b)=>{const I=S("a-tree-select");return r.value?(T(),L(I,j({key:0,allowClear:"",labelInValue:"",style:{width:"100%"},getPopupContainer:$=>$==null?void 0:$.parentNode,dropdownStyle:{maxHeight:"400px",overflow:"auto"},placeholder:e.placeholder,loadData:O,value:n.value,treeData:t.value,multiple:e.multiple},D(i),{onChange:M,onSearch:F,"tree-checkable":e.treeCheckAble}),et({_:2},[oe(ne.value,$=>({name:$,fn:V(q=>[tt(m.$slots,$,at(lt(q)))])}))]),1040,["getPopupContainer","placeholder","value","treeData","multiple","tree-checkable"])):te("",!0)}}}),Ht=K({name:"UserSelectByDepModal",components:{BasicModal:be,BasicTree:ct,BasicTable:ve(()=>ye(()=>import("./BasicTable-xCEZpGLb.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79])),{loading:!0})},props:ue(W({},_e),{modalTitle:{type:String,default:"部门用户选择"}}),emits:["register","getSelectResult"],setup(e,{emit:l,refs:a}){const i=_(),p=_(),[u,{closeModal:s}]=me(R=>A(null,null,function*(){yield w()})),o=N(),t=_([]),n=_([]),g=_([]),C={},y={columns:[{title:"用户账号",dataIndex:"username",width:180},{title:"用户姓名",dataIndex:"realname",width:180},{title:"性别",dataIndex:"sex_dictText",width:80},{title:"手机号码",dataIndex:"phone"}],useSearchForm:!0,canResize:!1,showIndexColumn:!1,striped:!0,bordered:!0,size:"small",formConfig:{baseColProps:{xs:24,sm:8,md:6,lg:8,xl:6,xxl:10},actionColOptions:{xs:24,sm:12,md:12,lg:12,xl:8,xxl:8},schemas:[{label:"账号",field:"username",component:"Input"}],resetFunc:H}},r=Object.assign({},D(e),D(o),y),[{rowSelection:B,visibleChange:d,indexColumnProps:h,getSelectResult:f,reset:v}]=Se(O,r);function O(R){return R=M(R),Qe(W({},R))}function k(R){return R=M(R),ft(W({},R))}function M(R){return e!=null&&e.params?W(W({},R),e.params):R}function w(){k().then(R=>{R&&(t.value=R)})}function F(R){return A(this,null,function*(){try{const G=yield k({pid:R.eventKey}),ne=D(p);ne&&(ne.updateNodeByKey(R.eventKey,{children:G}),ne.setExpandedKeys([R.eventKey,...ne.getExpandedKeys()]))}catch(G){}return Promise.resolve()})}function z(R){R[0]!=null&&(D(n)[0]!==R[0]&&(n.value=[R[0]]),C.departId=D(n).join(","),i.value.reload())}function H(){return A(this,null,function*(){n.value=[],C.departId="",v()})}function Q(){f((R,G)=>{l("getSelectResult",R,G),s()})}return{handleOk:Q,searchInfo:C,register:u,indexColumnProps:h,visibleChange:d,getBindValue:r,rowSelection:B,departTree:t,selectedDepIds:n,expandedKeys:g,treeRef:p,tableRef:i,getTableList:O,onDepSelect:z,loadChildrenTreeData:F}}});function Gt(e,l,a,i,p,u){const s=S("BasicTree"),o=S("a-card"),t=S("a-col"),n=S("BasicTable"),g=S("a-row"),C=S("BasicModal");return T(),L(C,j(e.$attrs,{onRegister:e.register,title:e.modalTitle,width:"1200px",onOk:e.handleOk,destroyOnClose:"",onVisibleChange:e.visibleChange}),{default:V(()=>[P(g,{gutter:10},{default:V(()=>[P(t,{md:7,sm:24},{default:V(()=>[P(o,{style:{minHeight:"613px",overflow:"auto"}},{default:V(()=>[P(s,{ref:"treeRef",style:{minWidth:"250px"},selectable:"",onSelect:e.onDepSelect,"load-data":e.loadChildrenTreeData,treeData:e.departTree,selectedKeys:e.selectedDepIds,expandedKeys:e.expandedKeys,clickRowToExpand:!1},null,8,["onSelect","load-data","treeData","selectedKeys","expandedKeys"])]),_:1})]),_:1}),P(t,{md:17,sm:24},{default:V(()=>[P(o,{style:{minHeight:"613px",overflow:"auto"}},{default:V(()=>[P(n,j({ref:"tableRef"},e.getBindValue,{searchInfo:e.searchInfo,api:e.getTableList,rowSelection:e.rowSelection}),null,16,["searchInfo","api","rowSelection"])]),_:1})]),_:1})]),_:1})]),_:1},16,["onRegister","title","onOk","onVisibleChange"])}const Wt=E(Ht,[["render",Gt]]),Yt=K({name:"JSelectUserByDept",components:{UserSelectByDepModal:Wt,JSelectBiz:Oe},inheritAttrs:!1,props:{value:c.oneOfType([c.string,c.array]),rowKey:{type:String,default:"username"},labelKey:{type:String,default:"realname"}},emits:["options-change","change","update:value"],setup(e,{emit:l,refs:a}){const i=_(),[p,{openModal:u}]=Ce(),[s]=Z(e,"value","change",i),o=_([]);let t=pe({value:[],change:!1});const n=_(!1);Y("selectOptions",o),Y("selectValues",t),Y("loadingEcho",n);const g=_(!1),C=N();se(()=>{r()}),J(t,()=>{t&&(s.value=t.value)});function y(){g.value=!0,u(!0,{isUpdate:!1})}function r(){let f=e.value?e.value:[];f&&typeof f=="string"&&f!="null"&&f!="undefined"?(s.value=f.split(","),t.value=f.split(",")):t.value=f}function B(f,v){o.value=f,s.value=v,t.value=v,l("update:value",v),l("options-change",f)}function d(f){l("update:value",f)}const h=Object.assign({},D(e),D(C));return{state:s,attrs:C,selectOptions:o,getBindValue:h,selectValues:t,loadingEcho:n,tag:g,regModal:p,setValue:B,handleOpen:y,handleChange:d}}});function Qt(e,l,a,i,p,u){const s=S("JSelectBiz"),o=S("UserSelectByDepModal");return T(),U("div",null,[P(s,j({onChange:e.handleChange,onHandleOpen:e.handleOpen,loading:e.loadingEcho},e.attrs),null,16,["onChange","onHandleOpen","loading"]),P(o,j({rowKey:e.rowKey,onRegister:e.regModal,onGetSelectResult:e.setValue},e.getBindValue),null,16,["rowKey","onRegister","onGetSelectResult"])])}const ka=E(Yt,[["render",Qt],["__scopeId","data-v-a5d65088"]]),Xt=K({name:"JAddInput",props:{value:c.string.def(""),min:c.integer.def(1)},emits:["change","update:value"],setup(e,{emit:l}){const a=pe({params:[]}),i=o=>{let t=a.params.indexOf(o);t!==-1&&a.params.splice(t,1),s()},p=()=>{a.params.push({label:"",value:""}),s()};se(()=>{u()});function u(){if(a.params=[],e.value&&e.value.indexOf("{")==0){let o=JSON.parse(e.value);Object.keys(o).forEach(t=>{a.params.push({label:t,value:o[t]})})}}function s(){let o={};a.params.length>0&&a.params.forEach(t=>{o[t.label]=t.value}),l("change",Pe(o)?"":JSON.stringify(o)),l("update:value",Pe(o)?"":JSON.stringify(o))}return{dynamicInput:a,emitChange:s,remove:i,add:p}},components:{MinusCircleOutlined:ut,PlusOutlined:rt}});function Zt(e,l,a,i,p,u){const s=S("a-input"),o=S("MinusCircleOutlined"),t=S("PlusOutlined"),n=S("a-button");return T(),U(ee,null,[(T(!0),U(ee,null,oe(e.dynamicInput.params,(g,C)=>(T(),U("div",{key:C,style:{display:"flex"}},[P(s,{placeholder:"请输入参数key",value:g.label,"onUpdate:value":y=>g.label=y,style:{width:"30%","margin-bottom":"5px"},onInput:e.emitChange},null,8,["value","onUpdate:value","onInput"]),P(s,{placeholder:"请输入参数value",value:g.value,"onUpdate:value":y=>g.value=y,style:{width:"30%",margin:"0 0 5px 5px"},onInput:e.emitChange},null,8,["value","onUpdate:value","onInput"]),e.dynamicInput.params.length>e.min?(T(),L(o,{key:0,class:"dynamic-delete-button",onClick:y=>e.remove(g),style:{width:"50px"}},null,8,["onClick"])):te("",!0)]))),128)),X("div",null,[P(n,{type:"dashed",style:{width:"60%"},onClick:e.add},{default:V(()=>[P(t),l[0]||(l[0]=de(" 新增 "))]),_:1,__:[0]},8,["onClick"])])],64)}const Ia=E(Xt,[["render",Zt],["__scopeId","data-v-aea2876b"]]);export{je as C,ca as J,ha as _,fa as a,ga as b,va as c,ma as d,Ca as e,Oa as f,wa as g,$a as h,Ta as i,Ia as j,_a as o,ka as t};
