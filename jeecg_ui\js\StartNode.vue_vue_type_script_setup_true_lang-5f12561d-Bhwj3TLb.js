import{d as r,aB as o,ar as t,u as p,aE as a}from"./vue-vendor-dy9k-Yad.js";import"./NodeStyle-59980363-DzErCCzZ.js";import"./index-9c51646a-BviQbLw-.js";import"./antd-vue-vendor-me9YkNVC.js";import{x as m}from"./NodeContainer.vue_vue_type_style_index_0_lang-60bcf3b8-DFhy0o3x.js";import"./NodeKV.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const x=r({__name:"StartNode",props:{node:{type:Object,required:!0},graph:{type:Object,required:!0}},setup(i){return(e,n)=>(t(),o(p(m),a(e.$props,{hideAction:""}),null,16))}});export{x as l};
