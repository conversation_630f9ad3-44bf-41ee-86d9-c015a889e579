import{d as a,ag as p,aB as n,ar as c,aE as l,aD as f,at as d,k as u}from"./vue-vendor-dy9k-Yad.js";import{B,u as g}from"./index-JbqXEynz.js";import"./index-L3cSIXth.js";import{B as _}from"./BasicForm-DBcXiHk0.js";import{u as w}from"./useForm-CgkFTrrO.js";import{a as D}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const e=[{field:"field1",component:"Input",label:"字段1",colProps:{span:12},defaultValue:"111"},{field:"field2",component:"Input",label:"字段2",colProps:{span:12}}],F=a({components:{BasicDrawer:B,BasicForm:_},setup(){const[o,{setFieldsValue:t}]=w({labelWidth:120,schemas:e,showActionButtonGroup:!1,actionColOptions:{span:24}}),[i]=g(r=>{t({field2:r.data,field1:r.info})});return{register:i,schemas:e,registerForm:o}}});function h(o,t,i,r,$,C){const m=p("BasicForm"),s=p("BasicDrawer");return c(),n(s,l(o.$attrs,{onRegister:o.register,title:"Drawer Title",width:"50%"}),{default:f(()=>[d("div",null,[u(m,{onRegister:o.registerForm},null,8,["onRegister"])])]),_:1},16,["onRegister"])}const bo=D(F,[["render",h]]);export{bo as default};
