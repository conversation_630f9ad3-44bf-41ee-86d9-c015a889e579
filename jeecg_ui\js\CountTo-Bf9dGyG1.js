import{d as w,f as d,w as y,o as h,aq as g,ar as B,au as T}from"./vue-vendor-dy9k-Yad.js";import{a as V}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const C={class:"count-to"},D=w({__name:"CountTo",props:{start:{default:0},end:{},duration:{default:2e3},decimals:{default:0},separator:{default:","},prefix:{default:""},suffix:{default:""},autoplay:{type:Boolean,default:!0}},setup(f,{expose:m}){const t=f,o=d(""),c=d(t.start),n=e=>{const a=e.toFixed(t.decimals).split(".");t.separator&&(a[0]=a[0].replace(/\B(?=(\d{3})+(?!\d))/g,t.separator));const u=a.join(".");return t.prefix+u+t.suffix},_=e=>1-Math.pow(1-e,4),r=()=>{const e=Date.now(),s=t.start,a=t.end,u=t.duration,i=()=>{const x=Date.now()-e,l=Math.min(x/u,1),v=_(l),p=s+(a-s)*v;c.value=p,o.value=n(p),l<1?requestAnimationFrame(i):(c.value=a,o.value=n(a))};i()};return y(()=>t.end,()=>{t.autoplay&&r()}),h(()=>{o.value=n(t.start),t.autoplay&&setTimeout(()=>{r()},100)}),m({start:r}),(e,s)=>(B(),g("span",C,T(o.value),1))}}),F=V(D,[["__scopeId","data-v-29cb3fd8"]]);export{F as default};
