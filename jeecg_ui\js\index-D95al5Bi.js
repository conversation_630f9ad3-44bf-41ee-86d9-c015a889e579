var f=(d,n,t)=>new Promise((s,o)=>{var p=e=>{try{r(t.next(e))}catch(i){o(i)}},m=e=>{try{r(t.throw(e))}catch(i){o(i)}},r=e=>e.done?s(e.value):Promise.resolve(e.value).then(p,m);r((t=t.apply(d,n)).next())});import{d as b,aq as g,ar as w,k as h,aD as x,u as l}from"./vue-vendor-dy9k-Yad.js";import{u as _}from"./index-BkGZ5fiW.js";import{u as I,a as P}from"./index-CCWaWN5g.js";import{Q as T}from"./componentMap-Bkie1n3v.js";import v from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const k={class:"p-4"},z=b({__name:"index",setup(d){const{createMessage:n}=I(),t=[{title:"发送者",dataIndex:"sender",width:120,resizable:!0},{title:"关联产品信息",dataIndex:"productInfo",width:200,resizable:!0},{title:"留言内容",dataIndex:"content",width:300,resizable:!0,ellipsis:!0},{title:"消息来源",dataIndex:"source",width:120,resizable:!0},{title:"发送时间",dataIndex:"sendTime",width:180,resizable:!0}],s=[{field:"source",label:"消息来源",component:"Select",componentProps:{placeholder:"请选择消息来源",options:[{label:"网站留言",value:"website"},{label:"微信客服",value:"wechat"},{label:"电话咨询",value:"phone"},{label:"邮件咨询",value:"email"}]},colProps:{span:6}},{field:"status",label:"消息状态",component:"Select",componentProps:{placeholder:"请选择消息状态",options:[{label:"未读",value:"unread"},{label:"已读",value:"read"},{label:"已回复",value:"replied"}]},colProps:{span:6}},{field:"timeRange",label:"时间区间",component:"RangePicker",componentProps:{showTime:!0,format:"YYYY-MM-DD HH:mm:ss",placeholder:["开始时间","结束时间"]},colProps:{span:6}}],o=[{id:"1",sender:"张三",productInfo:"智能手机 iPhone 15",content:"请问这款手机有什么颜色可以选择？价格如何？",source:"网站留言",sendTime:"2024-01-15 10:30:00",status:"unread"},{id:"2",sender:"李四",productInfo:"笔记本电脑 MacBook Pro",content:"这款电脑的配置怎么样？适合做设计工作吗？",source:"微信客服",sendTime:"2024-01-15 14:20:00",status:"read"},{id:"3",sender:"王五",productInfo:"无线耳机 AirPods Pro",content:"耳机的降噪效果如何？电池续航时间多长？",source:"电话咨询",sendTime:"2024-01-16 09:15:00",status:"replied"},{id:"4",sender:"赵六",productInfo:"智能手表 Apple Watch",content:"手表支持哪些运动模式？防水等级是多少？",source:"邮件咨询",sendTime:"2024-01-16 16:45:00",status:"unread"},{id:"5",sender:"钱七",productInfo:"平板电脑 iPad Air",content:"平板的屏幕尺寸多大？支持手写笔吗？",source:"网站留言",sendTime:"2024-01-17 11:30:00",status:"read"}],p=a=>f(null,null,function*(){return yield new Promise(c=>setTimeout(c,500)),{items:o,total:o.length}}),[m,{reload:r}]=_({api:p,columns:t,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,inset:!0,maxHeight:478,actionColumn:{width:160,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"},rowKey:"id",formConfig:{labelWidth:64,size:"large",schemas:s}});function e(a){n.info(`回复给 ${a.sender} 的留言功能待开发`)}function i(a){n.success(`已删除 ${a.sender} 的留言`),r()}return(a,c)=>(w(),g("div",k,[h(l(v),{onRegister:l(m)},{action:x(({record:u})=>[h(l(T),{actions:[{label:"回复",icon:"ant-design:message-outlined",onClick:e.bind(null,u)},{label:"删除",icon:"ant-design:delete-outlined",color:"error",popConfirm:{title:"确定要删除这条留言吗？",confirm:i.bind(null,u)}}]},null,8,["actions"])]),_:1},8,["onRegister"])]))}}),Se=P(z,[["__scopeId","data-v-390a4e7a"]]);export{Se as default};
