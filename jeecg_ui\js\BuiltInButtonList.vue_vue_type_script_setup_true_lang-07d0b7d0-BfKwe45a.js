import{d as U,e as g,f as $,r as J,n as Z,ag as C,aB as E,ar as K,u as r,aD as s,k as d,aJ as Q,aK as W,at as X,G as Y}from"./vue-vendor-dy9k-Yad.js";import{useListPage as ee}from"./useListPage-Soxgnx9a.js";import"./index-BkGZ5fiW.js";import{B as R}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{j as m,ac as te,ad as oe}from"./index-CCWaWN5g.js";import{u as ne}from"./useForm-CgkFTrrO.js";import le from"./BasicTable-xCEZpGLb.js";import{Q as ae}from"./componentMap-Bkie1n3v.js";import{B as re}from"./BasicForm-DBcXiHk0.js";var ie=Object.defineProperty,ue=Object.defineProperties,se=Object.getOwnPropertyDescriptors,_=Object.getOwnPropertySymbols,de=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable,j=(e,t,n)=>t in e?ie(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,S=(e,t)=>{for(var n in t||(t={}))de.call(t,n)&&j(e,n,t[n]);if(_)for(var n of _(t))ce.call(t,n)&&j(e,n,t[n]);return e},B=(e,t)=>ue(e,se(t)),w=(e,t,n)=>new Promise((i,u)=>{var v=a=>{try{c(n.next(a))}catch(p){u(p)}},y=a=>{try{c(n.throw(a))}catch(p){u(p)}},c=a=>a.done?i(a.value):Promise.resolve(a.value).then(v,y);c((n=n.apply(e,t)).next())});const je=(e,t)=>m.get({url:"/online/cgform/button/list/"+e,params:t});function Be(e){return m.delete({url:"/online/cgform/button/deleteBatch",params:{ids:e.join(",")}},{joinParamsToUrl:!0})}const pe=(e,t)=>t?m.put({url:"/online/cgform/button/edit",params:e}):m.post({url:"/online/cgform/button/add",params:e}),me=(e,t)=>m.get({url:"/online/cgform/button/builtInList/"+e,params:t});function fe({text:e}){return e?"ant-design:"+e:""}const be=[{title:"按钮编码",align:"center",dataIndex:"buttonCode"},{title:"按钮名称",align:"center",dataIndex:"buttonName"},{title:"按钮样式",align:"center",dataIndex:"buttonStyle",customRender({text:e,record:t}){if(e==="form"){let n=t.optPosition;return e+"("+(n=="2"?"底部":"侧面")+")"}else return e}},{title:"按钮类型",align:"center",dataIndex:"optType"},{title:"排序",align:"center",dataIndex:"orderNum"},{title:"按钮图标",align:"center",dataIndex:"buttonIcon",customRender:({text:e})=>fe({text:e})},{title:"表达式",align:"center",dataIndex:"exp"},{title:"按钮状态",align:"center",dataIndex:"buttonStatus",customRender({text:e}){return e==1?"激活":"未激活"}}],ge=({redoModalHeight:e})=>[{label:"按钮编码",field:"buttonCode",component:"Input",required:!0,dynamicRules:()=>[{validator:(t,n)=>new Promise((i,u)=>{/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(n)?i():u("编码只能包含字母、数字、下划线 (_) 和美元符号 ($)且不能以数字开头")})},{validator:(t,n)=>new Promise((i,u)=>{["add","edit","detail","delete","batch_delete","import","export","query","reset","bpm","super_query","form_confirm"].includes(n)?u("不可使用内置按钮编码，请在“管理内置按钮”中修改内置按钮"):i()})}]},{label:"按钮名称",field:"buttonName",component:"Input",required:!0},{label:"按钮样式",field:"buttonStyle",component:"Select",componentProps:{options:[{label:"Link",value:"link"},{label:"Button",value:"button"},{label:"Form",value:"form"}],onChange:()=>{e()}},defaultValue:"link"},{label:"按钮位置",field:"optPosition",component:"Select",componentProps:{allowClear:!1,options:[{label:"底部",value:"2"}]},defaultValue:"2",show:({model:t})=>t.buttonStyle==="form"},{label:"按钮类型",field:"optType",component:"Select",componentProps:{allowClear:!1,options:[{label:"Js",value:"js"},{label:"Action",value:"action"}]},defaultValue:"js"},{label:"排序",field:"orderNum",component:"InputNumber",componentProps:{style:"width: 100%"}},{label:"按钮图标",field:"buttonIcon",component:"IconPicker",componentProps:{clearSelect:!0,iconPrefixSave:!1},ifShow:({values:t,model:n})=>t.buttonStyle=="button"||t.buttonStyle=="form"},{label:"表达式",field:"exp",component:"Input",ifShow:({values:t,model:n})=>t.buttonStyle=="link"?!0:(n.exp="",!1)},{label:"按钮状态",field:"buttonStatus",component:"RadioButtonGroup",componentProps:{options:[{label:"激活",value:"1"},{label:"未激活",value:"0"}]},defaultValue:"1"}],ve={style:{"margin-top":"20px"}},ke=U({__name:"BuiltInButtonList",props:{record:{type:Object,required:!0}},emits:["register"],setup(e,{emit:t}){const n=e,i=g(()=>{var o;return(o=n.record)==null?void 0:o.id}),u=g(()=>{var o;return(o=n.record)==null?void 0:o.tableType}),v=g(()=>u.value===1),{tableContext:y}=ee({tableProps:{api:o=>w(this,null,function*(){const l=yield me(i.value,o);return v.value?l.filter(P=>!P.buttonCode.includes("sub_")):l}),columns:be.filter(o=>!["buttonStyle","optType","orderNum","exp"].includes(o.dataIndex)),canResize:!1,useSearchForm:!1,pagination:!1}}),[c,{reload:a},{rowSelection:p}]=y,[k,{closeModal:O}]=te(()=>a()),[N,f]=oe(),x=$(!1),b=J({onRegister:N,title:g(()=>x!=null&&x.value?"修改":"新增"),width:600,centered:!0,confirmLoading:!1,onOk:z,onCancel:f.closeModal});let h={};const M=[...ge({redoModalHeight:f.redoModalHeight})].filter(o=>["buttonCode","buttonName","buttonIcon","buttonStatus"].includes(o.field)).map(o=>o.field==="buttonCode"?B(S({},o),{dynamicRules:()=>[],dynamicDisabled:()=>!0}):o.field==="buttonIcon"?B(S({},o),{ifShow:()=>!0,dynamicDisabled:({values:l})=>["bpm","edit","detail","delete"].includes(l.buttonCode)}):o),[L,{resetFields:V,setFieldsValue:T,validate:F}]=ne({schemas:M,showActionButtonGroup:!1});function q(o){return w(this,null,function*(){var l;x.value=o.isUpdate,h=S({},(l=o.record)!=null?l:{}),f.openModal(),yield Z(),yield V(),T(h)})}function H(o){q({isUpdate:!0,record:o})}function I(){O()}function z(){return w(this,null,function*(){try{b.confirmLoading=!0;let o=yield F();o=Object.assign({cgformHeadId:i.value},h,o);const l=o.id!=null;yield pe(o,l),a(),f.closeModal()}finally{b.confirmLoading=!1}})}function A(o){return[{label:"编辑",onClick:()=>H(o)}]}return(o,l)=>{const P=C("a-button"),D=C("a-spin");return K(),E(r(R),{onRegister:r(k),title:"内置按钮",width:1200,onCancel:I},{footer:s(()=>[d(P,{onClick:I},{default:s(()=>l[0]||(l[0]=[Y("关闭")])),_:1})]),default:s(()=>[d(r(le),{onRegister:r(c),rowSelection:r(p)},{action:s(({record:G})=>[d(r(ae),{actions:A(G)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"]),d(r(R),Q(W(b)),{default:s(()=>[d(D,{spinning:b.confirmLoading},{default:s(()=>[X("div",ve,[d(r(re),{onRegister:r(L)},null,8,["onRegister"])])]),_:1},8,["spinning"])]),_:1},16)]),_:1},8,["onRegister"])}}});export{je as C,Be as R,be as b,ge as g,ke as h,pe as m};
