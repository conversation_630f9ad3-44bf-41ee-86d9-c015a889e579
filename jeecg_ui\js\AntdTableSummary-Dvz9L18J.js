import{d as n,l as s,ag as d,aq as l,ar as c,k as u}from"./vue-vendor-dy9k-Yad.js";import{u as f}from"./index-BkGZ5fiW.js";import{d as h}from"./table-BDFKJhHv.js";import x from"./BasicTable-xCEZpGLb.js";import{a as _}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const g=n({components:{BasicTable:x},setup(){function r(t){const o=t.reduce((e,m)=>(e+=m.age,e),0),i=t.reduce((e,m)=>(e+=m.score,e),0);return[{_row:"合计",_index:"平均值",age:Math.round(o/t.length),score:Math.round(i/t.length)},{_row:"合计",_index:"总计",age:o,score:i}]}const p=()=>[{title:"ID",dataIndex:"id",fixed:"left",width:200,resizable:!0},{title:"姓名",dataIndex:"name",width:150,filters:[{text:"Male",value:"male"},{text:"Female",value:"female"}]},{title:"年龄",dataIndex:"age",width:100,resizable:!0,customSummaryRender:({text:t})=>s("span",{style:{color:"red"}},[t])},{title:"得分",dataIndex:"score",width:100,resizable:!0},{title:"地址",dataIndex:"address",width:300},{title:"编号",dataIndex:"no",width:150,sorter:!0,defaultHidden:!0},{title:"开始时间",width:150,sorter:!0,dataIndex:"beginTime"},{title:"结束时间",width:150,sorter:!0,dataIndex:"endTime"}],[a]=f({title:"原生总结栏（表尾合计）示例",api:h,rowSelection:{type:"checkbox"},columns:p(),summaryFunc:r,scroll:{x:1e3},canResize:!1});return{registerTable:a}}}),w={class:"p-4"};function b(r,p,a,t,o,i){const e=d("BasicTable");return c(),l("div",w,[u(e,{onRegister:r.registerTable},null,8,["onRegister"])])}const St=_(g,[["render",b]]);export{St as default};
