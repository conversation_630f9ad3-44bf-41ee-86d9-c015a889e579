import{j as r}from"./index-CCWaWN5g.js";const n=(e,t)=>{let s=t?"/sys/thirdApp/editThirdAppConfig":"/sys/thirdApp/addThirdAppConfig";return r.post({url:s,params:e},{joinParamsToUrl:!0})},i=e=>r.get({url:"/sys/thirdApp/getThirdConfigByTenantId",params:e}),a=()=>r.get({url:"/sys/thirdApp/sync/dingtalk/departAndUser/toLocal",timeout:6e4},{isTransformResponse:!1}),o=()=>r.get({url:"/sys/thirdApp/getThirdUserByWechat"},{isTransformResponse:!1}),p=e=>r.get({url:"/sys/thirdApp/sync/wechatEnterprise/departAndUser/toLocal",params:e},{isTransformResponse:!1}),h=()=>r.get({url:"/sys/thirdApp/getThirdUserBindByWechat"},{isTransformResponse:!1}),c=e=>r.delete({url:"/sys/thirdApp/deleteThirdAccount",params:e},{isTransformResponse:!1,joinParamsToUrl:!0});export{h as a,i as b,n as c,c as d,o as g,a as s,p as w};
