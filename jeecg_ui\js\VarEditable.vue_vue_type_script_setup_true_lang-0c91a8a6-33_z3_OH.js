import{d as q,f as F,w as S,o as U,ag as y,aq as i,ar as l,as as V,u as A,at as o,F as f,aC as v,au as D,aB as b,ah as h,k}from"./vue-vendor-dy9k-Yad.js";import{F as J}from"./index-CCWaWN5g.js";import{al as $,h as x}from"./antd-vue-vendor-me9YkNVC.js";import{r as z}from"./VarTextarea.vue_vue_type_script_setup_true_lang-8c82777b-BlFMq0Y1.js";import"./VarTextarea.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const E={class:"table-header"},I={class:"table-row"},L={class:"table-body"},_=["onClick"],M={class:"table-footer"},K=q({__name:"VarEditable",props:{columns:{type:Array,required:!0},data:{type:Array,required:!0},varsOptions:{type:Array,default:()=>[]}},emits:["update:data"],setup(c,{emit:C}){const{prefixCls:g}=J("airag-var-editable"),u=c,O=C,n=F([]);S(n,$(N,500),{deep:!0}),U(()=>{n.value=x(u.data),n.value.length===0&&m()});function m(){const t={};for(const a of u.columns)t[a.field]="";n.value.push(t)}function w(t){n.value.splice(t,1)}function N(){const t=[];e:for(const d of n.value){for(const r of u.columns)if(r.required&&!d[r.field])continue e;t.push(d)}const a=JSON.stringify(t);a!==JSON.stringify(u.data)&&O("update:data",JSON.parse(a))}return(t,a)=>{const d=y("a-input"),r=y("Icon");return l(),i("div",{class:V(`${A(g)}-container`)},[o("div",E,[o("div",I,[(l(!0),i(f,null,v(c.columns,s=>(l(),i("div",{key:s.field,class:"table-cell"},D(s.label),1))),128)),a[0]||(a[0]=o("div",{class:"table-cell action"},null,-1))])]),o("div",L,[(l(!0),i(f,null,v(n.value,(s,B)=>(l(),i("div",{key:s.field,class:"table-row"},[(l(!0),i(f,null,v(c.columns,e=>(l(),i("div",{key:e.field,class:"table-cell"},[e.type==="input"?(l(),b(d,{key:0,value:s[e.field],"onUpdate:value":p=>s[e.field]=p,placeholder:`请输入${e.label}`},null,8,["value","onUpdate:value","placeholder"])):h("",!0),e.type==="var-input"?(l(),b(z,{key:1,value:s[e.field],"onUpdate:value":p=>s[e.field]=p,width:260,type:"input",placeholder:`${e.label}，按下 “/” 可以选择变量`,varsOptions:c.varsOptions},null,8,["value","onUpdate:value","placeholder","varsOptions"])):h("",!0)]))),128)),o("div",{class:"table-cell action",onClick:e=>w(B)},[k(r,{icon:"ic:round-delete"})],8,_)]))),128))]),o("div",M,[o("a",{onClick:m},[k(r,{icon:"ic:round-add"}),a[1]||(a[1]=o("span",null,"添加参数",-1))])])],2)}}});export{K as P};
