var N=Object.defineProperty,A=Object.defineProperties;var B=Object.getOwnPropertyDescriptors;var g=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable;var b=(e,t,r)=>t in e?N(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,M=(e,t)=>{for(var r in t||(t={}))F.call(t,r)&&b(e,r,t[r]);if(g)for(var r of g(t))H.call(t,r)&&b(e,r,t[r]);return e},h=(e,t)=>A(e,B(t));import{d as T,f as _,o as Y,ag as L,aq as E,ar as G,k as p,aD as d,u as m,G as K}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import U from"./DetailModal-DbdGfGfj.js";import{e as y,s as I,g as V,r as q,a as O}from"./mynews.api-3wYWW1k0.js";import{render as l}from"./renderUtils-D7XVOFwj.js";import{b5 as Q,ad as j,B as z,u as J}from"./index-CCWaWN5g.js";import"./index-Diw57m_E.js";import{useListPage as W}from"./useListPage-Soxgnx9a.js";import{u as X}from"./useSysMessage-DA-cenrf.js";import{Q as Z}from"./componentMap-Bkie1n3v.js";import $ from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-QxsVJqiT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const ee=[{title:"标题",dataIndex:"titile",width:100,align:"left"},{title:"消息类型",dataIndex:"msgCategory",width:80,customRender:({text:e})=>l.renderDictNative(e,[{label:"通知公告",value:"1",color:"blue"},{label:"系统消息",value:"2"}],!0)},{title:"发布人",dataIndex:"sender",width:80},{title:"发布时间",dataIndex:"sendTime",width:80},{title:"优先级",dataIndex:"priority",width:80,customRender:({text:e})=>{const t=e=="L"?"blue":e=="M"?"yellow":"red";return l.renderTag(l.renderDict(e,"priority"),t)}},{title:"阅读状态",dataIndex:"readFlag",width:80,customRender:({text:e})=>l.renderDictNative(e,[{label:"未读",value:"0",color:"red"},{label:"已读",value:"1"}],!0)}],te=[{field:"titile",label:"标题",component:"Input",colProps:{span:6}},{field:"sender",label:"发布人",component:"Input",colProps:{span:6}},{field:"sendTime",label:"发布时间",component:"RangeDate",componentProps:{valueType:"Date"},colProps:{span:6}}],oe=T({name:"monitor-mynews"}),bt=T(h(M({},oe),{setup(e){const t=Q(),{createMessage:r}=J(),w=_([]),re=_({}),c={logType:"1"},[C,{openModal:u}]=j(),f=z(),{goPage:D}=X(),{prefixCls:ie,tableContext:v}=W({designScope:"mynews-list",tableProps:{title:"我的消息",api:O,columns:ee,formConfig:{schemas:te,fieldMapToTime:[["sendTime",["sendTimeBegin","sendTimeEnd"],"YYYY-MM-DD"]]}}}),[x,{reload:a}]=v;function P(o){return[{label:"查看",onClick:R.bind(null,o)}]}function R(o){let i=o.anntId;y({anntId:i}).then(s=>{a(),I({anntId:i})}),D(o,()=>{u(!0,{record:o,isUpdate:!0})})}function ne(o){c.logType=o,a()}function k(){q({},a)}function ae(o){w.value=o}Y(()=>{S()});function S(){let o=f.getMessageHrefParams;if(o){let i=o.id;i&&y({anntId:i}).then(()=>{a(),I({anntId:i})});let n=o.detailId;n&&V(n).then(s=>{u(!0,{record:s,isUpdate:!0}),f.setMessageHrefParams("")})}}return(o,i)=>{const n=L("a-button");return G(),E("div",null,[p(m($),{onRegister:m(x),searchInfo:c},{tableTitle:d(()=>[p(n,{type:"primary",onClick:k},{default:d(()=>i[0]||(i[0]=[K("全部标注已读")])),_:1,__:[0]})]),action:d(({record:s})=>[p(m(Z),{actions:P(s)},null,8,["actions"])]),_:1},8,["onRegister"]),p(U,{onRegister:m(C)},null,8,["onRegister"])])}}}));export{bt as default};
