import{j as C}from"./antd-vue-vendor-me9YkNVC.js";import{d as v,ap as g,f as b,e as c,u as a,ag as l,aB as B,ar as G,aD as s,G as n,at as e,k as u,au as P}from"./vue-vendor-dy9k-Yad.js";import{P as $}from"./index-CtJ0w2CP.js";import{a as y}from"./index-CCWaWN5g.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const I=v({name:"TestMenu",components:{PageWrapper:$,Input:C},setup(){const{currentRoute:o,replace:t}=g(),r=b("");return{value:r,handleClickGo:()=>{const{name:p}=a(o);t({name:p,params:{id:a(r)}})},params:c(()=>a(o).params)}}});function N(o,t,r,m,p,V){const i=l("Input"),d=l("a-button"),f=l("PageWrapper");return G(),B(f,{title:"带参数菜单（路由）",content:"支持多级参数"},{default:s(()=>[n(" 当前参数："+P(o.params)+" ",1),t[2]||(t[2]=e("br",null,null,-1)),t[3]||(t[3]=n(" 输入参数切换路由： ")),u(i,{value:o.value,"onUpdate:value":t[0]||(t[0]=k=>o.value=k),placeholder:"建议为url标准字符，输入后点击切换"},null,8,["value"]),u(d,{type:"primary",onClick:o.handleClickGo},{default:s(()=>t[1]||(t[1]=[n("切换路由")])),_:1,__:[1]},8,["onClick"]),t[4]||(t[4]=e("br",null,null,-1)),t[5]||(t[5]=n(" 切换路由后 ")),t[6]||(t[6]=e("ul",null,[e("li",null,"可刷新页面测试路由参数情况是否正常。"),e("li",null,"可于左侧菜单中展开子菜单，点击测试参数是否携带正常。")],-1))]),_:1,__:[2,3,4,5,6]})}const U=y(I,[["render",N]]);export{U as default};
