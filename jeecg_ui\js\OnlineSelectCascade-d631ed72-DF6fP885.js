import{d as B,f as h,w as m,ag as y,aB as S,ar as v,aD as w,aq as V,F as k,aC as O,at as P,au as $}from"./vue-vendor-dy9k-Yad.js";import{cs as M,u as N,j as x}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var R=(t,r,o)=>new Promise((u,n)=>{var f=a=>{try{d(o.next(a))}catch(s){n(s)}},p=a=>{try{d(o.throw(a))}catch(s){n(s)}},d=a=>a.done?u(a.value):Promise.resolve(a.value).then(f,p);d((o=o.apply(t,r)).next())});const C="/online/cgform/api/querySelectOptions",T=B({name:"OnlineSelectCascade",props:{table:{type:String,default:""},txt:{type:String,default:""},store:{type:String,default:""},idField:{type:String,default:""},pidField:{type:String,default:""},pidValue:{type:String,default:"-1"},origin:{type:Boolean,default:!1},condition:{type:String,default:""},value:{type:String,default:""},isNumber:{type:Boolean,default:!1},placeholder:{type:String,default:"请选择"}},emits:["change","next"],setup(t,{emit:r}){const{createMessage:o}=N(),u=h(""),n=h([]),f=h(!0);function p(e){let l=e||"";r("change",l),g(l)}m(()=>t.condition,e=>{f.value=!0,e&&a()},{immediate:!0}),m(()=>t.pidValue,e=>{e==="-1"?n.value=[]:a()}),m(()=>t.value,(e,l)=>{e?u.value=e:(u.value=[],l&&(r("change",""),r("next","-1"))),e&&!l&&d(e)},{immediate:!0});function d(e){return R(this,null,function*(){if(t.idField===t.store)r("next",e);else if(t.origin===!0)yield F(),g(e);else{let l=yield b();g(e,l)}})}function a(){let e=s();t.origin===!0?e.condition=t.condition:e.pidValue=t.pidValue,n.value=[],x.get({url:C,params:e},{isTransformResponse:!1}).then(l=>{l.success?n.value=[...l.result]:o.warning("联动组件数据加载失败,请检查配置!")})}function s(){return{table:t.table,txt:t.txt,key:t.store,idField:t.idField,pidField:t.pidField}}function b(){return new Promise(e=>{if(!t.value)u.value=[],e([]);else{let l=s();t.isNumber===!0?l.condition=`${t.store} = ${t.value}`:l.condition=`${t.store} = '${t.value}'`,x.get({url:C,params:l},{isTransformResponse:!1}).then(i=>{i.success?e(i.result):(o.warning("联动组件数据加载失败,请检查配置!"),e([]))})}})}function F(){return new Promise(e=>{(function l(i){i>10&&e([]);let c=n.value;c&&c.length>0?e(c):setTimeout(()=>{l(i++)},300)})(0)})}function g(e,l=[]){if(e&&e.length>0){(!l||l.length==0)&&(l=n.value);let i=l.filter(c=>c.store===e);if(i&&i.length>0){let c=i[0].id;r("next",c)}}}return{selectedValue:u,dictOptions:n,handleChange:p}}}),q=["title"];function D(t,r,o,u,n,f){const p=y("a-select-option"),d=y("a-select");return v(),S(d,{placeholder:t.placeholder,value:t.selectedValue,onChange:t.handleChange,allowClear:"",style:{width:"100%"}},{default:w(()=>[(v(!0),V(k,null,O(t.dictOptions,(a,s)=>(v(),S(p,{key:s,value:a.store},{default:w(()=>[P("span",{style:{display:"inline-block",width:"100%"},title:a.label},$(a.label),9,q)]),_:2},1032,["value"]))),128))]),_:1},8,["placeholder","value","onChange"])}const H=M(T,[["render",D]]);export{H as default};
