var q=Object.defineProperty,G=Object.defineProperties;var P=Object.getOwnPropertyDescriptors;var A=Object.getOwnPropertySymbols;var W=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var B=(i,t,a)=>t in i?q(i,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):i[t]=a,C=(i,t)=>{for(var a in t||(t={}))W.call(t,a)&&B(i,a,t[a]);if(A)for(var a of A(t))O.call(t,a)&&B(i,a,t[a]);return i},E=(i,t)=>G(i,P(t));var g=(i,t,a)=>new Promise((T,r)=>{var c=o=>{try{y(a.next(o))}catch(f){r(f)}},m=o=>{try{y(a.throw(o))}catch(f){r(f)}},y=o=>o.done?T(o.value):Promise.resolve(o.value).then(c,m);y((a=a.apply(i,t)).next())});import{d as z,f as u,o as H,aq as k,ar as x,at as l,k as M,ah as L,aA as S,u as n,au as U,as as J}from"./vue-vendor-dy9k-Yad.js";import{j as K,k as Q,l as R}from"./UserSetting.api-BJ086Ekj.js";import{F as X,ah as Y,b5 as Z,u as ee}from"./index-CCWaWN5g.js";import{bF as se,bG as te,bt as ae,M as ne}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const ie={class:"account-row-item"},le={key:0,class:"gray-75",style:{"margin-left":"8px"}},re={class:"account-row-item"},oe={key:0,class:"gray-75",style:{"margin-left":"8px"}},ce=z({name:"we-chat-ding-setting"}),ve=z(E(C({},ce),{setup(i){const{prefixCls:t}=X("j-user-tenant-setting-container"),a=se({scriptUrl:"//at.alicdn.com/t/font_2316098_umqusozousr.js"}),T=Y(),r=u({}),c=u({}),m=u({}),y=Z(),o=u(""),f=u(""),de=u({}),{createMessage:p}=ee(),v=u(""),h=u("");function D(){return g(this,null,function*(){let e=yield K({thirdType:"wechat_open,dingtalk,wechat_enterprise"});if(r.value="",c.value="",m.value="",e&&e.result){let s=e.result;for(let d=0;d<s.length;d++)F(s[d])}})}function ue(){let e=n(m);e.sysUserId?_({sysUserId:e.sysUserId,id:e.id},"企业微信"):b("wechat_enterprise")}function $(){let e=n(c);e.sysUserId?_({sysUserId:e.sysUserId,id:e.id},"钉钉"):b("dingtalk")}function j(){let e=n(r);e.sysUserId?_({sysUserId:e.sysUserId,id:e.id},"微信"):b("wechat_open")}function b(e){let s=`${y.uploadUrl}/sys/thirdLogin/render/${e}`;n(v)&&(v.value.close(),window.removeEventListener("message",n(h),!1)),v.value=window.open(s,`login ${e}`,"height=500, width=500, top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no"),o.value=e,h.value=function(d){return g(this,null,function*(){let w=d.data;if(typeof w=="string")if(w==="登录失败")I();else if(w.includes("绑定手机号")){let V=w.split(",");f.value=V[1],yield N()}else w&&p.warning("该敲敲云账号已被其它第三方账号绑定,请解绑或绑定其它敲敲云账号");else I();window.removeEventListener("message",n(h),!1),v.value=""})},window.addEventListener("message",n(h),!1)}function N(){return g(this,null,function*(){if(!n(f)){I();return}let e={thirdUserUuid:n(f),thirdType:n(o)};yield Q(e).then(s=>{s.success?s.result&&F(s.result):p.warning(s.message)}).catch(s=>{p.warning(s.message)})})}function I(){p.warning("第三方账号绑定异常")}function F(e){let s=e.thirdType;s=="wechat_open"?r.value=e:s=="dingtalk"?c.value=e:s=="wechat_enterprise"&&(m.value=e)}function _(e,s){return g(this,null,function*(){ne.confirm({title:"解绑"+s,content:"确定要解绑吗",okText:"确认",cancelText:"取消",onOk:()=>g(null,null,function*(){yield R(e).then(d=>{d.success?(D(),p.success(d.message)):p.warning(d.message)})})})})}return H(()=>{D()}),(e,s)=>(x(),k("div",{class:J([`${n(t)}`])},[s[4]||(s[4]=l("div",{class:"my-account"},"第三方APP",-1)),l("div",ie,[s[1]||(s[1]=l("div",{class:"account-label gray-75"},"钉钉绑定",-1)),l("span",null,[M(n(te),{style:S(c.value.sysUserId?{color:"#007FFF"}:{color:"#9e9e9e"}),class:"item-icon"},null,8,["style"]),s[0]||(s[0]=l("span",{class:"gray-75",style:{"margin-left":"12px"}},"钉钉",-1)),c.value.realname?(x(),k("span",le,U("已绑定："+c.value.realname),1)):L("",!0),l("span",{class:"blue-e5 pointer",style:{"margin-left":"24px"},onClick:$},U(c.value.sysUserId?"解绑":"绑定"),1)])]),l("div",re,[s[3]||(s[3]=l("div",{class:"account-label gray-75"},"账号绑定",-1)),l("span",null,[M(n(ae),{style:S(r.value.sysUserId?{color:"#1ec563"}:{color:"#9e9e9e"}),class:"item-icon"},null,8,["style"]),s[2]||(s[2]=l("span",{class:"gray-75",style:{"margin-left":"12px"}},"微信",-1)),r.value.realname?(x(),k("span",oe,U("已绑定："+r.value.realname),1)):L("",!0),l("span",{class:"blue-e5 pointer",style:{"margin-left":"24px"},onClick:j},U(r.value.sysUserId?"解绑":"绑定"),1)])])],2))}}));export{ve as default};
