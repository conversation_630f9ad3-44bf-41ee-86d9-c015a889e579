import{d as _,f as c,r as I,J as E,I as j,ag as b,aB as V,ar as G,aE as T,aD as S,k as O}from"./vue-vendor-dy9k-Yad.js";import{B as D}from"./index-Diw57m_E.js";import K from"./SetSwitchOptions-f914bc17-Cp2St2Ye.js";import"./index-L3cSIXth.js";import{p as C}from"./antd-vue-vendor-me9YkNVC.js";import{o as R}from"./constant-fa63bd66-Ddbq-fz2.js";import{cs as A,ac as J,R as U}from"./index-CCWaWN5g.js";import{u as q}from"./useForm-CgkFTrrO.js";import{B as W}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";var Y=Object.defineProperty,Z=Object.defineProperties,$=Object.getOwnPropertyDescriptors,P=Object.getOwnPropertySymbols,z=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable,x=(l,i,r)=>i in l?Y(l,i,{enumerable:!0,configurable:!0,writable:!0,value:r}):l[i]=r,F=(l,i)=>{for(var r in i||(i={}))z.call(i,r)&&x(l,r,i[r]);if(P)for(var r of P(i))H.call(i,r)&&x(l,r,i[r]);return l},Q=(l,i)=>Z(l,$(i)),k=(l,i,r)=>new Promise((f,a)=>{var t=p=>{try{u(r.next(p))}catch(m){a(m)}},d=p=>{try{u(r.throw(p))}catch(m){a(m)}},u=p=>p.done?f(p.value):Promise.resolve(p.value).then(t,d);u((r=r.apply(l,i)).next())});const X=_({name:"FieldExtendJsonModal",components:{BasicModal:D,BasicForm:W,SetSwitchOptions:K},emits:["success","register"],setup(l,{emit:i}){const r=c(!1);function f(){a.uploadnum=0,a.showLength="",a.popupMulti=!0,a.multiSelect=!0,a.store="",a.text="",a.orderRule="",a.validateError="",a.labelLength=R}const a=I({uploadnum:0,showLength:"",popupMulti:!0,store:"",text:"",multiSelect:!0,orderRule:"",validateError:"",labelLength:R}),t=c(""),d=c(""),u=c("0"),p=c(""),m=[{label:"rowKey",field:"rowKey",component:"Input",show:!1},{label:"文件上传数量",field:"uploadnum",component:"InputNumber",componentProps:{style:{width:"100%"}},ifShow:()=>t.value==="file"||t.value==="image"},{label:"是否多选",field:"popupMulti",component:"RadioGroup",defaultValue:!0,componentProps:{options:[{label:"否",value:!1},{label:"是",value:!0}]},ifShow:()=>t.value==="popup"||t.value==="popup_dict"},{label:"是否多选",field:"multiSelect",component:"RadioGroup",defaultValue:!0,componentProps:{options:[{label:"否",value:!1},{label:"是",value:!0}]},ifShow:()=>t.value==="sel_user"||t.value==="sel_depart"},{label:"存储字段",field:"store",component:"Input",ifShow:()=>t.value==="sel_user"||t.value==="sel_depart"},{label:"展示字段",field:"text",component:"Input",ifShow:()=>t.value==="sel_user"||t.value==="sel_depart"},{label:"默认排序",field:"orderRule",component:"RadioGroup",defaultValue:"",componentProps:{options:[{label:"降序",value:"desc"},{label:"升序",value:"asc"},{label:"不默认排序",value:""}]},ifShow:()=>u.value==="1"||["Datetime","string","Date","int","BigDecimal","double"].includes(p.value)},{label:"校验提示",field:"validateError",component:"Input",componentProps:{placeholder:"请输入校验提示文本"}},{label:"查询label长度",field:"labelLength",component:"InputNumber",componentProps:{placeholder:"请输入label长度"}},{label:"是否固定",field:"isFixed",component:"RadioGroup",defaultValue:0,componentProps:{options:[{label:"是",value:1},{label:"否",value:0}]}},{label:"是否独占一行",field:"isOneRow",component:"RadioGroup",defaultValue:!1,componentProps:{options:[{label:"否",value:!1},{label:"是",value:!0}]},ifShow:()=>t.value==="markdown"||t.value==="umeditor"},{label:"日期格式",field:"picker",component:"RadioGroup",defaultValue:"default",componentProps:{options:[{label:"年",value:"year"},{label:"年月",value:"month"},{label:"年周",value:"week"},{label:"年季度",value:"quarter"},{label:"年月日",value:"default"}]},ifShow:()=>t.value==="date"},{label:"开关值",field:"switchOptions",component:"Input",slot:"switchOptions",ifShow:()=>t.value==="switch"}],[h,{validate:v,setFieldsValue:w,resetFields:L}]=q({schemas:m,showActionButtonGroup:!1,labelAlign:"right",labelWidth:100}),[N,{closeModal:B}]=J(e=>k(this,null,function*(){if(f(),e.jsonStr){let o=JSON.parse(e.jsonStr);e.fieldShowType==="switch"&&U(o)&&o.length==2&&(a.switchOptions=o),Object.keys(o).map(s=>{a[s]=o[s]})}else e.fieldShowType==="switch"&&(a.switchOptions=["Y","N"]);t.value=e.fieldShowType,d.value=e.id,u.value=e.sortFlag,p.value=e.dbType;let n=E(a);yield L(),yield w(Q(F({},n),{rowKey:e.id}))}));function M(){return k(this,null,function*(){let e=yield v(),n=t.value,o={};if(n==="file"||n==="image"?e.uploadnum&&e.uploadnum>0&&(o.uploadnum=e.uploadnum):n==="textarea"||n==="text"?e.showLength&&e.showLength>0&&(o.showLength=e.showLength):n==="sel_user"||n==="sel_depart"?o=C(e,"store","text","multiSelect"):(n==="popup"||n==="popup_dict")&&(o.popupMulti=e.popupMulti),e.orderRule&&(o.orderRule=e.orderRule),e.validateError&&(o.validateError=e.validateError),e.labelLength&&(o.labelLength=e.labelLength),e.isFixed&&(o.isFixed=e.isFixed),e.isOneRow&&(o.isOneRow=e.isOneRow),e.picker&&(o.picker=e.picker),e.switchOptions){const s=e.switchOptions.split(",");let g=Number(s[0]),y=Number(s[1]);(Number.isNaN(g)||Number.isNaN(y))&&(g=s[0],y=s[1]),o.switchOptions=[g,y]}for(let s in o)o[s]===""&&delete o[s];i("success",o,e.rowKey),B()})}return F({spinningLoading:r,registerModal:N,registerForm:h,fieldShowType:t,rowKey:d,handleSubmit:M},j(a))}});function ee(l,i,r,f,a,t){const d=b("SetSwitchOptions"),u=b("BasicForm"),p=b("a-spin"),m=b("BasicModal");return G(),V(m,T({wrapClassName:"field-extend-config-modal"},l.$attrs,{title:"字段扩展配置项",onRegister:l.registerModal,keyboard:"",canFullscreen:!1,cancelText:"关闭",onOk:l.handleSubmit}),{default:S(()=>[O(p,{spinning:l.spinningLoading},{default:S(()=>[O(u,{onRegister:l.registerForm},{switchOptions:S(({model:h,field:v})=>[O(d,{value:h[v],"onUpdate:value":w=>h[v]=w},null,8,["value","onUpdate:value"])]),_:1},8,["onRegister"])]),_:1},8,["spinning"])]),_:1},16,["onRegister","onOk"])}const lo=A(X,[["render",ee]]);export{lo as default};
