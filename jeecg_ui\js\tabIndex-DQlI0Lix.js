import{d as y,f as p,e as b,ag as a,aq as c,ar as t,k as n,aD as l,aB as F,F as d,aC as _,aQ as k}from"./vue-vendor-dy9k-Yad.js";import C from"./BasicFiledsLayotForm-DVtc028A.js";import h from"./BasicFixedWidthForm-DwxXIp0l.js";import $ from"./BasicFormAdd-BpP-_GGu.js";import v from"./BasicFormBtn-BhkUmFpE.js";import S from"./BasicFormCleanRule-BkUHdEqg.js";import g from"./BasicFormCompact-NZ2eDEhI.js";import R from"./BasicFormComponent-Dvy6uyVD.js";import L from"./BasicFormConAttribute-DXqaD7TZ.js";import K from"./BasicFormCustom-kvhhuXL3.js";import A from"./BasicFormCustomComponent-BZsXjltf.js";import D from"./BasicFormCustomSlots-BG5djuty.js";import V from"./BasicFormDynamicsRules-EnFxK90I.js";import w from"./BasicFormFieldShow-CLxSVFDC.js";import E from"./BasicFormFieldTip-Z1O3JOKr.js";import x from"./BasicFormFooter-DgVbetEf.js";import M from"./BasicFormLayout-CzKJ1fBK.js";import T from"./BasicFormModal-DbuN8eXj.js";import W from"./BasicFormRander-C0qpddUS.js";import q from"./BasicFormRules-C6ki-we1.js";import G from"./BasicFormSchemas-C2wZkpdi.js";import I from"./BasicFormSearch-HIC6fwgR.js";import J from"./BasicFormSlots-OVMxUmE-.js";import N from"./BasicFormValue-B2eCNTz5.js";import Q from"./BasicFunctionForm-C-ruFYAW.js";import{a as U}from"./index-CCWaWN5g.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./antd-vue-vendor-me9YkNVC.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const j=y({name:"document-table-demo",components:{BasicFunctionForm:Q,BasicFormConAttribute:L,BasicFormFieldShow:w,BasicFormFieldTip:E,BasicFormRules:q,BasicFormDynamicsRules:V,BasicFormSlots:J,BasicFormCustomSlots:D,BasicFormRander:W,BasicFixedWidthForm:h,BasicFiledsLayotForm:C,BasicFormLayout:M,BasicFormBtn:v,BasicFormCompact:g,BasicFormCleanRule:S,BasicFormValue:N,BasicFormSchemas:G,BasicFormAdd:$,BasicFormFooter:x,BasicFormModal:T,BasicFormCustom:K,BasicFormSearch:I,BasicFormComponent:R,BasicFormCustomComponent:A},setup(){const o=p("BasicFunctionForm"),m=p([{key:"BasicFunctionForm",label:"基础表单"},{key:"BasicFormConAttribute",label:"字段控件属性"},{key:"BasicFormComponent",label:"Ant Design Vue自带控件"},{key:"BasicFormCustomComponent",label:"JEECG封装的控件"},{key:"BasicFormFieldShow",label:"字段显示和隐藏"},{key:"BasicFormFieldTip",label:"字段标题提示"},{key:"BasicFormRules",label:"表单检验"},{key:"BasicFormDynamicsRules",label:"自定义动态检验"},{key:"BasicFormSlots",label:"字段插槽"},{key:"BasicFormCustomSlots",label:"自定义组件(插槽)"},{key:"BasicFormCustom",label:"自定义组件(component)"},{key:"BasicFormRander",label:"自定义渲染"},{key:"BasicFixedWidthForm",label:"固定label宽度"},{key:"BasicFiledsLayotForm",label:"标题与字段布局"},{key:"BasicFormLayout",label:"表单布局"},{key:"BasicFormBtn",label:"操作按钮示例"},{key:"BasicFormCompact",label:"表单紧凑"},{key:"BasicFormCleanRule",label:"表单检验配置"},{key:"BasicFormValue",label:"获取value值"},{key:"BasicFormSchemas",label:"更新schemas表单配置"},{key:"BasicFormAdd",label:"动态增减表单"},{key:"BasicFormFooter",label:"自定义页脚"},{key:"BasicFormModal",label:"弹出层表单"},{key:"BasicFormSearch",label:"查询区域"}]),i=b(()=>o.value);function e(s){o.value=s}return{activeKey:o,currentComponent:i,tabChange:e,compList:m}}}),z={class:"p-4"};function H(o,m,i,e,s,O){const B=a("a-tab-pane"),f=a("a-tabs"),u=a("a-card");return t(),c("div",z,[n(u,{bordered:!1,style:{height:"100%"}},{default:l(()=>[n(f,{activeKey:o.activeKey,"onUpdate:activeKey":m[0]||(m[0]=r=>o.activeKey=r),onChange:o.tabChange},{default:l(()=>[(t(!0),c(d,null,_(o.compList,r=>(t(),F(B,{key:r.key,tab:r.label},null,8,["tab"]))),128))]),_:1},8,["activeKey","onChange"]),(t(),F(k(o.currentComponent)))]),_:1})])}const Cm=U(j,[["render",H]]);export{Cm as default};
