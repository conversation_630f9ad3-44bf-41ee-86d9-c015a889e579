var g=(h,m,e)=>new Promise((s,r)=>{var c=t=>{try{i(e.next(t))}catch(a){r(a)}},d=t=>{try{i(e.throw(t))}catch(a){r(a)}},i=t=>t.done?s(t.value):Promise.resolve(t.value).then(c,d);i((e=e.apply(h,m)).next())});import{d as I,p as y,f as _,ag as w,aB as v,ar as k,aD as n,k as l,at as N,u as x,G as S,au as T,as as P}from"./vue-vendor-dy9k-Yad.js";import{F as B}from"./index-CCWaWN5g.js";import{_ as R,p as z,l as D}from"./DepartLeftTree-CSNBPnGC.js";import"./index-BkGZ5fiW.js";import{useListPage as F}from"./useListPage-Soxgnx9a.js";import V from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./depart.user.api-D_abnxSU.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const j=[{title:"姓名",dataIndex:"realname",width:150},{title:"工号",dataIndex:"workNo",width:100},{title:"部门",dataIndex:"departName",width:200},{title:"职务",dataIndex:"post",width:150,slots:{customRender:"post"}},{title:"手机",width:150,dataIndex:"telephone"},{title:"邮箱",width:150,dataIndex:"email"}],K=[{label:"姓名",field:"realname",component:"Input",colProps:{span:6}},{label:"工号",field:"workNo",component:"Input",colProps:{span:6}}],L={style:{height:"100%"},class:"address-book"},Mt=I({__name:"index",setup(h){const{prefixCls:m}=B("address-list");y("prefixCls",m);const e=_(),s=_(""),r=_({}),{tableContext:c}=F({tableProps:{api:D,columns:j,rowKey:"userId",showIndexColumn:!0,formConfig:{schemas:K},canResize:!1,actionColumn:null,showTableSetting:!1,beforeFetch(o){o.orgCode=s.value}}}),[d,{reload:i}]=c;function t(o){s.value=o.orgCode,i()}function a(){return g(this,null,function*(){const o=yield z({pageSize:99999});if(o){let f={};o.records.forEach(p=>{f[p.id]=p.name}),r.value=f}})}return a(),(o,f)=>{const p=w("a-col"),C=w("a-row");return k(),v(C,{class:P(["p-4",`${x(m)}--box`]),type:"flex",gutter:10,style:{"max-height":"800px"}},{default:n(()=>[l(p,{xl:6,lg:24,md:24,style:{"margin-bottom":"10px"}},{default:n(()=>[l(R,{ref_key:"leftTree",ref:e,onSelect:t},null,512)]),_:1}),l(p,{xl:18,lg:24,md:24,style:{"margin-bottom":"10px"}},{default:n(()=>[N("div",L,[l(x(V),{onRegister:x(d)},{post:n(({text:b})=>[S(T((b||"").split(",").map(u=>r.value[u]?r.value[u]:u).join(",")),1)]),_:1},8,["onRegister"])])]),_:1})]),_:1},8,["class"])}}});export{Mt as default};
