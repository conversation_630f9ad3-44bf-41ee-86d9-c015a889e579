import{d as et,f as I,w as ot,e as at,ag as i,aB as c,ar as r,aE as nt,aH as it,aD as o,k as f,aq as R,ah as mt,F as D,aC as L,G as B,au as h,at as S}from"./vue-vendor-dy9k-Yad.js";import{B as rt}from"./index-Diw57m_E.js";import lt from"./OnlineTabForm-1940e88b-DH6ovygw.js";import{b as pt}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{t as dt}from"./CommentPanel-HxTGfoA9.js";import{t as st}from"./constant-fa63bd66-Ddbq-fz2.js";import{cs as ct,ap as ut}from"./index-CCWaWN5g.js";import"./index-L3cSIXth.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useCustomHook-acb00837-B7NPzH0H.js";import"./componentMap-Bkie1n3v.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./JUpload-CRos0F1P.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-BLwcuZxD.js";var bt=(t,a,l)=>new Promise((u,p)=>{var C=n=>{try{d(l.next(n))}catch(s){p(s)}},g=n=>{try{d(l.throw(n))}catch(s){p(s)}},d=n=>n.done?u(n.value):Promise.resolve(n.value).then(C,g);d((l=l.apply(t,a)).next())});const ft=et({name:"OnlineTabAutoModal",props:{id:{type:String,default:""},cgBIBtnMap:Object,buttonSwitch:Object,confirmBtnCfg:{type:Object,default:()=>({enabled:!0,buttonName:"确定",buttonIcon:""})}},components:{BasicModal:rt,OnlineForm:lt,CommentPanel:dt},emits:["success","register","formConfig"],setup(t,{emit:a}){const l=I(),u=I([]),p=I("-1"),C=I(0),{getIsMobile:g}=ut();function d(){l.value&&l.value.reload()}const{title:n,modalWidth:s,registerModal:y,closeModal:T,cgButtonList:N,handleCgButtonClick:x,disableSubmit:w,handleSubmit:M,submitLoading:F,handleCancel:e,handleFormConfig:b,onlineFormCompRef:j,formTemplate:A,isTreeForm:W,pidFieldName:H,renderSuccess:$,formRendered:q,tableName:G,formDataId:U,enableComment:z,onCloseEvent:E,themeTemplate:J}=pt(!1,{emit:a},d);function K(m){a("success",m),T(),E(),P()}ot(()=>t.id,Q,{immediate:!0});function Q(){return bt(this,null,function*(){q.value=!1,t.id&&(yield b(t.id,{},m=>{const k=[],_=[],{head:V,schema:Z}=m,{properties:tt}=Z;k.push({tableName:V.tableName,tableTxt:V.tableTxt}),Object.entries(tt).forEach(([O,v])=>{v.view=="tab"&&_.push({tableName:O,tableTxt:v.describe,order:v.order})}),_.sort((O,v)=>O.order-v.order),u.value=[...k,..._]}))})}const X=m=>String(m-1),P=()=>{setTimeout(()=>{p.value="-1"},500)},Y=at(()=>g.value&&u.value.length>2);return{title:n,onlineFormCompRef:j,renderSuccess:$,registerModal:y,handleSubmit:M,handleSuccess:K,handleCancel:e,modalWidth:s,formTemplate:A,disableSubmit:w,cgButtonList:N,handleCgButtonClick:x,isTreeForm:W,pidFieldName:H,submitLoading:F,tableName:G,formDataId:U,enableComment:z,commentPanelRef:l,onCloseEvent:E,themeTemplate:J,tabNav:u,tabValue:X,tabIndex:p,TAB:st,restTabIndex:P,handleMenuClick:({key:m})=>{p.value=m},showDropdownBtn:Y,handleToggleTab:m=>{p.value=m},handleCommentOpen:(m,k)=>{C.value=k},commentSpan:C}}}),ht={class:"titleArea"},Ct={class:"title"},gt={class:"right"};function Tt(t,a,l,u,p,C){const g=i("a-menu-item"),d=i("a-menu"),n=i("a-dropdown-button"),s=i("a-radio-button"),y=i("a-radio-group"),T=i("a-button"),N=i("a-col"),x=i("a-row"),w=i("online-form"),M=i("comment-panel"),F=i("BasicModal");return r(),c(F,nt({title:t.title,onCancel:a[2]||(a[2]=()=>{t.onCloseEvent(),t.restTabIndex()}),enableComment:t.enableComment,width:t.modalWidth},t.$attrs,{maxHeight:600,onRegister:t.registerModal,wrapClassName:"jeecg-online-modal",onOk:t.handleSubmit,onCommentOpen:t.handleCommentOpen}),it({footer:o(()=>[f(x,null,{default:o(()=>[f(N,{span:24-t.commentSpan},{default:o(()=>[(r(!0),R(D,null,L(t.cgButtonList,e=>(r(),c(T,{key:e.id,type:"primary",onClick:b=>t.handleCgButtonClick(e.optType,e.buttonCode),preIcon:e.buttonIcon?"ant-design:"+e.buttonIcon:""},{default:o(()=>[B(h(e.buttonName),1)]),_:2},1032,["onClick","preIcon"]))),128)),!t.disableSubmit&&t.confirmBtnCfg.enabled?(r(),c(T,{key:"submit",type:"primary",preIcon:t.confirmBtnCfg.buttonIcon,loading:t.submitLoading,onClick:t.handleSubmit},{default:o(()=>[S("span",null,h(t.confirmBtnCfg.buttonName),1)]),_:1},8,["preIcon","loading","onClick"])):mt("",!0),f(T,{key:"back",onClick:a[1]||(a[1]=()=>{t.handleCancel(),t.restTabIndex()})},{default:o(()=>a[3]||(a[3]=[B("关闭")])),_:1})]),_:1},8,["span"])]),_:1})]),comment:o(()=>[f(M,{ref:"commentPanelRef",tableName:t.tableName,dataId:t.formDataId},null,8,["tableName","dataId"])]),default:o(()=>[f(w,{ref:"onlineFormCompRef",id:t.id,disabled:t.disableSubmit,"form-template":t.formTemplate,isTree:t.isTreeForm,pidField:t.pidFieldName,themeTemplate:t.themeTemplate,tabIndex:t.tabIndex,cgBIBtnMap:t.cgBIBtnMap,buttonSwitch:t.buttonSwitch,onRendered:t.renderSuccess,onSuccess:t.handleSuccess,onToggleTab:t.handleToggleTab},null,8,["id","disabled","form-template","isTree","pidField","themeTemplate","tabIndex","cgBIBtnMap","buttonSwitch","onRendered","onSuccess","onToggleTab"])]),_:2},[t.themeTemplate===t.TAB?{name:"title",fn:o(()=>[S("div",ht,[S("div",Ct,h(t.title),1),S("div",gt,[t.showDropdownBtn?(r(),c(n,{key:0,trigger:"click"},{overlay:o(()=>[f(d,{onClick:t.handleMenuClick},{default:o(()=>[(r(!0),R(D,null,L(t.tabNav,(e,b)=>(r(),c(g,{key:t.tabValue(b)},{default:o(()=>[B(h(e.tableTxt),1)]),_:2},1024))),128))]),_:1},8,["onClick"])]),default:o(()=>[B(h(t.tabNav[+t.tabIndex+1].tableTxt)+" ",1)]),_:1})):(r(),c(y,{key:1,value:t.tabIndex,"onUpdate:value":a[0]||(a[0]=e=>t.tabIndex=e)},{default:o(()=>[(r(!0),R(D,null,L(t.tabNav,(e,b)=>(r(),c(s,{value:t.tabValue(b),key:e.tableName},{default:o(()=>[B(h(e.tableTxt),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"]))])])]),key:"0"}:void 0]),1040,["title","enableComment","width","onRegister","onOk","onCommentOpen"])}const Ee=ct(ft,[["render",Tt],["__scopeId","data-v-e1588480"]]);export{Ee as default};
