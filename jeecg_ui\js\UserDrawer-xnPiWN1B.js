var _=Object.defineProperty,L=Object.defineProperties;var k=Object.getOwnPropertyDescriptors;var v=Object.getOwnPropertySymbols;var O=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var D=(t,o,r)=>o in t?_(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,g=(t,o)=>{for(var r in o||(o={}))O.call(o,r)&&D(t,r,o[r]);if(v)for(var r of v(o))C.call(o,r)&&D(t,r,o[r]);return t},F=(t,o)=>L(t,k(o));var I=(t,o,r)=>new Promise((y,i)=>{var f=p=>{try{l(r.next(p))}catch(c){i(c)}},u=p=>{try{l(r.throw(p))}catch(c){i(c)}},l=p=>p.done?y(p.value):Promise.resolve(p.value).then(f,u);l((r=r.apply(t,o)).next())});import{d as V,K as W,f as a,e as x,u as s,aB as N,ar as j,aD as E,k as G,aE as K}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{f as $}from"./user.data-CLRTqTDz.js";import{u as q,B as z}from"./index-JbqXEynz.js";import{g as H,b as J,c as M,s as Q}from"./user.api-mLAlJze4.js";import{useDrawerAdaptiveWidth as X}from"./useAdaptiveWidth-SDQVNQ1K.js";import{bo as Y}from"./index-CCWaWN5g.js";import{u as Z}from"./useForm-CgkFTrrO.js";import{B as ee}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";const lr=V({__name:"UserDrawer",emits:["success","register"],setup(t,{emit:o}){const r=o,y=W(),i=a(!0),f=a(""),u=a([]);let l=!1;const[p,{setProps:c,resetFields:b,setFieldsValue:w,validate:A,updateSchema:B}]=Z({labelWidth:90,schemas:$,showActionButtonGroup:!1}),d=a(!0),[R,{setDrawerProps:h,closeDrawer:S}]=q(e=>I(null,null,function*(){var n;if(yield b(),d.value=(n=e==null?void 0:e.showFooter)!=null?n:!0,h({confirmLoading:!1,showFooter:d.value}),i.value=!!(e!=null&&e.isUpdate),s(i)){f.value=e.record.id,e.record.relTenantIds&&!Array.isArray(e.record.relTenantIds)&&(e.record.relTenantIds=e.record.relTenantIds.split(","));try{const m=yield H({userid:e.record.id});m&&m.length>0&&(e.record.selectedroles=m)}catch(m){}e.record.departIds&&!Array.isArray(e.record.departIds)&&(e.record.departIds=e.record.departIds.split(",")),e.record.departIds=!e.record.departIds||e.record.departIds==""?[]:e.record.departIds}e.selectedroles&&(yield w({selectedroles:e.selectedroles})),l=(e==null?void 0:e.departDisabled)===!0,B([{field:"password",ifShow:!s(i)},{field:"confirmPassword",ifShow:!s(i)},{field:"selectedroles",show:!e.isRole},{field:"departIds",componentProps:{options:u}},{field:"selecteddeparts",show:!(e!=null&&e.departDisabled)},{field:"selectedroles",show:!(e!=null&&e.departDisabled),componentProps:{api:e.tenantSaas?J:M}},{field:"relTenantIds",componentProps:{disabled:!!e.tenantSaas}}]),!s(i)&&e.tenantSaas&&(yield w({relTenantIds:Y().toString()})),typeof e.record=="object"&&w(g({},e.record)),c({disabled:!d.value})})),T=x(()=>s(i)?s(d)?"编辑用户":"用户详情":"新增用户"),{adaptiveWidth:U}=X();function P(){return I(this,null,function*(){try{let e=yield A();h({confirmLoading:!0}),e.userIdentity===1&&(e.departIds="");let n=s(i),m=e;l&&(m=F(g({},m),{updateFromPage:"deptUsers"})),yield Q(m,n),S(),r("success",{isUpdateVal:n,values:e})}finally{h({confirmLoading:!1})}})}return(e,n)=>(j(),N(s(z),K(e.$attrs,{onRegister:s(R),title:T.value,width:s(U),onOk:P,showFooter:d.value,destroyOnClose:""}),{default:E(()=>[G(s(ee),{onRegister:s(p)},null,8,["onRegister"])]),_:1},16,["onRegister","title","width","showFooter"]))}});export{lr as default};
