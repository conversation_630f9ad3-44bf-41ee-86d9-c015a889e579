var de=Object.defineProperty,ge=Object.defineProperties;var he=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var we=Object.prototype.hasOwnProperty,me=Object.prototype.propertyIsEnumerable;var W=(e,s,o)=>s in e?de(e,s,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[s]=o,y=(e,s)=>{for(var o in s||(s={}))we.call(s,o)&&W(e,o,s[o]);if(U)for(var o of U(s))me.call(s,o)&&W(e,o,s[o]);return e},N=(e,s)=>ge(e,he(s));var Q=(e,s,o)=>new Promise((c,l)=>{var f=i=>{try{r(o.next(i))}catch(p){l(p)}},t=i=>{try{r(o.throw(i))}catch(p){l(p)}},r=i=>i.done?c(i.value):Promise.resolve(i.value).then(f,t);r((o=o.apply(e,s)).next())});import{N as x,F as M,a as z,H as A,a9 as ye,au as X,P as Ce,n as ee,I as q,q as te,K as oe,w as De}from"./index-CCWaWN5g.js";import{d as G,e as d,ag as D,aq as b,ah as v,ar as g,aA as se,as as $,aG as h,F as ke,aB as S,aE as F,aD as w,G as E,au as H,at as Y,k as I,f as k,g as L,u as a,w as j,n as ne,v as $e,aH as Z,q as Be,aC as be,aJ as ve,aK as Pe,r as re,J as T,h as Se}from"./vue-vendor-dy9k-Yad.js";import{bm as Oe,h as Te,W as Fe,ak as He}from"./antd-vue-vendor-me9YkNVC.js";import{B as Le}from"./index-CImCetrx.js";import{S as Ve}from"./index-LCGLvkB3.js";const{t:_}=x(),ae={confirmLoading:{type:Boolean},showCancelBtn:{type:Boolean,default:!0},cancelButtonProps:Object,cancelText:{type:String,default:_("common.cancelText")},showOkBtn:{type:Boolean,default:!0},okButtonProps:Object,okText:{type:String,default:_("common.okText")},okType:{type:String,default:"primary"},showFooter:{type:Boolean},footerHeight:{type:[String,Number],default:60}},Re=y({class:{type:[String,Object,Array]},isDetail:{type:Boolean},title:{type:String,default:""},loadingText:{type:String},showDetailBack:{type:Boolean,default:!0},visible:{type:Boolean},open:{type:Boolean},loading:{type:Boolean},maskClosable:{type:Boolean,default:!0},getContainer:{type:[Object,String,Function,Boolean],default:()=>"body"},closeFunc:{type:[Function,Object],default:null},destroyOnClose:{type:Boolean}},ae),Ne=G({name:"BasicDrawerFooter",props:N(y({},ae),{height:{type:String,default:"60px"}}),emits:["ok","close"],setup(e,{emit:s}){const{prefixCls:o}=M("basic-drawer-footer"),c=d(()=>{const t=`${e.height}`;return{height:t,lineHeight:t}});function l(){s("ok")}function f(){s("close")}return{handleOk:l,prefixCls:o,handleClose:f,getStyle:c}}});function Ae(e,s,o,c,l,f){const t=D("a-button");return e.showFooter||e.$slots.footer?(g(),b("div",{key:0,class:$(e.prefixCls),style:se(e.getStyle)},[e.$slots.footer?h(e.$slots,"footer",{key:1}):(g(),b(ke,{key:0},[h(e.$slots,"insertFooter"),e.showCancelBtn?(g(),S(t,F({key:0},e.cancelButtonProps,{onClick:e.handleClose,class:"mr-2"}),{default:w(()=>[E(H(e.cancelText),1)]),_:1},16,["onClick"])):v("",!0),h(e.$slots,"centerFooter"),e.showOkBtn?(g(),S(t,F({key:1,type:e.okType,onClick:e.handleOk},e.okButtonProps,{class:"mr-2",loading:e.confirmLoading}),{default:w(()=>[E(H(e.okText),1)]),_:1},16,["type","onClick","loading"])):v("",!0),h(e.$slots,"appendFooter")],64))],6)):v("",!0)}const je=z(Ne,[["render",Ae]]),qe=G({name:"BasicDrawerHeader",components:{BasicTitle:Le,ArrowLeftOutlined:Oe},props:{isDetail:A.bool,showDetailBack:A.bool,title:A.string},emits:["close"],setup(e,{emit:s}){const{prefixCls:o}=M("basic-drawer-header");function c(){s("close")}return{prefixCls:o,handleClose:c}}}),Ee={key:1};function Ie(e,s,o,c,l,f){const t=D("BasicTitle"),r=D("ArrowLeftOutlined");return e.isDetail?(g(),b("div",{key:1,class:$([e.prefixCls,`${e.prefixCls}--detail`])},[Y("span",{class:$(`${e.prefixCls}__twrap`)},[e.showDetailBack?(g(),b("span",{key:0,onClick:s[0]||(s[0]=(...i)=>e.handleClose&&e.handleClose(...i))},[I(r,{class:$(`${e.prefixCls}__back`)},null,8,["class"])])):v("",!0),e.title?(g(),b("span",Ee,H(e.title),1)):v("",!0)],2),Y("span",{class:$(`${e.prefixCls}__toolbar`)},[h(e.$slots,"titleToolbar")],2)],2)):(g(),S(t,{key:0,class:$([e.prefixCls,"is-drawer"])},{default:w(()=>[h(e.$slots,"title"),E(" "+H(e.$slots.title?"":e.title),1)]),_:3},8,["class"]))}const Me=z(qe,[["render",Ie]]),ze=G({components:{Drawer:Fe,ScrollContainer:Ve,DrawerFooter:je,DrawerHeader:Me},inheritAttrs:!1,props:Re,emits:["visible-change","open-change","ok","close","register"],setup(e,{emit:s}){const o=k(!1),c=ye(),l=k(null),{t:f}=x(),{prefixVar:t,prefixCls:r}=M("basic-drawer"),i={setDrawerProps:ue,emitVisible:void 0},p=L();p&&s("register",i,p.uid);const C=d(()=>y({},X(Te(e),a(l)))),m=d(()=>{const n=N(y(y({placement:"right"},a(c)),a(C)),{open:a(o)});n.title=void 0;let{isDetail:u,width:R,wrapClassName:O,getContainer:fe}=n;if(u){R||(n.width="100%");const K=`${r}__detail`;O=n.class?n.class:O,n.class=O?`${O} ${K}`:K,fe||(n.getContainer=`.${t}-layout-content`)}return n}),V=d(()=>y(y({},c),a(m))),J=d(()=>{const{footerHeight:n,showFooter:u}=a(m);return u&&n?Ce(n)?`${n}px`:`${n.replace("px","")}px`:"0px"}),le=d(()=>({position:"relative",height:`calc(100% - ${a(J)})`})),ie=d(()=>{var n;return!!((n=a(m))!=null&&n.loading)});j(()=>e.visible,(n,u)=>{n!==u&&(o.value=n)},{deep:!0}),j(()=>e.open,(n,u)=>{n!==u&&(o.value=n)},{deep:!0}),j(()=>o.value,n=>{ne(()=>{var u;s("visible-change",n),s("open-change",n),p&&((u=i.emitVisible)==null||u.call(i,n,p.uid))})});function ce(n){return Q(this,null,function*(){const{closeFunc:u}=a(m);if(s("close",n),u&&ee(u)){const R=yield u();o.value=!R;return}o.value=!1})}function ue(n){l.value=X(a(l)||{},n),Reflect.has(n,"visible")&&(o.value=!!n.visible),Reflect.has(n,"open")&&(o.value=!!n.open)}function pe(){s("ok")}return{onClose:ce,t:f,prefixCls:r,getMergeProps:C,getScrollContentStyle:le,getProps:m,getLoading:ie,getBindValues:V,getFooterHeight:J,handleOk:pe}}});function Ge(e,s,o,c,l,f){const t=D("DrawerHeader"),r=D("ScrollContainer"),i=D("DrawerFooter"),p=D("Drawer"),C=$e("loading");return g(),S(p,F({class:e.prefixCls,onClose:e.onClose},e.getBindValues),Z({default:w(()=>[Be((g(),S(r,{style:se(e.getScrollContentStyle),"loading-tip":e.loadingText||e.t("common.loadingText")},{default:w(()=>[h(e.$slots,"default")]),_:3},8,["style","loading-tip"])),[[C,e.getLoading]]),I(i,F(e.getProps,{onClose:e.onClose,onOk:e.handleOk,height:e.getFooterHeight}),Z({_:2},[be(Object.keys(e.$slots),m=>({name:m,fn:w(V=>[h(e.$slots,m,ve(Pe(V||{})))])}))]),1040,["onClose","onOk","height"])]),_:2},[e.$slots.title?{name:"title",fn:w(()=>[h(e.$slots,"title")]),key:"1"}:{name:"title",fn:w(()=>[I(t,{title:e.getMergeProps.title,isDetail:e.isDetail,showDetailBack:e.showDetailBack,onClose:e.onClose},{titleToolbar:w(()=>[h(e.$slots,"titleToolbar")]),_:3},8,["title","isDetail","showDetailBack","onClose"])]),key:"0"}]),1040,["class","onClose"])}const Je=z(ze,[["render",Ge]]),B=re({}),P=re({});function Ze(){if(!L())throw new Error("useDrawer() can only be used inside setup() or functional components!");const e=k(null),s=k(!1),o=k("");function c(t,r){q()&&te(()=>{e.value=null,s.value=null,B[a(o)]=null}),!(a(s)&&q()&&t===a(e))&&(o.value=r,e.value=t,s.value=!0,t.emitVisible=(i,p)=>{P[p]=i})}const l=()=>{const t=a(e);return t||oe("useDrawer instance is undefined!"),t},f={setDrawerProps:t=>{var r;(r=l())==null||r.setDrawerProps(t)},getVisible:d(()=>P[~~a(o)]),getOpen:d(()=>P[~~a(o)]),openDrawer:(t=!0,r,i=!0)=>{var C;if((C=l())==null||C.setDrawerProps({open:t}),!r)return;if(i){B[a(o)]=null,B[a(o)]=T(r);return}He(T(B[a(o)]),T(r))||(B[a(o)]=T(r))},closeDrawer:()=>{var t;(t=l())==null||t.setDrawerProps({open:!1})}};return[c,f]}const _e=e=>{const s=k(null),o=L(),c=k("");if(!L())throw new Error("useDrawerInner() can only be used inside setup() or functional components!");const l=()=>{const t=a(s);if(!t){oe("useDrawerInner instance is undefined!");return}return t},f=(t,r)=>{q()&&te(()=>{s.value=null}),c.value=r,s.value=t,o==null||o.emit("register",t,r)};return Se(()=>{const t=B[a(c)];t&&(!e||!ee(e)||ne(()=>{e(t)}))}),[f,{changeLoading:(t=!0)=>{var r;(r=l())==null||r.setDrawerProps({loading:t})},changeOkLoading:(t=!0)=>{var r;(r=l())==null||r.setDrawerProps({confirmLoading:t})},getVisible:d(()=>P[~~a(c)]),getOpen:d(()=>P[~~a(c)]),closeDrawer:()=>{var t;(t=l())==null||t.setDrawerProps({open:!1})},setDrawerProps:t=>{var r;(r=l())==null||r.setDrawerProps(t)}}]},xe=De(Je);export{xe as B,Ze as a,_e as u};
