import{d as U,e as P,ag as l,v as k,aB as u,ar as a,aD as s,k as r,at as E,ah as f,q as R,G as t,au as S}from"./vue-vendor-dy9k-Yad.js";import{V as A,A as D}from"./antd-vue-vendor-me9YkNVC.js";import B from"./CurrentPermissionMode-DzGbOpfp.js";import{X as $,ah as b,bE as y,a as V}from"./index-CCWaWN5g.js";import{A as N}from"./index-LZBOYjDt.js";import{P as w}from"./index-CtJ0w2CP.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const L=U({components:{Alert:D,PageWrapper:w,CurrentPermissionMode:B,Divider:A,Authority:N},setup(){const{changeRole:o,hasPermission:e}=$(),m=b();return{userStore:m,RoleEnum:y,isSuper:P(()=>m.getRoleList.includes(y.SUPER)),isTest:P(()=>m.getRoleList.includes(y.TEST)),changeRole:o,hasPermission:e}}}),M={class:"mt-4"};function W(o,e,m,q,G,I){const T=l("CurrentPermissionMode"),v=l("Alert"),n=l("a-button"),_=l("a-button-group"),i=l("Divider"),p=l("Authority"),g=l("PageWrapper"),d=k("auth");return a(),u(g,{title:"前端权限按钮示例",contentBackground:"",contentClass:"p-4",content:"由于刷新的时候会请求用户信息接口，会根据接口重置角色信息，所以刷新后界面会恢复原样，如果不需要，可以注释 src/layout/default/index内的获取用户信息接口"},{default:s(()=>[r(T),E("p",null,[e[2]||(e[2]=t(" 当前角色: ")),E("a",null,S(o.userStore.getRoleList),1)]),r(v,{class:"mt-4",type:"info",message:"点击后请查看按钮变化","show-icon":""}),E("div",M,[e[3]||(e[3]=t(" 权限切换(请先切换权限模式为前端角色权限模式): ")),r(_,null,{default:s(()=>[r(n,{onClick:e[0]||(e[0]=C=>o.changeRole(o.RoleEnum.SUPER)),type:o.isSuper?"primary":"default"},{default:s(()=>[t(S(o.RoleEnum.SUPER),1)]),_:1},8,["type"]),r(n,{onClick:e[1]||(e[1]=C=>o.changeRole(o.RoleEnum.TEST)),type:o.isTest?"primary":"default"},{default:s(()=>[t(S(o.RoleEnum.TEST),1)]),_:1},8,["type"])]),_:1})]),r(i,null,{default:s(()=>e[4]||(e[4]=[t("组件方式判断权限(有需要可以自行全局注册)")])),_:1,__:[4]}),r(p,{value:o.RoleEnum.SUPER},{default:s(()=>[r(n,{type:"primary",class:"mx-4"},{default:s(()=>e[5]||(e[5]=[t(" 拥有super角色权限可见 ")])),_:1,__:[5]})]),_:1},8,["value"]),r(p,{value:o.RoleEnum.TEST},{default:s(()=>[r(n,{color:"success",class:"mx-4"},{default:s(()=>e[6]||(e[6]=[t(" 拥有test角色权限可见 ")])),_:1,__:[6]})]),_:1},8,["value"]),r(p,{value:[o.RoleEnum.TEST,o.RoleEnum.SUPER]},{default:s(()=>[r(n,{color:"error",class:"mx-4"},{default:s(()=>e[7]||(e[7]=[t(" 拥有[test,super]角色权限可见 ")])),_:1,__:[7]})]),_:1},8,["value"]),r(i,null,{default:s(()=>e[8]||(e[8]=[t("函数方式方式判断权限(适用于函数内部过滤)")])),_:1,__:[8]}),o.hasPermission(o.RoleEnum.SUPER)?(a(),u(n,{key:0,type:"primary",class:"mx-4"},{default:s(()=>e[9]||(e[9]=[t(" 拥有super角色权限可见 ")])),_:1,__:[9]})):f("",!0),o.hasPermission(o.RoleEnum.TEST)?(a(),u(n,{key:1,color:"success",class:"mx-4"},{default:s(()=>e[10]||(e[10]=[t(" 拥有test角色权限可见 ")])),_:1,__:[10]})):f("",!0),o.hasPermission([o.RoleEnum.TEST,o.RoleEnum.SUPER])?(a(),u(n,{key:2,color:"error",class:"mx-4"},{default:s(()=>e[11]||(e[11]=[t(" 拥有[test,super]角色权限可见 ")])),_:1,__:[11]})):f("",!0),r(i,null,{default:s(()=>e[12]||(e[12]=[t("指令方式方式判断权限(该方式不能动态修改权限.)")])),_:1,__:[12]}),R((a(),u(n,{type:"primary",class:"mx-4"},{default:s(()=>e[13]||(e[13]=[t(" 拥有super角色权限可见 ")])),_:1,__:[13]})),[[d,o.RoleEnum.SUPER]]),R((a(),u(n,{color:"success",class:"mx-4"},{default:s(()=>e[14]||(e[14]=[t(" 拥有test角色权限可见 ")])),_:1,__:[14]})),[[d,o.RoleEnum.TEST]]),R((a(),u(n,{color:"error",class:"mx-4"},{default:s(()=>e[15]||(e[15]=[t(" 拥有[test,super]角色权限可见 ")])),_:1,__:[15]})),[[d,[o.RoleEnum.TEST,o.RoleEnum.SUPER]]])]),_:1})}const c=V(L,[["render",W],["__scopeId","data-v-18603eae"]]);export{c as default};
