import{d as i,e as r,ag as l,aq as p,ar as m,as as d,at as c,k as u,au as f,aE as g}from"./vue-vendor-dy9k-Yad.js";import{w as C}from"./antd-vue-vendor-me9YkNVC.js";import{F as b,a as S}from"./index-CCWaWN5g.js";import{b as y}from"./index-CI-8_pdX.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-JbqXEynz.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useHeaderSetting-C-h5S52e.js";import"./useMultipleTabSetting-QBbnIi9J.js";const h=i({name:"SelectItem",components:{Select:C},props:{event:{type:Number},disabled:{type:Boolean},title:{type:String},def:{type:[String,Number]},initValue:{type:[String,Number]},options:{type:Array,default:()=>[]}},setup(e){const{prefixCls:t}=b("setting-select-item"),a=r(()=>e.def?{value:e.def,defaultValue:e.initValue||e.def}:{});function n(s){e.event&&y(e.event,s)}return{prefixCls:t,handleChange:n,getBindValue:a}}});function v(e,t,a,n,s,V){const o=l("Select");return m(),p("div",{class:d(e.prefixCls)},[c("span",null,f(e.title),1),u(o,g(e.getBindValue,{class:`${e.prefixCls}-select`,onChange:e.handleChange,disabled:e.disabled,size:"small",options:e.options}),null,16,["class","onChange","disabled","options"])],2)}const P=S(h,[["render",v],["__scopeId","data-v-317e2e94"]]);export{P as default};
