var l=(f,b,m)=>new Promise((g,n)=>{var a=o=>{try{p(m.next(o))}catch(u){n(u)}},h=o=>{try{p(m.throw(o))}catch(u){n(u)}},p=o=>o.done?g(o.value):Promise.resolve(o.value).then(a,h);p((m=m.apply(f,b)).next())});import{d as z,f as k,ag as d,aB as R,ar as y,aD as r,k as i,u as s,ah as I,G as _}from"./vue-vendor-dy9k-Yad.js";import{u as A}from"./index-BkGZ5fiW.js";import{u as G,a as P,B as Q}from"./index-JbqXEynz.js";import"./index-Diw57m_E.js";import $ from"./UserDrawer-xnPiWN1B.js";import q from"./UseSelectModal-B88seyfc.js";import{u as J,c as X,e as Y,f as Z}from"./role.api-BvRyEQIC.js";import{b as ee,u as te}from"./role.data-BepCB_2a.js";import{g as oe}from"./user.api-mLAlJze4.js";import{ad as re,a as ie}from"./index-CCWaWN5g.js";import ne from"./BasicTable-xCEZpGLb.js";import{Q as ae}from"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./user.data-CLRTqTDz.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";import"./useAdaptiveWidth-SDQVNQ1K.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const se=z({__name:"RoleUserTable",props:{disableUserEdit:{type:Boolean,default:!1}},emits:["register","hideUserList"],setup(f,{emit:b}){const m=b,g=f,n=k([]),a=k(""),[h]=G(e=>l(null,null,function*(){a.value=e.id,x({searchInfo:{roleId:e.id}}),c()})),[p,{openDrawer:o}]=P(),[u,{openModal:S}]=re(),[U,{reload:c,updateTableDataRecord:v,setProps:x}]=A({title:"用户列表",api:J,columns:te,maxHeight:500,formConfig:{labelWidth:60,schemas:ee,autoSubmitOnEnter:!0},striped:!0,useSearchForm:!0,showTableSetting:!1,clickToRowSelect:!1,bordered:!1,showIndexColumn:!1,tableSetting:{fullScreen:!0,cacheKey:"role_user_table"},canResize:!0,rowKey:"id",actionColumn:{width:180,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:void 0}}),D={type:"checkbox",columnWidth:50,selectedRowKeys:n,onChange:T};function T(e,t){n.value=e}function B(){o(!0,{isUpdate:!1,selectedroles:[a.value],isRole:!0})}function E(e){return l(this,null,function*(){try{const t=yield oe({userid:e.id});t&&t.length>0&&(e.selectedroles=t)}catch(t){}o(!0,{record:e,isUpdate:!0,isRole:!0})})}function K(e){return l(this,null,function*(){yield X({userId:e.id,roleId:a.value},c)})}function M(){return l(this,null,function*(){yield Y({userIds:n.value.join(","),roleId:a.value},()=>{c(),n.value=[]})})}function L({isUpdate:e,values:t}){e?v(t.id,t):c()}function N(){S(!0)}function O(e){return l(this,null,function*(){yield Z({roleId:a.value,userIdList:e},c)})}function V(e){return[{label:"编辑",onClick:E.bind(null,e),ifShow:()=>!g.disableUserEdit},{label:"取消关联",popConfirm:{title:"是否确认取消关联",confirm:K.bind(null,e)}}]}return(e,t)=>{const w=d("a-button"),C=d("Icon"),F=d("a-menu-item"),H=d("a-menu"),W=d("a-dropdown");return y(),R(s(Q),{onRegister:s(h),title:"角色用户",width:"800",destroyOnClose:""},{default:r(()=>[i(s(ne),{onRegister:s(U),rowSelection:D},{tableTitle:r(()=>[f.disableUserEdit?I("",!0):(y(),R(w,{key:0,type:"primary",onClick:B},{default:r(()=>t[0]||(t[0]=[_(" 新增用户")])),_:1,__:[0]})),i(w,{type:"primary",onClick:N},{default:r(()=>t[1]||(t[1]=[_(" 已有用户")])),_:1,__:[1]}),n.value.length>0?(y(),R(W,{key:1},{overlay:r(()=>[i(H,null,{default:r(()=>[i(F,{key:"1",onClick:M},{default:r(()=>[i(C,{icon:"bx:bx-unlink"}),t[2]||(t[2]=_(" 取消关联 "))]),_:1,__:[2]})]),_:1})]),default:r(()=>[i(w,null,{default:r(()=>[t[3]||(t[3]=_("批量操作 ")),i(C,{icon:"ant-design:down-outlined"})]),_:1,__:[3]})]),_:1})):I("",!0)]),action:r(({record:j})=>[i(s(ae),{actions:V(j)},null,8,["actions"])]),_:1},8,["onRegister"]),i($,{onRegister:s(p),onSuccess:L},null,8,["onRegister"]),i(q,{onRegister:s(u),onSelect:O},null,8,["onRegister"])]),_:1},8,["onRegister"])}}}),Ct=ie(se,[["__scopeId","data-v-82ab065f"]]);export{Ct as default};
