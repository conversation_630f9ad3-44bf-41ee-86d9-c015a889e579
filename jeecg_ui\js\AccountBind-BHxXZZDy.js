import{L as s}from"./antd-vue-vendor-me9YkNVC.js";import{d as I,ag as e,aB as n,ar as o,aD as a,k as c,aq as L,F as k,aC as x,at as v,au as r,G as i,ah as l}from"./vue-vendor-dy9k-Yad.js";import{C as B}from"./index-LCGLvkB3.js";import{c as y,a as M}from"./index-CCWaWN5g.js";import{a as N}from"./data-DwTukD7a.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";const V=I({components:{CollapseContainer:B,List:s,ListItem:s.Item,ListItemMeta:s.Item.Meta,Icon:y},setup(){return{list:N}}});function $(p,g,h,b,D,E){const m=e("Icon"),_=e("a-button"),u=e("ListItemMeta"),d=e("ListItem"),f=e("List"),C=e("CollapseContainer");return o(),n(C,{title:"账号绑定",canExpan:!1},{default:a(()=>[c(f,null,{default:a(()=>[(o(!0),L(k,null,x(p.list,t=>(o(),n(d,{key:t.key},{default:a(()=>[c(u,null,{avatar:a(()=>[t.avatar?(o(),n(m,{key:0,class:"avatar",icon:t.avatar,color:t.color},null,8,["icon","color"])):l("",!0)]),title:a(()=>[i(r(t.title)+" ",1),t.extra?(o(),n(_,{key:0,type:"link",size:"small",class:"extra"},{default:a(()=>[i(r(t.extra),1)]),_:2},1024)):l("",!0)]),description:a(()=>[v("div",null,r(t.description),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})}const P=M(V,[["render",$],["__scopeId","data-v-24c6ada8"]]);export{P as default};
