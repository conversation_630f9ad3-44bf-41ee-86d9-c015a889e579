import{d as w,ag as t,aq as g,ar as a,ah as d,q as v,k as o,F as D,aC as V,aB as i,aD as n,at as p,au as f,G as u,aE as B,B as M}from"./vue-vendor-dy9k-Yad.js";import"./index-B4ez5KWV.js";import{cj as N,a as P}from"./index-CCWaWN5g.js";import{e as U,c as J,u as $}from"./useFileCell-C3OnsDV3.js";import{U as L}from"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const R=w({name:"JVxeFileCell",components:J,props:N(),setup(e){return $(e,L.file)},enhanced:U}),T={style:{"margin-left":"5px"}},E={style:{"margin-left":"5px"}};function I(e,l,O,q,S,j){const C=t("LoadingOutlined"),m=t("a-tooltip"),s=t("Icon"),c=t("a-menu-item"),k=t("a-menu"),_=t("Dropdown"),y=t("a-button"),h=t("a-upload"),b=t("JUploadModal");return a(),g("div",null,[e.hasFile?(a(!0),g(D,{key:0},V([e.innerFile||{}],(r,F)=>(a(),g("div",{key:F,style:{position:"relative"}},[r.status==="uploading"?(a(),i(m,{key:0,title:`上传中(${Math.floor(r.percent)}%)`},{default:n(()=>[o(C),l[0]||(l[0]=p("span",{style:{"margin-left":"5px"}},"上传中…",-1))]),_:2,__:[0]},1032,["title"])):r.status==="done"?(a(),i(m,{key:1,title:r.name},{default:n(()=>[o(s,{icon:"ant-design:paper-clip"}),p("span",T,f(e.ellipsisFileName),1)]),_:2},1032,["title"])):(a(),i(m,{key:2,title:r.message||"上传失败"},{default:n(()=>[o(s,{icon:"ant-design:exclamation-circle",style:{color:"red"}}),p("span",E,f(e.ellipsisFileName),1)]),_:2},1032,["title"])),o(_,{trigger:["click"],placement:"bottomRight",style:{"margin-left":"10px"}},{overlay:n(()=>[o(k,null,{default:n(()=>[e.originColumn.allowDownload!==!1?(a(),i(c,{key:0,onClick:e.handleClickDownloadFile},{default:n(()=>[p("span",null,[o(s,{icon:"ant-design:download"}),l[1]||(l[1]=u(" 下载"))])]),_:1},8,["onClick"])):d("",!0),e.originColumn.allowRemove!==!1?(a(),i(c,{key:1,disabled:e.cellProps.disabled,onClick:e.handleClickDeleteFile},{default:n(()=>[p("span",null,[o(s,{icon:"ant-design:delete"}),l[2]||(l[2]=u(" 删除"))])]),_:1},8,["disabled","onClick"])):d("",!0),o(c,{disabled:e.cellProps.disabled,onClick:e.handleMoreOperation},{default:n(()=>[p("span",null,[o(s,{icon:"ant-design:bars"}),l[3]||(l[3]=u(" 更多"))])]),_:1},8,["disabled","onClick"])]),_:1})]),default:n(()=>[o(m,{title:"操作"},{default:n(()=>[r.status!=="uploading"?(a(),i(s,{key:0,icon:"ant-design:setting",style:{cursor:"pointer"}})):d("",!0)]),_:2},1024)]),_:2},1024)]))),128)):d("",!0),e.cellProps.disabledTable?d("",!0):v((a(),i(h,B({key:1,name:"file",data:{isup:1},multiple:!1,action:e.uploadAction,headers:e.uploadHeaders,showUploadList:!1},e.cellProps,{onChange:e.handleChangeUpload}),{default:n(()=>[o(y,{preIcon:"ant-design:upload"},{default:n(()=>[u(f(e.originColumn.btnText||"点击上传"),1)]),_:1})]),_:1},16,["action","headers","onChange"])),[[M,!e.hasFile]]),o(b,{value:e.modalValue,onRegister:e.registerModel,onChange:e.onModalChange},null,8,["value","onRegister","onChange"])])}const re=P(R,[["render",I]]);export{re as default};
