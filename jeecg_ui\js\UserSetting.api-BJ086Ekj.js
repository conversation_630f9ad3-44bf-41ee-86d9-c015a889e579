import{j as s}from"./index-CCWaWN5g.js";const r=e=>s.post({url:"/sys/user/login/setting/userEdit",params:e},{isTransformResponse:!1}),t=()=>s.get({url:"/sys/user/login/setting/getUserData"},{isTransformResponse:!1});const a=e=>s.put({url:"/sys/user/updateMobile",params:e},{isTransformResponse:!1}),o=e=>s.get({url:"/sys/user/passwordChange",params:e},{isTransformResponse:!1}),u=e=>s.get({url:"/sys/tenant/getTenantListByUserId",params:e},{isTransformResponse:!1}),i=e=>s.put({url:"/sys/tenant/cancelApplyTenant",data:e},{joinParamsToUrl:!0,isTransformResponse:!1}),l=e=>s.delete({url:"/sys/tenant/exitUserTenant",params:e},{isTransformResponse:!1,joinParamsToUrl:!0}),d=e=>s.post({url:"/sys/tenant/changeOwenUserTenant",params:e},{isTransformResponse:!1,joinParamsToUrl:!0}),c=e=>s.get({url:"/sys/thirdApp/getThirdAccountByUserId",params:e},{isTransformResponse:!1}),T=e=>s.post({url:"/sys/thirdApp/bindThirdAppAccount",params:e},{isTransformResponse:!1,joinParamsToUrl:!0}),y=e=>s.delete({url:"/sys/thirdApp/deleteThirdAccount",params:e},{isTransformResponse:!1,joinParamsToUrl:!0}),p=e=>s.put({url:"/sys/tenant/agreeOrRefuseJoinTenant",params:e},{joinParamsToUrl:!0}),g=e=>s.put({url:"/sys/user/changePhone",params:e},{joinParamsToUrl:!0,isTransformResponse:!1});export{g as a,r as b,d as c,o as d,u as e,i as f,t as g,l as h,p as i,c as j,T as k,y as l,a as u};
