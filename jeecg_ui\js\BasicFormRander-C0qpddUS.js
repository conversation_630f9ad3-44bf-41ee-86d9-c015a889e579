import{d as c,ag as d,aB as b,ar as f,aD as i,k as a,at as n,u as s,l as N}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{c as _}from"./JAddInput-CxJ-JBK-.js";import{j as B}from"./antd-vue-vendor-me9YkNVC.js";import{u as h}from"./useForm-CgkFTrrO.js";import{B as g}from"./BasicForm-DBcXiHk0.js";import{a as v}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";const x=c({__name:"BasicFormRander",setup(y){const u=[{field:"productName",label:"商品名称",component:"Input"},{field:"price",label:"价格",component:"InputNumber"},{field:"buyNums",label:"购买个数",component:"InputNumber",render:({model:t,field:o})=>N(B,{placeholder:"请输入购买个数",value:t[o],style:{width:"100%"},type:"number",onChange:p=>{t[o]=p.target.value}})},{field:"describe",label:"描述",component:"Input",componentProps:{disabled:!0},render:({values:t})=>{let o=t.productName?t.productName:"",p=t.price?t.price:0,r=t.buyNums?t.buyNums:0;return"购买商品名称："+o+", 总价格: "+p*r+"元"}}],[l]=h({schemas:u,showResetButton:!1,labelWidth:"150px",submitButtonOptions:{text:"提交",preIcon:""},actionColOptions:{span:17}});return(t,o)=>{const p=d("a-input");return f(),b(s(g),{onRegister:s(l),style:{"margin-top":"20px"}},{phone:i(({model:r,field:e})=>[a(p,{value:r[e],"onUpdate:value":m=>r[e]=m,placeholder:"请输入手机号"},null,8,["value","onUpdate:value"]),o[0]||(o[0]=n("span",{class:"font-color"},"请输入您的手机号，方便我们联系您",-1))]),feedback:i(({model:r,field:e})=>[a(_,{value:r[e],"onUpdate:value":m=>r[e]=m,placeholder:"请输入问题反馈"},null,8,["value","onUpdate:value"]),o[1]||(o[1]=n("span",{class:"font-color"},"请您图文并茂，方便我们了解问题并及时反馈",-1))]),_:1},8,["onRegister"])}}}),It=v(x,[["__scopeId","data-v-bb010d6e"]]);export{It as default};
