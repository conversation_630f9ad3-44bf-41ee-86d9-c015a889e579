import{d as T,r as S,ag as e,aB as _,ar as C,aD as i,k as n,ah as w,G as y}from"./vue-vendor-dy9k-Yad.js";import{u as k}from"./index-BkGZ5fiW.js";import{b as A}from"./system-bqUZCbh5.js";import{P as D}from"./index-CtJ0w2CP.js";import I from"./DeptTree-CAotd4Ub.js";import"./index-Diw57m_E.js";import{A as M,s as B,c as R}from"./AccountModal-DeZqrx8T.js";import{bu as V,ad as F,a as P}from"./index-CCWaWN5g.js";import{Q as W}from"./componentMap-Bkie1n3v.js";import $ from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./index-BtIdS_Qz.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const E=T({name:"system-account",components:{BasicTable:$,PageWrapper:D,DeptTree:I,AccountModal:M,TableAction:W},setup(){const o=V(),[r,{openModal:a}]=F(),p=S({}),[b,{reload:m,updateTableDataRecord:s}]=k({title:"账号列表",api:A,rowKey:"id",columns:R,formConfig:{labelWidth:120,schemas:B,autoSubmitOnEnter:!0},useSearchForm:!0,showTableSetting:!0,bordered:!0,handleSearchInfoFn(t){return t},actionColumn:{width:120,title:"操作",dataIndex:"action"}});function l(){a(!0,{isUpdate:!1})}function c(t){a(!0,{record:t,isUpdate:!0})}function d(t){}function u({isUpdate:t,values:g}){if(t){const x=s(g.id,g)}else m()}function f(t=""){p.deptId=t,m()}function h(t){o("/system/account_detail/"+t.id)}return{registerTable:b,registerModal:r,handleCreate:l,handleEdit:c,handleDelete:d,handleSuccess:u,handleSelect:f,handleView:h,searchInfo:p}}});function N(o,r,a,p,b,m){const s=e("DeptTree"),l=e("a-button"),c=e("TableAction"),d=e("BasicTable"),u=e("AccountModal"),f=e("PageWrapper");return C(),_(f,{dense:"",contentFullHeight:"",fixedHeight:"",contentClass:"flex"},{default:i(()=>[n(s,{class:"w-1/4 xl:w-1/5",onSelect:o.handleSelect},null,8,["onSelect"]),n(d,{onRegister:o.registerTable,class:"w-3/4 xl:w-4/5",searchInfo:o.searchInfo},{toolbar:i(()=>[n(l,{type:"primary",onClick:o.handleCreate},{default:i(()=>r[0]||(r[0]=[y("新增账号")])),_:1,__:[0]},8,["onClick"])]),bodyCell:i(({column:h,record:t})=>[h.key==="action"?(C(),_(c,{key:0,actions:[{icon:"clarity:info-standard-line",tooltip:"查看用户详情",onClick:o.handleView.bind(null,t)},{icon:"clarity:note-edit-line",tooltip:"编辑用户资料",onClick:o.handleEdit.bind(null,t)},{icon:"ant-design:delete-outlined",color:"error",tooltip:"删除此账号",popConfirm:{title:"是否确认删除",placement:"left",confirm:o.handleDelete.bind(null,t)}}]},null,8,["actions"])):w("",!0)]),_:1},8,["onRegister","searchInfo"]),n(u,{onRegister:o.registerModal,onSuccess:o.handleSuccess},null,8,["onRegister","onSuccess"])]),_:1})}const zt=P(E,[["render",N]]);export{zt as default};
