import{d as _,f as a,e as v,u as e,aq as y,ar as x,k as H,aD as w,at as S,as as l,aA as m}from"./vue-vendor-dy9k-Yad.js";import{n as R}from"./antd-vue-vendor-me9YkNVC.js";import{useWindowSizeFn as k}from"./useWindowSizeFn-DDbrQbks.js";import{H as z,F as C,a as B}from"./index-CCWaWN5g.js";import{u as F}from"./useContentViewHeight-Md7r1NIg.js";import"./vxe-table-vendor-B22HppNm.js";import"./usePageContext-CxiNGbPs.js";const L=["src"],$=_({__name:"index",props:{frameSrc:z.string.def("")},setup(p){const n=a(!0),u=a(50),o=a(window.innerHeight),r=a(),{headerHeightRef:d}=F(),{prefixCls:i}=C("iframe-page");k(f,150,{immediate:!0});const c=v(()=>({height:`${e(o)}px`}));function f(){const s=e(r);if(!s)return;const t=d.value;u.value=t,o.value=window.innerHeight-t;const h=document.documentElement.clientHeight-t;s.style.height=`${h}px`}function g(){n.value=!1,f()}return(s,t)=>(x(),y("div",{class:l(e(i)),style:m(c.value)},[H(e(R),{spinning:n.value,size:"large",style:m(c.value)},{default:w(()=>[S("iframe",{src:p.frameSrc,class:l(`${e(i)}__main`),ref_key:"frameRef",ref:r,onLoad:g},null,42,L)]),_:1},8,["spinning","style"])],6))}}),I=B($,[["__scopeId","data-v-3ac3885e"]]);export{I as default};
