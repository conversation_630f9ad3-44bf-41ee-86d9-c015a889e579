var et=Object.defineProperty,at=Object.defineProperties;var st=Object.getOwnPropertyDescriptors;var G=Object.getOwnPropertySymbols;var ot=Object.prototype.hasOwnProperty,nt=Object.prototype.propertyIsEnumerable;var A=(t,o,n)=>o in t?et(t,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[o]=n,j=(t,o)=>{for(var n in o||(o={}))ot.call(o,n)&&A(t,n,o[n]);if(G)for(var n of G(o))nt.call(o,n)&&A(t,n,o[n]);return t},$=(t,o)=>at(t,st(o));var N=(t,o,n)=>new Promise((d,g)=>{var v=e=>{try{i(n.next(e))}catch(r){g(r)}},y=e=>{try{i(n.throw(e))}catch(r){g(r)}},i=e=>e.done?d(e.value):Promise.resolve(e.value).then(v,y);i((n=n.apply(t,o)).next())});import{d as V,f as R,e as S,u as T,w as it,ag as l,aB as h,ar as p,as as E,aD as f,aq as b,F as I,aC as rt,aA as lt,k as c,at as P,ah as C,au as O,G as ct,r as pt,o as dt,g as mt,aE as ut}from"./vue-vendor-dy9k-Yad.js";import{X as L,bi as gt,L as k,a3 as F,Z as yt,a4 as q,cn as ft,_ as vt}from"./antd-vue-vendor-me9YkNVC.js";import{j as W,F as X,P as ht,a as Z,ah as bt,b5 as _t,ad as H,aZ as Ct,ci as kt}from"./index-CCWaWN5g.js";import{T as Mt}from"./index-CXHeQyuE.js";import wt from"./DetailModal-DbdGfGfj.js";import St from"./DynamicNotice-ISEJ4Nrz.js";import"./index-Diw57m_E.js";import{connectWebSocket as Tt,onWebSocket as Bt}from"./useWebSocket-B-g1Ud0G.js";import{r as Dt}from"./mynews.api-3wYWW1k0.js";import jt from"./SysMessageModal-Vow4XX6i.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";var J=(t=>(t.L="L",t.M="M",t.H="H",t))(J||{});const Nt=[{key:"1",name:"通知",list:[{id:"000000001",avatar:"https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png",title:"你收到了 14 份新周报",description:"",datetime:"2017-08-09",type:"1"},{id:"000000002",avatar:"https://gw.alipayobjects.com/zos/rmsportal/OKJXDXrmkNshAMvwtvhu.png",title:"你推荐的 曲妮妮 已通过第三轮面试",description:"",datetime:"2017-08-08",type:"1"},{id:"000000003",avatar:"https://gw.alipayobjects.com/zos/rmsportal/kISTdvpyTAhtGxpovNWd.png",title:"这种模板可以区分多种通知类型",description:"",datetime:"2017-08-07",type:"1"},{id:"000000004",avatar:"https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png",title:"左侧图标用于区分不同的类型",description:"",datetime:"2017-08-07",type:"1"},{id:"000000005",avatar:"https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png",title:"标题可以设置自动显示省略号，本例中标题行数已设为1行，如果内容超过1行将自动截断并支持tooltip显示完整标题。",description:"",datetime:"2017-08-07",type:"1"},{id:"000000006",avatar:"https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png",title:"左侧图标用于区分不同的类型",description:"",datetime:"2017-08-07",type:"1"},{id:"000000007",avatar:"https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png",title:"左侧图标用于区分不同的类型",description:"",datetime:"2017-08-07",type:"1"},{id:"000000008",avatar:"https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png",title:"左侧图标用于区分不同的类型",description:"",datetime:"2017-08-07",type:"1"},{id:"000000009",avatar:"https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png",title:"左侧图标用于区分不同的类型",description:"",datetime:"2017-08-07",type:"1"},{id:"000000010",avatar:"https://gw.alipayobjects.com/zos/rmsportal/GvqBnKhFgObvnSGkDsje.png",title:"左侧图标用于区分不同的类型",description:"",datetime:"2017-08-07",type:"1"}],count:0},{key:"2",name:"系统消息",list:[{id:"000000006",avatar:"https://gw.alipayobjects.com/zos/rmsportal/fcHMVNCjPOsbUGdEduuv.jpeg",title:"曲丽丽 评论了你",description:"描述信息描述信息描述信息",datetime:"2017-08-07",type:"2",clickClose:!0},{id:"000000007",avatar:"https://gw.alipayobjects.com/zos/rmsportal/fcHMVNCjPOsbUGdEduuv.jpeg",title:"朱偏右 回复了你",description:"这种模板用于提醒谁与你发生了互动",datetime:"2017-08-07",type:"2",clickClose:!0},{id:"000000008",avatar:"https://gw.alipayobjects.com/zos/rmsportal/fcHMVNCjPOsbUGdEduuv.jpeg",title:"标题",description:"请将鼠标移动到此处，以便测试超长的消息在此处将如何处理。本例中设置的描述最大行数为2，超过2行的描述内容将被省略并且可以通过tooltip查看完整内容",datetime:"2017-08-07",type:"2",clickClose:!0}],count:0}];const Pt=t=>W.get({url:"/sys/annountCement/listByUser",params:t});const K=(t,o)=>W.put({url:"/sys/sysAnnouncementSend/editByAnntIdAndUserId",params:j({anntId:t},o)});const Rt=V({components:{[F.name]:F,[k.name]:k,[k.Item.name]:k.Item,AListItemMeta:k.Item.Meta,ATypographyParagraph:gt.Paragraph,[L.name]:L,Time:Mt},props:{list:{type:Array,default:()=>[]},pageSize:{type:[Boolean,Number],default:5},currentPage:{type:Number,default:1},titleRows:{type:Number,default:1},descRows:{type:Number,default:1},onTitleClick:{type:Function}},emits:["update:currentPage"],setup(t,{emit:o}){const{prefixCls:n}=X("header-notify-list"),d=R(t.currentPage||1),g=S(()=>{const{pageSize:e,list:r}=t;if(e===!1)return[];let m=ht(e)?e:5;return r.slice(m*(T(d)-1),m*T(d))});it(()=>t.currentPage,e=>{d.value=e});const v=S(()=>!!t.onTitleClick),y=S(()=>{const{list:e,pageSize:r}=t;return r>0&&e&&e.length>r?{total:e.length,pageSize:r,current:T(d),onChange(m){d.value=m,o("update:currentPage",m)}}:!1});function i(e){t.onTitleClick&&t.onTitleClick(e)}return{prefixCls:n,getPagination:y,getData:g,handleTitleClick:i,isTitleClickable:v,PriorityTypes:J}}}),Ut={class:"title"},zt={key:0,class:"extra"},Gt={key:2},At={key:0,class:"description"},$t={class:"datetime"};function It(t,o,n,d,g,v){const y=l("a-typography-paragraph"),i=l("a-tag"),e=l("a-avatar"),r=l("Icon"),m=l("Time"),B=l("a-list-item-meta"),_=l("a-list-item"),M=l("a-list");return p(),h(M,{class:E(t.prefixCls),pagination:t.getPagination},{default:f(()=>[(p(!0),b(I,null,rt(t.getData,s=>(p(),h(_,{key:s.id,class:"list-item",onClick:U=>t.handleTitleClick(s),style:lt({cursor:t.isTitleClickable?"pointer":""})},{default:f(()=>[c(B,null,{title:f(()=>[P("div",Ut,[c(y,{style:{width:"100%","margin-bottom":"0 !important"},delete:!!s.titleDelete,ellipsis:t.$props.titleRows&&t.$props.titleRows>0?{rows:t.$props.titleRows,tooltip:!!s.title}:!1,content:s.title},null,8,["delete","ellipsis","content"]),s.extra?(p(),b("div",zt,[c(i,{class:"tag",color:s.color},{default:f(()=>[ct(O(s.extra),1)]),_:2},1032,["color"])])):C("",!0)])]),avatar:f(()=>[s.avatar?(p(),h(e,{key:0,class:"avatar",src:s.avatar},null,8,["src"])):s.priority?(p(),b(I,{key:1},[s.priority===t.PriorityTypes.L?(p(),h(e,{key:0,class:"avatar priority-L",title:"一般消息"},{icon:f(()=>[c(r,{icon:"entypo:info"})]),_:1})):C("",!0),s.priority===t.PriorityTypes.M?(p(),h(e,{key:1,class:"avatar priority-M",title:"重要消息"},{icon:f(()=>[c(r,{icon:"bi:exclamation-lg"})]),_:1})):C("",!0),s.priority===t.PriorityTypes.H?(p(),h(e,{key:2,class:"avatar priority-H",title:"紧急消息"},{icon:f(()=>[c(r,{icon:"ant-design:warning-filled"})]),_:1})):C("",!0)],64)):(p(),b("span",Gt,O(s.avatar),1))]),description:f(()=>[P("div",null,[s.description?(p(),b("div",At,[c(y,{style:{width:"100%","margin-bottom":"0 !important"},ellipsis:t.$props.descRows&&t.$props.descRows>0?{rows:t.$props.descRows,tooltip:!!s.description}:!1,content:s.description},null,8,["ellipsis","content"])])):C("",!0),P("div",$t,[c(m,{value:s.datetime,title:s.datetime},null,8,["value","title"])])])]),_:2},1024)]),_:2},1032,["onClick","style"]))),128))]),_:1},8,["class","pagination"])}const Ot=Z(Rt,[["render",It],["__scopeId","data-v-f011ee3f"]]),Lt=V({components:{Popover:vt,BellOutlined:ft,Tabs:q,TabPane:q.TabPane,Badge:yt,NoticeList:Ot,DetailModal:wt,DynamicNotice:St,SysMessageModal:jt},setup(){const{prefixCls:t}=X("header-notify"),o=mt(),n=bt(),d=_t(),g=pt({path:"",formData:{}}),[v,y]=H(),i=R(Nt),e=S(()=>{let a=0;for(let u=0;u<i.value.length;u++)a+=i.value[u].count;return a}),[r,{openModal:m}]=H();function B(){for(let a=0;a<i.value.length;a++)i.value[a].count=0;m(!0,{})}const _=R(!1);dt(()=>{Y()});function M(a){return $(j({},a),{title:a.titile,description:a.msgAbstract,datetime:a.sendTime})}function s(){return N(this,null,function*(){try{let{anntMsgList:a,sysMsgList:u,anntMsgTotal:w,sysMsgTotal:D}=yield Pt({pageSize:5});i.value[0].list=a.map(M),i.value[1].list=u.map(M),i.value[0].count=w,i.value[1].count=D}catch(a){}})}s();function U(a){var u;try{K(a.id),s()}catch(w){}a.openType==="component"?(g.path=a.openPage,g.formData={id:a.busId},(u=o.refs.dynamicNoticeRef)==null||u.detail(a.openPage)):y.openModal(!0,{record:a,isUpdate:!0}),_.value=!1}function Y(){var z;let a=Ct(),u=kt(a),w=T(n.getUserInfo).id+"_"+u,D=((z=d.domainUrl)==null?void 0:z.replace("https://","wss://").replace("http://","ws://"))+"/websocket/"+w;Tt(D),Bt(Q)}function Q(a){(a.cmd==="topic"||a.cmd==="user")&&setTimeout(()=>{s()},1e3)}function x(){_.value=!1,Dt({},s)}function tt(a){return N(this,null,function*(){try{yield K(a),yield s()}catch(u){}})}return{prefixCls:t,listData:i,count:e,clickBadge:B,registerMessageModal:r,reloadCount:tt,onNoticeClick:U,onEmptyNotify:x,numberStyle:{},popoverVisible:_,registerDetail:v,dynamicNoticeProps:g}}});function Ft(t,o,n,d,g,v){const y=l("BellOutlined"),i=l("Badge"),e=l("DynamicNotice"),r=l("DetailModal"),m=l("sys-message-modal");return p(),b("div",{class:E(t.prefixCls)},[c(i,{count:t.count,overflowCount:9,offset:[-4,10],numberStyle:t.numberStyle,onClick:t.clickBadge},{default:f(()=>[c(y)]),_:1},8,["count","numberStyle","onClick"]),c(e,ut({ref:"dynamicNoticeRef"},t.dynamicNoticeProps),null,16),c(r,{onRegister:t.registerDetail},null,8,["onRegister"]),c(m,{onRegister:t.registerMessageModal,onRefresh:t.reloadCount},null,8,["onRegister","onRefresh"])],2)}const ue=Z(Lt,[["render",Ft]]);export{ue as default};
