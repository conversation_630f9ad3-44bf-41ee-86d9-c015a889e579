import{d as R,e as l,ag as t,aB as E,ar as S,aD as s,k as n,at as a,G as r,au as p}from"./vue-vendor-dy9k-Yad.js";import{A as _}from"./antd-vue-vendor-me9YkNVC.js";import P from"./CurrentPermissionMode-DzGbOpfp.js";import{X as y,ah as C,bE as i,a as T}from"./index-CCWaWN5g.js";import{P as k}from"./index-CtJ0w2CP.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const $=R({components:{Alert:_,CurrentPermissionMode:P,PageWrapper:k},setup(){const{changeRole:o}=y(),e=C();return{userStore:e,RoleEnum:i,isSuper:l(()=>e.getRoleList.includes(i.SUPER)),isTest:l(()=>e.getRoleList.includes(i.TEST)),changeRole:o}}}),b={class:"mt-4"};function v(o,e,B,A,U,L){const m=t("CurrentPermissionMode"),d=t("Alert"),u=t("a-button"),c=t("a-button-group"),f=t("PageWrapper");return S(),E(f,{title:"前端权限示例",contentBackground:"",contentClass:"p-4",content:"由于刷新的时候会请求用户信息接口，会根据接口重置角色信息，所以刷新后界面会恢复原样，如果不需要，可以注释 src/layout/default/index内的获取用户信息接口"},{default:s(()=>[n(m),a("p",null,[e[2]||(e[2]=r(" 当前角色: ")),a("a",null,p(o.userStore.getRoleList),1)]),n(d,{class:"mt-4",type:"info",message:"点击后请查看左侧菜单变化","show-icon":""}),a("div",b,[e[3]||(e[3]=r(" 权限切换(请先切换权限模式为前端角色权限模式): ")),n(c,null,{default:s(()=>[n(u,{onClick:e[0]||(e[0]=g=>o.changeRole(o.RoleEnum.SUPER)),type:o.isSuper?"primary":"default"},{default:s(()=>[r(p(o.RoleEnum.SUPER),1)]),_:1},8,["type"]),n(u,{onClick:e[1]||(e[1]=g=>o.changeRole(o.RoleEnum.TEST)),type:o.isTest?"primary":"default"},{default:s(()=>[r(p(o.RoleEnum.TEST),1)]),_:1},8,["type"])]),_:1})])]),_:1})}const q=T($,[["render",v],["__scopeId","data-v-a8c1cfff"]]);export{q as default};
