import{k as l,G as p,ap as Me,f as C,n as ke,l as g}from"./vue-vendor-dy9k-Yad.js";import{a8 as Se,B as S,c9 as xe,R as E,j as De}from"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import{a as Le}from"./index-JbqXEynz.js";import{useListPage as $e}from"./useListPage-Soxgnx9a.js";import{l as Ae,c as Ee,e as Re,g as Be,f as Ie,a as Pe,b as Oe,d as Ye}from"./CgformModal-c4a4e0c2-DLUwczVd.js";import{u as je,R as Ge,ad as u}from"./index-CCWaWN5g.js";import{h as Je}from"./cgformState-d9f8ec42-C8rx7JjX.js";var y=(o,i,t)=>new Promise((h,f)=>{var T=c=>{try{a(t.next(c))}catch(r){f(r)}},M=c=>{try{a(t.throw(c))}catch(r){f(r)}},a=c=>c.done?h(c.value):Promise.resolve(c.value).then(T,M);a((t=t.apply(o,i)).next())}),B=(o=>(o[o.normal=0]="normal",o[o.copy=1]="copy",o))(B||{});const{createConfirm:Ne}=je();function R(o,i){const{destroy:t}=Ne({title:"请确认删除表单",content:()=>l("p",null,[l("br",null,null),l("span",{style:"cursor: text;"},[l("a",{style:"pointer-events: none;font-weight: bold; "},[p("仅移除：")])]),l("span",null,[p("只删除表单配置，数据库表保留！")]),l("br",null,null),l("span",{style:"color: #ee0000;font-weight: bold; "},[p("删除：")]),l("span",null,[p("同时删除数据库表，不可恢复！")]),l("br",null,null),l("br",null,null),l("span",null,[p("此操作很敏感，请谨慎操作..")])]),iconType:"warning",closable:!0,maskClosable:!0,footer:()=>l("div",{style:"text-align: right;"},[l(Se,{align:"center"},{default:()=>[l(S,{onClick:()=>t()},{default:()=>[p("取消")]}),l(S,{type:"primary",onClick:h(i)},{default:()=>[p("仅移除")]}),l(S,{type:"primary",danger:!0,onClick:h(o)},{default:()=>[p("删除")]})]})])});function h(f){return()=>y(this,null,function*(){yield f(),t()})}}function Qe(o){const i=o.pageType===B.normal,t=Me(),h=Je(),f=C(),T=$e({designScope:o.designScope,tableProps:{api:Ae,columns:o.columns,formConfig:{schemas:o.formSchemas},beforeFetch:e=>{let n=i?0:1,s=i?void 0:t.currentRoute.value.params.code;return Ge(e.tableType_MultiString)&&(e.tableType_MultiString=e.tableType_MultiString.join(",")),Object.assign(e,{copyType:n,physicId:s})}}}),{tableContext:M,createMessage:a,createConfirm:c}=T,[,{reload:r,setLoading:v},{selectedRowKeys:x,selectedRows:d}]=M,[I,D]=u(),[P,O]=u(),[Y,j]=u(),[G,J]=u(),[N,U]=u(),[_,q]=u(),[F,V]=u(),[K,z]=u(),[H,Q]=Le(),[W,X]=u();function Z(){D.openModal(!0,{isUpdate:!1})}function ee(){j.openModal(!0)}function ne(){r()}let m=null;function te(e){m=e,D.openModal(!0,{isUpdate:!0,record:e})}function le(){m!=null&&m.id&&(h.addChangedTable(m.id),m=null),r()}function ae(e){return y(this,null,function*(){yield Ee(e),r()})}function oe(e){return y(this,null,function*(){yield Re(e),r()})}function ie(e){return R(()=>ae(e.id),()=>oe(e.id))}function re(){let e=x.value;if(e.length<=0){a.warning("请先选择一条记录！");return}R(()=>L(Be,e,!0),()=>L(Ie,e,!0))}function L(e,n,s=!1){return y(this,null,function*(){try{v(!0);const k=yield e(n);return r(),s&&(x.value=[]),k}finally{v(!1)}return Promise.reject()})}function se(){w(([e])=>U.openModal(!0,{row:e}))}function ce(){w(([e])=>q.openModal(!0,{row:e}))}function ue(){w(([e])=>V.openModal(!0,{row:e}))}function de(){w(([e])=>z.openModal(!0,{row:e}))}function pe(){O.openModal(!0,{})}function w(e,n=1,s=1){d.value.length<n?a.warning(`请先至少选中 ${n} 条记录`):d.value.length>s?a.warning(`最多只能选中 ${n} 条记录`):e(d.value)}function fe(){if(d.value.length===0)a.warning("请先选中一条记录");else if(d.value.length>1)a.warning("代码生成只能选中一条记录");else{let e=d.value[0];e?e.isDbSynch!="Y"?a.warning("请先同步数据库！"):e.tableType==3?a.warning("请选中该表对应的主表"):J.openModal(!0,{code:e.id}):a.warning("请选中当前页的数据！")}}function he(e){if(e.isTree=="Y")t.push({path:"/online/cgformTreeList/"+e.id});else switch(e.themeTemplate){case"erp":t.push({path:"/online/cgformErpList/"+e.id});break;case"tab":t.push({path:"/online/cgformTabList/"+e.id});break;case"innerTable":t.push({path:"/online/cgformInnerTableList/"+e.id});break;default:t.push({path:"/online/cgformList/"+e.id});break}}function ge(e){const n=C("normal"),s=C(!1),k=c({iconType:"info",title:"同步数据库",content:()=>g("div",{style:"margin: 20px 0;"},g(xe,{value:n.value,disabled:s.value,"onUpdate:value":b=>n.value=b},()=>[g(E,{value:"normal"},()=>"普通同步（保留表数据）"),g(E,{value:"force"},()=>"强制同步（删除表，重新生成）")])),maskClosable:!0,okText:"开始同步",onOk(){return y(this,null,function*(){s.value=!0,k.update({maskClosable:!1,keyboard:!1,okText:"同步中…",okButtonProps:{loading:s.value},cancelButtonProps:{disabled:s.value}});try{yield Pe(e.id,n.value)}catch(b){}finally{yield r(),ke(()=>{d.value.length&&d.value.forEach(b=>{var $;const A=(($=f.value.getDataSource())!=null?$:[]).find(Ce=>Ce.id===b.id);A&&Object.assign(b,A)})})}})}})}const[me,be]=u();function ye(e){let n;e.themeTemplate==="erp"?n=`/online/cgformErpList/${e.id}`:e.themeTemplate==="innerTable"?n=`/online/cgformInnerTableList/${e.id}`:e.themeTemplate==="tab"?n=`/online/cgformTabList/${e.id}`:e.isTree=="Y"?n=`/online/cgformTreeList/${e.id}`:n=`/online/cgformList/${e.id}`,be.openModal(!0,{title:`菜单链接【${e.tableTxt}】`,content:n,copyText:n,copyTitle:`${e.tableTxt}`,record:e})}function Te(e){const n=C(e.tableName+"_copy");c({title:"复制表",content:()=>g("div",{style:"margin: 20px 0;"},["请输入新表名：",g(De,{value:n.value,"onUpdate:value":s=>n.value=s})]),iconType:"info",closable:!0,okText:"复制",onOk(){n.value?n.value===e.tableName?a.warning("新表名和旧表名不能一致"):Oe(e.id,n.value).then(r):a.warning("请输入新表名")}})}function ve(e){return[{label:"编辑",onClick:()=>te(e)}]}function we(e){return[{label:"同步数据库",onClick:()=>ge(e),ifShow:()=>i&&e.isDbSynch!="Y"},{label:"功能测试",class:["low-app-hide"],onClick:()=>he(e),ifShow:()=>i?e.isDbSynch=="Y"&&e.tableType!==3:!0},{label:"配置地址",class:["low-app-hide"],onClick:()=>ye(e),ifShow:()=>i?e.isDbSynch=="Y"&&e.tableType!==3:!0},{label:"权限控制",onClick:()=>Q.openDrawer(!0,{cgformId:e.id,tableType:e.tableType})},{label:"角色授权",onClick:()=>X.openModal(!0,{cgformId:e.id})},{label:"视图管理",class:["low-app-hide"],onClick:()=>t.push(`/online/copyform/${e.id}`),ifShow:()=>i&&e.hascopy==1},{label:"生成视图",class:["low-app-hide"],popConfirm:{title:"确定生成视图吗？",placement:"left",confirm:()=>{v(!0),Ye(e.id).then(()=>{a.success("已成功生成视图")}).finally(()=>{v(!1),r()})}},ifShow:()=>i},{label:"复制表",onClick:()=>Te(e),ifShow:()=>i},{label:"删除",onClick:()=>ie(e),ifShow:()=>i}]}return{router:t,pageContext:T,onAdd:Z,onAiCreateTable:ee,onSuccess:le,onDeleteBatch:re,onImportDbTable:pe,onGenerateCode:fe,onShowCustomButton:se,onShowEnhanceJs:ce,onShowEnhanceSql:ue,onShowEnhanceJava:de,onCreateAiTable:ne,getTableAction:ve,getDropDownAction:we,registerCustomButtonModal:N,registerEnhanceJsModal:_,registerEnhanceSqlModal:F,registerEnhanceJavaModal:K,registerAuthManagerDrawer:H,registerAuthSetterModal:W,registerCgformModal:I,registerDbToOnlineModal:P,registerCodeGeneratorModal:G,registerAiToOnlineModal:Y,registerAddressModal:me,tableRef:f}}export{B as $,Qe as e};
