var Z=Object.defineProperty,oo=Object.defineProperties;var eo=Object.getOwnPropertyDescriptors;var I=Object.getOwnPropertySymbols;var to=Object.prototype.hasOwnProperty,ro=Object.prototype.propertyIsEnumerable;var U=(n,e,t)=>e in n?Z(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,P=(n,e)=>{for(var t in e||(e={}))to.call(e,t)&&U(n,t,e[t]);if(I)for(var t of I(e))ro.call(e,t)&&U(n,t,e[t]);return n},S=(n,e)=>oo(n,eo(e));var R=(n,e,t)=>new Promise((w,c)=>{var f=a=>{try{s(t.next(a))}catch(u){c(u)}},b=a=>{try{s(t.throw(a))}catch(u){c(u)}},s=a=>a.done?w(a.value):Promise.resolve(a.value).then(f,b);s((t=t.apply(n,e)).next())});import{d as T,f as no,ag as p,v as io,aq as lo,ar as _,k as r,aD as m,u as i,q as F,aB as h,ah as mo,G as d,F as ao}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{a as g}from"./index-JbqXEynz.js";import"./index-Diw57m_E.js";import po from"./RoleDrawer-BvP-LLM1.js";import so from"./RoleDesc-CjhDo5X3.js";import uo from"./RolePermissionDrawer-Dsd2_k1g.js";import co from"./RoleIndexModal-CYwM4Bnx.js";import fo from"./RoleUserTable-C2HEMOJR.js";import{s as _o,c as go}from"./role.data-BepCB_2a.js";import{b as wo,g as bo,a as Co,p as Do,d as Ro}from"./role.api-BvRyEQIC.js";import{useListPage as ho}from"./useListPage-Soxgnx9a.js";import{ad as ko}from"./index-CCWaWN5g.js";import{Q as xo}from"./componentMap-Bkie1n3v.js";import yo from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-Dce_QJ6p.js";import"./RoleDataRuleDrawer-C7vwg56A.js";import"./UserDrawer-xnPiWN1B.js";import"./user.data-CLRTqTDz.js";import"./user.api-mLAlJze4.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";import"./useAdaptiveWidth-SDQVNQ1K.js";import"./UseSelectModal-B88seyfc.js";import"./injectionKey-DPVn4AgL.js";const vo=T({name:"system-role"}),Oe=T(S(P({},vo),{setup(n){const e=no(!0),[t,{openDrawer:w}]=g(),[c,{openDrawer:f}]=g(),[b,{openModal:s}]=ko(),[a,{openDrawer:u}]=g(),[E,{openDrawer:A}]=g(),{prefixCls:Io,tableContext:B,onImportXls:M,onExportXls:N}=ho({designScope:"role-template",tableProps:{title:"系统角色列表",api:Do,columns:go,bordered:!1,showTableSetting:!1,maxHeight:478,formConfig:{labelWidth:65,rowProps:{gutter:24},schemas:_o},actionColumn:{width:120},defSort:{column:"id",order:"desc"}},exportConfig:{name:"角色列表",url:Co},importConfig:{url:bo}}),[V,{reload:C},{rowSelection:Uo,selectedRowKeys:k}]=B;function $(){e.value=!0,f(!0,{isUpdate:!1})}function j(o){e.value=!0,f(!0,{record:o,isUpdate:!0})}function q(o){e.value=!1,A(!0,{record:o,isUpdate:!0})}function H(o){return R(this,null,function*(){yield Ro({id:o.id},C)})}function K(){return R(this,null,function*(){yield wo({ids:k.value},C)})}function X(o){u(!0,{roleId:o.id})}function G(o){s(!0,{roleCode:o})}function L(o){w(!0,o)}function Q(o){return[{label:"用户",onClick:L.bind(null,o)},{label:"授权",onClick:X.bind(null,o)}]}function W(o){return[{label:"编辑",onClick:j.bind(null,o)},{label:"详情",onClick:q.bind(null,o)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:H.bind(null,o)}},{label:"首页配置",onClick:G.bind(null,o.roleCode)}]}return(o,l)=>{const D=p("a-button"),z=p("j-upload-button"),x=p("Icon"),J=p("a-menu-item"),O=p("a-menu"),Y=p("a-dropdown"),y=io("auth");return _(),lo(ao,null,[r(i(yo),{onRegister:i(V)},{tableTitle:m(()=>[F((_(),h(D,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:$},{default:m(()=>l[0]||(l[0]=[d(" 新增")])),_:1,__:[0]})),[[y,"system:role:add"]]),r(D,{type:"primary",preIcon:"ant-design:export-outlined",onClick:i(N)},{default:m(()=>l[1]||(l[1]=[d(" 导出")])),_:1,__:[1]},8,["onClick"]),F((_(),h(z,{type:"primary",preIcon:"ant-design:import-outlined",onClick:i(M)},{default:m(()=>l[2]||(l[2]=[d("导入")])),_:1,__:[2]},8,["onClick"])),[[y,"system:role:importExcel"]]),i(k).length>0?(_(),h(Y,{key:0},{overlay:m(()=>[r(O,null,{default:m(()=>[r(J,{key:"1",onClick:K},{default:m(()=>[r(x,{icon:"ant-design:delete-outlined"}),l[3]||(l[3]=d(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:m(()=>[r(D,null,{default:m(()=>[l[4]||(l[4]=d("批量操作 ")),r(x,{icon:"mdi:chevron-down"})]),_:1,__:[4]})]),_:1})):mo("",!0)]),action:m(({record:v})=>[r(i(xo),{actions:Q(v),dropDownActions:W(v)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister"]),r(fo,{onRegister:i(t)},null,8,["onRegister"]),r(po,{onRegister:i(c),onSuccess:i(C),showFooter:e.value},null,8,["onRegister","onSuccess","showFooter"]),r(so,{onRegister:i(E)},null,8,["onRegister"]),r(uo,{onRegister:i(a)},null,8,["onRegister"]),r(co,{onRegister:i(b)},null,8,["onRegister"])],64)}}}));export{Oe as default};
