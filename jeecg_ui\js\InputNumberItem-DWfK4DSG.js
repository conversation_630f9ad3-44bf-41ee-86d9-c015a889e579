import{d as s,ag as o,aq as p,ar as m,as as i,at as u,k as l,au as c,aE as f}from"./vue-vendor-dy9k-Yad.js";import{ac as d}from"./antd-vue-vendor-me9YkNVC.js";import{F as b,a as g}from"./index-CCWaWN5g.js";import{b as C}from"./index-CI-8_pdX.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-JbqXEynz.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useHeaderSetting-C-h5S52e.js";import"./useMultipleTabSetting-QBbnIi9J.js";const I=s({name:"InputNumberItem",components:{InputNumber:d},props:{event:{type:Number},title:{type:String}},setup(e){const{prefixCls:t}=b("setting-input-number-item");function n(a){e.event&&C(e.event,a)}return{prefixCls:t,handleChange:n}}});function N(e,t,n,a,h,_){const r=o("InputNumber");return m(),p("div",{class:i(e.prefixCls)},[u("span",null,c(e.title),1),l(r,f({max:7200},e.$attrs,{size:"small",class:`${e.prefixCls}-input-number`,onChange:e.handleChange}),null,16,["class","onChange"])],2)}const P=g(I,[["render",N],["__scopeId","data-v-e024f411"]]);export{P as default};
