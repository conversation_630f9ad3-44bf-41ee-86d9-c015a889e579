import{d as C,l as g,ag as e,aB as h,ar as b,aD as p,k as i}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{C as v}from"./index-LCGLvkB3.js";import{u as P,a as B}from"./index-CCWaWN5g.js";import{j as m}from"./antd-vue-vendor-me9YkNVC.js";import{P as S}from"./index-CtJ0w2CP.js";import{B as F}from"./BasicForm-DBcXiHk0.js";import{u as k}from"./useForm-CgkFTrrO.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const l=[{field:"field1",component:"Input",label:"render方式",colProps:{span:8},rules:[{required:!0}],render:({model:o,field:t})=>g(m,{placeholder:"请输入",value:o[t],onChange:r=>{o[t]=r.target.value}})},{field:"field2",component:"Input",label:"render组件slot",colProps:{span:8},rules:[{required:!0}],renderComponentContent:()=>({suffix:()=>"suffix"})},{field:"field3",component:"Input",label:"自定义Slot",slot:"f3",colProps:{span:8},rules:[{required:!0}]}],x=C({components:{BasicForm:F,CollapseContainer:v,PageWrapper:S,[m.name]:m},setup(){const{createMessage:o}=P(),[t,{setProps:r}]=k({labelWidth:120,schemas:l,actionColOptions:{span:24}});return{register:t,schemas:l,handleSubmit:n=>{o.success("click search,values:"+JSON.stringify(n))},setProps:r}}});function I(o,t,r,n,W,$){const u=e("a-input"),c=e("BasicForm"),f=e("CollapseContainer"),d=e("PageWrapper");return b(),h(d,{title:"自定义组件示例"},{default:p(()=>[i(f,{title:"自定义表单"},{default:p(()=>[i(c,{onRegister:o.register,onSubmit:o.handleSubmit},{f3:p(({model:a,field:s})=>[i(u,{value:a[s],"onUpdate:value":_=>a[s]=_,placeholder:"自定义slot"},null,8,["value","onUpdate:value"])]),_:1},8,["onRegister","onSubmit"])]),_:1})]),_:1})}const jo=B(x,[["render",I]]);export{jo as default};
