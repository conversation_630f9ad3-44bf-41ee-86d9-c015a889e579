var X=Object.defineProperty,J=Object.defineProperties;var Z=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var ee=Object.prototype.hasOwnProperty,te=Object.prototype.propertyIsEnumerable;var B=(r,a,n)=>a in r?X(r,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[a]=n,h=(r,a)=>{for(var n in a||(a={}))ee.call(a,n)&&B(r,n,a[n]);if(U)for(var n of U(a))te.call(a,n)&&B(r,n,a[n]);return r},M=(r,a)=>J(r,Z(a));var S=(r,a,n)=>new Promise((I,_)=>{var y=o=>{try{c(n.next(o))}catch(s){_(s)}},b=o=>{try{c(n.throw(o))}catch(s){_(s)}},c=o=>o.done?I(o.value):Promise.resolve(o.value).then(y,b);c((n=n.apply(r,a)).next())});import{d as j,f as m,e as ae,ag as P,aq as g,ar as f,k as p,aD as d,u as C,G as R,au as T,at as u,ah as D}from"./vue-vendor-dy9k-Yad.js";import{u as ie}from"./index-BkGZ5fiW.js";import{u as ne,aX as oe,a as se}from"./index-CCWaWN5g.js";import{a as re,d as le}from"./SupplyDemand-DK00S9Ao.js";import{D as ue}from"./index-CyU3vcHV.js";import{Q as ce}from"./componentMap-Bkie1n3v.js";import me from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFilePreview-CazplhRu.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const pe={class:"p-4"},de={class:"image-container"},ge={key:0,class:"no-image"},fe={key:1,class:"image-list"},ve={class:"image-item"},he=["src","onClick"],_e={key:0,class:"image-item second-image"},ye=["src","onClick"],be=["onClick"],ke={class:"preview-container"},we=["src"],Ce={key:0,class:"preview-controls"},Te={class:"image-counter"},Ie=j({name:"SupplyList"}),xe=j(M(h({},Ie),{setup(r){const{createMessage:a,createConfirm:n}=ne(),I=[{title:"序号",dataIndex:"index",width:60,customRender:({index:e})=>e+1},{title:"图片",dataIndex:"images",width:120,slots:{customRender:"images"}},{title:"求购标题",dataIndex:"infoTitle",width:200,ellipsis:!0},{title:"物资类型",dataIndex:"materialTypeName",width:120,ellipsis:!0},{title:"所在地区",dataIndex:"address",width:180,ellipsis:!0,customRender:({record:e})=>{const t=e.provinceName||"",v=e.cityName||"",w=e.districtName||"";return[t,v,w].filter(Boolean).join(" ")}},{title:"审核状态",dataIndex:"status",width:100,slots:{customRender:"status"}},{title:"发布时间",dataIndex:"createTime",width:150,customRender:({text:e})=>e?oe(e):"-"}],_=[{key:"all",label:"全部求购",icon:""},{key:"draft",label:"草稿",icon:""},{key:"pending",label:"待审核",icon:""},{key:"approved",label:"已通过",icon:""},{key:"rejected",label:"未通过",icon:""},{key:"published",label:"已发布",icon:""}],y=m("all"),b=m({}),c=m(!1),o=m([]),s=m(0),Y=ae(()=>o.value[s.value]||""),k=m(!1),x=m(null);function A(e){y.value=e;let t={};switch(e){case"draft":t={status:1};break;case"pending":t={status:2};break;case"approved":t={status:3};break;case"rejected":t={status:4};break;case"published":t={status:5};break;default:t={}}b.value=t,V()}function K(e){return S(this,null,function*(){const t={type:"5"},v=h(h(h({},e),t),b.value);return re(v)})}function l(e){try{return e.attachmentList&&Array.isArray(e.attachmentList)?e.attachmentList.filter(t=>t.fileType==="image").map(t=>t.filePath).filter(Boolean):[]}catch(t){return[]}}function N(e,t){o.value=e,s.value=t,c.value=!0}function $(){s.value>0&&s.value--}function q(){s.value<o.value.length-1&&s.value++}function z(e){switch(e){case 1:return"default";case 2:return"processing";case 3:return"success";case 4:return"error";case 5:return"success";case 6:return"warning";case 7:return"default";case 8:return"default";default:return"default"}}function Q(e){switch(e){case 1:return"草稿";case 2:return"待审核";case 3:return"审核通过";case 4:return"审核拒绝";case 5:return"已发布";case 6:return"已成交";case 7:return"已撤拍";case 8:return"已过期";default:return"未知"}}function E(e){if(!e||!e.id){a.error("数据异常，无法查看详情");return}try{const t={id:String(e.id),entrustType:3,serviceType:4,status:Number(e.status)||2,projectName:String(e.infoTitle||"求购需求"),relationUser:String(e.relationUser||"-"),relationPhone:String(e.relationPhone||"-"),applicantUser:String(e.createBy||"-"),auditUser:String(e.auditUser||"-"),submitTime:String(e.createTime||""),auditTime:String(e.auditTime||"")};x.value=t,k.value=!0}catch(t){a.error("数据处理异常，无法查看详情")}}function F(){k.value=!1,x.value=null}function G(e){n({iconType:"warning",title:"确认删除",content:"确定要删除这条求购需求吗？删除后不可恢复。",onOk:()=>S(null,null,function*(){try{yield le({id:e.id}),a.success("删除成功"),V()}catch(t){a.error("删除失败")}})})}function H(e){return[{label:"浏览",onClick:()=>E(e)},{label:"删除",color:"error",onClick:()=>G(e)}]}const[O,{reload:V}]=ie({api:K,columns:I,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,showNavigation:!0,navigationItems:_,activeNavigationKey:y.value,inset:!0,maxHeight:478,actionColumn:{width:150,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"},formConfig:{labelWidth:64,size:"large",schemas:[{field:"infoTitle",label:"求购标题",component:"Input",componentProps:{placeholder:"请输入求购标题"},colProps:{span:6}},{field:"materialTypeName",label:"物资类型",component:"Input",componentProps:{placeholder:"请输入物资类型"},colProps:{span:6}},{field:"createTimeRange",label:"发布时间",component:"RangePicker",componentProps:{format:"YYYY-MM-DD",placeholder:["开始时间","结束时间"]},colProps:{span:6}}]}});return(e,t)=>{const v=P("a-tag"),w=P("a-button"),W=P("a-modal");return f(),g("div",pe,[p(C(me),{onRegister:C(O),onNavigationChange:A},{images:d(({record:i})=>[u("div",de,[l(i).length===0?(f(),g("div",ge,t[2]||(t[2]=[u("span",null,"暂无图片",-1)]))):(f(),g("div",fe,[u("div",ve,[u("img",{src:l(i)[0],alt:"求购图片",onClick:L=>N(l(i),0)},null,8,he)]),l(i).length>1?(f(),g("div",_e,[u("img",{src:l(i)[1],alt:"求购图片",onClick:L=>N(l(i),1)},null,8,ye),l(i).length>2?(f(),g("div",{key:0,class:"image-overlay",onClick:L=>N(l(i),1)},[u("span",null,T(l(i).length)+"张",1)],8,be)):D("",!0)])):D("",!0)]))])]),status:d(({record:i})=>[p(v,{color:z(i.status)},{default:d(()=>[R(T(Q(i.status)),1)]),_:2},1032,["color"])]),action:d(({record:i})=>[p(C(ce),{actions:H(i)},null,8,["actions"])]),_:1},8,["onRegister"]),p(W,{open:c.value,"onUpdate:open":t[0]||(t[0]=i=>c.value=i),footer:null,width:800,title:"图片预览"},{default:d(()=>[u("div",ke,[u("img",{src:Y.value,alt:"预览图片",class:"preview-image"},null,8,we),o.value.length>1?(f(),g("div",Ce,[p(w,{onClick:$,disabled:s.value===0},{default:d(()=>t[3]||(t[3]=[R("上一张")])),_:1,__:[3]},8,["disabled"]),u("span",Te,T(s.value+1)+" / "+T(o.value.length),1),p(w,{onClick:q,disabled:s.value===o.value.length-1},{default:d(()=>t[4]||(t[4]=[R("下一张")])),_:1,__:[4]},8,["disabled"])])):D("",!0)])]),_:1},8,["open"]),p(C(ue),{open:k.value,"onUpdate:open":t[1]||(t[1]=i=>k.value=i),record:x.value,"entrust-type":3,"service-type":4,onClose:F},null,8,["open","record"])])}}})),Ut=se(xe,[["__scopeId","data-v-67a7f934"]]);export{Ut as default};
