var _=(b,u,s)=>new Promise((d,t)=>{var f=n=>{try{m(s.next(n))}catch(c){t(c)}},r=n=>{try{m(s.throw(n))}catch(c){t(c)}},m=n=>n.done?d(n.value):Promise.resolve(n.value).then(f,r);m((s=s.apply(b,u)).next())});import{d as j,f as B,w as E,ag as p,aB as y,ar as h,aD as i,k as a,u as l,ah as S,G as w,aE as q,J as T}from"./vue-vendor-dy9k-Yad.js";import{B as z}from"./index-Diw57m_E.js";import{u as A}from"./index-BkGZ5fiW.js";import{r as G}from"./user.data-CLRTqTDz.js";import{n as J,o as L,q as P}from"./user.api-mLAlJze4.js";import{ac as Q,u as U}from"./index-CCWaWN5g.js";import W from"./BasicTable-xCEZpGLb.js";import{Q as $}from"./componentMap-Bkie1n3v.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";import"./index-QxsVJqiT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const rt=j({__name:"UserRecycleBinModal",emits:["success","register"],setup(b,{emit:u}){const{createConfirm:s}=U(),d=u,t=B([]),[f]=Q(()=>{t.value=[]}),r=B({y:0}),[m,{reload:n}]=A({api:J,columns:G,rowKey:"id",striped:!0,useSearchForm:!1,showTableSetting:!1,clickToRowSelect:!1,bordered:!0,showIndexColumn:!1,pagination:!0,tableSetting:{fullScreen:!0},canResize:!1,actionColumn:{width:150,title:"操作",dataIndex:"action",fixed:void 0}}),c=e=>{C(e)},C=e=>{const o=document.documentElement.clientHeight;r.value={y:o-(e?300:500)}};C(!1),E(t,(e,o)=>{t.value.length&&o.length==0?r.value={y:r.value.y-50}:t.value.length==0&&o.length&&(r.value={y:r.value.y+50})},{deep:!0});const x={type:"checkbox",columnWidth:50,selectedRowKeys:t,onChange:I};function I(e){t.value=e}function v(e){return _(this,null,function*(){yield L({userIds:e.id},n),d("success")})}function H(){v({id:T(l(t)).join(",")})}function k(e){return _(this,null,function*(){yield P({userIds:e.id},n)})}function M(){s({iconType:"warning",title:"删除",content:"确定要永久删除吗？删除后将不可恢复！",onOk:()=>k({id:T(l(t)).join(",")}),onCancel(){}})}function K(e){return[{label:"取回",icon:"ant-design:redo-outlined",popConfirm:{title:"是否确认还原",confirm:v.bind(null,e)}},{label:"彻底删除",icon:"ant-design:scissor-outlined",popConfirm:{title:"是否确认删除",confirm:k.bind(null,e)}}]}return(e,o)=>{const g=p("Icon"),R=p("a-menu-item"),D=p("a-menu"),F=p("a-button"),N=p("a-dropdown");return h(),y(l(z),q(e.$attrs,{onRegister:l(f),title:"用户回收站",showOkBtn:!1,width:"1000px",destroyOnClose:"",onFullScreen:c}),{default:i(()=>[a(l(W),{onRegister:l(m),rowSelection:x,scroll:r.value},{tableTitle:i(()=>[t.value.length>0?(h(),y(N,{key:0},{overlay:i(()=>[a(D,null,{default:i(()=>[a(R,{key:"1",onClick:M},{default:i(()=>[a(g,{icon:"ant-design:delete-outlined"}),o[0]||(o[0]=w(" 批量删除 "))]),_:1,__:[0]}),a(R,{key:"1",onClick:H},{default:i(()=>[a(g,{icon:"ant-design:redo-outlined"}),o[1]||(o[1]=w(" 批量还原 "))]),_:1,__:[1]})]),_:1})]),default:i(()=>[a(F,null,{default:i(()=>[o[2]||(o[2]=w("批量操作 ")),a(g,{icon:"ant-design:down-outlined"})]),_:1,__:[2]})]),_:1})):S("",!0)]),bodyCell:i(({column:O,record:V})=>[O.key==="action"?(h(),y(l($),{key:0,actions:K(V)},null,8,["actions"])):S("",!0)]),_:1},8,["onRegister","scroll"])]),_:1},16,["onRegister"])}}});export{rt as default};
