import{j as s}from"./index-CCWaWN5g.js";const c=t=>s.get({url:"/mock/system/getAccountList",params:t}),m=t=>s.get({url:"/mock/system/getDeptList",params:t}),g=t=>s.get({url:"/mock/system/getMenuList",params:t}),a=t=>s.get({url:"/mock/system/getRoleListByPage",params:t}),l=t=>s.get({url:"/mock/system/getAllRoleList",params:t}),L=(t,e)=>s.post({url:"/mock/system/setRoleStatus",params:{id:t,status:e}}),i=t=>s.get({url:"/mock/system/getTestListByPage",params:t});const y=t=>s.post({url:"/mock/system/accountExist",params:{account:t}},{errorMessageMode:"none"});export{m as a,c as b,g as c,a as d,i as e,l as g,y as i,L as s};
