import{d as b,ag as f,aq as u,ar as c,at as o,ah as g,k as i,u as t,H as n}from"./vue-vendor-dy9k-Yad.js";import{b as V}from"./useSettings-4a774f12-DjycrGR6.js";import q from"./VarListPicker-4ef8c64a-BYzXoS8G.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./index-CCWaWN5g.js";import"./VarListEditor.vue_vue_type_style_index_0_scoped_407b7ab3_lang-4ed993c7-l0sNRNKZ.js";import{r as y}from"./VarTextarea.vue_vue_type_script_setup_true_lang-8c82777b-BlFMq0Y1.js";import"./VarTextarea.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import"./VarEditable.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const O={class:"end-setting"},C={class:"setting-item"},x={class:"setting-item"},B={key:0,class:"setting-item"},T=b({__name:"EndSetting",props:{type:{type:String,required:!0},node:{type:Object,required:!0},properties:{type:Object,required:!0},setProperties:{type:Function,required:!0}},setup(d){const v=d,{prevVariables:m,outputParams:l,outputVarsOptions:h,createOptionRef:p}=V(v),a=p("outputText");a.value==null&&(a.value=!1);const r=p("outputContent");return r.value==null&&(r.value=""),(E,e)=>{const k=f("a-switch");return c(),u("div",O,[o("div",C,[e[3]||(e[3]=o("div",{class:"label"},"输出变量",-1)),i(t(q),{vars:t(l),"onUpdate:vars":e[0]||(e[0]=s=>n(l)?l.value=s:null),prevVariables:t(m)},null,8,["vars","prevVariables"])]),o("div",x,[e[4]||(e[4]=o("div",{class:"label"},"返回文本",-1)),i(k,{checked:t(a),"onUpdate:checked":e[1]||(e[1]=s=>n(a)?a.value=s:null),"checked-children":"是","un-checked-children":"否"},null,8,["checked"])]),t(a)?(c(),u("div",B,[e[5]||(e[5]=o("div",{class:"label"},"文本内容",-1)),i(t(y),{value:t(r),"onUpdate:value":e[2]||(e[2]=s=>n(r)?r.value=s:null),varsOptions:t(h),placeholder:"请输入返回的文本内容。按下 “/” 可以选择变量"},null,8,["value","varsOptions"])])):g("",!0)])}}});export{T as A};
