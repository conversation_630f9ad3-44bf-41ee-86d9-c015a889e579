import{d as x,f as n,ag as r,aB as S,ar as R,aD as C,at as y,k as h}from"./vue-vendor-dy9k-Yad.js";import{bx as e,j as V,u as B}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const D={style:{"margin-bottom":"8px"}};const $=x({__name:"SocketReload",setup(E){const{createMessage:u}=B(),p=n(),c=n(!0),d=n(!1),f=n([]),w=n([{key:"num",title:"序号",width:80},{key:"enabled",title:"启用",width:80,type:e.checkbox},{key:"ship_name",title:"船名",width:180,type:e.input},{key:"call",title:"呼叫",width:80,type:e.input},{key:"len",title:"长",width:80,type:e.input},{key:"ton",title:"吨",width:120,type:e.input},{key:"payer",title:"付款方",width:120,type:e.input},{key:"count",title:"数",width:40},{key:"company",title:"公司",minWidth:180,type:e.input},{key:"trend",title:"动向",width:120,type:e.input}]);g();function g(){d.value=!0,V.get({url:"/mock/vxe/getData",params:{pageNo:1,pageSize:200}}).then(t=>{f.value=t.records}).finally(()=>{d.value=!1})}function _(t){switch(t.type){case e.radio:case e.checkbox:m(t);break}}function v(t){m(t)}function m(t){let{$table:a,row:o,column:l}=t,i=l.property;a.isUpdateByRow(o,i)&&a.validate(o).then(s=>{if(!s){let b=u.loading(`正在保存"${l.title}"`,0);setTimeout(()=>{var k;b(),u.success(`"${l.title}"保存成功！`),a.reloadRow(o,null,i),(k=p.value)==null||k.socketSendUpdateRow(o)},555)}})}return(t,a)=>{const o=r("a-switch"),l=r("JVxeTable"),i=r("a-card");return R(),S(i,{title:"无痕刷新示例",bordered:!1},{default:C(()=>[y("div",D,[a[1]||(a[1]=y("span",null,"启用数据变动特效：",-1)),h(o,{checked:c.value,"onUpdate:checked":a[0]||(a[0]=s=>c.value=s)},null,8,["checked"])]),h(l,{ref_key:"tableRef",ref:p,rowNumber:"",rowSelection:"",keepSource:"",socketReload:"",socketKey:"demo-socket-reload",reloadEffect:c.value,height:340,loading:d.value,columns:w.value,dataSource:f.value,onValueChange:_,onEditClosed:v},null,8,["reloadEffect","loading","columns","dataSource"])]),_:1})}}});export{$ as default};
