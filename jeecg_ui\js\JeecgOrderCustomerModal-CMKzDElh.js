var v=Object.defineProperty;var f=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var g=(t,r,o)=>r in t?v(t,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[r]=o,h=(t,r)=>{for(var o in r||(r={}))F.call(r,o)&&g(t,o,r[o]);if(f)for(var o of f(r))M.call(r,o)&&g(t,o,r[o]);return t};var d=(t,r,o)=>new Promise((n,s)=>{var c=i=>{try{p(o.next(i))}catch(a){s(a)}},l=i=>{try{p(o.throw(i))}catch(a){s(a)}},p=i=>i.done?n(i.value):Promise.resolve(i.value).then(c,l);p((o=o.apply(t,r)).next())});import{d as k,c as y,f as C,e as I,u as m,aB as R,ar as L,aD as O,k as U,aE as b}from"./vue-vendor-dy9k-Yad.js";import{B as x}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{e as P,s as S}from"./erplist.api-wW14l8Z-.js";import{u as V}from"./useForm-CgkFTrrO.js";import{ac as j}from"./index-CCWaWN5g.js";import{B as A}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./renderUtils-D7XVOFwj.js";const To=k({__name:"JeecgOrderCustomerModal",emits:["success","register"],setup(t,{emit:r}){const o=y("orderId")||"",n=r,s=C(!0),[c,{resetFields:l,setFieldsValue:p,validate:i}]=V({labelWidth:150,schemas:P,showActionButtonGroup:!1}),[a,{setModalProps:u,closeModal:B}]=j(e=>d(null,null,function*(){yield l(),u({confirmLoading:!1}),s.value=!!(e!=null&&e.isUpdate),m(s)&&(yield p(h({},e.record)))})),_=I(()=>m(s)?"编辑":"新增");function w(){return d(this,null,function*(){try{const e=yield i();u({confirmLoading:!0}),m(o)&&(e.orderId=m(o)),yield S(e,s.value),B(),n("success")}finally{u({confirmLoading:!1})}})}return(e,D)=>(L(),R(m(x),b(e.$attrs,{onRegister:m(a),title:_.value,onOk:w,width:700}),{default:O(()=>[U(m(A),{onRegister:m(c)},null,8,["onRegister"])]),_:1},16,["onRegister","title"]))}});export{To as default};
