import{d as m,f as u,ag as y,aq as h,ar as f,aB as v,ah as x,aD as K,k as D,aA as k}from"./vue-vendor-dy9k-Yad.js";import{cs as S,af as w}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var B=(a,s,n)=>new Promise((l,r)=>{var i=e=>{try{o(n.next(e))}catch(t){r(t)}},d=e=>{try{o(n.throw(e))}catch(t){r(t)}},o=e=>e.done?l(e.value):Promise.resolve(e.value).then(i,d);o((n=n.apply(a,s)).next())});const C=m({name:"LeftDepart",emits:["select"],setup(a,{emit:s}){const n=u([]),l=u([]),r=u([]);function i(t,c){let p=c.node.dataRef;l.value=[p.key],s("select",p.id)}d();function d(){return B(this,null,function*(){let t=yield w();n.value=[],t.forEach(c=>o(c))})}function o(t,c=1){if(t.slots={icon:"depIcon"},c===1&&(n.value.push(t),r.value.push(t.id)),t.children&&t.children.length>0)for(const p of t.children)o(p,c+1)}function e(){l.value=[]}return{treeData:n,expandedKeys:r,selectedKeys:l,clearSelected:e,onSelect:i}}});function g(a,s,n,l,r,i){const d=y("a-icon"),o=y("a-tree");return f(),h("div",null,[a.treeData.length>0?(f(),v(o,{key:0,showIcon:"",autoExpandParent:"",treeData:a.treeData,selectedKeys:a.selectedKeys,expandedKeys:a.expandedKeys,"onUpdate:expandedKeys":s[0]||(s[0]=e=>a.expandedKeys=e),onSelect:a.onSelect},{depIcon:K(({selected:e})=>[D(d,{style:k({color:e?"blue":""}),type:"apartment"},null,8,["style"])]),_:1},8,["treeData","selectedKeys","expandedKeys","onSelect"])):x("",!0)])}const V=S(C,[["render",g]]);export{V as default};
