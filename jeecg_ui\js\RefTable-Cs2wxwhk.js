import{d as T,f as S,u as L,ag as f,aq as y,ar as K,at as c,k as e,aD as n,G as l}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{getBasicColumns as d,getBasicShortColumns as B}from"./tableData-B4J3mkj4.js";import{u as D,a as P}from"./index-CCWaWN5g.js";import{d as v}from"./table-BDFKJhHv.js";import A from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const $=T({components:{BasicTable:A},setup(){const t=S(null),{createMessage:o}=D();function r(){const u=L(t);if(!u)throw new Error("tableAction is null");return u}function a(){r().setLoading(!0),setTimeout(()=>{r().setLoading(!1)},1e3)}function m(){r().setColumns(B())}function p(){r().setColumns(d()),r().reload({page:1})}function i(){o.info("请在控制台查看！")}function s(){o.info("请在控制台查看！")}function C(){o.info("请在控制台查看！")}function g(){o.info("请在控制台查看！")}function k(){r().setPagination({current:2}),r().reload()}function b(){o.info("请在控制台查看！")}function _(){o.info("请在控制台查看！")}function R(){r().setSelectedRowKeys(["0","1","2"])}function w(){r().clearSelectedRowKeys()}return{tableRef:t,api:v,columns:d(),changeLoading:a,changeColumns:m,reloadTable:p,getColumn:i,getTableData:s,getTableRawData:C,getPagination:g,setPaginationInfo:k,getSelectRowList:b,getSelectRowKeyList:_,setSelectedRowKeyList:R,clearSelect:w}}}),M={class:"p-4"},N={class:"mb-4"},V={class:"mb-4"};function E(t,o,r,a,m,p){const i=f("a-button"),s=f("BasicTable");return K(),y("div",M,[c("div",N,[e(i,{class:"mr-2",onClick:t.reloadTable},{default:n(()=>o[0]||(o[0]=[l(" 还原 ")])),_:1,__:[0]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.changeLoading},{default:n(()=>o[1]||(o[1]=[l(" 开启loading ")])),_:1,__:[1]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.changeColumns},{default:n(()=>o[2]||(o[2]=[l(" 更改Columns ")])),_:1,__:[2]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.getColumn},{default:n(()=>o[3]||(o[3]=[l(" 获取Columns ")])),_:1,__:[3]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.getTableData},{default:n(()=>o[4]||(o[4]=[l(" 获取表格数据 ")])),_:1,__:[4]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.getTableRawData},{default:n(()=>o[5]||(o[5]=[l(" 获取接口原始数据 ")])),_:1,__:[5]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.setPaginationInfo},{default:n(()=>o[6]||(o[6]=[l(" 跳转到第2页 ")])),_:1,__:[6]},8,["onClick"])]),c("div",V,[e(i,{class:"mr-2",onClick:t.getSelectRowList},{default:n(()=>o[7]||(o[7]=[l(" 获取选中行 ")])),_:1,__:[7]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.getSelectRowKeyList},{default:n(()=>o[8]||(o[8]=[l(" 获取选中行Key ")])),_:1,__:[8]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.setSelectedRowKeyList},{default:n(()=>o[9]||(o[9]=[l(" 设置选中行 ")])),_:1,__:[9]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.clearSelect},{default:n(()=>o[10]||(o[10]=[l(" 清空选中行 ")])),_:1,__:[10]},8,["onClick"]),e(i,{class:"mr-2",onClick:t.getPagination},{default:n(()=>o[11]||(o[11]=[l(" 获取分页信息 ")])),_:1,__:[11]},8,["onClick"])]),e(s,{canResize:!1,title:"RefTable示例",titleHelpMessage:"使用Ref调用表格内方法",ref:"tableRef",api:t.api,columns:t.columns,rowKey:"id",rowSelection:{type:"checkbox"}},null,8,["api","columns"])])}const Fo=P($,[["render",E]]);export{Fo as default};
