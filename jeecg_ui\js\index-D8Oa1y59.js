const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/SiderTrigger-BhUaEEVb.js","js/vue-vendor-dy9k-Yad.js","js/antd-vue-vendor-me9YkNVC.js","js/index-CCWaWN5g.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/index-CI-8_pdX.js","js/index-JbqXEynz.js","js/index-CImCetrx.js","assets/index-BObJM2Lc.css","js/index-LCGLvkB3.js","js/index-De_W6s5g.js","js/index-D6l0IxOU.js","js/useTimeout-CeTdFD_D.js","js/useIntersectionObserver-C4LVxQJW.js","assets/index-zj-Vfn3Q.css","assets/index-Yp9ECMoG.css","js/useHeaderSetting-C-h5S52e.js","js/useMultipleTabSetting-QBbnIi9J.js","js/index-Dn0aDJhb.js","js/SessionTimeoutLogin-DNwUJYr8.js","js/Login-BbE4lNlw.js","js/LoginForm-Cv1KIRFA.js","js/checkcode-DLY3GIII.js","js/LoginFormTitle-Bms3O5Qx.js","js/ThirdModal-1GWAIf70.js","js/index-DFrpKMGa.js","js/useCountdown-CCWNeb_r.js","js/index-Diw57m_E.js","js/BasicModal-BLFvpBuk.js","js/ModalHeader-BJG9dHtK.js","assets/ModalHeader-HwQKX-UU.css","js/useWindowSizeFn-DDbrQbks.js","assets/BasicModal-ByeTDAzn.css","js/CustomModal-BakuIxQv.js","assets/CustomModal-DWxHZmza.css","assets/index-yRxe3SQ1.css","js/useFormItemSingle-Cw668yj5.js","assets/index-BB9COjV3.css","js/useThirdLogin-C4qxd7Ms.js","js/ForgetPasswordForm-D5tOYC3b.js","js/step1-CkQhLHRg.js","js/step2-tEElob-Y.js","js/index-CBCjSSNZ.js","assets/index-NmxXH94f.css","js/step3-dZT9Navo.js","js/RegisterForm-DI32cwRE.js","js/MobileForm-BL07NIax.js","js/QrCodeForm-CVY8RjiB.js","js/index-C1YxH9KC.js","js/download-CZ-9H9a3.js","js/base64Conver-24EVOS6V.js","assets/Login-Bpjy9ZdB.css","assets/SessionTimeoutLogin-DuIgM9qf.css","assets/index-DsevbakK.css","js/index-D1H52R0n.js","js/siteSetting-DoyCDlSB.js","js/useContentViewHeight-Md7r1NIg.js","js/usePageContext-CxiNGbPs.js","assets/index-BBPHso6E.css"])))=>i.map(i=>d[i]);
var jt=Object.defineProperty,qt=Object.defineProperties;var Xt=Object.getOwnPropertyDescriptors;var Oe=Object.getOwnPropertySymbols;var at=Object.prototype.hasOwnProperty,it=Object.prototype.propertyIsEnumerable;var st=(e,n,o)=>n in e?jt(e,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[n]=o,re=(e,n)=>{for(var o in n||(n={}))at.call(n,o)&&st(e,o,n[o]);if(Oe)for(var o of Oe(n))it.call(n,o)&&st(e,o,n[o]);return e},He=(e,n)=>qt(e,Xt(n));var lt=(e,n)=>{var o={};for(var i in e)at.call(e,i)&&n.indexOf(i)<0&&(o[i]=e[i]);if(e!=null&&Oe)for(var i of Oe(e))n.indexOf(i)<0&&it.call(e,i)&&(o[i]=e[i]);return o};var Y=(e,n,o)=>new Promise((i,c)=>{var u=s=>{try{l(o.next(s))}catch(d){c(d)}},r=s=>{try{l(o.throw(s))}catch(d){c(d)}},l=s=>s.done?i(s.value):Promise.resolve(s.value).then(u,r);l((o=o.apply(e,n)).next())});import{H as O,ay as Yt,cR as Ae,M as de,N as ge,F as q,c as Me,a as V,l as gt,m as ft,bV as Ke,cS as Ee,cT as We,C as Z,cU as Ie,cV as ze,cW as Ge,cX as Be,cY as ht,n as St,cZ as Ne,cI as bt,O as Qt,k as Zt,aj as we,_ as Le,x as Ct,B as Ue,ap as fe,bD as Jt,v as xt,c_ as ae,c$ as Pe,d0 as Mt,bu as Ve,D as yt,b5 as _e,o as en,bY as Je,d1 as tn,bW as nn,ah as Tt,bn as on,d2 as rt,d3 as Re,aU as vt,U as sn,d4 as an,d5 as ln,c5 as je,bt as $t,aa as rn,d6 as Se,d7 as un,a4 as ut,b0 as cn,Q as dn,A as pn}from"./index-CCWaWN5g.js";import{d as j,e as g,ag as b,aq as P,ar as h,as as y,aB as H,ah as D,G as It,au as te,aD as W,k,aE as J,w as oe,F as Q,aJ as wt,aC as ye,u as t,J as qe,f as G,r as ke,I as be,ap as pe,h as Ze,n as De,p as Lt,g as xe,o as Xe,aG as ue,aA as ce,A as kt,at as z,c as mn,D as gn,q as me,B as Ce,aK as fn,z as hn,l as Sn,aH as _t,v as bn}from"./vue-vendor-dy9k-Yad.js";import{k as et,bA as Ot,T as Cn,_ as Mn,cl as yn,cm as Tn,a9 as tt,W as vn,a4 as ct,y as dt}from"./antd-vue-vendor-me9YkNVC.js";import{useTimeoutFn as Ht}from"./useTimeout-CeTdFD_D.js";import{C as $n}from"./index-De_W6s5g.js";import{S as Rt}from"./index-LCGLvkB3.js";import{useHeaderSetting as Ye}from"./useHeaderSetting-C-h5S52e.js";import{a as In,E as wn,b as Ln,N as kn,U as _n,L as On}from"./index-DBlzbq35.js";import Hn from"./LoginSelect-xAWftqo5.js";import Pt from"./index-B7uPTIuA.js";import Rn from"./index-UAmqLQAd.js";import{useMultipleTabSetting as nt}from"./useMultipleTabSetting-QBbnIi9J.js";import{triggerWindowResize as Pn}from"./index-D6l0IxOU.js";import{useSortable as An}from"./useSortable-DxSEgwLd.js";import{u as En}from"./useContentViewHeight-Md7r1NIg.js";import{useLockPage as Bn}from"./useLockPage-DXXvgKdk.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./index-CuNbEm2f.js";import"./usePageContext-CxiNGbPs.js";import"./lock-CRalNsZJ.js";const Nn={items:{type:Array,default:()=>[]},collapsedShowTitle:O.bool,inlineIndent:O.number.def(20),mode:{type:String,default:de.INLINE},type:{type:String,default:Ae.MIX},theme:{type:String,default:Yt.DARK},inlineCollapsed:O.bool,mixSider:O.bool,isHorizontal:O.bool,accordion:O.bool.def(!0),beforeClickFn:{type:Function}},At={item:{type:Object,default:{}},level:O.number,theme:O.oneOf(["dark","light"]),showTitle:O.bool,isHorizontal:O.bool},Dn={item:{type:Object,default:null},showTitle:O.bool.def(!0),level:O.number.def(0),isHorizontal:O.bool.def(!0)},{t:Fn}=ge(),Kn=j({name:"MenuItemContent",components:{Icon:Me},props:Dn,setup(e){const{prefixCls:n}=q("basic-menu-item-content"),o=g(()=>{var c;return Fn((c=e.item)==null?void 0:c.name)}),i=g(()=>{var c;return(c=e.item)==null?void 0:c.icon});return{prefixCls:n,getI18nName:o,getIcon:i}}});function Wn(e,n,o,i,c,u){const r=b("Icon");return h(),P("span",{class:y(`${e.prefixCls}- flex items-center `)},[e.getIcon?(h(),H(r,{key:0,icon:e.getIcon,size:18,class:y(`${e.prefixCls}-wrapper__icon mr-2`)},null,8,["icon","class"])):D("",!0),It(" "+te(e.getI18nName),1)],2)}const Et=V(Kn,[["render",Wn]]),zn=j({name:"BasicMenuItem",components:{MenuItem:et.Item,MenuItemContent:Et},props:At,setup(){return{}}});function Gn(e,n,o,i,c,u){const r=b("MenuItemContent"),l=b("MenuItem");return h(),H(l,{key:e.item.path,title:e.item.title},{default:W(()=>[k(r,J(e.$props,{item:e.item}),null,16,["item"])]),_:1},8,["title"])}const Un=V(zn,[["render",Gn]]),Bt=Symbol();function Vn(e){return gt(e,Bt,{readonly:!1,native:!0})}function jn(){return ft(Bt)}const qn=j({name:"BasicSubMenuItem",isSubMenu:!0,components:{BasicMenuItem:Un,SubMenu:et.SubMenu,MenuItemContent:Et},props:At,setup(e){const{prefixCls:n}=q("basic-subMenu"),{menuState:o}=jn(),i=Ke(),c=g(()=>{var l;return!((l=e.item.meta)!=null&&l.hideMenu)});function u(l){var s;return!((s=l.meta)!=null&&s.hideChildrenInMenu)&&Reflect.has(l,"children")&&!!l.children&&l.children.length>0&&Ee(l)}const r=(l,s)=>{var d,a;for(let m=0,p=l.length;m<p;m++){const S=l[m];if(S.path===s&&!S.redirect&&!S.paramPath)return(d=S.meta)==null?void 0:d.title;if((a=S.children)!=null&&a.length){const f=r(S.children,s);if(f)return f}}return""};return oe(()=>o.selectedKeys,l=>Y(null,null,function*(){if(l.length&&l.includes(e.item.path)){const s=yield We(),d=r(s,e.item.path);i.setPathTitle(e.item.path,d||e.item.name)}}),{immediate:!0}),{prefixCls:n,menuHasChildren:u,checkChildrenHidden:Ee,getShowMenu:c}}});function Xn(e,n,o,i,c,u){const r=b("BasicMenuItem"),l=b("MenuItemContent"),s=b("BasicSubMenuItem",!0),d=b("SubMenu");return h(),P(Q,null,[!e.menuHasChildren(e.item)&&e.getShowMenu?(h(),H(r,wt(J({key:0},e.$props)),null,16)):D("",!0),e.menuHasChildren(e.item)&&e.getShowMenu?(h(),H(d,{class:y([e.theme]),key:`submenu-${e.item.path}`,popupClassName:e.prefixCls},{title:W(()=>[k(l,J(e.$props,{item:e.item}),null,16,["item"])]),default:W(()=>[(h(!0),P(Q,null,ye(e.item.children||[],a=>(h(),H(s,J({key:a.path,ref_for:!0},e.$props,{item:a}),null,16,["item"]))),128))]),_:1},8,["class","popupClassName"])):D("",!0)],64)}const Yn=V(qn,[["render",Xn]]);function Qn(e,n,o,i){const{getCollapsed:c,getIsMixSidebar:u}=Z();function r(a){return Y(this,null,function*(){if(o.value===de.HORIZONTAL)return;const m=t(u);Ht(()=>{const p=qe(n.value);if((p==null?void 0:p.length)===0){e.openKeys=[];return}t(i)?e.openKeys=Ie(p,a):e.openKeys=Ot([...e.openKeys,...Ie(p,a)])},16,!m)})}const l=g(()=>(t(u)?!1:t(c))?e.collapsedOpenKeys:e.openKeys);function s(){e.selectedKeys=[],e.openKeys=[]}function d(a){if(t(o)===de.HORIZONTAL||!t(i)||t(u))e.openKeys=a;else{const m=[];for(const{children:p,path:S}of t(n))p&&p.length>0&&m.push(S);if(t(c))e.collapsedOpenKeys=a;else{const p=a.find(S=>e.openKeys.indexOf(S)===-1);m.indexOf(p)===-1?e.openKeys=a:e.openKeys=p?[p]:[]}}}return{setOpenKeys:r,resetKeys:s,getOpenKeys:l,handleOpenChange:d}}const Zn=j({name:"BasicMenu",components:{Menu:et,BasicSubMenuItem:Yn},props:Nn,emits:["menuClick"],setup(e,{emit:n}){const o=G(!1),i=G(""),c=ke({defaultSelectedKeys:[],openKeys:[],selectedKeys:[],collapsedOpenKeys:[]});Vn({menuState:c});const{prefixCls:u}=q("basic-menu"),{items:r,mode:l,accordion:s}=be(e),{getCollapsed:d,getTopMenuAlign:a,getSplit:m}=Z(),{currentRoute:p}=pe(),{handleOpenChange:S,setOpenKeys:f,getOpenKeys:$}=Qn(c,r,l,s),w=g(()=>{const{type:L,mode:M}=e;return L===Ae.TOP_MENU&&M===de.HORIZONTAL||e.isHorizontal&&t(m)}),C=g(()=>{const L=e.isHorizontal&&t(m)?"start":t(a);return[u,`justify-${L}`,{[`${u}__second`]:!e.isHorizontal&&t(m),[`${u}__sidebar-hor`]:t(w)}]}),v=g(()=>{const L=e.mode===de.INLINE,M={};return L&&(M.inlineCollapsed=e.mixSider?!1:t(d)),M});ze(L=>{var M;L.name!==Ge&&(I(L),i.value=(M=L.meta)==null?void 0:M.currentActiveMenu,t(i)&&(c.selectedKeys=[t(i)],f(t(i))))}),!e.mixSider&&oe(()=>e.items,()=>{I()});function R(T){return Y(this,arguments,function*({item:L,key:M}){var B,X;const{beforeClickFn:A}=e;if(Be(M)){M=M.replace(ht,"#"),window.open(M);return}const E=U(e.items,M);if((E==null?void 0:E.internalOrExternal)==!0){window.open(location.origin+M);return}if(!(A&&St(A)&&!(yield A(M)))){if(e.type===Ae.MIX){const le=yield We(),x=N(le,M);if(x&&!x.redirect&&((B=x.children)!=null&&B.length)){const _=K(x.children);if(_!=null&&_.path){const ee=(X=_.redirect)!=null?X:_.path;let ne=ee;Be(ee)&&(window.open(ee),ne=M),n("menuClick",ne,{title:_.title})}else n("menuClick",M,L)}else n("menuClick",M,L)}else n("menuClick",M,L);o.value=!0,c.selectedKeys=[M]}})}function I(L){return Y(this,null,function*(){var T;if(t(o)){o.value=!1;return}const M=((T=(L||t(p)).meta)==null?void 0:T.currentActiveMenu)||(L||t(p)).path;if(f(M),!t(i))if(e.isHorizontal&&t(m)){const A=yield Ne(M);c.selectedKeys=[A]}else{const A=yield Ie(e.items,M);c.selectedKeys=A}})}function K(L){var M,T;for(let A=0,E=L.length;A<E;A++){const B=L[A];if(B.path&&!((M=B.children)!=null&&M.length))return B;if((T=B.children)!=null&&T.length){const X=K(B.children);if(X)return X}}return null}function N(L,M){var T;for(let A=0,E=L.length;A<E;A++){const B=L[A];if(B.path===M)return B;if((T=B.children)!=null&&T.length){const X=N(B.children,M);if(X)return X}}return null}const U=(L,M)=>{var T;for(let A=0,E=L.length;A<E;A++){const B=L[A];if(B.path===M&&!B.redirect&&!B.paramPath)return B;if((T=B.children)!=null&&T.length){const X=U(B.children,M);if(X)return X}}return""};return re({handleMenuClick:R,getInlineCollapseOptions:v,getMenuClass:C,handleOpenChange:S,getOpenKeys:$},be(c))}});function Jn(e,n,o,i,c,u){const r=b("BasicSubMenuItem"),l=b("Menu");return h(),H(l,J({selectedKeys:e.selectedKeys,defaultSelectedKeys:e.defaultSelectedKeys,mode:e.mode,openKeys:e.getOpenKeys,inlineIndent:e.inlineIndent,theme:e.theme,onOpenChange:e.handleOpenChange,class:e.getMenuClass,onClick:e.handleMenuClick,subMenuOpenDelay:.2},e.getInlineCollapseOptions),{default:W(()=>[(h(!0),P(Q,null,ye(e.items,s=>(h(),H(r,{key:s.path,item:s,theme:e.theme,isHorizontal:e.isHorizontal},null,8,["item","theme","isHorizontal"]))),128))]),_:1},16,["selectedKeys","defaultSelectedKeys","mode","openKeys","inlineIndent","theme","onOpenChange","class","onClick"])}const xn=V(Zn,[["render",Jn]]),Nt=Symbol();function eo(e){return gt(e,Nt,{readonly:!1,native:!0})}function Dt(){return ft(Nt)}const to=j({name:"Menu",props:{theme:O.oneOf(["light","dark"]).def("light"),activeName:O.oneOfType([O.string,O.number]),openNames:{type:Array,default:()=>[]},accordion:O.bool.def(!0),width:O.string.def("100%"),collapsedWidth:O.string.def("48px"),indentSize:O.number.def(16),collapse:O.bool.def(!0),activeSubMenuNames:{type:Array,default:()=>[]}},emits:["select","open-change"],setup(e,{emit:n}){const o=bt(),i=xe(),c=G(""),u=G([]),{prefixCls:r}=q("menu"),l=G(!1);eo({rootMenuEmitter:o,activeName:c});const s=g(()=>{const{theme:f}=e;return[r,`${r}-${f}`,`${r}-vertical`,{[`${r}-collapse`]:e.collapse}]});Ze(()=>{u.value=e.openNames}),Ze(()=>{e.activeName&&(c.value=e.activeName)}),oe(()=>e.openNames,()=>{De(()=>{d()})});function d(){o.emit("on-update-opened",u.value)}function a(f){u.value.includes(f)||(u.value.push(f),d())}function m(f){u.value=u.value.filter($=>$!==f),d()}function p(){u.value=[],d()}function S(f){f!==-1&&(u.value=u.value.slice(0,f+1),d())}return Lt(`subMenu:${i==null?void 0:i.uid}`,{addSubMenu:a,removeSubMenu:m,getOpenNames:()=>u.value,removeAll:p,isRemoveAllPopup:l,sliceIndex:S,level:0,props:e}),Xe(()=>{u.value=e.collapse?[]:[...e.openNames],d(),o.on("on-menu-item-select",f=>{c.value=f,De(()=>{e.collapse&&p()}),n("select",f)}),o.on("open-name-change",({name:f,opened:$})=>{if($&&!u.value.includes(f))u.value.push(f);else if(!$){const w=u.value.findIndex(C=>C===f);w!==-1&&u.value.splice(w,1)}})}),{getClass:s,openedNames:u}}});function no(e,n,o,i,c,u){return h(),P("ul",{class:y(e.getClass)},[ue(e.$slots,"default")],2)}const oo=V(to,[["render",no]]);function Ft(e){const n=g(()=>u(["Menu","SubMenu"])),o=g(()=>u(["Menu"])),i=g(()=>u(["SubMenu"])),c=g(()=>{var m,p,S;let s=e==null?void 0:e.parent;if(!s)return{};const d=(p=(m=t(o))==null?void 0:m.props.indentSize)!=null?p:20;let a=d;if((S=t(o))!=null&&S.props.collapse)a=d;else for(;s&&s.type.name!=="Menu";)s.type.name==="SubMenu"&&(a+=d),s=s.parent;return{paddingLeft:a+"px"}});function u(s){let d=e==null?void 0:e.parent;if(!d)return null;for(;d&&s.indexOf(d.type.name)===-1;)d=d.parent;return d}function r(){let s=e;if(!s)return{uidList:[],list:[]};const d=[];for(;s&&s.type.name!=="Menu";)s.type.name==="SubMenu"&&d.push(s),s=s.parent;return{uidList:d.map(a=>a.uid),list:d}}function l(s,d="SubMenu"){let a=s.parent;for(;a;){if(a.type.name!==d)return a;a=a.parent}return a}return{getParentMenu:n,getParentInstance:l,getParentRootMenu:o,getParentList:r,getParentSubMenu:i,getItemStyle:c}}const so=j({name:"MenuItem",components:{Tooltip:Cn},props:{name:{type:[String,Number],required:!0},disabled:O.bool},setup(e,{slots:n}){const o=xe(),i=Ke(),c=G(!1),{getItemStyle:u,getParentList:r,getParentMenu:l,getParentRootMenu:s}=Ft(o),{prefixCls:d}=q("menu"),{rootMenuEmitter:a,activeName:m}=Dt(),p=g(()=>[`${d}-item`,{[`${d}-item-active`]:t(c),[`${d}-item-selected`]:t(c),[`${d}-item-disabled`]:!!e.disabled}]),S=g(()=>{var C;return(C=t(s))==null?void 0:C.props.collapse}),f=g(()=>{var C;return((C=t(l))==null?void 0:C.type.name)==="Menu"&&t(S)&&n.title});function $(){const{disabled:C}=e;if(C||(a.emit("on-menu-item-select",e.name),t(S)))return;const{uidList:v}=r();a.emit("on-update-opened",{opend:!1,parent:o==null?void 0:o.parent,uidList:v})}oe(()=>m.value,C=>{if(C===e.name){const{list:v,uidList:R}=r();c.value=!0,v.forEach(I=>{I.proxy&&(I.proxy.active=!0)}),w(e.name),a.emit("on-update-active-name:submenu",R)}else c.value=!1},{immediate:!0});function w(C){let v="";if(o.attrs){let R=o.attrs.item;R&&(v=R.title)}i&&i.setPathTitle(C,v)}return{getClass:p,prefixCls:d,getItemStyle:u,getCollapse:S,handleClickItem:$,showTooptip:f}}});function ao(e,n,o,i,c,u){const r=b("Tooltip");return h(),P("li",{class:y(e.getClass),onClick:n[0]||(n[0]=kt((...l)=>e.handleClickItem&&e.handleClickItem(...l),["stop"])),style:ce(e.getCollapse?{}:e.getItemStyle)},[e.showTooptip?(h(),H(r,{key:0,placement:"right"},{title:W(()=>[ue(e.$slots,"title")]),default:W(()=>[z("div",{class:y(`${e.prefixCls}-tooltip`)},[ue(e.$slots,"default")],2)]),_:3})):(h(),P(Q,{key:1},[ue(e.$slots,"default"),ue(e.$slots,"title")],64))],6)}const io=V(so,[["render",ao]]),pt=200,lo=j({name:"SubMenu",components:{Icon:Me,CollapseTransition:$n,Popover:Mn},props:{name:{type:[String,Number],required:!0},disabled:O.bool,collapsedShowTitle:O.bool,isThemeBright:{type:Boolean,default:!1}},setup(e){var x;const n=xe(),o=ke({active:!1,opened:!1}),i=ke({timeout:null,mouseInChild:!1,isChild:!1}),{getParentSubMenu:c,getItemStyle:u,getParentMenu:r,getParentList:l}=Ft(n),{prefixCls:s}=q("menu"),d=bt(),{rootMenuEmitter:a}=Dt(),{addSubMenu:m,removeSubMenu:p,removeAll:S,getOpenNames:f,isRemoveAllPopup:$,sliceIndex:w,level:C,props:v,handleMouseleave:R}=mn(`subMenu:${(x=r.value)==null?void 0:x.uid}`),I=g(()=>[`${s}-submenu`,{[`${s}-item-active`]:o.active,[`${s}-opened`]:o.opened,[`${s}-submenu-disabled`]:e.disabled,[`${s}-submenu-has-parent-submenu`]:t(c),[`${s}-child-item-active`]:o.active}]),K=g(()=>v.accordion),N=g(()=>v.collapse),U=g(()=>v.theme),L=g(()=>({minWidth:"150px"})),M=g(()=>{const _=e.name;return t(N)?f().includes(_):o.opened}),T=g(()=>{const _=v.activeSubMenuNames.includes(e.name);return[`${s}-submenu-title`,{[`${s}-submenu-active`]:_,[`${s}-submenu-active-border`]:_&&C===0,[`${s}-submenu-collapse`]:t(N)&&C===0}]});function A(_){return t(N)?{onMouseenter:B,onMouseleave:()=>X(_)}:{}}function E(){const{disabled:_}=e;if(_||t(N))return;const ee=o.opened;if(t(K)){const{uidList:ne}=l();a.emit("on-update-opened",{opend:!1,parent:n==null?void 0:n.parent,uidList:ne})}else a.emit("open-name-change",{name:e.name,opened:!ee});o.opened=!ee}function B(){if(e.disabled)return;d.emit("submenu:mouse-enter-child");const ee=f().findIndex(Te=>Te===e.name);w(ee),C===0&&f().length===2&&S(),i.isChild=f().includes(e.name),clearTimeout(i.timeout),i.timeout=setTimeout(()=>{m(e.name)},pt)}function X(_=!1){var ne;((ne=r.value)==null?void 0:ne.props.name)||($.value=!0),f().slice(-1)[0]===e.name&&(i.isChild=!1),d.emit("submenu:mouse-leave-child"),i.timeout&&(clearTimeout(i.timeout),i.timeout=setTimeout(()=>{$.value?S():i.mouseInChild||p(e.name)},pt)),_&&c.value&&(R==null||R(!0))}gn(()=>{d.on("submenu:mouse-enter-child",()=>{i.mouseInChild=!0,$.value=!1,clearTimeout(i.timeout)}),d.on("submenu:mouse-leave-child",()=>{i.isChild||(i.mouseInChild=!1,clearTimeout(i.timeout))}),a.on("on-update-opened",_=>{if(!t(N)){if(Qt(_)){o.opened=_;return}if(Zt(_)&&v.accordion){const{opend:ee,parent:ne,uidList:Te}=_;ne===(n==null?void 0:n.parent)?o.opened=ee:Te.includes(n==null?void 0:n.uid)||(o.opened=!1);return}e.name&&Array.isArray(_)&&(o.opened=_.includes(e.name))}}),a.on("on-update-active-name:submenu",_=>{n!=null&&n.uid&&(o.active=_.includes(n==null?void 0:n.uid))})});function le(_){o.opened=_}return Lt(`subMenu:${n==null?void 0:n.uid}`,{addSubMenu:m,removeSubMenu:p,getOpenNames:f,removeAll:S,isRemoveAllPopup:$,sliceIndex:w,level:C+1,handleMouseleave:X,props:v}),re(re({getClass:I,prefixCls:s,getCollapse:N,getItemStyle:u,handleClick:E,handleVisibleChange:le,getParentSubMenu:c,getOverlayStyle:L,getTheme:U,getIsOpend:M,getEvents:A,getSubClass:T},be(o)),be(i))}});function ro(e,n,o,i,c,u){const r=b("Icon"),l=b("CollapseTransition"),s=b("Popover");return h(),P("li",{class:y(e.getClass)},[e.getCollapse?(h(),H(s,{key:1,placement:"right",overlayClassName:`${e.prefixCls}-menu-popover`,open:e.getIsOpend,onOpenChange:e.handleVisibleChange,overlayStyle:e.getOverlayStyle,align:{offset:[0,0]}},{content:W(()=>[z("div",wt(fn(e.getEvents(!0))),[z("ul",{class:y([e.prefixCls,`${e.prefixCls}-${e.getTheme}`,`${e.prefixCls}-popup`,`${e.isThemeBright&&"bright"}`])},[ue(e.$slots,"default")],2)],16)]),default:W(()=>[z("div",J({class:e.getSubClass},e.getEvents(!1)),[z("div",{class:y([{[`${e.prefixCls}-submenu-popup`]:!e.getParentSubMenu,[`${e.prefixCls}-submenu-collapsed-show-tit`]:e.collapsedShowTitle}])},[ue(e.$slots,"title")],2),e.getParentSubMenu?(h(),H(r,{key:0,icon:"eva:arrow-ios-downward-outline",size:14,class:y(`${e.prefixCls}-submenu-title-icon`)},null,8,["class"])):D("",!0)],16)]),_:3},8,["overlayClassName","open","onOpenChange","overlayStyle"])):(h(),P(Q,{key:0},[z("div",{class:y(`${e.prefixCls}-submenu-title`),onClick:n[0]||(n[0]=kt((...d)=>e.handleClick&&e.handleClick(...d),["stop"])),style:ce(e.getItemStyle)},[ue(e.$slots,"title",{getIsOpened:e.getIsOpend}),k(r,{icon:"eva:arrow-ios-downward-outline",size:14,class:y(`${e.prefixCls}-submenu-title-icon`)},null,8,["class"])],6),k(l,null,{default:W(()=>[me(z("ul",{class:y(e.prefixCls)},[ue(e.$slots,"default")],2),[[Ce,e.opened]])]),_:3})],64))],2)}const uo=V(lo,[["render",ro]]),co=j({name:"SimpleSubMenu",components:{SubMenu:uo,MenuItem:io,SimpleMenuTag:we(()=>Le(()=>Promise.resolve().then(()=>yo),void 0)),Icon:Me},props:{item:{type:Object,default:()=>({})},parent:O.bool,collapsedShowTitle:O.bool,collapse:O.bool,theme:O.oneOf(["dark","light"]),isThemeBright:{type:Boolean,default:!1}},setup(e){const{t:n}=ge(),{prefixCls:o}=q("simple-menu"),i=g(()=>{var a,m;return!((m=(a=e.item)==null?void 0:a.meta)!=null&&m.hideMenu)}),c=g(()=>{var a;return(a=e.item)==null?void 0:a.icon}),u=g(()=>{var a;return n((a=e.item)==null?void 0:a.name)}),r=g(()=>!e.collapse||!e.parent),l=g(()=>!!e.collapse&&!!e.parent),s=g(()=>[{[`${o}__parent`]:e.parent,[`${o}__children`]:!e.parent}]);function d(a){var m;return!((m=a.meta)!=null&&m.hideChildrenInMenu)&&Reflect.has(a,"children")&&!!a.children&&a.children.length>0&&Ee(a)}return{prefixCls:o,menuHasChildren:d,checkChildrenHidden:Ee,getShowMenu:i,getIcon:c,getI18nName:u,getShowSubTitle:r,getLevelClass:s,getIsCollapseParent:l}}}),po={key:1,class:"mt-1 collapse-title"},mo={key:1,class:"mt-2 collapse-title"};function go(e,n,o,i,c,u){const r=b("Icon"),l=b("SimpleMenuTag"),s=b("MenuItem"),d=b("SimpleSubMenu",!0),a=b("SubMenu");return h(),P(Q,null,[!e.menuHasChildren(e.item)&&e.getShowMenu?(h(),H(s,J({key:0,name:e.item.path},e.$props,{class:e.getLevelClass}),{title:W(()=>[z("span",{class:y(["ml-2",`${e.prefixCls}-sub-title`])},te(e.getI18nName),3),k(l,{item:e.item,collapseParent:e.getIsCollapseParent},null,8,["item","collapseParent"])]),default:W(()=>[e.getIcon?(h(),H(r,{key:0,icon:e.getIcon,size:16},null,8,["icon"])):D("",!0),e.collapsedShowTitle&&e.getIsCollapseParent?(h(),P("div",po,te(e.getI18nName),1)):D("",!0)]),_:1},16,["name","class"])):D("",!0),e.menuHasChildren(e.item)&&e.getShowMenu?(h(),H(a,{key:1,isThemeBright:e.isThemeBright,name:e.item.path,class:y([e.getLevelClass,e.theme]),collapsedShowTitle:e.collapsedShowTitle},{title:W(()=>[e.getIcon?(h(),H(r,{key:0,icon:e.getIcon,size:16},null,8,["icon"])):D("",!0),e.collapsedShowTitle&&e.getIsCollapseParent?(h(),P("div",mo,te(e.getI18nName),1)):D("",!0),me(z("span",{class:y(["ml-2",`${e.prefixCls}-sub-title`])},te(e.getI18nName),3),[[Ce,e.getShowSubTitle]]),k(l,{item:e.item,collapseParent:!!e.collapse&&!!e.parent},null,8,["item","collapseParent"])]),default:W(()=>[(h(!0),P(Q,null,ye(e.item.children||[],m=>(h(),H(d,J({key:m.path,ref_for:!0},e.$props,{isThemeBright:e.isThemeBright,item:m,parent:!1}),null,16,["isThemeBright","item"]))),128))]),_:1},8,["isThemeBright","name","class","collapsedShowTitle"])):D("",!0)],64)}const fo=V(co,[["render",go]]);function ho(e,n,o,i,c){const u=Ct(r,50);function r(s){return Y(this,null,function*(){const d=!i.value,a=qe(n.value);Ht(()=>{if((a==null?void 0:a.length)===0){e.activeSubMenuNames=[],e.openNames=[];return}const m=Ie(a,s);t(o)?e.openNames=m:e.openNames=Ot([...e.openNames,...m]),e.activeSubMenuNames=e.openNames},30,d)})}const l=g(()=>t(c)?[]:e.openNames);return{setOpenKeys:u,getOpenKeys:l}}const So=j({name:"SimpleMenu",components:{Menu:oo,SimpleSubMenu:fo},inheritAttrs:!1,props:{items:{type:Array,default:()=>[]},collapse:O.bool,mixSider:O.bool,theme:O.string,accordion:O.bool.def(!0),collapsedShowTitle:O.bool,beforeClickFn:{type:Function},isSplitMenu:O.bool},emits:["menuClick"],setup(e,{attrs:n,emit:o}){const i=G(""),c=G(!1),u=Ue(),r=G(!1),l=ke({activeName:"",openNames:[],activeSubMenuNames:[]}),{currentRoute:s}=pe(),{prefixCls:d}=q("simple-menu"),{items:a,accordion:m,mixSider:p,collapse:S}=be(e),{setOpenKeys:f,getOpenKeys:$}=ho(l,a,m,p,S),w=g(()=>re(re({},n),e));oe(()=>e.collapse,I=>{I?l.openNames=[]:f(s.value.path)},{immediate:!0}),oe(()=>e.items,()=>{e.isSplitMenu&&f(s.value.path)},{flush:"post"}),oe(()=>u.getProjectConfig.menuSetting,I=>{r.value=!!(I!=null&&I.isThemeBright)},{immediate:!0,deep:!0}),ze(I=>{var K;I.name!==Ge&&(i.value=(K=I.meta)==null?void 0:K.currentActiveMenu,C(I),t(i)&&(l.activeName=t(i),f(t(i))))});function C(I){return Y(this,null,function*(){if(t(c)){c.value=!1;return}const K=(I||t(s)).path;l.activeName=K,f(K)})}function v(I){return Y(this,null,function*(){if(Be(I)){let U=I.replace(ht,"#");window.open(U);return}const K=R(e.items,I);if((K==null?void 0:K.internalOrExternal)==!0){window.open(location.origin+I);return}const{beforeClickFn:N}=e;N&&St(N)&&!(yield N(I))||(o("menuClick",I),c.value=!0,f(I),l.activeName=I)})}const R=(I,K)=>{var N;for(let U=0,L=I.length;U<L;U++){const M=I[U];if(M.path===K&&!M.redirect&&!M.paramPath)return M;if((N=M.children)!=null&&N.length){const T=R(M.children,K);if(T)return T}}return""};return He(re({prefixCls:d,getBindValues:w,handleSelect:v,getOpenKeys:$},be(l)),{isThemeBright:r})}});function bo(e,n,o,i,c,u){const r=b("SimpleSubMenu"),l=b("Menu");return h(),H(l,J(e.getBindValues,{activeName:e.activeName,openNames:e.getOpenKeys,class:`${e.prefixCls} ${e.isThemeBright?"bright":""}`,activeSubMenuNames:e.activeSubMenuNames,onSelect:e.handleSelect}),{default:W(()=>[(h(!0),P(Q,null,ye(e.items,s=>(h(),H(r,{key:s.path,isThemeBright:e.isThemeBright,item:s,parent:!0,collapsedShowTitle:e.collapsedShowTitle,collapse:e.collapse},null,8,["isThemeBright","item","collapsedShowTitle","collapse"]))),128))]),_:1},16,["activeName","openNames","class","activeSubMenuNames","onSelect"])}const Kt=V(So,[["render",bo]]),Co=j({name:"SimpleMenuTag",props:{item:{type:Object,default:()=>({})},dot:O.bool,collapseParent:O.bool},setup(e){const{prefixCls:n}=q("simple-menu"),o=g(()=>{const{item:u}=e;if(!u)return!1;const{tag:r}=u;if(!r)return!1;const{dot:l,content:s}=r;return!(!l&&!s)}),i=g(()=>{if(!o.value)return"";const{item:u,collapseParent:r}=e,{tag:l}=u,{dot:s,content:d}=l;return s||r?"":d});return{getTagClass:g(()=>{const{item:u,collapseParent:r}=e,{tag:l={}}=u||{},{dot:s,type:d="error"}=l,a=`${n}-tag`;return[a,[`${a}--${d}`],{[`${a}--collapse`]:r,[`${a}--dot`]:s||e.dot}]}),getShowTag:o,getContent:i}}});function Mo(e,n,o,i,c,u){return e.getShowTag?(h(),P("span",{key:0,class:y(e.getTagClass)},te(e.getContent),3)):D("",!0)}const Wt=V(Co,[["render",Mo]]),yo=Object.freeze(Object.defineProperty({__proto__:null,default:Wt},Symbol.toStringTag,{value:"Module"}));function To(e){const n=G([]),{currentRoute:o}=pe(),{getIsMobile:i}=fe(),c=Jt(),{setMenuSetting:u,getIsHorizontal:r,getSplit:l}=Z(),s=xt(S,50),d=g(()=>t(e)!==ae.LEFT&&!t(r)),a=g(()=>!t(l)||t(e)!==ae.LEFT),m=g(()=>t(e)===ae.TOP),p=g(()=>t(e)===ae.NONE||!t(l));oe([()=>t(o).path,()=>t(e)],w=>Y(null,[w],function*([$]){if(t(d)||t(i))return;const{meta:C}=t(o),v=C.currentActiveMenu;let R=yield Ne($);R||(R=yield Ne(v)),R&&s(R)}),{immediate:!0}),oe([()=>c.getLastBuildMenuTime,()=>c.getBackMenuList],()=>{f()},{immediate:!0}),oe(()=>l.value,()=>{f()});function S($){return Y(this,null,function*(){if(t(a)||t(i))return;const w=yield Pe($);if(!w||!w.length){u({hidden:!0}),n.value=[];return}u({hidden:!1}),n.value=w})}function f(){return Y(this,null,function*(){if(t(p)||t(i)){n.value=yield We();return}if(t(m)){const $=yield Mt();n.value=$;return}})}return{menusRef:n}}const vo=j({name:"LayoutMenu",props:{theme:O.oneOf(["light","dark"]),splitType:{type:Number,default:ae.NONE},isHorizontal:O.bool,menuMode:{type:[String],default:""}},setup(e){const n=Ve(),{getMenuMode:o,getMenuType:i,getMenuTheme:c,getCollapsed:u,getCollapsedShowTitle:r,getAccordion:l,getIsHorizontal:s,getIsSidebarType:d,getSplit:a}=Z(),{getShowLogo:m}=yt(),{prefixCls:p}=q("layout-menu"),S=_e(),{menusRef:f}=To(hn(e,"splitType")),{getIsMobile:$}=fe(),w=g(()=>t($)?de.INLINE:e.menuMode||t(o)),C=g(()=>e.theme||t(c)),v=g(()=>t(m)&&t(d)),R=g(()=>S.isQiankunMicro?!1:!t(s)&&(t(d)||e.splitType===ae.LEFT||e.splitType===ae.NONE)),I=g(()=>({height:`calc(100% - ${t(v)?"86px":"0px"})`})),K=g(()=>[`${p}-logo`,t(C),{[`${p}--mobile`]:t($)}]),N=g(()=>{const E=t(f);return{menus:E,beforeClickFn:M,items:E,theme:t(C),accordion:t(l),collapse:t(u),collapsedShowTitle:t(r),onMenuClick:L}}),U=Ke();function L(E,B){B&&U.setPathTitle(E,B.title||""),n(E)}function M(E){return Y(this,null,function*(){return Be(E)?(en(E),!1):!0})}function T(){return!t(v)&&!t($)?null:k(Je,{showTitle:!t(u),class:t(K),theme:t(C)},null)}function A(){const X=t(N),{menus:E}=X,B=lt(X,["menus"]);return!E||!E.length?null:e.isHorizontal?k(xn,J(B,{isHorizontal:e.isHorizontal,type:t(i),showLogo:t(v),mode:t(w),items:E}),null):k(Kt,J(B,{isSplitMenu:t(a),items:E}),null)}return()=>k(Q,null,[T(),t(R)?k(Rt,{style:t(I)},{default:()=>A()}):A()])}}),zt=V(vo,[["__scopeId","data-v-e6ae23ec"]]),$o=j({name:"HeaderTrigger",components:{MenuUnfoldOutlined:Tn,MenuFoldOutlined:yn},props:{theme:O.oneOf(["light","dark"])},setup(){const{getCollapsed:e,toggleCollapsed:n}=Z(),{prefixCls:o}=q("layout-header-trigger");return{getCollapsed:e,toggleCollapsed:n,prefixCls:o}}});function Io(e,n,o,i,c,u){const r=b("MenuUnfoldOutlined"),l=b("MenuFoldOutlined");return h(),P("span",{class:y([e.prefixCls,e.theme]),onClick:n[0]||(n[0]=(...s)=>e.toggleCollapsed&&e.toggleCollapsed(...s))},[e.getCollapsed?(h(),H(r,{key:0})):(h(),H(l,{key:1}))],2)}const wo=V($o,[["render",Io]]),Lo=j({name:"LayoutTrigger",components:{SiderTrigger:we(()=>Le(()=>import("./SiderTrigger-BhUaEEVb.js"),__vite__mapDeps([0,1,2,3,4,5]))),HeaderTrigger:wo},props:{sider:O.bool.def(!0),theme:O.oneOf(["light","dark"])}});function ko(e,n,o,i,c,u){const r=b("SiderTrigger"),l=b("HeaderTrigger");return e.sider?(h(),H(r,{key:0})):(h(),H(l,{key:1,theme:e.theme},null,8,["theme"]))}const Fe=V(Lo,[["render",ko]]),{t:_o}=ge(),Oo=j({name:"LayoutHeader",components:{Header:tt.Header,AppLogo:Je,LayoutTrigger:Fe,LayoutBreadcrumb:On,LayoutMenu:zt,UserDropDown:_n,AppLocalePicker:nn,FullScreen:kn,Notify:Ln,AppSearch:tn,ErrorAction:wn,LockScreen:In,LoginSelect:Hn,SettingDrawer:we(()=>Le(()=>import("./index-CI-8_pdX.js").then(e=>e.i),__vite__mapDeps([6,1,7,3,2,4,5,8,9,10,11,12,13,14,15,16,17,18])),{loading:!0}),Aide:Pt},props:{fixed:O.bool},setup(e){const{prefixCls:n}=q("layout-header"),o=Tt(),{getShowTopMenu:i,getShowHeaderTrigger:c,getSplit:u,getIsMixMode:r,getMenuWidth:l,getIsMixSidebar:s}=Z(),{getUseErrorHandle:d,getShowSettingButton:a,getSettingButtonPosition:m}=yt(),{title:p}=_e(),{getHeaderTheme:S,getShowFullScreen:f,getShowNotice:$,getShowContent:w,getShowBread:C,getShowHeaderLogo:v,getShowHeader:R,getShowSearch:I,getUseLockPage:K,getShowBreadTitle:N}=Ye(),{getShowLocalePicker:U}=on(),{getIsMobile:L}=fe(),M=g(()=>{const _=t(S);return[n,{[`${n}--fixed`]:e.fixed,[`${n}--mobile`]:t(L),[`${n}--${_}`]:_}]}),T=g(()=>{if(!t(a))return!1;const _=t(m);return _===rt.AUTO?t(R):_===rt.HEADER}),A=g(()=>!t(r)||t(L)?{}:{width:`${t(l)<180?180:t(l)}px`}),E=g(()=>t(u)?ae.TOP:ae.NONE),B=g(()=>t(u)?de.HORIZONTAL:null),X=G();function le(){const _=qe(o.getLoginInfo)||{};_.isLogin&&X.value.show(_)}function x(){}return Xe(()=>{le()}),{prefixCls:n,getHeaderClass:M,getShowHeaderLogo:v,getHeaderTheme:S,getShowHeaderTrigger:c,getIsMobile:L,getShowBreadTitle:N,getShowBread:C,getShowContent:w,getSplitType:E,getSplit:u,getMenuMode:B,getShowTopMenu:i,getShowLocalePicker:U,getShowFullScreen:f,getShowNotice:$,getUseErrorHandle:d,getLogoWidth:A,getIsMixSidebar:s,getShowSettingButton:a,getShowSetting:T,getShowSearch:I,getUseLockPage:K,loginSelectOk:x,loginSelectRef:X,title:p,t:_o}}});function Ho(e,n,o,i,c,u){const r=b("AppLogo"),l=b("LayoutTrigger"),s=b("LayoutBreadcrumb"),d=b("LayoutMenu"),a=b("AppSearch"),m=b("ErrorAction"),p=b("Notify"),S=b("FullScreen"),f=b("LockScreen"),$=b("AppLocalePicker"),w=b("UserDropDown"),C=b("SettingDrawer"),v=b("Header"),R=b("LoginSelect");return h(),P(Q,null,[k(v,{class:y(e.getHeaderClass)},{default:W(()=>[z("div",{class:y(`${e.prefixCls}-left`)},[e.getShowHeaderLogo||e.getIsMobile?(h(),H(r,{key:0,class:y(`${e.prefixCls}-logo`),theme:e.getHeaderTheme,style:ce(e.getLogoWidth)},null,8,["class","theme","style"])):D("",!0),e.getShowContent&&e.getShowHeaderTrigger&&!e.getSplit&&!e.getIsMixSidebar||e.getIsMobile?(h(),H(l,{key:1,theme:e.getHeaderTheme,sider:!1},null,8,["theme"])):D("",!0),e.getShowContent&&e.getShowBread?(h(),H(s,{key:2,theme:e.getHeaderTheme},null,8,["theme"])):D("",!0),e.getShowContent&&e.getShowBreadTitle&&!e.getIsMobile?(h(),P("span",{key:3,class:y([e.prefixCls,`${e.prefixCls}--${e.getHeaderTheme}`,"headerIntroductionClass"])},te(e.t("layout.header.welcomeIn"))+" "+te(e.title),3)):D("",!0)],2),e.getShowTopMenu&&!e.getIsMobile?(h(),P("div",{key:0,class:y(`${e.prefixCls}-menu`)},[k(d,{isHorizontal:!0,theme:e.getHeaderTheme,splitType:e.getSplitType,menuMode:e.getMenuMode},null,8,["theme","splitType","menuMode"])],2)):D("",!0),z("div",{class:y(`${e.prefixCls}-action`)},[e.getShowSearch?(h(),H(a,{key:0,class:y(`${e.prefixCls}-action__item `)},null,8,["class"])):D("",!0),e.getUseErrorHandle?(h(),H(m,{key:1,class:y(`${e.prefixCls}-action__item error-action`)},null,8,["class"])):D("",!0),e.getShowNotice?(h(),H(p,{key:2,class:y(`${e.prefixCls}-action__item notify-item`)},null,8,["class"])):D("",!0),e.getShowFullScreen?(h(),H(S,{key:3,class:y(`${e.prefixCls}-action__item fullscreen-item`)},null,8,["class"])):D("",!0),e.getUseLockPage?(h(),H(f,{key:4})):D("",!0),e.getShowLocalePicker?(h(),H($,{key:5,reload:!0,showText:!1,class:y(`${e.prefixCls}-action__item`)},null,8,["class"])):D("",!0),k(w,{theme:e.getHeaderTheme},null,8,["theme"]),e.getShowSetting?(h(),H(C,{key:6,class:y(`${e.prefixCls}-action__item`)},null,8,["class"])):D("",!0)],2)]),_:1},8,["class"]),k(R,{ref:"loginSelectRef",onSuccess:e.loginSelectOk},null,8,["onSuccess"])],64)}const Gt=V(Oo,[["render",Ho]]);function Ro(){const e=G(!1),{getMiniWidthNumber:n}=Z(),o=g(()=>t(e)?0:t(n));function i(c){e.value=c}return{getCollapsedWidth:o,onBreakpointChange:i}}function Po(e){const{getTrigger:n,getSplit:o}=Z(),i=g(()=>{const r=t(n);return r!==Re.NONE&&!t(e)&&(r===Re.FOOTER||t(o))}),c=g(()=>t(n)===Re.RIGHT&&!t(e));return{getTriggerAttr:g(()=>{const r=t(n);return t(i)?{}:r===Re.RIGHT?{trigger:null}:{trigger:null}}),getShowTrigger:i,getShowRightTrigger:c}}function Ut(e,n,o=!1){const{getMiniWidthNumber:i,getCollapsed:c,setMenuSetting:u}=Z();Xe(()=>{De(()=>{Ct(d,80)()})});function r(a){var p;const m=t(a);return m?Reflect.has(m,"$el")?(p=t(a))==null?void 0:p.$el:t(a):null}function l(a,m,p){document.onmousemove=function(S){let f=a.left+(S.clientX-p);S=S||window.event;const $=800,w=t(i);return f<0&&(f=0),f>$&&(f=$),f<w&&(f=w),a.style.left=m.style.width=f+"px",!1}}function s(a){const m=r(e);document.onmouseup=function(){var S;document.onmousemove=null,document.onmouseup=null,m.style.transition="width 0.2s";const p=parseInt(m.style.width);if(o)u({menuWidth:p});else{const f=t(i);t(c)?p>f&&u({collapsed:!1,menuWidth:p}):p>f+20?u({menuWidth:p}):u({collapsed:!0})}(S=a.releaseCapture)==null||S.call(a)}}function d(){const a=r(n);if(!a)return;const m=r(e);m&&(a.onmousedown=p=>{var f;m.style.transition="unset";const S=p==null?void 0:p.clientX;return a.left=a.offsetLeft,l(a,m,S),s(a),(f=a.setCapture)==null||f.call(a),!1})}return{}}const Ao=j({name:"DargBar",props:{mobile:Boolean},setup(e){const{getMiniWidthNumber:n,getCollapsed:o,getCanDrag:i}=Z(),{prefixCls:c}=q("darg-bar"),u=g(()=>t(o)?{left:`${t(n)}px`}:{}),r=g(()=>[c,{[`${c}--hide`]:!t(i)||e.mobile}]);return{prefixCls:c,getDragBarStyle:u,getClass:r}}});function Eo(e,n,o,i,c,u){return h(),P("div",{class:y(e.getClass),style:ce(e.getDragBarStyle)},null,6)}const Bo=V(Ao,[["render",Eo],["__scopeId","data-v-8137ab03"]]),No=j({name:"LayoutSideBar",components:{Sider:tt.Sider,LayoutMenu:zt,DragBar:Bo,LayoutTrigger:Fe,SvgIcon:vt},setup(){const e=G(null),n=G(null),{getCollapsed:o,getMenuWidth:i,getSplit:c,getMenuTheme:u,getRealWidth:r,getMenuHidden:l,getMenuFixed:s,getIsMixMode:d,toggleCollapsed:a}=Z(),{prefixCls:m}=q("layout-sideBar"),p=_e(),S=Ue(),{getIsMobile:f}=fe(),{getTriggerAttr:$,getShowTrigger:w,getShowRightTrigger:C}=Po(f);Ut(n,e);const{getCollapsedWidth:v,onBreakpointChange:R}=Ro(),I=g(()=>t(c)?de.INLINE:null),K=g(()=>t(c)?ae.LEFT:ae.NONE),N=g(()=>S.getLayoutHideSider?!1:t(c)?!t(l):!0),U=g(()=>[m,{[`${m}--fixed`]:t(s),[`${m}--mix`]:t(d)&&!t(f),[`${m}--qiankun-micro`]:p.isQiankunMicro}]),L=g(()=>{const T=`${t(r)}px`;return{width:T,overflow:"hidden",flex:`0 0 ${T}`,maxWidth:T,minWidth:T,transition:"all 0.2s"}}),M=Sn(Fe);return{prefixCls:m,sideRef:n,dragBarRef:e,getIsMobile:f,getHiddenDomStyle:L,getSiderClass:U,getTrigger:M,getTriggerAttr:$,getCollapsedWidth:v,getMenuFixed:s,showClassSideBarRef:N,getMenuWidth:i,getCollapsed:o,getMenuTheme:u,onBreakpointChange:R,getMode:I,getSplitType:K,getShowTrigger:w,getShowRightTrigger:C,toggleCollapsed:a}}}),Do={class:"menu-bg"},Fo={class:"right-trigger-outer"},Ko={class:"right-trigger-inner"};function Wo(e,n,o,i,c,u){const r=b("LayoutTrigger"),l=b("LayoutMenu"),s=b("DragBar"),d=b("SvgIcon"),a=b("Sider");return h(),P(Q,null,[e.getMenuFixed&&!e.getIsMobile?me((h(),P("div",{key:0,style:ce(e.getHiddenDomStyle)},null,4)),[[Ce,e.showClassSideBarRef]]):D("",!0),me(k(a,J({ref:"sideRef",breakpoint:"lg",collapsible:"",class:e.getSiderClass,width:e.getMenuWidth,collapsed:e.getCollapsed,collapsedWidth:e.getCollapsedWidth,theme:e.getMenuTheme,onBreakpoint:e.onBreakpointChange,trigger:e.getTrigger},e.getTriggerAttr),_t({default:W(()=>[k(l,{theme:e.getMenuTheme,menuMode:e.getMode,splitType:e.getSplitType},null,8,["theme","menuMode","splitType"]),k(s,{ref:"dragBarRef"},null,512),e.getCollapsed?D("",!0):(h(),P(Q,{key:0},[z("div",Do,[k(d,{name:"menu-logo",size:190})]),n[1]||(n[1]=z("div",{class:"version"},[z("span",{class:"version-num"},"V1.0"),z("span",{class:"version-copyright"},"COPYRIGHT©2024河南灰谷科技有限公司")],-1))],64)),e.getShowRightTrigger?(h(),P("div",{key:1,class:"right-trigger",onClick:n[0]||(n[0]=(...m)=>e.toggleCollapsed&&e.toggleCollapsed(...m))},[z("div",Fo,[z("div",Ko,[k(d,{name:"fold",size:12})])])])):D("",!0)]),_:2},[e.getShowTrigger?{name:"trigger",fn:W(()=>[k(r)]),key:"0"}:void 0]),1040,["class","width","collapsed","collapsedWidth","theme","onBreakpoint","trigger"]),[[Ce,e.showClassSideBarRef]])],64)}const zo=V(No,[["render",Wo]]),Go=j({name:"LayoutMixSider",components:{ScrollContainer:Rt,AppLogo:Je,SimpleMenu:Kt,Icon:Me,LayoutTrigger:Fe,SimpleMenuTag:Wt},directives:{clickOutside:sn},setup(){let e=G([]);const n=G(""),o=G([]),i=G(!1),c=G(null),u=G(null),r=G(null),l=Ue(),s=G(!1),{prefixCls:d}=q("layout-mix-sider"),a=Ve(),{t:m}=ge(),{getMenuWidth:p,getCanDrag:S,getCloseMixSidebarOnChange:f,getMenuTheme:$,getMixSideTrigger:w,getRealWidth:C,getMixSideFixed:v,mixSideHasChildren:R,setMenuSetting:I,getIsMixSidebar:K,getCollapsed:N}=Z(),{shortTitle:U}=_e();Ut(u,c,!0);const L=g(()=>({width:t(i)?`${t(p)-60}px`:0,left:`${t(T)}px`})),M=g(()=>{R.value=t(o).length>0;const F=t(v)&&t(R);return F&&(i.value=!0),F}),T=g(()=>t(N)?an:ln),A=g(()=>{const F=t(M)?t(C):0,ie=`${t(T)+F}px`;return le(ie)}),E=g(()=>{const F=`${t(T)}px`;return le(F)}),B=g(()=>t(v)?{}:{onMouseleave:()=>{_(!0),ve()}}),X=g(()=>t(S));Xe(()=>Y(null,null,function*(){e.value=yield Mt()})),ze(F=>{r.value=F,_(!0),t(f)&&ve()});function le(F){return{width:F,maxWidth:F,minWidth:F,flex:`0 0 ${F}`}}function x(F,ie=!1){return Y(this,null,function*(){const he=yield Pe(F);if(t(n)===F?(ie?t(i)||(i.value=!0):t(i)?ve():i.value=!0,t(i)||_()):(i.value=!0,n.value=F),!he||he.length===0){ie||a(F),o.value=[],ve();return}o.value=he})}function _(F=!1){return Y(this,null,function*(){var he;const ie=(he=r.value)==null?void 0:he.path;if(ie&&(n.value=yield Ne(ie),t(K))){const Qe=t(e).find($e=>$e.path===t(n)),ot=Qe==null?void 0:Qe.path;if(ot){const $e=yield Pe(ot);F&&(o.value=$e,t(v)&&(i.value=$e.length>0)),$e.length===0&&(o.value=[])}}})}function ee(F){a(F)}function ne(){_(!0),ve()}function Te(F){return t(w)==="hover"?{onMouseenter:()=>x(F.path,!0),onClick:()=>Y(null,null,function*(){const ie=yield Pe(F.path);F.path&&(!ie||ie.length===0)&&a(F.path)})}:{onClick:()=>x(F.path)}}function Vt(){I({mixSideFixed:!t(M)})}function ve(){t(M)||(i.value=!1)}return oe(()=>l.getProjectConfig.menuSetting,F=>{s.value=!!(F!=null&&F.isThemeBright)},{immediate:!0,deep:!0}),{t:m,prefixCls:d,menuModules:e,handleModuleClick:x,activePath:n,childrenMenus:o,getShowDragBar:X,handleMenuClick:ee,getMenuStyle:L,handleClickOutside:ne,sideRef:u,dragBarRef:c,shortTitle:U,openMenu:i,getMenuTheme:$,getItemEvents:Te,getMenuEvents:B,getDomStyle:A,handleFixedMenu:Vt,getMixSideFixed:v,getWrapStyle:E,getCollapsed:N,isThemeBright:s}}}),Uo={class:"text"};function Vo(e,n,o,i,c,u){const r=b("AppLogo"),l=b("LayoutTrigger"),s=b("SimpleMenuTag"),d=b("Icon"),a=b("ScrollContainer"),m=b("SimpleMenu"),p=bn("click-outside");return h(),P(Q,null,[z("div",{class:y(`${e.prefixCls}-dom`),style:ce(e.getDomStyle)},null,6),me((h(),P("div",J({style:e.getWrapStyle,class:[e.prefixCls,e.getMenuTheme,{open:e.openMenu,mini:e.getCollapsed,bright:e.isThemeBright}]},e.getMenuEvents),[k(r,{showTitle:!1,class:y(`${e.prefixCls}-logo`)},null,8,["class"]),k(l,{class:y(`${e.prefixCls}-trigger`)},null,8,["class"]),k(a,null,{default:W(()=>[z("ul",{class:y(`${e.prefixCls}-module`)},[(h(!0),P(Q,null,ye(e.menuModules,S=>(h(),P("li",J({class:[`${e.prefixCls}-module__item `,{[`${e.prefixCls}-module__item--active`]:S.path===e.activePath}]},{ref_for:!0},e.getItemEvents(S),{key:S.path}),[k(s,{item:S,collapseParent:"",dot:""},null,8,["item"]),k(d,{class:y(`${e.prefixCls}-module__icon`),size:e.getCollapsed?16:20,icon:S.icon||S.meta&&S.meta.icon},null,8,["class","size","icon"]),z("p",{class:y(`${e.prefixCls}-module__name`)},te(e.t(S.name)),3)],16))),128))],2)]),_:1}),z("div",{class:y(`${e.prefixCls}-menu-list`),ref:"sideRef",style:ce(e.getMenuStyle)},[me(z("div",{class:y([`${e.prefixCls}-menu-list__title`,{show:e.openMenu}])},[z("span",Uo,te(e.shortTitle),1),k(d,{size:16,icon:e.getMixSideFixed?"ri:pushpin-2-fill":"ri:pushpin-2-line",class:"pushpin",onClick:e.handleFixedMenu},null,8,["icon","onClick"])],2),[[Ce,e.openMenu]]),k(a,{class:y(`${e.prefixCls}-menu-list__content`)},{default:W(()=>[k(m,{items:e.childrenMenus,theme:e.getMenuTheme,mixSider:"",onMenuClick:e.handleMenuClick},null,8,["items","theme","onMenuClick"])]),_:1},8,["class"]),me(z("div",{class:y(`${e.prefixCls}-drag-bar`),ref:"dragBarRef"},null,2),[[Ce,e.getShowDragBar&&e.openMenu]])],6)],16)),[[p,e.handleClickOutside]])],64)}const jo=V(Go,[["render",Vo]]),qo=j({name:"SiderWrapper",components:{Sider:zo,Drawer:vn,MixSider:jo},setup(){const{prefixCls:e}=q("layout-sider-wrapper"),{getIsMobile:n}=fe(),{setMenuSetting:o,getCollapsed:i,getMenuWidth:c,getIsMixSidebar:u}=Z();function r(){o({collapsed:!0})}return{prefixCls:e,getIsMobile:n,getCollapsed:i,handleClose:r,getMenuWidth:c,getIsMixSidebar:u}}});function Xo(e,n,o,i,c,u){const r=b("Sider"),l=b("Drawer"),s=b("MixSider");return e.getIsMobile?(h(),H(l,{key:0,placement:"left",class:y(e.prefixCls),width:e.getMenuWidth,getContainer:null,open:!e.getCollapsed,onClose:e.handleClose},{default:W(()=>[k(r)]),_:1},8,["class","width","open","onClose"])):e.getIsMixSidebar?(h(),H(s,{key:1})):(h(),H(r,{key:2}))}const Yo=V(qo,[["render",Xo]]);var se=(e=>(e[e.REFRESH_PAGE=0]="REFRESH_PAGE",e[e.CLOSE_CURRENT=1]="CLOSE_CURRENT",e[e.CLOSE_LEFT=2]="CLOSE_LEFT",e[e.CLOSE_RIGHT=3]="CLOSE_RIGHT",e[e.CLOSE_OTHER=4]="CLOSE_OTHER",e[e.CLOSE_ALL=5]="CLOSE_ALL",e[e.SCALE=6]="SCALE",e))(se||{});function Qo(e,n){const o=ke({current:null,currentIndex:0}),{t:i}=ge(),c=je(),{currentRoute:u}=pe(),{refreshPage:r,closeAll:l,close:s,closeLeft:d,closeOther:a,closeRight:m}=$t(),p=g(()=>t(n)?e.tabItem:t(u)),S=g(()=>{if(!t(p))return;const{meta:w}=t(p),{path:C}=t(u),v=o.current,R=v?v.path===C:!1,I=o.currentIndex,K=!R,N=()=>{var T;return I===0?!0:c.getTabList.filter(E=>{var B;return!((B=E==null?void 0:E.meta)!=null&&B.affix)})[0].path===((T=o.current)==null?void 0:T.path)},U=()=>c.getTabList.length===1?!0:c.getTabList.filter(A=>{var E;return!((E=A==null?void 0:A.meta)!=null&&E.affix)}).length==1,L=I===c.getTabList.length-1&&c.getLastDragEndIndex>=0;return[{icon:"jam:refresh-reverse",event:se.REFRESH_PAGE,text:i("layout.multipleTab.reload"),disabled:K,divider:!0},{icon:"mdi:arrow-left",event:se.CLOSE_LEFT,text:i("layout.multipleTab.closeLeft"),disabled:N(),divider:!1},{icon:"mdi:arrow-right",event:se.CLOSE_RIGHT,text:i("layout.multipleTab.closeRight"),disabled:L,divider:!0},{icon:"material-symbols:arrows-outward",event:se.CLOSE_OTHER,text:i("layout.multipleTab.closeOther"),disabled:U()}]});function f(w){return C=>{if(!w)return;C==null||C.preventDefault();const v=c.getTabList.findIndex(R=>R.path===w.path);o.current=w,o.currentIndex=v}}function $(w){const{event:C}=w;switch(C){case se.REFRESH_PAGE:r();break;case se.CLOSE_CURRENT:s(e.tabItem);break;case se.CLOSE_LEFT:d(o.current);break;case se.CLOSE_RIGHT:m(o.current);break;case se.CLOSE_OTHER:a(o.current);break;case se.CLOSE_ALL:l(o.current);break}}return{getDropMenuList:S,handleMenuEvent:$,handleContextMenu:f}}const Zo=j({name:"TabContent",components:{Dropdown:rn,Icon:Me},props:{tabItem:{type:Object,default:null},isExtra:Boolean},setup(e){const{prefixCls:n}=q("multiple-tabs-content"),{t:o}=ge(),i=Ke(),c=g(()=>{const{tabItem:{meta:f,fullPath:$}={}}=e;let w=i.getPathTitle($);return w||f&&o(f.title)}),u=g(()=>!e.isExtra),r=g(()=>e.tabItem.meta.icon?e.tabItem.meta.icon:e.tabItem.path==="/dashboard/analysis"?"ant-design:home-outlined":"ant-design:code"),l=g(()=>t(u)?["contextmenu"]:["click"]),{getDropMenuList:s,handleMenuEvent:d,handleContextMenu:a}=Qo(e,u);function m(f){e.tabItem&&a(e.tabItem)(f)}const{getTabsTheme:p}=nt(),S=g(()=>t(p)===Se.SMOOTH);return{prefixCls:n,getDropMenuList:s,handleMenuEvent:d,handleContext:m,getTrigger:l,getIsTabs:u,getTitle:c,prefixIconType:r,showPrefixIcon:S}}}),Jo={class:"ml-1"};function xo(e,n,o,i,c,u){const r=b("Icon"),l=b("Dropdown");return h(),H(l,{dropMenuList:e.getDropMenuList,trigger:e.getTrigger,onMenuEvent:e.handleMenuEvent,overlayClassName:e.prefixCls},{default:W(()=>[e.getIsTabs?(h(),P("div",{key:0,class:y(`${e.prefixCls}__info`),onContextmenu:n[0]||(n[0]=(...s)=>e.handleContext&&e.handleContext(...s))},[z("span",Jo,te(e.getTitle),1)],34)):(h(),P("span",{key:1,class:y(`${e.prefixCls}__extra-quick`),onClick:n[1]||(n[1]=(...s)=>e.handleContext&&e.handleContext(...s))},[k(r,{icon:"ion:chevron-down"})],2))]),_:1},8,["dropMenuList","trigger","onMenuEvent","overlayClassName"])}const es=V(Zo,[["render",xo]]),ts=j({name:"FoldButton",components:{Icon:Me},setup(){const{prefixCls:e}=q("multiple-tabs-content"),{getShowMenu:n,setMenuSetting:o}=Z(),{getShowHeader:i,setHeaderSetting:c}=Ye(),u=g(()=>!t(n)&&!t(i)),r=g(()=>t(u)?"codicon:screen-normal":"codicon:screen-full");function l(){const s=t(u);o({show:s,hidden:!s}),c({show:s}),Pn()}return{prefixCls:e,getIcon:r,handleFold:l}}});function ns(e,n,o,i,c,u){const r=b("Icon");return h(),P("span",{class:y(`${e.prefixCls}__extra-fold`),onClick:n[0]||(n[0]=(...l)=>e.handleFold&&e.handleFold(...l))},[k(r,{icon:e.getIcon},null,8,["icon"])],2)}const os=V(ts,[["render",ns]]),ss=j({name:"TabRedo",components:{SvgIcon:vt},setup(){const e=G(!1),{prefixCls:n}=q("multiple-tabs-content"),{refreshPage:o}=$t();function i(){return Y(this,null,function*(){e.value=!0,yield o(),setTimeout(()=>{e.value=!1},1200)})}return{prefixCls:n,handleRedo:i,loading:e}}});function as(e,n,o,i,c,u){const r=b("SvgIcon");return h(),P("span",{class:y(`${e.prefixCls}__extra-redo`),onClick:n[0]||(n[0]=(...l)=>e.handleRedo&&e.handleRedo(...l))},[k(r,{name:"reload-01"})],2)}const is=V(ss,[["render",as]]);function ls(){const e=G([]),n=je(),o=pe();function i(r){const l=[];return r&&r.forEach(s=>{s.meta&&s.meta.affix&&l.push(qe(s))}),l}function c(){const r=i(o.getRoutes());e.value=r;for(const l of r)n.addTab({meta:l.meta,name:l.name,path:l.path})}let u=!1;return u||(c(),u=!0),e.value.map(r=>{var l;return(l=r.meta)==null?void 0:l.title}).filter(Boolean)}function rs(e){const n=je(),{multiTabsSetting:o}=un,{prefixCls:i}=q("multiple-tabs");De(()=>{var r;if(!o.canDrag)return;const c=(r=document.querySelectorAll(`.${i} .ant-tabs-nav > div`))==null?void 0:r[0],{initSortable:u}=An(c,{filter:l=>{var d;const s=(d=l==null?void 0:l.target)==null?void 0:d.innerText;return s?e.includes(s):!1},onEnd:l=>{const{oldIndex:s,newIndex:d}=l;ut(s)||ut(d)||s===d||n.sortTabs(s,d)}});u()})}const us=j({name:"MultipleTabs",components:{TabRedo:is,FoldButton:os,Tabs:ct,TabPane:ct.TabPane,TabContent:es,Aide:Pt},setup(){const e=ls(),n=G("");rs(e);const o=je(),i=Tt(),c=pe(),{prefixCls:u}=q("multiple-tabs"),r=Ve(),{getShowQuick:l,getShowRedo:s,getShowFold:d,getTabsTheme:a}=nt(),m=g(()=>t(a)===Se.TAG),p=g(()=>o.getTabList.filter(C=>{var v;return!((v=C.meta)!=null&&v.hideTab)})),S=g(()=>t(p).length===1),f=g(()=>[u,{[`${u}--hide-close`]:t(S)},`${u}--theme-${t(a)}`]);ze(C=>{const{name:v}=C;if(v===Ge||!C||!i.getToken)return;const{path:R,fullPath:I,meta:K={}}=C,{currentActiveMenu:N,hideTab:U}=K,L=U?N:null,M=L||I||R;if(n.value!==M&&(n.value=M),L){const T=c.getRoutes().find(A=>A.path===N);T&&o.addTab(T)}else o.addTab(t(C))});function $(C){n.value=C,r(C,!1)}function w(C){t(S)||o.closeTabByKey(C,c)}return{prefixCls:u,unClose:S,getWrapClass:f,handleEdit:w,handleChange:$,activeKeyRef:n,getTabsState:p,getShowQuick:l,getShowRedo:s,getShowFold:d,isTagTheme:m}}}),cs={class:"rightExtra"};function ds(e,n,o,i,c,u){const r=b("TabContent"),l=b("TabPane"),s=b("TabRedo"),d=b("a-tooltip"),a=b("router-link"),m=b("Tabs");return h(),P("div",{class:y(e.getWrapClass)},[k(m,{type:"editable-card",size:"small",animated:!1,hideAdd:!0,tabBarGutter:3,activeKey:e.activeKeyRef,onChange:e.handleChange,onEdit:e.handleEdit},_t({default:W(()=>[(h(!0),P(Q,null,ye(e.getTabsState,p=>(h(),H(l,{key:p.query?p.fullPath:p.path,closable:!(p&&p.meta&&p.meta.affix)},{tab:W(()=>[k(r,{tabItem:p},null,8,["tabItem"])]),_:2},1032,["closable"]))),128))]),_:2},[(e.getShowRedo||e.getShowQuick)&&!e.isTagTheme?{name:"rightExtra",fn:W(()=>[z("div",cs,[e.getShowRedo?(h(),H(s,{key:0})):D("",!0),k(a,{to:"/ai",class:"ai-icon"},{default:W(()=>[k(d,{title:"AI助手",placement:"left"},{default:W(()=>n[0]||(n[0]=[z("svg",{t:"1737024931936",class:"icon",viewBox:"0 0 3011 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"4248",width:"256",height:"256"},[z("path",{d:"M2028.242824 558.561882h415.021176V445.620706h-346.352941V321.415529h346.352941V210.823529l-328.463059 10.842353a2367.969882 2367.969882 0 0 0-31.021176-127.09647c341.955765-4.397176 602.352941-13.492706 781.131294-27.286588l41.441882 124.265411-320.933647 14.095059v115.772235h307.802353v124.205177h-307.802353v112.941176h375.506824v127.096471h-375.506824v113.844706c0 30.117647-5.782588 56.982588-17.468235 80.474353a114.447059 114.447059 0 0 1-50.778353 53.187764c-22.287059 11.926588-41.261176 19.154824-56.922353 21.684706-15.36 2.770824-82.522353 5.300706-201.426824 7.529412a5065.065412 5065.065412 0 0 0-31.984941-142.155294c64.632471 4.999529 114.447059 7.529412 149.624471 7.529412 44.574118 0 66.861176-23.190588 66.861176-69.632v-72.463059h-415.081411v-127.096471zM1685.624471 956.717176a1296.865882 1296.865882 0 0 0-27.286589-133.662117 2187.745882 2187.745882 0 0 0 96.978824 3.794823c18.191059 0 32.587294-4.758588 43.248941-14.155294 10.661647-9.697882 17.468235-23.491765 20.239059-41.381647 3.132235-17.889882 6.023529-66.379294 8.432941-145.408 2.590118-79.390118 3.614118-179.621647 3.373177-300.754823h-109.206589c-3.734588 212.389647-24.455529 364.604235-62.102588 456.523294-37.345882 91.919059-77.161412 156.491294-119.567059 193.837176-28.551529-27.888941-59.632941-54.392471-93.123764-79.510588-142.757647 12.830118-265.456941 25.298824-368.037648 37.165176l-15.962352-126.132705c14.095059-0.602353 28.190118-1.385412 42.345411-2.349177V88.003765h377.374118v687.043764c12.227765-1.204706 24.154353-2.349176 35.779765-3.312941 50.838588-95.653647 77.040941-244.555294 78.607058-446.58447h-85.172705V200.944941h87.04c0.301176-47.706353 0.481882-100.412235 0.481882-158.117647h125.168941c0 58.368-0.301176 111.073882-0.963765 158.117647H1957.647059l-9.878588 525.613177c-1.566118 62.464-5.481412 104.688941-11.745883 126.614588-6.324706 22.287059-16.805647 41.441882-31.563294 57.404235-14.757647 16.022588-32.768 27.105882-54.091294 33.430588-21.082353 6.264471-75.896471 10.480941-164.743529 12.649412z m-321.837177-759.567058h-140.227765V327.077647h140.227765V197.150118z m-140.227765 359.604706h140.227765V426.767059h-140.227765v129.867294z m140.227765 229.616941v-129.92753h-140.227765v140.288l140.227765-10.36047zM963.764706 114.326588V843.294118h-155.286588V114.326588H963.764706zM716.679529 843.294118H547.297882l-54.573176-167.032471H227.689412L174.140235 843.294118H5.662118l266.842353-728.96753h182.512941L716.739765 843.294118zM455.981176 556.212706L373.157647 303.585882c-5.300706-16.022588-9.035294-37.165176-11.264-63.548235h-4.216471a269.010824 269.010824 0 0 1-12.709647 61.680941L261.662118 556.212706H455.981176z",fill:"#1F1F1F","p-id":"4249"})],-1)])),_:1,__:[0]})]),_:1})])]),key:"0"}:void 0]),1032,["activeKey","onChange","onEdit"])],2)}const ps=V(us,[["render",ds],["__scopeId","data-v-86015c07"]]),ms=j({name:"SimpleBreadcrumb",components:{[dt.name]:dt},props:{theme:O.oneOf(["dark","light"])},setup(){const e=G([]),{currentRoute:n}=pe(),{prefixCls:o}=q("simple-breadcrumb"),i=Ve(),{t:c}=ge();Ze(()=>Y(null,null,function*(){var C,v,R;if(n.value.name===Ge)return;const d=yield We(),a=n.value.matched,m=a==null?void 0:a[a.length-1];let p=n.value.path;m&&((C=m==null?void 0:m.meta)!=null&&C.currentActiveMenu)&&(p=m.meta.currentActiveMenu);const S=Ie(d,p),f=d.filter(I=>I.path===S[0]),$=u(f,S);if(!$||$.length===0)return;const w=r($);(v=n.value.meta)!=null&&v.currentActiveMenu&&w.push(He(re({},n.value),{name:((R=n.value.meta)==null?void 0:R.title)||n.value.name})),e.value=w}));function u(d,a){const m=[];return d.forEach(p=>{var S,f;a.includes(p.path)&&m.push(He(re({},p),{name:((S=p.meta)==null?void 0:S.title)||p.name})),(f=p.children)!=null&&f.length&&m.push(...u(p.children,a))}),m}function r(d){return cn(d,a=>{const{meta:m,name:p}=a;if(!m)return!!p;const{title:S,hideBreadcrumb:f}=m;return!(!S||f)}).filter(a=>{var m;return!((m=a.meta)!=null&&m.hideBreadcrumb)})}function l(d,a){a==null||a.preventDefault();const{children:m,redirect:p,meta:S}=d;if(m!=null&&m.length&&!p){a==null||a.stopPropagation();return}if(!(S!=null&&S.carryParam))if(p&&dn(p))i(p);else{const f=d.path;i(f)}}function s(d,a){return d.indexOf(a)!==d.length-1}return{routes:e,t:c,prefixCls:o,handleClick:l,hasRedirect:s}}}),gs={key:0};function fs(e,n,o,i,c,u){const r=b("router-link"),l=b("a-breadcrumb");return h(),P("div",{class:y([e.prefixCls,`${e.prefixCls}--${e.theme}`])},[k(l,{routes:e.routes},{itemRender:W(({route:s,routes:d})=>[e.hasRedirect(d,s)?(h(),H(r,{key:1,to:"",onClick:a=>e.handleClick(s,a),style:{color:"inherit"}},{default:W(()=>[It(te(e.t(s.name||s.meta.title)),1)]),_:2},1032,["onClick"])):(h(),P("span",gs,te(e.t(s.name||s.meta.title)),1))]),_:1},8,["routes"])],2)}const hs=V(ms,[["render",fs]]),mt=86,Ss=32,bs=50,Cs=50,Ms=j({name:"LayoutMultipleHeader",components:{LayoutHeader:Gt,MultipleTabs:ps,SimpleBreadcrumb:hs},setup(){const{setHeaderHeight:e}=En(),{prefixCls:n}=q("layout-multiple-header"),o=pe(),i=Ue(),c=_e(),u=G(o.currentRoute.value.path);oe(()=>o.currentRoute.value.path,(T,A)=>{u.value=T,T==="/hgy/visual"?i.setMainAppProps({hideMultiTabs:!0}):A==="/hgy/visual"&&i.setMainAppProps({hideMultiTabs:!1})},{immediate:!0});const{getCalcContentWidth:r,getSplit:l,getMenuType:s}=Z(),{getIsMobile:d}=fe(),{getFixed:a,getShowInsetHeaderRef:m,getShowFullHeaderRef:p,getHeaderTheme:S}=Ye(),{getFullContent:f}=pn(),{getShowMultipleTab:$,getTabsTheme:w}=nt(),C=g(()=>i.getLayoutHideHeader?!1:t(m)),v=g(()=>i.getLayoutHideMultiTabs||t(w)===Se.TAG?!1:t($)&&!t(f)),R=g(()=>i.getLayoutHideMultiTabs?!1:t(w)===Se.TAG&&t($)&&!t(f)),I=g(()=>t(a)||t(p)),K=g(()=>{const T={};return t(a)&&!c.isQiankunMicro&&(T.width=t(d)?"100%":t(r)),t(p)&&(T.top=`${mt}px`),T}),N=g(()=>t(a)||t(p)),U=g(()=>{let T=t(w);return T===Se.CARD?bs:T===Se.SMOOTH?Cs:Ss}),L=g(()=>{let T=0;return((t(p)||!t(l))&&t(C)&&!t(f)||t(s)==Ae.MIX)&&(T+=mt),(t(v)||t(R))&&!t(f)&&(T+=t(U)),e(T),{height:`${T}px`}}),M=g(()=>[n,`${n}--${t(S)}`,{[`${n}--fixed`]:t(N),[`${n}--qiankun-micro`]:c.isQiankunMicro}]);return{glob:c,getClass:M,prefixCls:n,getPlaceholderDomStyle:L,getIsFixed:N,getWrapStyle:K,getIsShowPlaceholderDom:I,getShowTabs:v,getShowHeader:C,getShowBreadcrumbInTag:R,getHeaderTheme:S}}});function ys(e,n,o,i,c,u){const r=b("LayoutHeader"),l=b("MultipleTabs"),s=b("SimpleBreadcrumb");return h(),P(Q,null,[e.getIsShowPlaceholderDom?(h(),P("div",{key:0,style:ce(e.getPlaceholderDomStyle)},null,4)):D("",!0),z("div",{style:ce(e.getWrapStyle),class:y(e.getClass)},[e.getShowHeader?(h(),H(r,{key:0})):D("",!0),e.getShowTabs?(h(),H(l,{key:1})):D("",!0),e.getShowBreadcrumbInTag?(h(),P("div",{key:2,class:y(`${e.prefixCls}-breadcrumb-wrapper`)},[k(s,{theme:e.getHeaderTheme},null,8,["theme"])],2)):D("",!0)],6)],64)}const Ts=V(Ms,[["render",ys],["__scopeId","data-v-5532de8e"]]),vs=j({name:"DefaultLayout",components:{LayoutFeatures:we(()=>Le(()=>import("./index-Dn0aDJhb.js"),__vite__mapDeps([19,3,1,2,4,5,17,20,21,22,23,24,25,26,27,28,29,30,13,8,9,31,32,10,11,12,14,15,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54]))),LayoutFooter:we(()=>Le(()=>import("./index-D1H52R0n.js"),__vite__mapDeps([55,1,2,56,3,4,5,57,58,32,59]))),LayoutHeader:Gt,LayoutContent:Rn,LayoutSideBar:Yo,LayoutMultipleHeader:Ts,Layout:tt},setup(){const{prefixCls:e}=q("default-layout"),{getIsMobile:n}=fe(),{getShowFullHeaderRef:o}=Ye(),{getShowSidebar:i,getIsMixSidebar:c,getShowMenu:u}=Z(),r=Bn(),l=g(()=>{let s=["ant-layout"];return(t(c)||t(u))&&s.push("ant-layout-has-sider"),s});return{getShowFullHeaderRef:o,getShowSidebar:i,prefixCls:e,getIsMobile:n,getIsMixSidebar:c,layoutClass:l,lockEvents:r}}});function $s(e,n,o,i,c,u){const r=b("LayoutFeatures"),l=b("LayoutHeader"),s=b("LayoutSideBar"),d=b("LayoutMultipleHeader"),a=b("LayoutContent"),m=b("LayoutFooter"),p=b("Layout");return h(),H(p,J({class:e.prefixCls},e.lockEvents),{default:W(()=>[k(r),e.getShowFullHeaderRef?(h(),H(l,{key:0,fixed:""})):D("",!0),k(p,{class:y([e.layoutClass])},{default:W(()=>[e.getShowSidebar||e.getIsMobile?(h(),H(s,{key:0})):D("",!0),k(p,{class:y(`${e.prefixCls}-main`)},{default:W(()=>[k(d),k(a),k(m)]),_:1},8,["class"])]),_:1},8,["class"])]),_:1},16,["class"])}const xs=V(vs,[["render",$s]]);export{xs as default};
