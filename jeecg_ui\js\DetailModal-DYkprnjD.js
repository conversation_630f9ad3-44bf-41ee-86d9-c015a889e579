import{d as i,aB as s,ar as m,aD as p,k as n,u as t,aE as c}from"./vue-vendor-dy9k-Yad.js";import{B as l}from"./index-Diw57m_E.js";import{u,D as f}from"./index-Dce_QJ6p.js";import{N as d}from"./index-CCWaWN5g.js";import{getDescSchema as D}from"./data-WLa0JRnp.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const O=i({__name:"DetailModal",props:{info:{type:Object,default:null}},setup(o){const{t:r}=d(),[e]=u({column:2,schema:D()});return(a,g)=>(m(),s(t(l),c({width:800,title:t(r)("sys.errorLog.tableActionDesc")},a.$attrs),{default:p(()=>[n(t(f),{data:o.info,onRegister:t(e)},null,8,["data","onRegister"])]),_:1},16,["title"]))}});export{O as default};
