import{d as v,ag as u,aq as $,ar as M,at as c,k as i,aD as n,G as r}from"./vue-vendor-dy9k-Yad.js";import{u as N}from"./index-BkGZ5fiW.js";import{getBasicShortColumns as V,getBasicColumns as f}from"./tableData-B4J3mkj4.js";import{u as I,a as q}from"./index-CCWaWN5g.js";import{d as z}from"./table-BDFKJhHv.js";import A from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const E=v({components:{BasicTable:A},setup(){const{createMessage:t}=I();function o(){}const[a,{setLoading:s,setColumns:l,getColumns:d,getDataSource:e,getRawDataSource:m,reload:p,getPaginationRef:F,setPagination:g,getSelectRows:J,getSelectRowKeys:O,setSelectedRowKeys:C,clearSelectedRowKeys:k}]=N({canResize:!0,title:"useTable示例",titleHelpMessage:"使用useTable调用表格内方法",api:z,columns:f(),defSort:{field:"name",order:"ascend"},rowKey:"id",showTableSetting:!0,onChange:o,rowSelection:{type:"checkbox"},onColumnsChange:Q=>{}});function b(){s(!0),setTimeout(()=>{s(!1)},1e3)}function S(){l(V())}function T(){l(f()),p({page:1})}function _(){t.info("请在控制台查看！")}function w(){t.info("请在控制台查看！")}function R(){t.info("请在控制台查看！")}function y(){t.info("请在控制台查看！")}function K(){g({current:2}),p()}function L(){t.info("请在控制台查看！")}function B(){t.info("请在控制台查看！")}function D(){C(["0","1","2"])}function P(){k()}return{registerTable:a,changeLoading:b,changeColumns:S,reloadTable:T,getColumn:_,getTableData:w,getTableRawData:R,getPagination:y,setPaginationInfo:K,getSelectRowList:L,getSelectRowKeyList:B,setSelectedRowKeyList:D,clearSelect:P,onChange:o}}}),G={class:"p-4"},H={class:"mb-4"},U={class:"mb-4"};function j(t,o,a,s,l,d){const e=u("a-button"),m=u("BasicTable");return M(),$("div",G,[c("div",H,[i(e,{class:"mr-2",onClick:t.reloadTable},{default:n(()=>o[0]||(o[0]=[r(" 还原 ")])),_:1,__:[0]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.changeLoading},{default:n(()=>o[1]||(o[1]=[r(" 开启loading ")])),_:1,__:[1]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.changeColumns},{default:n(()=>o[2]||(o[2]=[r(" 更改Columns ")])),_:1,__:[2]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.getColumn},{default:n(()=>o[3]||(o[3]=[r(" 获取Columns ")])),_:1,__:[3]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.getTableData},{default:n(()=>o[4]||(o[4]=[r(" 获取表格数据 ")])),_:1,__:[4]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.getTableRawData},{default:n(()=>o[5]||(o[5]=[r(" 获取接口原始数据 ")])),_:1,__:[5]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.setPaginationInfo},{default:n(()=>o[6]||(o[6]=[r(" 跳转到第2页 ")])),_:1,__:[6]},8,["onClick"])]),c("div",U,[i(e,{class:"mr-2",onClick:t.getSelectRowList},{default:n(()=>o[7]||(o[7]=[r(" 获取选中行 ")])),_:1,__:[7]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.getSelectRowKeyList},{default:n(()=>o[8]||(o[8]=[r(" 获取选中行Key ")])),_:1,__:[8]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.setSelectedRowKeyList},{default:n(()=>o[9]||(o[9]=[r(" 设置选中行 ")])),_:1,__:[9]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.clearSelect},{default:n(()=>o[10]||(o[10]=[r(" 清空选中行 ")])),_:1,__:[10]},8,["onClick"]),i(e,{class:"mr-2",onClick:t.getPagination},{default:n(()=>o[11]||(o[11]=[r(" 获取分页信息 ")])),_:1,__:[11]},8,["onClick"])]),i(m,{onRegister:t.registerTable},null,8,["onRegister"])])}const ot=q(E,[["render",j]]);export{ot as default};
