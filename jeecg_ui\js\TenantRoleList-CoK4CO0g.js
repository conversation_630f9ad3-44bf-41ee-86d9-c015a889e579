var j=Object.defineProperty,z=Object.defineProperties;var J=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var O=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable;var k=(r,t,o)=>t in r?j(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o,T=(r,t)=>{for(var o in t||(t={}))O.call(t,o)&&k(r,o,t[o]);if(y)for(var o of y(t))W.call(t,o)&&k(r,o,t[o]);return r},U=(r,t)=>z(r,J(t));var f=(r,t,o)=>new Promise((g,c)=>{var u=i=>{try{d(o.next(i))}catch(p){c(p)}},D=i=>{try{d(o.throw(i))}catch(p){c(p)}},d=i=>i.done?g(i.value):Promise.resolve(i.value).then(u,D);d((o=o.apply(r,t)).next())});import{d as F,f as S,o as Y,ag as l,aq as Z,ar as N,k as n,aD as m,u as s,aB as tt,ah as ot,at as B,G as _,au as et,F as rt}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{a as b}from"./index-JbqXEynz.js";import"./index-Diw57m_E.js";import nt from"./RoleDesc-CjhDo5X3.js";import it from"./RoleDrawer-BvP-LLM1.js";import at from"./RoleUserTable-C2HEMOJR.js";import{s as mt,c as st}from"./role.data-BepCB_2a.js";import{b as pt,g as lt,a as ct,l as ut,d as dt}from"./role.api-BvRyEQIC.js";import{useListPage as ft}from"./useListPage-Soxgnx9a.js";import{g as _t}from"./tenant.api-CTNrRQ_d.js";import{ad as gt,ce as wt,a as bt}from"./index-CCWaWN5g.js";import{Q as Dt}from"./componentMap-Bkie1n3v.js";import Ct from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./index-Dce_QJ6p.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./UserDrawer-xnPiWN1B.js";import"./user.data-CLRTqTDz.js";import"./user.api-mLAlJze4.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";import"./useAdaptiveWidth-SDQVNQ1K.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./UseSelectModal-B88seyfc.js";import"./injectionKey-DPVn4AgL.js";const Rt={style:{"margin-left":"10px","margin-top":"5px"}},ht={class:"tenant-name"},vt=F({name:"tenant-role-list"}),xt=F(U(T({},vt),{setup(r){const t=S(!0),[o,{openDrawer:g}]=b(),[c,{openDrawer:u}]=b(),[D,{openModal:d}]=gt(),[i,{openDrawer:p}]=b(),{prefixCls:yt,tableContext:I,onImportXls:kt,onExportXls:Tt}=ft({designScope:"role-template",tableProps:{title:"租户角色列表",api:ut,columns:st,formConfig:{schemas:mt},actionColumn:{width:120},rowSelection:null,defSort:{column:"id",order:"desc"}},exportConfig:{name:"角色列表",url:ct},importConfig:{url:lt}}),[E,{reload:w},{rowSelection:Ut,selectedRowKeys:C}]=I;function M(){t.value=!0,u(!0,{isUpdate:!1})}function A(e){t.value=!0,u(!0,{record:e,isUpdate:!0})}function V(e){t.value=!1,p(!0,{record:e,isUpdate:!0})}function L(e){return f(this,null,function*(){yield dt({id:e.id},w)})}function K(){return f(this,null,function*(){yield pt({ids:C.value},w)})}function P(e){g(!0,e)}function X(e){return[{label:"用户",onClick:P.bind(null,e)}]}function $(e){return[{label:"编辑",onClick:A.bind(null,e)},{label:"详情",onClick:V.bind(null,e)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:L.bind(null,e)}}]}const R=S("");q();function q(){return f(this,null,function*(){R.value=yield _t()})}return Y(()=>{wt("租户角色")}),(e,a)=>{const h=l("a-button"),v=l("Icon"),G=l("a-menu-item"),H=l("a-menu"),Q=l("a-dropdown");return N(),Z(rt,null,[n(s(Ct),{onRegister:s(E)},{tableTitle:m(()=>[n(h,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:M},{default:m(()=>a[0]||(a[0]=[_(" 新增")])),_:1,__:[0]}),s(C).length>0?(N(),tt(Q,{key:0},{overlay:m(()=>[n(H,null,{default:m(()=>[n(G,{key:"1",onClick:K},{default:m(()=>[n(v,{icon:"ant-design:delete-outlined"}),a[1]||(a[1]=_(" 删除 "))]),_:1,__:[1]})]),_:1})]),default:m(()=>[n(h,null,{default:m(()=>[a[2]||(a[2]=_("批量操作 ")),n(v,{icon:"mdi:chevron-down"})]),_:1,__:[2]})]),_:1})):ot("",!0),B("div",Rt,[a[3]||(a[3]=_("当前登录租户: ")),B("span",ht,et(R.value),1)])]),action:m(({record:x})=>[n(s(Dt),{actions:X(x),dropDownActions:$(x)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister"]),n(at,{onRegister:s(o),disableUserEdit:!0},null,8,["onRegister"]),n(it,{onRegister:s(c),onSuccess:s(w),showFooter:t.value},null,8,["onRegister","onSuccess","showFooter"]),n(nt,{onRegister:s(i)},null,8,["onRegister"])],64)}}})),Oo=bt(xt,[["__scopeId","data-v-bfe85526"]]);export{Oo as default};
