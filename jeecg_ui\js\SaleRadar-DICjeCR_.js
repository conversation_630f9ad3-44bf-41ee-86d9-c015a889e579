import{d as r,f as s,w as i,ag as n,aB as d,ar as l,aD as m,at as p,aA as f}from"./vue-vendor-dy9k-Yad.js";import{J as c}from"./antd-vue-vendor-me9YkNVC.js";import{useECharts as h}from"./useECharts-BU6FzBZi.js";import{a as u}from"./index-CCWaWN5g.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const g=r({components:{Card:c},props:{loading:Boolean,width:{type:String,default:"100%"},height:{type:String,default:"400px"}},setup(e){const a=s(null),{setOptions:t}=h(a);return i(()=>e.loading,()=>{e.loading||t({legend:{bottom:0,data:["Visits","Sales"]},tooltip:{},radar:{radius:"60%",splitNumber:8,indicator:[{name:"2017"},{name:"2017"},{name:"2018"},{name:"2019"},{name:"2020"},{name:"2021"}]},series:[{type:"radar",symbolSize:0,areaStyle:{shadowBlur:0,shadowColor:"rgba(0,0,0,.2)",shadowOffsetX:0,shadowOffsetY:10,opacity:1},data:[{value:[90,50,86,40,50,20],name:"Visits",itemStyle:{color:"#9f8ed7"}},{value:[70,75,70,76,20,85],name:"Sales",itemStyle:{color:"#1edec5"}}]}]})},{immediate:!0}),{chartRef:a}}});function w(e,a,t,y,S,C){const o=n("Card");return l(),d(o,{title:"销售统计",loading:e.loading},{default:m(()=>[p("div",{ref:"chartRef",style:f({width:e.width,height:e.height})},null,4)]),_:1},8,["loading"])}const k=u(g,[["render",w]]);export{k as default};
