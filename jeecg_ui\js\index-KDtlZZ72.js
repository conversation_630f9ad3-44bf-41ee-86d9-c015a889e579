import{d as D,s as A,f as b,n as V,ag as n,aB as c,ar as _,aD as i,k as e,G as a,aQ as P}from"./vue-vendor-dy9k-Yad.js";import{a8 as B,A as W}from"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import R from"./Modal1-q2FSoRmo.js";import w from"./Modal2-BusMlahn.js";import $ from"./Modal3-DpsxFdHm.js";import T from"./Modal4-BKGI7RZL.js";import{P as I}from"./index-CtJ0w2CP.js";import{ad as u,a as L}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const N=D({components:{Alert:W,Modal1:R,Modal2:w,Modal3:$,Modal4:T,PageWrapper:I,ASpace:B},setup(){const t=A(null),[o,{openModal:f}]=u(),[k,{openModal:C}]=u(),[v,{openModal:p}]=u(),[r,{openModal:m}]=u(),l=b(!1),d=b(null);function g(){m(!0,{data:"content",info:"Info"})}function M(){f(!0)}function y(s){switch(s){case 1:t.value=R;break;case 2:t.value=w;break;case 3:t.value=$;break;default:t.value=T;break}V(()=>{d.value={data:Math.random(),info:"Info222"},l.value=!0})}return{register1:o,openModal1:f,register2:k,openModal2:C,register3:v,openModal3:p,register4:r,openModal4:m,modalVisible:l,userData:d,openTargetModal:y,send:g,currentModal:t,openModalLoading:M}}});function S(t,o,f,k,C,v){const p=n("Alert"),r=n("a-button"),m=n("a-space"),l=n("Modal1"),d=n("Modal2"),g=n("Modal3"),M=n("Modal4"),y=n("PageWrapper");return _(),c(y,{title:"modal组件使用示例"},{default:i(()=>[e(p,{message:`使用 useModal 进行弹窗操作，默认可以拖动，可以通过 draggable
    参数进行控制是否可以拖动/全屏，并演示了在Modal内动态加载内容并自动调整高度`,"show-icon":""}),e(r,{type:"primary",class:"my-4",onClick:t.openModalLoading},{default:i(()=>o[5]||(o[5]=[a(" 打开弹窗,加载动态数据并自动调整高度(默认可以拖动/全屏) ")])),_:1,__:[5]},8,["onClick"]),e(p,{message:"内外同时同时显示隐藏","show-icon":""}),e(r,{type:"primary",class:"my-4",onClick:t.openModal2},{default:i(()=>o[6]||(o[6]=[a(" 打开弹窗")])),_:1,__:[6]},8,["onClick"]),e(p,{message:"自适应高度","show-icon":""}),e(r,{type:"primary",class:"my-4",onClick:t.openModal3},{default:i(()=>o[7]||(o[7]=[a(" 打开弹窗")])),_:1,__:[7]},8,["onClick"]),e(p,{message:"内外数据交互","show-icon":""}),e(r,{type:"primary",class:"my-4",onClick:t.send},{default:i(()=>o[8]||(o[8]=[a(" 打开弹窗并传递数据")])),_:1,__:[8]},8,["onClick"]),e(p,{message:"使用动态组件的方式在页面内使用多个弹窗","show-icon":""}),e(m,null,{default:i(()=>[e(r,{type:"primary",class:"my-4",onClick:o[0]||(o[0]=s=>t.openTargetModal(1))},{default:i(()=>o[9]||(o[9]=[a(" 打开弹窗1")])),_:1,__:[9]}),e(r,{type:"primary",class:"my-4",onClick:o[1]||(o[1]=s=>t.openTargetModal(2))},{default:i(()=>o[10]||(o[10]=[a(" 打开弹窗2")])),_:1,__:[10]}),e(r,{type:"primary",class:"my-4",onClick:o[2]||(o[2]=s=>t.openTargetModal(3))},{default:i(()=>o[11]||(o[11]=[a(" 打开弹窗3")])),_:1,__:[11]}),e(r,{type:"primary",class:"my-4",onClick:o[3]||(o[3]=s=>t.openTargetModal(4))},{default:i(()=>o[12]||(o[12]=[a(" 打开弹窗4")])),_:1,__:[12]})]),_:1}),(_(),c(P(t.currentModal),{visible:t.modalVisible,"onUpdate:visible":o[4]||(o[4]=s=>t.modalVisible=s),userData:t.userData},null,40,["visible","userData"])),e(l,{onRegister:t.register1,minHeight:100},null,8,["onRegister"]),e(d,{onRegister:t.register2},null,8,["onRegister"]),e(g,{onRegister:t.register3},null,8,["onRegister"]),e(M,{onRegister:t.register4},null,8,["onRegister"])]),_:1})}const Ko=L(N,[["render",S]]);export{Ko as default};
