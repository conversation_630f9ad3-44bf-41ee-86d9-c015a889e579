import{d as w,aB as R,ar as B,aD as g,k as n,u as t,at as o,G as D,l as m}from"./vue-vendor-dy9k-Yad.js";import{X as q}from"./antd-vue-vendor-me9YkNVC.js";import{P as T}from"./index-CtJ0w2CP.js";import{u as r,D as l}from"./index-Dce_QJ6p.js";import{G as v,S as O,D as z}from"./siteSetting-DoyCDlSB.js";import"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";var S={pkg:{dependencies:{"@ant-design/colors":"^7.2.0","@ant-design/icons-vue":"^7.0.1","@iconify/iconify":"^3.1.1","@jeecg/aiflow":"1.0.0","@jeecg/online":"3.7.4-beta","@logicflow/core":"^2.0.10","@logicflow/extension":"^2.0.14","@logicflow/vue-node-registry":"^1.0.12","@tinymce/tinymce-vue":"4.0.7","@tiptap/extension-color":"^3.0.7","@tiptap/extension-font-family":"^3.0.7","@tiptap/extension-highlight":"^3.0.7","@tiptap/extension-image":"^3.0.7","@tiptap/extension-link":"^3.0.7","@tiptap/extension-subscript":"^3.0.7","@tiptap/extension-superscript":"^3.0.7","@tiptap/extension-table":"^3.0.7","@tiptap/extension-table-cell":"^3.0.7","@tiptap/extension-table-header":"^3.0.7","@tiptap/extension-table-row":"^3.0.7","@tiptap/extension-text-align":"^3.0.7","@tiptap/extension-text-style":"^3.0.7","@tiptap/extension-underline":"^3.0.7","@tiptap/starter-kit":"^3.0.7","@tiptap/vue-3":"^3.0.7","@traptitech/markdown-it-katex":"^3.6.0","@types/pdfjs-dist":"^2.10.377","@vant/area-data":"^1.5.2","@vue/shared":"^3.5.13","@vueuse/core":"^10.11.1","@zxcvbn-ts/core":"^3.0.4","ant-design-vue":"^4.2.6",axios:"^1.7.9","china-area-data":"^5.0.1",clipboard:"^2.0.11",codemirror:"^5.65.18","cron-parser":"^4.9.0",cropperjs:"^1.6.2","crypto-js":"^4.2.0",dayjs:"^1.11.13","docx-preview":"^0.3.6","dom-align":"^1.12.4",echarts:"^5.6.0","emoji-mart-vue-fast":"^15.0.3","enquire.js":"^2.1.6","event-source-polyfill":"^1.0.31","highlight.js":"^11.11.1","intro.js":"^7.2.0","lodash-es":"^4.17.21","lodash.get":"^4.4.2",mammoth:"^1.9.1","markdown-it":"^14.1.0","markdown-it-link-attributes":"^4.0.1",md5:"^2.3.0",mockjs:"^1.1.0",nprogress:"^0.2.0","path-to-regexp":"^6.3.0","pdfjs-dist":"^5.4.54",pinia:"2.1.7","print-js":"^1.6.0",qrcode:"^1.5.4",qs:"^6.13.1","resize-observer-polyfill":"^1.5.1",showdown:"^2.1.0",sortablejs:"^1.15.6","swagger-ui-dist":"^5.21.0",tinymce:"6.6.2",vditor:"^3.10.8",vue:"^3.5.13","vue-cropper":"^0.6.5","vue-cropperjs":"^5.0.0","vue-i18n":"^9.14.2","vue-infinite-scroll":"^2.0.2","vue-print-nb-jeecg":"^1.0.12","vue-router":"^4.5.0","vue-types":"^5.1.3",vuedraggable:"^4.1.0","vxe-table":"4.6.17","vxe-table-plugin-antd":"4.0.7","xe-utils":"3.5.26",xlsx:"^0.18.5",xss:"^1.0.15"},devDependencies:{"@commitlint/cli":"^18.6.1","@commitlint/config-conventional":"^18.6.3","@iconify/json":"^2.2.292","@purge-icons/generated":"^0.10.0","@rys-fe/vite-plugin-theme":"^0.8.6","@types/codemirror":"^5.60.15","@types/crypto-js":"^4.2.2","@types/fs-extra":"^11.0.4","@types/inquirer":"^9.0.7","@types/intro.js":"^5.1.5","@types/jest":"^29.5.14","@types/lodash-es":"^4.17.12","@types/mockjs":"^1.0.10","@types/node":"^20.17.12","@types/nprogress":"^0.2.3","@types/qrcode":"^1.5.5","@types/qs":"^6.9.17","@types/showdown":"^2.0.6","@types/sortablejs":"^1.15.8","@typescript-eslint/eslint-plugin":"^6.21.0","@typescript-eslint/parser":"^6.21.0","@vitejs/plugin-vue":"^5.2.1","@vitejs/plugin-vue-jsx":"^4.1.1","@vue/compiler-sfc":"^3.5.13","@vue/test-utils":"^2.4.6",autoprefixer:"^10.4.20","big.js":"^6.2.2",commitizen:"^4.3.1","conventional-changelog-cli":"^4.1.0","cross-env":"^7.0.3","cz-git":"^1.11.0",czg:"^1.11.0","dingtalk-jsapi":"^3.0.42",dotenv:"^16.4.7",eslint:"^8.57.1","eslint-config-prettier":"^9.1.0","eslint-define-config":"^2.1.0","eslint-plugin-jest":"^27.9.0","eslint-plugin-prettier":"^5.2.1","eslint-plugin-vue":"^9.32.0",esno:"^4.8.0","fs-extra":"^11.2.0","http-server":"^14.1.1",husky:"^8.0.3",inquirer:"^9.3.7","is-ci":"^3.0.1",jest:"^29.7.0",less:"^4.2.1","lint-staged":"15.2.2","npm-run-all":"^4.1.5",picocolors:"^1.1.1",postcss:"^8.4.49","postcss-html":"^1.7.0","postcss-less":"^6.0.0",prettier:"^3.4.2","pretty-quick":"^4.0.0",rimraf:"^5.0.10",rollup:"^4.30.0","rollup-plugin-visualizer":"^5.13.1","sass-embedded":"^1.89.2",stylelint:"^16.12.0","stylelint-config-prettier":"^9.0.5","stylelint-config-recommended":"^14.0.1","stylelint-config-recommended-vue":"^1.5.0","stylelint-config-standard":"^36.0.1","stylelint-order":"^6.0.4","ts-jest":"^29.2.5","ts-node":"^10.9.2",typescript:"^4.9.5",unocss:"^0.58.9",vite:"^6.0.7","vite-plugin-compression":"^0.5.1","vite-plugin-html":"^3.2.2","vite-plugin-mkcert":"^1.17.6","vite-plugin-mock":"^2.9.8","vite-plugin-optimize-persist":"^0.1.2","vite-plugin-package-config":"^0.1.1","vite-plugin-purge-icons":"^0.10.0","vite-plugin-qiankun":"^1.0.15","vite-plugin-svg-icons":"^2.0.1","vite-plugin-vue-setup-extend-plus":"^0.1.0","vue-eslint-parser":"^9.4.3","vue-tsc":"^1.8.27"},name:"jeecgboot-vue3",version:"3.8.0"},lastBuildTime:"2025-08-11 15:37:22"};const C={class:"flex justify-between items-center"},G={class:"flex-1"},E=["href"],ie=w({__name:"index",setup(P){const{pkg:f,lastBuildTime:y}=S,{dependencies:a,devDependencies:p,name:J,version:h}=f,c=[],u=[],d=e=>i=>m(q,{color:e},()=>i),s=e=>i=>m("a",{href:i,target:"_blank"},e),x=[{label:"版本",field:"version",render:d("blue")},{label:"最后编译时间",field:"lastBuildTime",render:d("blue")},{label:"文档地址",field:"doc",render:s("文档地址")},{label:"预览地址",field:"preview",render:s("预览地址")},{label:"Github",field:"github",render:s("Github")}],b={version:h,lastBuildTime:y,doc:z,preview:O,github:v};Object.keys(a).forEach(e=>{c.push({field:e,label:e})}),Object.keys(p).forEach(e=>{u.push({field:e,label:e})});const[j]=r({title:"生产环境依赖",data:a,schema:c,column:3}),[k]=r({title:"开发环境依赖",data:p,schema:u,column:3}),[_]=r({title:"项目信息",data:b,schema:x,column:2});return(e,i)=>(B(),R(t(T),{title:"关于"},{headerContent:g(()=>[o("div",C,[o("span",G,[o("a",{href:t(v),target:"_blank"}," JeecgBoot ",8,E),i[0]||(i[0]=D(" 是一款基于BPM的低代码平台！前后端分离架构 SpringBoot 2.x，SpringCloud，Ant Design&Vue，Mybatis-plus，Shiro，JWT，支持微服务。强大的代码生成器让前后端代码一键生成，实现低代码开发！ JeecgBoot引领新低代码开发模式 OnlineCoding-> 代码生成器-> 手工MERGE， 帮助Java项目解决70%的重复工作，让开发更多关注业务，既能快速提高效率，节省研发成本，同时又不失灵活性！一系列低代码能力：Online表单、Online报表、Online图表、表单设计、流程设计、报表设计、大屏设计 等等...。 "))])])]),default:g(()=>[n(t(l),{onRegister:t(_),class:"enter-y"},null,8,["onRegister"]),n(t(l),{onRegister:t(j),class:"my-4 enter-y"},null,8,["onRegister"]),n(t(l),{onRegister:t(k),class:"enter-y"},null,8,["onRegister"])]),_:1}))}});export{ie as default};
