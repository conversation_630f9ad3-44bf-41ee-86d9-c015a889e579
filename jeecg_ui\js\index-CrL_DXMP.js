import{d as s,ag as a,aB as n,ar as i,aD as c,at as t,k as o}from"./vue-vendor-dy9k-Yad.js";import{S as d}from"./index-CBCjSSNZ.js";import{P as l}from"./index-CtJ0w2CP.js";import{a as m}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const _=s({components:{StrengthMeter:d,PageWrapper:l}}),f={class:"flex justify-center"},u={class:"demo-wrap p-10"};function h(g,r,x,v,w,B){const e=a("StrengthMeter"),p=a("PageWrapper");return i(),n(p,{title:"密码强度校验组件"},{default:c(()=>[t("div",f,[t("div",u,[o(e,{placeholder:"默认"}),o(e,{placeholder:"禁用",disabled:""}),r[0]||(r[0]=t("br",null,null,-1)),o(e,{placeholder:"隐藏input","show-input":!1,value:"!@#qwe12345"})])])]),_:1})}const q=m(_,[["render",h],["__scopeId","data-v-6ac86eeb"]]);export{q as default};
