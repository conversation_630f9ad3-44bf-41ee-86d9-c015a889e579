var X=Object.defineProperty,q=Object.defineProperties;var z=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var G=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable;var D=(e,o,t)=>o in e?X(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,S=(e,o)=>{for(var t in o||(o={}))G.call(o,t)&&D(e,t,o[t]);if(R)for(var t of R(o))H.call(o,t)&&D(e,t,o[t]);return e},h=(e,o)=>q(e,z(o));var w=(e,o,t)=>new Promise((f,d)=>{var g=p=>{try{u(t.next(p))}catch(c){d(c)}},C=p=>{try{u(t.throw(p))}catch(c){d(c)}},u=p=>p.done?f(p.value):Promise.resolve(p.value).then(g,C);u((t=t.apply(e,o)).next())});import{d as A,ag as s,aq as Q,ar as I,k as r,aD as l,u as a,aB as $,ah as J,G as k,at as v,as as O}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as W}from"./useListPage-Soxgnx9a.js";import{_ as Y,b as Z,i as tt,e as ot,s as et,c as nt,g as it,d as rt,h as lt}from"./FillRuleModal-Bw9tU-2D.js";import"./index-Diw57m_E.js";import{ad as at}from"./index-CCWaWN5g.js";import{Q as pt}from"./componentMap-Bkie1n3v.js";import mt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./user.api-mLAlJze4.js";const st=A({name:"system-fillrule"}),wo=A(h(S({},st),{setup(e){const[o,{openModal:t}]=at(),{prefixCls:f,tableContext:d,createMessage:g,createSuccessModal:C,onExportXls:u,onImportXls:p}=W({designScope:"fill-rule",tableProps:{title:"填值规则管理页面",api:it,columns:nt,showIndexColumn:!0,formConfig:{schemas:et}},exportConfig:{url:ot,name:"填值规则列表"},importConfig:{url:tt,success:()=>_()}}),[c,{reload:_},{rowSelection:B,selectedRowKeys:b}]=d;function M(){t(!0,{isUpdate:!1})}function T(i){t(!0,{record:i,isUpdate:!0})}function F(i){return w(this,null,function*(){yield rt({id:i.id},_)})}function N(){return w(this,null,function*(){yield Z({ids:b.value},()=>{b.value=[],_()})})}function U(i){let n={ruleCode:i.ruleCode};lt(n).then(m=>{m.success?C({title:"填值规则功能测试",content:"生成结果："+m.result}):g.warn(m.message)})}function V(i){return[{label:"编辑",onClick:T.bind(null,i)}]}function E(i){return[{label:"功能测试",onClick:U.bind(null,i)},{label:"删除",color:"error",popConfirm:{title:"确认要删除吗？",confirm:F.bind(null,i)}}]}return(i,n)=>{const m=s("a-button"),j=s("j-upload-button"),y=s("Icon"),K=s("a-menu-item"),L=s("a-menu"),P=s("a-dropdown");return I(),Q("div",{class:O(a(f))},[r(a(mt),{onRegister:a(c),rowSelection:a(B)},{tableTitle:l(()=>[r(m,{preIcon:"ant-design:plus-outlined",type:"primary",onClick:M},{default:l(()=>n[0]||(n[0]=[k("新增")])),_:1,__:[0]}),r(m,{type:"primary",preIcon:"ant-design:export-outlined",onClick:a(u)},{default:l(()=>n[1]||(n[1]=[k(" 导出")])),_:1,__:[1]},8,["onClick"]),r(j,{type:"primary",preIcon:"ant-design:import-outlined",onClick:a(p)},{default:l(()=>n[2]||(n[2]=[k("导入")])),_:1,__:[2]},8,["onClick"]),a(b).length>0?(I(),$(P,{key:0},{overlay:l(()=>[r(L,null,{default:l(()=>[r(K,{key:"1",onClick:N},{default:l(()=>[r(y,{icon:"ant-design:delete-outlined"}),n[3]||(n[3]=v("span",null,"删除",-1))]),_:1,__:[3]})]),_:1})]),default:l(()=>[r(m,null,{default:l(()=>[n[4]||(n[4]=v("span",null,"批量操作",-1)),r(y,{icon:"mdi:chevron-down"})]),_:1,__:[4]})]),_:1})):J("",!0)]),action:l(({record:x})=>[r(a(pt),{actions:V(x),dropDownActions:E(x)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),r(Y,{onRegister:a(o),onSuccess:a(_)},null,8,["onRegister","onSuccess"])],2)}}}));export{wo as default};
