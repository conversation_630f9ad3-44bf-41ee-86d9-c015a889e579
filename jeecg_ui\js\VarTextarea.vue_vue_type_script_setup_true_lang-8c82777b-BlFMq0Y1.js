import{d as Q,aI as X,e as k,f as c,w as F,o as Z,ag as y,aq as b,ar as o,as as g,u as h,ah as O,k as R,at as p,au as S,aD as w,aB as _,F as ee,aC as ae}from"./vue-vendor-dy9k-Yad.js";import{F as te,i as V}from"./index-CCWaWN5g.js";const ne={style:{display:"flex","justify-content":"space-between"}},le={style:{color:"#999999"}},se=Q({__name:"VarTextarea",props:{value:{type:String,required:!0},type:{type:String,default:"textarea"},placeholder:{type:String,default:""},width:{type:Number,default:300},height:{type:Number,default:80},minHeight:{type:Number,default:32},varsOptions:{type:Array,default:()=>[]}},emits:["update:value","blur","focus"],setup(f,{emit:z}){X(e=>({"55a8485a":q.value,"00b4a2d4":H.value}));const{prefixCls:m}=te("airag-var-textarea"),t=f,s=z,n=c(),r=k(()=>t.type==="input"),A=k(()=>!V(t.placeholder)&&V(t.value)),q=k(()=>r.value?`${t.width}px`:"100%"),x=c(t.height);F(()=>t.height,e=>x.value=e);const H=k(()=>r.value?"32px":`${x.value}px`),C=c(t.value),E=c(!0),N=c(!1);F(()=>t.value,e=>{if(e===`
`){e="",s("update:value",e);return}E.value&&$(e)});const i=c(!1);let l=null,d=null;function I(e){if(i.value=!1,e.data==="/"){const a=window.getSelection();if(a){const v=document.createRange();v.setStart(a.anchorNode,a.anchorOffset-1),v.setEnd(a.anchorNode,a.anchorOffset),a.removeAllRanges(),a.addRange(v),d=v,l=a}i.value=!0}const u=n.value.innerText;s("update:value",u)}function M(e){N.value=!1,E.value=!0,s("blur",e),setTimeout(()=>{i.value&&(i.value=!1)},300)}function Y(e){N.value=!0,E.value=!1,s("focus",e)}function $(e){r.value?C.value=e.replace(/\n/g,""):C.value=e,n.value&&(n.value.innerText=C.value)}function j({key:e}){e=`{{${e}}}`,n.value.focus(),d?(d.deleteContents(),d.insertNode(document.createTextNode(e)),l==null||l.removeAllRanges(),l==null||l.addRange(d),l==null||l.collapseToEnd()):n.value.innerText+=e,i.value=!1,r.value?s("update:value",n.value.innerText.replace(/\n/g,"")):s("update:value",n.value.innerText),d=null,l=null}function K(e){r.value&&e.key==="Enter"&&e.preventDefault()}function P(e){const u=e.clipboardData||window.clipboardData;if(!u)return;e.preventDefault();const a=u.getData("text/plain");document.execCommand("insertText",!1,a)}Z(()=>{$(t.value)});let D=0;function G(e){D=e.clientY,n.value.style.pointerEvents="none",window.addEventListener("mousemove",L),window.addEventListener("mouseup",B)}function B(){window.removeEventListener("mousemove",L),window.removeEventListener("mouseup",B),n.value.style.pointerEvents="auto"}function L(e){const u=e.clientY-D,a=x.value+u;a>t.minHeight&&(x.value=a),D=e.clientY}return(e,u)=>{const a=y("Icon"),v=y("a-menu-item"),J=y("a-menu"),U=y("a-empty"),W=y("a-dropdown");return o(),b("div",{class:g([h(m),f.type,{focus:N.value}])},[A.value?(o(),b("div",{key:0,class:g([`${h(m)}-place`])},[p("span",null,S(f.placeholder),1)],2)):O("",!0),R(W,{open:i.value},{overlay:w(()=>[f.varsOptions.length?(o(),_(J,{key:0,onClick:j},{default:w(()=>[(o(!0),b(ee,null,ae(f.varsOptions,T=>(o(),_(v,{key:T.name},{icon:w(()=>[R(a,{icon:"mdi:variable"})]),default:w(()=>[p("div",ne,[p("div",null,S(T.name),1),p("div",le,S(T.type),1)])]),_:2},1024))),128))]),_:1})):(o(),_(U,{key:1,description:"没有变量可选"}))]),default:w(()=>[p("div",{class:g([`${h(m)}-border`])},[p("div",{ref_key:"inputRef",ref:n,class:g([`${h(m)}-inner`]),contenteditable:"true",onInput:I,onBlur:M,onFocus:Y,onKeydown:K,onPaste:P},null,34),r.value?O("",!0):(o(),b("div",{key:0,class:g([`${h(m)}-resize`]),onMousedown:G},[R(a,{icon:"hugeicons:resize-field"})],34))],2)]),_:1},8,["open"])],2)}}});export{se as r};
