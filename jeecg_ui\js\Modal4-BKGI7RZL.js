import{d as l,f as c,n as d,ag as s,aB as f,ar as u,aE as g,aD as B,at as h,k as _}from"./vue-vendor-dy9k-Yad.js";import{B as b}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{B as C}from"./BasicForm-DBcXiHk0.js";import{u as V}from"./useForm-CgkFTrrO.js";import{ac as F,a as M}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const a=[{field:"field1",component:"Input",label:"字段1",colProps:{span:24},defaultValue:"111"},{field:"field2",component:"Input",label:"字段2",colProps:{span:24}}],R=l({components:{BasicModal:b,BasicForm:C},props:{userData:{type:Object}},setup(t){const r=c({}),[e,{}]=V({labelWidth:120,schemas:a,showActionButtonGroup:!1,actionColOptions:{span:24}}),[p]=F(o=>{o&&i(o)});function i(o){r.value={field2:o.data,field1:o.info}}function m(o){o&&t.userData&&d(()=>i(t.userData))}return{register:p,schemas:a,registerForm:e,model:r,handleVisibleChange:m}}}),D={class:"pt-3px pr-3px"};function $(t,r,e,p,i,m){const o=s("BasicForm"),n=s("BasicModal");return u(),f(n,g(t.$attrs,{onRegister:t.register,title:"Modal Title",onVisibleChange:t.handleVisibleChange}),{default:B(()=>[h("div",D,[_(o,{onRegister:t.registerForm,model:t.model},null,8,["onRegister","model"])])]),_:1},16,["onRegister","onVisibleChange"])}const vo=M(R,[["render",$]]);export{vo as default};
