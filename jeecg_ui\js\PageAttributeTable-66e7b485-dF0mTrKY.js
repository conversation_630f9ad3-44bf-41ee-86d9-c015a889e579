import{d as X,f as Y,ag as g,aq as Z,ar as _,F as ee,k as v,aE as te,aD as M,aB as le,ah as ie,G as ae}from"./vue-vendor-dy9k-Yad.js";import{cs as oe,bx as u,ad as V}from"./index-CCWaWN5g.js";import{L as de}from"./useTableSync-075826a1-CL-4GwR8.js";import re from"./LinkTableConfigModal-7eeb3e58-DvcKZSWX.js";import se from"./LinkTableFieldConfigModal-b078fcef-ClSEfCRc.js";import ne from"./FieldExtendJsonModal-bf04d70e-CPf_A5jR.js";import"./index-Diw57m_E.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./SetSwitchOptions-f914bc17-Cp2St2Ye.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./vxe-table-vendor-B22HppNm.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";var ue=Object.defineProperty,ce=Object.defineProperties,pe=Object.getOwnPropertyDescriptors,O=Object.getOwnPropertySymbols,me=Object.prototype.hasOwnProperty,fe=Object.prototype.propertyIsEnumerable,N=(i,n,r)=>n in i?ue(i,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[n]=r,be=(i,n)=>{for(var r in n||(n={}))me.call(n,r)&&N(i,r,n[r]);if(O)for(var r of O(n))fe.call(n,r)&&N(i,r,n[r]);return i},ye=(i,n)=>ce(i,pe(n));const K=[{title:"文本框",value:"text"},{title:"密码",value:"password"},{title:"下拉框",value:"list"},{title:"单选框",value:"radio"},{title:"多选框",value:"checkbox"},{title:"开关",value:"switch"},{title:"日期(年月日)",value:"date"},{title:"日期(年月日时分秒)",value:"datetime"},{title:"时间(HH:mm:ss)",value:"time"},{title:"文件",value:"file"},{title:"图片",value:"image"},{title:"多行文本",value:"textarea"},{title:"富文本",value:"umeditor"},{title:"MarkDown",value:"markdown"},{title:"用户选择",value:"sel_user"},{title:"部门选择",value:"sel_depart"},{title:"关联记录",value:"link_table"},{title:"他表字段",value:"link_table_field"},{title:"省市区组件",value:"pca"},{title:"Popup弹框",value:"popup"},{title:"Popup字典",value:"popup_dict"},{title:"下拉多选框",value:"list_multi"},{title:"下拉搜索框",value:"sel_search"},{title:"分类字典树",value:"cat_tree"},{title:"自定义树控件",value:"sel_tree"},{title:"联动组件",value:"link_down"}],ge=[{title:"文本框",value:"text"},{title:"单选框",value:"radio"},{title:"开关",value:"switch"},{title:"日期(yyyy-MM-dd)",value:"date"},{title:"日期（yyyy-MM-dd HH:mm:ss）",value:"datetime"},{title:"时间（HH:mm:ss）",value:"time"},{title:"文件",value:"file"},{title:"图片",value:"image"},{title:"下拉框",value:"list"},{title:"下拉多选框",value:"list_multi"},{title:"下拉搜索框",value:"sel_search"},{title:"popup弹出框",value:"popup"},{title:"部门选择",value:"sel_depart"},{title:"用户选择",value:"sel_user"},{title:"省市区组件",value:"pca"},{title:"多行文本",value:"textarea"}],he=X({name:"PageAttributeTable",components:{LinkTableConfigModal:re,LinkTableFieldConfigModal:se,FieldExtendJsonModal:ne},setup(){const i=Y([{title:"字段名称",key:"dbFieldName",width:100},{title:"字段备注",key:"dbFieldTxt",width:150},{title:"表单显示",key:"isShowForm",width:80,type:u.checkbox,align:"center",customValue:["1","0"],defaultChecked:!0},{title:"列表显示",key:"isShowList",width:80,type:u.checkbox,align:"center",customValue:["1","0"],defaultChecked:!0},{title:"是否排序",key:"sortFlag",width:80,type:u.checkbox,align:"center",customValue:["1","0"],defaultChecked:!1,props:{isDisabledCell({row:e,column:t}){let{dbTable:l}=p;const o=l.value.tableRef.getTableData({rowIds:[e.id]})[0];return(o==null?void 0:o.dbIsPersist)=="0"}}},{title:"是否只读",key:"isReadOnly",width:80,type:u.checkbox,align:"center",customValue:["1","0"],defaultChecked:!1},{title:"控件类型",key:"fieldShowType",width:170,type:u.select,options:K,defaultValue:"text",placeholder:"请选择${title}",validateRules:[{required:!0,message:"请选择${title}"},{handler:S}]},{title:"控件长度",key:"fieldLength",width:100,titleHelp:{message:"此长度只对子表列字段宽度有效！"},type:u.inputNumber,defaultValue:120,placeholder:"请输入控件长度"},{title:"是否查询",key:"isQuery",width:80,type:u.checkbox,align:"center",customValue:["1","0"],defaultChecked:!1,props:{isDisabledCell({row:e,column:t}){let{dbTable:l}=p;const o=l.value.tableRef.getTableData({rowIds:[e.id]})[0];return(o==null?void 0:o.dbIsPersist)=="0"}}},{title:"查询类型",key:"queryMode",width:120,type:u.select,options:[{title:"普通查询",value:"single"},{title:"模糊查询",value:"like"},{title:"范围查询",value:"group"}],defaultValue:"single",placeholder:"请选择${title}",validateRules:[{handler:A}],props:{isDisabledCell({row:e,column:t}){}}},{title:"控件默认值",key:"fieldDefaultValue",width:140,type:u.textarea,props:{allowClear:!0},defaultValue:""},{title:"定义转换器",key:"converter",width:150,type:u.input,defaultValue:""},{title:"扩展参数",key:"fieldExtendJson",width:120,type:u.textarea,defaultValue:""},{title:"更多配置",minWidth:100,key:"fieldConfig",type:u.slot,slotName:"fieldConfig"}]),n=de(i),{tableRef:r,tables:p}=n;function S({cellValue:e,row:t},l){let{dbTable:o}=p,a=o.value.tableRef.getTableData({rowIds:[t.id]})[0].dbType;e==="time"&&a!=="string"?l(!1,"当控件类型为时间时,数据库属性里的字段类型必须是String！"):e==="date"&&a!=="Date"&&a!=="Datetime"?l(!1,"当控件类型为日期时，数据库属性里的字段类型必须是Date或Datetime！"):e==="datetime"&&a!=="Datetime"?l(!1,"当控件类型为datetime时，数据库属性里的字段类型必须是Datetime！"):l(!0)}function C(e){let t="text";e.dbType==="Datetime"?t="datetime":e.dbType==="Date"&&(t="date"),r.value.setValues([{rowKey:e.id,values:{fieldShowType:t}}])}function w(e){e.dbIsPersist==="0"&&r.value.setValues([{rowKey:e.id,values:{isQuery:"0",sortFlag:"0"}}])}function T(e){for(let t of i.value)if(t.key=="fieldShowType"){t.options=e?ge:K;break}}function x(e){r.value.setValues([{rowKey:e,values:{isQuery:"1"}}])}const[k,{openModal:F}]=V(),[h,{openModal:D}]=V(),[J,{openModal:E}]=V();function I(e){return e.row.dbFieldName!="id"}function P(e){e.row.fieldShowType.indexOf("link_table")>=0?R(e):Q(e)}function R(e){let{row:t}=e,{checkTable:l}=p;if(l){let o=l.value.tableRef.getTableData({rowIds:[t.id]})[0];if(e.row.fieldShowType=="link_table"){let a=L(t,o);F(!0,{record:a,fieldName:t.dbFieldName})}else if(e.row.fieldShowType=="link_table_field"){let a=H(t,o);D(!0,a)}}}function H(e,t){let l=r.value.getTableData().filter(d=>d.fieldShowType=="link_table"),o={};if(l&&l.length>0){let d=p.checkTable.value.tableRef.getTableData();for(let y of l){let f=d.filter(b=>b.dbFieldName==y.dbFieldName);if(f&&f.length>0){let b=f[0];o[b.dbFieldName]={title:y.dbFieldTxt,table:b.dictTable,fields:b.dictText}}}}const{dictTable:a,dictText:m}=t,{id:c,dbFieldTxt:s}=e;return{record:{rowKey:c,dbFieldTxt:s,dictText:m,dictTable:a},tableAndFieldsMap:o}}function L(e,t){const{id:l,dbFieldTxt:o,fieldExtendJson:a}=e,{dictTable:m,dictText:c}=t;let s={rowKey:l,dbFieldTxt:o,dictTable:m};if(c){let d=c.split(",");s.titleField=d[0],d.length>1&&(s.otherFields=c.substring(c.indexOf(",")+1))}else s.titleField="",s.otherFields="";if(a)try{let d=JSON.parse(a);d.multiSelect?s.multiSelect=d.multiSelect:s.multiSelect=!1,d.isListReadOnly?s.isListReadOnly=d.isListReadOnly:s.isListReadOnly=!1,d.showType?s.showType=d.showType:s.showType="card",d.imageField?s.imageField=d.imageField:s.imageField=""}catch(d){}return s}function B(e){const{multiSelect:t,showType:l,imageField:o,fieldName:a,isListReadOnly:m}=e;let c={showType:l,multiSelect:t,imageField:o,isListReadOnly:m},s=[{rowKey:e.rowKey,values:{fieldExtendJson:JSON.stringify(c),dbFieldTxt:e.dbFieldTxt}}];r.value.setValues(s);let{checkTable:d,dbTable:y}=p;if(y){let f=[{rowKey:e.rowKey,values:{dbFieldTxt:e.dbFieldTxt}}];y.value.tableRef.setValues(f)}if(d){let f=e.titleField;e.otherFields&&(f+=","+e.otherFields);const{dictTable:b,dictField:G}=e;let W={dictTable:b,dictField:G,dictText:f,dbFieldName:a},U=[{rowKey:e.rowKey,values:W}];d.value.tableRef.setValues(U)}}function j(e){const{dbFieldTxt:t,dictTable:l,dictText:o,rowKey:a}=e;let m=[{rowKey:a,values:{dbFieldTxt:t}}];r.value.setValues(m);let{checkTable:c,dbTable:s}=p;if(s){let d=[{rowKey:a,values:{dbFieldTxt:t,dbIsPersist:"0"}}];s.value.tableRef.setValues(d)}if(c){let d=[{rowKey:a,values:{dictTable:l,dictText:o}}];c.value.tableRef.setValues(d)}}function Q(e){let t=e.row.fieldExtendJson||"",l=e.rowId,o=e.row.fieldShowType||"",a=e.row.sortFlag||"0";const m=e.row.dbType;E(!0,{jsonStr:t,fieldShowType:o,sortFlag:a,id:l,dbType:m})}function q(e,t){let l;e&&Object.keys(e).length>0?l=[{rowKey:t,values:{fieldExtendJson:JSON.stringify(e)}}]:l=[{rowKey:t,values:{fieldExtendJson:""}}],r.value.setValues(l)}const $=e=>{};function A({cellValue:e,row:t},l){let{dbTable:o}=p;const a=o.value.tableRef.getTableData({rowIds:[t.id]})[0].dbType;e==="group"?["double","int","BigDecimal","Date","Datetime"].includes(a)||t.fieldShowType==="time"?l(!0):l(!1,"范围查询，只支持数据库属里的字段类型为：Integer、Double、BigDecimal及Date、Datetime 或 控件类型是时间(HH:mm:ss)"):e==="like"?["string"].includes(a)&&t.fieldShowType==="text"?l(!0):l(!1,"模糊查询，只支持控件类型是文本框且数据库属里的字段类型是String"):l(!0)}const z=({oldValue:e,row:t})=>{t.dbType!="string"&&e=="string"&&r.value.getTableData({rowIds:[t.id]})[0].queryMode=="like"&&r.value.setValues([{rowKey:t.id,values:{queryMode:"single"}}])};return ye(be({},n),{columns:i,enableQuery:x,syncFieldShowType:C,changePageType:T,showConfigButton:I,showFieldConfig:R,registerExtJsonModal:J,handleExtJson:q,openConfig:P,registerModal:k,handleConfigData:B,registerFieldModal:h,handleFieldConfigData:j,syncIsQuery:w,handleValueChange:$,syncQueryMode:z})}});function ve(i,n,r,p,S,C){const w=g("a-button"),T=g("JVxeTable"),x=g("link-table-config-modal"),k=g("link-table-field-config-modal"),F=g("FieldExtendJsonModal");return _(),Z(ee,null,[v(T,te({ref:"tableRef","row-class-name":"online-config-page",rowNumber:"",keyboardEdit:"",maxHeight:i.tableHeight.noToolbar,loading:i.loading,columns:i.columns,dataSource:i.dataSource,disabledRows:{dbFieldName:["id","has_child"]},onValueChange:i.handleValueChange},i.tableProps),{fieldConfig:M(h=>[i.showConfigButton(h)?(_(),le(w,{key:0,type:"primary",size:"small",ghost:"",onClick:D=>i.openConfig(h)},{default:M(()=>n[0]||(n[0]=[ae("更多配置")])),_:2},1032,["onClick"])):ie("",!0)]),_:1},16,["maxHeight","loading","columns","dataSource","onValueChange"]),v(x,{onRegister:i.registerModal,onSuccess:i.handleConfigData},null,8,["onRegister","onSuccess"]),v(k,{onRegister:i.registerFieldModal,onSuccess:i.handleFieldConfigData},null,8,["onRegister","onSuccess"]),v(F,{onRegister:i.registerExtJsonModal,onSuccess:i.handleExtJson},null,8,["onRegister","onSuccess"])],64)}const Ct=oe(he,[["render",ve],["__scopeId","data-v-519eccff"]]);export{Ct as default};
