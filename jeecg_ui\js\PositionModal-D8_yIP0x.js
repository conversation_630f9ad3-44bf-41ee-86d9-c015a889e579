var x=Object.defineProperty;var f=Object.getOwnPropertySymbols;var _=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var y=(e,t,s)=>t in e?x(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,g=(e,t)=>{for(var s in t||(t={}))_.call(t,s)&&y(e,s,t[s]);if(f)for(var s of f(t))M.call(t,s)&&y(e,s,t[s]);return e};var m=(e,t,s)=>new Promise((r,d)=>{var p=i=>{try{l(s.next(i))}catch(n){d(n)}},u=i=>{try{l(s.throw(i))}catch(n){d(n)}},l=i=>i.done?r(i.value):Promise.resolve(i.value).then(p,u);l((s=s.apply(e,t)).next())});import{d as b,f as v,e as w,u as a,aB as I,ar as U,aD as F,k,aE as E}from"./vue-vendor-dy9k-Yad.js";import{B as T}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{j as c,ac as j}from"./index-CCWaWN5g.js";import{M as O}from"./antd-vue-vendor-me9YkNVC.js";import{u as S}from"./useForm-CgkFTrrO.js";import{B as L}from"./BasicForm-DBcXiHk0.js";const Q=[{title:"职务名称",dataIndex:"name",align:"left"}],W=[{field:"name",label:"职务名称",component:"Input",colProps:{span:8}}],R=[{label:"主键",field:"id",component:"Input",show:!1},{field:"name",label:"职务名称",component:"Input",required:!0}];const Y="/sys/position/exportXls",Z="/sys/position/importExcel",A=e=>c.get({url:"/sys/position/list",params:e}),q=(e,t)=>{let s=t?"/sys/position/edit":"/sys/position/add";return c.post({url:s,params:e})},X=e=>c.get({url:"/sys/position/queryById",params:e}),ee=(e,t)=>c.delete({url:"/sys/position/delete",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),te=(e,t)=>{O.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>c.delete({url:"/sys/position/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})};const C=b({__name:"PositionModal",emits:["success","register"],setup(e,{emit:t}){const s=t,r=v(!0),[d,{resetFields:p,setFieldsValue:u,validate:l}]=S({schemas:R,showActionButtonGroup:!1}),[i,{setModalProps:n,closeModal:h}]=j(o=>m(null,null,function*(){yield p(),n({confirmLoading:!1}),r.value=!!(o!=null&&o.isUpdate),a(r)&&(o.record=yield X({id:o.record.id}),yield u(g({},o.record)))})),B=w(()=>a(r)?"编辑职务":"新增职务");function P(){return m(this,null,function*(){try{const o=yield l();n({confirmLoading:!0}),yield q(o,r.value),h(),s("success")}finally{n({confirmLoading:!1})}})}return(o,D)=>(U(),I(a(T),E(o.$attrs,{onRegister:a(i),title:B.value,onOk:P,width:700}),{default:F(()=>[k(a(L),{onRegister:a(d)},null,8,["onRegister"])]),_:1},16,["onRegister","title"]))}}),se=Object.freeze(Object.defineProperty({__proto__:null,default:C},Symbol.toStringTag,{value:"Module"}));export{se as P,C as _,Y as a,te as b,Q as c,ee as d,A as e,Z as g,W as s};
