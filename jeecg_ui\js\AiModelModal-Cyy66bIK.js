var ne=Object.defineProperty;var J=Object.getOwnPropertySymbols;var re=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable;var z=(l,a,d)=>a in l?ne(l,a,{enumerable:!0,configurable:!0,writable:!0,value:d}):l[a]=d,W=(l,a)=>{for(var d in a||(a={}))re.call(a,d)&&z(l,d,a[d]);if(J)for(var d of J(a))de.call(a,d)&&z(l,d,a[d]);return l};var T=(l,a,d)=>new Promise((e,x)=>{var R=p=>{try{s(d.next(p))}catch(f){x(f)}},v=p=>{try{s(d.throw(p))}catch(f){x(f)}},s=p=>p.done?e(p.value):Promise.resolve(p.value).then(R,v);s((d=d.apply(l,a)).next())});import{f as u,ag as c,aB as b,ar as r,aH as ie,aD as o,at as A,aq as h,ah as g,as as se,au as K,F as Q,aC as Z,G as C,k as E}from"./vue-vendor-dy9k-Yad.js";import{I as ue}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{j as F,ac as ce,aF as pe,a as me}from"./index-CCWaWN5g.js";import{M as be,aL as Ae}from"./antd-vue-vendor-me9YkNVC.js";import{B as ge}from"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import ve from"./AiModelSeniorForm-DwUUzhZR.js";import{u as fe}from"./useForm-CgkFTrrO.js";const ye=[{title:"DeepSeek",value:"DEEPSEEK",LLM:[{label:"deepseek-reasoner",value:"deepseek-reasoner",descr:`【官方模型】深度求索 新推出的推理模型R1满血版
火便全球。
支持64k上下文，其中支持8k最大回复。`,type:"text"},{label:"deepseek-chat",value:"deepseek-chat",descr:"最强开源 MoE 模型 DeepSeek-V3，全球首个在代码、数学能力上与GPT-4-Turbo争锋的模型，在代码、数学的多个榜单上位居全球第二；",type:"text"}],type:["LLM"],baseUrl:"https://api.deepseek.com/v1",LLMDefaultValue:"deepseek-chat"},{title:"Ollama",value:"OLLAMA",LLM:[{label:"llama2",value:"llama2"},{label:"llama2:13b",value:"llama2:13b"},{label:"llama2:70b",value:"llama2:70b"},{label:"llama2-chinese:13b",value:"llama2-chinese:13b"},{label:"llama3:8b",value:"llama3:8b"},{label:"llama3:70b",value:"llama3:70b"},{label:"qwen:0.5b",value:"qwen:0.5b"},{label:"qwen:1.8b",value:"qwen:1.8b"},{label:"qwen:4b",value:"qwen:4b"},{label:"qwen:7b",value:"qwen:7b"},{label:"qwen:14b",value:"qwen:14b"},{label:"qwen:32b",value:"qwen:32b"},{label:"qwen:72b",value:"qwen:72b"},{label:"qwen:110b",value:"qwen:110b"},{label:"qwen2:72b-instruct",value:"qwen2:72b-instruct"},{label:"qwen2:57b-a14b-instruct",value:"qwen2:57b-a14b-instruct"},{label:"qwen2:7b-instruct",value:"qwen2:7b-instruct"},{label:"qwen2.5:72b-instruct",value:"qwen2.5:72b-instruct"},{label:"qwen2.5:32b-instruct",value:"qwen2.5:32b-instruct"},{label:"qwen2.5:14b-instruct",value:"qwen2.5:14b-instruct"},{label:"qwen2.5:7b-instruct",value:"qwen2.5:7b-instruct"},{label:"qwen2.5:1.5b-instruct",value:"qwen2.5:1.5b-instruct"},{label:"qwen2.5:0.5b-instruct",value:"qwen2.5:0.5b-instruct"},{label:"qwen2.5:3b-instruct",value:"qwen2.5:3b-instruct"},{label:"phi3",value:"phi3"}],EMBED:[{label:"nomic-embed-text",value:"nomic-embed-text"}],type:["LLM","EMBED"],baseUrl:"http://localhost:11434",LLMDefaultValue:"llama2",EMBEDDefaultValue:"nomic-embed-text"},{title:"OpenAI",value:"OPENAI",LLM:[{label:"gpt-3.5-turbo",value:"gpt-3.5-turbo",descr:`纯官方高速GPT3.5系列，目前指向gpt-35-turbo-0125模型，最大回复小于4k。
综合能力强，过去使用最广泛的文本模型。`,type:"text"},{label:"gpt-4",value:"gpt-4",descr:"纯官方GPT4系列。知识库截止于2021年，价格适中，具有中等参数，比gpt-4turbo系列略强。",type:"text"},{label:"gpt-4o",value:"gpt-4o",descr:`GPT-4o，是openai的新旗舰型号，支持文本和图片分析。

是迈向更自然的人机交互的一步——它接受文本和图像的任意组合作为输入，并生成文本和图像输出的任意组合。`,type:"text,image"},{label:"gpt-4o-mini",value:"gpt-4o-mini",descr:`GPT-4o mini是目前性价比最高的小参数模型，性能介于GPT3.5~GPT4o之间。

成本相比GPT-3.5 Turbo便宜60%以上，支持50种不同语言，用于替代GPT-3.5版本的模型。

4o-mini的图像分析价格和4o差不多，如果有图像分析需求还是4o更好一些。

当前指向 gpt-4o-mini-2024-07-18`,type:"text,image"},{label:"gpt-4-turbo",value:"gpt-4-turbo",descr:"纯官方GPT4系列，支持文本和图片分析，最大回复4k，openai于2024-4-9新增的模型，知识库更新于2023年12月。提高了写作、数学、逻辑推理和编码能力。当前指向gpt-4-turbo-2024-04-09",type:"text,image"},{label:"gpt-4-turbo-preview",value:"gpt-4-turbo-preview",descr:"纯官方GPT4系列，最大回复4k，知识库更新于2023年4月。当前指向gpt-4-0125-preview",type:"text"},{label:"gpt-3.5-turbo-0125",value:"gpt-3.5-turbo-0125",descr:`openai于2024年1月25号更新的gpt-3.5模型，最大回复4k。

综合能力强，过去使用最广泛的文本模型。`,type:"text"},{label:"gpt-3.5-turbo-1106",value:"gpt-3.5-turbo-1106",descr:`openai于2023年11月6号更新的gpt-3.5模型，最大回复4k。属于即将被淘汰的模型。

建议使用gpt-3.5-turbo或gpt-4o-mini`,type:"text"},{label:"gpt-3.5-turbo-0613",value:"gpt-3.5-turbo-0613",descr:"通过微调后可以更准确地按照用户的指示进行操作，生成更简洁和针对性的输出。它不仅可以用于文本生成，还可以通过函数调用功能与其他系统和API进行集成，实现更复杂的任务自动化",type:"text"},{label:"gpt-4o-2024-05-13",value:"gpt-4o-2024-05-13",descr:`GPT-4o，是openai的新旗舰型号，支持文本和图片分析。

是迈向更自然的人机交互的一步——它接受文本和图像的任意组合作为输入，并生成文本和图像输出的任意组合。

该模型为初代的4o模型`,type:"text,image"},{label:"gpt-4-turbo-2024-04-09",value:"gpt-4-turbo-2024-04-09",descr:"纯官方GPT4系列，支持文本和图片分析，最大回复4k，openai于2024-4-9新增的模型，提高了写作、数学、逻辑推理和编码能力。知识库更新于2023年12月。",type:"text,image"},{label:"gpt-4-0125-preview",value:"gpt-4-0125-preview",descr:"纯官方GPT4系列，最大回复4k，知识库更新于2023年4月。当前与gpt-4-turbo-preview属于同一模型",type:"text"},{label:"gpt-4-1106-preview",value:"gpt-4-1106-preview",descr:"纯官方GPT4系列，最大回复4k，知识库更新于2023年4月。正在逐渐被新的模型gpt-4-turbo和gpt-4-turbo-preview取代。",type:"text"}],EMBED:[{label:"text-embedding-ada-002",value:"text-embedding-ada-002",descr:"用于生成文本嵌入的模型。文本嵌入是将文本转换为数值形式（通常是向量），以便可以用于机器学习模型。",type:"vector,embeddings"},{label:"text-embedding-3-small",value:"text-embedding-3-small",descr:'用于生成文本的嵌入表示，网络结构较小，计算资源需求较低。虽然可能不如"large"版本那样精准，但它更适合于资源受限的环境或需要更快速处理的任务。',type:"vector,embeddings"},{label:"text-embedding-3-large",value:"text-embedding-3-large",descr:"用于生成文本的嵌入表示，即将文本转换为高维空间中的点，这些点的距离可以表示文本之间的相似度。有较大的网络结构，能够捕捉更丰富的语言特征，适用于需要高质量文本相似度或分类任务的场景。",type:"vector,embeddings"}],type:["LLM","EMBED"],baseUrl:"https://api.openai.com/v1/",LLMDefaultValue:"gpt-3.5-turbo",EMBEDDefaultValue:"text-embedding-ada-002"},{title:"通义千问",value:"QWEN",LLM:[{label:"qwen-turbo",value:"qwen-turbo",descr:"通义千问超大规模语言模型，支持中文、英文等不同语言输入。适合文本创作、文本处理、编程辅助、翻译服务、对话模拟。",type:"text"},{label:"qwen-plus",value:"qwen-plus",descr:"通义千问超大规模语言模型，支持中文、英文等不同语言输入。适合文本创作、文本处理、编程辅助、翻译服务、对话模拟。",type:"text"},{label:"qwen-max",value:"qwen-max",descr:"暂无描述内容！",type:"text"}],EMBED:[{label:"text-embedding-v2",value:"text-embedding-v2",descr:"是一种将文本数据转换为向量的技术，通过深度学习模型将文本的语义信息嵌入到高维向量空间中。这些向量不仅能表达文本内容，还能捕捉文本之间的相似性和关系，从而让计算机高效地进行文本检索、分类、聚类等任务。",type:"vector"}],type:["LLM","EMBED"],baseUrl:"https://dashscope.aliyuncs.com/api/v1/services/",LLMDefaultValue:"qwen-plus",EMBEDDefaultValue:"text-embedding-v2"},{title:"千帆大模型",value:"QIANFAN",LLM:[{label:"ERNIE-Bot",value:"ERNIE-Bot",descr:"是百度推出的一款知识增强大语言模型，主要用于与人对话互动、回答问题、协助创作，帮助人们高效便捷地获取信息、知识和灵感",type:"text"},{label:"ERNIE-Bot 4.0",value:"ERNIE-Bot 4.0",descr:`百度自行研发的文心产业级知识增强大语言模型4.0版本

实现了基础模型的全面升级，在理解、生成、逻辑和记忆能力上相对ERNIE 3.5都有着显著提升，支持5K输入+2K输出。`,type:"text"},{label:"ERNIE-Bot-8K",value:"ERNIE-Bot-8K",descr:"主要用于数据分析场景，特别是在企业数据分析中表现出色。ERNIE-Bot-8K是百度文心大模型的一个版本，具有模型效果优、生成能力强、应用门槛低等独特优势。",type:"text"},{label:"ERNIE-Bot-turbo",value:"ERNIE-Bot-turbo",descr:"是一个大语言模型，主要用于对话问答、内容创作生成等任务。它是百度自行研发的大语言模型，覆盖了海量中文数据，具有更强的对话问答和内容创作生成能力",type:"text"},{label:"ERNIE-Speed-128K",value:"ERNIE-Speed-128K",descr:"是一款基于Transformer结构的轻量级语言模型，旨在满足实时数据处理的需求。它具有高效、低延迟和高准确性的特点，广泛应用于自然语言处理、信息检索和文本分类等领域",type:"text"},{label:"EB-turbo-AppBuilder",value:"EB-turbo-AppBuilder",descr:"主要用于企业级应用场景，如智能客服、内容创作和知识问答等任务。它是基于文心高性能大语言模型ERNIE-Bot-turbo构建的，针对企业特定需求进行了深度的场景效果优化和输出格式定制，因此在满足企业特定需求方面具有更高的灵活性和实用性",type:"text"},{label:"Yi-34B-Chat",value:"Yi-34B-Chat",descr:"Yi-34B-Chat是一款基于Transformer架构的生成式预训练语言模型，它拥有340亿个参数，使其在处理自然语言任务时表现出了强大的能力。",type:"text"},{label:"BLOOMZ-7B",value:"BLOOMZ-7B",descr:"是一个用于生成文本序列的自回归模型，它可以进行多语言处理，支持46种语言和13种编程语言。BLOOMZ-7B是BLOOM模型的一个调优版本，具有更出色的泛化和零样本学习能力，适用于多种任务和场景",type:"text"},{label:"Qianfan-BLOOMZ-7B-compressed",value:"Qianfan-BLOOMZ-7B-compressed",descr:"是千帆团队在BLOOMZ-7B基础上的压缩版本，融合量化、稀疏化等技术，显存占用降低30%以上。",type:"text"},{label:"Mixtral-8x7B-Instruct",value:"Mixtral-8x7B-Instruct",descr:"由Mistral AI发布的首个高质量稀疏专家混合模型 (MOE)，模型由8个70亿参数专家模型组成，在多个基准测试中表现优于Llama-2-70B及GPT3.5，能够处理32K上下文，在代码生成任务中表现尤为优异。",type:"text"},{label:"Llama-2-7b-chat",value:"Llama-2-7b-chat",descr:"由Meta AI研发并开源，在编码、推理及知识应用等场景表现优秀，Llama-2-7b-chat是高性能原生开源版本，适用于对话场景。",type:"text"},{label:"Llama-2-13b-chat",value:"Llama-2-13b-chat",descr:"由Meta AI研发并开源，在编码、推理及知识应用等场景表现优秀，Llama-2-13b-chat是性能与效果均衡的原生开源版本，适用于对话场景。",type:"text"},{label:"Llama-2-70b-chat",value:"Llama-2-70b-chat",descr:"由Meta AI研发并开源，在编码、推理及知识应用等场景表现优秀，Llama-2-70b-chat是高精度效果的原生开源版本。",type:"text"},{label:"Qianfan-Chinese-Llama-2-7B",value:"Qianfan-Chinese-Llama-2-7B",descr:"是千帆团队在Llama-2-7b基础上的中文增强版本，在CMMLU、C-EVAL等中文数据集上表现优异。",type:"text"},{label:"ChatGLM2-6B-32K",value:"ChatGLM2-6B-32K",descr:"是在ChatGLM2-6B的基础上进一步强化了对于长文本的理解能力，能够更好的处理最多32K长度的上下文。",type:"text"},{label:"AquilaChat-7B",value:"AquilaChat-7B",descr:"是由智源研究院研发，基于Aquila-7B训练的对话模型，支持流畅的文本对话及多种语言类生成任务，通过定义可扩展的特殊指令规范，实现 AquilaChat对其它模型和工具的调用，且易于扩展。",type:"text"}],EMBED:[{label:"Embedding-V1",value:"Embedding-V1",descr:"主要用于将离散对象（如文本、图像等）映射为连续的数值向量，以便于计算机处理和机器学习模型的训练和使用",type:"vector,embeddings"},{label:"tao-8k",value:"tao-8k",descr:"是由Huggingface开发者amu研发并开源的长文本向量表示模型,支持8k上下文长度,模型效果在C-MTEB上居前列,是当前最优的中文长文本embeddings模型之一",type:"vector"},{label:"bge-large-zh",value:"bge-large-zh",descr:"是由智源研究院研发的中文版文本表示模型，可将任意文本映射为低维稠密向量，以用于检索、分类、聚类或语义匹配等任务，并可支持为大模型调用外部知识。",type:"vector"},{label:"bge-large-en",value:"bge-large-en",descr:"是由智源研究院研发的英文版文本表示模型，可将任意文本映射为低维稠密向量，以用于检索、分类、聚类或语义匹配等任务，并可支持为大模型调用外部知识。",type:"vector"}],type:["LLM","EMBED"],baseUrl:"https://aip.baidubce.com",LLMDefaultValue:"Yi-34B-Chat",EMBEDDefaultValue:"Embedding-V1"},{title:"智谱AI",value:"ZHIPU",LLM:[{label:"glm-4",value:"glm-4",descr:"是一个多模态大语言模型，主要用于处理复杂的指令和任务，支持长文本处理、多模态理解和文生图等功能",type:"text,image"},{label:"glm-4v",value:"glm-4v",descr:`智谱：多模态模型

更懂中文的视觉理解、文生图等多模态模型能力。准确理解各任务场景语言描述及指令，更精确的完成多模态理解类任务，或生成高质量的图片、视频等多模态内容。`,type:"text,image"},{label:"glm-4-flash",value:"glm-4-flash",descr:"该模型官方免费，主要用于处理多种自然语言处理任务，包括智能对话助手、辅助论文翻译、ppt及会议内容生产、网页智能搜索、数据生成和抽取、网页解析、智能规划和决策、辅助科研等场景",type:"text"},{label:"glm-3-turbo",value:"glm-3-turbo",descr:"是一种基于transformer结构的语言模型，由智谱AI推出。其主要特点包括使用三层transformer结构、采用Turbo机制以实时生成文本、处理长文本输入并具有强大的语言理解能力",type:"text"}],EMBED:[{label:"Embedding-3",value:"Embedding-3",descr:"主要用于文本搜索、聚类、推荐等任务。它通过将文本映射到低维向量空间，使得文本之间的语义关系可以通过向量之间的距离或相似度来衡量，从而支持各种基于向量的应用。",type:"vector"},{label:"Embedding-2",value:"Embedding-2",descr:"用于将高维离散数据映射到低维连续数值向量中，以便机器学习模型能够更好地处理和理解这些数据",type:"vector"}],type:["LLM","EMBED"],baseUrl:"https://open.bigmodel.cn",LLMDefaultValue:"glm-4-flash",EMBEDDefaultValue:"Embedding-2"}],P={data:ye},Be="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAAyhJREFUSIntlk1slFUUhp/3Tv+1RmOKFTUiGBJ/kIWJC42LsiJtmQkaWBCqUSSmnakGG+MOwsINiyqtU+iKiMCidWHHaPyJdqELXNAmLhoTgsoGFmCJ1Jp2fr7XRTt0hk4LrRoWcFZfzvnOec49973f/WSbW2HhllDvgO+A/0+rWk3SziR3z4hWFD1NFH7MpPmmGNuW9Lf1a9Q2dIDscjW03DmOd/OYHHXaehHRDNQAF4AngQZgsi6n9UOD/AmwvZN1hZh/k3x8tjrs+6qXyWKtxB4aaWDrSD/Dy644nqQLudeoFpWF1pY83zUb41HgZ4CcCAGw9Upt1lu2vqPN1bO0S9Ez1CuBPQZhGJbY40SK15HTQO2S45izWgefTiR5CaDhMufnJ4Lh4Zqsf5H8MagHeFwK3xUTF4HbkjQbf3gDYKnVWx7elmTv0BAFSe/NcQFouvaWGb/6F8eXBMeIUkDjCsAAQfLReJI3R/o5Ievdktgk+EhdXi2jx5gpOheJK5HyuM2MgnrnGo02YcWBzcuAo4VF+BPEFFYX8MfnaTXZLFLwInEZ1iO+LqpvXgz7491swe4DnipLkAcUhQ8sHwZaQR0lmAuVoFBZXDXMHZcyy/TzfT7oWeGT5Z3qCYh6yGkHMHFdUz9Ugi4FvghsbEvSfH3gyz5mM+nQAT5V4m6xtGlqLVnQwXJuuHmw8U9AdRXsrZRg46npsAcYK3G/0HjJv4IHSnyXnSNz0+BAGAFwcGfrW9xTKWn0GDMhqAMWVAo8Aty/sAAdzgzy902DrzbxKXAO82BVIUpLC9+t9i7u297JOoDP+pjA6qlUVOJMfROHloJWBI8eIG/pDaCAtLs9FQ22vEYdQCzGhkLMZxOp6NDOg9Rk0gxgnyincjFvvbzqSyKRotM4DQgYw3q7Os+5XLXHgQeAscjaPb2Gs42XfArYMZ96BXmw4HD0i484v2IwQLybXdhHoPJeA1lgRPh3S89hHgI2AJHkkyP94dVVgaF41UX7QbuofGlcAffXNYX3bzTeFYGvNbCPe/M5nhdsBGplph2YIMvp5dT7r8H/td1+P3u3H/gf+dA5stOC/XQAAAAASUVORK5CYII=",he="/assets/ollama-Bt9O-2K_.png",Ee="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAAvBJREFUSImV1UuIllUYB/Df+RjGxqFpVkrZDUJGKCQdRIIoqKjZtJnBRS0+q4VQGLRKxCKRchGFyhCkRVIGgZAKXUBrKhPa1EBYGF2EqRaSU6HmbCad0+J93jzz9s039MLhPee5/Z/beY6cs4UWbsY4TuNvTONDjHbTm2eji/ENmMFlfIG9OBggGYcwGE70/S8A3BUef4e1Dd5SHA+Qel3GV9iI1BUACd+Epzc0eKvxcRj9E/uwDa/g16AfRs9/AMLwBnwQgs8VvGV4FZcibdvR3wBfgl2hu2seAK7BsSLcjLuD9zDOYQ5vl1FhCEsaQO+E7K1x1oqw57ATWwKgFpjAl1hfGFmJIyF3GmMFb1XQn68BHgnCC0HY3AGgHftBvBxpGkcbP4f88boh8AcO1QCf4wx6uwA8iidcadE9dSHRF4X+K9L7Bi7gYA1wpj4sAPBJKJ/CSKxT+Bb3F3rXYX9Rw+01wHkcWATgX4+D1hO0jPexquA9EE0xhYGWqufXufLNxn95QZvMOV+qD7GfVF2unzCZUtqTUurPOR/FJtyEJ2FreDIaHvRjdxRydxhpd7iQbUzE/t6wsam4Uz/i65bqFk5hf0rpvpzzTM75KazFLRjGcEqpp44g9sNFhL/FvzcizOHYUCvnfAFvYQDHUkpvppRW5Jy/zzk/GDm9BydTSiMppRGcxGO6f72YrcN9FxfxuqoLLuJZLO1Q1Gk8rmrdOkW3BW9znK/CWXxaAxzBudivwWeh8ItqQo5HTV7CYIcaNAF2xLldA+wMwuqiiGOqMZDDgZUFb71qfDQBni5sfYSWQmBOcR+KCTlUnK/HgZA9j4eCfqf5g/IoBprj+rVgvijGRsHrV43oGdXI3otlBf+Z0H0Po4pHp+nt4RCciqJuUT0qvwd9okxj6K0I/olFn0zVBdmo6uE63HqdwNUN+dtVz+os7lgUoKHch2tVj1Ed2XSkYV8A1i09tqCdhRgdAEdVz+nZ8PiHSOON3fT+AREgFSb/oZrhAAAAAElFTkSuQmCC",Me="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAAxtJREFUSImlll1oFFcUgL9zZ/OzG//qRgTRYiQxWh9E1GAr1n+EShoE9SHRhNhsBREafDBJG5CArUqDD4qg1lh2Vyuo0Ad9UEFlRZRERKxFEa1SFX9qDVbXZJPdmeNDyZJudzYTPI/3nvN951xm5o6oKl5jfdeOBRjzyZN4ouPiom0pLzXiRVDT+WOZsezvVVkNCHBXoDE6t/nMBwlqLu/8yBTS5CiNAgVZUk6r8M3ROc0PhiXYeP1gXly7642yXWHcEE32A/sL+k1rx/ytb4cUVF/bVWlUdyOUDgHOjKei0nKkoimqkIamBXVdO6baIoeB+TkgNvAOGOWWIMplIVUbqWh9CGAGNqZU9N8XlYPAsyx1SZSoiJlhCnwTBGkEnmfJe4XIycSf5Y/+M0FZQ88sYPb9N4Gf17e2F9qJ1GaBVhXyDIRT2NuPzf3u8WBS7W/tRdpnNyjaDBQLHDaq34bbb7zuLnhebxu5Pi4cuyGqSmkovkQw54E7qGy7d8h/orrzh/GpfHGOz2p5mePIWHu7bYS/t3BseHbTo1d1i5eparugM4GlwUjsQqZgIM4mRgZWPd5Nby74QDxZ+5nfX5j3K7Bi0PLSYCR2wbjUrPC/jU/2AgcI5PtKMuDpcBOgWKO9CrCMa667QJ0xXvkCrrmuAsS9q8yw1Rn+BKDej0hk+AKB+tINvZOGYnevWzxRlLphC4B54tPfS0M9X7vCaxetUePcBD51y/EB4KBZVcoogQNloZ4vbHTjg5+KXgD8Vb1svOVLHgCq3MAqoukJ/vh4RAwlBLi9tVUWcmtaKFHyT+3CEsuXvJUD/lJUQ8VTYjHI+FyX1L8eY/ny2gTZlJ5ucFeOs6QzuVKA85l7gAN6ND9lbRn5y8W/BxazXjjlX72baRv2CrJg8LoRXX61d6Wo6LmMkmuIbg6GL3Vlsv7XJcDdjqKbwOdlDT2VCPuASQCOjUEc+fdaBuAZSnPwyKUoLndvrqeIe4cCpwJ9gelAG9Cnllg2YgFJYI84fdOC0VjEDQ4e/yoApobi08WoXun/EmxHgpHYHS917wFHPzh2fqyb6QAAAABJRU5ErkJggg==",xe="/assets/qianwen-DcU66u7N.png",we="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAgCAYAAAAFQMh/AAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAABIBJREFUSIntl1tsFGUUx39nZttugVQEdmYpFQOxJAKKF7wQpBETCBheIErURBNCZ7pEbBDjizZixSiJ1xAS2C3qAyCKGhMvGNGAYJAoQlQakSAmFJe628pVemN3jg/t0N12d6GriS/+k0lmvvOd//98c86cnBFVpRiEIkwnjbY1caAYfylW+J/C+E9ULydsu2y3XdbnsoXreCjs8GAevw1hl08LcQcKGUXYCyQGrlsR5omyBQHb5VQixudZfnAApaUgdzE5tlxuEvgOUJTbEk38NFSOKxaurqcM4OhaugEqa7lWTDQeLXyyvFBVKl0dpqrku6xafdJytNNytCdUq88PtIdcvd+u1f0hR3fZrt5RiMvXMgBSHovyBRZeykSENUAQKBHh6ZDDrb7dilAtytsqTBe4W5WPpi6mNB+frxUASDSxGWDCEoIdJXwBTDJgQWuM/QQIodnVb8CoS/dpJqtkFal1+mrCQMvYWm73DD4BfqkIMufoWrp9rSzCrjImAncBlgfzARKVHKC3kPqzU8o+/8E02Au0Z9gPnoxxAsAzuRcIAbMudDJxQPD9aN3AzyirFd7REmIAuopUQJgtsALYC0igm9G+TzxKu3rMQHgJ5dlSjzmqKICXIqbCu8BzJ5s4nKk1pM9JGjGsOHuAclXexORQm7JPo1y8YpLLCY9ZxjgzxehkFc26Cs9ft+tYh/JoPwPNmmZhciO/ZgXYyg1pob19PfFc/DlbplVHvZnmOMKP1u/s8r/hCUsIotyXtVmZKgab/MfqesqsOF/h8YOZ5rjl8lhOYdthi+2yMiPagCgvAmbfiWrOd7EQoKOMmwE7B8+dVct7836+k0XArL51U2CNNPZXve2y0nbZbACIUDjRfcVCmgt5dnSnztGV1zsHcubYqmO5KK/3Rbx7zBnmNm+jRwSxHJqByVnMysZkEw70vupznexAqAHSKqxIRlk3UCNnjpNR1nlpxotwY2Ic9zRvo6dPQAV2ZmxNifCW0c0Kf+HoWrqTVcxGmealGZ9LNO+J82F0PRUlXXyrwvemxwujznLMD2qoGCRsuaxGqFZ4vC1KK4AdYRoeDUANEAydIZQpONZligdLVUimYP2pKGcBQkupNE1eU+VIoolnMnWyBoGxESYLNKAgymGgccISgpSwA7D6tnUkQ4wC/vB9PPgGqBCFElhAb9vFMHAUFiNQ6bA1s3tl5XhEKcdQ9gAJz+wdXToCTMoQBRgmF/my0uF622GO57ETqMiwzxxXxzAAQ9gOJAR2Dy/nt0Gv2nKZn4zxWa5cXLOS8p6/aAHGAKD86XnMbHuDIwB2HU+gvJzh8nUiRk0uLgDb5eFEjE0GQFo5lG/jiVfpVI+5wAfAh57JPF8UIBHlFWAZcFDgPS/NA/m4AEpH8P6lExeD8CMMN8vReJSOYvyLmqutCNUaJJ5S4lYt1/3rwmGXhrDb25EyIcoU4CpgpGFkdzGAsIMbdmkoxF1wrgZmKIOnyORpPg6N5CkETZ4ePLircItAVSHi//+drhihCNMzx9yh4m+4H+V9/qHZOgAAAABJRU5ErkJggg==",Ce=[{label:"id",field:"id",component:"Input",show:!1},{label:"模型名称",field:"name",required:!0,component:"Input"},{label:"模型类型",field:"modelType",slot:"modelType",required:!0,component:"Select"},{label:"基础模型",field:"modelName",required:!0,slot:"modelName",component:"Select"},{label:"API域名",field:"baseUrl",required:!0,component:"Input"},{label:"API Key",field:"apiKey",required:!0,component:"InputPassword",ifShow:({values:l})=>l.provider!=="OLLAMA"},{label:"Secret Key",field:"secretKey",required:!0,component:"InputPassword",ifShow:({values:l})=>!(l.provider==="DEEPSEEK"||l.provider==="OLLAMA"||l.provider==="OPENAI"||l.provider==="ZHIPU"||l.provider==="QWEN")},{label:"供应者",field:"provider",component:"Input",show:!1}],Le=u({DEEPSEEK:Be,OLLAMA:he,OPENAI:Ee,QIANFAN:Me,QWEN:xe,ZHIPU:we});const na=l=>F.get({url:"/airag/airagModel/list",params:l},{isTransformResponse:!1}),qe=l=>F.get({url:"/airag/airagModel/queryById",params:l},{isTransformResponse:!1}),Ie=l=>F.post({url:"/airag/airagModel/add",params:l}),Oe=l=>F.put({url:"/airag/airagModel/edit",params:l}),ra=(l,a)=>{be.confirm({title:"确认删除",content:"是否删除名称为"+l.name+"的模型吗？",okText:"确认",cancelText:"取消",onOk:()=>F.delete({url:"/airag/airagModel/delete",params:l},{joinParamsToUrl:!0}).then(()=>{a()})})},De={name:"AddModelModal",components:{BasicForm:ge,BasicModal:ue,AiModelSeniorForm:ve,AutoComplete:Ae},emits:["success","register"],setup(l,{emit:a}){const d=u([]),e=u([]),x=u(!1),R=u("all"),v=u([]),s=u("list"),p=u(""),f=u([]),L=u([]),y=u({}),D=u(1),U=u({}),w=u(!1),q=u(),H=t=>Le.value[t],S=(t,i)=>i.value.toUpperCase().indexOf(t.toUpperCase())>=0,[k,{resetFields:_,setFieldsValue:n,validate:M,clearValidate:B}]=fe({schemas:Ce,showActionButtonGroup:!1,layout:"vertical",wrapperCol:{span:24}}),[V,{closeModal:G,setModalProps:m}]=ce(t=>T(null,null,function*(){if(D.value=1,w.value=!1,s.value!=="list"&&(yield _()),m({minHeight:500}),t.id){s.value="edit";let i=yield qe({id:t.id});if(i){if(i.result.credential){let O=JSON.parse(i.result.credential);O.secretKey&&(i.result.secretKey=O.secretKey),O.apiKey&&(i.result.apiKey=O.apiKey)}let I=i.result.provider,N=P.data.filter(O=>O.value.includes(I));N&&N.length>0&&(f.value=N[0].type,L.value=N[0][i.result.modelType]),i.result.modelType&&i.result.modelType==="LLM"&&(w.value=!0),i.result.modelParams&&(U.value=JSON.parse(i.result.modelParams)),x.value=!0,yield n(W({},i.result)),X()}}else x.value=!1,X(),s.value="list",L.value=[]}));Y();function Y(){pe("model_type").then(t=>{e.value=t,t[0].value!="all"&&e.value.unshift({text:"全部类型",value:"all"})})}function j(t){if(t=="all"){v.value=P.data;return}let i=P.data.filter(I=>I.type.includes(t));v.value=i}function X(){v.value=P.data}function $(t){s.value="add",y.value=t,p.value=t.title,f.value=t.type,setTimeout(()=>{n({provider:t.value,baseUrl:t.baseUrl})},100)}function ee(){return T(this,null,function*(){try{m({confirmLoading:!0});let t=yield M(),i={apiKey:t.apiKey,secretKey:t.secretKey};if(q.value){let I=q.value.emitChange();I&&(t.modelParams=JSON.stringify(I))}t.credential=JSON.stringify(i),t.id?(yield Oe(t),G(),a("success")):(t.provider=y.value.value,yield Ie(t),G(),a("success"))}catch(t){t.hasOwnProperty("errorFields")&&(D.value=1)}finally{m({confirmLoading:!1})}})}function ae(){s.value="list",G()}function te(t){return T(this,null,function*(){yield n({modelName:""}),yield B("modelName"),yield n({modelName:y.value[t+"DefaultValue"]}),L.value=y.value[t],t==="LLM"?w.value=!0:w.value=!1})}function le(){s.value==="add"&&(s.value="list")}function oe(t){return t?t.replaceAll(`
`,"<br>"):"暂无描述内容"}return{registerModal:V,modelTypeData:d,modelTypeOption:e,modelType:R,handleChange:j,modelTypeList:v,getImage:H,handleClick:$,dataIndex:s,providerName:p,save:ee,cancel:ae,registerForm:k,handleModelTypeChange:te,modelTypeAddOption:f,modelNameAddOption:L,goToList:le,modelTypeDisabled:x,activeKey:D,modelParams:U,modelParamsShow:w,modelParamsRef:q,filterOption:S,getTitle:oe}}},Ge={class:"modal"},Fe={class:"header"},Re={class:"header-title"},Ue={key:1,class:"add-header-title"},ke={key:2,style:{color:"#1f2329"}},Ne={key:0,class:"model-content"},Te={class:"model-header"},Ke={class:"flex"},Pe=["src"],He={class:"header-text"},Se={class:"model-content"},_e={key:0},Ve={key:1},Qe=["innerHTML"],Ze={style:{display:"flex","justify-content":"space-between"}};function Xe(l,a,d,e,x,R){const v=c("a-select-option"),s=c("a-select"),p=c("a-card"),f=c("a-col"),L=c("a-row"),y=c("a-tag"),D=c("a-tooltip"),U=c("AutoComplete"),w=c("BasicForm"),q=c("a-tab-pane"),H=c("AiModelSeniorForm"),S=c("a-tabs"),k=c("a-button"),_=c("BasicModal");return r(),b(_,{destroyOnClose:"",onRegister:e.registerModal,canFullscreen:!1,width:"600px",wrapClassName:"ai-model-modal"},ie({default:o(()=>[A("div",Ge,[A("div",Fe,[A("span",Re,[e.dataIndex==="list"||e.dataIndex==="add"?(r(),h("span",{key:0,class:se(e.dataIndex==="list"?"":"add-header-title pointer"),onClick:a[0]||(a[0]=(...n)=>e.goToList&&e.goToList(...n))},"选择供应商",2)):g("",!0),e.dataIndex==="add"?(r(),h("span",Ue," > ")):g("",!0),e.dataIndex==="add"?(r(),h("span",ke,"添加 "+K(e.providerName),1)):g("",!0)]),e.dataIndex==="list"?(r(),b(s,{key:0,bordered:!1,class:"header-select",size:"small",value:e.modelType,"onUpdate:value":a[1]||(a[1]=n=>e.modelType=n),onChange:e.handleChange},{default:o(()=>[(r(!0),h(Q,null,Z(e.modelTypeOption,n=>(r(),b(v,{value:n.value},{default:o(()=>[C(K(n.text),1)]),_:2},1032,["value"]))),256))]),_:1},8,["value","onChange"])):g("",!0)]),e.dataIndex==="list"?(r(),h("div",Ne,[E(L,{span:24},{default:o(()=>[(r(!0),h(Q,null,Z(e.modelTypeList,n=>(r(),b(f,{xxl:12,xl:12,lg:12,md:12,sm:12,xs:24},{default:o(()=>[E(p,{class:"model-card",onClick:M=>e.handleClick(n)},{default:o(()=>[A("div",Te,[A("div",Ke,[A("img",{src:e.getImage(n.value),class:"header-img"},null,8,Pe),A("div",He,K(n.title),1)])])]),_:2},1032,["onClick"])]),_:2},1024))),256))]),_:1})])):g("",!0),e.dataIndex==="add"||e.dataIndex==="edit"?(r(),b(S,{key:1,activeKey:e.activeKey,"onUpdate:activeKey":a[2]||(a[2]=n=>e.activeKey=n)},{default:o(()=>[(r(),b(q,{key:1,tab:"基础信息"},{default:o(()=>[A("div",Se,[E(w,{onRegister:e.registerForm},{modelType:o(({model:n,field:M})=>[E(s,{value:n[M],"onUpdate:value":B=>n[M]=B,onChange:e.handleModelTypeChange,disabled:e.modelTypeDisabled},{default:o(()=>[(r(!0),h(Q,null,Z(e.modelTypeAddOption,B=>(r(),b(v,{value:B},{default:o(()=>[B==="LLM"?(r(),h("span",_e,"语言模型")):(r(),h("span",Ve,"向量模型"))]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value","onChange","disabled"])]),modelName:o(({model:n,field:M})=>[E(U,{value:n[M],"onUpdate:value":B=>n[M]=B,options:e.modelNameAddOption,"filter-option":e.filterOption},{option:o(({value:B,label:V,descr:G,type:m})=>[E(D,{placement:"right",color:"#ffffff",overlayInnerStyle:{color:"#646a73"}},{title:o(()=>[A("div",{innerHTML:e.getTitle(G)},null,8,Qe)]),default:o(()=>[A("div",Ze,[A("span",null,K(V),1),A("div",null,[m&&m.indexOf("text")!=-1?(r(),b(y,{key:0,color:"#E8D7C3"},{default:o(()=>a[3]||(a[3]=[C("文本")])),_:1,__:[3]})):g("",!0),m&&m.indexOf("image")!=-1?(r(),b(y,{key:1,color:"#C3D9DC"},{default:o(()=>a[4]||(a[4]=[C("图像分析")])),_:1,__:[4]})):g("",!0),m&&m.indexOf("vector")!=-1?(r(),b(y,{key:2,color:"#D4E0D8"},{default:o(()=>a[5]||(a[5]=[C("向量")])),_:1,__:[5]})):g("",!0),m&&m.indexOf("embeddings")!=-1?(r(),b(y,{key:3,color:"#FFEBD3"},{default:o(()=>a[6]||(a[6]=[C("文本嵌入")])),_:1,__:[6]})):g("",!0)])])]),_:2},1024)]),_:2},1032,["value","onUpdate:value","options","filter-option"])]),_:1},8,["onRegister"])])]),_:1})),e.modelParamsShow?(r(),b(q,{key:2,tab:"高级配置"},{default:o(()=>[E(H,{ref:"modelParamsRef",modelParams:e.modelParams},null,8,["modelParams"])]),_:1})):g("",!0)]),_:1},8,["activeKey"])):g("",!0)])]),_:2},[e.dataIndex==="add"||e.dataIndex==="edit"?{name:"footer",fn:o(()=>[E(k,{onClick:e.cancel},{default:o(()=>a[7]||(a[7]=[C("关闭")])),_:1,__:[7]},8,["onClick"]),E(k,{onClick:e.save,type:"primary"},{default:o(()=>a[8]||(a[8]=[C("保存")])),_:1,__:[8]},8,["onClick"])]),key:"0"}:{name:"footer",fn:o(()=>[]),key:"1"}]),1032,["onRegister"])}const Je=me(De,[["render",Xe],["__scopeId","data-v-6a39bbfd"]]),da=Object.freeze(Object.defineProperty({__proto__:null,default:Je},Symbol.toStringTag,{value:"Module"}));export{Je as A,da as a,ra as d,Le as i,na as l};
