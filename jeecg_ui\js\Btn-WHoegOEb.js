var C=(n,e,d)=>new Promise((p,f)=>{var k=a=>{try{i(d.next(a))}catch(s){f(s)}},_=a=>{try{i(d.throw(a))}catch(s){f(s)}},i=a=>a.done?p(a.value):Promise.resolve(a.value).then(k,_);i((d=d.apply(n,e)).next())});import{d as b,e as M,ag as u,v as D,aB as l,ar as m,aD as o,k as t,at as g,aq as S,ah as P,G as r,au as w,F as T,q as v}from"./vue-vendor-dy9k-Yad.js";import{V as $,A as x}from"./antd-vue-vendor-me9YkNVC.js";import V from"./CurrentPermissionMode-DzGbOpfp.js";import{X as N,bD as W,B as q,ah as E,bC as F,a as I}from"./index-CCWaWN5g.js";import{A as U}from"./index-LZBOYjDt.js";import{P as j}from"./index-CtJ0w2CP.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const G=b({components:{Alert:x,PageWrapper:j,CurrentPermissionMode:V,Divider:$,Authority:U},setup(){const{hasPermission:n}=N(),e=W(),d=q(),p=E(),f=M(()=>d.getProjectConfig.permissionMode===F.BACK);function k(_){return C(this,null,function*(){const i="fakeToken"+_;p.setToken(i),p.getUserInfoAction(),e.changePermissionCode()})}return{hasPermission:n,permissionStore:e,switchToken:k,isBackPremissionMode:f}}});function K(n,e,d,p,f,k){const _=u("CurrentPermissionMode"),i=u("Divider"),a=u("Alert"),s=u("a-button"),y=u("Authority"),B=u("PageWrapper"),c=D("auth");return m(),l(B,{contentBackground:"",title:"按钮权限控制",contentClass:"p-4"},{default:o(()=>[t(_),g("p",null,[e[2]||(e[2]=r(" 当前拥有的code列表: ")),g("a",null,w(n.permissionStore.getPermCodeList),1)]),t(i),t(a,{class:"mt-4",type:"info",message:"点击后请查看按钮变化(必须处于后台权限模式才可测试此页面所展示的功能)","show-icon":""}),t(i),t(s,{type:"primary",class:"mr-2",onClick:e[0]||(e[0]=A=>n.switchToken(2)),disabled:!n.isBackPremissionMode},{default:o(()=>e[3]||(e[3]=[r(" 点击切换按钮权限(用户id为2) ")])),_:1,__:[3]},8,["disabled"]),t(s,{type:"primary",onClick:e[1]||(e[1]=A=>n.switchToken(1)),disabled:!n.isBackPremissionMode},{default:o(()=>e[4]||(e[4]=[r(" 点击切换按钮权限(用户id为1,默认) ")])),_:1,__:[4]},8,["disabled"]),n.isBackPremissionMode?(m(),S(T,{key:0},[t(i,null,{default:o(()=>e[5]||(e[5]=[r("组件方式判断权限")])),_:1,__:[5]}),t(y,{value:"1000"},{default:o(()=>[t(s,{type:"primary",class:"mx-4"},{default:o(()=>e[6]||(e[6]=[r(" 拥有code ['1000']权限可见 ")])),_:1,__:[6]})]),_:1}),t(y,{value:"2000"},{default:o(()=>[t(s,{color:"success",class:"mx-4"},{default:o(()=>e[7]||(e[7]=[r(" 拥有code ['2000']权限可见 ")])),_:1,__:[7]})]),_:1}),t(y,{value:["1000","2000"]},{default:o(()=>[t(s,{color:"error",class:"mx-4"},{default:o(()=>e[8]||(e[8]=[r(" 拥有code ['1000','2000']角色权限可见 ")])),_:1,__:[8]})]),_:1}),t(i,null,{default:o(()=>e[9]||(e[9]=[r("函数方式方式判断权限")])),_:1,__:[9]}),n.hasPermission("1000")?(m(),l(s,{key:0,type:"primary",class:"mx-4"},{default:o(()=>e[10]||(e[10]=[r(" 拥有code ['1000']权限可见 ")])),_:1,__:[10]})):P("",!0),n.hasPermission("2000")?(m(),l(s,{key:1,color:"success",class:"mx-4"},{default:o(()=>e[11]||(e[11]=[r(" 拥有code ['2000']权限可见 ")])),_:1,__:[11]})):P("",!0),n.hasPermission(["1000","2000"])?(m(),l(s,{key:2,color:"error",class:"mx-4"},{default:o(()=>e[12]||(e[12]=[r(" 拥有code ['1000','2000']角色权限可见 ")])),_:1,__:[12]})):P("",!0),t(i,null,{default:o(()=>e[13]||(e[13]=[r("指令方式方式判断权限(该方式不能动态修改权限.)")])),_:1,__:[13]}),v((m(),l(s,{type:"primary",class:"mx-4"},{default:o(()=>e[14]||(e[14]=[r(" 拥有code ['1000']权限可见 ")])),_:1,__:[14]})),[[c,"1000"]]),v((m(),l(s,{color:"success",class:"mx-4"},{default:o(()=>e[15]||(e[15]=[r(" 拥有code ['2000']权限可见 ")])),_:1,__:[15]})),[[c,"2000"]]),v((m(),l(s,{color:"error",class:"mx-4"},{default:o(()=>e[16]||(e[16]=[r(" 拥有code ['1000','2000']角色权限可见 ")])),_:1,__:[16]})),[[c,["1000","2000"]]])],64)):P("",!0)]),_:1})}const se=I(G,[["render",K],["__scopeId","data-v-eeb8ba51"]]);export{se as default};
