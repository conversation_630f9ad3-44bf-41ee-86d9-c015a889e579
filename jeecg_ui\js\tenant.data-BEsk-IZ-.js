var r=Object.defineProperty;var o=Object.getOwnPropertySymbols;var s=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var d=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,a=(e,t)=>{for(var n in t||(t={}))s.call(t,n)&&d(e,n,t[n]);if(o)for(var n of o(t))c.call(t,n)&&d(e,n,t[n]);return e};import{render as i}from"./renderUtils-D7XVOFwj.js";import{rules as l}from"./validator-B_KkcUnu.js";const h=[{title:"企业名称",dataIndex:"name",width:200,align:"left"},{title:"企业编号(ID)",dataIndex:"id",width:180},{title:"企业LOGO",dataIndex:"companyLogo",width:100,customRender:({text:e})=>e&&i.renderImage({text:e})},{dataIndex:"trade_dictText",title:"所属行业",width:150},{dataIndex:"companySize_dictText",title:"公司规模",width:100},{dataIndex:"createBy_dictText",title:"创建者(拥有者)",width:150},{title:"状态",dataIndex:"status_dictText",width:100}],f=[{field:"name",label:"企业名称",component:"Input",colProps:{span:8}},{field:"status",label:"状态",component:"Select",componentProps:{options:[{label:"正常",value:1},{label:"冻结",value:0}]},colProps:{span:8}}],I=[{field:"name",label:"企业名称",component:"Input",required:!0},{field:"id",label:"企业编号(ID)",component:"InputNumber",required:!0,ifShow:({values:e})=>e.id!=null},{field:"companyLogo",label:"组织LOGO",component:"JImageUpload",componentProps:{text:"logo"}},{field:"trade",label:"所属行业",component:"JDictSelectTag",componentProps:{dictCode:"trade"}},{field:"companySize",label:"公司规模",component:"JDictSelectTag",componentProps:{dictCode:"company_size"}},{field:"companyAddress",label:"公司地址",component:"InputTextArea",componentProps:{placeholder:"请输入公司地址",rows:4}},{field:"description",label:"公司简介",component:"InputTextArea",componentProps:{placeholder:"请输入公司简介",rows:4}},{field:"status",label:"状态",component:"RadioButtonGroup",defaultValue:1,componentProps:{options:[{label:"正常",value:1},{label:"冻结",value:0}]}}],b=[{title:"用户账号",dataIndex:"username",width:100,align:"left"},{title:"用户姓名",dataIndex:"realname",width:100},{title:"手机号码",dataIndex:"phone",width:100}],w=[{field:"username",label:"账号",component:"Input"},{field:"realname",label:"姓名",component:"Input"}],x=[{title:"套餐包名称",dataIndex:"packName",width:100},{title:"状态",dataIndex:"status",width:100,customRender:({text:e})=>e==="1"?"开启":"关闭"},{title:"备注",dataIndex:"remarks",width:150}],y=[{field:"packName",label:"套餐包名称",component:"JInput",colProps:{xxl:8}}],k=[{field:"packName",label:"套餐包名称",component:"Input"},{field:"permissionIds",label:"菜单列表",component:"JTreeSelect",componentProps:{dict:"sys_permission,name,id",pidField:"parent_id",multiple:!0,treeCheckAble:!0,treeCheckStrictly:!0,getPopupContainer:()=>document.body}},{field:"remarks",label:"描述",component:"InputTextArea"},{field:"status",label:"开启状态",component:"Switch",componentProps:{checkedValue:"1",checkedChildren:"开启",unCheckedValue:"0",unCheckedChildren:"关闭"},defaultValue:"1"},{field:"id",label:"开启状态",component:"Input",show:!1}],S=[{title:"企业名称",dataIndex:"name",width:100,align:"left"},{title:"企业编号(ID)",dataIndex:"id",width:100},{title:"企业LOGO",dataIndex:"companyLogo",width:100,customRender:({text:e})=>e&&i.renderImage({text:e})}],C=[{field:"name",label:"企业名称",component:"Input"}],P=[{title:"用户",dataIndex:"realname",width:200},{title:"部门",dataIndex:"departNames",width:200,ellipsis:!0,slots:{customRender:"departNames"}},{title:"职位",dataIndex:"positionNames",ellipsis:!0,width:200,slots:{customRender:"positionNames"}}],g=[{field:"id",label:"id",component:"Input",show:!1},{field:"username",label:"username",component:"Input",show:!1},{field:"realname",label:"姓名",component:"Input",dynamicDisabled:({values:e})=>!!e.id},{field:"phone",label:"手机",component:"Input",dynamicRules:({model:e,schema:t})=>e.id?[]:[a({},l.phone(!0)[0]),a({},l.duplicateCheckRule("sys_user","phone",e,t,!1)[0])],dynamicDisabled:({values:e})=>!!e.id},{field:"email",label:"邮箱",component:"Input",dynamicRules:({model:e,schema:t})=>e.id?[]:[a({},l.email(!0)[0]),a({},l.duplicateCheckRule("sys_user","email",e,t,!1)[0])],dynamicDisabled:({values:e})=>!!e.id},{field:"selecteddeparts",label:"部门",component:"JSelectDept",componentProps:{checkStrictly:!0}},{field:"post",label:"职位",component:"JSelectPosition"},{field:"workNo",label:"工号",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入工号"},a({},l.duplicateCheckRule("sys_user","work_no",e,t,!1)[0])]},{field:"relTenantIds",label:"租户",component:"Input",show:!1},{field:"selectedroles",label:"角色",component:"Input",show:!1}];export{b as a,f as b,h as c,x as d,k as e,I as f,P as g,y as p,S as r,C as s,g as t,w as u};
