import{d as n,ag as a,aq as c,ar as d,k as r,aD as i,G as s}from"./vue-vendor-dy9k-Yad.js";import{u}from"./index-BkGZ5fiW.js";import{getTreeTableData as f,getBasicColumns as _}from"./tableData-B4J3mkj4.js";import b from"./BasicTable-xCEZpGLb.js";import{a as T}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const g=n({components:{BasicTable:b},setup(){const[t,{expandAll:o,collapseAll:e}]=u({title:"树形表格",isTreeTable:!0,rowSelection:{type:"checkbox",getCheckboxProps(p){return p.id==="0"?{disabled:!0}:{disabled:!1}}},titleHelpMessage:"树形组件不能和序列号列同时存在",columns:_(),dataSource:f(),rowKey:"id"});return{register:t,expandAll:o,collapseAll:e}}}),k={class:"p-4"};function C(t,o,e,p,x,y){const m=a("a-button"),l=a("BasicTable");return d(),c("div",k,[r(l,{onRegister:t.register},{toolbar:i(()=>[r(m,{type:"primary",onClick:t.expandAll},{default:i(()=>o[0]||(o[0]=[s("展开全部")])),_:1,__:[0]},8,["onClick"]),r(m,{type:"primary",onClick:t.collapseAll},{default:i(()=>o[1]||(o[1]=[s("折叠全部")])),_:1,__:[1]},8,["onClick"])]),_:1},8,["onRegister"])])}const Do=T(g,[["render",C]]);export{Do as default};
