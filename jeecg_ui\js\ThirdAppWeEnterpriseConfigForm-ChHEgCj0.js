var C=(o,e,l)=>new Promise((v,u)=>{var g=i=>{try{a(l.next(i))}catch(p){u(p)}},m=i=>{try{a(l.throw(i))}catch(p){u(p)}},a=i=>i.done?v(i.value):Promise.resolve(i.value).then(g,m);a((l=l.apply(o,e)).next())});import{d as A,f as B,o as T,ag as f,aq as k,ar as I,F as S,at as t,k as s,aD as d,G as b}from"./vue-vendor-dy9k-Yad.js";import{b as E}from"./ThirdApp.api-3uEF4_Sr.js";import W from"./ThirdAppConfigModal-axPPClFk.js";import"./index-Diw57m_E.js";import{ad as M,bo as w,u as D,a as x}from"./index-CCWaWN5g.js";import z from"./ThirdAppBindWeEnterpriseModal-D8FTJSQr.js";import{M as _}from"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";const U=A({name:"ThirdAppWeEnterpriseConfigForm",components:{ThirdAppConfigModal:W,ThirdAppBindWeEnterpriseModal:z},setup(){const o=B(!1),e=B({agentId:"",clientId:"",clientSecret:""}),[l,{openModal:v}]=M(),[u,{openModal:g}]=M(),{createMessage:m}=D();function a(n){return C(this,null,function*(){let c=yield E(n);c&&(e.value=c)})}function i(){return C(this,null,function*(){let n=w();v(!0,{tenantId:n,thirdType:"wechat_enterprise"})})}function p(){return C(this,null,function*(){g(!0,{izBind:!1})})}function y(){let n=w();a({tenantId:n,thirdType:"wechat_enterprise"})}function h(n,c){c.success?n!=null?_.success(n):m.warning(c.message):n&&n.title?_.warning(n):m.warning({content:"同步失败，请检查对接信息录入中是否填写正确，并确认是否已开启企业微信配置！",duration:5})}function r(){g(!0,{izBind:!0})}return T(()=>{let n=w();a({tenantId:n,thirdType:"wechat_enterprise"})}),{appConfigData:e,weEnterpriseEditClick:i,registerAppConfigModal:l,registerBindAppConfigModal:u,handleSuccess:y,btnLoading:o,thirdUserByWechat:p,handleBindSuccess:h,seeBindWeChat:r}}}),F={class:"base-collapse"},L={class:"flex-flow"},R={class:"base-message"},$={class:"flex-flow"},q={class:"base-message"},N={class:"flex-flow"},V={class:"base-message"},j={style:{"margin-top":"20px",width:"100%","text-align":"right"}},G={class:"sync-padding"},K={style:{"margin-top":"20px"},class:"base-desc"},H={style:{float:"right"}};function J(o,e,l,v,u,g){const m=f("a-collapse-panel"),a=f("a-collapse"),i=f("a-input-password"),p=f("a-button"),y=f("ThirdAppConfigModal"),h=f("ThirdAppBindWeEnterpriseModal");return I(),k(S,null,[t("div",F,[e[15]||(e[15]=t("div",{class:"header"}," 企业微信集成 ",-1)),s(a,{"expand-icon-position":"right",bordered:!1},{default:d(()=>[s(m,{key:"1"},{header:d(()=>e[4]||(e[4]=[t("div",{style:{"font-size":"16px"}}," 1.获取对接信息",-1)])),default:d(()=>[e[5]||(e[5]=t("div",{class:"base-desc"},"从企业微信平台获取对接信息，即可开始集成以及同步通讯录",-1)),e[6]||(e[6]=t("div",{style:{"margin-top":"5px"}},[t("a",{href:"https://help.qiaoqiaoyun.com/expand/dingding.html",target:"_blank"},"如何获取对接信息?")],-1))]),_:1,__:[5,6]})]),_:1}),t("div",null,[s(a,{"expand-icon-position":"right",bordered:!1},{default:d(()=>[s(m,{key:"2"},{header:d(()=>e[7]||(e[7]=[t("div",{style:{width:"100%","justify-content":"space-between",display:"flex"}},[t("div",{style:{"font-size":"16px"}}," 2.对接信息录入")],-1)])),default:d(()=>[t("div",L,[e[8]||(e[8]=t("div",{class:"base-title"},"Agentld",-1)),t("div",R,[s(i,{value:o.appConfigData.agentId,"onUpdate:value":e[0]||(e[0]=r=>o.appConfigData.agentId=r),readonly:""},null,8,["value"])])]),t("div",$,[e[9]||(e[9]=t("div",{class:"base-title"},"AppKey",-1)),t("div",q,[s(i,{value:o.appConfigData.clientId,"onUpdate:value":e[1]||(e[1]=r=>o.appConfigData.clientId=r),readonly:""},null,8,["value"])])]),t("div",N,[e[10]||(e[10]=t("div",{class:"base-title"},"AppSecret",-1)),t("div",V,[s(i,{value:o.appConfigData.clientSecret,"onUpdate:value":e[2]||(e[2]=r=>o.appConfigData.clientSecret=r),readonly:""},null,8,["value"])])]),t("div",j,[s(p,{onClick:o.weEnterpriseEditClick},{default:d(()=>e[11]||(e[11]=[b("编辑")])),_:1,__:[11]},8,["onClick"])])]),_:1})]),_:1}),t("div",G,[e[14]||(e[14]=t("div",{style:{"font-size":"16px",width:"100%"}}," 3.数据同步",-1)),t("div",K,[e[13]||(e[13]=b(" 从企业微信同步到敲敲云 ")),t("a",{style:{"margin-left":"10px"},onClick:e[3]||(e[3]=(...r)=>o.seeBindWeChat&&o.seeBindWeChat(...r))},"查看已绑定的企业微信用户"),t("div",H,[s(p,{onLoading:o.btnLoading,onClick:o.thirdUserByWechat},{default:d(()=>e[12]||(e[12]=[b("同步")])),_:1,__:[12]},8,["onLoading","onClick"])])])])])]),s(y,{onRegister:o.registerAppConfigModal,onSuccess:o.handleSuccess},null,8,["onRegister","onSuccess"]),s(h,{onRegister:o.registerBindAppConfigModal,onSuccess:o.handleBindSuccess},null,8,["onRegister","onSuccess"])],64)}const Ye=x(U,[["render",J],["__scopeId","data-v-f0327ba7"]]);export{Ye as default};
