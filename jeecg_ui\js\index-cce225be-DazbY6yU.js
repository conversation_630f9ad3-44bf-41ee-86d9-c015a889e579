const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/index-1a2e8832-DUClKHCv.js","js/index-CCWaWN5g.js","js/vue-vendor-dy9k-Yad.js","js/antd-vue-vendor-me9YkNVC.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/useHeaderSetting-C-h5S52e.js","js/header-OZa5fSDc.js","js/dict.api-BW6kWzU4.js"])))=>i.map(i=>d[i]);
import{cs as S,aj as _,_ as $,N as b,F as I,ah as D,C as v,b5 as w,ap as A,H as M,bY as T}from"./index-CCWaWN5g.js";import{d as B,e as C,u as o,f as F,J as N,o as k,ag as i,aq as x,ar as L,F as R,k as l,ah as V,as as s,aD as z,at as u,aA as E,au as y}from"./vue-vendor-dy9k-Yad.js";import{a9 as P}from"./antd-vue-vendor-me9YkNVC.js";import{useHeaderSetting as U}from"./useHeaderSetting-C-h5S52e.js";import{N as W,L as j}from"./index-DBlzbq35.js";import q from"./LoginSelect-xAWftqo5.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const J=_(()=>$(()=>import("./index-1a2e8832-DUClKHCv.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8])),{loading:!0}),{t:O}=b(),G=B({name:"LayoutHeader",components:{Header:P.Header,AppLogo:T,LayoutBreadcrumb:j,UserDropDown:J,FullScreen:W,LoginSelect:q},props:{fixed:M.bool},setup(e){const{prefixCls:t}=I("layout-header"),g=D(),{getIsMixMode:f,getMenuWidth:m}=v(),{title:h}=w(),{getHeaderTheme:n}=U(),{getIsMobile:a}=A(),c=C(()=>{const r=o(n);return[t,{[`${t}--fixed`]:e.fixed,[`${t}--mobile`]:o(a),[`${t}--${r}`]:r}]}),d=C(()=>!o(f)||o(a)?{}:{width:`${o(m)<180?180:o(m)}px`}),p=F();function H(){const r=N(g.getLoginInfo)||{};r.isLogin&&p.value.show(r)}return k(()=>{H()}),{t:O,title:h,loginSelectRef:p,prefixCls:t,getHeaderClass:c,getHeaderTheme:n,getIsMobile:a,getLogoWidth:d}}}),K={key:0,style:{height:"60px"}};function Y(e,t,g,f,m,h){const n=i("AppLogo"),a=i("FullScreen"),c=i("UserDropDown"),d=i("Header"),p=i("LoginSelect");return L(),x(R,null,[l(d,{class:s(e.getHeaderClass)},{default:z(()=>[u("div",{class:s(`${e.prefixCls}-left`)},[l(n,{class:s(`${e.prefixCls}-logo`),theme:e.getHeaderTheme,style:E(e.getLogoWidth)},null,8,["class","theme","style"]),u("span",{class:s([e.prefixCls,`${e.prefixCls}--${e.getHeaderTheme}`,"headerIntroductionClass"])},y(e.t("layout.header.welcomeIn"))+" "+y(e.title),3)],2),u("div",{class:s(`${e.prefixCls}-action`)},[l(a,{class:s(`${e.prefixCls}-action__item fullscreen-item`)},null,8,["class"]),l(c,{theme:e.getHeaderTheme},null,8,["theme"])],2)]),_:1},8,["class"]),l(p,{ref:"loginSelectRef"},null,512),e.fixed?(L(),x("div",K)):V("",!0)],64)}const fe=S(G,[["render",Y]]);export{fe as default};
