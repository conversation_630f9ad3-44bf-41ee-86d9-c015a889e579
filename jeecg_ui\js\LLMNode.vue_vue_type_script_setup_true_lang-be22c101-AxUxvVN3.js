import{d as k,aB as B,ar as C,u as e,aJ as h,aK as j,aD as q,k as o}from"./vue-vendor-dy9k-Yad.js";import"./NodeStyle-59980363-DzErCCzZ.js";import"./index-9c51646a-BviQbLw-.js";import"./antd-vue-vendor-me9YkNVC.js";import{x as y}from"./NodeContainer.vue_vue_type_style_index_0_lang-60bcf3b8-DFhy0o3x.js";import{_ as n}from"./NodeKV.vue_vue_type_script_setup_true_lang-6b8102ac-BLGiFDkQ.js";import"./NodeKV.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const D=k({__name:"LLMNode",props:{node:{type:Object,required:!0},graph:{type:Object,required:!0}},setup(l){return(x,L)=>(C(),B(e(y),h(j(x.$props)),{default:q(()=>{var r,a,i,u,t,p,d,s,v,m,c,f,b,g,_;return[o(e(n),{label:"模型",value:(t=(u=(i=(a=(r=l.node)==null?void 0:r.properties)==null?void 0:a.options)==null?void 0:i.model)==null?void 0:u.params)==null?void 0:t.model},null,8,["value"]),o(e(n),{label:"系统",value:(m=(v=(s=(d=(p=l.node)==null?void 0:p.properties)==null?void 0:d.options)==null?void 0:s.messages)==null?void 0:v[0])==null?void 0:m.content},null,8,["value"]),o(e(n),{label:"用户",value:(_=(g=(b=(f=(c=l.node)==null?void 0:c.properties)==null?void 0:f.options)==null?void 0:b.messages)==null?void 0:g[1])==null?void 0:_.content},null,8,["value"])]}),_:1},16))}});export{D as R};
