var ee=Object.defineProperty,te=Object.defineProperties;var oe=Object.getOwnPropertyDescriptors;var S=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable;var R=(n,t,o)=>t in n?ee(n,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[t]=o,v=(n,t)=>{for(var o in t||(t={}))ne.call(t,o)&&R(n,o,t[o]);if(S)for(var o of S(t))re.call(t,o)&&R(n,o,t[o]);return n},A=(n,t)=>te(n,oe(t));var u=(n,t,o)=>new Promise((l,p)=>{var y=s=>{try{c(o.next(s))}catch(d){p(d)}},x=s=>{try{c(o.throw(s))}catch(d){p(d)}},c=s=>s.done?l(s.value):Promise.resolve(s.value).then(y,x);c((o=o.apply(n,t)).next())});import{d as P,f as T,ag as _,aq as ie,ar as q,k as i,aD as a,u as m,aB as ae,ah as le,G as h,n as se}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as ue}from"./useListPage-Soxgnx9a.js";import{a as F}from"./index-JbqXEynz.js";import pe from"./MenuDrawer-BNzl65Pj.js";import me from"./DataRuleList-CoYsKmAa.js";import{h as ce,i as U,j as de,l as fe,k as _e}from"./menu.data-Dh4mGeCS.js";import{b7 as he,cc as Ue,j as B,N as ye}from"./index-CCWaWN5g.js";import{Q as xe}from"./componentMap-Bkie1n3v.js";import we from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./useAdaptiveWidth-SDQVNQ1K.js";import"./renderUtils-D7XVOFwj.js";import"./DataRuleModal-BlROFYgp.js";const ge=he({id:"defIndex",state:()=>({url:"",component:""}),getters:{},actions:{query(){return u(this,null,function*(){const n=yield N.query();this.url=n.url,this.component=n.component})},update(n,t,o){return u(this,null,function*(){yield N.update(n,t,o),yield this.query()})},check(n){return n===this.url}}});const N={query(){return u(this,null,function*(){return yield B.get({url:"/sys/sysRoleIndex/queryDefIndex"})})},update(n,t,o){return u(this,null,function*(){let l="/sys/sysRoleIndex/updateDefIndex";return l+="?url="+n,l+="&component="+t,l+="&isRoute="+o,yield B.put({url:l})})}},be=P({name:"system-menu"}),Et=P(A(v({},be),{setup(n){const t=T([]),o=T(!0),[l,{openDrawer:p}]=F(),[y,{openDrawer:x}]=F(),{t:c}=ye();U[0].customRender=function({text:e,record:r}){return C(r)&&(e+="（默认首页）"),e.includes("t('")&&c?new Function("t",`return ${e}`)(c):e};const{prefixCls:s,tableContext:d}=ue({tableProps:{title:"菜单列表",api:fe,columns:U,size:"small",pagination:!1,isTreeTable:!0,striped:!0,useSearchForm:!0,showTableSetting:!1,bordered:!0,showIndexColumn:!1,tableSetting:{fullScreen:!0},formConfig:{labelWidth:74,rowProps:{gutter:24},schemas:de,autoAdvancedCol:4,baseColProps:{xs:24,sm:12,md:6,lg:6,xl:6,xxl:6},actionColOptions:{xs:24,sm:12,md:6,lg:6,xl:6,xxl:6}},actionColumn:{width:120}}}),[E,{reload:w,expandAll:b,collapseAll:K}]=d,O={type:"checkbox",columnWidth:30,selectedRowKeys:t,onChange:V};function V(e){t.value=e}function W(){o.value=!0,p(!0,{isUpdate:!1})}function $(e){o.value=!0,p(!0,{record:e,isUpdate:!0})}function Ce(e){o.value=!1,p(!0,{record:e,isUpdate:!0})}function j(e){p(!0,{record:{parentId:e.id,menuType:1},isUpdate:!1})}function H(e){x(!0,{id:e.id})}function M(e){return u(this,null,function*(){yield _e({id:e.id},w)})}function z(){return u(this,null,function*(){yield ce({ids:t.value},()=>{w(),t.value=[]})})}function G(){w(),D()}function De(){se(b)}const g=ge();function L(e){return u(this,null,function*(){g.update(e.url,e.component,e.route)})}function C(e){return g.check(e.url)}function D(){try{g.query()}catch(e){}}D();function Q(e){return[{label:"编辑",onClick:$.bind(null,e)}]}function J(e){return[{label:"添加下级",onClick:j.bind(null,e)},{label:"数据规则",onClick:H.bind(null,e)},{label:"设为默认首页",onClick:L.bind(null,e),ifShow:()=>!e.internalOrExternal&&e.component&&!C(e)},{label:"删除",color:"error",popConfirm:{title:"是否确认删除",confirm:M.bind(null,e)}}]}return(e,r)=>{const f=_("a-button"),k=_("Icon"),X=_("a-menu-item"),Y=_("a-menu"),Z=_("a-dropdown");return q(),ie("div",null,[i(m(we),{onRegister:m(E),rowSelection:O},{tableTitle:a(()=>[i(f,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:W},{default:a(()=>r[0]||(r[0]=[h(" 新增菜单")])),_:1,__:[0]}),i(f,{type:"primary",preIcon:"ic:round-expand",onClick:m(b)},{default:a(()=>r[1]||(r[1]=[h("展开全部")])),_:1,__:[1]},8,["onClick"]),i(f,{type:"primary",preIcon:"ic:round-compress",onClick:m(K)},{default:a(()=>r[2]||(r[2]=[h("折叠全部")])),_:1,__:[2]},8,["onClick"]),t.value.length>0?(q(),ae(Z,{key:0},{overlay:a(()=>[i(Y,null,{default:a(()=>[i(X,{key:"1",onClick:z},{default:a(()=>[i(k,{icon:"ant-design:delete-outlined"}),r[3]||(r[3]=h(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:a(()=>[i(f,null,{default:a(()=>[r[4]||(r[4]=h("批量操作 ")),i(k,{icon:"ant-design:down-outlined"})]),_:1,__:[4]})]),_:1})):le("",!0)]),action:a(({record:I})=>[i(m(xe),{actions:Q(I),dropDownActions:J(I)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister"]),i(pe,{onRegister:m(l),onSuccess:G,showFooter:o.value},null,8,["onRegister","showFooter"]),i(me,{onRegister:m(y)},null,8,["onRegister"])])}}}));export{Et as default};
