import{d as o,f as l,w as s,aB as u,ar as m,u as d,aD as c,at as f,aA as p}from"./vue-vendor-dy9k-Yad.js";import{J as h}from"./antd-vue-vendor-me9YkNVC.js";import{useECharts as g}from"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const b=o({__name:"SalesProductPie",props:{loading:Boolean,width:{type:String,default:"100%"},height:{type:String,default:"300px"}},setup(e){const a=e,t=l(null),{setOptions:r}=g(t);return s(()=>a.loading,()=>{a.loading||r({tooltip:{trigger:"item"},series:[{name:"访问来源",type:"pie",radius:"80%",center:["50%","50%"],color:["#5ab1ef","#b6a2de","#67e0e3","#2ec7c9"],data:[{value:500,name:"电子产品"},{value:310,name:"服装"},{value:274,name:"化妆品"},{value:400,name:"家居"}].sort(function(i,n){return i.value-n.value}),roseType:"radius",animationType:"scale",animationEasing:"exponentialInOut",animationDelay:function(){return Math.random()*400}}]})},{immediate:!0}),(i,n)=>(m(),u(d(h),{title:"成交占比",loading:e.loading},{default:c(()=>[f("div",{ref_key:"chartRef",ref:t,style:p({width:e.width,height:e.height})},null,4)]),_:1},8,["loading"]))}});export{b as default};
