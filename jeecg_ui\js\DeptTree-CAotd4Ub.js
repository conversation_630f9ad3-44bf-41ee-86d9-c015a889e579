var p=(o,r,e)=>new Promise((n,a)=>{var s=t=>{try{c(e.next(t))}catch(m){a(m)}},i=t=>{try{c(e.throw(t))}catch(m){a(m)}},c=t=>t.done?n(t.value):Promise.resolve(t.value).then(s,i);c((e=e.apply(o,r)).next())});import{d as l,f,o as d,ag as _,aq as u,ar as h,k as D}from"./vue-vendor-dy9k-Yad.js";import{_ as T}from"./index-BtIdS_Qz.js";import{a as k}from"./system-bqUZCbh5.js";import{a as B}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./vxe-table-vendor-B22HppNm.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";const $=l({name:"DeptTree",components:{BasicTree:T},emits:["select"],setup(o,{emit:r}){const e=f([]);function n(){return p(this,null,function*(){e.value=yield k()})}function a(s){r("select",s[0])}return d(()=>{n()}),{treeData:e,handleSelect:a}}}),v={class:"m-4 mr-0 overflow-hidden bg-white"};function w(o,r,e,n,a,s){const i=_("BasicTree");return h(),u("div",v,[D(i,{title:"部门列表",toolbar:"",search:"",clickRowToExpand:!1,treeData:o.treeData,fieldNames:{key:"id",title:"deptName"},onSelect:o.handleSelect},null,8,["treeData","onSelect"])])}const F=B($,[["render",w]]);export{F as default};
