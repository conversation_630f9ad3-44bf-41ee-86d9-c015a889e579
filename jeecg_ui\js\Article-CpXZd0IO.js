import{d as k,ag as n,aB as i,ar as s,as as a,aD as r,aq as p,F as m,aC as u,k as v,at as c,au as l,G as d,ah as x}from"./vue-vendor-dy9k-Yad.js";import{X as y,L as _}from"./antd-vue-vendor-me9YkNVC.js";import{c as g,a as B}from"./index-CCWaWN5g.js";import{actions as M,articleList as N}from"./data-BsYYuWeG.js";import"./vxe-table-vendor-B22HppNm.js";const T=k({components:{List:_,ListItem:_.Item,ListItemMeta:_.Item.Meta,Tag:y,Icon:g},setup(){return{prefixCls:"account-center-article",list:N,actions:M}}});function V(e,h,D,F,b,q){const f=n("Tag"),C=n("ListItemMeta"),I=n("Icon"),L=n("ListItem"),$=n("List");return s(),i($,{"item-layout":"vertical",class:a(e.prefixCls)},{default:r(()=>[(s(!0),p(m,null,u(e.list,o=>(s(),i(L,{key:o.title},{default:r(()=>[v(C,null,{description:r(()=>[c("div",{class:a(`${e.prefixCls}__content`)},l(o.content),3)]),title:r(()=>[c("p",{class:a(`${e.prefixCls}__title`)},l(o.title),3),c("div",null,[(s(!0),p(m,null,u(o.description,t=>(s(),i(f,{key:t,class:"mb-2"},{default:r(()=>[d(l(t),1)]),_:2},1024))),128))])]),_:2},1024),c("div",null,[(s(!0),p(m,null,u(e.actions,t=>(s(),p("div",{key:t.text,class:a(`${e.prefixCls}__action`)},[t.icon?(s(),i(I,{key:0,class:a(`${e.prefixCls}__action-icon`),icon:t.icon,color:t.color},null,8,["class","icon","color"])):x("",!0),d(" "+l(t.text),1)],2))),128)),c("span",{class:a(`${e.prefixCls}__time`)},l(o.time),3)])]),_:2},1024))),128))]),_:1},8,["class"])}const S=B(T,[["render",V],["__scopeId","data-v-962c4cf4"]]);export{S as default};
