import{d as h,f as g,ag as w,aB as T,ar as k,aE as C}from"./vue-vendor-dy9k-Yad.js";import{cs as q,bx as r}from"./index-CCWaWN5g.js";import{L as _}from"./useTableSync-075826a1-CL-4GwR8.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var x=Object.defineProperty,V=Object.defineProperties,F=Object.getOwnPropertyDescriptors,b=Object.getOwnPropertySymbols,P=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable,m=(e,t,l)=>t in e?x(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,O=(e,t)=>{for(var l in t||(t={}))P.call(t,l)&&m(e,l,t[l]);if(b)for(var l of b(t))D.call(t,l)&&m(e,l,t[l]);return e},H=(e,t)=>V(e,F(t));const j=h({name:"QueryTable",emits:["query"],setup(e,{emit:t}){const l=g([{title:"字段名称",key:"dbFieldName",width:130},{title:"字段备注",key:"dbFieldTxt",width:130},{title:"控件类型",key:"queryShowType",width:170,type:r.select,options:[{title:"文本框",value:"text"},{title:"日期(yyyy-MM-dd)",value:"date"},{title:"日期（yyyy-MM-dd HH:mm:ss）",value:"datetime"},{title:"时间（HH:mm:ss）",value:"time"},{title:"日期-年",value:"date_year"},{title:"日期-月",value:"date_month"},{title:"日期-周",value:"date_week"},{title:"日期-季度",value:"date_quarter"},{title:"下拉框",value:"list"},{title:"下拉多选框",value:"list_multi"},{title:"下拉搜索框",value:"sel_search"},{title:"分类字典树",value:"cat_tree"},{title:"Popup弹框",value:"popup"},{title:"部门选择",value:"sel_depart"},{title:"用户选择",value:"sel_user"},{title:"省市区组件",value:"pca"},{title:"自定义树控件",value:"sel_tree"},{title:"开关",value:"switch"},{title:"Popup字典",value:"popup_dict"}],defaultValue:"text",placeholder:"请选择${title}",validateRules:[{handler:d}]},{title:"字典Table",key:"queryDictTable",width:130,type:r.textarea,defaultValue:""},{title:"字典Code",key:"queryDictField",width:130,type:r.input,defaultValue:""},{title:"字典Text",key:"queryDictText",width:130,type:r.input,defaultValue:""},{title:"默认值",key:"queryDefVal",width:130,type:r.input,defaultValue:""},{title:"是否启用",key:"queryConfigFlag",minWidth:80,type:r.checkbox,customValue:["1","0"],defaultChecked:!1,props:{isDisabledCell({row:a,column:u}){let{pageTable:i,dbTable:f,fkTable:v}=s;const c=i.value.tableRef.getTableData({rowIds:[a.id]})[0];if(["link_table"].includes(c==null?void 0:c.fieldShowType))return a.queryConfigFlag="0",!0;const y=f.value.tableRef.getTableData({rowIds:[a.id]})[0];if((y==null?void 0:y.dbIsPersist)=="0")return a.queryConfigFlag="0",!0;const o=v.value.tableRef.getTableData({rowIds:[a.id]})[0];return!!(o!=null&&o.mainTable&&o!=null&&o.mainField)}}}]),n=_(l),{tables:s}=n;function p({row:a,column:u,value:i}){u.key==="queryConfigFlag"&&i==="1"&&t("query",a.id)}function d({cellValue:a,row:u},i){a==null&&u.queryConfigFlag=="1"&&i(!1,"查询启用状态下，控件类型必选~"),i(!0)}return H(O({},n),{columns:l,handleChange:p})}});function I(e,t,l,n,s,p){const d=w("JVxeTable");return k(),T(d,C({ref:"tableRef",rowNumber:"",keyboardEdit:"",maxHeight:e.tableHeight.noToolbar,loading:e.loading,columns:e.columns,dataSource:e.dataSource,disabledRows:{dbFieldName:["id","has_child"]},onValueChange:e.handleChange},e.tableProps),null,16,["maxHeight","loading","columns","dataSource","onValueChange"])}const N=q(j,[["render",I]]);export{N as default};
