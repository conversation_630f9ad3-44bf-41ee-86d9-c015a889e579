var _=Object.defineProperty;var g=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable;var h=(e,o,t)=>o in e?_(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,v=(e,o)=>{for(var t in o||(o={}))k.call(o,t)&&h(e,t,o[t]);if(g)for(var t of g(o))x.call(o,t)&&h(e,t,o[t]);return e};var d=(e,o,t)=>new Promise((n,c)=>{var m=i=>{try{s(t.next(i))}catch(a){c(a)}},u=i=>{try{s(t.throw(i))}catch(a){c(a)}},s=i=>i.done?n(i.value):Promise.resolve(i.value).then(m,u);s((t=t.apply(e,o)).next())});import{d as C,c as I,f as B,e as U,aB as D,ar as O,u as p,aE as S,aD as b,k as j}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{B as L}from"./index-Diw57m_E.js";import{h as P}from"./depart.user.api-D_abnxSU.js";import{b as V}from"./depart.user.data-BFBNnnrj.js";import{u as q}from"./useForm-CgkFTrrO.js";import{ac as A}from"./index-CCWaWN5g.js";import{B as E}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./user.api-mLAlJze4.js";const Jt=C({__name:"DepartRoleModal",props:{departId:{require:!0,type:String}},emits:["success","register"],setup(e,{emit:o}){const t=o,n=e,c=I("prefixCls"),m=B(!0),u=B({}),s=U(()=>m.value?"编辑":"新增"),[i,{resetFields:a,setFieldsValue:w,validate:M,updateSchema:G}]=q({schemas:V,showActionButtonGroup:!1}),[R,{setModalProps:f,closeModal:y}]=A(r=>d(null,null,function*(){yield a(),m.value=p(r==null?void 0:r.isUpdate);let l=p(r==null?void 0:r.record);typeof l=="object"&&(u.value=l,yield w(v({},l)))}));function F(){return d(this,null,function*(){try{f({confirmLoading:!0});let r=yield M();r.departId=p(n.departId),yield P(r,m.value),y(),t("success",{isUpdate:p(m),values:r})}finally{f({confirmLoading:!1})}})}return(r,l)=>(O(),D(p(L),S({title:s.value,width:800},r.$attrs,{onOk:F,onRegister:p(R)}),{default:b(()=>[j(p(E),{onRegister:p(i)},null,8,["onRegister"])]),_:1},16,["title","onRegister"]))}});export{Jt as default};
