import{d as de,f as V,r as D,u as g,e as se,ag as w,aB as L,ar as F,aE as pe,aD as h,k as y,at as C,G as _}from"./vue-vendor-dy9k-Yad.js";import{B as ue}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{useJvxeMethod as ce}from"./useJvxeMethods-CdpRH_1y.js";import{cs as me,j as b,u as A,bD as fe,bx as n,ac as ge}from"./index-CCWaWN5g.js";import{a as he}from"./user.api-mLAlJze4.js";import{u as ye}from"./useForm-CgkFTrrO.js";import{B as be}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./vxeUtils-B1NxCh07.js";var xe=Object.defineProperty,ve=Object.defineProperties,Se=Object.getOwnPropertyDescriptors,M=Object.getOwnPropertySymbols,ke=Object.prototype.hasOwnProperty,we=Object.prototype.propertyIsEnumerable,J=(t,l,o)=>l in t?xe(t,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[l]=o,z=(t,l)=>{for(var o in l||(l={}))ke.call(l,o)&&J(t,o,l[o]);if(M)for(var o of M(l))we.call(l,o)&&J(t,o,l[o]);return t},Ce=(t,l)=>ve(t,Se(l)),R=(t,l,o)=>new Promise((x,c)=>{var f=p=>{try{s(o.next(p))}catch(S){c(S)}},v=p=>{try{s(o.throw(p))}catch(S){c(S)}},s=p=>p.done?x(p.value):Promise.resolve(p.value).then(f,v);s((o=o.apply(t,l)).next())});const{createConfirm:Ie}=A(),Pe="/online/cgreport/param/listByHeadId",Ve="/online/cgreport/item/listByHeadId",zt=t=>b.get({url:"/online/cgreport/head/list",params:t}),At=(t,l)=>b.delete({url:"/online/cgreport/head/delete",params:t},{joinParamsToUrl:!0}).then(()=>{l()}),Ht=(t,l)=>{Ie({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",iconType:"warning",onOk:()=>b.delete({url:"/online/cgreport/head/deleteBatch",data:t},{joinParamsToUrl:!0}).then(()=>{l()})})},_e=(t,l)=>l?b.put({url:"/online/cgreport/head/editAll",params:t}):b.post({url:"/online/cgreport/head/add",params:t}),Qt=t=>b.get({url:"/online/cgreport/api/getParamsInfo/"+t}),Ne=()=>b.get({url:"/sys/dataSource/options"}),$e=t=>b.get({url:"/online/cgreport/head/parseSql?"+t}),qe=fe(),Ut=[{title:"报表名字",align:"center",dataIndex:"name",width:120},{title:"报表编码",align:"center",dataIndex:"code",width:120},{title:"报表SQL",align:"center",dataIndex:"cgrSql",width:360},{title:"数据源",align:"center",dataIndex:"dbSource",customRender:({text:t,record:l})=>l.dbSource_dictText?l.dbSource_dictText:t,width:120},{title:"创建时间",align:"center",dataIndex:"createTime",width:120}],Et=[{label:"报表名称",field:"name",component:"JInput"},{label:"报表编码",field:"code",component:"JInput"}],Te=/^[a-z|A-Z][a-z|A-Z|\d|_|-]{0,}$/,Be=[{label:"",field:"id",component:"Input",show:!1},{label:"报表编码",field:"code",component:"Input",colProps:{sm:24,xs:24,md:12,lg:8,xl:8,xxl:8},dynamicRules:({values:t,model:l})=>[{required:!0,validator:(o,x)=>new Promise((c,f)=>{if(!x)return f("请输入报表编码！");if(!Te.test(x))return f("编码必须以字母开头，可包含数字、下划线、横杠！");let v={tableName:"onl_cgreport_head",fieldName:"code",fieldVal:x,dataId:l.id};he(v).then(s=>{s.success?c():f("报表编码已存在!")}).catch(s=>{f(s.message||"校验失败")})})}]},{label:"报表名字",field:"name",component:"Input",colProps:{sm:24,xs:24,md:12,lg:8,xl:8,xxl:8},dynamicRules:()=>[{required:!0,message:"请输入报表名字!"}]},{label:"动态数据源",field:"dbSource",colProps:{sm:24,xs:24,md:12,lg:8,xl:8,xxl:8},component:"ApiSelect",rules:[{required:qe.sysSafeMode,message:"请选择数据源！"}],componentProps:{api:Ne}},{label:"报表SQL",field:"cgrSql",component:"JCodeEditor",rules:[{required:!0,message:"请填写报表SQL"}],componentProps:{height:"200px",fullScreen:!0},colProps:{sm:24,xs:24,md:18,lg:16,xl:16,xxl:16}},{label:" ",field:"analyseButton",component:"Input",slot:"analyseButton",colProps:{xs:24,sm:24,md:6,lg:8,xl:8,xxl:8},itemProps:{labelCol:{xs:1,sm:1},wrapperCol:{xs:23,sm:23},colon:!1}}],Oe=[{title:"参数字段",key:"paramName",type:n.input,width:"200px",placeholder:"请输入${title}",defaultValue:"",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"参数文本",key:"paramTxt",type:n.input,width:"200px",placeholder:"请输入${title}",defaultValue:"",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"参数默认值",key:"paramValue",type:n.input,width:"200px",placeholder:"请输入${title}",defaultValue:""}],Le=[{title:"字段名字",key:"fieldName",type:n.input,minWidth:"150px",placeholder:"请输入${title}",defaultValue:"",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"字段文本",key:"fieldTxt",type:n.input,minWidth:"150px",placeholder:"请输入${title}",defaultValue:"",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"字段宽度",key:"fieldWidth",type:n.input,minWidth:"100px",defaultValue:""},{title:"字段类型",key:"fieldType",minWidth:"150px",placeholder:"请输入${title}",defaultValue:"",validateRules:[{required:!0,message:"${title}不能为空"}],type:n.select,options:[{title:"数值类型",value:"Integer"},{title:"字符类型",value:"String"},{title:"日期类型",value:"Date"},{title:"时间类型",value:"Datetime"},{title:"长整型",value:"Long"},{title:"图片类型",value:"Image"}]},{title:"是否显示",key:"isShow",minWidth:"80px",align:"center",type:n.checkbox,customValue:[1,0],defaultChecked:!0},{title:"字段href",key:"fieldHref",type:n.input,minWidth:"150px",placeholder:"请输入${title}",defaultValue:""},{title:"查询模式",key:"searchMode",type:n.select,minWidth:"150px",placeholder:"请选择${title}",options:[{title:"单条件查询",value:"single"},{title:"范围查询",value:"group"}]},{title:"取值表达式",key:"replaceVal",type:n.input,minWidth:"150px",placeholder:"请输入${title}",defaultValue:""},{title:"字典code",key:"dictCode",type:n.input,minWidth:"150px",placeholder:"请输入${title}",defaultValue:""},{title:"分组标题",key:"groupTitle",type:n.input,minWidth:"150px",placeholder:"请输入${title}",defaultValue:""},{title:"是否查询",key:"isSearch",type:n.checkbox,customValue:["1","0"],minWidth:"80px",align:"center",defaultChecked:!1},{title:"是否合计",align:"center",key:"isTotal",type:n.checkbox,customValue:["1","0"],minWidth:"80px",defaultChecked:!1}],Fe={style:{flex:"1","text-align":"left"}},Re=de({__name:"CgreportModal",emits:["register","success"],setup(t,{emit:l}){const{createMessage:o}=A(),x=l,c=V(!0),f=V(!0),v=V(["onlCgreportItem","onlCgreportParam"]),s=V("onlCgreportItem"),p=V(),S=V(),H={onlCgreportItem:S,onlCgreportParam:p},I=D({loading:!1,dataSource:[],columns:Oe}),P=D({loading:!1,dataSource:[],columns:Le}),[Q,{setProps:U,resetFields:E,setFieldsValue:G,validate:je,validateFields:K}]=ye({schemas:Be,showActionButtonGroup:!1,labelWidth:100,wrapperCol:null}),[Z,{setModalProps:N,closeModal:X}]=ge(e=>R(this,null,function*(){var r,i;yield re(),N({confirmLoading:!1,showCancelBtn:e==null?void 0:e.showFooter,showOkBtn:e==null?void 0:e.showFooter}),c.value=!!(e!=null&&e.isUpdate),g(c)&&(yield G(z({},e.record)),W(Pe,{headId:(r=e==null?void 0:e.record)==null?void 0:r.id},I),W(Ve,{headId:(i=e==null?void 0:e.record)==null?void 0:i.id},P)),U({disabled:!(e!=null&&e.showFooter)})})),[Y,ee,W,te]=ce(ae,oe,H,s,v),le=se(()=>g(c)?"编辑":"新增");function re(){return R(this,null,function*(){yield E(),s.value="onlCgreportItem",I.dataSource=[],P.dataSource=[]})}function oe(e){let r=Object.assign({},e.formValue);return Ce(z({},r),{onlCgreportParamList:e.tablesValue[1].tableData,onlCgreportItemList:e.tablesValue[0].tableData})}function ae(e){return R(this,null,function*(){try{N({confirmLoading:!0});let r=[],i=[],d={};Object.keys(e).map(a=>{a=="onlCgreportItemList"?i=e[a]:a=="onlCgreportParamList"?r=e[a]:d[a]=e[a]}),yield _e({head:d,params:r,items:i},c.value),X(),x("success")}finally{N({confirmLoading:!1})}})}function ie(){N({confirmLoading:!0}),K(["cgrSql","dbSource"]).then(e=>{let{cgrSql:r,dbSource:i}=e,d="sql="+encodeURIComponent(r);i&&(d+="&dbKey="+i),$e(d).then(a=>{if(a){o.success("解析成功");let{fields:k,params:u}=a,m=k.filter(q=>q.fieldName!="__row_number__"),T=S.value.getTableData(),$=j(T,m||[],"fieldName");$=$.sort((q,O)=>q.orderNum-O.orderNum),P.dataSource=$;let ne=p.value.getTableData(),B=j(ne,u||[],"paramName");B=B.sort((q,O)=>q.orderNum-O.orderNum),I.dataSource=B}})}).catch(()=>{}).finally(()=>{N({confirmLoading:!1})})}function j(e,r,i){if(e.length>0){let d=[],a=[],k=1;for(let u of r)for(let m of e)if(m[i]==u[i]){d.push(m),a.push(u[i]),m.orderNum>k&&(k=m.orderNum);break}for(let u of r)a.indexOf(u[i])<0&&(u.orderNum=++k,d.push(u));return d}else{let d=0;for(let a of r)a.orderNum||(a.orderNum=++d);return r}}return(e,r)=>{const i=w("a-icon"),d=w("a-popover"),a=w("a-button"),k=w("a-divider"),u=w("JVxeTable"),m=w("a-tab-pane"),T=w("a-tabs");return F(),L(g(ue),pe(e.$attrs,{onRegister:g(Z),title:le.value,width:1200,maskClosable:!1,defaultFullscreen:!0,confirmLoading:f.value,onOk:g(ee)}),{default:h(()=>[y(g(be),{onRegister:g(Q),ref_key:"formRef",ref:te},{analyseButton:h(()=>[C("div",Fe,[y(d,{title:"使用指南",trigger:"hover",style:{margin:"0 10px 0 6px"}},{content:h(()=>r[1]||(r[1]=[_(" 您可以键入“”作为一个参数，这里abc是参数的名称。例如："),C("br",null,null,-1),_(" select * from table where id = ${abc}。"),C("br",null,null,-1),_(" select * from table where id like concat('%',${abc},'%')。(mysql模糊查询)"),C("br",null,null,-1),_(" select * from table where id like '%'||${abc}||'%'。(oracle模糊查询)"),C("br",null,null,-1),_(" select * from table where id like '%'+${abc}+'%'。(sqlserver模糊查询)"),C("br",null,null,-1),C("span",{style:{color:"red"}},"注：参数只支持动态报表，popup暂不支持",-1)])),default:h(()=>[y(i,{type:"question-circle"})]),_:1}),y(a,{style:{"margin-left":"10px"},type:"primary",onClick:ie},{default:h(()=>r[2]||(r[2]=[_("SQL解析")])),_:1})])]),_:1},8,["onRegister"]),y(k,{style:{margin:"1px 0"},class:"cust-divider"}),y(T,{activeKey:s.value,"onUpdate:activeKey":r[0]||(r[0]=$=>s.value=$),animated:"",onChange:g(Y)},{default:h(()=>[(F(),L(m,{tab:"动态报表配置明细",key:v.value[0],forceRender:!0},{default:h(()=>[y(u,{"keep-source":"",dragSort:"",resizable:"",ref_key:"onlCgreportItem",ref:S,loading:P.loading,columns:P.columns,dataSource:P.dataSource,height:390,rowNumber:!0,rowSelection:!0,dragSortFixed:"none",rowNumberFixed:"none",rowSelectionFixed:"none",toolbar:!0},null,8,["loading","columns","dataSource"])]),_:1})),(F(),L(m,{tab:"报表参数",key:v.value[1],forceRender:!0},{default:h(()=>[y(u,{"keep-source":"",resizable:"",dragSort:"",ref_key:"onlCgreportParam",ref:p,loading:I.loading,columns:I.columns,dataSource:I.dataSource,height:390,rowNumber:!0,rowSelection:!0,dragSortFixed:"none",rowNumberFixed:"none",rowSelectionFixed:"none",toolbar:!0},null,8,["loading","columns","dataSource"])]),_:1}))]),_:1},8,["activeKey","onChange"])]),_:1},16,["onRegister","title","confirmLoading","onOk"])}}}),We=me(Re,[["__scopeId","data-v-e89d83a0"]]),Gt=Object.freeze(Object.defineProperty({__proto__:null,default:We},Symbol.toStringTag,{value:"Module"}));export{We as C,Gt as a,Ht as b,Ut as c,At as d,Qt as g,zt as l,Et as s};
