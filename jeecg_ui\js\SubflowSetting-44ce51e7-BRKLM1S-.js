import{d as j,f as q,ag as v,aq as w,ar as y,F as B,aB as A,k as l,at as p,aD as g,G as N,u as d,H as R}from"./vue-vendor-dy9k-Yad.js";import"./index-Diw57m_E.js";import{$ as D}from"./api-0389a176-CuvsoQ7H.js";import{b as E}from"./useSettings-4a774f12-DjycrGR6.js";import T from"./VarListPicker-4ef8c64a-BYzXoS8G.js";import U from"./VarListEditor-010e347e-DRyQV5fi.js";import{ad as Z}from"./index-CCWaWN5g.js";import"./VarTextarea.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import"./antd-vue-vendor-me9YkNVC.js";import"./VarEditable.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import{J as z}from"./AiAppAddFlowModal-BuhG0aib.js";import{s as G}from"./_plugin-vue_export-helper-dad06003-lGy7RumW.js";import"./_commonjsHelpers-ce4d82cc-RqGMvybJ.js";import"./VarPicker.vue_vue_type_script_setup_true_lang-5fb9829d-C8jb2Rkc.js";import"./index-a84962f9-BJDiPJBg.js";import"./VarListPicker.vue_vue_type_style_index_0_scoped_9a10b0de_lang-4ed993c7-l0sNRNKZ.js";import"./index-L3cSIXth.js";import"./VarListEditor.vue_vue_type_style_index_0_scoped_407b7ab3_lang-4ed993c7-l0sNRNKZ.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./knowledge-BXTupIwn.js";var H=(h,I,n)=>new Promise((u,m)=>{var x=e=>{try{s(n.next(e))}catch(f){m(f)}},_=e=>{try{s(n.throw(e))}catch(f){m(f)}},s=e=>e.done?u(e.value):Promise.resolve(e.value).then(x,_);s((n=n.apply(h,I)).next())});const J={key:1,class:"subflow-setting"},K={key:0,class:"setting-item"},L={class:"setting-item"},W={class:"setting-item"},X={class:"subflow-info"},Y={class:"setting-item"},Q=j({__name:"SubflowSetting",props:{type:{type:String,required:!0},node:{type:Object,required:!0},properties:{type:Object,required:!0},setProperties:{type:Function,required:!0}},setup(h){const I=h,n=q(!1),{inputParams:u,outputParams:m,prevVariables:x,createOptionRef:_}=E(I),s=_("subflowId"),e=q(null);function f(){const a=e.value.inputParams.filter(t=>t.field!=="history");return P(u,a,"name",(t,o)=>{const r={name:t.field,nameText:t.name,field:"",nodeId:""};if(o.has(r.name)){const c=o.get(r.name);r.field=c.field,r.nodeId=c.nodeId}return r})}function S(){return P(m,e.value.outputParams,"field",(a,t)=>{const o={field:a.name,name:a.name,type:a.type||"string"};if(t.has(o.field)){const r=t.get(o.field);o.name=r.name,o.type=r.type}return o})}function P(a,t,o,r){const c=[...a.value].filter(i=>!!i[o]),b=new Map;for(const i of c)b.set(i[o],i);const k=[];for(const i of t){const O=r(i,b);k.push(O)}a.value=k}function V(){return H(this,null,function*(){if(s.value)try{n.value=!0,e.value=yield D(s.value),f(),S()}catch(a){}finally{n.value=!1}})}V();const[F,M]=Z();function C(){M.openModal(!0,{})}function $(a){s.value=a.flowId,V()}return(a,t)=>{const o=v("a-skeleton"),r=v("a-button"),c=v("a-input"),b=v("a-space-compact"),k=v("a-space");return y(),w(B,null,[n.value?(y(),A(o,{key:0,active:""})):(y(),w("div",J,[e.value==null?(y(),w("div",K,[t[3]||(t[3]=p("div",{class:"label"},"子流程",-1)),l(r,{preIcon:"ant-design:plus",onClick:C},{default:g(()=>t[2]||(t[2]=[N("选择子流程")])),_:1})])):(y(),w(B,{key:1},[p("div",L,[t[4]||(t[4]=p("div",{class:"label"},"输入变量",-1)),l(d(T),{vars:d(u),"onUpdate:vars":t[0]||(t[0]=i=>R(u)?u.value=i:null),allowAdd:!1,allowDelete:!1,allowEditName:!1,prevVariables:d(x)},null,8,["vars","prevVariables"])]),p("div",W,[t[6]||(t[6]=p("div",{class:"label"},"子流程",-1)),p("div",X,[l(k,{direction:"vertical",size:"middle"},{default:g(()=>[l(b,{block:""},{default:g(()=>{var i;return[l(c,{value:(i=e.value)==null?void 0:i.name,readonly:"",style:{width:"480px"},onClick:C},null,8,["value"]),l(r,{onClick:C},{default:g(()=>t[5]||(t[5]=[N("重新选择")])),_:1})]}),_:1})]),_:1})])]),p("div",Y,[t[7]||(t[7]=p("div",{class:"label"},"输出变量",-1)),l(d(U),{type:"subflow",vars:d(m),"onUpdate:vars":t[1]||(t[1]=i=>R(m)?m.value=i:null)},null,8,["vars"])])],64))])),l(z,{onRegister:d(F),onSuccess:$},null,8,["onRegister"])],64)}}}),fe=G(Q,[["__scopeId","data-v-2753649d"]]);export{fe as default};
