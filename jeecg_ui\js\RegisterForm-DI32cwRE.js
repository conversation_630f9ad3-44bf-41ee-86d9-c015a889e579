var x=(b,i,u)=>new Promise((o,p)=>{var g=l=>{try{f(u.next(l))}catch(m){p(m)}},d=l=>{try{f(u.throw(l))}catch(m){p(m)}},f=l=>l.done?o(l.value):Promise.resolve(l.value).then(g,d);f((u=u.apply(b,i)).next())});import{d as z,f as k,r as F,e as P,u as e,aq as U,ah as B,ar as M,F as T,k as a,aD as n,G as y,au as w,J as N}from"./vue-vendor-dy9k-Yad.js";import L from"./LoginFormTitle-Bms3O5Qx.js";import{a5 as h,j as v,z as V,B as C}from"./antd-vue-vendor-me9YkNVC.js";import{S as D}from"./index-CBCjSSNZ.js";import{C as G}from"./index-DFrpKMGa.js";import{bO as j,bP as q,bU as A,N as J,u as O,bQ as Q,b_ as $,b6 as H,bS as K}from"./index-CCWaWN5g.js";import"./useCountdown-CCWNeb_r.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFormItemSingle-Cw668yj5.js";const we=z({__name:"RegisterForm",setup(b){const i=h.Item,u=v.Password,{t:o}=J(),{handleBackLogin:p,getLoginState:g}=j(),{notification:d,createErrorModal:f}=O(),l=k(),m=k(!1),t=F({account:"",password:"",confirmPassword:"",mobile:"",sms:"",policy:!1}),{getFormRules:S}=q(t),{validForm:R}=Q(l),E=P(()=>e(g)===A.REGISTER);function I(){return x(this,null,function*(){const c=yield R();if(c)try{m.value=!0;const s=yield $(N({username:c.account,password:c.password,phone:c.mobile,smscode:c.sms}));s&&s.data.success?(d.success({description:s.data.message||o("sys.api.registerMsg"),duration:3}),p()):d.warning({message:o("sys.api.errorTip"),description:s.data.message||o("sys.api.networkExceptionMsg"),duration:3})}catch(s){d.error({message:o("sys.api.errorTip"),description:s.message||o("sys.api.networkExceptionMsg"),duration:3})}finally{m.value=!1}})}function _(){return H({mobile:t.mobile,smsmode:K.REGISTER})}return(c,s)=>E.value?(M(),U(T,{key:0},[a(L,{class:"enter-x"}),a(e(h),{class:"p-4 enter-x",model:t,rules:e(S),ref_key:"formRef",ref:l},{default:n(()=>[a(e(i),{name:"account",class:"enter-x"},{default:n(()=>[a(e(v),{class:"fix-auto-fill",size:"large",value:t.account,"onUpdate:value":s[0]||(s[0]=r=>t.account=r),placeholder:e(o)("sys.login.userName")},null,8,["value","placeholder"])]),_:1}),a(e(i),{name:"mobile",class:"enter-x"},{default:n(()=>[a(e(v),{size:"large",value:t.mobile,"onUpdate:value":s[1]||(s[1]=r=>t.mobile=r),placeholder:e(o)("sys.login.mobile"),class:"fix-auto-fill"},null,8,["value","placeholder"])]),_:1}),a(e(i),{name:"sms",class:"enter-x"},{default:n(()=>[a(e(G),{size:"large",class:"fix-auto-fill",value:t.sms,"onUpdate:value":s[2]||(s[2]=r=>t.sms=r),placeholder:e(o)("sys.login.smsCode"),sendCodeApi:_},null,8,["value","placeholder"])]),_:1}),a(e(i),{name:"password",class:"enter-x"},{default:n(()=>[a(e(D),{size:"large",value:t.password,"onUpdate:value":s[3]||(s[3]=r=>t.password=r),placeholder:e(o)("sys.login.password")},null,8,["value","placeholder"])]),_:1}),a(e(i),{name:"confirmPassword",class:"enter-x"},{default:n(()=>[a(e(u),{size:"large",visibilityToggle:"",value:t.confirmPassword,"onUpdate:value":s[4]||(s[4]=r=>t.confirmPassword=r),placeholder:e(o)("sys.login.confirmPassword")},null,8,["value","placeholder"])]),_:1}),a(e(i),{class:"enter-x",name:"policy"},{default:n(()=>[a(e(V),{checked:t.policy,"onUpdate:checked":s[5]||(s[5]=r=>t.policy=r),size:"small"},{default:n(()=>[y(w(e(o)("sys.login.policy")),1)]),_:1},8,["checked"])]),_:1}),a(e(C),{type:"primary",class:"enter-x",size:"large",block:"",onClick:I,loading:m.value},{default:n(()=>[y(w(e(o)("sys.login.registerButton")),1)]),_:1},8,["loading"]),a(e(C),{size:"large",block:"",class:"mt-4 enter-x",onClick:e(p)},{default:n(()=>[y(w(e(o)("sys.login.backSignIn")),1)]),_:1},8,["onClick"])]),_:1},8,["model","rules"])],64)):B("",!0)}});export{we as default};
