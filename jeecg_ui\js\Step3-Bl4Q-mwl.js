import{d as m,ag as n,aq as u,ar as c,k as e,at as f,aD as s,G as o}from"./vue-vendor-dy9k-Yad.js";import{K as r,a2 as d}from"./antd-vue-vendor-me9YkNVC.js";import{a as b}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const x=m({components:{[d.name]:d,[r.name]:r,[r.Item.name]:r.Item},emits:["redo"],setup(l,{emit:t}){return{redo:()=>{t("redo")}}}}),k={class:"step3"},C={class:"desc-wrap"};function v(l,t,$,g,y,B){const _=n("a-button"),i=n("a-result"),a=n("a-descriptions-item"),p=n("a-descriptions");return c(),u("div",k,[e(i,{status:"success",title:"操作成功","sub-title":"预计两小时内到账"},{extra:s(()=>[e(_,{type:"primary",onClick:l.redo},{default:s(()=>t[0]||(t[0]=[o(" 再转一笔 ")])),_:1,__:[0]},8,["onClick"]),e(_,null,{default:s(()=>t[1]||(t[1]=[o(" 查看账单 ")])),_:1,__:[1]})]),_:1}),f("div",C,[e(p,{column:1,class:"mt-5"},{default:s(()=>[e(a,{label:"付款账户"},{default:s(()=>t[2]||(t[2]=[o(" <EMAIL> ")])),_:1,__:[2]}),e(a,{label:"收款账户"},{default:s(()=>t[3]||(t[3]=[o(" <EMAIL> ")])),_:1,__:[3]}),e(a,{label:"收款人姓名"},{default:s(()=>t[4]||(t[4]=[o(" Jeecg ")])),_:1,__:[4]}),e(a,{label:"转账金额"},{default:s(()=>t[5]||(t[5]=[o(" 500元 ")])),_:1,__:[5]})]),_:1})])])}const D=b(x,[["render",v],["__scopeId","data-v-e16fcdd0"]]);export{D as default};
