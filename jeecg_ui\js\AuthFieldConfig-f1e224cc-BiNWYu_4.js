import{d as q,f as v,w as K,ag as z,aq as L,ar as E,k as b,aD as f,G as S,aB as B,F as R,au as P}from"./vue-vendor-dy9k-Yad.js";import{u as V}from"./index-BkGZ5fiW.js";import{l as A,r as G,s as M,c as D,i as O}from"./auth.api-53df4c33-CWNFk1-w.js";import{S as Q}from"./auth.data-626c5083-DVuUJlaU.js";import{cs as W}from"./index-CCWaWN5g.js";import X from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";var C=(l,h,g)=>new Promise((x,k)=>{var p=s=>{try{m(g.next(s))}catch(c){k(c)}},w=s=>{try{m(g.throw(s))}catch(c){k(c)}},m=s=>s.done?x(s.value):Promise.resolve(s.value).then(p,w);m((g=g.apply(l,h)).next())});const j=q({name:"AuthFieldConfig",components:{BasicTable:X},props:{headId:{type:String,default:"",required:!0}},emits:["update:authFields"],setup(l,{emit:h}){const g=v(""),[x,{reload:k,getTableRef:p,setPagination:w}]=V({api:U,rowKey:"code",bordered:!0,columns:Q,showIndexColumn:!1}),m=v(!1),s=v(!1),c=v(!1),r=v(!1),u=v(!1),y=v(!1),I=v(!1);K(()=>l.headId,o=>{g.value=o.split("?")[0],p().value&&w({current:1,pageSize:10}),k().catch(()=>null)},{immediate:!0});function U(o){return C(this,null,function*(){const e=["id"];let n=yield A(g.value,o),a=[],i=[];return n.forEach(d=>{e.indexOf(d.code)<0&&((d.isShowForm==1||d.isShowList==1)&&i.push(d),d.dbIsPersist==1&&a.push({text:d.title,value:d.code,view:d.fieldShowType,dbType:d.dbType}))}),h("update:authFields",a),T(o.pageNo,o.pageSize,i),i})}function N(o,e){return C(this,null,function*(){yield G({cgformId:g.value,code:e.code,status:o?1:0}),e.formEditable||e.formShow||e.listShow||(e.formEditable=!0,e.formShow=!0,e.listShow=!0),e.status=Math.abs(e.status-1),_()})}function $(o,e,n){return C(this,null,function*(){let a=o.target.checked;yield M({cgformId:g.value,code:e.code,switchFlag:n,listShow:a,formShow:a,formEditable:a}),n==1?e.listShow=a:n==2?e.formShow=a:n==3&&(e.formEditable=a),e.listShow===!1&&e.formShow===!1&&e.formEditable===!1&&(e.status=0),_()})}function T(o,e,n){const a=[];if(n!=null&&n.length){const i=o*e>n.length?n.length:o*e;for(let d=o*e-e;d<i;d++){const t=n[d];a.push(t)}}a.length?(m.value=!0,s.value=!0,c.value=!0,a.forEach(i=>{m.value&&i.status==0&&(m.value=!1),s.value&&i.listShow==!1&&(s.value=!1),c.value&&(i.formEditable==!1||i.formShow==!1)&&(c.value=!1)}),s.value==!0?I.value=!1:a.find(i=>i.listShow)?I.value=!0:I.value=!1,c.value==!0?y.value=!1:a.find(i=>i.formEditable||i.formShow)?y.value=!0:y.value=!1):(m.value=!1,s.value=!1,c.value=!1)}const _=()=>{const{current:o,pageSize:e}=p().value.getPaginationRef(),n=p().value.getDataSource();T(o,e,n)},F=(o,e)=>{const n=[],a=p().value.getDataSource();if(a!=null&&a.length){const i=o*e>a.length?a.length:o*e;for(let d=o*e-e;d<i;d++){const t=a[d];n.push(t)}}return n};return{registerTable:x,onUpdateStatus:N,onCheckboxChange:$,handleChangeSwitch:o=>C(this,null,function*(){u.value=!0,m.value=o;const{current:e,pageSize:n}=p().value.getPaginationRef(),a=F(e,n);let i=a.map(t=>({cgformId:t.cgformId,code:t.code,status:o?1:0}));r.value=!0,yield O(i),a.forEach(t=>{o?t.status=1:t.status=0,t.formEditable||t.formShow||t.listShow||(t.formEditable=!0,t.formShow=!0,t.listShow=!0)}),r.value=!1,u.value=!1;const d=p().value.getDataSource();T(e,n,d)}),allSwitch:m,allFormControl:c,allListControl:s,allSloading:r,handleTableChange:o=>{},handleChangeList:o=>C(this,null,function*(){u.value=!0;const e=o.target.checked;s.value=e;const{current:n,pageSize:a}=p().value.getPaginationRef(),i=F(n,a);let d=i.map(t=>({cgformId:t.cgformId,code:t.code,switchFlag:1,listShow:!!e}));yield D(d),i.forEach(t=>{t.listShow=!!e,t.listShow===!1&&t.formShow===!1&&t.formEditable===!1&&(t.status=0,m.value=!1)}),e&&(I.value=!1),u.value=!1}),handleChangeForm:o=>C(this,null,function*(){u.value=!0;const e=o.target.checked;c.value=e;const{current:n,pageSize:a}=p().value.getPaginationRef(),i=F(n,a),d=[...i.map(t=>({cgformId:t.cgformId,code:t.code,switchFlag:4,formShow:!!e,formEditable:!!e}))];yield D(d),i.forEach(t=>{t.formEditable=!!e,t.formShow=!!e,t.listShow===!1&&t.formShow===!1&&t.formEditable===!1&&(t.status=0,m.value=!1)}),e&&(y.value=!1),u.value=!1}),tableLoading:u,formIndeterminate:y,listIndeterminate:I}}}),H={class:"auth-field-config"};function J(l,h,g,x,k,p){const w=z("a-switch"),m=z("a-checkbox"),s=z("BasicTable");return E(),L("div",H,[b(s,{onRegister:l.registerTable,onChange:l.handleTableChange,loading:l.tableLoading},{headerCell:f(({column:c})=>[c.dataIndex==="switch"?(E(),L(R,{key:0},[b(w,{loading:l.allSloading,checked:l.allSwitch,"onUpdate:checked":h[0]||(h[0]=r=>l.allSwitch=r),size:"small",onChange:l.handleChangeSwitch},null,8,["loading","checked","onChange"]),h[3]||(h[3]=S("启用 "))],64)):c.dataIndex==="list"?(E(),B(m,{key:1,indeterminate:l.listIndeterminate,checked:l.allListControl,"onUpdate:checked":h[1]||(h[1]=r=>l.allListControl=r),disabled:!l.allSwitch,onChange:l.handleChangeList},{default:f(()=>[S(P(c.customTitle),1)]),_:2},1032,["indeterminate","checked","disabled","onChange"])):c.dataIndex==="form"?(E(),B(m,{key:2,indeterminate:l.formIndeterminate,checked:l.allFormControl,"onUpdate:checked":h[2]||(h[2]=r=>l.allFormControl=r),disabled:!l.allSwitch,onChange:l.handleChangeForm},{default:f(()=>[S(P(c.customTitle),1)]),_:2},1032,["indeterminate","checked","disabled","onChange"])):(E(),L(R,{key:3},[S(P(c.customTitle),1)],64))]),switch:f(({text:c,record:r})=>[b(w,{size:"small",checked:r.status===1,onChange:u=>l.onUpdateStatus(u,r)},null,8,["checked","onChange"])]),list:f(({text:c,record:r})=>[b(m,{checked:r.listShow,disabled:r.status===0,onChange:u=>l.onCheckboxChange(u,r,1)},{default:f(()=>h[4]||(h[4]=[S(" 可见 ")])),_:2},1032,["checked","disabled","onChange"])]),form:f(({text:c,record:r})=>[b(m,{checked:r.formShow,disabled:r.status===0,onChange:u=>l.onCheckboxChange(u,r,2)},{default:f(()=>h[5]||(h[5]=[S(" 可见 ")])),_:2},1032,["checked","disabled","onChange"]),b(m,{checked:r.formEditable,disabled:r.status===0,onChange:u=>l.onCheckboxChange(u,r,3)},{default:f(()=>h[6]||(h[6]=[S(" 可编辑 ")])),_:2},1032,["checked","disabled","onChange"])]),_:1},8,["onRegister","onChange","loading"])])}const at=W(j,[["render",J],["__scopeId","data-v-c48da65c"]]);export{at as default};
