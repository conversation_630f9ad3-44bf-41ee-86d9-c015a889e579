import{f,r as T,ag as g,aq as C,ar as v,F as y,k as p,aD as s,at as M,aA as x,aC as b,G as u,au as F}from"./vue-vendor-dy9k-Yad.js";import{cs as R,u as _,ac as G,j as z,ad as I}from"./index-CCWaWN5g.js";import"./index-L3cSIXth.js";import{B as N}from"./index-Diw57m_E.js";import{ck as S}from"./antd-vue-vendor-me9YkNVC.js";import j from"./CodeFileViewModal-405e2b58-DtLwgNIA.js";import{downloadByData as H}from"./download-CZ-9H9a3.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./base64Conver-24EVOS6V.js";var K=(m,e,a)=>new Promise((o,d)=>{var c=t=>{try{r(a.next(t))}catch(l){d(l)}},n=t=>{try{r(a.throw(t))}catch(l){d(l)}},r=t=>t.done?o(t.value):Promise.resolve(t.value).then(c,n);r((a=a.apply(m,e)).next())});const P={name:"CodeFileListModal",components:{BasicModal:N,InfoCircleTwoTone:S,CodeFileViewModal:j},emits:["register"],setup(){const{createMessage:m}=_(),e=f([]),a=window.innerHeight-150,o=T({overflowY:"auto",maxHeight:a+"px"}),d=f(!1),c=f(""),n=f(""),[r,{closeModal:t}]=G(i=>K(this,null,function*(){e.value=i.codeList,c.value=i.tableName,n.value=i.pathKey}));function l(){t()}function w(){let i=e.value;if(!i||i.length==0){m.warning("无代码！");return}let B=i.join(",");return z.post({url:"/online/cgform/api/downGenerateCode",params:{fileList:encodeURI(B),pathKey:n.value},responseType:"blob"},{isTransformResponse:!1}).then(h=>{if(!h||h.size==0){m.warning("导出代码失败！");return}let L="导到生成代码_"+c.value+"_"+new Date().getTime()+".zip";H(h,L,"application/zip")})}const[k,{openModal:D}]=I();function V(){let i=e.value;D(!0,{codeList:i,pathKey:n.value})}return{registerModal:r,registerCodeViewModal:k,divStyle:o,codeList:e,onDownloadGenerateCode:w,handleClose:l,handleView:V,loading:d}}};function U(m,e,a,o,d,c){const n=g("info-circle-two-tone"),r=g("a-button"),t=g("BasicModal"),l=g("code-file-view-modal");return v(),C(y,null,[p(t,{onRegister:o.registerModal,width:1200,defaultFullscreen:!1,canFullscreen:!1},{title:s(()=>[p(n),e[0]||(e[0]=u(" 代码生成结果 "))]),footer:s(()=>[p(r,{onClick:o.handleClose},{default:s(()=>e[2]||(e[2]=[u("关闭")])),_:1},8,["onClick"]),p(r,{type:"primary",ghost:"",onClick:o.handleView},{default:s(()=>e[3]||(e[3]=[u("在线预览")])),_:1},8,["onClick"]),p(r,{type:"primary",onClick:o.onDownloadGenerateCode,loading:o.loading},{default:s(()=>e[4]||(e[4]=[u("下载到本地")])),_:1},8,["onClick","loading"])]),default:s(()=>[M("div",{style:x(o.divStyle)},[M("p",null,[(v(!0),C(y,null,b(o.codeList,w=>(v(),C(y,null,[u(F(w),1),e[1]||(e[1]=M("br",null,null,-1))],64))),256))])],4)]),_:1},8,["onRegister"]),p(l,{onRegister:o.registerCodeViewModal,onDownload:o.onDownloadGenerateCode,onClose:o.handleClose},null,8,["onRegister","onDownload","onClose"])],64)}const Ae=R(P,[["render",U]]);export{Ae as default};
