var te=Object.defineProperty,ae=Object.defineProperties;var se=Object.getOwnPropertyDescriptors;var Y=Object.getOwnPropertySymbols;var le=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable;var G=(n,p,u)=>p in n?te(n,p,{enumerable:!0,configurable:!0,writable:!0,value:u}):n[p]=u,E=(n,p)=>{for(var u in p||(p={}))le.call(p,u)&&G(n,u,p[u]);if(Y)for(var u of Y(p))re.call(p,u)&&G(n,u,p[u]);return n},L=(n,p)=>ae(n,se(p));var O=(n,p,u)=>new Promise((Z,U)=>{var c=g=>{try{T(u.next(g))}catch(d){U(d)}},h=g=>{try{T(u.throw(g))}catch(d){U(d)}},T=g=>g.done?Z(g.value):Promise.resolve(g.value).then(c,h);T((u=u.apply(n,p)).next())});import{d as oe,f as _,e as x,r as H,o as ne,ag as A,aq as P,ar as N,ah as w,at as t,as as M,k as i,aD as z,G as B,u as D}from"./vue-vendor-dy9k-Yad.js";import{f}from"./antd-vue-vendor-me9YkNVC.js";import"./index-B4ez5KWV.js";import{j as F,ah as ie,X as ue,a as pe}from"./index-CCWaWN5g.js";import{_ as k}from"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const $=n=>F.post({url:"/hgy/personalCenter/hgyPersonalAuth/addOrUpdatePersonalAuth",params:n}),q=n=>F.post({url:"/hgy/personalCenter/hgyEnterpriseAuth/addOrUpdateEnterpriseAuth",params:n}),de=n=>F.get({url:"/hgy/personalCenter/hgyPersonalAuth/getPersonalAuthByUserId",params:{userId:n}}),me=n=>F.get({url:"/hgy/personalCenter/hgyEnterpriseAuth/getEnterpriseAuthByUserId",params:{userId:n}}),ve={class:"authentication-container"},fe={key:0,class:"auth-nav"},ce={class:"auth-content"},he={key:0,class:"no-permission"},ye={key:1,class:"form-section"},ge={class:"form-item"},be={class:"form-value"},_e={class:"form-item"},Ae={class:"form-value"},Pe={class:"form-item"},Ne={class:"form-value"},Ue={class:"form-item"},Te={class:"form-value"},Ce={class:"form-item"},Ie={class:"form-value"},Se={class:"form-item"},Ee={class:"form-value"},Le={class:"form-item"},we={class:"form-value"},ze={class:"form-item"},Oe={class:"form-value"},xe={class:"form-item"},De={class:"form-value"},ke={class:"form-item"},Fe={class:"form-value"},Ze={class:"form-item"},Be={class:"form-value"},Je={key:2,class:"form-section"},je={class:"form-item"},Re={class:"form-value"},Ve={class:"form-item"},Ye={class:"form-value"},Ge={class:"form-item"},He={class:"form-value"},Me={class:"form-item"},$e={class:"form-value"},qe={class:"form-item"},Xe={class:"form-value"},Ke={key:1,class:"action-buttons"},Qe="hgy.personalCenter:hgy_enterprise_auth:addOrUpdateEnterpriseAuth",We="hgy_personal_auth:addOrUpdatePersonalAuth",et=oe({__name:"AuthenticationSetting",setup(n){const p=_(!1),u=_(!1),Z=ie(),{hasPermission:U}=ue(),c=x(()=>U(Qe)),h=x(()=>U(We)),T=x(()=>c.value&&h.value),d=_(c.value&&h.value||c.value?"enterprise":h.value?"personal":"enterprise"),o=H({enterpriseName:"",creditCode:"",relationUser:"",relationPhone:"",legalName:"",cartType:1,cartId:"",companyLogo:"",description:"",review:1}),m=H({name:"",phone:"",cartType:1,cartId:"",review:1}),C=_(""),I=_(""),S=_(""),y=x(()=>{var a;return(a=Z.getUserInfo)==null?void 0:a.id}),J=a=>{if(a==="enterprise"&&!c.value){f.warning("您没有企业认证权限");return}if(a==="personal"&&!h.value){f.warning("您没有个人认证权限");return}d.value=a,j()},j=()=>O(null,null,function*(){if(y.value)try{if(d.value==="enterprise"){const a=yield me(y.value);if(a){if(a.hgyEnterpriseAuth){const l=a.hgyEnterpriseAuth;Object.assign(o,{enterpriseName:l.enterpriseName,creditCode:l.creditCode,relationUser:l.relationUser,relationPhone:l.relationPhone,legalName:l.legalName,cartType:l.cartType,cartId:l.cartId,companyLogo:l.companyLogo,description:l.description,review:1})}else Object.assign(o,a);const e=a.hgyAttachmentList;e&&e.length>0&&X(e)}}else{const a=yield de(y.value);if(a){if(a.hgyPersonalAuth){const l=a.hgyPersonalAuth;Object.assign(m,{name:l.name,phone:l.phone,cartType:l.cartType,cartId:l.cartId,review:1})}else Object.assign(m,a);const e=a.hgyAttachmentList;e&&e.length>0&&K(e)}}}catch(a){}}),X=a=>{const e=[],l=[];a.forEach(r=>{const b={fileName:r.fileName,filePath:r.filePath,fileSize:r.fileSize,fileType:r.fileType};r.bizType==="YYZZ"?e.push(b):r.bizType==="SFZ"&&l.push(b)}),e.length>0&&(C.value=JSON.stringify(e)),l.length>0&&(I.value=JSON.stringify(l))},K=a=>{const e=[];a.forEach(l=>{if(l.bizType==="SFZ"){const r={fileName:l.fileName,filePath:l.filePath,fileSize:l.fileSize,fileType:l.fileType};e.push(r)}}),e.length>0&&(S.value=JSON.stringify(e))},R=(a,e)=>{const l=[];if(a)try{const r=typeof a=="string"?JSON.parse(a):a;(Array.isArray(r)?r:[]).forEach(v=>{l.push({bizType:"YYZZ",fileName:v.fileName,filePath:v.filePath,fileSize:v.fileSize,fileType:v.fileType||"image"})})}catch(r){}if(e)try{const r=typeof e=="string"?JSON.parse(e):e;(Array.isArray(r)?r:[]).forEach(v=>{l.push({bizType:"SFZ",fileName:v.fileName,filePath:v.filePath,fileSize:v.fileSize,fileType:v.fileType||"image"})})}catch(r){}return l},V=a=>{if(!a)return[];try{const e=typeof a=="string"?JSON.parse(a):a;return(Array.isArray(e)?e:[]).map(r=>({bizType:"SFZ",fileName:r.fileName,filePath:r.filePath,fileSize:r.fileSize,fileType:r.fileType||"image"}))}catch(e){return[]}},tt=()=>O(null,null,function*(){if(!y.value){f.warning("用户信息异常，请重新登录");return}if(d.value==="enterprise"&&!c.value){f.warning("您没有企业认证权限");return}if(d.value==="personal"&&!h.value){f.warning("您没有个人认证权限");return}p.value=!0;try{if(d.value==="enterprise"){const a=L(E({},o),{userId:String(y.value),attachmentList:R(C.value,I.value)});yield q(a)}else{const a=L(E({},m),{userId:String(y.value),attachmentList:V(S.value)});yield $(a)}f.success("保存成功")}catch(a){f.error("保存失败")}finally{p.value=!1}}),Q=()=>O(null,null,function*(){if(!y.value){f.warning("用户信息异常，请重新登录");return}if(d.value==="enterprise"&&!c.value){f.warning("您没有企业认证权限");return}if(d.value==="personal"&&!h.value){f.warning("您没有个人认证权限");return}if(d.value==="enterprise"){if(!o.enterpriseName||!o.creditCode||!o.legalName){f.warning("请填写完整的企业信息");return}}else if(!m.name||!m.phone||!m.cartId){f.warning("请填写完整的个人信息");return}u.value=!0;try{if(d.value==="enterprise"){const a=L(E({},o),{userId:String(y.value),attachmentList:R(C.value,I.value)});yield q(a)}else{const a=L(E({},m),{userId:String(y.value),attachmentList:V(S.value)});yield $(a)}}catch(a){}finally{u.value=!1}});return ne(()=>{j()}),(a,e)=>{const l=A("a-empty"),r=A("a-input"),b=A("a-radio"),v=A("a-radio-group"),W=A("a-textarea"),ee=A("a-button");return N(),P("div",ve,[T.value?(N(),P("div",fe,[t("div",{class:M(["nav-tab",{active:d.value==="enterprise"}]),onClick:e[0]||(e[0]=s=>J("enterprise"))}," 企业认证 ",2),t("div",{class:M(["nav-tab",{active:d.value==="personal"}]),onClick:e[1]||(e[1]=s=>J("personal"))}," 个人认证 ",2)])):w("",!0),t("div",ce,[!c.value&&!h.value?(N(),P("div",he,[i(l,{description:"您没有认证相关权限，请联系管理员"})])):w("",!0),d.value==="enterprise"&&c.value?(N(),P("div",ye,[t("div",ge,[e[18]||(e[18]=t("label",{class:"form-label"},"企业名称",-1)),t("div",be,[i(r,{value:o.enterpriseName,"onUpdate:value":e[2]||(e[2]=s=>o.enterpriseName=s),placeholder:"请输入企业名称",class:"form-input"},null,8,["value"])])]),t("div",_e,[e[19]||(e[19]=t("label",{class:"form-label"},"统一社会信用代码",-1)),t("div",Ae,[i(r,{value:o.creditCode,"onUpdate:value":e[3]||(e[3]=s=>o.creditCode=s),placeholder:"请输入统一社会信用代码",class:"form-input"},null,8,["value"])])]),t("div",Pe,[e[20]||(e[20]=t("label",{class:"form-label"},"业务联系人",-1)),t("div",Ne,[i(r,{value:o.relationUser,"onUpdate:value":e[4]||(e[4]=s=>o.relationUser=s),placeholder:"请输入业务联系人",class:"form-input"},null,8,["value"])])]),t("div",Ue,[e[21]||(e[21]=t("label",{class:"form-label"},"联系电话",-1)),t("div",Te,[i(r,{value:o.relationPhone,"onUpdate:value":e[5]||(e[5]=s=>o.relationPhone=s),placeholder:"请输入联系电话",class:"form-input"},null,8,["value"])])]),t("div",Ce,[e[22]||(e[22]=t("label",{class:"form-label"},"法人真实姓名",-1)),t("div",Ie,[i(r,{value:o.legalName,"onUpdate:value":e[6]||(e[6]=s=>o.legalName=s),placeholder:"请输入法人真实姓名",class:"form-input"},null,8,["value"])])]),t("div",Se,[e[24]||(e[24]=t("label",{class:"form-label"},"法人证件类型",-1)),t("div",Ee,[i(v,{value:o.cartType,"onUpdate:value":e[7]||(e[7]=s=>o.cartType=s)},{default:z(()=>[i(b,{value:1},{default:z(()=>e[23]||(e[23]=[B("身份证")])),_:1,__:[23]})]),_:1},8,["value"])])]),t("div",Le,[e[25]||(e[25]=t("label",{class:"form-label"},"法人证件号",-1)),t("div",we,[i(r,{value:o.cartId,"onUpdate:value":e[8]||(e[8]=s=>o.cartId=s),placeholder:"请输入法人证件号",class:"form-input"},null,8,["value"])])]),t("div",ze,[e[26]||(e[26]=t("label",{class:"form-label"},"营业执照照片",-1)),t("div",Oe,[i(D(k),{value:C.value,"onUpdate:value":e[9]||(e[9]=s=>C.value=s),maxCount:1,"file-type":"image",bizPath:"temp",text:"上传营业执照","return-url":!1},null,8,["value"])])]),t("div",xe,[e[27]||(e[27]=t("label",{class:"form-label"},"身份证正反面照片",-1)),t("div",De,[i(D(k),{value:I.value,"onUpdate:value":e[10]||(e[10]=s=>I.value=s),maxCount:2,"file-type":"image",bizPath:"temp",text:"上传身份证照片","return-url":!1},null,8,["value"])])]),t("div",ke,[e[28]||(e[28]=t("label",{class:"form-label"},"企业logo",-1)),t("div",Fe,[i(D(k),{value:o.companyLogo,"onUpdate:value":e[11]||(e[11]=s=>o.companyLogo=s),maxCount:1,"file-type":"image",bizPath:"temp",text:"上传企业logo"},null,8,["value"])])]),t("div",Ze,[e[29]||(e[29]=t("label",{class:"form-label"},"企业简介",-1)),t("div",Be,[i(W,{value:o.description,"onUpdate:value":e[12]||(e[12]=s=>o.description=s),placeholder:"请输入企业简介",rows:4,class:"form-input"},null,8,["value"])])])])):w("",!0),d.value==="personal"&&h.value?(N(),P("div",Je,[t("div",je,[e[30]||(e[30]=t("label",{class:"form-label"},"真实姓名",-1)),t("div",Re,[i(r,{value:m.name,"onUpdate:value":e[13]||(e[13]=s=>m.name=s),placeholder:"请输入真实姓名",class:"form-input"},null,8,["value"])])]),t("div",Ve,[e[31]||(e[31]=t("label",{class:"form-label"},"手机号",-1)),t("div",Ye,[i(r,{value:m.phone,"onUpdate:value":e[14]||(e[14]=s=>m.phone=s),placeholder:"请输入手机号",class:"form-input"},null,8,["value"])])]),t("div",Ge,[e[33]||(e[33]=t("label",{class:"form-label"},"证件类型",-1)),t("div",He,[i(v,{value:m.cartType,"onUpdate:value":e[15]||(e[15]=s=>m.cartType=s)},{default:z(()=>[i(b,{value:1},{default:z(()=>e[32]||(e[32]=[B("身份证")])),_:1,__:[32]})]),_:1},8,["value"])])]),t("div",Me,[e[34]||(e[34]=t("label",{class:"form-label"},"证件号",-1)),t("div",$e,[i(r,{value:m.cartId,"onUpdate:value":e[16]||(e[16]=s=>m.cartId=s),placeholder:"请输入证件号",class:"form-input"},null,8,["value"])])]),t("div",qe,[e[35]||(e[35]=t("label",{class:"form-label"},"身份证正反面照片",-1)),t("div",Xe,[i(D(k),{value:S.value,"onUpdate:value":e[17]||(e[17]=s=>S.value=s),maxCount:2,"file-type":"image",bizPath:"temp",text:"上传身份证照片","return-url":!1},null,8,["value"])])])])):w("",!0)]),c.value||h.value?(N(),P("div",Ke,[i(ee,{type:"primary",class:"submit-btn",onClick:Q,loading:u.value},{default:z(()=>e[36]||(e[36]=[B("提交审核")])),_:1,__:[36]},8,["loading"])])):w("",!0)])}}}),At=pe(et,[["__scopeId","data-v-afec2067"]]);export{At as default};
