import{p as u}from"./index-CCWaWN5g.js";import"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function n(t,r){return r?typeof r=="string"?` ${t}--${r}`:Array.isArray(r)?r.reduce((i,e)=>i+n(t,e),""):Object.keys(r).reduce((i,e)=>i+(r[e]?n(t,e):""),""):""}function f(t){return(r,i)=>(r&&typeof r!="string"&&(i=r,r=""),r=r?`${t}__${r}`:t,`${r}${n(r,i)}`)}function a(t){return[f(`${u}-${t}`)]}function y(t){const r=`${u}-${t}`;return[r,f(r)]}export{f as buildBEM,a as createBEM,y as createNamespace};
