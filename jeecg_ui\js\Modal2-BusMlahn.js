import{d as l,ag as a,aB as m,ar as d,aD as e,k as i,G as p}from"./vue-vendor-dy9k-Yad.js";import{B as c}from"./index-Diw57m_E.js";import{ac as u,a as M}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const _=l({components:{BasicModal:c},setup(){const[t,{closeModal:o,setModalProps:r}]=u();return{register:t,closeModal:o,setModalProps:()=>{r({title:"Modal New Title"})}}}});function f(t,o,r,k,B,C){const s=a("a-button"),n=a("BasicModal");return d(),m(n,{onRegister:t.register,title:"Modal Title",helpMessage:["提示1","提示2"],okButtonProps:{disabled:!0}},{default:e(()=>[i(s,{type:"primary",onClick:t.closeModal,class:"mr-2"},{default:e(()=>o[0]||(o[0]=[p(" 从内部关闭弹窗 ")])),_:1,__:[0]},8,["onClick"]),i(s,{type:"primary",onClick:t.setModalProps},{default:e(()=>o[1]||(o[1]=[p(" 从内部修改title ")])),_:1,__:[1]},8,["onClick"])]),_:1},8,["onRegister"])}const j=M(_,[["render",f]]);export{j as default};
