import{d as x,f as m,r as f,ag as p,aq as k,ar as w,at as t,k as o,G as l,aD as a}from"./vue-vendor-dy9k-Yad.js";import{bx as n,u as S}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const B=x({__name:"JVxeDemo3",setup(V){const b=m(),c=m(),i=f({columns:[{title:"ID",key:"id",width:120,type:n.normal},{title:"姓名",key:"name",width:240,type:n.input,defaultValue:"new name"},{title:"字段长度",key:"dbLength",width:2400,type:n.inputNumber,defaultValue:32},{title:"sortNum",key:"sortNum",width:120,type:n.normal}],data:[{id:"uuid-0001",name:"张三",dbLength:123},{id:"uuid-0002",name:"李四",dbLength:777},{id:"uuid-0003",name:"王五",dbLength:666},{id:"uuid-0004",name:"赵六",dbLength:233}]}),u=f({columns:[{title:"ID",key:"id",width:320,fixed:"left",type:n.normal},{title:"姓名",key:"name",width:720,type:n.input,defaultValue:"new name"},{title:"字段长度",key:"dbLength",width:720,type:n.inputNumber,defaultValue:32}],data:[{id:"uuid-0001",name:"张三",dbLength:123},{id:"uuid-0002",name:"李四",dbLength:777},{id:"uuid-0003",name:"王五",dbLength:666},{id:"uuid-0004",name:"赵六",dbLength:233}]}),{createMessage:d}=S();function g(){d.info("请看控制台")}function y(){d.info("请看控制台")}return(_,e)=>{const r=p("a-button"),s=p("JVxeTable");return w(),k("div",null,[e[2]||(e[2]=t("ol",{style:{border:"1px solid #cccccc",width:"600px",padding:"8px"}},[t("li",null,"1. 开启 dragSort 属性之后即可实现上下拖拽排序。"),t("li",null,"2. 使用 sortKey 属性可以自定义排序保存的 key，默认为 orderNum。"),t("li",null,"3. 使用 sortBegin 属性可以自定义排序的起始值，默认为 0。"),t("li",null,"4. sortKey 定义的字段不需要定义在 columns 中也能正常获取到值。"),t("li",null,"5. 当存在 fixed 列时，拖拽排序将会失效，仅能上下排序。")],-1)),e[3]||(e[3]=t("p",null,[l(" 以下示例开启了拖拽排序，排序值保存字段为 sortNum，排序起始值为 3"),t("br")],-1)),o(s,{ref_key:"tableRef1",ref:b,toolbar:"",dragSort:"",sortKey:"sortNum",sortBegin:3,rowSelection:"",dragSortFixed:"none",rowSelectionFixed:"none",maxHeight:580,columns:i.columns,dataSource:i.data},{toolbarSuffix:a(()=>[o(r,{onClick:g},{default:a(()=>e[0]||(e[0]=[l("获取数据")])),_:1,__:[0]})]),_:1},8,["columns","dataSource"]),e[4]||(e[4]=t("br",null,null,-1)),e[5]||(e[5]=t("p",null,"以下 fixed 表格不支持拖拽排序，仅支持点击上下排序",-1)),o(s,{ref_key:"tableRef2",ref:c,toolbar:"",dragSort:"",rowSelection:"",maxHeight:580,columns:u.columns,dataSource:u.data},{toolbarSuffix:a(()=>[o(r,{onClick:y},{default:a(()=>e[1]||(e[1]=[l("获取数据")])),_:1,__:[1]})]),_:1},8,["columns","dataSource"])])}}});export{B as default};
