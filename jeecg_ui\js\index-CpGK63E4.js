var d=(e,i,r)=>new Promise((c,a)=>{var f=o=>{try{t(r.next(o))}catch(l){a(l)}},n=o=>{try{t(r.throw(o))}catch(l){a(l)}},t=o=>o.done?c(o.value):Promise.resolve(o.value).then(f,n);t((r=r.apply(e,i)).next())});import"./index-L3cSIXth.js";import{d as h,f as v,ag as m,aB as k,ar as q,aD as s,k as p,G as B}from"./vue-vendor-dy9k-Yad.js";import T from"./PersonTable-CYIehavj.js";import{P as y}from"./index-CtJ0w2CP.js";import{J as b}from"./antd-vue-vendor-me9YkNVC.js";import{B as C}from"./BasicForm-DBcXiHk0.js";import{u as P}from"./useForm-CgkFTrrO.js";import{a as F}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-BkGZ5fiW.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";const u=[{label:"付晓晓",value:"1"},{label:"周毛毛",value:"2"}],_=[{label:"私密",value:"1"},{label:"公开",value:"2"}],R=[{field:"f1",component:"Input",label:"仓库名",required:!0},{field:"f2",component:"Input",label:"仓库域名",required:!0,componentProps:{addonBefore:"http://",addonAfter:"com"},colProps:{offset:2}},{field:"f3",component:"Select",label:"仓库管理员",componentProps:{options:u},required:!0,colProps:{offset:2}},{field:"f4",component:"Select",label:"审批人",componentProps:{options:u},required:!0},{field:"f5",component:"RangePicker",label:"生效日期",required:!0,colProps:{offset:2}},{field:"f6",component:"Select",label:"仓库类型",componentProps:{options:_},required:!0,colProps:{offset:2}}],S=[{field:"t1",component:"Input",label:"任务名",required:!0},{field:"t2",component:"Input",label:"任务描述",required:!0,colProps:{offset:2}},{field:"t3",component:"Select",label:"执行人",componentProps:{options:u},required:!0,colProps:{offset:2}},{field:"t4",component:"Select",label:"责任人",componentProps:{options:u},required:!0},{field:"t5",component:"TimePicker",label:"生效日期",required:!0,componentProps:{style:{width:"100%"}},colProps:{offset:2}},{field:"t6",component:"Select",label:"任务类型",componentProps:{options:_},required:!0,colProps:{offset:2}}],w=h({name:"FormHightPage",components:{BasicForm:C,PersonTable:T,PageWrapper:y,[b.name]:b},setup(){const e=v(null),[i,{validate:r}]=P({baseColProps:{span:6},labelWidth:200,layout:"vertical",schemas:R,showActionButtonGroup:!1}),[c,{validate:a}]=P({baseColProps:{span:6},labelWidth:200,layout:"vertical",schemas:S,showActionButtonGroup:!1});function f(){return d(this,null,function*(){try{e.value;const[n,t]=yield Promise.all([r(),a()])}catch(n){}})}return{register:i,registerTask:c,submitAll:f,tableRef:e}}});function A(e,i,r,c,a,f){const n=m("BasicForm"),t=m("a-card"),o=m("PersonTable"),l=m("a-button"),g=m("PageWrapper");return q(),k(g,{class:"high-form",title:"高级表单",content:" 高级表单常见于一次性输入和提交大批量数据的场景。"},{rightFooter:s(()=>[p(l,{type:"primary",onClick:e.submitAll},{default:s(()=>i[0]||(i[0]=[B(" 提交 ")])),_:1,__:[0]},8,["onClick"])]),default:s(()=>[p(t,{title:"仓库管理",bordered:!1},{default:s(()=>[p(n,{onRegister:e.register},null,8,["onRegister"])]),_:1}),p(t,{title:"任务管理",bordered:!1,class:"!mt-5"},{default:s(()=>[p(n,{onRegister:e.registerTask},null,8,["onRegister"])]),_:1}),p(t,{title:"成员管理",bordered:!1},{default:s(()=>[p(o,{ref:"tableRef"},null,512)]),_:1})]),_:1})}const Jo=F(w,[["render",A],["__scopeId","data-v-976e482a"]]);export{Jo as default};
