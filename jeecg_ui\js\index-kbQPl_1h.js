var B=(K,h,r)=>new Promise((D,g)=>{var U=d=>{try{v(r.next(d))}catch(m){g(m)}},T=d=>{try{v(r.throw(d))}catch(m){g(m)}},v=d=>d.done?D(d.value):Promise.resolve(d.value).then(U,T);v((r=r.apply(K,h)).next())});import{d as ge,f,e as _e,r as I,w as ve,u as l,ag as s,aq as E,ar as S,ah as F,k as t,aO as ye,aD as o,at as R,F as be,G as p,au as A,aB as Ce,as as xe,J as we}from"./vue-vendor-dy9k-Yad.js";import{u as ke}from"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{g as Q,a as Ie,_ as Se,d as Re,b as he,s as De,c as Ue,e as Te}from"./DemoModal-C5LFdZJn.js";import{a as Me}from"./JImportModal-BnQ3nPZC.js";import{_ as V}from"./JAddInput-CxJ-JBK-.js";import{bu as je,ad as Y,G as qe,E as Be,a as Ee}from"./index-CCWaWN5g.js";import{Q as Fe}from"./componentMap-Bkie1n3v.js";import Ke from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./renderUtils-D7XVOFwj.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const $e={style:{float:"left",overflow:"hidden"},class:"table-page-search-submitButtons"},Ne=ge({__name:"index",setup(K){const h=je(),r=f([]),[D,{openModal:g}]=Y(),[U,{openModal:T}]=Y(),{handleExportXls:v,handleImportXls:d}=Be(),m=f(),M=f(),w=f(!1),[z,{reload:_,setProps:$}]=ke({title:"单表示例",api:Te,columns:Ue,formConfig:{schemas:De,fieldMapToTime:[["birthday",["birthday_begin","birthday_end"],"YYYY-MM-DD"]],fieldMapToNumber:[["age",["age_begin","age_end"]]],autoAdvancedCol:2,actionColOptions:{style:{textAlign:"left"}}},defSort:{column:"createTime,sex",order:"desc"},striped:!0,useSearchForm:!0,showTableSetting:!0,clickToRowSelect:!1,bordered:!0,showIndexColumn:!1,tableSetting:{fullScreen:!0},canResize:!1,rowKey:"id",actionColumn:{width:180,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:void 0}}),G={type:"checkbox",columnWidth:40,selectedRowKeys:r,onChange:X};function J(){T(!0)}const P=_e(()=>{let n={};return r.value&&r.value.length>0&&(n.selections=r.value.join(",")),qe(n)});function L(n){return[{label:"编辑",onClick:W.bind(null,n)},{label:"详情",onClick:Z.bind(null,n)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:ee.bind(null,n)}}]}function X(n){r.value=n}function H(){w.value=!1,g(!0,{isUpdate:!1})}function W(n){w.value=!1,g(!0,{record:n,isUpdate:!0})}function Z(n){w.value=!0,g(!0,{record:n,isUpdate:!0})}function ee(n){return B(this,null,function*(){yield Re({id:n.id},_)})}function te(){return B(this,null,function*(){yield he({ids:r.value},_)})}function N(n,e){n[e]=[l(m),l(M)]}function oe(){h("/comp/jeecg/basic")}const Oe=f(),ne=I({xs:{span:24},sm:{span:7}}),ae=I({xs:{span:24},sm:{span:16}}),y=f(!1),c=f(!1),i=I({name:"",age_begin:"",age_end:"",sex:"",id:""});ve(c,()=>{$({useSearchForm:!l(c)})});function j(){$({searchInfo:we(i)}),_()}function le(){Object.assign(i,{name:"",age_begin:"",age_end:"",sex:"",id:""}),_()}const ie=I({name:{title:"名称",view:"text",type:"string",order:1},sex:{title:"性别",view:"list",type:"string",dictCode:"sex",order:2}});function re(n){Object.keys(n).map(e=>{i[e]=n[e]}),j()}return(n,e)=>{const b=s("a-input"),k=s("a-form-item"),C=s("a-col"),u=s("a-button"),q=s("Icon"),se=s("a-row"),pe=s("a-form"),ue=s("a-upload"),de=s("super-query"),me=s("a-menu-item"),ce=s("a-menu"),fe=s("a-dropdown");return S(),E("div",null,[c.value?(S(),E("div",{key:0,class:"jeecg-basic-table-form-container",onKeyup:ye(j,["enter"])},[t(pe,{ref:"formRef",model:i,"label-col":ne,"wrapper-col":ae},{default:o(()=>[t(se,{gutter:24},{default:o(()=>[t(C,{lg:8},{default:o(()=>[t(k,{label:"用户名"},{default:o(()=>[t(b,{placeholder:"请输入名称模糊查询",value:i.name,"onUpdate:value":e[0]||(e[0]=a=>i.name=a)},null,8,["value"])]),_:1})]),_:1}),t(C,{lg:8},{default:o(()=>[t(k,{label:"年龄"},{default:o(()=>[t(b,{placeholder:"最小年龄",type:"ge",value:i.age_begin,"onUpdate:value":e[1]||(e[1]=a=>i.age_begin=a),style:{width:"calc(50% - 15px)"}},null,8,["value"]),e[11]||(e[11]=R("span",null,"~",-1)),t(b,{placeholder:"最大年龄",type:"le",value:i.age_end,"onUpdate:value":e[2]||(e[2]=a=>i.age_end=a),style:{width:"calc(50% - 15px)"}},null,8,["value"])]),_:1,__:[11]})]),_:1}),y.value?(S(),E(be,{key:0},[t(C,{lg:8},{default:o(()=>[t(k,{label:"性别"},{default:o(()=>[t(V,{value:i.sex,"onUpdate:value":e[3]||(e[3]=a=>i.sex=a),placeholder:"请选择性别",dictCode:"sex"},null,8,["value"])]),_:1})]),_:1}),t(C,{lg:8},{default:o(()=>[t(k,{label:"选择用户"},{default:o(()=>[t(V,{value:i.id,"onUpdate:value":e[4]||(e[4]=a=>i.id=a),placeholder:"请选择用户",dictCode:"demo,name,id"},null,8,["value"])]),_:1})]),_:1})],64)):F("",!0),R("span",$e,[t(C,{lg:6},{default:o(()=>[t(u,{type:"primary",preIcon:"ant-design:search-outlined",onClick:j},{default:o(()=>e[12]||(e[12]=[p("查询")])),_:1,__:[12]}),t(u,{type:"primary",preIcon:"ant-design:reload-outlined",onClick:le,style:{"margin-left":"8px"}},{default:o(()=>e[13]||(e[13]=[p("重置")])),_:1,__:[13]}),R("a",{onClick:e[5]||(e[5]=a=>y.value=!y.value),style:{"margin-left":"8px"}},[p(A(y.value?"收起":"展开")+" ",1),t(q,{icon:y.value?"ant-design:up-outlined":"ant-design:down-outlined"},null,8,["icon"])])]),_:1})])]),_:1})]),_:1},8,["model","label-col","wrapper-col"])],32)):F("",!0),t(l(Ke),{onRegister:l(z),rowSelection:G,class:xe({"p-4":c.value})},{"form-age":o(({model:a,field:O})=>[t(b,{placeholder:"最小年龄",type:"ge",value:m.value,"onUpdate:value":e[6]||(e[6]=x=>m.value=x),style:{width:"calc(50% - 15px)"},onChange:x=>N(a,O)},null,8,["value","onChange"]),e[14]||(e[14]=R("span",null,"~",-1)),t(b,{placeholder:"最大年龄",type:"le",value:M.value,"onUpdate:value":e[7]||(e[7]=x=>M.value=x),style:{width:"calc(50% - 15px)"},onChange:x=>N(a,O)},null,8,["value","onChange"])]),tableTitle:o(()=>[t(u,{preIcon:"ant-design:plus-outlined",type:"primary",onClick:H},{default:o(()=>e[15]||(e[15]=[p("新增")])),_:1,__:[15]}),t(ue,{name:"file",showUploadList:!1,customRequest:a=>l(d)(a,l(Q),l(_))},{default:o(()=>[t(u,{preIcon:"ant-design:import-outlined",type:"primary"},{default:o(()=>e[16]||(e[16]=[p("导入")])),_:1,__:[16]})]),_:1},8,["customRequest"]),t(u,{preIcon:"ant-design:export-outlined",type:"primary",onClick:e[8]||(e[8]=a=>l(v)("单表示例",l(Ie),P.value))},{default:o(()=>e[17]||(e[17]=[p("导出")])),_:1,__:[17]}),t(u,{preIcon:"ant-design:filter",type:"primary",onClick:e[9]||(e[9]=()=>{})},{default:o(()=>e[18]||(e[18]=[p("高级查询")])),_:1,__:[18]}),t(u,{preIcon:"ant-design:plus-outlined",type:"primary",onClick:oe},{default:o(()=>e[19]||(e[19]=[p("打开Tab页")])),_:1,__:[19]}),t(u,{preIcon:"ant-design:retweet-outlined",type:"primary",onClick:e[10]||(e[10]=a=>c.value=!c.value)},{default:o(()=>[p(A(c.value?"表单配置查询":"自定义查询"),1)]),_:1}),t(u,{preIcon:"ant-design:import-outlined",type:"primary",onClick:J},{default:o(()=>e[20]||(e[20]=[p("弹窗导入")])),_:1,__:[20]}),t(de,{config:ie,onSearch:re},null,8,["config"]),r.value.length>0?(S(),Ce(fe,{key:0},{overlay:o(()=>[t(ce,null,{default:o(()=>[t(me,{key:"1",onClick:te},{default:o(()=>[t(q,{icon:"ant-design:delete-outlined"}),e[21]||(e[21]=p(" 删除 "))]),_:1,__:[21]})]),_:1})]),default:o(()=>[t(u,null,{default:o(()=>[e[22]||(e[22]=p("批量操作 ")),t(q,{style:{fontsize:"12px"},icon:"ant-design:down-outlined"})]),_:1,__:[22]})]),_:1})):F("",!0)]),action:o(({record:a})=>[t(l(Fe),{actions:L(a)},null,8,["actions"])]),_:1},8,["onRegister","class"]),t(Se,{onRegister:l(D),onSuccess:l(_),isDisabled:w.value},null,8,["onRegister","onSuccess","isDisabled"]),t(Me,{onRegister:l(U),url:l(Q),online:""},null,8,["onRegister","url"])])}}}),Pt=Ee(Ne,[["__scopeId","data-v-ef347cb7"]]);export{Pt as default};
