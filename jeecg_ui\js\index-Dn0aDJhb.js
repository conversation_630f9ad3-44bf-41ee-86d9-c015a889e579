const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/index-XZ8pAyWp.js","js/vue-vendor-dy9k-Yad.js","js/LockPage-DpLmyeWL.js","js/antd-vue-vendor-me9YkNVC.js","js/index-CCWaWN5g.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/lock-CRalNsZJ.js","js/header-OZa5fSDc.js","assets/LockPage-Brcp2jQw.css","js/index-CI-8_pdX.js","js/index-JbqXEynz.js","js/index-CImCetrx.js","assets/index-BObJM2Lc.css","js/index-LCGLvkB3.js","js/index-De_W6s5g.js","js/index-D6l0IxOU.js","js/useTimeout-CeTdFD_D.js","js/useIntersectionObserver-C4LVxQJW.js","assets/index-zj-Vfn3Q.css","assets/index-Yp9ECMoG.css","js/useHeaderSetting-C-h5S52e.js","js/useMultipleTabSetting-QBbnIi9J.js"])))=>i.map(i=>d[i]);
import{aj as d,D as k,cL as B,F as L,d2 as S,_ as f,a as D}from"./index-CCWaWN5g.js";import{d as y,e as T,u as o,ag as r,aq as P,ar as i,F as w,k as C,aB as m,ah as p,as as F}from"./vue-vendor-dy9k-Yad.js";import{co as E}from"./antd-vue-vendor-me9YkNVC.js";import{useHeaderSetting as I}from"./useHeaderSetting-C-h5S52e.js";import h from"./SessionTimeoutLogin-DNwUJYr8.js";import"./vxe-table-vendor-B22HppNm.js";import"./Login-BbE4lNlw.js";import"./LoginForm-Cv1KIRFA.js";import"./checkcode-DLY3GIII.js";import"./LoginFormTitle-Bms3O5Qx.js";import"./ThirdModal-1GWAIf70.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFormItemSingle-Cw668yj5.js";import"./useThirdLogin-C4qxd7Ms.js";import"./ForgetPasswordForm-D5tOYC3b.js";import"./step1-CkQhLHRg.js";import"./step2-tEElob-Y.js";import"./index-CBCjSSNZ.js";import"./step3-dZT9Navo.js";import"./RegisterForm-DI32cwRE.js";import"./MobileForm-BL07NIax.js";import"./QrCodeForm-CVY8RjiB.js";import"./index-C1YxH9KC.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";const O=y({name:"LayoutFeatures",components:{BackTop:E,LayoutLockPage:d(()=>f(()=>import("./index-XZ8pAyWp.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]))),SettingDrawer:d(()=>f(()=>import("./index-CI-8_pdX.js").then(t=>t.i),__vite__mapDeps([10,1,11,4,3,5,6,12,13,14,15,16,17,18,19,20,21,22]))),SessionTimeoutLogin:h},setup(){const{getUseOpenBackTop:t,getShowSettingButton:u,getSettingButtonPosition:c,getFullContent:g}=k(),_=B(),{prefixCls:l}=L("setting-drawer-fearure"),{getShowHeader:n}=I(),s=T(()=>_.getSessionTimeout),a=T(()=>{if(!o(u))return!1;const e=o(c);return e===S.AUTO?!o(n)||o(g):e===S.FIXED});return{getTarget:()=>document.body,getUseOpenBackTop:t,getIsFixedSettingDrawer:a,prefixCls:l,getIsSessionTimeout:s}}});function v(t,u,c,g,_,l){const n=r("LayoutLockPage"),s=r("BackTop"),a=r("SettingDrawer"),e=r("SessionTimeoutLogin");return i(),P(w,null,[C(n),t.getUseOpenBackTop?(i(),m(s,{key:0,target:t.getTarget},null,8,["target"])):p("",!0),t.getIsFixedSettingDrawer?(i(),m(a,{key:1,class:F(t.prefixCls)},null,8,["class"])):p("",!0),t.getIsSessionTimeout?(i(),m(e,{key:2})):p("",!0)],64)}const St=D(O,[["render",v]]);export{St as default};
