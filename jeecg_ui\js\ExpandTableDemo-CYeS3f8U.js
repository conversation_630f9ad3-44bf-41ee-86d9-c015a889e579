import{d as c,ag as e,aB as u,ar as f,aD as i,k as m,at as b,au as x}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{P as g}from"./index-CtJ0w2CP.js";import{d as w}from"./table-BDFKJhHv.js";import{useListPage as h}from"./useListPage-Soxgnx9a.js";import{Q as B}from"./componentMap-Bkie1n3v.js";import T from"./BasicTable-xCEZpGLb.js";import{a as _}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const C=[{title:"ID",dataIndex:"id",fixed:"left",width:200},{title:"姓名",dataIndex:"name",width:150,filters:[{text:"Male",value:"male"},{text:"Female",value:"female"}]},{title:"地址",dataIndex:"address",width:300},{title:"编号",dataIndex:"no",width:150,sorter:!0,defaultHidden:!0},{title:"开始时间",width:150,sorter:!0,dataIndex:"beginTime"},{title:"结束时间",width:150,sorter:!0,dataIndex:"endTime"}],P=c({components:{BasicTable:T,TableAction:B,PageWrapper:g},setup(){const{tableContext:t}=h({designScope:"basic-table-demo",tableProps:{api:w,title:"可展开表格演示",titleHelpMessage:["已启用expandRowByClick","已启用stopButtonPropagation"],columns:C,rowKey:"id",canResize:!1,expandRowByClick:!0,actionColumn:{width:160,title:"Action",dataIndex:"action"},useSearchForm:!1}}),[r]=t;function a(n){}function p(n){}return{registerTable:r,handleDelete:a,handleOpen:p}}});function A(t,r,a,p,n,D){const l=e("TableAction"),s=e("BasicTable"),d=e("PageWrapper");return f(),u(d,{title:"可展开表格",content:"不可与scroll共用。TableAction组件可配置stopButtonPropagation来阻止操作按钮的点击事件冒泡，以便配合Table组件的expandRowByClick"},{default:i(()=>[m(s,{onRegister:t.registerTable},{expandedRowRender:i(({record:o})=>[b("span",null,"No: "+x(o.no),1)]),action:i(({record:o})=>[m(l,{stopButtonPropagation:"",actions:[{label:"删除",icon:"ic:outline-delete-outline",onClick:t.handleDelete.bind(null,o)}],dropDownActions:[{label:"启用",popConfirm:{title:"是否启用？",confirm:t.handleOpen.bind(null,o)}}]},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister"])]),_:1})}const Ft=_(P,[["render",A]]);export{Ft as default};
