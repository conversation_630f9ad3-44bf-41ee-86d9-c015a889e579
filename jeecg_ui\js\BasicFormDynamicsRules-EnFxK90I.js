import{d as a,aB as u,ar as c,u as i}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as l}from"./useForm-CgkFTrrO.js";import{B as d}from"./BasicForm-DBcXiHk0.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const Bt=a({__name:"BasicFormDynamicsRules",setup(f){const m=[{field:"visitor",label:"来访人员",component:"Input",required:!0},{field:"accessed",label:"来访日期",component:"DatePicker",required:({values:t})=>!t.accessed},{field:"phone",label:"来访人手机号",component:"Input",dynamicRules:({values:t})=>[{required:!0,validator:(n,r)=>new Promise((s,o)=>{r||o("请输入手机号！"),/^1[3456789]\d{9}$/.test(r)||o("请输入正确手机号！"),s()})}]}],[p]=l({schemas:m,showResetButton:!1,labelWidth:"150px",submitButtonOptions:{text:"提交",preIcon:""},actionColOptions:{span:17}});function e(t){}return(t,n)=>(c(),u(i(d),{onRegister:i(p),style:{"margin-top":"20px"},onSubmit:e},null,8,["onRegister"]))}});export{Bt as default};
