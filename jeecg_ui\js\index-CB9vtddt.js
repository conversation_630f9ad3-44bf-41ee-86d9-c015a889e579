import{d as m,f as s,ag as a,aB as i,ar as l,aD as d,k as c}from"./vue-vendor-dy9k-Yad.js";import{T as u}from"./index-QxsVJqiT.js";import{P as f}from"./index-CtJ0w2CP.js";import{a as g}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const h=m({components:{Tinymce:u,PageWrapper:f},setup(){const e=s("hello world!");function o(n){}return{handleChange:o,value:e}}});function _(e,o,n,C,v,P){const t=a("Tinymce"),r=a("PageWrapper");return l(),i(r,{title:"富文本组件示例"},{default:d(()=>[c(t,{modelValue:e.value,"onUpdate:modelValue":o[0]||(o[0]=p=>e.value=p),onChange:e.handleChange,width:"100%"},null,8,["modelValue","onChange"])]),_:1})}const U=g(h,[["render",_]]);export{U as default};
