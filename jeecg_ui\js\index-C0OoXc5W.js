import{d as f,ag as r,aq as b,ar as h,k as i,aD as a,G as g}from"./vue-vendor-dy9k-Yad.js";import{u as _}from"./index-BkGZ5fiW.js";import{d as C}from"./system-bqUZCbh5.js";import{a as T}from"./index-JbqXEynz.js";import{R as w,s as D,c as R}from"./RoleDrawer-CMRjSodC.js";import{Q as S}from"./componentMap-Bkie1n3v.js";import k from"./BasicTable-xCEZpGLb.js";import{a as B}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const y=f({name:"system-demorole",components:{BasicTable:k,RoleDrawer:w,TableAction:S},setup(){const[o,{openDrawer:t}]=T(),[p,{reload:s}]=_({title:"角色列表",api:C,columns:R,formConfig:{labelWidth:120,schemas:D},useSearchForm:!0,showTableSetting:!0,bordered:!0,showIndexColumn:!1,actionColumn:{width:80,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:void 0}});function l(){t(!0,{isUpdate:!1})}function c(e){t(!0,{record:e,isUpdate:!0})}function n(e){}function m(){s()}return{registerTable:p,registerDrawer:o,handleCreate:l,handleEdit:c,handleDelete:n,handleSuccess:m}}});function $(o,t,p,s,l,c){const n=r("a-button"),m=r("TableAction"),e=r("BasicTable"),u=r("RoleDrawer");return h(),b("div",null,[i(e,{onRegister:o.registerTable},{tableTitle:a(()=>[i(n,{type:"primary",onClick:o.handleCreate},{default:a(()=>t[0]||(t[0]=[g(" 新增角色 ")])),_:1,__:[0]},8,["onClick"])]),action:a(({record:d})=>[i(m,{actions:[{icon:"clarity:note-edit-line",onClick:o.handleEdit.bind(null,d)},{icon:"ant-design:delete-outlined",color:"error",popConfirm:{title:"是否确认删除",confirm:o.handleDelete.bind(null,d)}}]},null,8,["actions"])]),_:1},8,["onRegister"]),i(u,{onRegister:o.registerDrawer,onSuccess:o.handleSuccess},null,8,["onRegister","onSuccess"])])}const Uo=B(y,[["render",$]]);export{Uo as default};
