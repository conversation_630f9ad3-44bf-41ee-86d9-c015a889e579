import{e as b,l as m}from"./vue-vendor-dy9k-Yad.js";import{j as f,B as h,ca as P}from"./antd-vue-vendor-me9YkNVC.js";import{X as v,cC as u}from"./index-CCWaWN5g.js";import{rules as w}from"./validator-B_KkcUnu.js";v();function x(d,n,o){const e=u({one:{colProps:{xs:24,sm:24},itemProps:{labelCol:{xs:24,sm:2},wrapperCol:{xs:24,sm:22}}},tow:{colProps:{xs:24,sm:12},itemProps:{labelCol:{xs:24,sm:4},wrapperCol:{xs:24,sm:20}}},three:{colProps:{xs:24,sm:8},itemProps:{labelCol:{xs:24,sm:6},wrapperCol:{xs:24,sm:18}}}},"three"),a=[{label:"",field:"id",component:"Input",show:!1},{label:"",field:"tableVersion",component:"Input",show:!1},e({label:"表名",field:"tableName",component:"Input",required:!0,dynamicDisabled:({model:l})=>l.tableVersion&&l.tableVersion!=1,dynamicRules:({model:l,schema:t})=>[{validator:(c,p)=>new Promise((s,i)=>{/[\u4E00-\u9FA5]/g.test(p)&&i("不允许输入中文"),s()})},{validator:(c,p)=>new Promise((s,i)=>{p.length>50&&i("表名最长50个字符"),s()})},...w.duplicateCheckRule("onl_cgform_head","table_name",l,t,!0)]}),e({label:"表描述",field:"tableTxt",component:"Input",required:!0,dynamicRules:({model:l,schema:t})=>[{validator:(c,p)=>new Promise((s,i)=>{p.length>200&&i("表描述最长200个字"),s()})}]}),e({label:"表类型",field:"tableType",component:"Select",defaultValue:1,componentProps:{options:[{label:"单表",value:1},{label:"主表",value:2},{label:"附表",value:3}],onChange:o.onTableTypeChange,allowClear:!1}}),{label:"",field:"relationType",component:"InputNumber",render:()=>"",colProps:{xs:0,sm:17},ifShow:r},e({label:"",field:"relationType",component:"RadioGroup",defaultValue:0,componentProps:{options:[{label:"一对多",value:0},{label:"一对一",value:1}],allowClear:!1},colProps:{xs:24,sm:4},itemProps:{colon:!1,labelCol:{xs:0,sm:0},wrapperCol:{xs:24,sm:24}},ifShow:r}),e({label:"序号",field:"tabOrderNum",component:"InputNumber",componentProps:{style:{width:"100%"}},colProps:{xs:24,sm:3},itemProps:{labelCol:{xs:24,sm:7},wrapperCol:{xs:24,sm:17}},ifShow:r}),e({label:"表单分类",field:"formCategory",component:"JDictSelectTag",defaultValue:"temp",componentProps:{dictCode:"ol_form_biz_type",allowClear:!1}}),e({label:"主键策略",field:"idType",component:"Select",defaultValue:"UUID",componentProps:{options:[{label:"ID_WORKER(分布式自增)",value:"UUID"}],allowClear:!1}}),e({label:"序号名称",field:"idSequence",component:"Input",componentProps:{},ifShow:r}),e({label:"显示复选框",field:"isCheckbox",component:"Select",defaultValue:"Y",componentProps:{options:[{label:"是",value:"Y"},{label:"否",value:"N"}],allowClear:!1}}),e({label:"主题模板",field:"themeTemplate",component:"Select",defaultValue:"normal",componentProps:{options:[{label:"默认主题",value:"normal"},{label:"ERP主题(一对多)",value:"erp"},{label:"内嵌子表主题(一对多)",value:"innerTable"},{label:"TAB主题(一对多)",value:"tab"}],allowClear:!1},dynamicDisabled:({model:l})=>l.tableType===1,dynamicRules(){return[{validator({},l){const t=n.value;if(l==="erp"){if(t.joinQuery)return Promise.reject("ERP不支持联合查询功能")}else if(l==="innerTable"&&t.joinQuery)return Promise.reject("内嵌子表不支持联合查询功能");return Promise.resolve()}}]}}),e({label:"表单风格",field:"formTemplate",component:"Select",defaultValue:"1",componentProps:{options:[{label:"一列",value:"1"},{label:"两列",value:"2"},{label:"三列",value:"3"},{label:"四列",value:"4"}],placeholder:"请选择PC表单风格",allowClear:!1}}),e({label:"移动表单风格",field:"formTemplateMobile",component:"Select",defaultValue:"1",componentProps:{options:[{label:"AntDesign模板",value:"1"},{label:"Bootstrap模板",value:"2"}],placeholder:"请选择移动表单风格"},ifShow:!1}),e({label:"滚动条",field:"scroll",component:"Select",defaultValue:1,componentProps:{options:[{label:"有",value:1},{label:"无",value:0}],allowClear:!1}}),e({label:"是否分页",field:"isPage",component:"Select",defaultValue:"Y",componentProps:{options:[{label:"是",value:"Y"},{label:"否",value:"N"}],allowClear:!1}}),e({label:"是否树",field:"isTree",component:"Select",defaultValue:"N",componentProps:{options:[{label:"是",value:"Y"},{label:"否",value:"N"}],onChange:o.onIsTreeChange,allowClear:!1},dynamicRules({model:l}){return[{validator({},t){return t==="Y"&&(l.tableType==2||l.tableType==3)?Promise.reject("主表和附表不支持树类型！"):Promise.resolve()}}]},show({model:l,values:t}){return l.tableType==2||l.tableType==3?(l.isTree="N",o.onIsTreeChange("N"),!1):!0}}),e({label:" ",field:"extConfigJson",component:"Input",slot:"extConfigButton",itemProps:{colon:!1}}),e({label:"树表单父ID",field:"treeParentIdField",component:"Input",ifShow:r}),e({label:"是否有子节点字段",field:"treeIdField",component:"Input",show:!1}),e({label:"树开表单列",field:"treeFieldname",required:!0,component:"Input",ifShow:r}),e({label:"附表",field:"subTableStr",component:"Input",componentProps:{disabled:!0,placeholder:" ",allowClear:!1},ifShow:o.ifShowOfSubTableStr},"one")];function r({field:l,model:t}){switch(l){case"relationType":case"tabOrderNum":return t.tableType===3;case"treeParentIdField":case"treeIdField":case"treeFieldname":return t.isTree==="Y";case"idSequence":return t.idType==="SEQUENCE"}return!0}return{formSchemas:a}}function I(d,n){const o=u({left:{colProps:{xs:24,sm:7},itemProps:{labelCol:{xs:24,sm:11},wrapperCol:{xs:24,sm:13}},style:{width:"100%"}},right:{colProps:{xs:24,sm:17},itemProps:{labelCol:{xs:24,sm:3},wrapperCol:{xs:24,sm:20}},style:{width:"100%"}}},"left");return{formSchemas:[o({label:"弹窗默认全屏",field:"modelFullscreen",component:"RadioButtonGroup",componentProps:{options:[{label:"开启",value:1},{label:"关闭",value:0}],buttonStyle:"solid"}},"left"),o({label:"弹窗宽度",field:"modalMinWidth",component:"InputNumber",componentProps:{style:"width: 80%",placeholder:"弹窗最小宽度（单位：px）"},dynamicDisabled:({model:e})=>e.modelFullscreen},"right"),o({label:"开启表单评论",field:"commentStatus",component:"RadioButtonGroup",componentProps:{options:[{label:"开启",value:1},{label:"关闭",value:0}],buttonStyle:"solid"}},"left"),o({label:"",field:"commentStatus",component:"InputNumber",render:()=>""},"right"),o({label:"启用联合查询",field:"joinQuery",component:"RadioButtonGroup",componentProps:{options:[{label:"开启",value:1},{label:"关闭",value:0}],buttonStyle:"solid",onChange:n.onJoinQueryChange}},"left"),o({label:"",field:"joinQuery",component:"InputNumber",render:()=>""},"right"),o({label:"集成积木报表",field:"reportPrintShow",component:"RadioButtonGroup",componentProps:{options:[{label:"开启",value:1},{label:"关闭",value:0}],buttonStyle:"solid",onChange:n.onReportPrintShowChange}},"left"),o({label:"报表地址",field:"reportPrintUrl",component:"Input",componentProps:{style:"width: 80%"},dynamicDisabled:({model:e})=>!e.reportPrintShow,dynamicRules:({model:e})=>[{required:!!e.reportPrintShow,message:"请输入报表地址！"},{validator({},a){return/\/jmreport\/view\/{积木报表ID}/.test(a)?Promise.reject("请将{积木报表ID}替换为真实的积木报表ID！"):Promise.resolve()}}]},"right"),o({label:"固定操作列",field:"tableFixedAction",component:"RadioButtonGroup",componentProps:{options:[{label:"开启",value:1},{label:"关闭",value:0}],buttonStyle:"solid"},defaultValue:1},"left"),o({label:"固定方式",field:"tableFixedActionType",component:"Select",componentProps:{options:[{label:"固定到右侧",value:"right"},{label:"固定到左侧",value:"left"}],style:"width: 80%"},defaultValue:"right",dynamicDisabled:({model:e})=>!e.tableFixedAction,dynamicRules:({model:e})=>[{required:!!e.tableFixedAction,message:"请选择固定方式！"}]},"right"),o({label:"表单Label长度",field:"formLabelLengthShow",component:"RadioButtonGroup",componentProps:{options:[{label:"开启",value:1},{label:"关闭",value:0}],buttonStyle:"solid",onChange:n.onFormLabelLengthShow}},"left"),o({label:"Label长度",field:"formLabelLength",component:"InputNumber",componentProps:{style:"width: 80%",placeholder:"自定义表单Label长度"},dynamicDisabled:({model:e})=>!e.formLabelLengthShow,dynamicRules:({model:e})=>[{required:!!e.formLabelLengthShow,message:"请填写表单label长度"}]},"right")]}}function T(d,n,o){const e=u({one:{colProps:{xs:24,sm:24},itemProps:{labelCol:{xs:24,sm:5},wrapperCol:{xs:24,sm:16}}},towOne:{colProps:{xs:24,sm:24},itemProps:{labelCol:{xs:24,sm:3},wrapperCol:{xs:24,sm:20}}},tow:{colProps:{xs:24,sm:12},itemProps:{labelCol:{xs:24,sm:6},wrapperCol:{xs:24,sm:16}}}},"one"),a=b(()=>o.value?"one":"tow");return{formSchemas:b(()=>[e({label:"代码生成目录",field:"projectPath",render:({model:r,field:l})=>m(f.Search,{value:r[l],onChange:t=>{r[l]=t.target.value,n.onProjectPathChange(t)},onSearch:n.onProjectPathSearch},{enterButton:()=>m(h,{preIcon:"ant-design:folder-open"},{default:()=>"浏览",icon:()=>m(P)})}),component:"InputSearch",required:!0},o.value?"one":"towOne"),e({label:"页面风格",field:"jspMode",component:"Select",componentProps:{options:n.jspModeOptions.value,allowClear:!1}},a.value),e({label:"功能说明",field:"ftlDescription",component:"Input"},a.value),{label:"数据模型",field:"jformType",component:"Input",show:!1},e({label:"表名",field:"tableName_tmp",required:!0,dynamicDisabled:!0,component:"Input"},a.value),e({label:"实体类名",field:"entityName",required:!0,component:"Input",componentProps:{placeholder:"请输入实体类名(首字母大写)"}},a.value),e({label:"包名(小写)",field:"entityPackage",component:"Input",rules:[{required:!0,pattern:/^[a-zA-Z0-9._]*$/,message:"包名必填，且只允许字母、数字、下划线、小数点组合"}]},a.value),e({label:"代码分层样式",field:"packageStyle",component:"Select",componentProps:{disabled:!0,options:[{label:"业务分层",value:"service"},{label:"代码分层",value:"project"}]}},a.value),e({label:"页面代码",field:"vueStyle",required:!0,component:"Input",defaultValue:"vue3",slot:"pageCode"},a.value),{label:"需要生成的代码",field:"codeTypes",component:"Input",show:!1}])}}export{x as F,T as N,I as R};
