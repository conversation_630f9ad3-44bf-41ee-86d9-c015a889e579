var k=(y,v,u)=>new Promise((g,r)=>{var c=t=>{try{e(u.next(t))}catch(p){r(p)}},i=t=>{try{e(u.throw(t))}catch(p){r(p)}},e=t=>t.done?g(t.value):Promise.resolve(t.value).then(c,i);e((u=u.apply(y,v)).next())});import{d as P,f,ag as n,aB as w,ar as _,u as o,aE as j,aD as s,k as l,aq as I,F as z,aC as A,G as b,au as G,at as h}from"./vue-vendor-dy9k-Yad.js";import{u as K,B as O}from"./index-JbqXEynz.js";import{u as S}from"./index-CCWaWN5g.js";import{q as T,h as U}from"./role.api-BvRyEQIC.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";const $={style:{width:"100%","margin-top":"15px"}},H={key:1},le=P({__name:"RoleDataRuleDrawer",emits:["success","register"],setup(y,{emit:v}){const u=v,{createMessage:g}=S(),r=f(""),c=f(""),i=f([]),e=f([]),[t,{setDrawerProps:p,closeDrawer:R}]=K(d=>k(null,null,function*(){yield x(),p({confirmLoading:!1}),r.value=d.functionId,c.value=d.roleId;const a=yield T({functionId:o(r),roleId:o(c)});a.success&&(i.value=a.result.datarule,a.result.drChecked&&(e.value=a.result.drChecked.split(",")))}));function x(){r.value="",c.value="",i.value=[],e.value=[]}function C(){return k(this,null,function*(){(!o(e)||o(e).length==0)&&g.warning("请注意，现未勾选任何数据权限!");let d={permissionId:o(r),roleId:o(c),dataRuleIds:o(e).join(",")};yield U(d),R(),u("success")})}return(d,a)=>{const B=n("a-checkbox"),D=n("a-col"),N=n("Icon"),q=n("a-button"),F=n("a-row"),L=n("a-checkbox-group"),V=n("a-tab-pane"),E=n("a-tabs");return _(),w(o(O),j(d.$attrs,{onRegister:o(t),title:"数据规则配置",width:"450px",destroyOnClose:""}),{default:s(()=>[l(E,{defaultActiveKey:"1"},{default:s(()=>[l(V,{tab:"数据规则",key:"1"},{default:s(()=>[i.value.length>0?(_(),w(L,{key:0,value:e.value,"onUpdate:value":a[0]||(a[0]=m=>e.value=m)},{default:s(()=>[l(F,null,{default:s(()=>[(_(!0),I(z,null,A(i.value,(m,M)=>(_(),w(D,{span:24,key:"dr"+M},{default:s(()=>[l(B,{value:m.id},{default:s(()=>[b(G(m.ruleName),1)]),_:2},1032,["value"])]),_:2},1024))),128)),l(D,{span:24},{default:s(()=>[h("div",$,[l(q,{onClick:C,type:"primary",size:"small"},{default:s(()=>[l(N,{icon:"ant-design:save-outlined"}),a[1]||(a[1]=b("点击保存"))]),_:1,__:[1]})])]),_:1})]),_:1})]),_:1},8,["value"])):(_(),I("div",H,a[2]||(a[2]=[h("h3",null,"无配置信息!",-1)])))]),_:1})]),_:1})]),_:1},16,["onRegister"])}}});export{le as default};
