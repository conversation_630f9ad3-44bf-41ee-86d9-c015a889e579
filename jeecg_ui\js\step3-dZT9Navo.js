import{d as l,e as d,u as c,h as _,o as b,ag as i,aB as h,ar as k,aD as p,k as C,G as T}from"./vue-vendor-dy9k-Yad.js";import{a2 as g,j as y,a5 as m,B}from"./antd-vue-vendor-me9YkNVC.js";import{H as I,bO as x,N as $,a as w}from"./index-CCWaWN5g.js";import{u as N}from"./useCountdown-CCWNeb_r.js";import"./vxe-table-vendor-B22HppNm.js";const R=l({name:"step3",components:{Button:B,Form:m,FormItem:m.Item,Input:y,Result:g},props:{accountInfo:{type:Object,default:()=>({})},count:I.number.def(5)},emits:["finish"],setup(t,{emit:e}){const{t:a}=$(),{accountInfo:f}=t,{handleBackLogin:u}=x(),{currentCount:o,start:s}=N(t.count),n=d(()=>a("sys.login.subTitleText",[c(o)]));_(()=>{c(o)===1&&setTimeout(()=>{r()},500)});function r(){u(),e("finish")}return b(()=>{s()}),{getSubTitle:n,finish:r}}});function S(t,e,a,f,u,o){const s=i("a-button"),n=i("Result");return k(),h(n,{status:"success",title:"更改密码成功","sub-title":t.getSubTitle},{extra:p(()=>[C(s,{key:"console",type:"primary",onClick:t.finish},{default:p(()=>e[0]||(e[0]=[T(" 返回登录 ")])),_:1,__:[0]},8,["onClick"])]),_:1},8,["sub-title"])}const v=w(R,[["render",S]]);export{v as default};
