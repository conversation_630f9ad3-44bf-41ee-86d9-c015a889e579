var pe=Object.defineProperty;var K=Object.getOwnPropertySymbols;var fe=Object.prototype.hasOwnProperty,ve=Object.prototype.propertyIsEnumerable;var U=(c,l,a)=>l in c?pe(c,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):c[l]=a,G=(c,l)=>{for(var a in l||(l={}))fe.call(l,a)&&U(c,a,l[a]);if(K)for(var a of K(l))ve.call(l,a)&&U(c,a,l[a]);return c};var J=(c,l,a)=>new Promise((P,h)=>{var k=f=>{try{u(a.next(f))}catch(x){h(x)}},r=f=>{try{u(a.throw(f))}catch(x){h(x)}},u=f=>f.done?P(f.value):Promise.resolve(f.value).then(k,r);u((a=a.apply(c,l)).next())});import{d as ge,f as i,r as _e,o as xe,ag as g,aB as N,ar as p,aD as n,at as m,k as o,ah as L,G as _,u as b,au as ye,aq as y,F as O}from"./vue-vendor-dy9k-Yad.js";import{aZ as he,f as T,c as R,j as M,u as ke,d as we,bA as be,aF as F,E as Ce,G as Z}from"./index-CCWaWN5g.js";import Se from"./OneNativeModal-Ct4hqN7g.js";import{P as Ie,M as je}from"./antd-vue-vendor-me9YkNVC.js";import{downloadFile as De}from"./renderUtils-D7XVOFwj.js";import"./vxe-table-vendor-B22HppNm.js";import"./OneNativeForm-Bd1nD0Af.js";import"./JAddInput-CxJ-JBK-.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./useFormItem-CHvpjy4o.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./JPopup-CeU6ry6r.js";import"./JMarkdownEditor-CxtN1OHq.js";import"./index-mbACBRQ9.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */const Te={class:"table-operator"},Ee={class:"ant-alert ant-alert-info",style:{"margin-bottom":"16px"}},ze={style:{"font-weight":"600"}},Ne={key:0,style:{"font-size":"12px","font-style":"italic"}},Oe=["src","preview"],Re={key:0,style:{"font-size":"12px","font-style":"italic"}},Me=["onClick"],Fe={class:"ant-dropdown-link"},Pe=["onClick"],Nt=ge({__name:"OneNativeList",setup(c){const{handleExportXls:l,handleImportXls:a}=Ce(),P=i(!1),h=i({}),k=i(!1),r=i([]),u=i(),f={"X-Access-Token":he()},x=i([{title:"文本",align:"center",dataIndex:"name"},{title:"字典下拉",align:"center",dataIndex:"xiala",customRender:({text:e})=>e?T(r.value.xiala,e):""},{title:"字典单选",align:"center",dataIndex:"danxuan",customRender:({text:e})=>e?T(r.value.danxuan,e):""},{title:"字典多选",align:"center",dataIndex:"duoxuan",customRender:({text:e})=>e?T(r.value.duoxuan,e):""},{title:"开关",align:"center",dataIndex:"kaiguan",customRender:({text:e})=>e?T(r.value.kaiguan,e):""},{title:"日期",align:"center",dataIndex:"riqi",customRender:function({text:e}){return e?e.length>10?e.substr(0,10):e:""}},{title:"年月日时分秒",align:"center",dataIndex:"nyrsfm"},{title:"时间",align:"center",dataIndex:"shijian"},{title:"文件",align:"center",dataIndex:"wenjian"},{title:"图片",align:"center",dataIndex:"tupian"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147}]),w=_e({list:"/test/jeecgDemo/oneNative/list",delete:"/test/jeecgDemo/oneNative/delete",exportXls:"/test/jeecgDemo/oneNative/exportXls",importExcel:"test/jeecgDemo/oneNative/importExcel"}),B=i([]),V=i(!1),v=i({current:1,pageSize:10,pageSizeOptions:["10","20","30"],showTotal:(e,t)=>t[0]+"-"+t[1]+" 共"+e+"条",showQuickJumper:!0,showSizeChanger:!0,total:0}),d=i([]),C=i([]),X=i({column:"createTime",order:"desc"}),A=i({}),{createMessage:W}=ke();function Y(e,t){d.value=e,C.value=t}function ee({pagination:e,filters:t,sorter:s}){v.value=e,X.value=s,A.value=G({},t)}function te(){u.value.disableSubmit=!1,u.value.add()}function ae(){d.value=[],C.value=[]}function ne(){je.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>{M.delete({url:w.delete,data:{ids:d.value}},{joinParamsToUrl:!0}).then(()=>{I()})}})}function Be(e){let t=Q();d.value&&d.value.length>0&&(t.selections=d.join(",")),l(e,w.exportXls,Z(t))}function Ve(e){a(e,w.importExcel,"").then(()=>{I()})}function Q(){let e=Object.assign(h.value,X.value,A.value);return e.field=oe(),e.pageNo=v.value.current,e.pageSize=v.value.pageSize,Z(e)}function oe(){let e="id,";return x.value.forEach(function(t){e+=","+t.dataIndex}),e}function S(e){e===1&&(v.value.current=1),k.value=!0;let t=Q();M.get({url:w.list,params:t},{isTransformResponse:!1}).then(s=>{s.success?(B.value=s.result.records,s.result&&s.result.total?v.value.total=s.result.total:v.value.total=0):W.warning(s.message)}).finally(()=>{k.value=!1})}function Xe(){S(1),d.value=[],C.value=[]}function Ae(){V.value=!V.value}function Qe(){h.value={},S(1)}function le(e){return e&&e.indexOf(",")>0&&(e=e.substring(0,e.indexOf(","))),we(e)}function ie(e){u.value.disableSubmit=!1,u.value.edit(e)}function se(e){u.value.disableSubmit=!0,u.value.edit(e)}function re(e){M.delete({url:w.delete,data:{ids:e}},{joinParamsToUrl:!0}).then(t=>{I()})}function ue(){return J(this,null,function*(){r.value.flzds=yield be({code:"B01"}),r.value.xiala=yield F("sex"),r.value.danxuan=yield F("sex"),r.value.duoxuan=yield F("urgent_level")})}function I(){d.value=[],C.value=[],S(1)}return xe(()=>{r.value.kaiguan=[{text:"是",value:"1"},{text:"否",value:"2"}],S(),ue()}),(e,t)=>{const s=g("a-button"),E=g("a-menu-item"),$=g("a-menu"),q=g("a-dropdown"),de=g("a-divider"),ce=g("a-table"),me=g("a-card");return p(),N(me,{bordered:!1},{default:n(()=>[m("div",Te,[o(s,{onClick:te,type:"primary",preIcon:"ant-design:plus"},{default:n(()=>t[0]||(t[0]=[_("新增")])),_:1,__:[0]}),d.value.length>0?(p(),N(q,{key:0},{overlay:n(()=>[o($,null,{default:n(()=>[o(E,{key:"1",onClick:ne},{default:n(()=>[o(b(R),{icon:"ant-design:delete-outlined"}),t[1]||(t[1]=_(" 删除 "))]),_:1,__:[1]})]),_:1})]),default:n(()=>[o(s,null,{default:n(()=>[t[2]||(t[2]=_("批量操作 ")),o(b(R),{icon:"mdi:chevron-down"})]),_:1,__:[2]})]),_:1})):L("",!0)]),m("div",null,[m("div",Ee,[t[3]||(t[3]=m("i",{class:"anticon anticon-info-circle ant-alert-icon"},null,-1)),t[4]||(t[4]=_(" 已选择 ")),m("a",ze,ye(d.value.length),1),t[5]||(t[5]=_("项 ")),m("a",{style:{"margin-left":"24px"},onClick:ae},"清空")]),o(ce,{ref:"table",size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",class:"j-table-force-nowrap",columns:x.value,dataSource:B.value,pagination:v.value,loading:k.value,rowSelection:{selectedRowKeys:d.value,onChange:Y},onChange:ee},{bodyCell:n(({column:z,text:j,record:D})=>[z.dataIndex==="tupian"?(p(),y(O,{key:0},[j?(p(),y("img",{key:1,src:le(j),preview:D.id,alt:"",class:"anty-img-wrap"},null,8,Oe)):(p(),y("span",Ne,"无图片"))],64)):z.dataIndex==="wenjian"?(p(),y(O,{key:1},[j?(p(),N(s,{key:1,ghost:!0,type:"primary",preIcon:"ant-design:download",size:"small",onClick:H=>b(De)(j)},{default:n(()=>t[6]||(t[6]=[_(" 下载 ")])),_:2,__:[6]},1032,["onClick"])):(p(),y("span",Re,"无文件"))],64)):z.dataIndex==="action"?(p(),y(O,{key:2},[m("a",{onClick:H=>ie(D)},"编辑",8,Me),o(de,{type:"vertical"}),o(q,null,{overlay:n(()=>[o($,{class:"antd-more"},{default:n(()=>[o(E,null,{default:n(()=>[m("a",{onClick:H=>se(D)},"详情",8,Pe)]),_:2},1024),o(E,null,{default:n(()=>[o(b(Ie),{title:"确定删除吗?",onConfirm:()=>re(D.id)},{default:n(()=>t[8]||(t[8]=[m("a",null,"删除",-1)])),_:2,__:[8]},1032,["onConfirm"])]),_:2},1024)]),_:2},1024)]),default:n(()=>[m("a",Fe,[t[7]||(t[7]=_("更多 ")),o(b(R),{icon:"mdi-light:chevron-down"})])]),_:2},1024)],64)):L("",!0)]),_:1},8,["columns","dataSource","pagination","loading","rowSelection"])]),o(Se,{ref_key:"oneProtogenesisModal",ref:u,onOk:I},null,512)]),_:1})}}});export{Nt as default};
