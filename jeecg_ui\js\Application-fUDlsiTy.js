import{d as v,ag as n,aB as c,ar as t,as as e,aD as r,k as p,aq as x,F as B,aC as g,at as l,ah as m,G as i,au as d}from"./vue-vendor-dy9k-Yad.js";import{a7 as _,a6 as f,J as y,L as C}from"./antd-vue-vendor-me9YkNVC.js";import{c as N,a as V}from"./index-CCWaWN5g.js";import{applicationList as D}from"./data-BsYYuWeG.js";import"./vxe-table-vendor-B22HppNm.js";const F=v({components:{List:C,ListItem:C.Item,Card:y,Icon:N,[f.name]:f,[_.name]:_},setup(){return{prefixCls:"account-center-application",list:D}}});function b(o,s,q,z,A,E){const u=n("Icon"),L=n("Card"),$=n("ListItem"),w=n("a-col"),k=n("a-row"),I=n("List");return t(),c(I,{class:e(o.prefixCls)},{default:r(()=>[p(k,{gutter:16},{default:r(()=>[(t(!0),x(B,null,g(o.list,a=>(t(),c(w,{key:a.title,span:6},{default:r(()=>[p($,null,{default:r(()=>[p(L,{hoverable:!0,class:e(`${o.prefixCls}__card`)},{default:r(()=>[l("div",{class:e(`${o.prefixCls}__card-title`)},[a.icon?(t(),c(u,{key:0,class:"icon",icon:a.icon,color:a.color},null,8,["icon","color"])):m("",!0),i(" "+d(a.title),1)],2),l("div",{class:e(`${o.prefixCls}__card-num`)},[s[0]||(s[0]=i(" 活跃用户：")),l("span",null,d(a.active),1),s[1]||(s[1]=i(" 万 "))],2),l("div",{class:e(`${o.prefixCls}__card-num`)},[s[2]||(s[2]=i(" 新增用户：")),l("span",null,d(a.new),1)],2),a.download?(t(),c(u,{key:0,class:e(`${o.prefixCls}__card-download`),icon:a.download},null,8,["class","icon"])):m("",!0)]),_:2},1032,["class"])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["class"])}const j=V(F,[["render",b]]);export{j as default};
