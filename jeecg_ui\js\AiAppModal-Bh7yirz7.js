var L=Object.defineProperty;var k=Object.getOwnPropertySymbols;var R=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var w=(r,o,t)=>o in r?L(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,M=(r,o)=>{for(var t in o||(o={}))R.call(o,t)&&w(r,t,o[t]);if(k)for(var t of k(o))D.call(o,t)&&w(r,t,o[t]);return r};var C=(r,o,t)=>new Promise((e,c)=>{var s=a=>{try{n(t.next(a))}catch(l){c(l)}},d=a=>{try{n(t.throw(a))}catch(l){c(l)}},n=a=>a.done?e(a.value):Promise.resolve(a.value).then(s,d);n((t=t.apply(r,o)).next())});import{f as y,u as I,ag as f,aq as O,ar as B,k as v,aD as u,F as U,aC as V,aB as b,aA as N,at as x,au as A}from"./vue-vendor-dy9k-Yad.js";import{I as q}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{B as z}from"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import{f as E}from"./AiApp.data-wU-aGD0q.js";import{ac as G,aF as H,a as P}from"./index-CCWaWN5g.js";import{s as j}from"./AiApp.api-CU5CuCbf.js";import{u as J}from"./useForm-CgkFTrrO.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const K={name:"AiAppModal",components:{BasicForm:z,BasicModal:q},emits:["success","register"],setup(r,{emit:o}){const t=y("创建应用"),e=y(!1),c=y([]),s=y("chatSimple"),[d,{validate:n,resetFields:a,setFieldsValue:l}]=J({schemas:E,showActionButtonGroup:!1,layout:"vertical",wrapperCol:{span:24}}),[h,{closeModal:g,setModalProps:_}]=G(i=>C(null,null,function*(){yield a(),s.value="chatSimple",e.value=!!(i!=null&&i.isUpdate),I(e)&&(yield l(M({},i.record))),_({minHeight:500,bodyStyle:{padding:"10px"}})}));function p(){return C(this,null,function*(){try{let i=yield n();_({confirmLoading:!0}),i.type=s.value;let m=yield j(i);m&&(g(),e.value?o("success",i):o("success",m))}finally{_({confirmLoading:!1})}})}F();function F(){H("ai_app_type").then(i=>{if(i&&i.length>0)for(const m of i)m.value==="chatSimple"?m.desc="适合新手创建小助手":m.value==="chatFLow"&&(m.desc="适合高级用户自定义小助手的工作流");c.value=i})}function S(){g()}function T(i){s.value=i}return{registerModal:h,registerForm:d,title:t,handleOk:p,handleCancel:S,appTypeOption:c,type:s,handleTypeClick:T}}},Q={class:"p-2"},W={class:"type-title"},X={class:"type-desc"};function Y(r,o,t,e,c,s){const d=f("a-radio"),n=f("a-card"),a=f("a-radio-group"),l=f("BasicForm"),h=f("BasicModal");return B(),O("div",Q,[v(h,{destroyOnClose:"",onRegister:e.registerModal,canFullscreen:!1,width:"800px",title:e.title,onOk:e.handleOk,onCancel:e.handleCancel},{default:u(()=>[v(l,{onRegister:e.registerForm},{typeSlot:u(({model:g,field:_})=>[v(a,{value:e.type,"onUpdate:value":o[0]||(o[0]=p=>e.type=p),style:{display:"flex"}},{default:u(()=>[(B(!0),O(U,null,V(e.appTypeOption,p=>(B(),b(n,{style:N([{"margin-right":"10px",cursor:"pointer",width:"100%"},e.type===p.value?{borderColor:"#3370ff"}:{}]),onClick:F=>e.handleTypeClick(p.value)},{default:u(()=>[v(d,{value:p.value},{default:u(()=>[x("div",W,A(p.title),1),x("div",X,A(p.desc),1)]),_:2},1032,["value"])]),_:2},1032,["onClick","style"]))),256))]),_:1},8,["value"])]),_:1},8,["onRegister"])]),_:1},8,["onRegister","title","onOk","onCancel"])])}const et=P(K,[["render",Y],["__scopeId","data-v-5ee49d75"]]);export{et as default};
