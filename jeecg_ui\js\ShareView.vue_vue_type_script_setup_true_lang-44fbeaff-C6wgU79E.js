import{d as O,e as s,f as B,ag as i,aq as C,ah as v,ar as u,as as H,u as U,aB as w,aD as t,k as n,at as p,au as J,G as y,F as K,q as L,B as P}from"./vue-vendor-dy9k-Yad.js";import{S as Q}from"./shareStore-7de6c7a6-BGigCHd8.js";import W from"./OnlineAutoList-84475f1b-BHheqVRk.js";import{F as X,ap as Y,bu as Z}from"./index-CCWaWN5g.js";import{a as ee}from"./SingleView.vue_vue_type_style_index_0_lang-e0f2f6c0-DEnTX51M.js";const ae={key:0},ue=O({__name:"ShareView",props:{isUpdate:Boolean,isDetail:Boolean},setup(o){const{prefixCls:I}=X("online-share-view-box"),x=o,{getIsMobile:M}=Y(),f=Q(),m=s(()=>f.getCgformRecord),g=s(()=>{var e;return(e=m.value)==null?void 0:e.id}),N=s(()=>{var e;return(e=m.value)==null?void 0:e.tableTxt}),r=B(),S=s(()=>{var e;return(e=r.value)==null?void 0:e.buttonSwitch}),$=s(()=>{var e;return(e=r.value)==null?void 0:e.cgBIBtnMap}),V=s(()=>{var e;return(e=r.value)==null?void 0:e.getFormConfirmButtonCfg}),c=B("none"),d=B();function F(e){c.value="ok",d.value=e,x.isUpdate||f.setDataRecord(null)}function R(){c.value="none",d.value=""}const _=Z();function b({key:e}){const a=d.value;e==="add"?k("add",""):e==="view"?T(a):e==="edit"&&h(a),d.value="",c.value="none"}function h(e){k("u",e)}function T(e){k("d",e)}function k(e,a){var l;if(e==="add"){_(`/online/cgform/share/${g.value}/${e}`);return}a||(a=(l=f.getDataRecord)==null?void 0:l.id),_(`/online/cgform/share/${g.value}/${e}/${a}`)}function G(){window.close(),setTimeout(()=>alert("当前页面无法通过按钮关闭，请手动关闭"),1e3)}return(e,a)=>{const l=i("a-button"),D=i("a-menu-item"),E=i("a-menu"),j=i("a-dropdown"),q=i("a-space-compact"),z=i("a-space"),A=i("a-result");return m.value!=null?(u(),C("div",{key:0,class:H([U(I)])},[c.value!=="none"?(u(),w(A,{key:0,status:"success",title:`${o.isUpdate?"修改":"新增"}成功`,subTitle:"请选择下一步操作"},{extra:t(()=>[n(z,null,{default:t(()=>[n(l,{type:"primary",onClick:R},{default:t(()=>[p("span",null,"继续"+J(o.isUpdate?"修改":"新增"),1)]),_:1}),n(q,{block:""},{default:t(()=>[n(l,{onClick:a[0]||(a[0]=te=>b({key:"view"}))},{default:t(()=>[a[1]||(a[1]=p("span",null,"查看",-1)),o.isUpdate?v("",!0):(u(),C("span",ae,"新")),a[2]||(a[2]=p("span",null,"数据",-1))]),_:1}),n(j,{trigger:["click","hover"]},{overlay:t(()=>[n(E,{onClick:b},{default:t(()=>[o.isUpdate?(u(),w(D,{key:"add"},{default:t(()=>a[3]||(a[3]=[y("添加数据")])),_:1})):v("",!0),o.isUpdate?v("",!0):(u(),w(D,{key:"edit"},{default:t(()=>a[4]||(a[4]=[y("编辑新数据")])),_:1}))]),_:1})]),default:t(()=>[n(l,{preIcon:"ant-design:down"})]),_:1})]),_:1}),n(l,{onClick:G},{default:t(()=>a[5]||(a[5]=[y("关闭")])),_:1})]),_:1})]),_:1},8,["title"])):(u(),C(K,{key:1},[n(ee,{ID:g.value,formName:N.value,isUpdate:o.isUpdate,isDetail:o.isDetail,isMobile:U(M),buttonSwitch:S.value,cgBIBtnMap:$.value,confirmBtnCfg:V.value,onOk:F,onGoEdit:h},null,8,["ID","formName","isUpdate","isDetail","isMobile","buttonSwitch","cgBIBtnMap","confirmBtnCfg"]),L(p("div",null,[n(W,{ref_key:"listRef",ref:r},null,512)],512),[[P,!1]])],64))],2)):v("",!0)}}});export{ue as c};
