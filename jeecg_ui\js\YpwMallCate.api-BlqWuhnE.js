import{render as l}from"./renderUtils-D7XVOFwj.js";import{j as a,u as i}from"./index-CCWaWN5g.js";const p=[{title:"分类名",align:"left",dataIndex:"title"},{title:"分类图片",align:"center",dataIndex:"image",customRender:l.renderImage},{title:"排序",align:"center",dataIndex:"sort"},{title:"状态(0:启用,1:禁用)",align:"center",dataIndex:"isDel",customRender:({text:e})=>l.renderSwitch(e,[{text:"是",value:"Y"},{text:"否",value:"N"}])},{title:"上级ID",align:"center",dataIndex:"parentid"},{title:"创建时间",align:"center",dataIndex:"createTime"},{title:"父级节点",align:"center",dataIndex:"pid"},{title:"是否有子节点",align:"center",dataIndex:"hasChild"}],c=[{label:"创建时间",field:"createTime",component:"RangePicker",componentProps:{valueType:"Date",showTime:!0}}],n=[{label:"分类名",field:"title",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入分类名!"}]},{label:"分类图片",field:"image",component:"JImageUpload",componentProps:{fileMax:0},dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入分类图片!"}]},{label:"排序",field:"sort",component:"InputNumber"},{label:"状态(0:启用,1:禁用)",field:"isDel",component:"JSwitch",componentProps:{}},{label:"上级ID",field:"parentid",component:"InputNumber",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入上级ID!"}]},{label:"父级节点",field:"pid",component:"JTreeSelect",componentProps:{dict:"ypw_mall_cate,title,id",pidField:"pid",pidValue:"0",hasChildField:"has_child"},dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入父级节点!"}]},{label:"是否有子节点",field:"hasChild",component:"Input"},{label:"",field:"id",component:"Input",show:!1}],m={title:{title:"分类名",order:0,view:"text",type:"string"},image:{title:"分类图片",order:1,view:"image",type:"string"},sort:{title:"排序",order:2,view:"number",type:"number"},isDel:{title:"状态(0:启用,1:禁用)",order:3,view:"number",type:"number"},parentid:{title:"上级ID",order:4,view:"number",type:"number"},createTime:{title:"创建时间",order:5,view:"datetime",type:"string"},pid:{title:"父级节点",order:6,view:"text",type:"string"},hasChild:{title:"是否有子节点",order:7,view:"text",type:"string"}};function u(e){return n}const{createConfirm:o}=i();const w="/ypw/ypwMallCate/exportXls",y="/ypw/ypwMallCate/importExcel",g=e=>a.get({url:"/ypw/ypwMallCate/rootList",params:e}),h=(e,t)=>a.delete({url:"/ypw/ypwMallCate/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),C=(e,t)=>{o({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>a.delete({url:"/ypw/ypwMallCate/delete",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},x=(e,t)=>{let r=t?"/ypw/ypwMallCate/edit":"/ypw/ypwMallCate/add";return a.post({url:r,params:e})},M=e=>a.get({url:"/ypw/ypwMallCate/loadTreeRoot",params:e}),f=e=>a.get({url:"/ypw/ypwMallCate/childList",params:e}),I=e=>a.get({url:"/ypw/ypwMallCate/getChildListBatch",params:e},{isTransformResponse:!1});export{f as a,C as b,y as c,w as d,c as e,p as f,I as g,h,x as i,n as j,M as k,g as l,u as m,m as s};
