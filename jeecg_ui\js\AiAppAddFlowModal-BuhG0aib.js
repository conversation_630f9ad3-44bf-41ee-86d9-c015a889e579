var k=(B,s,A)=>new Promise((e,d)=>{var l=t=>{try{n(A.next(t))}catch(g){d(g)}},I=t=>{try{n(A.throw(t))}catch(g){d(g)}},n=t=>t.done?e(t.value):Promise.resolve(t.value).then(l,I);n((A=A.apply(B,s)).next())});import{f as i,ag as u,aq as f,ar as r,k as p,aD as E,at as c,ah as m,aB as M,F as U,aC as J,aA as O,au as x,G as W}from"./vue-vendor-dy9k-Yad.js";import{I as _}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{x as N,h as K}from"./antd-vue-vendor-me9YkNVC.js";import{ac as G,j as R,d as H,a as b}from"./index-CCWaWN5g.js";import{_ as X}from"./knowledge-BXTupIwn.js";const Y="data:image/png;base64,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",Z={name:"AiAppAddFlowModal",components:{Pagination:N,BasicModal:_},emits:["success","register"],setup(B,{emit:s}){const A=i("选择流程"),e=i([]),d=i({}),l=i({}),I=i(1),n=i(10),t=i(0),g=i(""),v=i(["10","20","30"]),[w,{closeModal:y,setModalProps:a}]=G(o=>k(null,null,function*(){e.value=o.flowId?K(o.flowId):"",l.value=o.flowData?K(o.flowData):{},a({minHeight:500,bodyStyle:{padding:"10px"}}),D()}));function h(){return k(this,null,function*(){s("success",{flowId:e.value,flowData:l.value}),Q()})}function Q(){y()}const L=o=>{if(e.value===o.id){e.value="",l.value=null;return}e.value=o.id,l.value=o};function D(){let o={pageNo:I.value,pageSize:n.value,column:"createTime",order:"desc",name:g.value,status:"enable"};P(o).then(C=>{if(C){for(const S of C.records)S.metadata=j(S.metadata);d.value=C.records,t.value=C.total}else d.value=[],t.value=0})}function P(o){return k(this,null,function*(){return R.get({url:"/airag/flow/list",params:o})})}function z(o,C){I.value=o,n.value=C,D()}function F(){e.value="",l.value=null}function T(o){return o?H(o):Y}function j(o){return o?[...JSON.parse(o).inputs]:[]}return{registerModal:w,title:A,handleOk:h,handleCancel:Q,flowList:d,flowId:e,handleSelect:L,pageNo:I,pageSize:n,pageSizeOptions:v,total:t,handlePageChange:z,knowledge:X,searchText:g,loadFlowData:D,handleClearClick:F,flowData:l,getImage:T}}},q={class:"p-2"},V={class:"flex header"},$={style:{display:"flex",width:"100%","align-items":"center"}},ee=["src"],oe={style:{display:"grid","margin-left":"5px","align-items":"center"}},ae={class:"checkbox-name ellipsis"},te={key:0,class:"flex text-status"},se={key:0,class:"tag-text"},le={class:"text-desc mt-10"},ne={key:0,class:"use-select"},ie={class:"ellipsis",style:{"max-width":"150px"}};function re(B,s,A,e,d,l){const I=u("a-input"),n=u("a-tag"),t=u("a-card"),g=u("a-col"),v=u("a-row"),w=u("Pagination"),y=u("BasicModal");return r(),f("div",q,[p(y,{destroyOnClose:"",onRegister:e.registerModal,canFullscreen:!1,width:"600px",title:e.title,onOk:e.handleOk,onCancel:e.handleCancel},{default:E(()=>[c("div",V,[p(I,{onPressEnter:e.loadFlowData,class:"header-search",size:"small",value:e.searchText,"onUpdate:value":s[0]||(s[0]=a=>e.searchText=a),placeholder:"请输入流程名称，回车搜索"},null,8,["onPressEnter","value"])]),p(v,{span:24},{default:E(()=>[(r(!0),f(U,null,J(e.flowList,a=>(r(),M(g,{span:12,onClick:h=>e.handleSelect(a)},{default:E(()=>[p(t,{style:O(a.id===e.flowId?{border:"1px solid #3370ff"}:{}),hoverable:"",class:"checkbox-card","body-style":{width:"100%"}},{default:E(()=>[c("div",$,[c("img",{src:e.getImage(a.icon),class:"flow-icon"},null,8,ee),c("div",oe,[c("span",ae,x(a.name),1),a.metadata&&a.metadata.length>0?(r(),f("div",te,[s[2]||(s[2]=c("span",{class:"tag-input"},"输入",-1)),(r(!0),f(U,null,J(a.metadata,(h,Q)=>(r(),f("div",null,[p(n,{color:"rgba(87,104,161,0.08)",class:"tags-meadata"},{default:E(()=>[Q<3?(r(),f("span",se,x(h.field),1)):m("",!0)]),_:2},1024)]))),256))])):m("",!0)])]),c("div",le,x(a.descr||"暂无描述"),1)]),_:2},1032,["style"])]),_:2},1032,["onClick"]))),256))]),_:1}),e.flowId?(r(),f("div",ne,[s[3]||(s[3]=W(" 已选择 ")),c("span",ie,x(e.flowData.name),1),c("span",{style:{"margin-left":"8px",color:"#3d79fb",cursor:"pointer"},onClick:s[1]||(s[1]=(...a)=>e.handleClearClick&&e.handleClearClick(...a))},"清空")])):m("",!0),e.flowList.length>0?(r(),M(w,{key:1,current:e.pageNo,"page-size":e.pageSize,"page-size-options":e.pageSizeOptions,total:e.total,showQuickJumper:!0,showSizeChanger:!0,onChange:e.handlePageChange,class:"list-footer",size:"small"},null,8,["current","page-size","page-size-options","total","onChange"])):m("",!0)]),_:1},8,["onRegister","title","onOk","onCancel"])])}const ce=b(Z,[["render",re],["__scopeId","data-v-799e47e2"]]),pe=Object.freeze(Object.defineProperty({__proto__:null,default:ce},Symbol.toStringTag,{value:"Module"}));export{pe as A,ce as J,Y as d};
