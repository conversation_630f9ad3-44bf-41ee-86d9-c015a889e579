const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/DropMenuItem-Bab4rTwY.js","js/antd-vue-vendor-me9YkNVC.js","js/vue-vendor-dy9k-Yad.js","js/index-CCWaWN5g.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/LockModal-O29-zQyI.js","js/index-Diw57m_E.js","js/BasicModal-BLFvpBuk.js","js/ModalHeader-BJG9dHtK.js","js/useTimeout-CeTdFD_D.js","js/index-CImCetrx.js","assets/index-BObJM2Lc.css","assets/ModalHeader-HwQKX-UU.css","js/useWindowSizeFn-DDbrQbks.js","js/index-LCGLvkB3.js","js/index-De_W6s5g.js","js/index-D6l0IxOU.js","js/useIntersectionObserver-C4LVxQJW.js","assets/index-zj-Vfn3Q.css","assets/BasicModal-ByeTDAzn.css","js/CustomModal-BakuIxQv.js","assets/CustomModal-DWxHZmza.css","assets/index-yRxe3SQ1.css","js/BasicForm-DBcXiHk0.js","js/componentMap-Bkie1n3v.js","js/useFormItem-CHvpjy4o.js","js/download-CZ-9H9a3.js","js/base64Conver-24EVOS6V.js","js/index-CBCjSSNZ.js","assets/index-NmxXH94f.css","js/index-DFrpKMGa.js","js/useCountdown-CCWNeb_r.js","js/useFormItemSingle-Cw668yj5.js","assets/index-BB9COjV3.css","js/JSelectUser-COkExGbu.js","js/props-CCT78mKr.js","js/JSelectBiz-jOYRdMJf.js","assets/JSelectBiz-CYw1rOZ6.css","assets/JSelectUser-CQvjZTEr.css","js/JAddInput-CxJ-JBK-.js","js/index-QxsVJqiT.js","js/index-BtIdS_Qz.js","js/bem-sRx7x0Ii.js","js/props-qAqCef5R.js","js/useContextMenu-BU2ycxls.js","assets/useContextMenu-DRJLeHo9.css","assets/index-D8VMPii6.css","js/depart.api-BoGnt_ZX.js","assets/JAddInput-i6a6KIoQ.css","js/JSelectDept-I-NqkbOH.js","assets/JSelectDept-WHP406xL.css","js/JAreaSelect-Db7Nhhc_.js","js/areaDataUtil-BXVjRArW.js","assets/JAreaSelect-Pwl_5U28.css","js/JEditorTiptap-BwAoWsi9.js","js/index-ByPySmGo.js","assets/index-BrdQT4ew.css","js/JPopup-CeU6ry6r.js","assets/JPopup-Dn0_YeSX.css","js/JEllipsis-BsXuWNHJ.js","js/JUpload-CRos0F1P.js","assets/JUpload-CsrjJkIs.css","js/JSearchSelect-c_lfTydU.js","js/index-CXHeQyuE.js","js/index-Dyko68ZT.js","assets/index-CTbO_Zqi.css","assets/componentMap-Degzw4_e.css","assets/BasicForm-DTEnYz8c.css","js/useForm-CgkFTrrO.js","js/lock-CRalNsZJ.js","js/header-OZa5fSDc.js","assets/LockModal-CxRaDJpF.css","js/DepartSelect-CmZStQpK.js","js/tenant.api-CTNrRQ_d.js","assets/DepartSelect-fujqtjiH.css","js/UpdatePassword-DoajBBfL.js","js/validator-B_KkcUnu.js","js/user.api-mLAlJze4.js"])))=>i.map(i=>d[i]);
var g=(e,u,i)=>new Promise((m,a)=>{var f=o=>{try{c(i.next(o))}catch(s){a(s)}},t=o=>{try{c(i.throw(o))}catch(s){a(s)}},c=o=>o.done?m(o.value):Promise.resolve(o.value).then(f,t);c((i=i.apply(e,u)).next())});import{H as q,aj as w,F as H,ah as j,bu as z,d as G,ad as W,N as Y,_,u as J,cP as P,o as Q,ca as X,cb as R,bh as Z,a as ee}from"./index-CCWaWN5g.js";import{k as L,D as te}from"./antd-vue-vendor-me9YkNVC.js";import{d as oe,f as d,e as U,ag as l,aq as ae,ar as p,F as se,k as n,aB as k,ah as y,aD as b,at as D,as as C,au as re}from"./vue-vendor-dy9k-Yad.js";import{S as ne}from"./siteSetting-DoyCDlSB.js";import{useHeaderSetting as ie}from"./useHeaderSetting-C-h5S52e.js";import"./index-Diw57m_E.js";import{h as E}from"./header-OZa5fSDc.js";import{r as ce,q as le}from"./dict.api-BW6kWzU4.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const{createMessage:V}=J(),ue=oe({name:"UserDropdown",components:{Dropdown:te,Menu:L,MenuItem:w(()=>_(()=>import("./DropMenuItem-Bab4rTwY.js"),__vite__mapDeps([0,1,2,3,4,5]))),MenuDivider:L.Divider,LockAction:w(()=>_(()=>import("./LockModal-O29-zQyI.js"),__vite__mapDeps([6,2,3,1,4,5,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72]))),DepartSelect:w(()=>_(()=>import("./DepartSelect-CmZStQpK.js"),__vite__mapDeps([73,2,7,3,1,4,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,48,74,75]))),UpdatePassword:w(()=>_(()=>import("./UpdatePassword-DoajBBfL.js"),__vite__mapDeps([76,2,77,3,1,4,5,78,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69])))},props:{theme:q.oneOf(["dark","light"])},setup(){const{prefixCls:e}=H("header-user-dropdown"),{t:u}=Y(),{getShowDoc:i,getUseLockPage:m}=ie(),a=j(),f=z(),t=d(!1),c=d(!1),o=d(null),s=U(()=>{const{realname:r="",avatar:h,desc:F}=a.getUserInfo||{};return{realname:r,avatar:h||E,desc:F}}),v=U(()=>{let{avatar:r}=s.value;return r==E?r:G(r)}),[I,{openModal:A}]=W(),M=d();function $(){return g(this,null,function*(){yield P(o),A(!0)})}function O(){a.confirmLoginOut()}function T(){Q(ne)}function K(){return g(this,null,function*(){if((yield ce()).success){const h=yield le();X(R),Z(R,h.result),V.success(u("layout.header.refreshCacheComplete")),a.setAllDictItems(h.result)}else V.error(u("layout.header.refreshCacheFailure"))})}function B(){M.value.show()}const S=d();function N(){return g(this,null,function*(){t.value=!0,yield P(S),S.value.show(a.getUserInfo.username)})}function x(r){switch(r.key){case"logout":O();break;case"doc":T();break;case"lock":$();break;case"cache":K();break;case"depart":B();break;case"password":N();break;case"account":f("/system/usersetting");break}}return{prefixCls:e,t:u,getUserInfo:s,getAvatarUrl:v,handleMenuClick:x,getShowDoc:i,register:I,getUseLockPage:m,loginSelectRef:M,updatePasswordRef:S,passwordVisible:t,lockActionVisible:c}}}),de=["src"];function pe(e,u,i,m,a,f){const t=l("MenuItem"),c=l("MenuDivider"),o=l("Menu"),s=l("Dropdown"),v=l("LockAction"),I=l("DepartSelect"),A=l("UpdatePassword");return p(),ae(se,null,[n(s,{placement:"bottomLeft",overlayClassName:`${e.prefixCls}-dropdown-overlay`},{overlay:b(()=>[n(o,{onClick:e.handleMenuClick},{default:b(()=>[e.getShowDoc?(p(),k(t,{key:0,itemKey:"doc",text:e.t("layout.header.dropdownItemDoc"),icon:"ion:document-text-outline"},null,8,["text"])):y("",!0),e.getShowDoc?(p(),k(c,{key:1})):y("",!0),n(t,{itemKey:"account",text:e.t("layout.header.dropdownItemSwitchAccount"),icon:"ant-design:setting-outlined"},null,8,["text"]),n(t,{itemKey:"password",text:e.t("layout.header.dropdownItemSwitchPassword"),icon:"ant-design:edit-outlined"},null,8,["text"]),n(t,{itemKey:"depart",text:e.t("layout.header.dropdownItemSwitchDepart"),icon:"ant-design:cluster-outlined"},null,8,["text"]),n(t,{itemKey:"cache",text:e.t("layout.header.dropdownItemRefreshCache"),icon:"ion:sync-outline"},null,8,["text"]),n(t,{itemKey:"logout",text:e.t("layout.header.dropdownItemLoginOut"),icon:"ion:power-outline"},null,8,["text"])]),_:1},8,["onClick"])]),default:b(()=>[D("span",{class:C([[e.prefixCls,`${e.prefixCls}--${e.theme}`],"flex"])},[D("img",{class:C(`${e.prefixCls}__header`),src:e.getAvatarUrl},null,10,de),D("span",{class:C(`${e.prefixCls}__info hidden md:block`)},[D("span",{class:C([`${e.prefixCls}__name  `,"truncate"])},re(e.getUserInfo.realname),3)],2)],2)]),_:1},8,["overlayClassName"]),e.lockActionVisible?(p(),k(v,{key:0,ref:"lockActionRef",onRegister:e.register},null,8,["onRegister"])):y("",!0),n(I,{ref:"loginSelectRef"},null,512),e.passwordVisible?(p(),k(A,{key:1,ref:"updatePasswordRef"},null,512)):y("",!0)],64)}const Ee=ee(ue,[["render",pe]]);export{Ee as default};
