var I=Object.defineProperty;var d=Object.getOwnPropertySymbols;var D=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable;var f=(e,t,o)=>t in e?I(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,b=(e,t)=>{for(var o in t||(t={}))D.call(t,o)&&f(e,o,t[o]);if(d)for(var o of d(t))_.call(t,o)&&f(e,o,t[o]);return e};var m=(e,t,o)=>new Promise((s,a)=>{var c=n=>{try{r(o.next(n))}catch(l){a(l)}},i=n=>{try{r(o.throw(n))}catch(l){a(l)}},r=n=>n.done?s(n.value):Promise.resolve(n.value).then(c,i);r((o=o.apply(e,t)).next())});import{l as w,d as F,f as R,e as M,u as h,ag as g,aB as k,ar as x,aE as N,aD as V,k as G}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{X as T}from"./antd-vue-vendor-me9YkNVC.js";import{c as $,a as C}from"./index-CCWaWN5g.js";import{B as q,u as L}from"./index-JbqXEynz.js";import{c as O}from"./system-bqUZCbh5.js";import{B as j}from"./BasicForm-DBcXiHk0.js";import{u as E}from"./useForm-CgkFTrrO.js";const ne=[{title:"菜单名称",dataIndex:"menuName",width:200,align:"left"},{title:"图标",dataIndex:"icon",width:50,customRender:({record:e})=>w($,{icon:e.icon})},{title:"权限标识",dataIndex:"permission",width:180},{title:"组件",dataIndex:"component"},{title:"排序",dataIndex:"orderNo",width:50},{title:"状态",dataIndex:"status",width:80,customRender:({record:e})=>{const o=~~e.status===0,s=o?"green":"red",a=o?"启用":"停用";return w(T,{color:s},()=>a)}},{title:"创建时间",dataIndex:"createTime",width:180}],U=e=>e==="0",v=e=>e==="1",p=e=>e==="2",ae=[{field:"menuName",label:"菜单名称",component:"Input",colProps:{span:8}},{field:"status",label:"状态",component:"Select",componentProps:{options:[{label:"启用",value:"0"},{label:"停用",value:"1"}]},colProps:{span:8}}],z=[{field:"type",label:"菜单类型",component:"RadioButtonGroup",defaultValue:"0",componentProps:{options:[{label:"目录",value:"0"},{label:"菜单",value:"1"},{label:"按钮",value:"2"}]},colProps:{lg:24,md:24}},{field:"menuName",label:"菜单名称",component:"Input",required:!0},{field:"parentMenu",label:"上级菜单",component:"TreeSelect",componentProps:{replaceFields:{title:"menuName",key:"id",value:"id"},getPopupContainer:()=>document.body}},{field:"orderNo",label:"排序",component:"InputNumber",required:!0},{field:"icon",label:"图标",component:"IconPicker",required:!0,ifShow:({values:e})=>!p(e.type)},{field:"routePath",label:"路由地址",component:"Input",required:!0,ifShow:({values:e})=>!p(e.type)},{field:"component",label:"组件路径",component:"Input",ifShow:({values:e})=>v(e.type)},{field:"permission",label:"权限标识",component:"Input",ifShow:({values:e})=>!U(e.type)},{field:"status",label:"状态",component:"RadioButtonGroup",defaultValue:"0",componentProps:{options:[{label:"启用",value:"0"},{label:"禁用",value:"1"}]}},{field:"isExt",label:"是否外链",component:"RadioButtonGroup",defaultValue:"0",componentProps:{options:[{label:"否",value:"0"},{label:"是",value:"1"}]},ifShow:({values:e})=>!p(e.type)},{field:"keepalive",label:"是否缓存",component:"RadioButtonGroup",defaultValue:"0",componentProps:{options:[{label:"否",value:"0"},{label:"是",value:"1"}]},ifShow:({values:e})=>v(e.type)},{field:"show",label:"是否显示",component:"RadioButtonGroup",defaultValue:"0",componentProps:{options:[{label:"是",value:"0"},{label:"否",value:"1"}]},ifShow:({values:e})=>!p(e.type)}],A=F({name:"MenuDrawer",components:{BasicDrawer:q,BasicForm:j},emits:["success","register"],setup(e,{emit:t}){const o=R(!0),[s,{resetFields:a,setFieldsValue:c,updateSchema:i,validate:r}]=E({labelWidth:100,schemas:z,showActionButtonGroup:!1,baseColProps:{lg:12,md:24}}),[n,{setDrawerProps:l,closeDrawer:B}]=L(u=>m(null,null,function*(){a(),l({confirmLoading:!1}),o.value=!!(u!=null&&u.isUpdate),h(o)&&c(b({},u.record));const S=yield O();i({field:"parentMenu",componentProps:{treeData:S}})})),P=M(()=>h(o)?"编辑菜单":"新增菜单");function y(){return m(this,null,function*(){try{const u=yield r();l({confirmLoading:!0}),B(),t("success")}finally{l({confirmLoading:!1})}})}return{registerDrawer:n,registerForm:s,getTitle:P,handleSubmit:y}}});function W(e,t,o,s,a,c){const i=g("BasicForm"),r=g("BasicDrawer");return x(),k(r,N(e.$attrs,{onRegister:e.registerDrawer,showFooter:"",title:e.getTitle,width:"50%",onOk:e.handleSubmit}),{default:V(()=>[G(i,{onRegister:e.registerForm},null,8,["onRegister"])]),_:1},16,["onRegister","title","onOk"])}const X=C(A,[["render",W]]),re=Object.freeze(Object.defineProperty({__proto__:null,default:X},Symbol.toStringTag,{value:"Module"}));export{X as M,re as a,ne as c,ae as s};
