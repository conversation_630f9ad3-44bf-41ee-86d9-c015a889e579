import{d as A,aq as a,ar as s,as as I,at as r,ah as l,au as d,F as g,aC as h,k as w}from"./vue-vendor-dy9k-Yad.js";import L from"./chatText-DGPEwQvb.js";import{ah as P,d as u,aL as b,a as Q}from"./index-CCWaWN5g.js";import{d as B}from"./ailogo-DG2_TD5d.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const F="/assets/avatar-DWLPAc1w.jpg",N={class:"avatar"},S=["src"],T=["src"],U={class:"content"},V={class:"date"},q={key:0,style:{"margin-right":"10px"}},M={key:0,class:"images"},O=["onClick"],W=["src"],_={class:"msgArea"},$=["onClick"],j=A({__name:"chatMessage",props:["dateTime","text","inversion","error","loading","appData","presetQuestion","images"],emits:["send"],setup(e,{emit:v}){const f=e,{userInfo:o}=P(),k=()=>u(o==null?void 0:o.avatar)||F,x=v,y=()=>{var t;return u((t=f.appData)==null?void 0:t.icon)||B};function C(t){x("send",t)}function m(t){let i=t;return t.hasOwnProperty("url")&&(i=t.url),t.hasOwnProperty("base64Data")&&t.base64Data?t.base64Data:u(i)}function D(t){const i=({index:c,url:p,dom:z})=>{};let n=[m(t)];b({imageList:n,defaultWidth:700,rememberState:!0,onImgLoad:i})}return(t,i)=>(s(),a("div",{class:I(["chat",[e.inversion==="user"?"self":"chatgpt"]])},[r("div",N,[e.inversion==="user"?(s(),a("img",{key:0,src:k()},null,8,S)):(s(),a("img",{key:1,src:y()},null,8,T))]),r("div",U,[r("p",V,[e.inversion==="ai"?(s(),a("span",q,d(e.appData.name||"AI助手"),1)):l("",!0),r("span",null,d(e.dateTime),1)]),e.inversion==="user"&&e.images&&e.images.length>0?(s(),a("div",M,[(s(!0),a(g,null,h(e.images,(n,c)=>(s(),a("div",{key:c,class:"image",onClick:p=>D(n)},[r("img",{src:m(n)},null,8,W)],8,O))),128))])):l("",!0),r("div",_,[w(L,{text:e.text,inversion:e.inversion,error:e.error,loading:e.loading},null,8,["text","inversion","error","loading"])]),e.presetQuestion?(s(!0),a(g,{key:1},h(e.presetQuestion,n=>(s(),a("div",{class:"question",onClick:c=>C(n.descr)},[r("span",null,d(n.descr),1)],8,$))),256)):l("",!0)])],2))}}),X=Q(j,[["__scopeId","data-v-af12ad45"]]);export{X as default};
