import{d as f,ag as w,aB as h,ar as g,aD as b,at as S}from"./vue-vendor-dy9k-Yad.js";import{cs as y,j as T}from"./index-CCWaWN5g.js";import"./index-BkGZ5fiW.js";import{useListPage as x}from"./useListPage-Soxgnx9a.js";import C from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";var R=(i,r,o)=>new Promise((a,m)=>{var s=t=>{try{n(o.next(t))}catch(e){m(e)}},p=t=>{try{n(o.throw(t))}catch(e){m(e)}},n=t=>t.done?a(t.value):Promise.resolve(t.value).then(s,p);n((o=o.apply(i,r)).next())});const v=f({name:"LeftUser",components:{BasicTable:C},emits:["select"],setup(i,{emit:r}){const{tableContext:o,createMessage:a}=x({tableProps:{api:n,rowKey:"id",size:"small",bordered:!0,columns:[{title:"账号",dataIndex:"username",width:200},{title:"姓名",dataIndex:"realname",width:200}],rowSelection:{type:"radio",onChange(e){e.length>0&&r("select",e[0])}},formConfig:{schemas:[{label:"账号",field:"username",component:"JInput",componentProps:{placeholder:"输入账号"}},{label:"姓名",field:"realname",component:"JInput",componentProps:{placeholder:"输入姓名"}}]},canResize:!1,clickToRowSelect:!0,showActionColumn:!1,showTableSetting:!1}}),[m,{clearSelectedRowKeys:s},{rowSelection:p}]=o;function n(e){return R(this,null,function*(){let{code:l,success:c,result:u,message:d}=yield T.get({url:"/sys/user/list",params:e},{isTransformResponse:!1});return c?u:(l===510&&a.warning(d),{})})}function t(){s()}return{rowSelection:p,registerTable:m,clearSelected:t}}});function B(i,r,o,a,m,s){const p=w("BasicTable");return g(),h(p,{onRegister:i.registerTable,rowSelection:i.rowSelection},{tableTop:b(()=>r[0]||(r[0]=[S("span",null,null,-1)])),_:1},8,["onRegister","rowSelection"])}const zt=y(v,[["render",B]]);export{zt as default};
