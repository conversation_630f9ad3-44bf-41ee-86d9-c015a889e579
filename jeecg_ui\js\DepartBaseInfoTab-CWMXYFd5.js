import{d as m,c,f as u,o as f,w as d,aB as l,ar as D,u as o}from"./vue-vendor-dy9k-Yad.js";import{q as _}from"./depart.user.api-D_abnxSU.js";import{u as h}from"./depart.user.data-BFBNnnrj.js";import{u as x,D as B}from"./index-Dce_QJ6p.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./user.api-mLAlJze4.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";const S=m({__name:"DepartBaseInfoTab",props:{data:{require:!0,type:Object}},setup(a){const I=c("prefixCls"),e=a,r=u([]),{descItems:s}=h(r),[i,{setDescProps:p}]=x({data:e.data,schema:s,column:1,labelStyle:{width:"180px"}});function n(t){p({data:t})}return f(()=>{d(()=>e.data,()=>n(e.data),{immediate:!0})}),_().then(t=>r.value=t),(t,b)=>(D(),l(o(B),{onRegister:o(i)},null,8,["onRegister"]))}});export{S as default};
