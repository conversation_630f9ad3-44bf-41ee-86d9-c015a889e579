import{cs as zt,u as Zt,ap as Gt,j as te,cr as Xt,R as el,aZ as tl,cx as ll,ad as nl,bD as ol,bj as al}from"./index-CCWaWN5g.js";import{f as v,c as rl,r as il,w as sl,u as De,n as Ee,J as $,e as ul,ag as x,aq as le,ar as M,as as dl,k as q,aB as ne,ah as de,aG as cl,aD as H,F as pl,aC as ml,aA as fl,at as xe,au as Me}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{d as bl,e as hl,u as gl,l as U,k as yl,g as vl,V as Q,j as ce,S as Sl,f as kl,h as Ve,O as Fl,c as wl}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{al as Cl,p as Tl,h as Ol,o as Ie,c7 as Pl}from"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import{I as Bl,g as Rl}from"./useCustomHook-acb00837-B7NPzH0H.js";import{s as oe}from"./constant-fa63bd66-Ddbq-fz2.js";import"./OnlineForm.vue_vue_type_style_index_0_scoped_3f26e7bd_lang-4ed993c7-l0sNRNKZ.js";import"./componentMap-Bkie1n3v.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import{u as Al}from"./useForm-CgkFTrrO.js";import{B as jl}from"./BasicForm-DBcXiHk0.js";import"./vxe-table-vendor-B22HppNm.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";var Dl=Object.defineProperty,El=Object.defineProperties,xl=Object.getOwnPropertyDescriptors,_e=Object.getOwnPropertySymbols,Ml=Object.prototype.hasOwnProperty,Vl=Object.prototype.propertyIsEnumerable,Ne=(s,f,i)=>f in s?Dl(s,f,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[f]=i,T=(s,f)=>{for(var i in f||(f={}))Ml.call(f,i)&&Ne(s,i,f[i]);if(_e)for(var i of _e(f))Vl.call(f,i)&&Ne(s,i,f[i]);return s},$e=(s,f)=>El(s,xl(f)),O=(s,f,i)=>new Promise((a,A)=>{var j=S=>{try{P(i.next(S))}catch(k){A(k)}},V=S=>{try{P(i.throw(S))}catch(k){A(k)}},P=S=>S.done?a(S.value):Promise.resolve(S.value).then(j,V);P((i=i.apply(s,f)).next())});const pe={optPre:"/online/cgform/api/form/",urlButtonAction:"/online/cgform/api/doButton"},Il={name:"OnlineForm",components:{BasicForm:jl,Loading:al,OnlineSubForm:wl,PrinterOutlined:Pl,OnlinePopModal:Fl},props:{id:{type:String,default:""},formTemplate:{type:Number,default:1},disabled:{type:Boolean,default:!1},isTree:{type:Boolean,default:!1},pidField:{type:String,default:""},submitTip:{type:Boolean,default:!0},modalClass:{type:String,default:""},themeTemplate:{type:String,default:""},subTableSource:{default:()=>({})},taskId:{type:String},cgBIBtnMap:Object,buttonSwitch:Object},emits:["success","rendered","close"],setup(s,{emit:f}){const{createMessage:i}=Zt(),a=v(null),A=v(!0),j=v(!1),V=v(1),P=v(""),S=v(!1),k=v(!1),{getIsMobile:I}=Gt(),z=v(!1),K=rl("foreignkey",{value:{}}),ae=v(!I.value),u=v(null);let J=null;const y=il({reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:"",commentStatus:0}),{onlineFormContext:g,resetContext:Ke,getSubAddBtnCfg:Je,getSubRemoveBtnCfg:Le,getSubOpenAddBtnCfg:Ue,getSubOpenEditBtnCfg:We}=bl(s),{formSchemas:Z,defaultValueFields:W,changeDataIfArray2String:me,tableName:D,dbData:E,checkOnlyFieldValue:Ye,hasSubTable:qe,subTabInfo:F,refMap:B,subDataSource:G,baseColProps:He,createFormSchemas:Qe,linkDownList:Kl,fieldDisplayStatus:L,labelCol:ze,wrapperCol:Ze,labelWidth:Ge}=hl(s,a);let{EnhanceJS:p,initCgEnhanceJs:Xe}=gl(g,!1);sl([F,I],()=>{I.value&&F.value.length&&F.value.forEach(e=>{e.relationType!=1&&e.columns.forEach(t=>{t.width=100})})},{immediate:!0});const{executeJsEnhanced:et}=Bl({},g),[tt,{setProps:lt,validate:fe,resetFields:be,clearValidate:nt,setFieldsValue:R,updateSchema:Y,getFieldsValue:X,scrollToField:he}]=Al({schemas:Z,showActionButtonGroup:!1,baseColProps:He,labelWidth:Ge,labelCol:ze,wrapperCol:Ze}),ge=v(!1);function ot(){let e=s.disabled;ge.value=e,lt({disabled:e})}function at(e,t,l){return O(this,null,function*(){yield rt(),P.value="",yield be(),setTimeout(()=>{nt()},0),E.value="";let n=De(e);k.value=n,dt(),n?yield ve(t):Se(),Ee(()=>{var o;!n&&l&&R(l),it(),re("js","loaded"),ot(),(o=u.value)!=null&&o.length&&(u.value[0].scrollTop=0)})})}function rt(){return O(this,null,function*(){if(s.isTree===!0){let e=s.pidField,t=Z.value;t&&t.length>0&&t.filter(l=>l.field===e).length>0&&(yield Y({field:e,componentProps:{reload:new Date().getTime(),hiddenNodeKey:""}}))}})}const _={keys:[],map:new Map,calcFn:new Map};function it(){let e=$(W[D.value]);De(k)===!1&&U(e,l=>{R(l)});const t=yl(e);_.keys=[...t.keys()],_.map=t,_.calcFn.clear()}function st(e,t){if(_.keys.includes(e)){let l=_.calcFn.get(e);typeof l!="function"&&(l=Cl(()=>{let n=$(W[D.value]);if(Array.isArray(n)&&n.length>0){const o=_.map.get(e);n=n.filter(r=>o.includes(r.field))}else n=[];if(n.length>0){let o=X();U(n,r=>R(r),o)}},150),_.calcFn.set(e,l)),l(t)}}function ye(e,t){let l=$(W[e.key]);U(l,n=>{const{row:o,target:r}=t;let b=[{rowKey:o.id,values:T({},n)}];r.setValues(b)})}function ve(e){return O(this,null,function*(){let t=yield pt(e.id);E.value=Object.assign({},e,t);let l=ct.value,n=Tl(t,...l);s.disabled&&Object.keys(n).map(o=>{!n[o]&&n[o]!==0&&n[o]!=="0"&&delete n[o]}),yield R(n),ut(e.id),Se(t)})}function Se(e){e||(e={});let t=Object.keys(G.value);if(t&&t.length>0){let l={};for(let n of t)l[n]=e[n]||[];G.value=l}}function ut(e){var t;if(s.isTree===!0){const{schema:l}=J,n=(t=l.properties)!=null?t:{},o=Object.entries(n);if(o.length){const r=o.find(([b,d])=>d.view==="sel_tree"&&d.pidComponent!=null);if(r){const b=r[0];Z.value.find(d=>d.field==b)&&Y({field:b,componentProps:{hiddenNodeKey:e}})}}}}function dt(){var e;(e=F.value)==null||e.forEach(t=>{t.relationType==1&&B[t.key].value&&B[t.key].value[0].resetFields()})}let ct=ul(()=>{let e=Z.value,t=[];for(let l of e)t.push(l.field);return t});function pt(e){let t=`${pe.optPre}${s.id}/${e}`;return new Promise((l,n)=>{te.get({url:t},{isTransformResponse:!1}).then(o=>{o.success?l(o.result):(n(),i.warning(o.message))}).catch(()=>{n()})})}function mt(e){return O(this,null,function*(){V.value=e.head.tableType,D.value=e.head.tableName,A.value=e.head.tableType==1,ht(e.head.extConfigJson),Qe(e.schema.properties,e.schema.required,Ye,y),p=Xe(e.enhanceJs),f("rendered",y);let t=yield vl(a);t.$formValueChange=(l,n,o)=>{Et(l,n),o&&R(o),ft(l,n,o),st(l,n)},p&&p.setup&&Pe(p.setup),J=e})}function ft(e,t,l){g.changEvent(e,t,l)}function bt(e){g.addObject2Context("changEvent",e)}function ht(e){let t={reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:"",commentStatus:0,formLabelLength:null};e&&(t=JSON.parse(e),I.value&&(t.commentStatus=0)),z.value=!!t.formLabelLength,Object.keys(t).map(l=>{y[l]=t[l]})}function gt(){S.value=!0,ke()}function ke(){A.value===!0?Ft():yt()}function yt(){vt().then(e=>{we(e)})}function vt(){let e={};return new Promise((t,l)=>{fe().then(n=>t(n),({errorFields:n,values:o})=>{l({errorFields:n,values:o,code:Q,key:D.value,scrollToField:()=>n[0]&&he(n[0].name,{behavior:"smooth",block:"center"})})})}).then(t=>(Object.assign(e,me(t)),s.themeTemplate===oe?Promise.resolve({}):kt())).then(t=>(Object.assign(e,t),Promise.resolve(e))).catch(t=>((t===Q||(t==null?void 0:t.code)===Q)&&(Fe(t.errorFields,t.values,A.value,t.key).then(l=>{l||i.warning("校验未通过")}),t.key&&(St(t.key),t.scrollToField&&setTimeout(()=>t.scrollToField(),150))),Promise.reject(null)))}function St(e){let t=F.value;for(let l=0;l<t.length;l++)if(e==t[l].key){let n=l+"";if(ee.value===n)break;if(ee.value=n,t[l].relationType===0){let o=w(e);Xt(300,()=>o==null?void 0:o.validateTable())}break}}function kt(){return new Promise((e,t)=>O(this,null,function*(){let l={};try{let n=F.value;for(let o=0;o<n.length;o++){let r=n[o].key,b=w(r);if(n[o].relationType==1)try{let d=yield b.getAll();l[r]=[],l[r].push(d)}catch(d){return t(T({code:Q,key:r},d))}else{if(yield b.fullValidateTable())return t({code:Q,key:r});l[r]=b.getTableData()}}}catch(n){t(n)}e(l)}))}function Ft(){return O(this,null,function*(){try{let e=yield fe();e=Object.assign({},E.value,e),e=me(e),j.value=!0,we(e)}catch(e){Array.isArray(e==null?void 0:e.errorFields)&&e.errorFields[0]&&(he(e.errorFields[0].name,{behavior:"smooth",block:"center"}),Fe(e.errorFields,e.values,A.value))}finally{j.value=!1,f("close")}})}function Fe(e,t,l,n=null){return O(this,null,function*(){var o;let r=!1;if(e!=null&&e.length){const b=(o=J.schema)!=null?o:{},{properties:d={}}=b,m=e[0].name[0];let c;if(l||n===D.value)c=d[m];else{const h=d[n],{properties:C={}}=h;c=C[m]}c.type==="number"&&c.view==="number"&&c.defVal&&(yield U([{field:m,type:c.type,value:c.defVal,view:c.view}],h=>{h[m]===t[m]&&(i.warning(`${c.title}的默认值是：${t[m]}，导致校验通不过，需要正确配置默认值！`),r=!0)}))}return r})}function we(e){s.themeTemplate===oe&&k.value&&Object.keys(s.subTableSource).length&&(e=T(T({},e),s.subTableSource)),Nt(je,e).then(()=>{Ct(e)}).catch(t=>{i.warning(t)})}const wt=e=>{const{schema:t}=J,{properties:l}=t,n=(o,r)=>{Object.entries(o).forEach(([b,d])=>{var m;const c=r[b];if(c){if(c.view==="tab"&&el(d)){if(c.properties&&d.forEach(h=>{n(h,c.properties)}),(m=c.columns)!=null&&m.length){const h=Ol(c.columns.filter(C=>C.type==="date"&&C.fieldExtendJson));if(h.length){const C={};h.forEach(N=>{C[N.key]={view:"date",fieldExtendJson:N.fieldExtendJson}}),d.forEach(N=>{n(N,C)})}}}else if(c.view==="date"&&typeof d=="string"&&d!==""){let h=c.fieldExtendJson;h&&(h=JSON.parse(h),h.picker&&h.picker!=="default"&&(h.picker==="year"?o[b]=ce(d).set("month",0).set("date",1).format("YYYY-MM-DD"):h.picker==="month"?o[b]=ce(d).set("date",1).format("YYYY-MM-DD"):h.picker==="week"&&(o[b]=ce(d).startOf("week").format("YYYY-MM-DD"))))}}})};n(e,l)};function Ct(e){Object.keys(e).map(o=>{Array.isArray(e[o])&&e[o].length==0&&(e[o]="")}),wt(e);let t=P.value,l=`${pe.optPre}${s.id}?tabletype=${V.value}`;t&&(l=`${t}?tabletype=${V.value}`),S.value===!0&&(e[Sl]=1),K.value.field&&K.value.value&&(e[K.value.field]=K.value.value);let n=k.value===!0?"put":"post";te.request({url:l,method:n,params:e},{isTransformResponse:!1}).then(o=>{o.success?(o.result&&(e[kl]=o.result),f("success",e),s.submitTip===!0&&i.success(o.message)):i.warning(o.message)}).finally(()=>{j.value=!1,f("close")})}function Tt(e,t,l){t&&l?l.vxeProps?l.setValues([{rowKey:t,values:e}]):l.setValues(e):R(e)}function Ot(e,t){let l={};l[e]=t,R(l)}const ee=v("0"),Ce=v(I.value?"auto":500),Te=v(340);function Pt(e){if(k.value===!0){let t=E.value;return Bt(t,e)}return""}function Bt(e,t){if(e){let l=e[t];return!l&&l!==0&&(l=e[t.toLowerCase()],!l&&l!==0&&(l=e[t.toUpperCase()])),l}return""}function Rt(e,t){if(p&&p[t+"_onlChange"]){let l=p[t+"_onlChange"](),n=Object.keys(e)[0];if(l[n]){let o=w(t).getFormEvent(),r=T({column:{key:n},value:e[n]},o);l[n].call(g,g,r)}}}function At(e,t){if(p&&p[t+"_onlChange"]){let l=p[t+"_onlChange"](g);if(e.column==="all"){let n=Object.keys(l);if(n.length>0)for(let o of n)l[o].call(g,g,e)}else{let n=e.column.key||e.col.key;l[n]&&e.row&&e.row.id&&l[n].call(g,g,e)}}}function jt(e,t){var l;if(p&&p[t+"_onlChange"]){let n=p[t+"_onlChange"](g),o=Object.keys(n);if(o.length>0)for(let r of o)(l=n[r])==null||l.call(g,g,$e(T({},e),{row:e.deleteRows}))}}function Dt(e,t){t.isModalData||ye(e,t)}function Oe(e){return"online_"+e+":"}function Et(e,t){return O(this,null,function*(){if(!p||!p.onlChange||!e)return!1;let l=p.onlChange();l[e]&&setTimeout(()=>O(this,null,function*(){let n={row:yield X(),column:{key:e},value:t};l[e].call(g,g,n)}),0)})}function Pe(e){let t=e.toLocaleString().match(Rl);if(t.length>1){let l=t[1];et(l)}}function re(e,t){if(e=="js"){let l=t+"_hook";p&&p[t]?p[t].call(g,g):p&&p[l]&&Pe(p[l])}else if(e=="action"){let l=E.value,n={formId:s.id,buttonCode:t,dataId:l.id,uiFormData:Object.assign({},l)};te.post({url:`${pe.urlButtonAction}`,params:n},{isTransformResponse:!1}).then(o=>{o.success?i.success("处理完成!"):i.warning("处理失败!")})}}function Be(e){let t=w(e),l=[...t.getNewDataWithId(),...G.value[e]];if(!l||l.length==0)return!1;let n=[];for(let o of l)n.push(o.id);t.removeRowsById(n)}function Re(e,t){if(!t)return!1;let l=w(e);typeof t=="object"?l.addRows(t,!0):this.$message.error("添加子表数据,参数不识别!")}function xt(e,t){Be(e),Re(e,t)}function Mt(e,t){!t&&t.length<=0&&(t=[]),t.map(l=>{l.hasOwnProperty("label")||(l.label=l.text)}),Y({field:e,componentProps:{options:t}})}function Vt({field:e,dict:t,label:l,type:n,subTableName:o}){var r,b;const d=t.split(",").map(m=>encodeURIComponent(m)).join(",");if(n=="subTable"){const m=F.value.find(c=>c.key===o);if(m){const c=m.columns.findIndex(h=>h.key===e);c!==-1&&te.get({url:`/sys/dict/loadDict/${d}`,params:{keyword:"",pageSize:1e3}}).then(h=>{const C=t.split(","),N={customOptions:!0,dictTable:C[0],dictCode:C[2],dictText:C[1],options:h};l&&(N.title=l),m.columns[c]=T(T({},m.columns[c]),N),window.findSubTableInfo=m})}}else if(n=="subForm"){if((b=(r=B[o])==null?void 0:r.value)!=null&&b[0]){const m={field:e,componentProps:{dict:d}};l&&(m.label=l),B[o].value[0].updateSchema(m)}}else{const m={field:e,componentProps:{dict:d}};l&&(m.label=l),Y(m)}}function It(e,t,l){const n=F.value.find(o=>o.key===e);if(n){!l&&l.length<=0&&(l=[]),l.map(r=>{r.hasOwnProperty("label")||(r.label=r.text)});const o=n.columns.findIndex(r=>r.key===t);o!==-1&&(n.columns[o]=$e(T({},n.columns[o]),{options:l,dictCode:""}))}}function _t(e,t,l){var n,o;(o=(n=B[e])==null?void 0:n.value)!=null&&o[0]&&(!l&&l.length<=0&&(l=[]),l.map(r=>{r.hasOwnProperty("label")||(r.label=r.text)}),B[e].value[0].updateSchema({field:t,componentProps:{dictCode:"",options:l}}))}function Nt(e,t){return p&&p.beforeSubmit?p.beforeSubmit(e,t):Promise.resolve()}function $t(e,t){let l=$(L);Object.keys(l).map(n=>{n.endsWith("_load")||n.endsWith("_disabled")||(L[n]=!0)}),e&&e.length>0?Object.keys(l).map(n=>{!n.endsWith("_load")&&e.indexOf(n)<0&&(L[n]=!1)}):t&&t.length>0&&Object.keys(l).map(n=>{t.indexOf(n)>=0&&(L[n]=!1)})}function Kt(e,t){return O(this,null,function*(){P.value=t,yield be(),E.value="",k.value=!0,yield ve(e),yield Ee(()=>{re("js","loaded")})})}function w(e){let t=B[e].value;if(t instanceof Array&&(t=t[0]),!t){i.warning("子表ref找不到:"+e);return}return t}function Jt(){let e=y.reportPrintUrl,t=E.value.id,l=tl();ll(e,t,l)}const[Lt,{openModal:Ae}]=nl(),ie=v(""),se=v(""),ue=v(!0);function Ut(e){ie.value=e.id,se.value=e.key,ue.value=!1,Ae(!0,{isUpdate:!1,tableType:"3"})}function Wt(e){let t=w(e.key).getSelectedData();if(t.length!=1){i.warning("请选择一条数据");return}ie.value=e.id,se.value=e.key,ue.value=!1,Ae(!0,{isUpdate:!0,record:t[0]})}function Yt(e){const t=e[Ve];let l=Ie(e,[Ve]);if(l.id){let n=Ie(T({},l),"id"),o=[{rowKey:l.id,values:n}];w(t).setValues(o)}else w(t).addRows(l,{isOnlineJS:!1,setActive:!1,emitChange:!0,isModalData:!0})}function qt(){if(s.themeTemplate===oe)return;let e=F.value;if(e&&e.length>0){for(let t of e)if(t.relationType!=1){let l=w(t.key);l&&l.clearSelection()}}}function Ht(){let e=X(),t=$(W[D.value]);U(t,l=>{R(l)},e)}function Qt(e,t){let l=F.value;if(l&&l.length>0){let n=l.filter(o=>o.key===e);if(n.length==0)return;if(n[0].relationType==1)w(e).executeFillRule();else{let o=$(W[e]),r=$(t.row);U(o,b=>{const{row:d,target:m}=t;let c=[{rowKey:d.id,values:T({},b)}];m.setValues(c)},r)}}}let je={tableName:D,loading:j,subActiveKey:ee,onlineFormRef:a,getFieldsValue:X,setFieldsValue:R,submitFlowFlag:S,subFormHeight:Ce,subTableHeight:Te,refMap:B,triggleChangeValues:Tt,triggleChangeValue:Ot,sh:L,clearSubRows:Be,addSubRows:Re,clearThenAddRows:xt,changeOptions:Mt,isUpdate:k,getSubTableInstance:w,updateSchema:Y,executeMainFillRule:Ht,executeSubFillRule:Qt,changeSubTableOptions:It,changeSubFormbleOptions:_t,changeRemoteOptions:Vt,changEvent:()=>{},onlineFormValueChange:bt,submitFormAndFlow:gt};return Ke(je),{tableName:D,onlineFormRef:a,registerForm:tt,loading:j,subActiveKey:ee,hasSubTable:qe,subTabInfo:F,refMap:B,subFormHeight:Ce,getSubTableForeignKeyValue:Pt,isUpdate:k,handleSubFormChange:Rt,subTableHeight:Te,onlineFormDisabled:ge,subDataSource:G,getSubTableAuthPre:Oe,handleAdded:Dt,handleSubTableDefaultValue:ye,handleValueChange:At,openSubFormModalForAdd:Ut,openSubFormModalForEdit:Wt,getBtnAuth:(e,t)=>{const l=Oe(t);let n=ol().getOnlineSubTableAuth(l);return n!=null&&n.length?!n.find(o=>o===e):!0},handleRemoved:jt,show:at,createRootProperties:mt,handleSubmit:ke,sh:L,handleCgButtonClick:re,handleCustomFormSh:$t,handleCustomFormEdit:Kt,dbData:E,onOpenReportPrint:Jt,onlineExtConfigJson:y,registerPopModal:Lt,popTableId:ie,popTableName:se,getPopFormData:Yt,popModalRequest:ue,onCloseModal:qt,ERP:oe,rowNumber:ae,isSetFormLabelLength:z,subFormWrapRef:u,getSubAddBtnCfg:Je,getSubRemoveBtnCfg:Le,getSubOpenAddBtnCfg:Ue,getSubOpenEditBtnCfg:We}}},_l=["id"],Nl={key:1};function $l(s,f,i,a,A,j){const V=x("BasicForm"),P=x("online-sub-form"),S=x("a-button"),k=x("JVxeTable"),I=x("a-tab-pane"),z=x("a-tabs"),K=x("Loading"),ae=x("online-pop-modal");return M(),le("div",{id:a.tableName+"_form",class:dl(["onlineFormWrap",[`formTemplate_${i.formTemplate}`]])},[q(V,{ref:"onlineFormRef",onRegister:a.registerForm,name:"online-form_"+a.tableName},null,8,["onRegister","name"]),i.themeTemplate!==a.ERP&&a.hasSubTable?(M(),ne(z,{key:0,activeKey:a.subActiveKey,"onUpdate:activeKey":f[0]||(f[0]=u=>a.subActiveKey=u)},{default:H(()=>[(M(!0),le(pl,null,ml(a.subTabInfo,(u,J)=>(M(),ne(I,{tab:u.describe,key:J+"",forceRender:!0},{default:H(()=>[u.relationType==1?(M(),le("div",{key:0,ref_for:!0,ref:"subFormWrapRef",style:fl({"overflow-y":"auto","overflow-x":"hidden","max-height":a.subFormHeight+"px"})},[q(P,{ref_for:!0,ref:a.refMap[u.key],table:u.key,disabled:a.onlineFormDisabled,"form-template":i.formTemplate,"main-id":a.getSubTableForeignKeyValue(u.foreignKey),properties:u.properties,"required-fields":u.requiredFields,"is-update":a.isUpdate,onFormChange:y=>a.handleSubFormChange(y,u.key)},null,8,["table","disabled","form-template","main-id","properties","required-fields","is-update","onFormChange"])],4)):(M(),le("div",Nl,[q(k,{ref_for:!0,ref:a.refMap[u.key],toolbar:"","keep-source":"","row-number":a.rowNumber,"row-selection":"",height:a.subTableHeight,disabled:a.onlineFormDisabled,columns:u.columns,dataSource:a.subDataSource[u.key],addBtnCfg:a.getSubAddBtnCfg,removeBtnCfg:a.getSubRemoveBtnCfg,onValueChange:y=>a.handleValueChange(y,u.key),onRemoved:y=>a.handleRemoved(y,u.key),authPre:a.getSubTableAuthPre(u.key),onAdded:y=>a.handleAdded(u,y),onExecuteFillRule:y=>a.handleSubTableDefaultValue(u,y)},{toolbarSuffix:H(()=>[!a.onlineFormDisabled&&a.getSubOpenAddBtnCfg.enabled&&a.getBtnAuth("add",u.key)?(M(),ne(S,{key:0,type:"primary",preIcon:a.getSubOpenAddBtnCfg.buttonIcon,onClick:y=>a.openSubFormModalForAdd(u)},{default:H(()=>[xe("span",null,Me(a.getSubOpenAddBtnCfg.buttonName),1)]),_:2},1032,["preIcon","onClick"])):de("",!0),!a.onlineFormDisabled&&a.getSubOpenEditBtnCfg.enabled&&a.getBtnAuth("update",u.key)?(M(),ne(S,{key:1,type:"primary",preIcon:a.getSubOpenEditBtnCfg.buttonIcon,onClick:y=>a.openSubFormModalForEdit(u)},{default:H(()=>[xe("span",null,Me(a.getSubOpenEditBtnCfg.buttonName),1)]),_:2},1032,["preIcon","onClick"])):de("",!0)]),_:2},1032,["row-number","height","disabled","columns","dataSource","addBtnCfg","removeBtnCfg","onValueChange","onRemoved","authPre","onAdded","onExecuteFillRule"])]))]),_:2},1032,["tab"]))),128))]),_:1},8,["activeKey"])):de("",!0),q(K,{loading:a.loading,absolute:!1},null,8,["loading"]),cl(s.$slots,"bottom",{},void 0,!0),q(ae,{formTableType:"3",request:a.popModalRequest,id:a.popTableId,onRegister:a.registerPopModal,onSuccess:a.getPopFormData,taskId:i.taskId,tableName:a.popTableName,topTip:"",isVxeTableData:""},null,8,["request","id","onRegister","onSuccess","taskId","tableName"])],10,_l)}const no=zt(Il,[["render",$l],["__scopeId","data-v-3f26e7bd"]]);export{no as default};
