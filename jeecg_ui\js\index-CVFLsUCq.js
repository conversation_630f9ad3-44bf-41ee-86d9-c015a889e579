import{d as i,ag as o,aB as c,ar as _,aD as t,at as e,k as n}from"./vue-vendor-dy9k-Yad.js";import{ae as l}from"./antd-vue-vendor-me9YkNVC.js";import d from"./TargetContent-BoSrkmuP.js";import{L as f}from"./index-LCGLvkB3.js";import{P as C}from"./index-CtJ0w2CP.js";import{a as u}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const g=i({components:{LazyContainer:f,PageWrapper:C,TargetContent:d,Skeleton:l}}),k={class:"lazy-base-demo-wrap"},x={class:"lazy-base-demo-box"};function y(z,r,B,L,P,$){const a=o("TargetContent"),s=o("Skeleton"),p=o("LazyContainer"),m=o("PageWrapper");return _(),c(m,{title:"懒加载基础示例",content:"向下滚动到可见区域才会加载组件"},{default:t(()=>[e("div",k,[r[0]||(r[0]=e("h1",null,"向下滚动",-1)),e("div",x,[n(p,null,{skeleton:t(()=>[n(s,{rows:10})]),default:t(()=>[n(a)]),_:1})])])]),_:1})}const I=u(g,[["render",y]]);export{I as default};
