import{a as n}from"./user.api-mLAlJze4.js";import{c8 as o}from"./index-CCWaWN5g.js";const u=[{title:"用户账号",dataIndex:"username",width:150},{title:"用户名称",dataIndex:"realname",width:180},{title:"部门",dataIndex:"orgCode",width:200},{title:"性别",dataIndex:"sex_dictText",width:80},{title:"电话",dataIndex:"phone",width:120}],c=[{field:"username",label:"用户账号",component:"Input"}],f=[{title:"部门角色名称",dataIndex:"roleName",width:100},{title:"部门角色编码",dataIndex:"roleCode",width:100},{title:"部门",dataIndex:"departId_dictText",width:100},{title:"备注",dataIndex:"description",width:100}],p=[{field:"roleName",label:"部门角色名称",component:"Input"}],I=[{label:"id",field:"id",component:"Input",show:!1},{field:"roleName",label:"部门角色名称",component:"Input",rules:[{required:!0,message:"部门角色名称不能为空！"},{min:2,max:30,message:"长度在 2 到 30 个字符",trigger:"blur"}]},{field:"roleCode",label:"部门角色编码",component:"Input",dynamicDisabled:({values:r})=>!!r.id,dynamicRules:({model:r})=>[{required:!0,message:"部门角色编码不能为空！"},{min:0,max:64,message:"长度不能超过 64 个字符",trigger:"blur"},{validator:(i,e)=>/[\u4E00-\u9FA5]/g.test(e)?Promise.reject("部门角色编码不可输入汉字！"):new Promise((a,t)=>{let l={tableName:"sys_depart_role",fieldName:"role_code",fieldVal:e,dataId:r.id};n(l).then(d=>{d.success?a():t(d.message||"校验失败")}).catch(d=>{t(d.message||"验证失败")})})}]},{field:"description",label:"描述",component:"Input",rules:[{min:0,max:126,message:"长度不能超过 126 个字符",trigger:"blur"}]}];function b(r){return{descItems:[{field:"departName",label:"机构名称"},{field:"parentId",label:"上级部门",render(e){var a;if(e){let t=o(r.value,l=>l.key==e);return(a=t==null?void 0:t.title)!=null?a:e}return e}},{field:"orgCode",label:"机构编码"},{field:"orgCategory",label:"机构类型",render(e){return e==="1"?"公司":e==="2"?"部门":e==="3"?"岗位":e}},{field:"departOrder",label:"排序"},{field:"mobile",label:"手机号"},{field:"address",label:"地址"},{field:"memo",label:"备注"}]}}export{f as a,I as b,c,p as d,u as e,b as u};
