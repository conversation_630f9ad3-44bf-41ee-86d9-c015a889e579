import{j as e}from"./index-CCWaWN5g.js";import{M as s}from"./antd-vue-vendor-me9YkNVC.js";const d=n=>e.get({url:"/sys/sysAnnouncementSend/getMyAnnouncementSend",params:n}),c=n=>e.put({url:"/sys/sysAnnouncementSend/editByAnntIdAndUserId",params:n}),y=(n,t)=>{s.confirm({title:"确认操作",content:"是否全部标注已读?",okText:"确认",cancelText:"取消",onOk:()=>e.put({url:"/sys/sysAnnouncementSend/readAll",data:n},{joinParamsToUrl:!0}).then(()=>{t()})})},u=n=>e.get({url:"/sys/annountCement/syncNotic",params:n}),a=n=>e.get({url:"/sys/sysAnnouncementSend/getOne",params:{sendId:n}});export{d as a,c as e,a as g,y as r,u as s};
