import"./BasicForm-DBcXiHk0.js";import{q as c}from"./index-CCWaWN5g.js";import{h as u,i as d}from"./componentMap-Bkie1n3v.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JSelectUser-COkExGbu.js";import"./JSelectDept-I-NqkbOH.js";import"./JCodeEditor-B-WXz11X.js";import"./JAddInput-CxJ-JBK-.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import{C as n}from"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";const m={validator({},o){if(!o)return Promise.resolve();const e=o.split(" ").filter(r=>!!r);if(e.length>7)return Promise.reject("Cron表达式最多7项！");let i=o;if(e.length===7){const r=e[6];if(r!=="*"&&r!=="?"){let t=[];if(r.indexOf("-")>=0?t=r.split("-"):r.indexOf("/")?t=r.split("/"):t=[r],t.some(s=>isNaN(Number(s))))return Promise.reject("Cron表达式参数[年]错误："+r)}i=e.slice(0,6).join(" ")}try{return n.parseExpression(i).next(),Promise.resolve()}catch(r){return Promise.reject("Cron表达式错误："+r)}}},Y=m.validator;export{Y as J};
