var te=Object.defineProperty;var j=Object.getOwnPropertySymbols;var ae=Object.prototype.hasOwnProperty,se=Object.prototype.propertyIsEnumerable;var Q=(f,u,r)=>u in f?te(f,u,{enumerable:!0,configurable:!0,writable:!0,value:r}):f[u]=r,R=(f,u)=>{for(var r in u||(u={}))ae.call(u,r)&&Q(f,r,u[r]);if(j)for(var r of j(u))se.call(u,r)&&Q(f,r,u[r]);return f};var T=(f,u,r)=>new Promise((L,D)=>{var d=c=>{try{v(r.next(c))}catch(h){D(h)}},P=c=>{try{v(r.throw(c))}catch(h){D(h)}},v=c=>c.done?L(c.value):Promise.resolve(c.value).then(d,P);v((r=r.apply(f,u)).next())});import{d as ie,f as y,ap as ne,aN as oe,r as B,o as re,ag as ce,aq as S,ar as A,at as q,k as C,ah as M,u as O,H as J,aD as x,G as _,F as E,aB as le}from"./vue-vendor-dy9k-Yad.js";import{f as F}from"./antd-vue-vendor-me9YkNVC.js";import{bF as ue,a as me}from"./index-CCWaWN5g.js";import pe from"./Step1-CdnTDNDA.js";import fe from"./Step2-D14LvGqe.js";import{g as de,a as ye,b as ve,c as ge}from"./auction-DMn7Tok0.js";import{q as Ie}from"./entrustDispose-D2XN6STB.js";import{q as he}from"./entrustBidding-7diNZrW8.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const be={class:"appreciation-entrust"},Ne={class:"steps-wrapper"},Ce={class:"step-content"},Pe={key:0},Te={class:"step-actions"},Ae={key:1},Se={class:"step-actions"},xe=ie({__name:"index",setup(f){const u=y(),r=y(),L=ne(),D=oe(),d=y(0),P=y(!1),v=y(!1),c=y(!1),h=y(""),w=y(2),b=y([]),z=y(!1),V=[{title:"编辑委托信息",description:"填写委托的基本信息"},{title:"发布委托信息",description:"填写联系人信息并发布"}];let t=B({serviceType:1,entrustInfo:{title:"",type:"",description:"",noticeName:""},basicInfo:{subjectName:"",subjectQuantity:"",measurementUnit:"台",auctionDate:"",quantityFlag:0,hasReservePrice:"no",reservePrice:void 0,assetName:"",assetCode:"",assetQuantity:"",assetMeasurementUnit:"",assetQuantityFlag:0},location:{province:"110000",city:"110100",area:"110101",detailAddress:"",coordinates:{latitude:"",longitude:""}},materials:{images:"",attachments:"",entrustDocument:"",specialNote:""},hgyAttachmentList:[]}),g=B({contactInfo:{contactName:"",contactPhone:""}});const G=s=>{s<d.value&&(d.value=s)},W=s=>{},$=()=>{v.value=!0,navigator.geolocation?navigator.geolocation.getCurrentPosition(s=>{setTimeout(()=>{t.location.detailAddress="模拟获取的详细地址：北京市海淀区中关村大街1号",v.value=!1,F.success("位置获取成功")},1e3)},s=>{v.value=!1,F.error("位置获取失败，请手动输入地址")}):(v.value=!1,F.error("浏览器不支持地理位置获取"))},H=s=>{t.serviceType=s,t.basicInfo={subjectName:"",subjectQuantity:"",measurementUnit:"",auctionDate:"",quantityFlag:0,hasReservePrice:"",reservePrice:void 0,assetName:"",assetCode:"",assetQuantity:"",assetMeasurementUnit:"",assetQuantityFlag:0},t.materials={images:"",attachments:"",entrustDocument:"",specialNote:""}},Z=()=>T(null,null,function*(){try{z.value=!0;const s=yield de();if(s&&Array.isArray(s)&&(b.value=s,!c.value)){const a=s.find(e=>e.value==="1004"),n=s.find(e=>e.value==="1003");a&&(t.entrustInfo.title=a.text),n&&(t.entrustInfo.type=n.text)}}catch(s){}finally{z.value=!1}}),K=s=>T(null,null,function*(){var a,n;try{let e;if(t.serviceType===1){if(e=yield he(s),e){yield X(e);return}}else e=yield Ie(s);if(e){const{hgyAssetEntrust:i,hgyEntrustOrder:p,hgyAttachmentList:N}=e;if(i&&(t.serviceType===1?(t.basicInfo.subjectName=i.assetName||"",t.basicInfo.subjectQuantity=((a=i.quantity)==null?void 0:a.toString())||"",t.basicInfo.measurementUnit=i.unit||"",t.basicInfo.reservePrice=i.disposalPrice||void 0,t.basicInfo.hasReservePrice=i.disposalPrice?"1":"0"):t.serviceType===2&&(t.basicInfo.assetName=i.assetName||"",t.basicInfo.assetCode=i.assetNo||"",t.basicInfo.assetQuantity=((n=i.quantity)==null?void 0:n.toString())||"",t.basicInfo.assetMeasurementUnit=i.unit||""),t.location.province=i.provinceCode||"",t.location.city=i.cityCode||"",t.location.area=i.districtCode||"",t.location.detailAddress=i.address||"",t.materials.specialNote=i.specialNotes||""),p&&(t.entrustInfo.title=p.entrustCompanyName||"",t.entrustInfo.type=p.onEntrustCompanyName||"",w.value=p.status||2,g.contactInfo.contactName=p.relationUser||"",g.contactInfo.contactPhone=p.relationPhone||""),c.value&&b.value.length>0){if(!t.entrustInfo.title){const I=b.value.find(o=>o.value==="1004");I&&(t.entrustInfo.title=I.text)}if(!t.entrustInfo.type){const I=b.value.find(o=>o.value==="1003");I&&(t.entrustInfo.type=I.text)}}N&&Array.isArray(N)&&N.length>0&&(t.hgyAttachmentList=N)}}catch(e){}}),X=s=>T(null,null,function*(){var a;try{if(s){const{hgyEntrustOrder:n,hgyAuctionItemTemp:e}=s;if(n&&(t.entrustInfo.title=n.entrustCompanyName||"",t.entrustInfo.type=n.onEntrustCompanyName||"",w.value=n.status||2,g.contactInfo.contactName=n.relationUser||"",g.contactInfo.contactPhone=n.relationPhone||""),c.value&&b.value.length>0){if(!t.entrustInfo.title){const i=b.value.find(p=>p.value==="1004");i&&(t.entrustInfo.title=i.text)}if(!t.entrustInfo.type){const i=b.value.find(p=>p.value==="1003");i&&(t.entrustInfo.type=i.text)}}e&&(t.serviceType===1&&(t.basicInfo.subjectName=e.itemName||"",t.basicInfo.subjectQuantity=((a=e.quantity)==null?void 0:a.toString())||"",t.basicInfo.measurementUnit=e.unit||"",t.basicInfo.quantityFlag=e.quantityFlag||0,t.basicInfo.hasReservePrice=e.hasReservePrice?"1":"0",e.hasReservePrice===1&&(t.basicInfo.reservePrice=e.reservePrice||void 0)),t.location.province=e.province||"",t.location.city=e.city||"",t.location.area=e.district||"",t.location.detailAddress=e.address||"",t.materials.specialNote=e.specialNotes||"",e.attachmentList&&Array.isArray(e.attachmentList)&&e.attachmentList.length>0&&(t.hgyAttachmentList=e.attachmentList))}}catch(n){}});re(()=>T(null,null,function*(){yield Z();const s=D.query.id,a=D.query.serviceType;s&&(c.value=!0,h.value=s,a&&(t.serviceType=parseInt(a)),K(s))}));const Y=()=>T(null,null,function*(){var s;if(d.value===0){if(!(yield(s=u.value)==null?void 0:s.validateForm()))return}else if(!ee())return;d.value<V.length-1&&d.value++}),ee=()=>!0,U=s=>{if(!s)return"other";switch(s.toLowerCase().split(".").pop()){case"pdf":return"pdf";case"ppt":case"pptx":return"ppt";case"xls":case"xlsx":return"excel";case"doc":case"docx":return"doc";case"jpg":case"jpeg":case"png":case"gif":case"bmp":case"webp":return"image";case"mp4":case"avi":case"mov":case"wmv":case"flv":return"video";case"mp3":case"wav":case"flac":case"aac":return"mp3";case"zip":case"rar":case"7z":case"tar":case"gz":return"zip";default:return"other"}},k=s=>T(null,null,function*(){var n;if(yield(n=r.value)==null?void 0:n.validateForm()){P.value=!0;try{const e=R(R({},t),g),i=o=>{const m=b.value.find(l=>l.text===o);return m?m.value:""},p=()=>{if(e.materials.images)try{const o=typeof e.materials.images=="string"?JSON.parse(e.materials.images):e.materials.images;if(Array.isArray(o)&&o.length>0)return o[0].filePath||""}catch(o){}return""},N=o=>[...(()=>{try{const m=typeof e.materials.images=="string"?JSON.parse(e.materials.images):e.materials.images;return(Array.isArray(m)?m:[]).map(l=>({bizType:o,fileName:l.fileName,filePath:l.filePath,fileSize:l.fileSize,fileType:U(l.filePath)}))}catch(m){return[]}})(),...(()=>{try{const m=typeof e.materials.attachments=="string"?JSON.parse(e.materials.attachments):e.materials.attachments;return(Array.isArray(m)?m:[]).map(l=>({bizType:o,fileName:l.fileName,filePath:l.filePath,fileSize:l.fileSize,fileType:U(l.filePath)}))}catch(m){return[]}})(),...(()=>{try{const m=typeof e.materials.entrustDocument=="string"?JSON.parse(e.materials.entrustDocument):e.materials.entrustDocument;return(Array.isArray(m)?m:[]).map(l=>({bizType:o,fileName:l.fileName,filePath:l.filePath,fileSize:l.fileSize,fileType:U(l.filePath)}))}catch(m){return[]}})()];let I;if(e.serviceType===1){const o={hgyEntrustOrder:{id:c.value?h.value:void 0,entrustType:1,serviceType:e.serviceType,status:s,entrustCompanyId:i(e.entrustInfo.title),entrustCompanyName:e.entrustInfo.title,onEntrustCompanyId:i(e.entrustInfo.type),onEntrustCompanyName:e.entrustInfo.type,relationUser:e.contactInfo.contactName,relationPhone:e.contactInfo.contactPhone},hgyAuctionItemTemp:{itemName:e.basicInfo.subjectName,quantity:e.basicInfo.subjectQuantity,unit:e.basicInfo.measurementUnit,auctionDate:e.basicInfo.auctionDate||void 0,quantityFlag:e.basicInfo.quantityFlag,hasReservePrice:e.basicInfo.hasReservePrice==="yes"?1:0,reservePrice:e.basicInfo.hasReservePrice==="yes"&&e.basicInfo.reservePrice?Number(e.basicInfo.reservePrice):void 0,province:e.location.province,city:e.location.city,district:e.location.area,address:e.location.detailAddress,specialNotes:e.materials.specialNote,attachmentList:N("WTJJ")}};I=yield ye(o)}else if(e.serviceType===2){const o={id:c.value?h.value:void 0,entrustCompanyId:Number(i(e.entrustInfo.title)),onEntrustCompanyId:Number(i(e.entrustInfo.type)),entrustType:1,serviceType:e.serviceType,status:s,assetName:e.basicInfo.assetName,assetNo:e.basicInfo.assetCode||void 0,assetType:1,quantity:e.basicInfo.assetQuantity,unit:e.basicInfo.assetMeasurementUnit,provinceCode:e.location.province,cityCode:e.location.city,districtCode:e.location.area,address:e.location.detailAddress,relationUser:e.contactInfo.contactName,relationPhone:e.contactInfo.contactPhone,coverImage:p(),attachmentList:N("WTCZ")};I=yield ve(o)}else if(e.serviceType===3){const o={id:c.value?h.value:void 0,entrustType:1,serviceType:e.serviceType,status:s,noticeName:e.entrustInfo.noticeName,unit:e.basicInfo.measurementUnit||e.basicInfo.assetMeasurementUnit,provinceCode:e.location.province,cityCode:e.location.city,districtCode:e.location.area,relationUser:e.contactInfo.contactName,relationPhone:e.contactInfo.contactPhone,attachmentList:N("WTCG")};I=yield ge(o)}s===2&&(e.serviceType===1?L.push("/orderManage/entrustBidding"):e.serviceType===2?L.push("/orderManage/entrustDispose"):e.serviceType===3&&L.push("/hgy/entrustService/hgyProcurementList"))}catch(e){}finally{P.value=!1}}});return(s,a)=>{const n=ce("a-button");return A(),S("div",be,[q("div",Ne,[C(ue,{current:d.value,steps:V,onChange:G},null,8,["current"])]),q("div",Ce,[d.value===0?(A(),S("div",Pe,[C(pe,{ref_key:"step1Ref",ref:u,modelValue:O(t),"onUpdate:modelValue":a[0]||(a[0]=e=>J(t)?t.value=e:t=e),"location-loading":v.value,"is-edit-mode":c.value,onAreaChange:W,onGetCurrentLocation:$,onServiceTypeChange:H},null,8,["modelValue","location-loading","is-edit-mode"]),q("div",Te,[C(n,{type:"primary",size:"large",class:"next-btn",onClick:Y},{default:x(()=>a[7]||(a[7]=[_(" 下一步 ")])),_:1,__:[7]})])])):M("",!0),d.value===1?(A(),S("div",Ae,[C(fe,{ref_key:"step2Ref",ref:r,modelValue:O(g),"onUpdate:modelValue":a[1]||(a[1]=e=>J(g)?g.value=e:g=e)},null,8,["modelValue"]),q("div",Se,[c.value?(A(),S(E,{key:0},[w.value===1?(A(),S(E,{key:0},[C(n,{size:"large",class:"draft-btn",onClick:a[2]||(a[2]=e=>k(1))},{default:x(()=>a[8]||(a[8]=[_(" 保存至草稿 ")])),_:1,__:[8]}),C(n,{type:"primary",size:"large",class:"submit-btn",loading:P.value,onClick:a[3]||(a[3]=e=>k(2))},{default:x(()=>a[9]||(a[9]=[_(" 确认发布 ")])),_:1,__:[9]},8,["loading"])],64)):(A(),le(n,{key:1,type:"primary",size:"large",class:"submit-btn",loading:P.value,onClick:a[4]||(a[4]=e=>k(2))},{default:x(()=>a[10]||(a[10]=[_(" 保存修改 ")])),_:1,__:[10]},8,["loading"]))],64)):(A(),S(E,{key:1},[C(n,{size:"large",class:"draft-btn",onClick:a[5]||(a[5]=e=>k(1))},{default:x(()=>a[11]||(a[11]=[_(" 保存至草稿 ")])),_:1,__:[11]}),C(n,{type:"primary",size:"large",class:"submit-btn",loading:P.value,onClick:a[6]||(a[6]=e=>k(2))},{default:x(()=>a[12]||(a[12]=[_(" 确认发布 ")])),_:1,__:[12]},8,["loading"])],64))])])):M("",!0)])])}}}),Ut=me(xe,[["__scopeId","data-v-7da8a060"]]);export{Ut as default};
