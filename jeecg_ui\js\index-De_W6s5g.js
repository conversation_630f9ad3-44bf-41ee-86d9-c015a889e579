import{d as l,m as p,O as T,k as g,aE as f,aB as h,ar as S,aP as w,aD as B,aG as _}from"./vue-vendor-dy9k-Yad.js";import{a7 as v,b3 as c,b4 as y,a as E}from"./index-CCWaWN5g.js";function a(t,n="top center 0",o){return l({name:t,props:{group:{type:Boolean,default:!1},mode:{type:String,default:o},origin:{type:String,default:n}},setup(s,{slots:d,attrs:i}){const e=r=>{r.style.transformOrigin=s.origin};return()=>{const r=s.group?T:p;return g(r,f({name:t,mode:s.mode},i,{onBeforeEnter:e}),{default:()=>v(d)})}}})}function m(t,n,o="in-out"){return l({name:t,props:{mode:{type:String,default:o}},setup(s,{attrs:d,slots:i}){return()=>g(p,f({name:t,mode:s.mode},d,{onBeforeEnter:n.beforeEnter,onEnter:n.enter,onLeave:n.leave,onAfterLeave:n.afterLeave,onLeaveCancelled:n.afterLeave}),{default:()=>v(i)})}})}function P(t){return t.charAt(0).toUpperCase()+t.slice(1)}function u(t="",n=!1){const o=n?"width":"height",s=`offset${P(o)}`;return{beforeEnter(e){e._parent=e.parentNode,e._initialStyle={transition:e.style.transition,overflow:e.style.overflow,[o]:e.style[o]}},enter(e){const r=e._initialStyle;e.style.setProperty("transition","none","important"),e.style.overflow="hidden",e.offsetHeight,e.style.transition=r.transition,t&&e._parent&&e._parent.classList.add(t),requestAnimationFrame(()=>{})},afterEnter:i,enterCancelled:i,leave(e){e._initialStyle={transition:"",overflow:e.style.overflow,[o]:e.style[o]},e.style.overflow="hidden",e.style[o]=`${e[s]}px`,e.offsetHeight,requestAnimationFrame(()=>e.style[o]="0")},afterLeave:d,leaveCancelled:d};function d(e){t&&e._parent&&e._parent.classList.remove(t),i(e)}function i(e){const r=e._initialStyle[o];e.style.overflow=e._initialStyle.overflow,r!=null&&(e.style[o]=r),Reflect.deleteProperty(e,"_initialStyle")}}const x=l({name:"CollapseTransition",setup(){return{on:{beforeEnter(t){y(t,"collapse-transition"),t.dataset||(t.dataset={}),t.dataset.oldPaddingTop=t.style.paddingTop,t.dataset.oldPaddingBottom=t.style.paddingBottom,t.style.height="0",t.style.paddingTop=0,t.style.paddingBottom=0},enter(t){t.dataset.oldOverflow=t.style.overflow,t.scrollHeight!==0?(t.style.height=t.scrollHeight+"px",t.style.paddingTop=t.dataset.oldPaddingTop,t.style.paddingBottom=t.dataset.oldPaddingBottom):(t.style.height="",t.style.paddingTop=t.dataset.oldPaddingTop,t.style.paddingBottom=t.dataset.oldPaddingBottom),t.style.overflow="hidden"},afterEnter(t){c(t,"collapse-transition"),t.style.height="",t.style.overflow=t.dataset.oldOverflow},beforeLeave(t){t.dataset||(t.dataset={}),t.dataset.oldPaddingTop=t.style.paddingTop,t.dataset.oldPaddingBottom=t.style.paddingBottom,t.dataset.oldOverflow=t.style.overflow,t.style.height=t.scrollHeight+"px",t.style.overflow="hidden"},leave(t){t.scrollHeight!==0&&(y(t,"collapse-transition"),t.style.height=0,t.style.paddingTop=0,t.style.paddingBottom=0)},afterLeave(t){c(t,"collapse-transition"),t.style.height="",t.style.overflow=t.dataset.oldOverflow,t.style.paddingTop=t.dataset.oldPaddingTop,t.style.paddingBottom=t.dataset.oldPaddingBottom}}}}});function L(t,n,o,s,d,i){return S(),h(p,f({mode:"out-in"},w(t.on)),{default:B(()=>[_(t.$slots,"default")]),_:3},16)}const H=E(x,[["render",L]]),$=a("fade-transition"),O=a("scale-transition"),R=a("slide-y-transition"),F=a("scroll-y-transition"),X=a("slide-y-reverse-transition"),A=a("scroll-y-reverse-transition"),Y=a("slide-x-transition"),k=a("scroll-x-transition"),G=a("slide-x-reverse-transition"),q=a("scroll-x-reverse-transition"),z=a("scale-rotate-transition"),N=m("expand-x-transition",u("",!0)),j=m("expand-transition",u(""));export{H as C,j as E,$ as F,z as S,N as a,q as b,G as c,k as d,Y as e,A as f,X as g,F as h,R as i,O as j};
