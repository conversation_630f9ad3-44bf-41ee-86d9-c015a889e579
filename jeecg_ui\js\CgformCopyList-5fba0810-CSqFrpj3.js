import{d as x,p as k,w as B,ag as e,aq as f,ar as C,F as v,at as q,k as t,as as I,aD as n,ah as D,G as m}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{C as N}from"./CgformModal-c4a4e0c2-DLUwczVd.js";import V from"./DbToOnlineModal-f28ff0a3-qxOiYrGP.js";import O from"./CustomButtonList-c453b654-B8wogI3Z.js";import _ from"./EnhanceJsModal-dc4f9ade-tCtxbi-J.js";import L from"./EnhanceJavaModal-d5a93f2a-DqqmhI7S.js";import $ from"./EnhanceSqlModal-984f045d-BOexfl2F.js";import F from"./AuthManagerDrawer-32556109-CxKgpb0A.js";import j from"./AuthSetterModal-364f1f67-BhvlpxWO.js";import{$ as K,e as Q}from"./useCgformList-f3cb9156-CvD_zrBj.js";import{cs as W}from"./index-CCWaWN5g.js";import"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./useSchemas-b074f3a1-CF0HohIK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./validator-B_KkcUnu.js";import"./DBAttributeTable-1a45c7b7-CnLXQ4iF.js";import"./useTableSync-075826a1-CL-4GwR8.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import"./PageAttributeTable-66e7b485-dF0mTrKY.js";import"./LinkTableConfigModal-7eeb3e58-DvcKZSWX.js";import"./LinkTableFieldConfigModal-b078fcef-ClSEfCRc.js";import"./FieldExtendJsonModal-bf04d70e-CPf_A5jR.js";import"./SetSwitchOptions-f914bc17-Cp2St2Ye.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./CheckDictTable-8a938e3a-CfhOeyF-.js";import"./index-BOX6--gq.js";import"./ForeignKeyTable-92decaea-C-yd8U8L.js";import"./IndexTable-2ded2014-BljD8O97.js";import"./QueryTable-65d3f54f-Dvsxk92z.js";import"./ExtendConfigModal-7d70f362-B_U9Wz5C.js";import"./useOnlineTest-e4bd8be3-hnhsV9Hd.js";import"./useExtendComponent-bb98e568-B7LlULaY.js";import{Q as z}from"./componentMap-Bkie1n3v.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./utils-9fce7606-LGhdtth6.js";import"./BuiltInButtonList.vue_vue_type_script_setup_true_lang-07d0b7d0-BfKwe45a.js";import"./EnhanceJsHistory-8ddb0657-CGGC_rnZ.js";import"./enhance.api-138e6826-BOpOzAwu.js";import"./enhance.data-6601ff44-CPj6ao2j.js";import"./index-JbqXEynz.js";import"./AuthFieldConfig-f1e224cc-BiNWYu_4.js";import"./auth.api-53df4c33-CWNFk1-w.js";import"./auth.data-626c5083-DVuUJlaU.js";import"./AuthButtonConfig-d5bffca0-g2rFBgWF.js";import"./AuthDataConfig-d3b7afa4-Dem3bbP6.js";import"./LeftRole-b0e0b496-Qa0mbQLH.js";import"./LeftDepart-52cb6743-Cn71KUoa.js";import"./LeftUser-dd4b10e2-CznvoKNw.js";import"./AuthFieldTree-5cc0da05-BA2QWlHM.js";import"./AuthButtonTree-b0bd6c40-Dgxz95FG.js";import"./AuthDataTree-f14a98d9-BhZVszsI.js";import"./cgformState-d9f8ec42-C8rx7JjX.js";import G from"./BasicTable-xCEZpGLb.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./CustomModal-BakuIxQv.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const P=x({name:"CgformCopyList",components:{BasicTable:G,TableAction:z,CgformModal:N,DbToOnlineModal:V,CustomButtonList:O,EnhanceJsModal:_,EnhanceJavaModal:L,EnhanceSqlModal:$,AuthManagerDrawer:F,AuthSetterModal:j},setup(){const o=K.copy;k("cgformPageType",o);const{router:r,pageContext:w,getTableAction:E,getDropDownAction:b,onShowCustomButton:A,onShowEnhanceJs:a,onShowEnhanceSql:p,onShowEnhanceJava:l,registerCustomButtonModal:s,registerEnhanceJsModal:c,registerEnhanceSqlModal:g,registerEnhanceJavaModal:d,registerAuthManagerDrawer:u,registerAuthSetterModal:h,registerCgformModal:S,registerDbToOnlineModal:M}=Q({pageType:o,designScope:"online-cgform-list",columns:[{title:"视图表名",dataIndex:"tableName"},{title:"视图表描述",dataIndex:"tableTxt"},{title:"原表版本",dataIndex:"copyVersion"},{title:"视图版本",dataIndex:"tableVersion"}],formSchemas:[{label:"表名",field:"tableName",component:"JInput"}]}),{prefixCls:i,tableContext:J}=w,[T,{reload:R},{rowSelection:y}]=J;return B(r.currentRoute,()=>R()),{prefixCls:i,reload:R,rowSelection:y,getTableAction:E,getDropDownAction:b,onShowCustomButton:A,onShowEnhanceJs:a,onShowEnhanceSql:p,onShowEnhanceJava:l,registerCustomButtonModal:s,registerEnhanceJsModal:c,registerEnhanceSqlModal:g,registerEnhanceJavaModal:d,registerAuthManagerDrawer:u,registerAuthSetterModal:h,registerTable:T,registerCgformModal:S,registerDbToOnlineModal:M}}}),U={key:0,style:{color:"limegreen"}},X={key:1,style:{color:"red"}};function Y(o,r,w,E,b,A){const a=e("a-button"),p=e("TableAction"),l=e("BasicTable"),s=e("CgformModal"),c=e("EnhanceJsModal"),g=e("EnhanceJavaModal"),d=e("EnhanceSqlModal"),u=e("DbToOnlineModal"),h=e("CustomButtonList"),S=e("AuthManagerDrawer"),M=e("AuthSetterModal");return C(),f(v,null,[q("div",{class:I(o.prefixCls)},[t(l,{onRegister:o.registerTable,rowSelection:o.rowSelection},{tableTitle:n(()=>[t(a,{onClick:o.onShowCustomButton,type:"primary",preIcon:"ant-design:highlight"},{default:n(()=>r[0]||(r[0]=[m("自定义按钮")])),_:1},8,["onClick"]),t(a,{onClick:o.onShowEnhanceJs,type:"primary",preIcon:"ant-design:strikethrough"},{default:n(()=>r[1]||(r[1]=[m("JS增强")])),_:1},8,["onClick"]),t(a,{onClick:o.onShowEnhanceSql,type:"primary",preIcon:"ant-design:filter"},{default:n(()=>r[2]||(r[2]=[m("SQL增强")])),_:1},8,["onClick"]),t(a,{onClick:o.onShowEnhanceJava,type:"primary",preIcon:"ant-design:tool"},{default:n(()=>r[3]||(r[3]=[m("Java增强")])),_:1},8,["onClick"])]),dbSync:n(({text:i})=>[i==="Y"?(C(),f("span",U,"已同步")):D("",!0),i==="N"?(C(),f("span",X,"未同步")):D("",!0)]),action:n(({record:i})=>[t(p,{actions:o.getTableAction(i),dropDownActions:o.getDropDownAction(i)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"])],2),t(s,{onRegister:o.registerCgformModal,actionButton:!1,onSuccess:o.reload},null,8,["onRegister","onSuccess"]),t(c,{onRegister:o.registerEnhanceJsModal},null,8,["onRegister"]),t(g,{onRegister:o.registerEnhanceJavaModal},null,8,["onRegister"]),t(d,{onRegister:o.registerEnhanceSqlModal},null,8,["onRegister"]),t(u,{onRegister:o.registerDbToOnlineModal,onSuccess:o.reload},null,8,["onRegister","onSuccess"]),t(h,{onRegister:o.registerCustomButtonModal},null,8,["onRegister"]),t(S,{onRegister:o.registerAuthManagerDrawer},null,8,["onRegister"]),t(M,{onRegister:o.registerAuthSetterModal},null,8,["onRegister"])],64)}const te=W(P,[["render",Y]]);export{te as default};
