import{f as y,u as r,s as p,g as x,j as b}from"./vue-vendor-dy9k-Yad.js";import{ba as v,bb as d}from"./index-CCWaWN5g.js";import{removeResizeListener as w,addResizeListener as k}from"./index-D6l0IxOU.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const W=Symbol("watermark-dom");function B(c=y(document.body)){const f=v(function(){const e=r(c);if(!e)return;const{clientHeight:t,clientWidth:i}=e;l({height:t,width:i})}),s=W.toString(),o=p(),m=()=>{const e=r(o);o.value=void 0;const t=r(c);t&&(e&&t.removeChild(e),w(t,f))};function u(e){const t=document.createElement("canvas"),i=300,a=240;Object.assign(t,{width:i,height:a});const n=t.getContext("2d");return n&&(n.rotate(-20*Math.PI/120),n.font="15px Vedana",n.fillStyle="rgba(0, 0, 0, 0.15)",n.textAlign="left",n.textBaseline="middle",n.fillText(e,i/20,a)),t.toDataURL("image/png")}function l(e={}){const t=r(o);t&&(d(e.width)&&(t.style.width=`${e.width}px`),d(e.height)&&(t.style.height=`${e.height}px`),d(e.str)&&(t.style.background=`url(${u(e.str)}) left top repeat`))}const h=e=>{if(r(o))return l({str:e}),s;const t=document.createElement("div");o.value=t,t.id=s,t.style.pointerEvents="none",t.style.top="0px",t.style.left="0px",t.style.position="absolute",t.style.zIndex="100000";const i=r(c);if(!i)return s;const{clientHeight:a,clientWidth:n}=i;return l({str:e,width:n,height:a}),i.appendChild(t),s};function g(e){h(e),k(document.documentElement,f),x()&&b(()=>{m()})}return{setWatermark:g,clear:m}}export{B as useWatermark};
