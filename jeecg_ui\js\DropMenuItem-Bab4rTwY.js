import{k as i}from"./antd-vue-vendor-me9YkNVC.js";import{d as u,e as d,g as l,ag as a,aB as _,ar as f,aD as I,at as r,k as y,au as g}from"./vue-vendor-dy9k-Yad.js";import{H as s,c as K,a as M}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const k=u({name:"DropdownMenuItem",components:{MenuItem:i.Item,Icon:K},props:{itemKey:s.string,text:s.string,icon:s.string},setup(e){const t=l();return{itemKey:d(()=>{var n,o;return e.itemKey||((o=(n=t==null?void 0:t.vnode)==null?void 0:n.props)==null?void 0:o.itemKey)})}}}),x={class:"flex items-center"};function B(e,t,c,n,o,C){const p=a("Icon"),m=a("MenuItem");return f(),_(m,{key:e.itemKey},{default:I(()=>[r("span",x,[y(p,{icon:e.icon,class:"mr-1"},null,8,["icon"]),r("span",null,g(e.text),1)])]),_:1})}const w=M(k,[["render",B]]);export{w as default};
