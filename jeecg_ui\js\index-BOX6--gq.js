var T=Object.defineProperty,x=Object.defineProperties;var H=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var K=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var F=(e,o,n)=>o in e?T(e,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[o]=n,_=(e,o)=>{for(var n in o||(o={}))K.call(o,n)&&F(e,n,o[n]);if(R)for(var n of R(o))N.call(o,n)&&F(e,n,o[n]);return e},k=(e,o)=>x(e,H(o));var C=(e,o,n)=>new Promise((c,i)=>{var u=a=>{try{p(n.next(a))}catch(d){i(d)}},t=a=>{try{p(n.throw(a))}catch(d){i(d)}},p=a=>a.done?c(a.value):Promise.resolve(a.value).then(u,t);p((n=n.apply(e,o)).next())});import{d as O,f as P,e as J,u as S,o as U,n as $,ag as m,aB as j,ar as z,aD as y,k as g,aJ as D,aK as G,at as V,y as A}from"./vue-vendor-dy9k-Yad.js";import{bn as Q,a as W,cq as X,K as Y}from"./index-CCWaWN5g.js";import"./index-L3cSIXth.js";import{l as Z,j as ee,n as ne,M as oe}from"./antd-vue-vendor-me9YkNVC.js";import{u as te}from"./useForm-CgkFTrrO.js";import{B as ae}from"./BasicForm-DBcXiHk0.js";const se=O({name:"JPrompt",components:{Modal:oe,Spin:ne,Input:ee,BasicForm:ae,ConfigProvider:Z},emits:["register"],setup(e,{emit:o}){const n=P(),{getAntdLocale:c}=Q(),i=P(!1),u=P(!1),t=P({}),p=J(()=>{var l;return(l=t.value.placeholder)!=null?l:"请输入内容"}),[a,{clearValidate:d,setFieldsValue:s,validate:r,updateSchema:v}]=te({compact:!0,wrapperCol:{span:24},schemas:[{label:"",field:"input",component:"Input",slot:"customInput"}],showActionButtonGroup:!1}),M=J(()=>{var I,L;let l=t.value,f={width:(I=l.width)!=null?I:500,title:(L=l.title)!=null?L:"prompt",open:S(i),confirmLoading:S(u)};return k(_(_(_({},f),e),l),{onOk:B,onCancel(){typeof t.value.onCancel=="function"&&t.value.onCancel(),w()}})});U(()=>{o("register",{openModal:q,setLoading:b,getVisible:i})});function q(l){return C(this,null,function*(){var f,h;document.body.focus(),t.value=l,i.value=!0,yield $(),yield v({field:"input",required:t.value.required,rules:t.value.rules,dynamicRules:t.value.dynamicRules}),yield s({input:(f=t.value.defaultValue)!=null?f:""}),yield d(),(h=n.value)==null||h.focus()})}function w(){i.value=!1}function E(){r()}function B(){return C(this,null,function*(){try{const{onOk:l}=t.value;let f=yield r();b(!0),typeof l=="function"?(yield l(f.input))!==!1&&w():w()}finally{b(!1)}})}function b(l){u.value=l}return{inputRef:n,getProps:M,loading:u,options:t,placeholder:p,getAntdLocale:c,onChange:E,onSubmit:B,registerForm:a}}}),le={style:{padding:"20px"}},ie=["innerHTML"];function re(e,o,n,c,i,u){const t=m("Input"),p=m("BasicForm"),a=m("Spin"),d=m("Modal"),s=m("ConfigProvider");return z(),j(s,{locale:e.getAntdLocale},{default:y(()=>[g(d,D(G(e.getProps)),{default:y(()=>[g(a,{spinning:e.loading},{default:y(()=>[V("div",le,[V("div",{innerHTML:e.options.content,style:{"margin-bottom":"8px"}},null,8,ie),g(p,{onRegister:e.registerForm},{customInput:y(({model:r,field:v})=>[g(t,{ref:"inputRef",value:r[v],"onUpdate:value":M=>r[v]=M,placeholder:e.placeholder,onPressEnter:e.onSubmit,onInput:e.onChange},null,8,["value","onUpdate:value","placeholder","onPressEnter","onInput"])]),_:1},8,["onRegister"])])]),_:1},8,["spinning"])]),_:1},16)]),_:1},8,["locale"])}const ue=W(se,[["render",re]]);function he(){function e(o){let n=null;const c=document.createElement("div"),i=g(ue,{onRegister(s){return C(this,null,function*(){n=s,yield $(),s.openModal(o)})},afterClose(){A(null,c),document.body.removeChild(c)}});i.appContext=X(),A(i,c),document.body.appendChild(c);function u(){return n==null&&Y("useJPrompt instance is undefined!"),n}function t(s){var r;(r=u())==null||r.updateModal(s)}function p(){var s;(s=u())==null||s.closeModal()}function a(s){var r;(r=u())==null||r.setLoading(s)}return{closeModal:p,updateModal:t,setLoading:a}}return{createJPrompt:e}}export{he as u};
