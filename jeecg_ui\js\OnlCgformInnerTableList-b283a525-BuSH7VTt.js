import{r as Ge,f as q,w as Ze,n as Xe,ag as S,aq as f,ar as n,aB as s,ah as c,q as z,k as b,u as e,B as J,as as Ye,aD as r,at as x,au as I,G as L,F,aC as V,aE as et,aP as tt,aQ as ot,aJ as nt,aK as rt}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{u as at}from"./index-CCWaWN5g.js";import it from"./OnlineAutoModal-95f46901-Co1FfZLa.js";import lt from"./OnlineCustomModal-c8b1e780-DLSsFULK.js";import U from"./OnlineDetailModal-5b412bb9-C14CjZpZ.js";import{a as st}from"./JImportModal-BnQ3nPZC.js";import{d as pt,m as ct}from"./useListButton-98908683-Bu4g4Tt-.js";import{u as mt,a as ut,g as dt,O as gt}from"./useExtendComponent-bb98e568-B7LlULaY.js";import ft from"./OnlineQueryForm-9248341f-Do5iwEgi.js";import bt from"./SuperQuery-46032e66-Dg-mJkkb.js";import{u as yt}from"./useOnlinePopEvent-687070b7-B8lc-COq.js";import Ct from"./OnlCgformInnerSubTable-6b6fa15a-j8XSUYca.js";import{n as ht}from"./constant-fa63bd66-Ddbq-fz2.js";import"./index-Diw57m_E.js";import"./OnlineForm-58282699-DaLYL1I2.js";import"./index-L3cSIXth.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useCustomHook-acb00837-B7NPzH0H.js";import"./OnlineForm.vue_vue_type_style_index_0_scoped_3f26e7bd_lang-4ed993c7-l0sNRNKZ.js";import"./CommentPanel-HxTGfoA9.js";import"./OnlineFormDetail-fc087725-DWuxKFgz.js";import"./DetailForm-c592b8d8-BIx7KBmJ.js";import"./index-mbACBRQ9.js";import"./OnlineSubFormDetail-8be879b9-LBHMpLKz.js";import"./cgformState-d9f8ec42-C8rx7JjX.js";import{Q as kt}from"./componentMap-Bkie1n3v.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./SuperQueryValComponent.vue_vue_type_script_lang-8fe34917-DN8CXDRQ.js";import It from"./BasicTable-xCEZpGLb.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-BLwcuZxD.js";var H=(K,d,p)=>new Promise((_,T)=>{var y=m=>{try{B(p.next(m))}catch(g){T(g)}},C=m=>{try{B(p.throw(m))}catch(g){T(g)}},B=m=>m.done?_(m.value):Promise.resolve(m.value).then(y,C);B((p=p.apply(K,d)).next())});const vt={class:"p-2"},xt={key:0,style:{"font-size":"12px","font-style":"italic"}},Bt={key:0,style:{"font-size":"12px","font-style":"italic"}},wt=["src","onClick"],St=["innerHTML","onClick"],Tt=["innerHTML"],Rt=["title"],cn={__name:"OnlCgformInnerTableList",setup(K){const d=Ge({tabNav:[],tabIndex:"0"}),p=q([]),_=q(null),{createMessage:T}=at(),{ID:y,onlineTableContext:C,onlineQueryFormOuter:B,loading:m,reload:g,dataSource:$,pagination:j,handleSpecialConfig:W,getColumnList:G,handleChangeInTable:Z,loadData:X,superQueryButtonRef:M,superQueryStatus:Y,handleSuperQuery:ee,onlineExtConfigJson:O,handleFormConfig:te,registerCustomModal:oe,tableReloading:N}=pt();if(!y.value)throw T.warning("地址错误, 配置ID不存在!"),new Error("地址错误, 配置ID不存在!");let{initCgEnhanceJs:ne}=mt(C);const{buttonSwitch:v,cgBIBtnMap:i,getQueryButtonCfg:re,getResetButtonCfg:ae,getFormConfirmButtonCfg:ie,cgTopButtonList:D,importUrl:le,registerModal:se,handleAdd:pe,handleBatchDelete:ce,registerImportModal:me,onImportExcel:ue,onExportExcel:de,cgButtonJsHandler:ge,cgButtonActionHandler:fe,getDropDownActions:be,getActions:ye,initButtonList:Ce,initButtonSwitch:he,registerDetailModal:ke,registerBpmModal:_t}=ct(C,O),E=q(!1);function Ie(){return H(this,null,function*(){try{E.value=!0,yield de()}finally{setTimeout(()=>E.value=!1,1500)}})}const{columns:ve,actionColumn:xe,selectedKeys:Be,rowSelection:we,enableScrollBar:Se,tableScroll:Te,downloadRowFile:Re,getImgView:_e,getPcaText:Q,getFormatDate:Me,handleColumnResult:Ne,hrefComponent:R,viewOnlineCellImage:De,hrefMainTableId:Ee,registerOnlineHrefModal:Pe,registerPopModal:qe,openPopModal:Le,onlinePopModalRef:Fe,popTableId:A,handleClickFieldHref:He}=ut(C,O);Ze(y,()=>{Ke()},{immediate:!0});function Ke(){return H(this,null,function*(){m.value=!0;let o=yield G(ht);Oe(o),yield X(),m.value=!1,C.execButtonEnhance("setup")})}function Oe(o){let l=ne(o.enhanceJs);C.EnhanceJS=l,Ce(o.cgButtonList),he(o.hideColumns),Ne(o),W(o)}function Qe(o){C.queryParam=o,g({mode:"search"})}function Ae(o){return H(this,null,function*(){yield dt(M),M.value.init(o)})}function ze(o){A.value=o.id;let l={title:o.describe};o.record&&o.record.id&&(l.record=o.record,l.isUpdate=!0),Le(!0,l)}yt(ze);const Je=o=>{te(o);const{schema:l}=o,{properties:P}=l,u=[];Object.entries(P).forEach(([w,h])=>{h.view=="tab"&&u.push({tableName:w,tableTxt:h.describe,id:h.id,order:h.order})}),u.sort((w,h)=>w.order-h.order),d.tabNav=u},Ve=(o,l)=>{p.value=[],o&&(p.value=[l.id],_.value=l)},Ue=o=>{g(o),$e()},$e=()=>{if(p.value.length){const o=d.tabIndex;d.tabIndex="-1",Xe(()=>{d.tabIndex=o})}};return(o,l)=>{const P=S("a-skeleton"),u=S("a-button"),w=S("a-tab-pane"),h=S("a-tabs"),je=S("a-modal");return n(),f("div",vt,[e(N)?(n(),s(P,{key:0,active:""})):c("",!0),z(b(ft,{ref_key:"onlineQueryFormOuter",ref:B,id:e(y),queryBtnCfg:e(re),resetBtnCfg:e(ae),onSearch:Qe,onLoaded:Ae},null,8,["id","queryBtnCfg","resetBtnCfg"]),[[J,!e(N)]]),e(N)?c("",!0):(n(),s(e(It),{key:1,ref:"onlineTable",rowKey:"jeecg_row_key",canResize:!0,bordered:!0,showIndexColumn:!1,loading:e(m),columns:e(ve),dataSource:e($),pagination:e(j),rowSelection:e(we),actionColumn:e(xe),showTableSetting:!0,clickToRowSelect:!1,scroll:e(Te),onTableRedo:e(g),class:Ye({"j-table-force-nowrap":e(Se)}),onChange:e(Z),expandedRowKeys:p.value,onExpand:Ve},{tableTitle:r(()=>[e(v).add&&e(i).add.enabled?(n(),s(u,{key:0,type:"primary",preIcon:e(i).add.buttonIcon,onClick:e(pe)},{default:r(()=>[x("span",null,I(e(i).add.buttonName),1)]),_:1},8,["preIcon","onClick"])):c("",!0),e(v).import&&e(i).import.enabled?(n(),s(u,{key:1,type:"primary",preIcon:e(i).import.buttonIcon,onClick:e(ue)},{default:r(()=>[x("span",null,I(e(i).import.buttonName),1)]),_:1},8,["preIcon","onClick"])):c("",!0),e(v).export&&e(i).export.enabled?(n(),s(u,{key:2,type:"primary",preIcon:e(i).export.buttonIcon,loading:E.value,onClick:Ie},{default:r(()=>[x("span",null,I(e(i).export.buttonName),1)]),_:1},8,["preIcon","loading"])):c("",!0),e(D)&&e(D).length>0?(n(!0),f(F,{key:3},V(e(D),(t,a)=>(n(),f(F,null,[t.optType=="js"?(n(),s(u,{key:"cgbtn"+a,onClick:k=>e(ge)(t.buttonCode),type:"primary",preIcon:t.buttonIcon?"ant-design:"+t.buttonIcon:""},{default:r(()=>[L(I(t.buttonName),1)]),_:2},1032,["onClick","preIcon"])):t.optType=="action"?(n(),s(u,{key:"cgbtn"+a,onClick:k=>e(fe)(t.buttonCode),type:"primary",preIcon:t.buttonIcon?"ant-design:"+t.buttonIcon:""},{default:r(()=>[L(I(t.buttonName),1)]),_:2},1032,["onClick","preIcon"])):c("",!0)],64))),256)):c("",!0),e(v).batch_delete&&e(i).batch_delete.enabled?z((n(),s(u,{key:4,preIcon:e(i).batch_delete.buttonIcon,onClick:e(ce)},{default:r(()=>[x("span",null,I(e(i).batch_delete.buttonName),1)]),_:1},8,["preIcon","onClick"])),[[J,e(Be).length>0]]):c("",!0),e(v).super_query&&e(i).super_query.enabled?(n(),s(bt,{key:5,ref_key:"superQueryButtonRef",ref:M,online:"",status:e(Y),queryBtnCfg:e(i).super_query,onSearch:e(ee)},null,8,["status","queryBtnCfg","onSearch"])):c("",!0)]),expandedRowRender:r(({record:t})=>[p.value[0]&&t.id==p.value[0]?(n(),s(h,{key:0,activeKey:d.tabIndex,"onUpdate:activeKey":l[0]||(l[0]=a=>d.tabIndex=a)},{default:r(()=>[p.value.length?(n(!0),f(F,{key:0},V(d.tabNav,(a,k)=>(n(),s(w,{tab:a.tableTxt,key:k+""},{default:r(()=>[d.tabIndex==k?(n(),s(Ct,{key:0,subTableId:a.id,mTableSelectedRcordId:p.value[0],subTableName:a.tableName},null,8,["subTableId","mTableSelectedRcordId","subTableName"])):c("",!0)]),_:2},1032,["tab"]))),128)):c("",!0)]),_:1},8,["activeKey"])):c("",!0)]),fileSlot:r(({text:t,record:a,column:k})=>[t?(n(),s(u,{key:1,ghost:!0,type:"primary",preIcon:"ant-design:download",size:"small",onClick:We=>e(Re)(t,a,k,e(y))},{default:r(()=>l[1]||(l[1]=[L(" 下载 ")])),_:2},1032,["onClick"])):(n(),f("span",xt,"无文件"))]),imgSlot:r(({text:t})=>[t?(n(),f("img",{key:1,src:e(_e)(t),alt:"图片不存在",class:"online-cell-image",onClick:a=>e(De)(t)},null,8,wt)):(n(),f("span",Bt,"无图片"))]),htmlSlot:r(({text:t,column:a,record:k})=>[a.fieldHref?(n(),f("a",{key:0,innerHTML:t,onClick:We=>e(He)(a.fieldHref,k)},null,8,St)):(n(),f("div",{key:1,innerHTML:t},null,8,Tt))]),pcaSlot:r(({text:t})=>[x("div",{title:e(Q)(t)},I(e(Q)(t)),9,Rt)]),dateSlot:r(({text:t,column:a})=>[x("span",null,I(e(Me)(t,a)),1)]),action:r(({record:t})=>[b(e(kt),{actions:e(ye)(t),dropDownActions:e(be)(t)},null,8,["actions","dropDownActions"])]),_:1},8,["loading","columns","dataSource","pagination","rowSelection","actionColumn","scroll","onTableRedo","class","onChange","expandedRowKeys"])),b(it,{onRegister:e(se),id:e(y),cgBIBtnMap:e(i),buttonSwitch:e(v),confirmBtnCfg:e(ie),onSuccess:Ue,onFormConfig:Je},null,8,["onRegister","id","cgBIBtnMap","buttonSwitch","confirmBtnCfg"]),b(U,{id:e(y),onRegister:e(ke)},null,8,["id","onRegister"]),b(st,{onRegister:e(me),url:e(le)(),onOk:e(g),online:""},null,8,["onRegister","url","onOk"]),b(je,et(e(R).model,tt(e(R).on)),{default:r(()=>[(n(),s(ot(e(R).is),nt(rt(e(R).params)),null,16))]),_:1},16),b(lt,{onRegister:e(oe),onSuccess:e(g)},null,8,["onRegister","onSuccess"]),b(U,{id:e(Ee),onRegister:e(Pe),defaultFullscreen:!1},null,8,["id","onRegister"]),b(gt,{ref_key:"onlinePopModalRef",ref:Fe,id:e(A),onRegister:e(qe),onSuccess:e(g),request:"",topTip:""},null,8,["id","onRegister","onSuccess"])])}}};export{cn as default};
