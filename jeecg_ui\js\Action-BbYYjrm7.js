import{d as _,f as C,u as k,ag as i,aB as v,ar as f,aD as s,at as a,k as e,G as p,aq as B,F as $,aC as g,au as S}from"./vue-vendor-dy9k-Yad.js";import{S as T}from"./index-LCGLvkB3.js";import{P as b}from"./index-CtJ0w2CP.js";import{a as w}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const y=_({components:{ScrollContainer:T,PageWrapper:b},setup(){const t=C(null),o=()=>{const l=k(t);if(!l)throw new Error("scroll is Null");return l};function m(l){o().scrollTo(l)}function c(){o().scrollBottom()}return{scrollTo:m,scrollRef:t,scrollBottom:c}}}),N={class:"my-4"},P={class:"scroll-wrap"},x={class:"p-3"};function V(t,o,m,c,l,W){const n=i("a-button"),u=i("ScrollContainer"),d=i("PageWrapper");return f(),v(d,{title:"滚动组件函数示例",content:"基于el-scrollbar"},{default:s(()=>[a("div",N,[e(n,{onClick:o[0]||(o[0]=r=>t.scrollTo(100)),class:"mr-2"},{default:s(()=>o[4]||(o[4]=[p(" 滚动到100px位置 ")])),_:1,__:[4]}),e(n,{onClick:o[1]||(o[1]=r=>t.scrollTo(800)),class:"mr-2"},{default:s(()=>o[5]||(o[5]=[p(" 滚动到800px位置 ")])),_:1,__:[5]}),e(n,{onClick:o[2]||(o[2]=r=>t.scrollTo(0)),class:"mr-2"},{default:s(()=>o[6]||(o[6]=[p(" 滚动到顶部 ")])),_:1,__:[6]}),e(n,{onClick:o[3]||(o[3]=r=>t.scrollBottom()),class:"mr-2"},{default:s(()=>o[7]||(o[7]=[p(" 滚动到底部 ")])),_:1,__:[7]})]),a("div",P,[e(u,{class:"mt-4",ref:"scrollRef"},{default:s(()=>[a("ul",x,[(f(),B($,null,g(100,r=>a("li",{key:r,class:"p-2",style:{border:"1px solid #eee"}},S(r),1)),64))])]),_:1},512)])]),_:1})}const Q=w(y,[["render",V],["__scopeId","data-v-672060ba"]]);export{Q as default};
