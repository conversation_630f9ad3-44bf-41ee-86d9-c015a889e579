var l=(t,a,r)=>new Promise((s,e)=>{var n=o=>{try{i(r.next(o))}catch(p){e(p)}},m=o=>{try{i(r.throw(o))}catch(p){e(p)}},i=o=>o.done?s(o.value):Promise.resolve(o.value).then(n,m);i((r=r.apply(t,a)).next())});import{B as u}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{d as f,f as g,ag as d,aB as B,ar as h,aD as M,k as _}from"./vue-vendor-dy9k-Yad.js";import{B as F}from"./BasicForm-DBcXiHk0.js";import{u as k}from"./useForm-CgkFTrrO.js";import{ac as v,a as w}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const C=f({name:"TenantInviteUserModal",components:{BasicModal:u,BasicForm:F},setup(t,{emit:a}){const r=g("邀请成员"),[s,{resetFields:e,validate:n}]=k({schemas:[{label:"手机号",field:"phone",component:"Input",dynamicRules:()=>[{required:!0,message:"请填写手机号"},{pattern:/^1[3456789]\d{9}$/,message:"手机号码格式有误"}]}],showActionButtonGroup:!1,labelCol:{span:24},wrapperCol:{span:24}}),[m,{setModalProps:i,closeModal:o}]=v(c=>l(null,null,function*(){yield e(),i({minHeight:100})}));function p(){return l(this,null,function*(){let c=yield n();a("inviteOk",c.phone),o()})}return{title:r,registerModal:m,registerForm:s,handleSubmit:p}}});function R(t,a,r,s,e,n){const m=d("BasicForm"),i=d("BasicModal");return h(),B(i,{onRegister:t.registerModal,width:500,title:t.title,onOk:t.handleSubmit},{default:M(()=>[_(m,{onRegister:t.registerForm},null,8,["onRegister"])]),_:1},8,["onRegister","title","onOk"])}const Io=w(C,[["render",R]]);export{Io as default};
