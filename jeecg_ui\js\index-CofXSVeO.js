import{d as p,ag as o,aB as _,ar as n,aD as t,k as e}from"./vue-vendor-dy9k-Yad.js";import l from"./Template1-B13eu5a4.js";import f from"./Template2-YHXl2Z5x.js";import s from"./Template3-wBrDMGPX.js";import c from"./Template4-Chn4uH1u.js";import d from"./Template5-DSkQ57Ge.js";import i from"./ErpTemplate-BaAmDzme.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const D=p({__name:"index",setup(u){return(b,k)=>{const a=o("a-tab-pane"),r=o("a-tabs"),m=o("a-card");return n(),_(m,{bordered:!1},{default:t(()=>[e(r,null,{default:t(()=>[e(a,{tab:"ERP布局模板",key:"erp"},{default:t(()=>[e(i)]),_:1}),e(a,{tab:"布局模板1",key:"1"},{default:t(()=>[e(l)]),_:1}),e(a,{tab:"布局模板2",key:"2"},{default:t(()=>[e(f)]),_:1}),e(a,{tab:"布局模板3",key:"3"},{default:t(()=>[e(s)]),_:1}),e(a,{tab:"布局模板4",key:"4"},{default:t(()=>[e(c)]),_:1}),e(a,{tab:"布局模板5",key:"5"},{default:t(()=>[e(d)]),_:1})]),_:1})]),_:1})}}});export{D as default};
