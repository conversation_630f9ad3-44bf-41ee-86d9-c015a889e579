import{d as c,ag as e,aB as p,ar as i,aD as a,k as o}from"./vue-vendor-dy9k-Yad.js";import{J as n}from"./antd-vue-vendor-me9YkNVC.js";import{C as l}from"./index-CWWAi5V9.js";import{P as m}from"./index-CtJ0w2CP.js";import{a as u}from"./index-CCWaWN5g.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const _=c({components:{Card:n,CardGrid:n.Grid,CountTo:l,PageWrapper:m}});function f(C,V,x,$,g,G){const t=e("CountTo"),r=e("CardGrid"),d=e("Card"),s=e("PageWrapper");return i(),p(s,{title:"数字动画示例"},{default:a(()=>[o(d,null,{default:a(()=>[o(r,{class:"count-to-demo-card"},{default:a(()=>[o(t,{prefix:"$",color:"#409EFF",startVal:1,endVal:2e5,duration:8e3})]),_:1}),o(r,{class:"count-to-demo-card"},{default:a(()=>[o(t,{suffix:"$",color:"red",startVal:1,endVal:3e5,decimals:2,duration:6e3})]),_:1}),o(r,{class:"count-to-demo-card"},{default:a(()=>[o(t,{suffix:"$",color:"rgb(0,238,0)",startVal:1,endVal:4e5,duration:7e3})]),_:1}),o(r,{class:"count-to-demo-card"},{default:a(()=>[o(t,{separator:"-",color:"rgba(138,43,226,.6)",startVal:1e4,endVal:5e5,duration:8e3})]),_:1})]),_:1})]),_:1})}const E=u(_,[["render",f],["__scopeId","data-v-e29e82d4"]]);export{E as default};
