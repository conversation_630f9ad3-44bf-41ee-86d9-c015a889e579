var H=(n,i,e)=>new Promise((u,o)=>{var f=t=>{try{a(e.next(t))}catch(g){o(g)}},m=t=>{try{a(e.throw(t))}catch(g){o(g)}},a=t=>t.done?u(t.value):Promise.resolve(t.value).then(f,m);a((e=e.apply(n,i)).next())});import{f as r,e as w,u as s}from"./vue-vendor-dy9k-Yad.js";import{createPageContext as d}from"./usePageContext-CxiNGbPs.js";import{useWindowSizeFn as p}from"./useWindowSizeFn-DDbrQbks.js";const c=r(0),h=r(0);function C(){function n(e){c.value=e}function i(e){h.value=e}return{headerHeightRef:c,footerHeightRef:h,setHeaderHeight:n,setFooterHeight:i}}function F(){const n=r(window.innerHeight),i=r(window.innerHeight),e=w(()=>s(n)-s(c)-s(h)||0);p(()=>{n.value=window.innerHeight},100,{immediate:!0});function u(o){return H(this,null,function*(){i.value=o})}d({contentHeight:e,setPageHeight:u,pageHeight:i})}export{F as a,C as u};
