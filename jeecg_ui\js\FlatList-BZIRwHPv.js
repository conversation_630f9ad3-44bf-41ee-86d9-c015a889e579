import{d as p,ag as o,aB as n,ar as s,aD as e,k as i,G as m}from"./vue-vendor-dy9k-Yad.js";import{P as c}from"./index-CtJ0w2CP.js";import{a as _}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const f=p({components:{PageWrapper:c}});function l(d,t,u,k,x,g){const r=o("router-link"),a=o("PageWrapper");return s(),n(a,{title:"平级面包屑示例",content:"子级页面面包屑会覆盖当前层级"},{default:e(()=>[i(r,{to:"/feat/breadcrumb/flatDetail"},{default:e(()=>t[0]||(t[0]=[m(" 进入平级详情页 ")])),_:1,__:[0]})]),_:1})}const w=_(f,[["render",l]]);export{w as default};
