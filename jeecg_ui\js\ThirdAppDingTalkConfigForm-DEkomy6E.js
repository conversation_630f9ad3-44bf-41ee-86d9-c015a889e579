var y=(i,t,l)=>new Promise((f,u)=>{var c=s=>{try{p(l.next(s))}catch(r){u(r)}},g=s=>{try{p(l.throw(s))}catch(r){u(r)}},p=s=>s.done?f(s.value):Promise.resolve(s.value).then(c,g);p((l=l.apply(i,t)).next())});import{d as A,f as D,o as M,l as C,ag as m,aq as S,ar as $,F as q,at as o,k as a,aD as d,G as b,au as U}from"./vue-vendor-dy9k-Yad.js";import{b as z,s as B}from"./ThirdApp.api-3uEF4_Sr.js";import"./index-Diw57m_E.js";import F from"./ThirdAppConfigModal-axPPClFk.js";import{M as h}from"./antd-vue-vendor-me9YkNVC.js";import{ad as L,bo as T,u as j,a as E}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const K=A({name:"OrganDingConfigForm",components:{ThirdAppConfigModal:F},setup(){const{createMessage:i}=j(),t=D(""),l=D(!1),f=D({agentId:void 0,clientId:"",clientSecret:""}),[u,{openModal:c}]=L();function g(){return y(this,null,function*(){let e=T();c(!0,{tenantId:e,thirdType:"dingtalk"})})}function p(e){return y(this,null,function*(){let n=yield z(e);n&&(f.value=n)})}function s(){let e=T();p({tenantId:e,thirdType:"dingtalk"})}function r(){return y(this,null,function*(){l.value=!0,yield B().then(e=>{let n={};e.result&&(n={width:600,title:e.message,content:()=>{let k,x=["成功信息如下：",v(C,e.result.successInfo.map((w,I)=>`${I+1}. ${w}`).join(`
`))];return e.success?k=[...x,C("br"),"无失败信息！"]:k=["失败信息如下：",v(C,e.result.failInfo.map((w,I)=>`${I+1}. ${w}`).join(`
`)),C("br"),...x],k}}),e.success?n!=null?h.success(n):i.warning(e.message):n&&n.title?h.warning(n):i.warning({content:e.message||"同步失败，请检查对接信息录入中是否填写正确，并确认是否已开启钉钉配置！",duration:5})}).finally(()=>{l.value=!1})})}function v(e,n){return e("div",{id:"box",style:{minHeight:"100px",border:"1px solid #d9d9d9",fontSize:"14px",maxHeight:"250px",whiteSpace:"pre",overflow:"auto",padding:"10px"}},n)}function _(){window.open("https://help.qiaoqiaoyun.com/expand/dingdingsyn.html","_target")}return M(()=>{let e=T();p({tenantId:e,thirdType:"dingtalk"})}),{appConfigData:f,collapseActiveKey:t,registerAppConfigModal:u,dingEditClick:g,handleSuccess:s,syncDingTalk:r,btnLoading:l,handleIconClick:_}}}),N={class:"base-collapse"},V={class:"sync-padding"},H={class:"flex-flow"},R={class:"base-message"},G={class:"flex-flow"},O={class:"base-message"},J={class:"flex-flow"},P={class:"base-message"},Q={class:"flex-flow"},W={class:"base-message"},X={style:{"margin-top":"20px",width:"100%","text-align":"right"}},Y={class:"sync-padding"},Z={style:{"margin-top":"20px"},class:"base-desc"},tt={style:{"list-style-type":"disc","margin-left":"20px"}},ot={style:{float:"right"}};function it(i,t,l,f,u,c){const g=m("a-collapse-panel"),p=m("a-collapse"),s=m("a-input-password"),r=m("a-button"),v=m("a-icon"),_=m("a-tooltip"),e=m("ThirdAppConfigModal");return $(),S(q,null,[o("div",N,[t[18]||(t[18]=o("div",{class:"header"}," 钉钉集成 ",-1)),a(p,{"expand-icon-position":"right",bordered:!1},{default:d(()=>[a(g,{key:"1"},{header:d(()=>t[4]||(t[4]=[o("div",{style:{"font-size":"16px"}}," 1.获取对接信息",-1)])),default:d(()=>[t[5]||(t[5]=o("div",{class:"base-desc"},"从钉钉开放平台获取对接信息，即可开始集成以及同步通讯录",-1)),t[6]||(t[6]=o("div",{style:{"margin-top":"5px"}},[o("a",{href:"https://help.qiaoqiaoyun.com/expand/dingding.html",target:"_blank"},"如何获取对接信息?")],-1))]),_:1,__:[5,6]})]),_:1}),o("div",V,[a(p,{"expand-icon-position":"right",bordered:!1},{default:d(()=>[a(g,{key:"2"},{header:d(()=>t[7]||(t[7]=[o("div",{style:{width:"100%","justify-content":"space-between",display:"flex"}},[o("div",{style:{"font-size":"16px"}}," 2.对接信息录入")],-1)])),default:d(()=>[t[13]||(t[13]=o("div",{class:"base-desc"},"完成步骤1后，填入Agentld、 AppKey、AppSecret后 可对接应用与同步通讯录",-1)),o("div",H,[t[8]||(t[8]=o("div",{class:"base-title"},"CorpId",-1)),o("div",R,[a(s,{value:i.appConfigData.corpId,"onUpdate:value":t[0]||(t[0]=n=>i.appConfigData.corpId=n),readonly:""},null,8,["value"])])]),o("div",G,[t[9]||(t[9]=o("div",{class:"base-title"},"Agentld",-1)),o("div",O,[a(s,{value:i.appConfigData.agentId,"onUpdate:value":t[1]||(t[1]=n=>i.appConfigData.agentId=n),readonly:""},null,8,["value"])])]),o("div",J,[t[10]||(t[10]=o("div",{class:"base-title"},"AppKey",-1)),o("div",P,[a(s,{value:i.appConfigData.clientId,"onUpdate:value":t[2]||(t[2]=n=>i.appConfigData.clientId=n),readonly:""},null,8,["value"])])]),o("div",Q,[t[11]||(t[11]=o("div",{class:"base-title"},"AppSecret",-1)),o("div",W,[a(s,{value:i.appConfigData.clientSecret,"onUpdate:value":t[3]||(t[3]=n=>i.appConfigData.clientSecret=n),readonly:""},null,8,["value"])])]),o("div",X,[a(r,{onClick:i.dingEditClick},{default:d(()=>t[12]||(t[12]=[b("编辑")])),_:1,__:[12]},8,["onClick"])])]),_:1,__:[13]})]),_:1}),o("div",Y,[t[17]||(t[17]=o("div",{style:{"font-size":"16px",width:"100%"}}," 3.数据同步",-1)),o("div",Z,[t[16]||(t[16]=b(" 从钉钉同步到本地 ")),o("ul",tt,[t[15]||(t[15]=o("li",null,"同步部门到本地",-1)),o("li",null,[t[14]||(t[14]=b(" 同步部门下的用户到本地 ")),a(_,{title:"同步用户与部门文档"},{default:d(()=>[a(v,{onClick:i.handleIconClick,type:"question-circle",class:"sync-text"},null,8,["onClick"])]),_:1})])]),o("div",ot,[a(r,{loading:i.btnLoading,onClick:i.syncDingTalk},{default:d(()=>[b(U(i.btnLoading?"同步中":"同步"),1)]),_:1},8,["loading","onClick"])])])])])]),a(e,{onRegister:i.registerAppConfigModal,onSuccess:i.handleSuccess},null,8,["onRegister","onSuccess"])],64)}const ao=E(K,[["render",it],["__scopeId","data-v-819eb780"]]);export{ao as default};
