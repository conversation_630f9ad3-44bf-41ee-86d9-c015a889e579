import{d as te,f as h,c as E,b as ae,ag as x,aB as w,ar as s,u as n,aD as f,ah as M,k as u,aq as l,F as O,at as t,as as z,au as g,G as ie,aC as se}from"./vue-vendor-dy9k-Yad.js";import{p as ne}from"./antd-vue-vendor-me9YkNVC.js";import{u as re,bJ as oe}from"./index-CCWaWN5g.js";import{u as le,B as ue}from"./index-JbqXEynz.js";import{y as pe}from"./DebugRunForm.vue_vue_type_script_setup_true_lang-d0e628c3-BP0AapVC.js";import{u as ce}from"./api-0389a176-CuvsoQ7H.js";import{x as me}from"./runStore-e1feeee0-BrPjIiN4.js";import{d as de}from"./NodeIcon.vue_vue_type_script_setup_true_lang-0fd734b2-CEYYkV5U.js";import{s as fe}from"./_plugin-vue_export-helper-dad06003-lGy7RumW.js";import"./index-L3cSIXth.js";import"./_commonjsHelpers-ce4d82cc-RqGMvybJ.js";import"./index-9c51646a-BviQbLw-.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";var ye=Object.defineProperty,V=Object.getOwnPropertySymbols,ve=Object.prototype.hasOwnProperty,ge=Object.prototype.propertyIsEnumerable,A=(b,p,r)=>p in b?ye(b,p,{enumerable:!0,configurable:!0,writable:!0,value:r}):b[p]=r,be=(b,p)=>{for(var r in p||(p={}))ve.call(p,r)&&A(b,r,p[r]);if(V)for(var r of V(p))ge.call(p,r)&&A(b,r,p[r]);return b},j=(b,p,r)=>new Promise((F,k)=>{var S=c=>{try{y(r.next(c))}catch(P){k(P)}},a=c=>{try{y(r.throw(c))}catch(P){k(P)}},y=c=>c.done?F(c.value):Promise.resolve(c.value).then(S,a);y((r=r.apply(b,p)).next())});const ke={style:{"margin-top":"12px"}},he={class:"params-bar"},xe={class:"params-item output"},we={class:"content"},Pe={key:0},Fe={key:1},Se={key:2},Ce={key:0,style:{"margin-top":"8px"}},Re={class:"logs-bar"},Ne={class:"bar-item status"},_e={class:"item-content"},De={key:3},Ie={class:"bar-item"},ze={class:"item-content"},Be={key:0},Me={key:1},Oe={class:"params-bar"},je={class:"params-item input"},Te={class:"content"},qe={class:"params-item output"},Ee={class:"content"},Ve={key:0},Ae={key:1},Je={key:2},Ke={key:0,class:"node-bar"},$e=["onClick"],Ge={class:"node-header"},Le={class:"icon"},Ue={class:"airag-node-label"},He={class:"time"},Qe={key:0},We={key:1},Xe={class:"params-bar"},Ye={class:"params-item input"},Ze={class:"content"},et={class:"params-item output"},tt={class:"content"},at={key:0},it={key:1},st=te({__name:"DebugRunDrawer",props:{},emits:["register"],setup(b,{emit:p}){const{createMessage:r}=re(),F=h(),k=E("lfRef",h()),S=E("doSubmit"),a=me(),y=h(!1),c=h(""),P=h({}),C=h("input"),J=i=>{if(y.value){r.warn("正在调试请稍后……");return}if(!a.isRunning&&!a.isFinished){r.warn("请先调试流程");return}C.value=i},R=h([]),[K,{getVisible:$,closeDrawer:G}]=le(i=>j(this,null,function*(){var e;if(c.value="",!k.value){c.value="尚未初始化";return}const m=k.value.getNodeDataById("start-node");if(!m){c.value="未找到开始节点";return}const o=(e=m.properties)==null?void 0:e.inputParams;Array.isArray(o)&&o.length>0?(P.value={x:1},R.value=o.flatMap(d=>d.field==="history"?[]:d.type==="string"||d.type==="text"?[L(d)]:d.type==="number"?[U(d)]:d.type==="picture"?[H(d)]:[T(d,"unknown")])):(P.value={},R.value=[])}));function L(i){return B(i)}function U(i){return B(i,{component:"InputNumber",componentProps:{style:{width:"180px"}}})}function H(i){return T(i,"picture")}function T(i,e){return B(i,{slot:e,required:!1})}function B(i,e={}){return be({field:i.field,label:i.name,component:"Input",required:i.required},e)}function Q(){return j(this,null,function*(){let i={};if(F.value)try{i=yield F.value.validate()}catch(e){return}try{if(y.value=!0,a.start(i),q(!0),S)try{yield S({silent:!0,needName:!1,saveFn(e){e=ne(e,["design","chain"]),W(e,i)},onError:()=>{N(),y.value=!1}})}catch(e){N(),y.value=!1,r.error("保存失败，请稍后重试")}finally{y.value=!1}else r.error("当前环境无法调试")}catch(e){}})}function W(i,e){return j(this,null,function*(){if(!k.value){c.value="尚未初始化";return}try{C.value="trace",y.value=!0;const m=ce(i,e);m.onFlowStarted(o=>{a.beginTime=Date.now()}),m.onFlowFinished(o=>{a.finish(o.success,o.message,o.outputs)}),m.onNodeStarted(o=>{a.addStep({node:{id:o.id,type:o.type,text:o.text},status:"running",inputParams:o.inputs,outputParams:o.outputs})}),m.onNodeFinished(o=>{const d=o.success?"success":"fail";a.updateStepStatus(o.id,d,o.outputs)}),yield m.run()}catch(m){}finally{y.value=!1}})}function X(){return!a.isRunning}function Y(){if(a.isRunning){r.warn("正在调试请稍后……");return}N(),G()}ae(()=>{N()});function N(){C.value="input",a.end(),q(!1)}function q(i){k.value&&k.value.graphModel.$J.updateEditConfig({isSilentMode:i})}function Z(){const i=typeof a.outputParams=="string"?a.outputParams:JSON.stringify(a.outputParams);oe(i)?r.success("复制成功"):prompt("复制失败，请手动复制",i)}return(i,e)=>{const m=x("a-alert"),o=x("a-button"),d=x("a-spin"),_=x("a-tab-pane"),D=x("Icon"),I=x("a-space"),ee=x("a-tabs");return s(),w(n(ue),{onRegister:n(K),width:600,title:"调试",mask:!1,maskStyle:{"background-color":"#00000015",cursor:"zoom-out"},getContainer:!1,closeFunc:X,onClose:Y},{default:f(()=>[n($)?(s(),w(ee,{key:0,activeKey:C.value,animated:"",onChange:J},{default:f(()=>[u(_,{tab:"输入",key:"input"},{default:f(()=>[u(d,{spinning:y.value},{default:f(()=>[c.value?(s(),w(m,{key:0,message:"错误",description:c.value,type:"error","show-icon":""},null,8,["description"])):(s(),l(O,{key:1},[R.value.length?(s(),l(O,{key:0},[u(m,{type:"info",message:"请填写开始节点中配置的参数","show-icon":""}),u(pe,{ref_key:"formRef",ref:F,schemas:R.value},null,8,["schemas"])],64)):(s(),w(m,{key:1,type:"info",message:"当前流程没有配置参数，可直接点击调试","show-icon":""})),t("div",ke,[u(o,{block:"",size:"large",type:"primary",preIcon:"codicon:debug-start",onClick:Q},{default:f(()=>e[0]||(e[0]=[t("span",null,"开始调试",-1)])),_:1})])],64))]),_:1},8,["spinning"])]),_:1}),u(_,{tab:"结果",key:"result"},{default:f(()=>[t("div",{class:z(["logs-box",n(a).status])},[t("div",he,[t("div",xe,[t("div",we,[n(a).isRunning?(s(),l("span",Pe,"-")):n(a).isFailed?(s(),l("span",Fe,g(n(a).resMessage),1)):(s(),l("pre",Se,g(n(a).outputParams),1))]),n(a).isFailed?M("",!0):(s(),l("div",Ce,[u(o,{preIcon:"codicon:copy",size:"small",ghost:"",onClick:Z,type:"primary"},{default:f(()=>e[1]||(e[1]=[ie("复制")])),_:1})]))])])],2)]),_:1}),u(_,{tab:"详情",key:"info"},{default:f(()=>[t("div",{class:z(["logs-box",n(a).status])},[t("div",Re,[t("div",Ne,[e[5]||(e[5]=t("div",{class:"item-title"},"状态",-1)),t("div",_e,[n(a).isRunning?(s(),w(I,{key:0},{default:f(()=>[u(D,{icon:"eos-icons:bubble-loading",size:14}),e[2]||(e[2]=t("span",null,"调试中",-1))]),_:1})):n(a).isFailed?(s(),w(I,{key:1},{default:f(()=>[u(D,{icon:"ix:namur-failure-filled",size:14}),e[3]||(e[3]=t("span",null,"调试失败",-1))]),_:1})):n(a).isFinished?(s(),w(I,{key:2},{default:f(()=>[u(D,{icon:"ix:success",size:14}),e[4]||(e[4]=t("span",null,"调试成功",-1))]),_:1})):(s(),l("span",De,g(n(a).status),1))])]),t("div",Ie,[e[6]||(e[6]=t("div",{class:"item-title"},"调试时间",-1)),t("div",ze,[n(a).isFinished||n(a).isFailed?(s(),l("span",Be,g(n(a).timeText),1)):(s(),l("span",Me,"-"))])])]),e[9]||(e[9]=t("div",{class:"divider-text"},"参数",-1)),t("div",Oe,[t("div",je,[e[7]||(e[7]=t("div",{class:"title"},"输入",-1)),t("div",Te,[t("pre",null,g(n(a).inputParams),1)])]),t("div",qe,[e[8]||(e[8]=t("div",{class:"title"},"输出",-1)),t("div",Ee,[n(a).isRunning?(s(),l("span",Ve,"-")):n(a).isFailed?(s(),l("span",Ae,g(n(a).resMessage),1)):(s(),l("pre",Je,g(n(a).outputParams),1))])])])],2)]),_:1}),u(_,{tab:"追踪",key:"trace"},{default:f(()=>[t("div",{class:z(["logs-box",n(a).status])},[n(a).nodeSteps.length?(s(),l("div",Ke,[(s(!0),l(O,null,se(n(a).nodeSteps,v=>(s(),l("div",{class:z(["node-item",v.status,{expansion:v.expansion}]),onClick:nt=>v.expansion=!v.expansion},[t("div",Ge,[u(I,{class:"info"},{default:f(()=>[t("div",Le,[u(de,{type:v.node.type},null,8,["type"])]),t("span",Ue,g(v.node.text),1)]),_:2},1024),t("div",He,[v.status==="running"?(s(),l("span",Qe,[u(D,{icon:"eos-icons:bubble-loading",size:14})])):(s(),l("span",We,"耗时："+g(v.timeText),1))])]),t("div",Xe,[t("div",Ye,[e[10]||(e[10]=t("div",{class:"title"},"输入",-1)),t("div",Ze,[t("pre",null,g(v.inputParams),1)])]),t("div",et,[e[11]||(e[11]=t("div",{class:"title"},"输出",-1)),t("div",tt,[v.status==="running"?(s(),l("span",at,"-")):(s(),l("pre",it,g(v.outputParams),1))])])])],10,$e))),256))])):M("",!0)],2)]),_:1})]),_:1},8,["activeKey"])):M("",!0)]),_:1},8,["onRegister"])}}}),fa=fe(st,[["__scopeId","data-v-94c68cdd"]]);export{fa as default};
