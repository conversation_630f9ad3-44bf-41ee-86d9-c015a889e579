var z=Object.defineProperty,C=Object.defineProperties;var k=Object.getOwnPropertyDescriptors;var l=Object.getOwnPropertySymbols;var y=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var n=(o,t,e)=>t in o?z(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,s=(o,t)=>{for(var e in t||(t={}))y.call(t,e)&&n(o,e,t[e]);if(l)for(var e of l(t))D.call(t,e)&&n(o,e,t[e]);return o},d=(o,t)=>C(o,k(t));import{d as u,ag as F,aq as P,ar as S,k as a,aD as c,u as m}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as T}from"./useListPage-Soxgnx9a.js";import{j as J}from"./index-CCWaWN5g.js";import{Q as R}from"./componentMap-Bkie1n3v.js";import V from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const j={class:"p-4"},A=u({name:"basic-table-demo"}),qt=u(d(s({},A),{setup(o){const t=[{title:"姓名",dataIndex:"name",width:170,align:"left",resizable:!0,sorter:{multiple:1}},{title:"关键词",dataIndex:"keyWord",width:130,resizable:!0},{title:"打卡时间",dataIndex:"punchTime",width:140,resizable:!0},{title:"工资",dataIndex:"salaryMoney",width:140,resizable:!0,sorter:{multiple:2}},{title:"奖金",dataIndex:"bonusMoney",width:140,resizable:!0},{title:"性别",dataIndex:"sex",sorter:{multiple:3},filters:[{text:"男",value:"1"},{text:"女",value:"2"}],customRender:({record:i})=>i.sex?i.sex=="1"?"男":"女":"",width:120,resizable:!0},{title:"生日",dataIndex:"birthday",width:120,resizable:!0},{title:"邮箱",dataIndex:"email",width:120,resizable:!0}],e=[{label:"姓名",field:"name",component:"JInput",defaultValue:"苏榕润"},{label:"性别",field:"sex",component:"JDictSelectTag",componentProps:{dictCode:"sex",placeholder:"请选择性别"}},{label:"邮箱",field:"email",component:"JInput",slot:"email"},{label:"生日",field:"birthday",component:"DatePicker"}],b=i=>J.get({url:"/test/jeecgDemo/list",params:i}),{tableContext:f}=T({designScope:"basic-table-demo-filter",tableProps:{title:"用户列表",api:b,columns:t,formConfig:{schemas:e},useSearchForm:!0}}),[h,{getForm:x}]=f;function _(i){return[{label:"编辑",onClick:g.bind(null,i)}]}function g(i){let{getFieldsValue:I}=x()}return(i,I)=>{const w=F("a-input");return S(),P("div",j,[a(m(V),{onRegister:m(h)},{"form-email":c(({model:r,field:p})=>[a(w,{placeholder:"请输入邮箱",value:r[p],"onUpdate:value":v=>r[p]=v,"addon-before":"邮箱:","addon-after":".com"},null,8,["value","onUpdate:value"])]),action:c(({record:r})=>[a(m(R),{actions:_(r)},null,8,["actions"])]),_:1},8,["onRegister"])])}}}));export{qt as default};
