import{a as h,j as u,bx as o}from"./index-CCWaWN5g.js";import{ag as a,aB as g,ar as k,aD as l,k as i}from"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const _={name:"Template5",data(){return{loading:!1,pagination:{current:1,pageSize:50,pageSizeOptions:["50"],total:0},selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:o.input},{key:"call",title:"呼叫",width:"80px",type:o.input},{key:"len",title:"长",width:"80px",type:o.input},{key:"ton",title:"吨",width:"120px",type:o.input},{key:"payer",title:"付款方",width:"120px",type:o.input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:o.input},{key:"trend",title:"动向",width:"120px",type:o.input}],treeData:[{title:"1-10页",key:"1-10",children:[{title:"第 1 页",key:1,slots:{icon:"myIcon"}},{title:"第 2 页",key:2,slots:{icon:"myIcon"}},{title:"第 3 页",key:3,slots:{icon:"myIcon"},children:[{title:"第 333 页",key:333,slots:{icon:"myIcon"}},{title:"第 444 页",key:444,slots:{icon:"myIcon"}},{title:"第 555 页",key:555,slots:{icon:"myIcon"}}]},{title:"第 4 页",key:4,slots:{icon:"myIcon"}},{title:"第 5 页",key:5,slots:{icon:"myIcon"}},{title:"第 6 页",key:6,slots:{icon:"myIcon"}},{title:"第 7 页",key:7,slots:{icon:"myIcon"}},{title:"第 8 页",key:8,slots:{icon:"myIcon"}},{title:"第 9 页",key:9,slots:{icon:"myIcon"}},{title:"第 10 页",key:10,slots:{icon:"myIcon"}}],slots:{icon:"myIcon"}},{title:"11-20页",key:"11-20",children:[{title:"第 11 页",key:11,slots:{icon:"myIcon"}},{title:"第 12 页",key:12,slots:{icon:"myIcon"}},{title:"第 13 页",key:13,slots:{icon:"myIcon"}},{title:"第 14 页",key:14,slots:{icon:"myIcon"}},{title:"第 15 页",key:15,slots:{icon:"myIcon"}},{title:"第 16 页",key:16,slots:{icon:"myIcon"}},{title:"第 17 页",key:17,slots:{icon:"myIcon"}},{title:"第 18 页",key:18,slots:{icon:"myIcon"}},{title:"第 19 页",key:19,slots:{icon:"myIcon"}},{title:"第 20 页",key:20,slots:{icon:"myIcon"}}],slots:{icon:"myIcon"}}],treeExpandedKeys:["1-10"],url:{getData:"/mock/vxe/getData"}}},created(){this.loadData()},methods:{loadData(){let e={pageNo:this.pagination.current,pageSize:this.pagination.pageSize};this.loading=!0,u.get({url:this.url.getData,params:e}).then(n=>{this.pagination.total=n.total,this.dataSource=n.records,this.selectedRows=[]}).finally(()=>{this.loading=!1})},handleTablePageChange(e){this.pagination.current=e.current,this.pagination.pageSize=e.pageSize,this.loadData(),e.current<=10?this.treeExpandedKeys=["1-10"]:this.treeExpandedKeys=["11-20"]},handleTreeSelect(e){let n=e[0];typeof n=="string"?this.treeExpandedKeys=e:(this.pagination.current=n,this.loadData())},handleTreeExpand(e){this.treeExpandedKeys=e}}};function x(e,n,I,f,t,s){const r=a("a-icon"),y=a("a-tree"),c=a("a-col"),p=a("JVxeTable"),d=a("a-row"),m=a("a-card");return k(),g(m,{bordered:!1},{default:l(()=>[i(d,{gutter:8},{default:l(()=>[i(c,{span:6},{default:l(()=>[i(y,{class:"template-5-tree","tree-data":t.treeData,"show-icon":"","show-line":"",expandedKeys:t.treeExpandedKeys,selectedKeys:[t.pagination.current],onExpand:s.handleTreeExpand,onSelect:s.handleTreeSelect},{default:l(()=>[i(r,{slot:"myIcon",type:"unordered-list",style:{color:"#0c8fcf"}})]),_:1},8,["tree-data","expandedKeys","selectedKeys","onExpand","onSelect"])]),_:1}),i(c,{span:18},{default:l(()=>[i(p,{rowNumber:"",rowSelection:"",height:750,loading:t.loading,columns:t.columns,dataSource:t.dataSource,pagination:t.pagination,onPageChange:s.handleTablePageChange},null,8,["loading","columns","dataSource","pagination","onPageChange"])]),_:1})]),_:1})]),_:1})}const E=h(_,[["render",x]]);export{E as default};
