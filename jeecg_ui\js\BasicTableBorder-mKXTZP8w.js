var g=Object.defineProperty,x=Object.defineProperties;var y=Object.getOwnPropertyDescriptors;var m=Object.getOwnPropertySymbols;var h=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var p=(o,t,e)=>t in o?g(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,a=(o,t)=>{for(var e in t||(t={}))h.call(t,e)&&p(o,e,t[e]);if(m)for(var e of m(t))C.call(t,e)&&p(o,e,t[e]);return o},s=(o,t)=>x(o,y(t));import{d as u,aq as d,ar as n,k as I,aD as l,G as w,ah as S,au as N,u as c}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as T}from"./useListPage-Soxgnx9a.js";import V from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const A={class:"p-4"},B={key:0},D=u({name:"basic-table-demo"}),Gt=u(s(a({},D),{setup(o){const t=[{title:"姓名",dataIndex:"name",key:"name",width:300},{title:"年龄",dataIndex:"age",key:"age",width:300},{title:"住址",dataIndex:"address",key:"address",ellipsis:!0},{title:"长内容列",dataIndex:"address",key:"address 2",ellipsis:!0},{title:"长内容列",dataIndex:"address",key:"address 3",ellipsis:!0}],{tableContext:e}=T({designScope:"basic-table-demo",tableProps:{title:"边框表格",dataSource:[{key:"1",name:"张三",age:32,address:"中国北京北京市朝阳区大屯路科学院南里1号楼3单元401"},{key:"2",name:"刘思",age:32,address:"中国北京北京市昌平区顺沙路尚湖世家2号楼7单元503"}],columns:t,showActionColumn:!1,useSearchForm:!1}}),[f]=e;function E(r){return[{label:"编辑",onClick:_.bind(null,r)}]}function _(r){}return(r,i)=>(n(),d("div",A,[I(c(V),{onRegister:c(f)},{bodyCell:l(({column:k,text:b})=>[k.dataIndex==="name"?(n(),d("a",B,N(b),1)):S("",!0)]),footer:l(()=>i[0]||(i[0]=[w("页脚")])),_:1},8,["onRegister"])]))}}));export{Gt as default};
