import{k as o}from"./vue-vendor-dy9k-Yad.js";import{X as l}from"./antd-vue-vendor-me9YkNVC.js";import{N as n,bK as r}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const{t}=n();function d(){return[{dataIndex:"type",title:t("sys.errorLog.tableColumnType"),width:80,customRender:({text:e})=>{const a=e===r.VUE?"green":e===r.RESOURCE?"cyan":e===r.PROMISE?"blue":r.AJAX?"red":"purple";return o(l,{color:a},{default:()=>e})}},{dataIndex:"url",title:"URL",width:200},{dataIndex:"time",title:t("sys.errorLog.tableColumnDate"),width:160},{dataIndex:"file",title:t("sys.errorLog.tableColumnFile"),width:200},{dataIndex:"name",title:"Name",width:200},{dataIndex:"message",title:t("sys.errorLog.tableColumnMsg"),width:300},{dataIndex:"stack",title:t("sys.errorLog.tableColumnStackMsg")}]}function c(){return d().map(e=>({field:e.dataIndex,label:e.title}))}export{d as getColumns,c as getDescSchema};
