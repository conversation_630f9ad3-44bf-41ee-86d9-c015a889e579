var D=Object.defineProperty,K=Object.defineProperties;var U=Object.getOwnPropertyDescriptors;var C=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,j=Object.prototype.propertyIsEnumerable;var I=(r,e,o)=>e in r?D(r,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[e]=o,c=(r,e)=>{for(var o in e||(e={}))V.call(e,o)&&I(r,o,e[o]);if(C)for(var o of C(e))j.call(e,o)&&I(r,o,e[o]);return r},P=(r,e)=>K(r,U(e));var M=(r,e,o)=>new Promise((m,p)=>{var y=l=>{try{u(o.next(l))}catch(d){p(d)}},b=l=>{try{u(o.throw(l))}catch(d){p(d)}},u=l=>l.done?m(l.value):Promise.resolve(l.value).then(y,b);u((o=o.apply(r,e)).next())});import{d as k,f as g,ag as F,aq as H,ar as G,k as s,aD as i,G as x,au as _,u as h}from"./vue-vendor-dy9k-Yad.js";import{u as W}from"./index-BkGZ5fiW.js";import{j as X,aX as J,a as L}from"./index-CCWaWN5g.js";import{a as Y}from"./index-CyU3vcHV.js";import{Q as Z}from"./componentMap-Bkie1n3v.js";import $ from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFilePreview-CazplhRu.js";import"./SupplyDemand-DK00S9Ao.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const tt=r=>{const e=c({status:2,pageNo:1,pageSize:10},r);return X.post({url:"/hgy/entrustService/hgyEntrustOrder/workbench/tobedoneQueryPage",data:e})},et={class:"p-4"},ot=k({name:"Backlog"}),at=k(P(c({},ot),{setup(r){const e=[{title:"序号",dataIndex:"index",width:60,customRender:({index:t})=>t+1},{title:"委托单号",dataIndex:"id",width:160},{title:"项目名称",dataIndex:"projectName",width:200,ellipsis:!0},{title:"委托类型",dataIndex:"entrustType",width:100,slots:{customRender:"entrustType"}},{title:"服务类型",dataIndex:"serviceType",width:120,slots:{customRender:"serviceType"}},{title:"审核状态",dataIndex:"status",width:100,slots:{customRender:"status"}},{title:"联系人",dataIndex:"relationUser",width:100},{title:"联系电话",dataIndex:"relationPhone",width:120},{title:"申请人",dataIndex:"applicantUser",width:100},{title:"提交时间",dataIndex:"submitTime",width:200,customRender:({text:t})=>t?J(t):"-"}],o=[{key:"all",label:"全部待办",icon:""},{key:"appreciation",label:"增值委托",icon:""},{key:"autonomous",label:"自主委托",icon:""},{key:"supply",label:"供求信息",icon:""}],m=g("all"),p=g({});function y(t){m.value=t;let a={};switch(t){case"appreciation":a={entrustType:1};break;case"autonomous":a={entrustType:2};break;case"supply":a={entrustType:3};break;default:a={}}p.value=a,l()}function b(t){return M(this,null,function*(){const a=c(c({},t),p.value);return tt(a)})}const[u,{reload:l,getForm:d}]=W({api:b,columns:e,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,showButtonTags:!0,buttonTagItems:o,activeButtonTagKey:m.value,inset:!0,maxHeight:490,actionColumn:{width:200,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"},formConfig:{labelWidth:64,size:"large",labelAlign:"left",actionColOptions:{span:6,style:{textAlign:"right"}},schemas:[{field:"entrustOrderId",label:"委托单号",component:"Input",componentProps:{placeholder:"请输入委托单号"},colProps:{span:6}},{field:"entrustType",label:"委托类型",component:"Select",componentProps:{placeholder:"请选择委托类型",options:[{label:"增值",value:1},{label:"自主",value:2},{label:"供应",value:3}]},colProps:{span:6}},{field:"serviceType",label:"服务类型",component:"Select",componentProps:{placeholder:"请选择服务类型",options:[{label:"竞价委托",value:1},{label:"资产处置",value:2},{label:"采购信息",value:3},{label:"供应",value:4},{label:"求购",value:5}]},colProps:{span:6}}]}});function S(t){return[{label:"审核",onClick:R.bind(null,t),ifShow:()=>A(t)}]}function A(t){return t.status===2}const f=g(!1),v=g(null);function R(t){v.value=t,f.value=!0}function w(){f.value=!1,v.value=null}function B(){w(),l()}function E(t){return{1:"增值",2:"自主",3:"供求"}[t]||"未知"}function N(t){return{1:"blue",2:"green",3:"orange"}[t]||"default"}function z(t){return{1:"竞价委托",2:"资产处置",3:"采购信息",4:"供应",5:"求购"}[t]||"未知"}function O(t){return{1:"purple",2:"cyan",3:"geekblue",4:"lime",5:"magenta"}[t]||"default"}function Q(t){return{1:"草稿",2:"待审核",3:"已通过",4:"已拒绝"}[t]||"未知"}function q(t){return{2:"processing",3:"success",4:"error"}[t]||"default"}return(t,a)=>{const T=F("a-tag");return G(),H("div",et,[s(h($),{onRegister:h(u),onButtonTagChange:y},{action:i(({record:n})=>[s(h(Z),{actions:S(n),size:"small"},null,8,["actions"])]),entrustType:i(({text:n})=>[s(T,{color:N(n)},{default:i(()=>[x(_(E(n)),1)]),_:2},1032,["color"])]),serviceType:i(({text:n})=>[s(T,{color:O(n)},{default:i(()=>[x(_(z(n)),1)]),_:2},1032,["color"])]),status:i(({text:n})=>[s(T,{color:q(n)},{default:i(()=>[x(_(Q(n)),1)]),_:2},1032,["color"])]),_:1},8,["onRegister"]),s(h(Y),{open:f.value,"onUpdate:open":a[0]||(a[0]=n=>f.value=n),record:v.value,onClose:w,onSuccess:B},null,8,["open","record"])])}}})),ce=L(at,[["__scopeId","data-v-4ae6cf7b"]]);export{ce as default};
