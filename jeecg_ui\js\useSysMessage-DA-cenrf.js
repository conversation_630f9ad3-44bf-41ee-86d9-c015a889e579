var p=(c,b,r)=>new Promise((o,g)=>{var y=t=>{try{n(r.next(t))}catch(f){g(f)}},d=t=>{try{n(r.throw(t))}catch(f){g(f)}},n=t=>t.done?o(t.value):Promise.resolve(t.value).then(y,d);n((r=r.apply(c,b)).next())});import{f as T,r as M,ap as A,aN as I}from"./vue-vendor-dy9k-Yad.js";import{j as w,u as P,b as S,B as C,bt as F}from"./index-CCWaWN5g.js";const H=c=>w.get({url:"/sys/annountCement/vue3List",params:c});function O(){const{createMessage:c}=P(),b=S("rangeDate"),r=T([]),o=T(1);let g=10;const y=M({fromUser:"",rangeDateKey:"",rangeDate:[],starFlag:""});function d(){let{fromUser:e,rangeDateKey:a,rangeDate:s,starFlag:l}=y,i={fromUser:e,starFlag:l,rangeDateKey:a,beginDate:"",endDate:"",pageNo:o.value,pageSize:g};return a=="zdy"&&(i.beginDate=s[0]+" 00:00:00",i.endDate=s[1]+" 23:59:59"),i}const n=T(!1);function t(){return p(this,null,function*(){if(n.value===!0)return;let e=d();const a=yield H(e);if(!a||a.length<=0){n.value=!0;return}a.length<g&&(n.value=!0),o.value=o.value+1;let s=r.value;s.push(...a),r.value=s})}function f(){r.value=[],o.value=1,n.value=!1}function h(e){return p(this,null,function*(){const a="/sys/sysAnnouncementSend/edit";let s="1";e.starFlag==s&&(s="0");const l={starFlag:s,id:e.sendId},i=yield w.put({url:a,params:l},{isTransformResponse:!1});i.success===!0||c.warning(i.message)})}const u=T(!1);function m(){return p(this,null,function*(){u.value=!0,yield t(),u.value=!1})}function v(e){return e.readFlag!=="1"}function D(e){return e.busType=="email"?"邮件提醒:":e.busType=="bpm"?"流程催办:":e.busType=="bpm_cc"?"流程抄送:":e.busType=="bpm_task"?"流程任务:":e.msgCategory=="2"?"系统消息:":e.msgCategory=="1"?"通知公告:":""}return{messageList:r,reset:f,loadData:t,loadEndStatus:n,searchParams:y,updateStarMessage:h,onLoadMore:m,noRead:v,getMsgCategory:D}}function _(c,b){const r=S("messageHref"),o=A(),g=C(),y=I(),{close:d,closeSameRoute:n}=F();function t(u,m){return p(this,null,function*(){!u.busType||u.busType=="msg_node"?m?m():yield h(u):yield f(u)})}function f(u){return p(this,null,function*(){const{busType:m,busId:v,msgAbstract:D}=u;let e=r.filter(l=>l.value===m);if(!e||e.length==0)return;let a=e[0].text;a=a.replace("{DETAIL_ID}",v);let s={detailId:v};if(D)try{let l=JSON.parse(D);Object.keys(l).map(i=>{s[i]=l[i]})}catch(l){}g.setMessageHrefParams(s),y.path.indexOf(a)>=0?(yield d(),yield o.replace({path:a,query:{time:new Date().getTime()}})):(n(a),yield o.push({path:a}))})}function h(u){return p(this,null,function*(){c("detail",u)})}return{goPage:t}}export{O as a,_ as u};
