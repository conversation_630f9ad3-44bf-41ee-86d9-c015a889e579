var Y=Object.defineProperty,Z=Object.defineProperties;var tt=Object.getOwnPropertyDescriptors;var T=Object.getOwnPropertySymbols;var et=Object.prototype.hasOwnProperty,ot=Object.prototype.propertyIsEnumerable;var P=(i,o,e)=>o in i?Y(i,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[o]=e,A=(i,o)=>{for(var e in o||(o={}))et.call(o,e)&&P(i,e,o[e]);if(T)for(var e of T(o))ot.call(o,e)&&P(i,e,o[e]);return i},v=(i,o)=>Z(i,tt(o));var s=(i,o,e)=>new Promise((u,f)=>{var g=r=>{try{k(e.next(r))}catch(d){f(d)}},p=r=>{try{k(e.throw(r))}catch(d){f(d)}},k=r=>r.done?u(r.value):Promise.resolve(r.value).then(g,p);k((e=e.apply(i,o)).next())});import{d as F,f as I,r as nt,ag as it,aq as at,ar as x,k as m,aD as c,u as n,aB as R,ah as S,G as C,aE as rt,F as st}from"./vue-vendor-dy9k-Yad.js";import{B as pt}from"./index-Diw57m_E.js";import{p as lt,d as mt}from"./tenant.data-BEsk-IZ-.js";import{k as B,m as ct,p as ut}from"./tenant.api-CTNrRQ_d.js";import{useListPage as dt}from"./useListPage-Soxgnx9a.js";import"./index-BkGZ5fiW.js";import ft from"./TenantPackMenuModal-CVBiAp3Q.js";import{M as D}from"./antd-vue-vendor-me9YkNVC.js";import kt from"./TenantPackUserModal-Bj-z_8ic.js";import{ad as O,ac as gt,u as yt}from"./index-CCWaWN5g.js";import ht from"./BasicTable-xCEZpGLb.js";import{Q as wt}from"./componentMap-Bkie1n3v.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./renderUtils-D7XVOFwj.js";import"./index-QxsVJqiT.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./TenantUserSelectModal-l6S-j4jy.js";const xt=F({name:"tenant-pack-modal"}),Ue=F(v(A({},xt),{emits:["register","success"],setup(i,{emit:o}){const[e,{openModal:u}]=O(),[f,{openModal:g}]=O(),p=I(0),{prefixCls:k,tableContext:r}=dt({designScope:"tenant-template",tableProps:{api:ut,columns:mt,immediate:!1,formConfig:{schemas:lt,labelCol:{xxl:8},actionColOptions:{xs:24,sm:8,md:8,lg:8,xl:8,xxl:8}},beforeFetch:t=>Object.assign(t,{tenantId:n(p),packType:"custom"})}}),[d,{reload:U},{rowSelection:E,selectedRowKeys:y,selectedRows:N}]=r,Ct=o,h=I(!1),[V,{setModalProps:_t,closeModal:j}]=gt(t=>s(null,null,function*(){p.value=t.tenantId,h.value=t.showPackAddAndEdit,l()})),K="租户个性化套餐包";function L(t){return s(this,null,function*(){j()})}function $(t){return[{label:"用户",onClick:J.bind(null,t)},{label:"编辑",onClick:q.bind(null,t),ifShow:()=>h.value}]}function l(){(y.value=[])&&U()}function q(t){return s(this,null,function*(){u(!0,{isUpdate:!0,record:t,tenantId:n(p),packType:"custom",showFooter:!0})})}const _=nt(["superAdmin","accountAdmin","appAdmin"]),{createMessage:b}=yt();function G(t){return s(this,null,function*(){if(_.indexOf(t.packCode)!=-1){b.warning("默认系统套餐包不允许删除");return}yield B({ids:t.id},l)})}function Q(){return s(this,null,function*(){let t=N.value;if(t&&t.length>0){for(let a=0;a<t.length;a++)if(_.indexOf(t[a].packCode)!=-1){b.warning("默认系统套餐包不允许删除");return}}D.confirm({title:"删除租户套餐包",content:"是否删除租户套餐包",okText:"确认",cancelText:"取消",onOk:()=>s(null,null,function*(){yield B({ids:y.value.join(",")},l)})})})}function z(){return s(this,null,function*(){D.confirm({title:"初始化默认套餐包",content:"是否初始化默认套餐包",okText:"确认",cancelText:"取消",onOk:()=>s(null,null,function*(){yield ct({tenantId:n(p)},l)})})})}function H(){u(!0,{isUpdate:!1,tenantId:n(p),packType:"custom",showFooter:!0})}function J(t){g(!0,{record:t})}function W(t){return[{label:"详情",onClick:X.bind(null,t)},{label:"删除",popConfirm:{title:"是否确认删除租户套餐包",confirm:G.bind(null,t)}}]}function X(t){u(!0,{isUpdate:!0,record:t,tenantId:n(p),packType:"custom",showFooter:!1})}return(t,a)=>{const w=it("a-button");return x(),at(st,null,[m(n(pt),rt(t.$attrs,{onRegister:n(V),title:K,onOk:L,width:"800px",showCancelBtn:!1,showOkBtn:!1}),{default:c(()=>[m(n(ht),{onRegister:n(d),rowSelection:n(E)},{tableTitle:c(()=>[h.value?(x(),R(w,{key:0,preIcon:"ant-design:plus-outlined",type:"primary",onClick:H,style:{"margin-right":"5px"}},{default:c(()=>a[0]||(a[0]=[C("新增 ")])),_:1,__:[0]})):S("",!0),n(y).length>0?(x(),R(w,{key:1,preIcon:"ant-design:delete-outlined",type:"primary",onClick:Q,style:{"margin-right":"5px"}},{default:c(()=>a[1]||(a[1]=[C("批量删除 ")])),_:1,__:[1]})):S("",!0),m(w,{preIcon:"ant-design:sync-outlined",type:"primary",onClick:z,style:{"margin-right":"5px"}},{default:c(()=>a[2]||(a[2]=[C("初始化默认套餐 ")])),_:1,__:[2]})]),action:c(({record:M})=>[m(n(wt),{actions:$(M),dropDownActions:W(M)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"])]),_:1},16,["onRegister"]),m(ft,{onRegister:n(e),onSuccess:l},null,8,["onRegister"]),m(kt,{onRegister:n(f),onSuccess:l},null,8,["onRegister"])],64)}}}));export{Ue as default};
