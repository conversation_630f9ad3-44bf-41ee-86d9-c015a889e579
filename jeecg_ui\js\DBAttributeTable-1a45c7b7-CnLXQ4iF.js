import{d as k,g as x,f as L,ag as Y,aB as K,ar as $,aE as W}from"./vue-vendor-dy9k-Yad.js";import{cs as X,bx as E}from"./index-CCWaWN5g.js";import{L as Q}from"./useTableSync-075826a1-CL-4GwR8.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var j=Object.defineProperty,q=Object.defineProperties,Z=Object.getOwnPropertyDescriptors,D=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable,b=(e,a,l)=>a in e?j(e,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[a]=l,S=(e,a)=>{for(var l in a||(a={}))J.call(a,l)&&b(e,l,a[l]);if(D)for(var l of D(a))z.call(a,l)&&b(e,l,a[l]);return e},C=(e,a)=>q(e,Z(a));const ee=["ADD","ALL","ALTER","ANALYZE","AND","AS","ASC","ASENSITIVE","BEFORE","BETWEEN","BIGINT","BINARY","BLOB","BOTH","BY","CALL","CASCADE","CASE","CHANGE","CHAR","CHARACTER","CHECK","COLLATE","COLUMN","CONDITION","CONNECTION","CONSTRAINT","CONTINUE","CONVERT","CREATE","CROSS","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","DATABASE","DATABASES","DAY_HOUR","DAY_MICROSECOND","DAY_MINUTE","DAY_SECOND","DEC","DECIMAL","DECLARE","DEFAULT","DELAYED","DELETE","DESC","DESCRIBE","DETERMINISTIC","DISTINCT","DISTINCTROW","DIV","DOUBLE","DROP","DUAL","EACH","ELSE","ELSEIF","ENCLOSED","ESCAPED","EXISTS","EXIT","EXPLAIN","FALSE","FETCH","FLOAT","FLOAT4","FLOAT8","FOR","FORCE","FOREIGN","FROM","FULLTEXT","GOTO","GRANT","GROUP","HAVING","HIGH_PRIORITY","HOUR_MICROSECOND","HOUR_MINUTE","HOUR_SECOND","IF","IGNORE","IN","INDEX","INFILE","INNER","INOUT","INSENSITIVE","INSERT","INT","INT1","INT2","INT3","INT4","INT8","INTEGER","INTERVAL","INTO","IS","ITERATE","JOIN","KEY","KEYS","KILL","LABEL","LEADING","LEAVE","LEFT","LIKE","LIMIT","LINEAR","LINES","LOAD","LOCALTIME","LOCALTIMESTAMP","LOCK","LONG","LONGBLOB","LONGTEXT","LOOP","LOW_PRIORITY","MATCH","MEDIUMBLOB","MEDIUMINT","MEDIUMTEXT","MIDDLEINT","MINUTE_MICROSECOND","MINUTE_SECOND","MOD","MODIFIES","NATURAL","NOT","NO_WRITE_TO_BINLOG","NULL","NUMERIC","ON","OPTIMIZE","OPTION","OPTIONALLY","OR","ORDER","OUT","OUTER","OUTFILE","PRECISION","PRIMARY","PROCEDURE","PURGE","RAID0","RANGE","READ","READS","REAL","REFERENCES","REGEXP","RELEASE","RENAME","REPEAT","REPLACE","REQUIRE","RESTRICT","RETURN","REVOKE","RIGHT","RLIKE","SCHEMA","SCHEMAS","SECOND_MICROSECOND","SELECT","SENSITIVE","SEPARATOR","SET","SHOW","SMALLINT","SPATIAL","SPECIFIC","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQL_BIG_RESULT","SQL_CALC_FOUND_ROWS","SQL_SMALL_RESULT","SSL","STARTING","STRAIGHT_JOIN","TABLE","TERMINATED","THEN","TINYBLOB","TINYINT","TINYTEXT","TO","TRAILING","TRIGGER","TRUE","UNDO","UNION","UNIQUE","UNLOCK","UNSIGNED","UPDATE","USAGE","USE","USING","UTC_DATE","UTC_TIME","UTC_TIMESTAMP","VALUES","VARBINARY","VARCHAR","VARCHARACTER","VARYING","WHEN","WHERE","WHILE","WITH","WRITE","X509","XOR","YEAR_MONTH","ZEROFILL"],te=k({name:"DBAttributeTable",props:{actionButton:{type:Boolean,default:!0}},emits:["added","removed","inserted","dragged","syncDbType","syncDbIsPersist","syncDbIsNull"],setup(e,{emit:a}){const l=x(),s=L(!1),R=L([{title:"字段名称",key:"dbFieldName",width:140,type:E.input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"},{pattern:/^[a-zA-Z]{1}(?!_)[a-zA-Z0-9_\\$]+$/,message:"命名规则：只能由字母、数字、下划线、$符号组成；必须以字母开头；不能以单个字母加下滑线开头"},{unique:!0,message:"${title}不能重复"},{handler({cellValue:t},i){ee.includes(t.toUpperCase())?i(!1,t+"是关键字，不能作为字段名称使用！"):i(!0)}},{handler:c},{handler({cellValue:t},i){t.length>32?i(!1,"字段名最长32个字符"):i(!0)}}],disabled:!e.actionButton},{title:"字段备注",key:"dbFieldTxt",width:140,type:E.input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"},{handler({cellValue:t},i){t.length>200?i(!1,"字段名最长200个字"):i(!0)}}]},{title:"字段长度",key:"dbLength",width:120,type:E.inputNumber,defaultValue:32,placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"}],disabled:!e.actionButton},{title:"小数点",key:"dbPointLength",width:100,type:E.inputNumber,defaultValue:0,placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"}],disabled:!e.actionButton},{title:"默认值",key:"dbDefaultVal",width:140,type:E.input,defaultValue:"",disabled:!e.actionButton},{title:"字段类型",key:"dbType",width:140,type:E.select,options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Date",value:"Date"},{title:"Datetime",value:"Datetime"},{title:"BigDecimal",value:"BigDecimal"},{title:"Text",value:"Text"},{title:"Blob",value:"Blob"}],defaultValue:"string",placeholder:"请选择${title}",disabled:!e.actionButton,validateRules:[{required:!0,message:"请选择${title}"},{handler:G}]},{title:"主键",key:"dbIsKey",width:80,type:E.checkbox,align:"center",customValue:["1","0"],defaultChecked:!1,disabled:!e.actionButton},{title:"允许空值",key:"dbIsNull",width:80,type:E.checkbox,customValue:["1","0"],defaultChecked:!0,disabled:!e.actionButton},{title:"同步数据库",key:"dbIsPersist",minWidth:80,type:E.checkbox,customValue:["1","0"],defaultChecked:!0,disabled:!e.actionButton},{title:"orderNum",key:"orderNum",type:E.hidden}]);let u=[];const T=Q(R),{tableRef:N,loading:A,dataSource:g,tableHeight:m,tableProps:p,setDataSource:h,validateData:v}=T;function y(){a("added",l)}function f(t){u=u.concat(t.deleteRows.map(i=>i.id)),a("removed",C(S({},t),{removeIds:u,target:l}))}function U(t){a("dragged",{oldIndex:t.oldIndex,newIndex:t.newIndex,target:l})}function B(t){a("inserted",C(S({},t),{target:l}))}function M(){return u}function P(t){let{type:i,row:n,col:o,value:d,target:r,oldValue:I}=t;i===E.select&&o.key==="dbType"?((d==="Date"||d==="Datetime")&&a("syncDbType",{row:n,value:d,target:l}),(d!=="Date"||d!=="Datetime")&&(I=="Date"||I=="Datetime")&&a("syncDbType",{row:n,value:d,target:l}),d==="Blob"||d==="Text"||d==="Date"?r.setValues([{rowKey:n.id,values:{dbLength:"0"}}]):d==="string"?r.setValues([{rowKey:n.id,values:{dbLength:"32"}}]):d==="int"||d==="double"||d==="BigDecimal"?r.setValues([{rowKey:n.id,values:{dbLength:"10"}}]):n.dbLength==="0"&&r.setValues([{rowKey:n.id,values:{dbLength:"32"}}])):o.key==="dbIsPersist"?a("syncDbIsPersist",{row:n,value:d,target:l}):o.key==="dbIsNull"&&a("syncDbIsNull",{row:n,value:d,target:l})}function V(t,i,n){var o;if(t==="has_child")return!0;if(t==="id"){const{tables:d}=T,r=((o=d.dbTable.value.tableRef.getTableData())!=null?o:[]).findIndex(I=>I.dbFieldName==="id");if((r===-1?0:r)===n)return!0}return!1}function _(t){N.value.pushRows(t),s.value||a("added",l)}function w(t){return N.value.removeRowsById(t)}function H(){s.value=!0,A.value=!0}function F(){s.value=!1,A.value=!1,a("added",l)}function c({cellValue:t,row:i},n){const{tables:o}=T;if(o){let d=o.dbTable.value.tableRef.dataSource.filter(O=>O.id===i.id);(!d||d.length<=0)&&n(!0);let r=d[0].dbFieldName;r==t&&n(!0);let I=o.idxTable.value.tableRef.getTableData();for(let O of I)O.indexField.split(",").indexOf(r)>=0&&n(!1,"当前字段存在索引配置，请先删除索引再修改字段")}n(!0)}function G({cellValue:t,row:i},n){i.dbType=="int"&&i.dbPointLength>0&&n(!1,"设置了小数点不可设置integer类型"),n(!0)}return{tableRef:N,loading:A,columns:R,dataSource:g,setDataSource:h,addBatchBegin:H,addBatchEnd:F,tableAddLine:_,tableHeight:m,tableProps:p,tableDeleteLines:w,handleAdded:y,handleRemoved:f,handleDragged:U,handleInserted:B,handleValueChange:P,handleDisabledDbFieldName:V,validateData:v,getRemoveIds:M,validateExistIndex:c}}});function ae(e,a,l,s,R,u){const T=Y("JVxeTable");return $(),K(T,W({class:"dBAttributeTable",ref:"tableRef",rowNumber:"",rowSelection:"",dragSort:"",notAllowDrag:[{key:"dbFieldName",value:"id"}],keyboardEdit:"",sortKey:"orderNum",addButtonSettings:"",loading:e.loading,columns:e.columns,dataSource:e.dataSource,toolbar:e.actionButton,maxHeight:e.tableHeight.normal,disabledRows:{dbFieldName:e.handleDisabledDbFieldName}},e.tableProps,{onAdded:e.handleAdded,onRemoved:e.handleRemoved,onDragged:e.handleDragged,onInserted:e.handleInserted,onValueChange:e.handleValueChange}),null,16,["loading","columns","dataSource","toolbar","maxHeight","disabledRows","onAdded","onRemoved","onDragged","onInserted","onValueChange"])}const re=X(te,[["render",ae],["__scopeId","data-v-7723c158"]]);export{re as default};
