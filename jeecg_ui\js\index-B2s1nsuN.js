var d=(o,t,n)=>new Promise((u,m)=>{var l=e=>{try{r(n.next(e))}catch(a){m(a)}},c=e=>{try{r(n.throw(e))}catch(a){m(a)}},r=e=>e.done?u(e.value):Promise.resolve(e.value).then(l,c);r((n=n.apply(o,t)).next())});import{d as k,ag as p,aB as T,ar as C,aD as s,k as i,G as _,at as g}from"./vue-vendor-dy9k-Yad.js";import{P as x}from"./index-CtJ0w2CP.js";import{j as y,ah as E,a as S}from"./index-CCWaWN5g.js";import{J as f}from"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const A=()=>y.post({url:"/mock/user/tokenExpired"}),I=k({name:"TestSessionTimeout",components:{ACardGrid:f.Grid,ACard:f,PageWrapper:x},setup(){const o=E();function t(){return d(this,null,function*(){o.setToken(void 0),o.setSessionTimeout(!0)})}function n(){return d(this,null,function*(){try{yield A()}catch(u){}})}return{test1:t,test2:n}}});function N(o,t,n,u,m,l){const c=p("a-button"),r=p("a-card-grid"),e=p("a-card"),a=p("PageWrapper");return C(),T(a,{title:"登录过期示例",content:"用户登录过期示例，不再跳转登录页，直接生成页面覆盖当前页面，方便保持过期前的用户状态！"},{default:s(()=>[i(e,{title:"请点击下面的按钮访问测试接口",extra:"所访问的接口会返回Token过期响应"},{default:s(()=>[i(r,{style:{width:"50%","text-align":"center"}},{default:s(()=>[i(c,{type:"primary",onClick:o.test1},{default:s(()=>t[0]||(t[0]=[_("HttpStatus == 401")])),_:1,__:[0]},8,["onClick"])]),_:1}),i(r,{style:{width:"50%","text-align":"center"}},{default:s(()=>[t[2]||(t[2]=g("span",null,null,-1)),i(c,{class:"ml-4",type:"primary",onClick:o.test2},{default:s(()=>t[1]||(t[1]=[_("Response.code == 401")])),_:1,__:[1]},8,["onClick"])]),_:1,__:[2]})]),_:1})]),_:1})}const D=S(I,[["render",N]]);export{D as default};
