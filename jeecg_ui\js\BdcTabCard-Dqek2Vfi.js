import{d as C,f as B,e as k,ag as s,aB as D,ar as M,aD as t,at as l,k as e}from"./vue-vendor-dy9k-Yad.js";import{B as j}from"./Bar-Tb_Evzfu.js";import{B as w,G as h}from"./Gauge-DelrgGV_.js";import m from"./QuickNav-u-J2mPxn.js";import{D as S,a as T}from"./index-CCWaWN5g.js";import"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const $={class:"salesCard"},E={class:"extra-wrapper"},G=C({__name:"BdcTabCard",props:{loading:{type:Boolean}},setup(n){const{getThemeColor:f}=S(),g=B(),x=[];for(let a=0;a<7;a++)x.push({name:"白鹭岛 "+(a+1)+" 号店",total:1234.56-a*100});const p=[];for(let a=0;a<12;a+=1)p.push({name:`${a+1}月`,value:Math.floor(Math.random()*1e3)+200});const _=[];for(let a=0;a<2;a++)for(let o=0;o<12;o+=1)_.push({type:a==0?"jeecg":"jeebt",name:`${o+1}月`,value:Math.floor(Math.random()*1e3)+200});const i=k(()=>(g.value=[{type:"jeecg",color:f.value},{type:"jeebt",color:v()}],f.value));function v(){for(var a="0123456789ABCDEF",o="#",d=0;d<6;d++)o+=a[Math.floor(Math.random()*16)];return o}return(a,o)=>{const d=s("a-range-picker"),r=s("a-col"),c=s("a-row"),u=s("a-tab-pane"),y=s("a-tabs"),b=s("a-card");return M(),D(b,{loading:n.loading,bordered:!1,"body-style":{padding:"0"}},{default:t(()=>[l("div",$,[e(y,{"default-active-key":"1",size:"large","tab-bar-style":{marginBottom:"24px",paddingLeft:"16px"}},{rightExtra:t(()=>[l("div",E,[o[0]||(o[0]=l("div",{class:"extra-item"},[l("a",null,"今日"),l("a",null,"本周"),l("a",null,"本月"),l("a",null,"本年")],-1)),e(d,{style:{width:"256px"}})])]),default:t(()=>[e(u,{loading:"true",tab:"受理监管",key:"1"},{default:t(()=>[e(c,null,{default:t(()=>[e(r,{xl:16,lg:12,md:12,sm:24,xs:24},{default:t(()=>[e(j,{chartData:p,option:{title:{text:"",textStyle:{fontWeight:"lighter"}}},height:"40vh",seriesColor:i.value},null,8,["seriesColor"])]),_:1}),e(r,{xl:8,lg:12,md:12,sm:24,xs:24},{default:t(()=>[e(m,{loading:n.loading,class:"enter-y",bordered:!1,"body-style":{padding:0}},null,8,["loading"])]),_:1})]),_:1})]),_:1}),e(u,{tab:"交互监管",key:"2"},{default:t(()=>[e(c,null,{default:t(()=>[e(r,{xl:16,lg:12,md:12,sm:24,xs:24},{default:t(()=>[e(w,{seriesColor:g.value,chartData:_,option:{title:{text:"",textStyle:{fontWeight:"lighter"}}},height:"40vh"},null,8,["seriesColor"])]),_:1}),e(r,{xl:8,lg:12,md:12,sm:24,xs:24},{default:t(()=>[e(m,{loading:n.loading,class:"enter-y",bordered:!1,"body-style":{padding:0}},null,8,["loading"])]),_:1})]),_:1})]),_:1}),e(u,{tab:"存储监管",key:"3"},{default:t(()=>[e(c,null,{default:t(()=>[e(r,{xl:16,lg:12,md:12,sm:24,xs:24,style:{display:"flex"}},{default:t(()=>[e(h,{seriesColor:i.value,chartData:{name:"C盘",value:70},height:"30vh"},null,8,["seriesColor"]),e(h,{seriesColor:i.value,chartData:{name:"D盘",value:50},height:"30vh"},null,8,["seriesColor"])]),_:1}),e(r,{xl:8,lg:12,md:12,sm:24,xs:24},{default:t(()=>[e(m,{loading:n.loading,class:"enter-y",bordered:!1,"body-style":{padding:0}},null,8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1})])]),_:1},8,["loading"])}}}),J=T(G,[["__scopeId","data-v-28549fef"]]);export{J as default};
