import{f as u}from"./vue-vendor-dy9k-Yad.js";import{S as i}from"./index-CCWaWN5g.js";import{useWindowSizeFn as n}from"./useWindowSizeFn-DDbrQbks.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function b(e){const t=u((e==null?void 0:e.def)||"600px");n(m,100,{immediate:!0});function m(){let a=document.documentElement.clientWidth;switch(!0){case a>i.XL:t.value=(e==null?void 0:e.xl)||"600px";break;case a>i.LG:t.value=(e==null?void 0:e.lg)||"600px";break;case a>i.MD:t.value=(e==null?void 0:e.md)||"600px";break;case a>i.SM:t.value=(e==null?void 0:e.sm)||"500px";break;case a>i.XS:t.value=(e==null?void 0:e.xs)||"400px";break;default:t.value=(e==null?void 0:e.mindef)||"300px";break}}return{width:t,calcWidth:m}}export{b as useAdapt};
