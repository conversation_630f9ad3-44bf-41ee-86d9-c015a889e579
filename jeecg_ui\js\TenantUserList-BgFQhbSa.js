var _e=Object.defineProperty,Ae=Object.defineProperties;var ve=Object.getOwnPropertyDescriptors;var oe=Object.getOwnPropertySymbols;var xe=Object.prototype.hasOwnProperty,Ce=Object.prototype.propertyIsEnumerable;var se=(t,n,o)=>n in t?_e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o,ae=(t,n)=>{for(var o in n||(n={}))xe.call(n,o)&&se(t,o,n[o]);if(oe)for(var o of oe(n))Ce.call(n,o)&&se(t,o,n[o]);return t},re=(t,n)=>Ae(t,ve(n));var U=(t,n,o)=>new Promise((A,i)=>{var m=a=>{try{r(o.next(a))}catch(l){i(l)}},y=a=>{try{r(o.throw(a))}catch(l){i(l)}},r=a=>a.done?A(a.value):Promise.resolve(a.value).then(m,y);r((o=o.apply(t,n)).next())});import{d as P,e as q,ag as R,aB as _,ar as g,aD as c,k as u,G as b,au as B,ah as j,f as E,aq as G,F as ie,aE as le,aP as pe,l as Q,o as Ie,u as p,at as ue}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{M as K,bh as Ue}from"./antd-vue-vendor-me9YkNVC.js";import{cg as ce,j as de,u as ye,a as fe,ad as V,ah as Me,ce as Le,bo as Re}from"./index-CCWaWN5g.js";import Be from"./UserQuitAgentModal-CMmrtyO_.js";import Ee from"./UserQuitModal-CVw5-61f.js";import{a as De}from"./index-JbqXEynz.js";import{useListPage as $e}from"./useListPage-Soxgnx9a.js";import"./index-Diw57m_E.js";import{u as Fe,e as Ne}from"./user.api-mLAlJze4.js";import{u as Qe,a as je}from"./user.data-CLRTqTDz.js";import{U as Pe}from"./JSelectUser-COkExGbu.js";import{c as ze}from"./UserSetting.api-BJ086Ekj.js";import{g as Oe}from"./tenant.api-CTNrRQ_d.js";import He from"./TenantUserDrawer-C78pZld3.js";import{Q as Ke}from"./componentMap-Bkie1n3v.js";import Ve from"./BasicTable-xCEZpGLb.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectDept-I-NqkbOH.js";import"./props-CCT78mKr.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JSelectBiz-jOYRdMJf.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";import"./tenant.data-BEsk-IZ-.js";const ge={getEnabledType:"/sys/thirdApp/getEnabledType",wechatEnterprise:{user:"/sys/thirdApp/sync/wechatEnterprise/user",depart:"/sys/thirdApp/sync/wechatEnterprise/depart"},dingtalk:{user:"/sys/thirdApp/sync/dingtalk/user",depart:"/sys/thirdApp/sync/dingtalk/depart"}};let J=null;const Je=()=>U(null,null,function*(){if(J!=null)return ce(J);{let{success:t,result:n}=yield de.get({url:ge.getEnabledType},{isTransformResponse:!1});if(t)return J=ce(n),n}return{}}),me=P({__name:"JThirdAppDropdown",props:{type:String,name:String,syncToApp:Boolean,syncToLocal:Boolean},emits:["to-app","to-local"],setup(t,{emit:n}){const o=t,A=n,i=q(()=>o.type==="wechatEnterprise"?!1:o.syncToLocal);function m(y){A(y.key,{type:o.type})}return(y,r)=>{const a=R("a-button"),l=R("a-menu-item"),v=R("a-menu"),D=R("a-dropdown");return t.syncToApp&&t.syncToLocal?(g(),_(D,{key:0},{overlay:c(()=>[u(v,{onClick:m},{default:c(()=>[t.syncToApp?(g(),_(l,{key:"to-app"},{default:c(()=>[b("同步到"+B(t.name),1)]),_:1})):j("",!0),i.value?(g(),_(l,{key:"to-local"},{default:c(()=>r[2]||(r[2]=[b("同步到本地")])),_:1,__:[2]})):j("",!0)]),_:1})]),default:c(()=>[u(a,{type:"primary",preIcon:"ant-design:sync-outlined"},{default:c(()=>[b("同步"+B(t.name),1)]),_:1})]),_:1})):t.syncToApp?(g(),_(a,{key:1,type:"primary",preIcon:"ant-design:sync-outlined",onClick:r[0]||(r[0]=x=>m({key:"to-app"}))},{default:c(()=>[b("同步"+B(t.name),1)]),_:1})):(g(),_(a,{key:2,type:"primary",preIcon:"ant-design:sync-outlined",onClick:r[1]||(r[1]=x=>m({key:"to-local"}))},{default:c(()=>[b("同步"+B(t.name)+"到本地",1)]),_:1}))}}}),qe=P({__name:"JThirdAppButton",props:{bizType:{type:String,required:!0},syncToApp:Boolean,syncToLocal:Boolean,selectedRowKeys:Array},emits:["sync-ok","sync-error","sync-finally"],setup(t,{emit:n}){const{createMessage:o,createWarningModal:A}=ye(),i=t,m=n,y=E({}),r=q(()=>({syncToApp:i.syncToApp,syncToLocal:i.syncToLocal})),a=q(()=>({"to-app":l,"to-local":v}));function l(f){x(f.type,"/toApp")}function v(f){x(f.type,"/toLocal")}function D(){return U(this,null,function*(){y.value=yield Je()})}function x(f,T){let w=ge[f];if(!(w&&w[i.bizType]))return;let X=w[i.bizType]+T,k=i.selectedRowKeys,O="确定要开始同步全部数据吗？可能花费较长时间！";return Array.isArray(k)&&k.length>0?O=`确定要开始同步这 ${k.length} 项吗？`:k=[],new Promise(($,W)=>{const F=K.confirm({icon:u(Ue),title:"同步",content:O,onOk:()=>{F.update({keyboard:!1,okText:"同步中…",cancelButtonProps:{disabled:!0}});let H={ids:k.join(",")};return de.get({url:X,params:H},{isTransformResponse:!1}).then(d=>{let S={};d.result&&(S={width:600,title:d.message,content:()=>{let M,N=["成功信息如下：",z(Q,d.result.successInfo.map((L,C)=>`${C+1}. ${L}`).join(`
`))];return d.success?M=[...N,Q("br"),"无失败信息！"]:M=["失败信息如下：",z(Q,d.result.failInfo.map((L,C)=>`${C+1}. ${L}`).join(`
`)),Q("br"),...N],M}}),d.success?(S!=null?K.success(S):o.warning(d.message),m("sync-ok")):(S!=null?K.warning(S):o.warning(d.message),m("sync-error"))}).catch(()=>F.destroy()).finally(()=>{$(),m("sync-finally",{type:f,direction:T,isToApp:T==="/toApp",isToLocal:T==="/toLocal"})})},onCancel(){$()}})})}function z(f,T){return f("div",{id:"box",style:{minHeight:"100px",border:"1px solid #d9d9d9",fontSize:"14px",maxHeight:"250px",whiteSpace:"pre",overflow:"auto",padding:"10px"}},T)}return D(),(f,T)=>t.syncToApp||t.syncToLocal?(g(),G(ie,{key:0},[y.value.wechatEnterprise?(g(),_(me,le({key:0,type:"wechatEnterprise",name:"企微"},r.value,pe(a.value)),null,16)):j("",!0),y.value.dingtalk?(g(),_(me,le({key:1,type:"dingtalk",name:"钉钉"},r.value,pe(a.value)),null,16)):j("",!0)],64)):(g(),G(ie,{key:1},[b("未设置任何同步方向")],64))}}),Ge=fe(qe,[["__scopeId","data-v-d8023e59"]]),Xe={style:{"margin-left":"10px","margin-top":"5px"}},We={class:"tenant-name"},Ye=P({name:"tenant-system-user"}),Ze=P(re(ae({},Ye),{setup(t){const{createMessage:n,createConfirm:o}=ye(),[A,{openDrawer:i}]=De(),[m,{openModal:y}]=V(),[r,{openModal:a}]=V(),l=Me(),v=l.getUserInfo.username,{prefixCls:D,tableContext:x,onExportXls:z,onImportXls:f}=$e({designScope:"user-list",tableProps:{title:"租户用户列表",api:Ne,columns:je,size:"small",formConfig:{schemas:Qe},actionColumn:{width:120},beforeFetch:e=>(e.userTenantStatus="1,3,4",Object.assign({column:"createTime",order:"desc"},e))}}),[T,{reload:w,updateTableDataRecord:X},{rowSelection:k,selectedRows:O,selectedRowKeys:$}]=x;function W(){i(!0,{isUpdate:!1,showFooter:!0,tenantSaas:!0})}function F(e){return U(this,null,function*(){i(!0,{record:e,isUpdate:!0,showFooter:!0,tenantSaas:!0})})}function H(e){return U(this,null,function*(){i(!0,{record:e,isUpdate:!0,showFooter:!1,tenantSaas:!0})})}function d(){w()}function S({isToLocal:e}){e&&w()}function M(e){return[{label:"编辑",onClick:F.bind(null,e)}]}function N(e){return[{label:"详情",onClick:H.bind(null,e)},{label:"离职",onClick:L.bind(null,e.username,e.id),ifShow:()=>e.status==="1"&&e.username!==e.createBy},{label:"交接",onClick:be.bind(null,e),ifShow:()=>e.username===e.createBy},{label:"同意",onClick:C.bind(null,e.id,"1"),ifShow:()=>(e.status==="3"||e.status==="4")&&e.createBy===v},{label:"拒绝",popConfirm:{title:"是否确认拒绝",confirm:C.bind(null,e.id,"4")},ifShow:()=>e.status==="3"&&e.createBy===v}]}function L(e,s){y(!0,{userName:e,userId:s})}function C(e,s){Fe({userId:e,status:s}).then(h=>{h.success&&d()}).catch(h=>{n.warning(h.message)})}const Y=E(""),Z=E([]),[Te,{openModal:we}]=V(),ee=E("");function be(e){Y.value=Re(),Z.value=[e.id],ee.value=e.createBy,we(!0)}function he(e,s){if(s&&s.length>0){let h=s[0];ze({userId:h,tenantId:p(Y)}).then(I=>{var ne;I.success?(n.success("交接成功"),((ne=l.getUserInfo)==null?void 0:ne.username)==ee.value?l.logout(!0):w()):n.warning(I.message)})}}const te=E("");ke();function ke(){return U(this,null,function*(){te.value=yield Oe()})}function Se(e){let s=l.getUserInfo.username;s&&e===s?(l.setTenant(null),window.location.reload()):w()}return Ie(()=>{Le("租户用户")}),(e,s)=>{const h=R("a-button");return g(),G("div",null,[u(p(Ve),{onRegister:p(T),rowSelection:p(k)},{tableTitle:c(()=>[u(h,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:W},{default:c(()=>s[1]||(s[1]=[b(" 新增")])),_:1,__:[1]}),u(Ge,{"biz-type":"user","selected-row-keys":p($),syncToApp:"",syncToLocal:"",onSyncFinally:S},null,8,["selected-row-keys"]),u(h,{type:"primary",onClick:s[0]||(s[0]=I=>p(a)(!0,{})),preIcon:"ant-design:user-delete-outlined"},{default:c(()=>s[2]||(s[2]=[b("离职信息")])),_:1,__:[2]}),ue("div",Xe,[s[3]||(s[3]=b(" 当前登录租户: ")),ue("span",We,B(te.value),1)])]),action:c(({record:I})=>[u(p(Ke),{actions:M(I),dropDownActions:N(I)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),u(He,{onRegister:p(A),onSuccess:d},null,8,["onRegister"]),u(Be,{onRegister:p(m),onSuccess:Se},null,8,["onRegister"]),u(Ee,{onRegister:p(r),onSuccess:p(w)},null,8,["onRegister","onSuccess"]),u(Pe,{onRegister:p(Te),excludeUserIdList:Z.value,maxSelectCount:1,onGetSelectResult:he},null,8,["onRegister","excludeUserIdList"])])}}})),wn=fe(Ze,[["__scopeId","data-v-a9171063"]]);export{wn as default};
