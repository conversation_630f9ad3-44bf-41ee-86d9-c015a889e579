import{d as f,f as d,w as c,ag as u,aq as o,ar as r,as as m,F as C,aC as k,aA as h,k as v}from"./vue-vendor-dy9k-Yad.js";import{aA as b}from"./antd-vue-vendor-me9YkNVC.js";import{F as _,D as g,ay as y,a as $}from"./index-CCWaWN5g.js";import{b as D}from"./index-CI-8_pdX.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-JbqXEynz.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useHeaderSetting-C-h5S52e.js";import"./useMultipleTabSetting-QBbnIi9J.js";const A=f({name:"ThemeColorPicker",components:{CheckOutlined:b},props:{colorList:{type:Array,defualt:[]},event:{type:Number},def:{type:String}},setup(e){const{prefixCls:i}=_("setting-theme-picker"),{getDarkMode:n}=g(),a=d(!1);function l(s){e.event&&D(e.event,s)}return c(()=>n.value,s=>{a.value=e.event===1?!1:s===y.DARK},{immediate:!0}),{prefixCls:i,handleClick:l,isDisabledColor:a}}}),F=["onClick"];function L(e,i,n,a,l,s){const p=u("CheckOutlined");return r(),o("div",{class:m(e.prefixCls)},[(r(!0),o(C,null,k(e.colorList||[],t=>(r(),o("span",{key:t,onClick:O=>!e.isDisabledColor&&e.handleClick(t),class:m([`${e.prefixCls}__item`,{[`${e.prefixCls}__item--active`]:e.def===t,[`${e.prefixCls}__item--black`]:t=="#ffffff",disabledColor:e.isDisabledColor}]),style:h({background:t})},[v(p)],14,F))),128))],2)}const j=$(A,[["render",L]]);export{j as default};
