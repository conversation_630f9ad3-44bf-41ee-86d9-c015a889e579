import{d as i,e as r,u as l,aq as a,ar as m,au as g}from"./vue-vendor-dy9k-Yad.js";import{bO as c,bU as e,N as p}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const u={class:"mb-3 text-2xl font-bold text-center xl:text-3xl enter-x xl:text-left"},b=i({__name:"LoginFormTitle",setup(_){const{t}=p(),{getLoginState:o}=c(),n=r(()=>({[e.RESET_PASSWORD]:t("sys.login.forgetFormTitle"),[e.LOGIN]:t("sys.login.signInFormTitle"),[e.REGISTER]:t("sys.login.signUpFormTitle"),[e.MOBILE]:t("sys.login.mobileSignInFormTitle"),[e.QR_CODE]:t("sys.login.qrSignInFormTitle")})[l(o)]);return(s,x)=>(m(),a("h2",u,g(n.value),1))}});export{b as default};
