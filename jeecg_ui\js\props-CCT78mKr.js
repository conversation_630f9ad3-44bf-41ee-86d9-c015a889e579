import{H as e}from"./index-CCWaWN5g.js";const l={isRadioSelection:{type:Boolean,default:!1},rowKey:{type:String,default:"id"},labelKey:{type:String,default:"name"},params:{type:Object,default:()=>{}},showButton:e.bool.def(!0),showSelected:e.bool.def(!1),maxSelectCount:{type:Number,default:0}},o={rowKey:{type:String,default:"key"},labelKey:{type:String,default:"title"},defaultExpandLevel:{type:[Number],default:0},startPid:{type:[Number,String],default:""},primaryKey:{type:[String],default:"id"},parentKey:{type:[String],default:"parentId"},titleKey:{type:[String],default:"title"},serverTreeData:e.bool.def(!0),sync:e.bool.def(!0),showButton:e.bool.def(!0),checkable:e.bool.def(!0),checkStrictly:e.bool.def(!1),multiple:e.bool.def(!0)};export{l as s,o as t};
