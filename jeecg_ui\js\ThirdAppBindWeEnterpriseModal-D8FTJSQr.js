var y=(t,d,g)=>new Promise((s,u)=>{var U=c=>{try{r(g.next(c))}catch(p){u(p)}},l=c=>{try{r(g.throw(c))}catch(p){u(p)}},r=c=>c.done?s(c.value):Promise.resolve(c.value).then(U,l);r((g=g.apply(t,d)).next())});import{d as O,f as _,l as k,ag as f,aB as b,ar as m,aD as i,k as v,at as D,aq as L,F as E,aC as W,G as $,au as F,ah as J}from"./vue-vendor-dy9k-Yad.js";import{B as q}from"./index-Diw57m_E.js";import{g as G,a as K,w as P,d as Q}from"./ThirdApp.api-3uEF4_Sr.js";import{ah as X,ac as Y,d as Z,u as ee,a as ae}from"./index-CCWaWN5g.js";import{M as H}from"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const te=O({name:"ThirdAppBindWeEnterpriseModal",components:{BasicModal:q},setup(t,{emit:d}){const g=_("企业微信绑定"),s=_({}),u=_(!1),U=_(!1),{createMessage:l}=ee(),r=_([]),c=_(""),p=_(!1),B=X(),[R,{closeModal:j}]=Y(e=>y(null,null,function*(){u.value=!0,e.izBind?yield n():yield M(),p.value=e.izBind}));function M(){return y(this,null,function*(){yield G().then(e=>{if(e.success){let a=e.result.userList;s.value=e.result,r.value=e.result.userList,u.value=!1}else l.warning(e.message),u.value=!1})})}function n(){return y(this,null,function*(){yield K().then(e=>{e.success?(s.value.jwUserDepartVos=e.result,u.value=!1):(l.warn(e.message),u.value=!1)})})}function N(){return y(this,null,function*(){U.value=!0;let e=s.value.userList,a=[];for(const o of s.value.jwUserDepartVos)o.wechatUserId&&(e=e.filter(w=>w.wechatUserId!=o.wechatUserId),a.push({wechatUserId:o.wechatUserId,wechatDepartId:o.wechatDepartId,wechatRealName:o.wechatRealName,userId:o.userId}));let h="";if(e&&e.length>0){for(const o of e)a.push({wechatUserId:o.wechatUserId,wechatDepartId:o.wechatDepartId,wechatRealName:o.wechatRealName});h="检测到未绑定的企业微信用户 "+e.length+" 位，平台将会为这 "+e.length+" 位用户创建新的账号"}H.confirm({title:"确认同步",content:h,okText:"确认",onOk:()=>{let o=JSON.stringify(a);P({jwUserDepartJson:o}).then(w=>{let A={};w.success&&(w.result&&(A={width:600,title:w.message,content:()=>{let V,x=["成功信息如下：",z(k,w.result.successInfo.map((C,T)=>`${T+1}. ${C}`).join(`
`))];return w.success?V=[...x,k("br"),"无失败信息！"]:V=["失败信息如下：",z(k,w.result.failInfo.map((C,T)=>`${T+1}. ${C}`).join(`
`)),k("br"),...x],V}}),j(),d("success",A,w))}).finally(()=>{U.value=!1})}})})}function I(e,a,h){s.value.jwUserDepartVos[h].wechatUserId=a.wechatUserId,s.value.jwUserDepartVos[h].wechatRealName=a.wechatRealName,s.value.jwUserDepartVos[h].wechatDepartId=a.wechatDepartId,r.value=r.value.filter(o=>o.wechatUserId!=a.wechatUserId)}function S(e,a){p.value?H.confirm({title:"确认取消绑定吗",okText:"确认",onOk:()=>y(null,null,function*(){yield Q({id:a.thirdId,sysUserId:B.getUserInfo.id}).then(h=>{h.success?(l.success("取消绑定成功！"),n()):l.warning(h.message)})})}):(r.value.push({wechatUserId:a.wechatUserId,wechatRealName:a.wechatRealName,wechatDepartId:a.wechatDepartId}),s.value.jwUserDepartVos[e].wechatUserId="",s.value.jwUserDepartVos[e].wechatRealName="",s.value.jwUserDepartVos[e].wechatDepartId="")}function z(e,a){return e("div",{id:"box",style:{minHeight:"100px",border:"1px solid #d9d9d9",fontSize:"14px",maxHeight:"250px",whiteSpace:"pre",overflow:"auto",padding:"10px"}},a)}return{title:g,registerModal:R,handleSubmit:N,bindData:s,getFileAccessHttpUrl:Z,loading:u,userList:r,handleSelect:I,handleRemoveClick:S,btnLoading:U,izBind:p}}}),se={class:"we-bind"},ne={class:"we-account"},oe={class:"we-account"},re={key:0,class:"we-remove"},le=["onClick"];function ce(t,d,g,s,u,U){const l=f("a-col"),r=f("a-row"),c=f("a-avatar"),p=f("a-input"),B=f("a-select"),R=f("a-spin"),j=f("a-button"),M=f("BasicModal");return m(),b(M,{onRegister:t.registerModal,width:800,title:t.title,destroyOnClose:""},{footer:i(()=>[t.izBind?J("",!0):(m(),b(j,{key:0,type:"primary",onClick:t.handleSubmit},{default:i(()=>d[2]||(d[2]=[$("同步")])),_:1,__:[2]},8,["onClick"]))]),default:i(()=>[v(R,{spinning:t.loading},{default:i(()=>[D("div",se,[v(r,{span:24,class:"we-title-background"},{default:i(()=>[v(l,{span:12,class:"border-right"},{default:i(()=>d[0]||(d[0]=[D("span",null,"组织用户",-1)])),_:1,__:[0]}),v(l,{span:12,class:"padding-left"},{default:i(()=>d[1]||(d[1]=[D("span",null,"企业微信用户",-1)])),_:1,__:[1]})]),_:1}),v(r,{span:24},{default:i(()=>[(m(!0),L(E,null,W(t.bindData.jwUserDepartVos,(n,N)=>(m(),L(E,null,[v(l,{span:12,class:"border-right padding-left border-bottom"},{default:i(()=>[D("div",ne,[n.avatar?(m(),b(c,{key:0,src:t.getFileAccessHttpUrl(n.avatar),size:28},null,8,["src"])):(m(),b(c,{key:1,size:28},{default:i(()=>[$(F(n.realName.length>2?n.realName.substr(0,2):n.realName),1)]),_:2},1024)),v(p,{style:{"margin-left":"20px"},value:n.realName,readonly:""},null,8,["value"])])]),_:2},1024),v(l,{span:12,class:"padding-left border-bottom"},{default:i(()=>[D("div",oe,[n.wechatUserId||t.izBind?(m(),L("span",re,[$(F(n.wechatRealName)+" ",1),D("span",{style:{"margin-right":"20px"},onClick:I=>t.handleRemoveClick(N,n)},"移出",8,le)])):(m(),b(B,{key:1,value:n.wechatUserId,"onUpdate:value":I=>n.wechatUserId=I,options:t.userList,fieldNames:{label:"wechatRealName",value:"wechatUserId"},style:{width:"200px"},showSearch:"",onSelect:(I,S)=>t.handleSelect(I,S,N)},null,8,["value","onUpdate:value","options","onSelect"]))])]),_:2},1024)],64))),256))]),_:1})])]),_:1},8,["spinning"])]),_:1},8,["onRegister","title"])}const ke=ae(te,[["render",ce],["__scopeId","data-v-81ac01dd"]]);export{ke as default};
