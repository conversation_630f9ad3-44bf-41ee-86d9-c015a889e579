import{d as U,f as s,ag as n,aq as k,ar as v,k as t,aD as o,as as q,u as r,aB as G,ah as z,au as L,at as $,G as b}from"./vue-vendor-dy9k-Yad.js";import{ah as j}from"./antd-vue-vendor-me9YkNVC.js";import A from"./ChartGroupCard-BvAE9ozT.js";import H from"./BdcTabCard-Dqek2Vfi.js";import{t as y,a as C}from"./data-BpJ37qIE.js";import{a as J}from"./index-CCWaWN5g.js";import"./SingleLine-kPL5Z-yi.js";import"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";import"./Bar-Tb_Evzfu.js";import"./Gauge-DelrgGV_.js";import"./QuickNav-u-J2mPxn.js";const M={class:"p-4"},O={class:"extra-wrapper"},Q={key:0,style:{color:"red"}},W=U({__name:"IndexBdc",setup(X){const i=s(!0);setTimeout(()=>{i.value=!1},500);const c=s("1"),l=s("转移登记"),x=s([]),S=s([]),p=s(y.ipagination),_=s(C.ipagination);function R(e){l.value=e.target.value,r(c)=="1"?m():f()}function T(e){p.value.current=e.current,p.value.pageSize=e.pageSize,m()}function h(e){_.value.current=e.current,_.value.pageSize=e.pageSize,f()}function I(e){return+e}function K(e){return e==100?"超时":e+"%"}function N(e){let a=+e;return a>=90&&a<100?"rgb(244, 240, 89)":a>=100?"red":"rgb(16, 142, 233)"}function m(){x.value=y.dataSource.filter(e=>r(l)?e.type==r(l):!0)}function f(){S.value=C.dataSource.filter(e=>r(l)?e.type==r(l):!0)}return m(),f(),(e,a)=>{const g=n("a-radio-button"),D=n("a-radio-group"),w=n("a-table"),B=n("a-tab-pane"),V=n("a-tabs"),P=n("a-card"),E=n("a-col"),F=n("a-row");return v(),k("div",M,[t(A,{class:"enter-y",loading:i.value,type:"bdc"},null,8,["loading"]),t(H,{class:"!my-4 enter-y",loading:i.value},null,8,["loading"]),t(F,null,{default:o(()=>[t(E,{span:24},{default:o(()=>[t(P,{loading:i.value,class:q({"anty-list-cust":!0}),bordered:!1},{default:o(()=>[t(V,{activeKey:c.value,"onUpdate:activeKey":a[1]||(a[1]=u=>c.value=u),size:"large","tab-bar-style":{marginBottom:"24px",paddingLeft:"16px"}},{rightExtra:o(()=>[$("div",O,[t(D,{value:l.value,"onUpdate:value":a[0]||(a[0]=u=>l.value=u),onChange:R},{default:o(()=>[t(g,{value:"转移登记"},{default:o(()=>a[2]||(a[2]=[b("转移登记")])),_:1,__:[2]}),t(g,{value:"抵押登记"},{default:o(()=>a[3]||(a[3]=[b("抵押登记")])),_:1,__:[3]}),t(g,{value:""},{default:o(()=>a[4]||(a[4]=[b("所有")])),_:1,__:[4]})]),_:1},8,["value"])])]),default:o(()=>[t(B,{tab:"业务流程限时监管",key:"1"},{default:o(()=>[t(w,{dataSource:x.value,size:"default",rowKey:"reBizCode",columns:r(y).columns,pagination:p.value,onChange:T},{bodyCell:o(({column:u,record:d})=>[u.dataIndex==="flowRate"?(v(),G(r(j),{key:0,strokeColor:N(d.flowRate),format:K,percent:I(d.flowRate),style:{width:"80px"}},null,8,["strokeColor","percent"])):z("",!0)]),_:1},8,["dataSource","columns","pagination"])]),_:1}),t(B,{loading:"true",tab:"业务节点限时监管",key:"2"},{default:o(()=>[t(w,{dataSource:S.value,size:"default",rowKey:"reBizCode",columns:r(C).columns,pagination:_.value,onChange:h},{bodyCell:o(({column:u,record:d})=>[u.dataIndex==="flowRate"?(v(),k("span",Q,L(d.flowRate)+"小时",1)):z("",!0)]),_:1},8,["dataSource","columns","pagination"])]),_:1})]),_:1},8,["activeKey"])]),_:1},8,["loading"])]),_:1})]),_:1})])}}}),_e=J(W,[["__scopeId","data-v-30283516"]]);export{_e as default};
