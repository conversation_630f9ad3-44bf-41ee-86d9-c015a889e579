<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!-- 工具栏按钮 -->
      <template #toolbar>
        <!-- <a-button class="setting" type="primary" @click="handleImport">
          <template #icon><upload-outlined /></template>
          导入
        </a-button> -->
        <j-upload-button type="primary" v-auth="'hgy.waste:hgy_industry_info:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls"
          >导入</j-upload-button
        >
        <a-button @click="onExportXls" preIcon="ant-design:export-outlined" v-auth="'hgy.waste:hgy_industry_info:exportXls'"> 导出 </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <!-- <template v-slot:bodyCell="{ column, record, index, text }"> </template> -->
    </BasicTable>
    <!-- 表单区域 -->
    <HgyIndustryInfoModal @register="registerModal" @success="handleSuccess"></HgyIndustryInfoModal>
  </div>
</template>

<script lang="ts" name="hgy.waste-hgyIndustryInfo" setup>
  import { reactive } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import HgyIndustryInfoModal from './components/HgyIndustryInfoModal.vue';
  import { columns, searchFormSchema } from './HgyIndustryInfo.data';
  import { list, deleteOne, getImportUrl, getExportUrl } from './HgyIndustryInfo.api';
  const queryParam = reactive<any>({});
  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册table数据
  const { tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      api: list,
      columns,
      canResize: true,
      bordered: false,
      showTableSetting: false,
      striped: true,
      inset: true,
      maxHeight: 450,
      formConfig: {
        //labelWidth: 120,
        labelWidth: 64,
        // 设置表单整体大小，可选值："default" | "small" | "large" | undefined”
        size: 'large',
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [['infoDate', ['infoDate_begin', 'infoDate_end'], 'YYYY-MM-DD']],
        // 操作按钮列配置，span设置为2让按钮更靠右
        actionColOptions: {
          span: 1,
          style: { textAlign: 'right' },
        },
      },
      // 操作栏
      actionColumn: {
        width: 180,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '行业资讯表',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }] = tableContext;
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record: Recordable) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record: Recordable) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'hgy.waste:hgy_industry_info:edit',
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'hgy.waste:hgy_industry_info:delete',
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record: Recordable) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-table-title) {
    margin-top: 12px;
    border-top: 1px solid #ddd;
    padding: 0 !important;
    padding-top: 15px !important;
    margin-right: -8px;
  }

  :deep(.ant-form-item-control-input-content) {
    /* display: flex;
    justify-content: end; */
    button {
      margin-right: 0;
      margin-left: 8px;
      box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
      height: 40px;
    }
  }
</style>
