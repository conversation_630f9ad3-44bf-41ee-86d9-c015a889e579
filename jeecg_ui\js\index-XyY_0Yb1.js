var R=Object.defineProperty,v=Object.defineProperties;var N=Object.getOwnPropertyDescriptors;var h=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable;var T=(r,e,t)=>e in r?R(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,_=(r,e)=>{for(var t in e||(e={}))C.call(e,t)&&T(r,t,e[t]);if(h)for(var t of h(e))F.call(e,t)&&T(r,t,e[t]);return r},y=(r,e)=>v(r,N(e));var w=(r,e,t)=>new Promise((d,c)=>{var l=n=>{try{m(t.next(n))}catch(p){c(p)}},u=n=>{try{m(t.throw(n))}catch(p){c(p)}},m=n=>n.done?d(n.value):Promise.resolve(n.value).then(l,u);m((t=t.apply(r,e)).next())});import{d as b,ag as S,aq as j,ar as P,k as x,aD as g,G as Y,au as I,at as z,u as X}from"./vue-vendor-dy9k-Yad.js";import{u as q}from"./index-BkGZ5fiW.js";import{aX as V,a as B}from"./index-CCWaWN5g.js";import A from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const G={class:"p-4"},H={class:"font-medium"},L=b({name:"AccountInventory"}),E=b(y(_({},L),{setup(r){const e=[{title:"交易单号",dataIndex:"transactionId",width:180},{title:"类型",dataIndex:"type",width:80,slots:{customRender:"type"}},{title:"项目",dataIndex:"project",width:200,ellipsis:!0},{title:"支付方式",dataIndex:"paymentMethod",width:120},{title:"金额",dataIndex:"amount",width:120,slots:{customRender:"amount"}},{title:"状态",dataIndex:"status",width:100,slots:{customRender:"status"}},{title:"时间",dataIndex:"createTime",width:160,customRender:({text:o})=>V(o)}];function t(o){return`¥${o.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})}`}function d(o){return{success:"green",pending:"orange",failed:"red"}[o]||"default"}function c(o){return{success:"成功",pending:"处理中",failed:"失败"}[o]||"未知"}function l(o){return w(this,null,function*(){const a=[{id:1,transactionId:"TXN202412090001",type:"XX特种钢",project:"资产处置收入",paymentMethod:"银行转账",amount:5e4,status:"success",createTime:"2024-12-09 10:30:00"},{id:2,transactionId:"TXN202412090002",type:"XX特种钢",project:"委托服务费",paymentMethod:"在线支付",amount:2500,status:"success",createTime:"2024-12-09 09:15:00"},{id:3,transactionId:"TXN202412080001",type:"XX特种钢",project:"竞价保证金",paymentMethod:"银行转账",amount:1e4,status:"pending",createTime:"2024-12-08 16:45:00"},{id:4,transactionId:"TXN202412080002",type:"XX特种钢",project:"平台服务费",paymentMethod:"余额支付",amount:1200,status:"success",createTime:"2024-12-08 14:20:00"},{id:5,transactionId:"TXN202412070001",type:"XX特种钢",project:"资产评估收入",paymentMethod:"银行转账",amount:8e3,status:"failed",createTime:"2024-12-07 11:30:00"}],{current:s=1,size:i=10}=o,f=(s-1)*i,M=f+i,k=a.slice(f,M);return Promise.resolve({records:k,total:a.length,current:s,size:i})})}const u=[{label:"今天",value:"today"},{label:"近7日",value:"week"},{label:"近1月",value:"month"}];function m(o){const a=new Date;let s,i=new Date(a);switch(o){case"today":s=new Date(a.getFullYear(),a.getMonth(),a.getDate());break;case"week":s=new Date(a.getTime()-7*24*60*60*1e3);break;case"month":s=new Date(a.getFullYear(),a.getMonth()-1,a.getDate());break;default:return}D().setFieldsValue({timeRange:[s,i]}),p()}const[n,{reload:p,getForm:D}]=q({api:l,columns:e,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,inset:!0,formConfig:{labelWidth:80,size:"large",schemas:[{field:"quickTime",label:"",component:"RadioGroup",componentProps:{options:u,onChange:o=>m(o.target.value),optionType:"button",buttonStyle:"solid",size:"small"},colProps:{span:6}},{field:"timeRange",label:"时间区间",component:"RangePicker",componentProps:{showTime:!0,format:"YYYY-MM-DD HH:mm:ss"},colProps:{span:12}}]}});return(o,a)=>{const s=S("a-tag");return P(),j("div",G,[x(X(A),{onRegister:X(n)},{amount:g(({text:i})=>[z("span",H,I(t(i)),1)]),status:g(({record:i})=>[x(s,{color:d(i.status)},{default:g(()=>[Y(I(c(i.status)),1)]),_:2},1032,["color"])]),_:1},8,["onRegister"])])}}})),$t=B(E,[["__scopeId","data-v-5c4ff650"]]);export{$t as default};
