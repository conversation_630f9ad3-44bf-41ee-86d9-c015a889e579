import{u as i,j as n}from"./index-CCWaWN5g.js";const d=[{title:"资产名称",align:"center",dataIndex:"assetName"},{title:"资产类型",align:"center",dataIndex:"assetType"},{title:"资产数量",align:"center",dataIndex:"quantity"},{title:"计量单位",align:"center",dataIndex:"unit"},{title:"使用年限",align:"center",dataIndex:"serviceLife"},{title:"折旧程度(01-九成新 02-八成新...)",align:"center",dataIndex:"depreciationDegree_dictText"},{title:"当前状态(01-在用 02-闲置 03-报废)",align:"center",dataIndex:"currentStatus_dictText"},{title:"评估价值",align:"center",dataIndex:"appraisalValue"},{title:"处置底价",align:"center",dataIndex:"disposalPrice"},{title:"处置开始时间",align:"center",dataIndex:"disposalStartTime"},{title:"处置结束时间",align:"center",dataIndex:"disposalEndTime"},{title:"省份",align:"center",dataIndex:"province"},{title:"城市",align:"center",dataIndex:"city"},{title:"区县",align:"center",dataIndex:"district"},{title:"详细地址",align:"center",dataIndex:"address"},{title:"付款方式(01-全款 02-分期)",align:"center",dataIndex:"paymentMethod_dictText"},{title:"是否含税(0-否 1-是)",align:"center",dataIndex:"isTaxIncluded_dictText"},{title:"特殊说明",align:"center",dataIndex:"specialNotes"},{title:"创建时间",align:"center",dataIndex:"createTime"},{title:"更新时间",align:"center",dataIndex:"updateTime"},{title:"创建人",align:"center",dataIndex:"createBy"},{title:"更新人",align:"center",dataIndex:"updateBy"}],o=[{label:"资产名称",field:"assetName",component:"JInput"}],a=[{label:"资产名称",field:"assetName",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入资产名称!"}]},{label:"资产类型",field:"assetType",component:"InputNumber"},{label:"资产数量",field:"quantity",component:"InputNumber"},{label:"计量单位",field:"unit",component:"Input"},{label:"使用年限",field:"serviceLife",component:"InputNumber"},{label:"折旧程度(01-九成新 02-八成新...)",field:"depreciationDegree",component:"JDictSelectTag",componentProps:{dictCode:""}},{label:"当前状态(01-在用 02-闲置 03-报废)",field:"currentStatus",component:"JDictSelectTag",componentProps:{dictCode:""}},{label:"评估价值",field:"appraisalValue",component:"InputNumber"},{label:"处置底价",field:"disposalPrice",component:"InputNumber"},{label:"处置开始时间",field:"disposalStartTime",component:"DatePicker",componentProps:{showTime:!0,valueFormat:"YYYY-MM-DD HH:mm:ss"}},{label:"处置结束时间",field:"disposalEndTime",component:"DatePicker",componentProps:{showTime:!0,valueFormat:"YYYY-MM-DD HH:mm:ss"}},{label:"省份",field:"province",component:"Input"},{label:"城市",field:"city",component:"Input"},{label:"区县",field:"district",component:"Input"},{label:"详细地址",field:"address",component:"Input"},{label:"付款方式(01-全款 02-分期)",field:"paymentMethod",component:"JDictSelectTag",componentProps:{dictCode:""}},{label:"是否含税(0-否 1-是)",field:"isTaxIncluded",component:"JDictSelectTag",componentProps:{dictCode:""}},{label:"特殊说明",field:"specialNotes",component:"Input"},{label:"",field:"id",component:"Input",show:!1}],c={assetName:{title:"资产名称",order:0,view:"text",type:"string"},assetType:{title:"资产类型",order:1,view:"number",type:"number"},quantity:{title:"资产数量",order:2,view:"number",type:"number"},unit:{title:"计量单位",order:3,view:"text",type:"string"},serviceLife:{title:"使用年限",order:4,view:"number",type:"number"},depreciationDegree:{title:"折旧程度(01-九成新 02-八成新...)",order:5,view:"number",type:"number",dictCode:""},currentStatus:{title:"当前状态(01-在用 02-闲置 03-报废)",order:6,view:"number",type:"number",dictCode:""},appraisalValue:{title:"评估价值",order:7,view:"number",type:"number"},disposalPrice:{title:"处置底价",order:8,view:"number",type:"number"},disposalStartTime:{title:"处置开始时间",order:9,view:"datetime",type:"string"},disposalEndTime:{title:"处置结束时间",order:10,view:"datetime",type:"string"},province:{title:"省份",order:11,view:"link_down",type:"string"},city:{title:"城市",order:12,view:"text",type:"string"},district:{title:"区县",order:13,view:"text",type:"string"},address:{title:"详细地址",order:14,view:"text",type:"string"},paymentMethod:{title:"付款方式(01-全款 02-分期)",order:15,view:"number",type:"number",dictCode:""},isTaxIncluded:{title:"是否含税(0-否 1-是)",order:16,view:"number",type:"number",dictCode:""},specialNotes:{title:"特殊说明",order:17,view:"text",type:"string"},createTime:{title:"创建时间",order:18,view:"datetime",type:"string"},updateTime:{title:"更新时间",order:19,view:"datetime",type:"string"},createBy:{title:"创建人",order:21,view:"text",type:"string"},updateBy:{title:"更新人",order:22,view:"text",type:"string"}};function p(e){return a}const{createConfirm:l}=i();const u="/hgy/entrustService/hgyAssetEntrust/exportXls",m="/hgy/entrustService/hgyAssetEntrust/importExcel",g=e=>n.get({url:"/hgy/entrustService/hgyAssetEntrust/list",params:e}),y=(e,t)=>n.delete({url:"/hgy/entrustService/hgyAssetEntrust/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),h=(e,t)=>{l({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>n.delete({url:"/hgy/entrustService/hgyAssetEntrust/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},v=(e,t)=>{let r=t?"/hgy/entrustService/hgyAssetEntrust/edit":"/hgy/entrustService/hgyAssetEntrust/add";return n.post({url:r,params:e})};export{u as a,h as b,o as c,d,y as e,v as f,m as g,a as h,p as i,g as l,c as s};
