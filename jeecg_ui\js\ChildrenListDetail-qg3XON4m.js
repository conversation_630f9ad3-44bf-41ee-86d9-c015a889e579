import{d as e,ag as r,aB as a,ar as p,aD as n,at as s}from"./vue-vendor-dy9k-Yad.js";import{P as i}from"./index-CtJ0w2CP.js";import{a as m}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const c=e({components:{PageWrapper:i}});function _(d,t,f,l,u,g){const o=r("PageWrapper");return p(),a(o,{title:"子级详情页"},{default:n(()=>t[0]||(t[0]=[s("div",null," 子级详情页内容在此 ",-1)])),_:1,__:[0]})}const w=m(c,[["render",_]]);export{w as default};
