import{d as X,f as l,r as Q,J as Y,u as d,n as Z,o as oo,ag as L,aq as I,ar as N,F as eo,ah as q,q as g,k as n,at as e,au as A,B as v,aD as f,A as to,G as no}from"./vue-vendor-dy9k-Yad.js";import io from"./AppLoginHeader-4432c584-CwU9kapN.js";import ro from"./AppRegister-79a62c59-juDRGxlb.js";import lo from"./AccountLoginForm-84cbd344-BmZ9aeg7.js";import ao from"./PhoneLoginForm-86437a9d-y3jEDeuN.js";import so from"./AppForgetPassword-52443f23-Bt5D_T5M.js";import{cs as uo,bn as co,ah as po,N as mo,u as go,r as D,bN as vo}from"./index-CCWaWN5g.js";import{bF as fo,bt as ho}from"./antd-vue-vendor-me9YkNVC.js";import yo from"./AppTenant-f0bef7ca-D1RYXh8S.js";import Lo from"./AppThirdForm-fd8b4604-BrgjIxkz.js";import"./AppNameEmail-3303a037-DZzNwvXO.js";import"./CaptchaModal-CFhSDiLW.js";import"./index-Diw57m_E.js";import"./checkcode-DLY3GIII.js";import"./index-DFrpKMGa.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";var B=(R,x,c)=>new Promise((u,p)=>{var a=s=>{try{m(c.next(s))}catch(r){p(r)}},w=s=>{try{m(c.throw(s))}catch(r){p(r)}},m=s=>s.done?u(s.value):Promise.resolve(s.value).then(a,w);m((c=c.apply(R,x)).next())});const wo={key:0,class:"login-box"},To={class:"login-subject"},ko={class:"flex-row align-items-center margin-top10"},_o={class:"login-title"},bo={class:"content-box"},Co={key:0,class:"text-center"},Ao={class:"login-other"},Bo={style:{width:"100%",display:"flex"}},xo={class:"aui-third-login"},So={class:"login-language"},Io=X({__name:"AppLogin",setup(R){const{changeLocale:x,getLocale:c}=co(),u=l(),p=po(),a=l("accountLogin");fo({scriptUrl:"//at.alicdn.com/t/font_2316098_umqusozousr.js"});const w=l(),m=l(),s=l(),{t:r}=mo(),i=l("login");l(),Q({phone:"",smscode:""});const{notification:U,createErrorModal:No}=go(),T=l("zh_CN"),k=l(!1),h=l(!1);function V(){i.value="register",setTimeout(()=>{w.value.clearValidate()},300)}function E(){i.value="login",a.value="accountLogin"}function P(o){i.value="login",a.value="accountLogin",setTimeout(()=>{m.value.setAccountData(o)},300)}function F(o){a.value=o}function $(){i.value="forgetPwd"}function S(o){return B(this,null,function*(){var t,_;const b=Y(p.getLoginInfo),{tenantList:y}=b;if(!y||y.length===0)i.value="tenant";else{U.success({message:r("sys.login.loginSuccessTitle"),description:`${r("sys.login.loginSuccessDesc")}: ${o}`,duration:3});let C=(_=(t=D.currentRoute.value)==null?void 0:t.query)==null?void 0:_.redirect;if(C){window.open(decodeURIComponent(C),"_self");return}yield D.replace(p.getUserInfo&&p.getUserInfo.homePath||vo.BASE_HOME),z()}})}function z(){window.location.reload()}function j(o){u.value.onThirdLogin(o)}function G(o){return B(this,null,function*(){yield x(o)})}function H({key:o}){T.value=o,d(c)!==o&&G(o)}function J(o){Z(()=>{o.loginType!="thirdLogin"&&(k.value=!0,u.value.hideBindThirdAccount()),o.loginType==="login"&&(a.value="phoneLogin"),i.value=o.loginType})}function O(o){return B(this,null,function*(){h.value=!0,yield u.value.bindThirdAccount(o),h.value=!1})}function W(o){return B(this,null,function*(){h.value=!0,yield u.value.createAccountBindThird(o),h.value=!1})}return oo(()=>{let o=document.getElementById("app");o.style.height="auto";let t=document.getElementsByTagName("body")[0].style;t.backgroundColor="#f2f5f7"}),(o,t)=>{const _=L("a-spin"),b=L("Icon"),y=L("a-menu-item"),C=L("a-menu"),K=L("a-dropdown");return N(),I(eo,null,[i.value==="login"?(N(),I("div",wo,[e("div",To,[n(io),e("div",ko,[e("div",_o,A(a.value==="accountLogin"?d(r)("sys.login.signInFormTitle"):d(r)("sys.login.mobileSignInFormTitle")),1)]),e("div",bo,[g(e("div",null,[n(lo,{ref_key:"accountLoginRef",ref:m,onLogin:F,onForgetPwd:$,onLoginSuccess:S},null,512)],512),[[v,a.value==="accountLogin"]]),g(e("div",null,[n(ao,{onLogin:F,onLoginSuccess:S,bindThirdAccount:k.value,onBindThirdPhone:O},null,8,["bindThirdAccount"])],512),[[v,a.value==="phoneLogin"]]),k.value?q("",!0):(N(),I("div",Co,[e("div",Ao,A(d(r)("sys.login.otherSignIn")),1),e("div",Bo,[e("div",xo,[e("a",{title:"微信",onClick:t[0]||(t[0]=M=>j("wechat_open"))},[n(d(ho),{style:{color:"rgb(75, 176, 79)"}})])])]),t[3]||(t[3]=e("div",{class:"line"},null,-1)),e("div",{class:"register-account pointer",onClick:V},A(d(r)("sys.login.registerButton")),1)]))])])])):q("",!0),g(e("div",null,[n(_,{spinning:h.value},{default:f(()=>[n(ro,{ref_key:"appRegisterRef",ref:w,onReturnLogin:E,bindThirdAccount:k.value,onLoginAccount:P,onBindThirdAccount:W},null,8,["bindThirdAccount"])]),_:1},8,["spinning"])],512),[[v,i.value==="register"]]),g(e("div",null,[n(so,{ref_key:"appForgetPwdRef",ref:s,onReturnLogin:E,onLoginAccount:P},null,512)],512),[[v,i.value==="forgetPwd"]]),g(e("div",null,[n(yo,{onSuccess:z})],512),[[v,i.value==="tenant"]]),g(e("div",So,[n(b,{icon:"ant-design:global-outlined",style:{"font-size":"13px",color:"#9e9e9e"}}),n(K,{trigger:["click"],placement:"top"},{overlay:f(()=>[n(C,{value:T.value,"onUpdate:value":t[2]||(t[2]=M=>T.value=M),onClick:H},{default:f(()=>[n(y,{key:"zh_CN"},{default:f(()=>t[4]||(t[4]=[e("span",null,"简体中文",-1)])),_:1}),n(y,{key:"en"},{default:f(()=>t[5]||(t[5]=[e("span",null,"English",-1)])),_:1})]),_:1},8,["value"])]),default:f(()=>[e("span",{class:"language-drop pointer",onClick:t[1]||(t[1]=to(()=>{},["prevent"]))},[no(A(T.value==="zh_CN"?"CN":"EN")+" ",1),n(b,{icon:"ant-design:down-outlined",style:{color:"#9e9e9e","font-size":"13px"}})])]),_:1})],512),[[v,i.value!=="thirdLogin"]]),n(Lo,{ref_key:"thirdModalRef",ref:u,onType:J,onLoginSuccess:S},null,512)],64)}}}),$e=uo(Io,[["__scopeId","data-v-1103e34e"]]);export{$e as default};
