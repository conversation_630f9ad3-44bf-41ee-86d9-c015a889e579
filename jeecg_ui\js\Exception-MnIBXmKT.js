import{d as y,f as O,aN as R,e as f,u as s,k as r}from"./vue-vendor-dy9k-Yad.js";import{B as S,a2 as A}from"./antd-vue-vendor-me9YkNVC.js";import{aV as e,bu as k,bM as G,F as D,N as P,bN as d}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const C="/assets/no-data-BKo151DF.svg",h="/assets/net-error-sXkbaH3y.svg",L=y({name:"ErrorPage",props:{status:{type:Number,default:e.PAGE_NOT_FOUND},title:{type:String,default:""},subTitle:{type:String,default:""},full:{type:Boolean,default:!1}},setup(n){const a=O(new Map),{query:p}=R(),o=k(),c=G(),{t}=P(),{prefixCls:E}=D("app-exception-page"),x=f(()=>{const{status:l}=p,{status:i}=n;return Number(l)||i}),N=f(()=>s(a).get(s(x))),b=t("sys.exception.backLogin"),u=t("sys.exception.backHome");return s(a).set(e.PAGE_NOT_ACCESS,{title:"403",status:`${e.PAGE_NOT_ACCESS}`,subTitle:t("sys.exception.subTitle403"),btnText:n.full?b:u,handler:()=>n.full?o(d.BASE_LOGIN):o()}),s(a).set(e.PAGE_NOT_FOUND,{title:"404",status:`${e.PAGE_NOT_FOUND}`,subTitle:t("sys.exception.subTitle404"),btnText:n.full?b:u,handler:()=>n.full?o(d.BASE_LOGIN):o()}),s(a).set(e.ERROR,{title:"500",status:`${e.ERROR}`,subTitle:t("sys.exception.subTitle500"),btnText:u,handler:()=>o()}),s(a).set(e.PAGE_NOT_DATA,{title:t("sys.exception.noDataTitle"),subTitle:"",btnText:t("common.redo"),handler:()=>c(),icon:C}),s(a).set(e.NET_WORK_ERROR,{title:t("sys.exception.networkErrorTitle"),subTitle:t("sys.exception.networkErrorSubTitle"),btnText:t("common.redo"),handler:()=>c(),icon:h}),()=>{const{title:l,subTitle:i,btnText:T,icon:m,handler:_,status:g}=s(N)||{};return r(A,{class:E,status:g,title:n.title||l,"sub-title":n.subTitle||i},{extra:()=>T&&r(S,{type:"primary",onClick:_},{default:()=>T}),icon:()=>m?r("img",{src:m},null):null})}}});export{L as default};
