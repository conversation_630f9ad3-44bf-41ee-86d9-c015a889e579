import{d as M,f as i,e as S,w as A,ag as v,aq as b,ar as y,aB as I,F as q,at as F,k,aD as C,G as K}from"./vue-vendor-dy9k-Yad.js";import{cs as T,u as V}from"./index-CCWaWN5g.js";import{C as $}from"./auth.data-626c5083-DVuUJlaU.js";import{b as z,B as J,L as O}from"./auth.api-53df4c33-CWNFk1-w.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var g=(e,t,d)=>new Promise((c,p)=>{var m=r=>{try{n(d.next(r))}catch(l){p(l)}},u=r=>{try{n(d.throw(r))}catch(l){p(l)}},n=r=>r.done?c(r.value):Promise.resolve(r.value).then(m,u);n((d=d.apply(e,t)).next())});const R=M({name:"AuthButtonTree",props:{cgformId:{type:String,required:!0}},setup(e){const{createMessage:t,createSuccessModal:d}=V(),c=i(""),p=i(2),m=i(!0),u=i([]),n=i([]),r=i([]),l=i(""),E=S(()=>!c.value);A(()=>e.cgformId,h,{immediate:!0});function h(){return g(this,null,function*(){if(!e.cgformId)return;let a=yield z(e.cgformId,p.value);a.forEach(s=>{for(const f of $)if(s.code==f.code){s.title||(s.title=f.title);break}});let o=[];for(let s of a){let f=P(s);o.push({key:s.id,title:f})}r.value=o})}function B(){h(),x(c.value,l.value)}function x(a,o){return g(this,null,function*(){c.value=a,l.value=o,n.value=[],yield h();let s=yield J({roleId:a,cgformId:e.cgformId,type:p.value,authMode:o});n.value=s.map(f=>f.authId)})}function D(){c.value="",h()}function N(){return g(this,null,function*(){try{const{success:a,message:o,result:s}=yield O(c.value,e.cgformId,{authId:JSON.stringify(n.value),authMode:l.value});a?Array.isArray(s==null?void 0:s.disabledNames)?d({title:"保存成功",content:`由于以下按钮未激活，所以权限未生效。<br>${s.disabledNames.join("<br>")}`}):t.success("保存成功"):t.error(o)}catch(a){t.error("保存出现异常")}})}function P(a){let o=a.title+"-";return a.code&&a.code.includes("form_sub")?o+="表单可见（附表）":a.page==3?o+="列表可见":a.page==5&&(o+="表单可见"),o}function _(a){u.value=a,m.value=!1}function w(){c.value="",n.value=[]}return{loadChecked:x,clear:w,expandedKeys:u,autoExpandParent:m,checkedKeys:n,treeData:r,disabled:E,onSave:N,onExpand:_,onRefresh:B,clearChecked:D}}}),j={class:"onl-auth-tree-btns"};function G(e,t,d,c,p,m){const u=v("a-empty"),n=v("a-button"),r=v("a-tree");return y(),b("div",null,[e.disabled?(y(),I(u,{key:0,description:"请先选中左侧角色/部门/用户"})):e.treeData.length===0?(y(),I(u,{key:1,description:"无权限信息"})):(y(),b(q,{key:2},[F("div",j,[k(n,{onClick:e.onRefresh,size:"small",type:"primary",preIcon:"ant-design:redo",ghost:""},{default:C(()=>t[1]||(t[1]=[K("刷新")])),_:1},8,["onClick"]),k(n,{onClick:e.onSave,size:"small",type:"primary",preIcon:"ant-design:save",ghost:""},{default:C(()=>t[2]||(t[2]=[K("保存")])),_:1},8,["onClick"])]),k(r,{checkable:"",checkedKeys:e.checkedKeys,"onUpdate:checkedKeys":t[0]||(t[0]=l=>e.checkedKeys=l),expandedKeys:e.expandedKeys,autoExpandParent:e.autoExpandParent,treeData:e.treeData,onExpand:e.onExpand},null,8,["checkedKeys","expandedKeys","autoExpandParent","treeData","onExpand"])],64))])}const Y=T(R,[["render",G],["__scopeId","data-v-b62449eb"]]);export{Y as default};
