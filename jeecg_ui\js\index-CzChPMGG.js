var O=Object.defineProperty,B=Object.defineProperties;var S=Object.getOwnPropertyDescriptors;var C=Object.getOwnPropertySymbols;var h=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable;var w=(t,e,a)=>e in t?O(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,I=(t,e)=>{for(var a in e||(e={}))h.call(e,a)&&w(t,a,e[a]);if(C)for(var a of C(e))x.call(e,a)&&w(t,a,e[a]);return t},V=(t,e)=>B(t,S(e));import{d as D,r as N,h as P,e as g,I as W,ag as r,aB as _,ar as v,aD as l,at as s,k as i,G as p,au as u,aq as L,F as A,aC as $}from"./vue-vendor-dy9k-Yad.js";import{X as E,j as T}from"./antd-vue-vendor-me9YkNVC.js";import{P as F}from"./index-CtJ0w2CP.js";import{bc as M,aX as R,a as U}from"./index-CCWaWN5g.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const X=D({components:{PageWrapper:F,[T.name]:T,InputTextArea:T.TextArea,Tag:E},setup(){const t=N({server:"ws://localhost:3300/test",sendValue:"",recordList:[]}),{status:e,data:a,send:b,close:k,open:y}=M(t.server,{autoReconnect:!1,heartbeat:!0});P(()=>{if(a.value)try{const o=JSON.parse(a.value);t.recordList.push(o)}catch(o){t.recordList.push({res:a.value,id:Math.ceil(Math.random()*1e3),time:new Date().getTime()})}});const n=g(()=>e.value==="OPEN"),m=g(()=>n.value?"success":"red"),d=g(()=>[...t.recordList].reverse());function f(){b(t.sendValue),t.sendValue=""}function c(){n.value?k():y()}return V(I({status:e,formatToDateTime:R},W(t)),{handlerSend:f,getList:d,toggle:c,getIsOpen:n,getTagColor:m})}}),j={class:"flex"},q={class:"w-1/3 bg-white p-4"},G={class:"flex items-center"},J={class:"flex"},z={class:"w-2/3 bg-white ml-4 p-4"},H={class:"max-h-80 overflow-auto"},K={class:"flex items-center"};function Q(t,e,a,b,k,y){const n=r("Tag"),m=r("a-input"),d=r("a-button"),f=r("InputTextArea"),c=r("PageWrapper");return v(),_(c,{title:"WebSocket 示例"},{default:l(()=>[s("div",j,[s("div",q,[s("div",G,[e[2]||(e[2]=s("span",{class:"text-lg font-medium mr-4"}," 连接状态: ",-1)),i(n,{color:t.getTagColor},{default:l(()=>[p(u(t.status),1)]),_:1},8,["color"])]),e[5]||(e[5]=s("hr",{class:"my-4"},null,-1)),s("div",J,[i(m,{value:t.server,"onUpdate:value":e[0]||(e[0]=o=>t.server=o),disabled:""},{addonBefore:l(()=>e[3]||(e[3]=[p(" 服务地址 ")])),_:1},8,["value"]),i(d,{type:t.getIsOpen?"danger":"primary",onClick:t.toggle},{default:l(()=>[p(u(t.getIsOpen?"关闭连接":"开启连接"),1)]),_:1},8,["type","onClick"])]),e[6]||(e[6]=s("p",{class:"text-lg font-medium mt-4"},"设置",-1)),e[7]||(e[7]=s("hr",{class:"my-4"},null,-1)),i(f,{placeholder:"需要发送到服务器的内容",disabled:!t.getIsOpen,value:t.sendValue,"onUpdate:value":e[1]||(e[1]=o=>t.sendValue=o),allowClear:""},null,8,["disabled","value"]),i(d,{type:"primary",block:"",class:"mt-4",disabled:!t.getIsOpen,onClick:t.handlerSend},{default:l(()=>e[4]||(e[4]=[p(" 发送 ")])),_:1,__:[4]},8,["disabled","onClick"])]),s("div",z,[e[9]||(e[9]=s("span",{class:"text-lg font-medium mr-4"}," 消息记录: ",-1)),e[10]||(e[10]=s("hr",{class:"my-4"},null,-1)),s("div",H,[s("ul",null,[(v(!0),L(A,null,$(t.getList,o=>(v(),L("li",{class:"mt-2",key:o.time},[s("div",K,[e[8]||(e[8]=s("span",{class:"mr-2 text-primary font-medium"},"收到消息:",-1)),s("span",null,u(t.formatToDateTime(o.time)),1)]),s("div",null,u(o.res),1)]))),128))])])])])]),_:1})}const de=U(X,[["render",Q]]);export{de as default};
