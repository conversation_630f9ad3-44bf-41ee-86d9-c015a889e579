import{e as t,u as e}from"./vue-vendor-dy9k-Yad.js";import{A as O,B as D,C as P,D as k,M as l}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function E(){const{getFullContent:S}=O(),o=D(),a=t(()=>!e(S)&&e(s)&&e(r)&&!e(u)&&!e(g)),p=t(()=>!e(H)&&!e(a)),m=t(()=>{const n=!e(S)&&e(r);return n&&!e(s)||n&&e(u)||n&&e(g)}),{getMenuMode:c,getSplit:d,getShowHeaderTrigger:f,getIsSidebarType:i,getIsMixSidebar:g,getIsTopMenu:u}=P(),{getShowBreadCrumb:h,getShowLogo:M}=k(),s=t(()=>!e(i)&&e(r)),C=t(()=>o.getHeaderSetting.showDoc),F=t(()=>o.getHeaderSetting.theme),r=t(()=>o.getHeaderSetting.show),H=t(()=>o.getHeaderSetting.fixed),T=t(()=>o.getHeaderSetting.bgColor),x=t(()=>o.getHeaderSetting.showSearch),I=t(()=>o.getHeaderSetting.useLockPage),L=t(()=>o.getHeaderSetting.showFullScreen),R=t(()=>o.getHeaderSetting.showNotice),w=t(()=>e(c)!==l.HORIZONTAL&&e(h)&&!e(d)),A=t(()=>e(c)!==l.HORIZONTAL&&!e(h)&&!e(d)),B=t(()=>e(M)&&!e(i)&&!e(g)),b=t(()=>e(w)||e(f));function N(n){o.setProjectConfig({headerSetting:n})}return{setHeaderSetting:N,getShowDoc:C,getShowSearch:x,getHeaderTheme:F,getUseLockPage:I,getShowFullScreen:L,getShowNotice:R,getShowBread:w,getShowContent:b,getShowHeaderLogo:B,getShowHeader:r,getFixed:H,getShowMixHeaderRef:s,getShowFullHeaderRef:a,getShowInsetHeaderRef:m,getUnFixedAndFull:p,getHeaderBgColor:T,getShowBreadTitle:A}}export{E as useHeaderSetting};
