import{d as h,e as S,f as c,ap as T,o as B,ag as l,aB as u,ar as r,aD as n,at as f,as as D,k as K,aq as y,F as $,aC as L,ah as k,aG as V,aQ as w,G as P,au as R}from"./vue-vendor-dy9k-Yad.js";import{a4 as v}from"./antd-vue-vendor-me9YkNVC.js";import{S as A}from"./index-LCGLvkB3.js";import{g as _}from"./UserAccountModal-EpftHlah.js";import F from"./BaseSetting-CZQpNOcN.js";import M from"./AccountSetting-CvaojAWH.js";import N from"./TenantSetting-ZEVdKp8B.js";import q from"./AuthenticationSetting-DWnyfVH8.js";import x from"./WeChatDingSetting-DpMHnaZL.js";import{F as E,D as G,ay as U,a as z}from"./index-CCWaWN5g.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";import"./UserSetting.api-BJ086Ekj.js";import"./index-BPAdgtaT.js";import"./header-OZa5fSDc.js";import"./upload-Dd1Qafin.js";import"./UnsubscribeModal-Ck67K_CM.js";import"./user-Cjsg8yWg.js";import"./UserPhoneModal-CF_2UuDe.js";import"./UserEmailModal-8eAOV2Ud.js";import"./UserPasswordModal-DTQupZFv.js";import"./index-B4ez5KWV.js";const I=h({components:{ScrollContainer:A,Tabs:v,TabPane:v.TabPane,BaseSetting:F,AccountSetting:M,TenantSetting:N,AuthenticationSetting:q,WeChatDingSetting:x},props:{componentList:{type:Array,default:()=>_()}},setup(){const{prefixCls:o}=E("user-account-setting-container"),{getDarkMode:i}=G(),d=S(()=>i.value===U.DARK),a=c("1"),g=c(!1),C=c(""),p=T(),s=S(()=>{const e=_();return g.value?e:e.filter(b=>b.component!="MyVipSetting")});function m(e){a.value=e}function t(){let e=p.currentRoute.value.query;e&&e.page==="tenantSetting"&&(a.value="2")}return B(()=>{t()}),{prefixCls:o,componentList:s,tabBarStyle:{width:"220px",marginBottom:"200px"},componentClick:m,activeKey:a,isDark:d}}}),Q={style:{display:"flex","align-items":"center",cursor:"pointer"}},W={style:{width:"30px"}},j=["src"],H=["src"];function J(o,i,d,a,g,C){const p=l("TabPane"),s=l("Tabs"),m=l("ScrollContainer");return r(),u(m,null,{default:n(()=>[f("div",{ref:"wrapperRef",class:D(["user-account-setting",[o.prefixCls]])},[K(s,{"tab-position":"left",tabBarStyle:o.tabBarStyle,onTabClick:o.componentClick,activeKey:o.activeKey,"onUpdate:activeKey":i[0]||(i[0]=t=>o.activeKey=t)},{default:n(()=>[(r(!0),y($,null,L(o.componentList,t=>(r(),u(p,{key:t.key},{tab:n(()=>[f("span",Q,[f("span",W,[o.activeKey===t.key||o.isDark?(r(),y("img",{key:0,src:t.img2,style:{height:"18px"}},null,8,j)):(r(),y("img",{key:1,src:t.img1,style:{height:"16px"}},null,8,H))]),P(" "+R(t.name),1)])]),default:n(()=>[o.activeKey===t.key&&!t.isSlot?(r(),u(w(t.component),{key:0})):k("",!0),o.activeKey===t.key&&t.isSlot?V(o.$slots,"component",{key:1},void 0,!0):k("",!0)]),_:2},1024))),128))]),_:3},8,["tabBarStyle","onTabClick","activeKey"])],2)]),_:3})}const yo=z(I,[["render",J],["__scopeId","data-v-21e09fec"]]);export{yo as default};
