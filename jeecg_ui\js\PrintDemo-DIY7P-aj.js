import{r as _,f,ag as r,aB as C,ar as y,aD as a,k as t,at as o,G as P,aq as b,ah as k}from"./vue-vendor-dy9k-Yad.js";import{P as V}from"./index-CtJ0w2CP.js";import{c as j,a as I}from"./index-CCWaWN5g.js";import{printJS as B}from"./usePrintJS-aLidQTiU.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./print-CZHAHgn6.js";const L={name:"PrintDemo",components:{PageWrapper:V,Icon:j},props:{reBizCode:{type:String,default:""}},setup(){const d=_({printer:"张三",printTime:"2021-12-31 23:59:59",printContent:"打印内容：这是一个打印测试！",printReason:"做一个打印测试",fileList:[{uid:"-1",name:"xxx.png",status:"done",url:"https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"},{uid:"-2",name:"pic1.png",status:"done",url:"https://www.gizbot.com/img/2016/11/whatsapp-error-lead-image-08-1478607387.jpg"}]}),n=f(""),m=f(!1);function l(){B({printable:"#printContent",type:"html"})}function u(s){n.value=s.url||s.thumbUrl,m.value=!0}function v({fileList:s}){d.fileList=s}return{model:d,previewImage:n,previewVisible:m,onPrint:l,handlePreview:u,handleChange:v}}},T={style:{"text-align":"right"}},z={ref:"print",id:"printContent"},N={class:"sign",style:{"text-align":"center",height:"inherit"}},W={key:0},D=["src"];function E(d,n,m,l,u,v){const s=r("a-button"),p=r("a-input"),i=r("a-col"),g=r("Icon"),w=r("a-upload"),x=r("a-modal"),R=r("a-card"),U=r("PageWrapper");return y(),C(U,null,{default:a(()=>[t(R,{bordered:!1,class:"j-print-demo"},{default:a(()=>[o("div",T,[t(s,{type:"primary",ghost:"",onClick:l.onPrint},{default:a(()=>n[18]||(n[18]=[P("打印")])),_:1,__:[18]},8,["onClick"])]),o("section",z,[n[39]||(n[39]=o("div",{style:{"text-align":"center"}},[o("p",{style:{"font-size":"24px","font-weight":"800"}},"打印测试表单")],-1)),t(i,{md:24,sm:24},{default:a(()=>[o("div",N,[t(i,{span:24},{default:a(()=>[n[19]||(n[19]=o("span",null,"打印人员:",-1)),t(p,{style:{width:"30%"},value:l.model.printer,"onUpdate:value":n[0]||(n[0]=e=>l.model.printer=e)},null,8,["value"]),n[20]||(n[20]=o("span",{style:{"margin-left":"12.5%"}},"打印日期:",-1)),t(p,{style:{width:"30%"},value:l.model.printTime,"onUpdate:value":n[1]||(n[1]=e=>l.model.printTime=e)},null,8,["value"])]),_:1,__:[19,20]}),t(i,{span:24}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[21]||(n[21]=o("span",null,"打印内容:",-1)),t(p,{style:{width:"80%"},value:l.model.printContent,"onUpdate:value":n[2]||(n[2]=e=>l.model.printContent=e)},null,8,["value"])]),_:1,__:[21]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[22]||(n[22]=o("span",null,"打印目的1:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[3]||(n[3]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[22]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[23]||(n[23]=o("span",null,"打印目的2:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[4]||(n[4]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[23]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[24]||(n[24]=o("span",null,"打印目的3:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[5]||(n[5]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[24]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[25]||(n[25]=o("span",null,"打印目的4:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[6]||(n[6]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[25]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[26]||(n[26]=o("span",null,"打印目的5:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[7]||(n[7]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[26]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[27]||(n[27]=o("span",null,"打印目的6:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[8]||(n[8]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[27]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[28]||(n[28]=o("span",null,"打印目的7:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[9]||(n[9]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[28]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[29]||(n[29]=o("span",null,"打印目的8:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[10]||(n[10]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[29]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[30]||(n[30]=o("span",null,"打印目的9:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[11]||(n[11]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[30]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[31]||(n[31]=o("span",null,"打印目的10:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[12]||(n[12]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[31]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[32]||(n[32]=o("span",null,"打印目的11:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[13]||(n[13]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[32]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[33]||(n[33]=o("span",null,"打印目的12:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[14]||(n[14]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[33]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[34]||(n[34]=o("span",null,"打印目的13:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[15]||(n[15]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[34]}),t(i,{span:24,style:{"margin-top":"20px"}},{default:a(()=>[n[35]||(n[35]=o("span",null,"打印目的14:",-1)),t(p,{style:{width:"80%"},value:l.model.printReason,"onUpdate:value":n[16]||(n[16]=e=>l.model.printReason=e)},null,8,["value"])]),_:1,__:[35]}),t(i,{style:{"margin-top":"20px"},span:24},{default:a(()=>[n[37]||(n[37]=o("span",null,"打印图片:",-1)),n[38]||(n[38]=o("br",null,null,-1)),t(w,{action:"/jsonplaceholder.typicode.com/posts/",listType:"picture-card",fileList:l.model.fileList,onPreview:l.handlePreview,onChange:l.handleChange},{default:a(()=>[l.model.fileList.length<3?(y(),b("div",W,[t(g,{icon:"ant-design:plus-outlined"}),n[36]||(n[36]=o("div",{class:"ant-upload-text"},"Upload",-1))])):k("",!0)]),_:1},8,["fileList","onPreview","onChange"]),t(x,{open:l.previewVisible,footer:null,onCancel:n[17]||(n[17]=e=>l.previewVisible=!1)},{default:a(()=>[o("img",{alt:"example",style:{width:"100%"},src:l.previewImage},null,8,D)]),_:1},8,["open"])]),_:1,__:[37,38]})])]),_:1})],512)]),_:1})]),_:1})}const X=I(L,[["render",E],["__scopeId","data-v-1d9a2db6"]]);export{X as default};
