import{d as a,ag as s,aB as i,ar as p,aE as n,aD as r,at as m,G as c}from"./vue-vendor-dy9k-Yad.js";import{B as l}from"./index-JbqXEynz.js";import{a as f}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";const d=a({components:{BasicDrawer:l}});function B(o,t,_,D,u,w){const e=s("BasicDrawer");return p(),i(e,n(o.$attrs,{isDetail:!0,title:"Drawer Title5"}),{titleToolbar:r(()=>t[0]||(t[0]=[c(" toolbar ")])),default:r(()=>[t[1]||(t[1]=m("p",{class:"h-20"}," Content Message ",-1))]),_:1,__:[1]},16)}const G=f(d,[["render",B]]);export{G as default};
