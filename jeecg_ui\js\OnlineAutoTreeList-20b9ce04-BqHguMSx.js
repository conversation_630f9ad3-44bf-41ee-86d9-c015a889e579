import"./index-BkGZ5fiW.js";import{cs as ze,u as je,j as $e}from"./index-CCWaWN5g.js";import Ue from"./OnlineAutoModal-95f46901-Co1FfZLa.js";import We from"./OnlineDetailModal-5b412bb9-C14CjZpZ.js";import Ge from"./OnlineCustomModal-c8b1e780-DLSsFULK.js";import{f as J,w as Ze,ag as s,aq as m,ar as i,aB as d,ah as g,q as N,k as y,B as V,as as Xe,aD as c,at as T,au as b,G as O,F as z,aC as Ye,aE as et,aP as tt,aQ as ot,aJ as nt,aK as rt}from"./vue-vendor-dy9k-Yad.js";import{a as at}from"./JImportModal-BnQ3nPZC.js";import{d as it,m as lt}from"./useListButton-98908683-Bu4g4Tt-.js";import{u as ct,a as st,g as dt}from"./useExtendComponent-bb98e568-B7LlULaY.js";import pt from"./OnlineQueryForm-9248341f-Do5iwEgi.js";import ut from"./SuperQuery-46032e66-Dg-mJkkb.js";import{a as j}from"./constant-fa63bd66-Ddbq-fz2.js";import"./index-Diw57m_E.js";import"./OnlineForm-58282699-DaLYL1I2.js";import"./index-L3cSIXth.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useCustomHook-acb00837-B7NPzH0H.js";import"./OnlineForm.vue_vue_type_style_index_0_scoped_3f26e7bd_lang-4ed993c7-l0sNRNKZ.js";import{Q as mt}from"./componentMap-Bkie1n3v.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./CommentPanel-HxTGfoA9.js";import"./OnlineFormDetail-fc087725-DWuxKFgz.js";import"./DetailForm-c592b8d8-BIx7KBmJ.js";import"./index-mbACBRQ9.js";import"./OnlineSubFormDetail-8be879b9-LBHMpLKz.js";import"./cgformState-d9f8ec42-C8rx7JjX.js";import"./SuperQueryValComponent.vue_vue_type_script_lang-8fe34917-DN8CXDRQ.js";import gt from"./BasicTable-xCEZpGLb.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-BLwcuZxD.js";var Q=(e,h,p)=>new Promise((r,S)=>{var w=l=>{try{f(p.next(l))}catch(C){S(C)}},u=l=>{try{f(p.throw(l))}catch(C){S(C)}},f=l=>l.done?r(l.value):Promise.resolve(l.value).then(w,u);f((p=p.apply(e,h)).next())});const ht={name:"DefaultOnlineList",components:{BasicTable:gt,TableAction:mt,OnlineAutoModal:Ue,JImportModal:at,OnlineQueryForm:pt,OnlineSuperQuery:ut,OnlineCustomModal:Ge,OnlineDetailModal:We},setup(){const{createMessage:e}=je(),h=J(),{ID:p,onlineTableContext:r,onlineQueryFormOuter:S,loading:w,reload:u,dataSource:f,pagination:l,handleSpecialConfig:C,getColumnList:F,handleChangeInTable:D,loadData:M,superQueryButtonRef:k,superQueryStatus:v,handleSuperQuery:E,registerCustomModal:L,getTreeDataByResult:t,expandedRowKeys:a,handleExpandedRowsChange:B,tableReloading:H,onlineExtConfigJson:_,handleFormConfig:$}=it();if(!p.value)throw e.warning("地址错误, 配置ID不存在!"),new Error("地址错误, 配置ID不存在!");r.isTree(!0);let{initCgEnhanceJs:U}=ct(r);const{buttonSwitch:W,cgLinkButtonList:G,cgBIBtnMap:Z,getQueryButtonCfg:X,getResetButtonCfg:Y,getFormConfirmButtonCfg:ee,cgTopButtonList:te,importUrl:oe,registerModal:ne,handleAdd:q,handleEdit:re,handleBatchDelete:ae,registerImportModal:ie,onImportExcel:le,onExportExcel:ce,cgButtonJsHandler:se,cgButtonActionHandler:de,cgButtonLinkHandler:pe,handleSubmitFlow:ue,getDropDownActions:me,getActions:ge,initButtonList:he,initButtonSwitch:fe,registerDetailModal:Ce}=lt(r,_),{columns:Be,actionColumn:ye,selectedKeys:be,rowSelection:we,enableScrollBar:Ie,tableScroll:Se,downloadRowFile:ke,getImgView:xe,getPcaText:Te,getFormatDate:Re,handleColumnResult:Fe,hrefComponent:De,viewOnlineCellImage:Me,handleClickFieldHref:ve}=st(r,_);Ze(p,()=>{Ee()},{immediate:!0});function Ee(){return Q(this,null,function*(){w.value=!0;let o=yield F(j);Le(o),yield M(),w.value=!1,r.execButtonEnhance("setup")})}function Le(o){let n=U(o.enhanceJs);r.EnhanceJS=n,he(o.cgButtonList),fe(o.hideColumns),Fe(o),C(o),r.hasChildrenField=o.hasChildrenField,r.pidField=o.pidField}function He(o,n){r.queryParam=o,n===!0?u({mode:"search"}):K()}function Ae(o){return Q(this,null,function*(){yield dt(k),k.value.init(o)})}function Oe(o,n){let I=a.value;if(o){if(Qe(n.id),n.children.length>0&&n.children[0].isLoading===!0){let x=r.hasChildrenField;const{sortField:Je,sortType:Ne}=r;let A=Object.assign({},{column:Je,order:Ne});A[r.pidField]=n.id,A[x]=n[x];let Ve=`${r.onlineUrl.getTreeData}${r.ID}`;$e.get({url:Ve,params:A},{isTransformResponse:!1}).then(R=>{R.success?Number(R.result.total)>0?n.children=t(R.result.records):(n.children="",n.hasChildrenField="0"):e.warning(R.message)}).catch(()=>{e.warning("加载子节点失败!")})}}else{let x=I.indexOf(n.id);x>=0&&(a.value=I.splice(x,1))}}function Qe(o){let n=a.value;n&&n.indexOf(o)<0&&n.push(o),a.value=n}function K(){return Q(this,null,function*(){r.isTree()===!0&&(a.value=[],h.value.collapseAll()),u()})}function _e(o){if(P.value===!0){let n=o[r.pidField];if(n){let I=a.value;I.indexOf(n)<0&&I.push(n),a.value=I}}u()}const qe=o=>({label:"添加下级",onClick:Ke.bind(null,o)}),P=J(!1);function Ke(o){P.value=!0;let n={[r.pidField]:o.id};q(n)}function Pe(o){let n=me(o,{themeTemplate:j});return n.unshift(qe(o)),n}return{ID:p,onlineQueryFormOuter:S,queryWithCondition:He,onQueryFormLoaded:Ae,reload:u,superQueryButtonRef:k,superQueryStatus:v,handleSuperQuery:E,loading:w,columns:Be,actionColumn:ye,dataSource:f,pagination:l,rowSelection:we,selectedKeys:be,tableScroll:Se,enableScrollBar:Ie,handleChangeInTable:D,buttonSwitch:W,handleAdd:q,handleEdit:re,onImportExcel:le,onExportExcel:ce,cgBIBtnMap:Z,getQueryButtonCfg:X,getResetButtonCfg:Y,getFormConfirmButtonCfg:ee,cgTopButtonList:te,cgLinkButtonList:G,cgButtonJsHandler:se,cgButtonActionHandler:de,cgButtonLinkHandler:pe,handleBatchDelete:ae,downloadRowFile:ke,getImgView:xe,getPcaText:Te,getFormatDate:Re,getActions:ge,getTreeDropDownActions:Pe,registerModal:ne,registerCustomModal:L,registerImportModal:ie,importUrl:oe,handleFormConfig:$,tableReloading:H,handleSubmitFlow:ue,hrefComponent:De,viewOnlineCellImage:Me,onlineTreeTableRef:h,handlerFormSuccess:_e,searchReset:K,handleExpand:Oe,expandedRowKeys:a,handleExpandedRowsChange:B,registerDetailModal:Ce,handleClickFieldHref:ve}}},ft={class:"p-2"},Ct={key:0,style:{"font-size":"12px","font-style":"italic"}},Bt={key:0,style:{"font-size":"12px","font-style":"italic"}},yt=["src","onClick"],bt=["innerHTML","onClick"],wt=["innerHTML"],It=["title"];function St(e,h,p,r,S,w){const u=s("a-skeleton"),f=s("online-query-form"),l=s("a-button"),C=s("online-super-query"),F=s("TableAction"),D=s("BasicTable"),M=s("OnlineAutoModal"),k=s("JImportModal"),v=s("a-modal"),E=s("online-custom-modal"),L=s("online-detail-modal");return i(),m("div",ft,[e.tableReloading?(i(),d(u,{key:0,active:""})):g("",!0),N(y(f,{ref:"onlineQueryFormOuter",id:e.ID,queryBtnCfg:e.getQueryButtonCfg,resetBtnCfg:e.getResetButtonCfg,onSearch:e.queryWithCondition,onLoaded:e.onQueryFormLoaded},null,8,["id","queryBtnCfg","resetBtnCfg","onSearch","onLoaded"]),[[V,!e.tableReloading]]),e.tableReloading?g("",!0):(i(),d(D,{key:1,ref:"onlineTreeTableRef",isTreeTable:!0,expandedRowKeys:e.expandedRowKeys,onExpandedRowsChange:e.handleExpandedRowsChange,onExpand:e.handleExpand,rowKey:"jeecg_row_key",canResize:!0,bordered:!0,showIndexColumn:!1,loading:e.loading,columns:e.columns,dataSource:e.dataSource,pagination:e.pagination,rowSelection:e.rowSelection,actionColumn:e.actionColumn,showTableSetting:!0,clickToRowSelect:!1,scroll:e.tableScroll,onTableRedo:e.reload,class:Xe({"j-table-force-nowrap":e.enableScrollBar}),onChange:e.handleChangeInTable},{tableTitle:c(()=>[e.buttonSwitch.add&&e.cgBIBtnMap.add.enabled?(i(),d(l,{key:0,type:"primary",preIcon:e.cgBIBtnMap.add.buttonIcon,onClick:e.handleAdd},{default:c(()=>[T("span",null,b(e.cgBIBtnMap.add.buttonName),1)]),_:1},8,["preIcon","onClick"])):g("",!0),e.buttonSwitch.export&&e.cgBIBtnMap.export.enabled?(i(),d(l,{key:1,type:"primary",preIcon:e.cgBIBtnMap.export.buttonIcon,onClick:e.onExportExcel},{default:c(()=>[T("span",null,b(e.cgBIBtnMap.export.buttonName),1)]),_:1},8,["preIcon","onClick"])):g("",!0),e.cgTopButtonList&&e.cgTopButtonList.length>0?(i(!0),m(z,{key:2},Ye(e.cgTopButtonList,(t,a)=>(i(),m(z,null,[t.optType=="js"?(i(),d(l,{key:"cgbtn"+a,onClick:B=>e.cgButtonJsHandler(t.buttonCode),type:"primary",preIcon:t.buttonIcon?"ant-design:"+t.buttonIcon:""},{default:c(()=>[O(b(t.buttonName),1)]),_:2},1032,["onClick","preIcon"])):t.optType=="action"?(i(),d(l,{key:"cgbtn"+a,onClick:B=>e.cgButtonActionHandler(t.buttonCode),type:"primary",preIcon:t.buttonIcon?"ant-design:"+t.buttonIcon:""},{default:c(()=>[O(b(t.buttonName),1)]),_:2},1032,["onClick","preIcon"])):g("",!0)],64))),256)):g("",!0),e.buttonSwitch.batch_delete&&e.cgBIBtnMap.batch_delete.enabled?N((i(),d(l,{key:3,preIcon:e.cgBIBtnMap.batch_delete.buttonIcon,onClick:e.handleBatchDelete},{default:c(()=>[T("span",null,b(e.cgBIBtnMap.batch_delete.buttonName),1)]),_:1},8,["preIcon","onClick"])),[[V,e.selectedKeys.length>0]]):g("",!0),e.buttonSwitch.super_query&&e.cgBIBtnMap.super_query.enabled?(i(),d(C,{key:4,ref:"superQueryButtonRef",online:"",status:e.superQueryStatus,queryBtnCfg:e.cgBIBtnMap.super_query,onSearch:e.handleSuperQuery},null,8,["status","queryBtnCfg","onSearch"])):g("",!0)]),fileSlot:c(({text:t,record:a,column:B})=>[t?(i(),d(l,{key:1,ghost:!0,type:"primary",preIcon:"ant-design:download",size:"small",onClick:H=>e.downloadRowFile(t,a,B,e.ID)},{default:c(()=>h[0]||(h[0]=[O(" 下载 ")])),_:2},1032,["onClick"])):(i(),m("span",Ct,"无文件"))]),imgSlot:c(({text:t})=>[t?(i(),m("img",{key:1,src:e.getImgView(t),alt:"图片不存在",class:"online-cell-image",onClick:a=>e.viewOnlineCellImage(t)},null,8,yt)):(i(),m("span",Bt,"无图片"))]),htmlSlot:c(({text:t,column:a,record:B})=>[a.fieldHref?(i(),m("a",{key:0,innerHTML:t,onClick:H=>e.handleClickFieldHref(a.fieldHref,B)},null,8,bt)):(i(),m("div",{key:1,innerHTML:t},null,8,wt))]),pcaSlot:c(({text:t})=>[T("div",{title:e.getPcaText(t)},b(e.getPcaText(t)),9,It)]),dateSlot:c(({text:t,column:a})=>[T("span",null,b(e.getFormatDate(t,a)),1)]),action:c(({record:t})=>[y(F,{actions:e.getActions(t),dropDownActions:e.getTreeDropDownActions(t)},null,8,["actions","dropDownActions"])]),_:1},8,["expandedRowKeys","onExpandedRowsChange","onExpand","loading","columns","dataSource","pagination","rowSelection","actionColumn","scroll","onTableRedo","class","onChange"])),y(M,{onRegister:e.registerModal,id:e.ID,cgBIBtnMap:e.cgBIBtnMap,buttonSwitch:e.buttonSwitch,confirmBtnCfg:e.getFormConfirmButtonCfg,onSuccess:e.handlerFormSuccess,onFormConfig:e.handleFormConfig},null,8,["onRegister","id","cgBIBtnMap","buttonSwitch","confirmBtnCfg","onSuccess","onFormConfig"]),y(k,{onRegister:e.registerImportModal,url:e.importUrl(),onOk:e.reload,online:""},null,8,["onRegister","url","onOk"]),y(v,et(e.hrefComponent.model,tt(e.hrefComponent.on)),{default:c(()=>[(i(),d(ot(e.hrefComponent.is),nt(rt(e.hrefComponent.params)),null,16))]),_:1},16),y(E,{onRegister:e.registerCustomModal,onSuccess:e.reload},null,8,["onRegister","onSuccess"]),y(L,{id:e.ID,onRegister:e.registerDetailModal},null,8,["id","onRegister"])])}const tn=ze(ht,[["render",St]]);export{tn as default};
