import{d as f,ag as e,aB as l,ar as n,aE as C,aD as s,aq as x,F as g,aC as k,at as o,k as d,au as a,G as y}from"./vue-vendor-dy9k-Yad.js";import{J as p}from"./antd-vue-vendor-me9YkNVC.js";import{c as h,a as B}from"./index-CCWaWN5g.js";import{g as G}from"./data-BJoU8O08.js";import"./vxe-table-vendor-B22HppNm.js";const $=f({components:{Card:p,CardGrid:p.Grid,Icon:h},setup(){return{items:G}}}),w={class:"flex"},I={class:"text-lg ml-4"},b={class:"flex mt-2 h-10 text-secondary"},v={class:"flex justify-between text-secondary"};function N(r,c,V,j,z,D){const i=e("a-button"),_=e("Icon"),u=e("CardGrid"),m=e("Card");return n(),l(m,C({title:"项目"},r.$attrs),{extra:s(()=>[d(i,{type:"link",size:"small"},{default:s(()=>c[0]||(c[0]=[y("更多")])),_:1,__:[0]})]),default:s(()=>[(n(!0),x(g,null,k(r.items,t=>(n(),l(u,{key:t,class:"!md:w-1/3 !w-full"},{default:s(()=>[o("span",w,[d(_,{icon:t.icon,color:t.color,size:"30"},null,8,["icon","color"]),o("span",I,a(t.title),1)]),o("div",b,a(t.desc),1),o("div",v,[o("span",null,a(t.group),1),o("span",null,a(t.date),1)])]),_:2},1024))),128))]),_:1},16)}const L=B($,[["render",N]]);export{L as default};
