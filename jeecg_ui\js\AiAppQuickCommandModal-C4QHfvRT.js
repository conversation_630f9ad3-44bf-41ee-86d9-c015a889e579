var F=Object.defineProperty;var u=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,v=Object.prototype.propertyIsEnumerable;var _=(r,t,o)=>t in r?F(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o,g=(r,t)=>{for(var o in t||(t={}))M.call(t,o)&&_(r,o,t[o]);if(u)for(var o of u(t))v.call(t,o)&&_(r,o,t[o]);return r};var d=(r,t,o)=>new Promise((i,a)=>{var c=e=>{try{m(o.next(e))}catch(n){a(n)}},s=e=>{try{m(o.throw(e))}catch(n){a(n)}},m=e=>e.done?i(e.value):Promise.resolve(e.value).then(c,s);m((o=o.apply(r,t)).next())});import{f as k,u as y,ag as h,aq as w,ar as x,k as C,aD as A}from"./vue-vendor-dy9k-Yad.js";import{I as O}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{B as R}from"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import{q as b}from"./AiApp.data-wU-aGD0q.js";import{u as q}from"./useForm-CgkFTrrO.js";import{ac as I,a as L}from"./index-CCWaWN5g.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const Q={name:"AiAppQuickCommandModal",components:{BasicForm:R,BasicModal:O},emits:["ok","update-ok","register"],setup(r,{emit:t}){const o=k("添加指令"),i=k(!1),[a,{validate:c,resetFields:s,setFieldsValue:m}]=q({schemas:b,showActionButtonGroup:!1,layout:"vertical",wrapperCol:{span:24}}),[e,{closeModal:n,setModalProps:l}]=I(p=>d(null,null,function*(){yield s(),i.value=!!(p!=null&&p.isUpdate),y(i)&&(yield m(g({},p.record))),l({minHeight:200,bodyStyle:{padding:"10px"}})}));function B(){return d(this,null,function*(){try{let p=yield c();l({confirmLoading:!0}),i.value?t("update-ok",p):t("ok",p),f()}finally{l({confirmLoading:!1})}})}function f(){n()}return{registerModal:e,registerForm:a,title:o,handleOk:B,handleCancel:f}}},S={class:"p-2"};function U(r,t,o,i,a,c){const s=h("BasicForm"),m=h("BasicModal");return x(),w("div",S,[C(m,{destroyOnClose:"",onRegister:i.registerModal,canFullscreen:!1,width:"800px",title:i.title,onOk:i.handleOk,onCancel:i.handleCancel},{default:A(()=>[C(s,{onRegister:i.registerForm},null,8,["onRegister"])]),_:1},8,["onRegister","title","onOk","onCancel"])])}const Go=L(Q,[["render",U],["__scopeId","data-v-ccbbe27c"]]);export{Go as default};
