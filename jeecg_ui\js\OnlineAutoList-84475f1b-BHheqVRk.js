import{f as Ht,w as At,ag as c,aq as u,ar as o,as as Q,aB as p,ah as s,q as _,k as d,B as q,aD as i,at as y,au as g,G as O,F as J,aC as Pt,aE as Qt,aP as _t,aQ as qt,aJ as Jt,aK as Nt}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{cs as Kt,u as Vt}from"./index-CCWaWN5g.js";import zt from"./OnlineAutoModal-95f46901-Co1FfZLa.js";import Ut from"./OnlineCustomModal-c8b1e780-DLSsFULK.js";import jt from"./OnlineDetailModal-5b412bb9-C14CjZpZ.js";import{a as Wt}from"./JImportModal-BnQ3nPZC.js";import{d as $t,m as Gt}from"./useListButton-98908683-Bu4g4Tt-.js";import{u as Zt,a as Xt,g as Yt,O as te}from"./useExtendComponent-bb98e568-B7LlULaY.js";import ee from"./OnlineQueryForm-9248341f-Do5iwEgi.js";import oe from"./SuperQuery-46032e66-Dg-mJkkb.js";import{u as ne}from"./useOnlinePopEvent-687070b7-B8lc-COq.js";import{e as re}from"./constant-fa63bd66-Ddbq-fz2.js";import"./index-Diw57m_E.js";import"./OnlineForm-58282699-DaLYL1I2.js";import"./index-L3cSIXth.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useCustomHook-acb00837-B7NPzH0H.js";import"./OnlineForm.vue_vue_type_style_index_0_scoped_3f26e7bd_lang-4ed993c7-l0sNRNKZ.js";import{Q as ie}from"./componentMap-Bkie1n3v.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./CommentPanel-HxTGfoA9.js";import"./OnlineFormDetail-fc087725-DWuxKFgz.js";import"./DetailForm-c592b8d8-BIx7KBmJ.js";import"./index-mbACBRQ9.js";import"./OnlineSubFormDetail-8be879b9-LBHMpLKz.js";import"./cgformState-d9f8ec42-C8rx7JjX.js";import"./SuperQueryValComponent.vue_vue_type_script_lang-8fe34917-DN8CXDRQ.js";import le from"./BasicTable-xCEZpGLb.js";import"./vxe-table-vendor-B22HppNm.js";import"./JUpload-CRos0F1P.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";import"./index-BLwcuZxD.js";var H=(t,m,l)=>new Promise((k,f)=>{var b=n=>{try{B(l.next(n))}catch(C){f(C)}},I=n=>{try{B(l.throw(n))}catch(C){f(C)}},B=n=>n.done?k(n.value):Promise.resolve(n.value).then(b,I);B((l=l.apply(t,m)).next())});const ae={name:"OnlineAutoList",components:{BasicTable:le,TableAction:ie,OnlineAutoModal:zt,JImportModal:Wt,OnlineQueryForm:ee,OnlineSuperQuery:oe,OnlineCustomModal:Ut,OnlineDetailModal:jt,OnlinePopModal:te},setup(){const{createMessage:t}=Vt(),{ID:m,onlineTableContext:l,onlineQueryFormOuter:k,loading:f,reload:b,dataSource:I,pagination:B,handleSpecialConfig:n,getColumnList:C,handleChangeInTable:x,loadData:R,superQueryButtonRef:M,superQueryStatus:T,handleSuperQuery:D,onlineExtConfigJson:S,handleFormConfig:F,registerCustomModal:v,tableReloading:e}=$t();if(!m.value)throw t.warning("地址错误, 配置ID不存在!"),new Error("地址错误, 配置ID不存在!");let{initCgEnhanceJs:a}=Zt(l);const{buttonSwitch:h,cgLinkButtonList:L,cgBIBtnMap:N,getQueryButtonCfg:K,getResetButtonCfg:V,getFormConfirmButtonCfg:z,cgTopButtonList:U,importUrl:j,registerModal:W,handleAdd:$,handleEdit:G,handleBatchDelete:Z,registerImportModal:X,onImportExcel:Y,onExportExcel:A,cgButtonJsHandler:tt,cgButtonActionHandler:et,cgButtonLinkHandler:ot,handleSubmitFlow:nt,getDropDownActions:rt,getActions:it,initButtonList:lt,initButtonSwitch:at,registerDetailModal:ct,registerBpmModal:pt}=Gt(l,S),E=Ht(!1);function st(){return H(this,null,function*(){try{E.value=!0,yield A()}finally{setTimeout(()=>E.value=!1,1500)}})}const{columns:dt,actionColumn:mt,selectedKeys:ut,rowSelection:gt,enableScrollBar:ft,tableScroll:Bt,downloadRowFile:Ct,getImgView:ht,getPcaText:yt,getFormatDate:bt,handleColumnResult:It,hrefComponent:Mt,viewOnlineCellImage:St,hrefMainTableId:wt,registerOnlineHrefModal:kt,registerPopModal:Tt,openPopModal:xt,onlinePopModalRef:Rt,popTableId:P,handleClickFieldHref:Dt}=Xt(l,S);At(m,()=>{Ft()},{immediate:!0});function Ft(){return H(this,null,function*(){f.value=!0;let r=yield C(re);vt(r),yield R(),f.value=!1,l.execButtonEnhance("setup")})}function vt(r){let w=a(r.enhanceJs);l.EnhanceJS=w,lt(r.cgButtonList),at(r.hideColumns),It(r),n(r)}function Lt(r){l.queryParam=r,b({mode:"search"})}function Et(r){return H(this,null,function*(){yield Yt(M),M.value.init(r)})}function Ot(r){P.value=r.id;let w={title:r.describe};r.record&&r.record.id&&(w.record=r.record,w.isUpdate=!0),xt(!0,w)}return ne(Ot),{ID:m,onlineQueryFormOuter:k,queryWithCondition:Lt,onQueryFormLoaded:Et,reload:b,superQueryButtonRef:M,superQueryStatus:T,handleSuperQuery:D,loading:f,columns:dt,dataSource:I,pagination:B,actionColumn:mt,rowSelection:gt,selectedKeys:ut,tableScroll:Bt,enableScrollBar:ft,handleChangeInTable:x,buttonSwitch:h,handleAdd:$,handleEdit:G,onImportExcel:Y,onExportExcel:A,exportLoading:E,onExportExcelOverride:st,cgBIBtnMap:N,getQueryButtonCfg:K,getResetButtonCfg:V,getFormConfirmButtonCfg:z,cgTopButtonList:U,cgLinkButtonList:L,cgButtonJsHandler:tt,cgButtonActionHandler:et,cgButtonLinkHandler:ot,handleBatchDelete:Z,downloadRowFile:Ct,getImgView:ht,getPcaText:yt,getFormatDate:bt,getActions:it,getDropDownActions:rt,registerModal:W,registerCustomModal:v,registerImportModal:X,registerDetailModal:ct,importUrl:j,handleFormConfig:F,onlinePopModalRef:Rt,tableReloading:e,handleSubmitFlow:nt,hrefComponent:Mt,viewOnlineCellImage:St,hrefMainTableId:wt,onlineExtConfigJson:S,registerOnlineHrefModal:kt,registerPopModal:Tt,popTableId:P,registerBpmModal:pt,handleClickFieldHref:Dt}}},ce={key:0,style:{"font-size":"12px","font-style":"italic"}},pe={key:0,style:{"font-size":"12px","font-style":"italic"}},se=["src","onClick"],de=["innerHTML","onClick"],me=["innerHTML"],ue=["title"];function ge(t,m,l,k,f,b){const I=c("a-skeleton"),B=c("online-query-form"),n=c("a-button"),C=c("online-super-query"),x=c("TableAction"),R=c("BasicTable"),M=c("OnlineAutoModal"),T=c("online-detail-modal"),D=c("JImportModal"),S=c("a-modal"),F=c("online-custom-modal"),v=c("online-pop-modal");return o(),u("div",{class:Q(["p-2",`online-list-${t.ID}`])},[t.tableReloading?(o(),p(I,{key:0,active:""})):s("",!0),_(d(B,{ref:"onlineQueryFormOuter",id:t.ID,queryBtnCfg:t.getQueryButtonCfg,resetBtnCfg:t.getResetButtonCfg,onSearch:t.queryWithCondition,onLoaded:t.onQueryFormLoaded},null,8,["id","queryBtnCfg","resetBtnCfg","onSearch","onLoaded"]),[[q,!t.tableReloading]]),t.tableReloading?s("",!0):(o(),p(R,{key:1,ref:"onlineTable",rowKey:"jeecg_row_key",canResize:!0,bordered:!0,showIndexColumn:!1,loading:t.loading,columns:t.columns,dataSource:t.dataSource,pagination:t.pagination,rowSelection:t.rowSelection,actionColumn:t.actionColumn,showTableSetting:!0,clickToRowSelect:!1,scroll:t.tableScroll,onTableRedo:t.reload,class:Q({"j-table-force-nowrap":t.enableScrollBar}),onChange:t.handleChangeInTable},{tableTitle:i(()=>[t.buttonSwitch.add&&t.cgBIBtnMap.add.enabled?(o(),p(n,{key:0,type:"primary",preIcon:t.cgBIBtnMap.add.buttonIcon,onClick:t.handleAdd},{default:i(()=>[y("span",null,g(t.cgBIBtnMap.add.buttonName),1)]),_:1},8,["preIcon","onClick"])):s("",!0),t.buttonSwitch.import&&t.cgBIBtnMap.import.enabled?(o(),p(n,{key:1,type:"primary",preIcon:t.cgBIBtnMap.import.buttonIcon,onClick:t.onImportExcel},{default:i(()=>[y("span",null,g(t.cgBIBtnMap.import.buttonName),1)]),_:1},8,["preIcon","onClick"])):s("",!0),t.buttonSwitch.export&&t.cgBIBtnMap.export.enabled?(o(),p(n,{key:2,type:"primary",preIcon:t.cgBIBtnMap.export.buttonIcon,loading:t.exportLoading,onClick:t.onExportExcelOverride},{default:i(()=>[y("span",null,g(t.cgBIBtnMap.export.buttonName),1)]),_:1},8,["preIcon","loading","onClick"])):s("",!0),t.cgTopButtonList&&t.cgTopButtonList.length>0?(o(!0),u(J,{key:3},Pt(t.cgTopButtonList,(e,a)=>(o(),u(J,null,[e.optType=="js"?(o(),p(n,{key:"cgbtn"+a,onClick:h=>t.cgButtonJsHandler(e.buttonCode),type:"primary",preIcon:e.buttonIcon?"ant-design:"+e.buttonIcon:""},{default:i(()=>[O(g(e.buttonName),1)]),_:2},1032,["onClick","preIcon"])):e.optType=="action"?(o(),p(n,{key:"cgbtn"+a,onClick:h=>t.cgButtonActionHandler(e.buttonCode),type:"primary",preIcon:e.buttonIcon?"ant-design:"+e.buttonIcon:""},{default:i(()=>[O(g(e.buttonName),1)]),_:2},1032,["onClick","preIcon"])):s("",!0)],64))),256)):s("",!0),t.buttonSwitch.batch_delete&&t.cgBIBtnMap.batch_delete.enabled?_((o(),p(n,{key:4,preIcon:t.cgBIBtnMap.batch_delete.buttonIcon,onClick:t.handleBatchDelete},{default:i(()=>[y("span",null,g(t.cgBIBtnMap.batch_delete.buttonName),1)]),_:1},8,["preIcon","onClick"])),[[q,t.selectedKeys.length>0]]):s("",!0),t.buttonSwitch.super_query&&t.cgBIBtnMap.super_query.enabled?(o(),p(C,{key:5,ref:"superQueryButtonRef",online:"",status:t.superQueryStatus,queryBtnCfg:t.cgBIBtnMap.super_query,onSearch:t.handleSuperQuery},null,8,["status","queryBtnCfg","onSearch"])):s("",!0)]),fileSlot:i(({text:e,record:a,column:h})=>[e?(o(),p(n,{key:1,ghost:!0,type:"primary",preIcon:"ant-design:download",size:"small",onClick:L=>t.downloadRowFile(e,a,h,t.ID)},{default:i(()=>m[0]||(m[0]=[O(" 下载 ")])),_:2},1032,["onClick"])):(o(),u("span",ce,"无文件"))]),imgSlot:i(({text:e})=>[e?(o(),u("img",{key:1,src:t.getImgView(e),alt:"图片不存在",class:"online-cell-image",onClick:a=>t.viewOnlineCellImage(e)},null,8,se)):(o(),u("span",pe,"无图片"))]),htmlSlot:i(({text:e,column:a,record:h})=>[a.fieldHref?(o(),u("a",{key:0,innerHTML:e,onClick:L=>t.handleClickFieldHref(a.fieldHref,h)},null,8,de)):(o(),u("div",{key:1,innerHTML:e},null,8,me))]),pcaSlot:i(({text:e})=>[y("div",{title:t.getPcaText(e)},g(t.getPcaText(e)),9,ue)]),dateSlot:i(({text:e,column:a})=>[y("span",null,g(t.getFormatDate(e,a)),1)]),action:i(({record:e})=>[d(x,{actions:t.getActions(e),dropDownActions:t.getDropDownActions(e)},null,8,["actions","dropDownActions"])]),_:1},8,["loading","columns","dataSource","pagination","rowSelection","actionColumn","scroll","onTableRedo","class","onChange"])),d(M,{onRegister:t.registerModal,id:t.ID,cgBIBtnMap:t.cgBIBtnMap,buttonSwitch:t.buttonSwitch,confirmBtnCfg:t.getFormConfirmButtonCfg,onSuccess:t.reload,onFormConfig:t.handleFormConfig},null,8,["onRegister","id","cgBIBtnMap","buttonSwitch","confirmBtnCfg","onSuccess","onFormConfig"]),d(T,{id:t.ID,onRegister:t.registerDetailModal},null,8,["id","onRegister"]),d(D,{onRegister:t.registerImportModal,url:t.importUrl(),onOk:t.reload,online:""},null,8,["onRegister","url","onOk"]),d(S,Qt(t.hrefComponent.model,_t(t.hrefComponent.on)),{default:i(()=>[(o(),p(qt(t.hrefComponent.is),Jt(Nt(t.hrefComponent.params)),null,16))]),_:1},16),d(F,{onRegister:t.registerCustomModal,onSuccess:t.reload},null,8,["onRegister","onSuccess"]),d(T,{id:t.hrefMainTableId,onRegister:t.registerOnlineHrefModal,defaultFullscreen:!1},null,8,["id","onRegister"]),d(v,{ref:"onlinePopModalRef",id:t.popTableId,onRegister:t.registerPopModal,onSuccess:t.reload,request:"",topTip:""},null,8,["id","onRegister","onSuccess"])],2)}const jo=Kt(ae,[["render",ge]]);export{jo as default};
