import{d as n,ag as e,aB as p,ar as i,aD as o,k as s,G as m}from"./vue-vendor-dy9k-Yad.js";import{P as c}from"./index-CtJ0w2CP.js";import{a as _}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const d=n({components:{PageWrapper:c}});function l(f,t,u,k,x,g){const r=e("router-link"),a=e("PageWrapper");return i(),p(a,{title:"层级面包屑示例",content:"子级页面面包屑会添加到当前层级后面"},{default:o(()=>[s(r,{to:"/feat/breadcrumb/children/childrenDetail"},{default:o(()=>t[0]||(t[0]=[m(" 进入子级详情页 ")])),_:1,__:[0]})]),_:1})}const v=_(d,[["render",l]]);export{v as default};
