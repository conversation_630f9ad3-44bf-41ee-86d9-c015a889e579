import{f as p,w as N,o as h,ag as f,aq as g,ar as b,at as t,k as i}from"./vue-vendor-dy9k-Yad.js";import{cs as w,R as y}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const S={class:"setSwitchOptions"},_="Y",k="N",B={__name:"SetSwitchOptions",props:{value:{type:[Array,String],default:["Y","N"]}},emits:"change",setup(c,{emit:m}){const d=c,n=m,l=p(_),u=p(k);N(()=>d.value,e=>{if(typeof e=="string"){const a=e.split(",");l.value=a[0],u.value=a[1]}else y(e)&&(l.value=e[0],u.value=e[1])},{immediate:!0});const o=()=>{l.value!=""&&u.value!=""&&r()},r=()=>{let e=Number(l.value),a=Number(u.value);(Number.isNaN(e)||Number.isNaN(a))&&(e=l.value,a=u.value),n("change",[e,a]),n("update:value",[e,a])};return h(()=>{r()}),(e,a)=>{const v=f("a-input");return b(),g("div",S,[t("p",null,[a[2]||(a[2]=t("span",null,"是",-1)),i(v,{value:l.value,"onUpdate:value":a[0]||(a[0]=s=>l.value=s),onChange:o},null,8,["value"])]),t("p",null,[a[3]||(a[3]=t("span",null,"否",-1)),i(v,{value:u.value,"onUpdate:value":a[1]||(a[1]=s=>u.value=s),onChange:o},null,8,["value"])])])}}},A=w(B,[["__scopeId","data-v-b718aa7e"]]);export{A as default};
