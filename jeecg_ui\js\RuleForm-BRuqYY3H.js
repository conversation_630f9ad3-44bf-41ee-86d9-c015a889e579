var f=(o,e,t)=>new Promise((m,l)=>{var d=r=>{try{s(t.next(r))}catch(a){l(a)}},i=r=>{try{s(t.throw(r))}catch(a){l(a)}},s=r=>r.done?m(r.value):Promise.resolve(r.value).then(d,i);s((t=t.apply(o,e)).next())});import{d as C,ag as b,aB as _,ar as k,aD as p,at as v,k as n,G as c}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{C as F}from"./index-LCGLvkB3.js";import{u as V,a as q}from"./index-CCWaWN5g.js";import{P as y}from"./index-CtJ0w2CP.js";import{i as B}from"./system-bqUZCbh5.js";import{B as S}from"./BasicForm-DBcXiHk0.js";import{u as D}from"./useForm-CgkFTrrO.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const g=[{field:"field1",component:"Input",label:"字段1",colProps:{span:8},required:!0},{field:"field2",component:"Input",label:"字段2",colProps:{span:8},required:!0},{field:"id",label:"id",required:!0,defaultValue:0,component:"InputNumber",show:!1},{field:"field3",component:"DatePicker",label:"字段3",colProps:{span:8},required:!0},{field:"field33",component:"DatePicker",label:"字段33",colProps:{span:8},componentProps:{valueFormat:"YYYY-MM-DD"},rules:[{required:!0,type:"string"}]},{field:"field44",component:"InputCountDown",label:"验证码",colProps:{span:8},required:!0},{field:"field4",component:"Select",label:"字段4",colProps:{span:8},componentProps:{mode:"multiple",options:[{label:"选项1",value:"1",key:"1"},{label:"选项2",value:"2",key:"2"}]},rules:[{required:!0,message:"请输入规则",type:"array"}]},{field:"field441",component:"Input",label:"自定义校验",colProps:{span:8},rules:[{required:!0,validator:(o,e)=>f(null,null,function*(){return e?e==="1"?Promise.reject("值不能为1"):Promise.resolve():Promise.reject("值不能为空")}),trigger:"change"}]},{field:"field5",component:"CheckboxGroup",label:"字段5",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},rules:[{required:!0}]},{field:"field7",component:"RadioGroup",label:"字段7",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},rules:[{required:!0,message:"覆盖默认生成的校验信息"}]},{field:"field8",component:"Input",label:"后端异步验证",colProps:{span:8},helpMessage:["本字段演示异步验证","本地规则：必须填写","后端规则：不能包含admin"],rules:[{required:!0,message:"请输入数据"},{validator(o,e){return new Promise((t,m)=>{B(e).then(()=>t()).catch(l=>{m(l.message||"验证失败")})})}}]}],I=C({components:{BasicForm:S,CollapseContainer:F,PageWrapper:y},setup(){const{createMessage:o}=V(),[e,{validateFields:t,clearValidate:m,getFieldsValue:l,resetFields:d,setFieldsValue:i}]=D({labelWidth:120,schemas:g,actionColOptions:{span:24}});function s(){return f(this,null,function*(){try{const u=yield t()}catch(u){}})}function r(){return f(this,null,function*(){m()})}function a(){const u=l();o.success("values:"+JSON.stringify(u))}function P(){i({field1:1111,field5:["1"],field7:"1",field33:"2020-12-12",field3:"2020-12-12"})}return{register:e,schemas:g,handleSubmit:u=>{o.success("click search,values:"+JSON.stringify(u))},getFormValues:a,setFormValues:P,validateForm:s,resetValidate:r,resetFields:d}}}),N={class:"mb-4"};function h(o,e,t,m,l,d){const i=b("a-button"),s=b("BasicForm"),r=b("CollapseContainer"),a=b("PageWrapper");return k(),_(a,{title:"表单校验示例"},{default:p(()=>[v("div",N,[n(i,{onClick:o.validateForm,class:"mr-2"},{default:p(()=>e[0]||(e[0]=[c(" 手动校验表单")])),_:1,__:[0]},8,["onClick"]),n(i,{onClick:o.resetValidate,class:"mr-2"},{default:p(()=>e[1]||(e[1]=[c(" 清空校验信息")])),_:1,__:[1]},8,["onClick"]),n(i,{onClick:o.getFormValues,class:"mr-2"},{default:p(()=>e[2]||(e[2]=[c(" 获取表单值")])),_:1,__:[2]},8,["onClick"]),n(i,{onClick:o.setFormValues,class:"mr-2"},{default:p(()=>e[3]||(e[3]=[c(" 设置表单值")])),_:1,__:[3]},8,["onClick"]),n(i,{onClick:o.resetFields,class:"mr-2"},{default:p(()=>e[4]||(e[4]=[c(" 重置")])),_:1,__:[4]},8,["onClick"])]),n(r,{title:"表单校验"},{default:p(()=>[n(s,{onRegister:o.register,onSubmit:o.handleSubmit},null,8,["onRegister","onSubmit"])]),_:1})]),_:1})}const Oe=q(I,[["render",h]]);export{Oe as default};
