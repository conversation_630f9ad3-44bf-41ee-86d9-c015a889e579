import{d as f,f as d,ag as n,aB as u,ar as C,aD as o,k as e,G as s}from"./vue-vendor-dy9k-Yad.js";import{C as k}from"./index-LCGLvkB3.js";import{useWatermark as _}from"./useWatermark-F6yKm61w.js";import{P as c}from"./index-CtJ0w2CP.js";import{a as W}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const g=f({components:{CollapseContainer:k,PageWrapper:c},setup(){const t=d(null),{setWatermark:r,clear:p}=_();return{setWatermark:r,clear:p,areaRef:t}}});function $(t,r,p,w,b,P){const a=n("a-button"),m=n("CollapseContainer"),l=n("PageWrapper");return C(),u(l,{title:"水印示例"},{default:o(()=>[e(m,{class:"w-full h-32 bg-white rounded-md",title:"Global WaterMark"},{default:o(()=>[e(a,{type:"primary",class:"mr-2",onClick:r[0]||(r[0]=i=>t.setWatermark("WaterMark Info"))},{default:o(()=>r[2]||(r[2]=[s(" Create ")])),_:1,__:[2]}),e(a,{color:"error",class:"mr-2",onClick:t.clear},{default:o(()=>r[3]||(r[3]=[s(" Clear ")])),_:1,__:[3]},8,["onClick"]),e(a,{color:"warning",class:"mr-2",onClick:r[1]||(r[1]=i=>t.setWatermark("WaterMark Info New"))},{default:o(()=>r[4]||(r[4]=[s(" Reset ")])),_:1,__:[4]})]),_:1})]),_:1})}const F=W(g,[["render",$]]);export{F as default};
