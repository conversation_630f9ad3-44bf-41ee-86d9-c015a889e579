<!doctype html><html lang="zh_CN" id="htmlRoot"><head><script src="/_app.config.js?v=3.8.0-1754897842538"></script><meta charset="UTF-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><meta name="renderer" content="webkit"/><meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=0"/><title>灰谷云</title><link rel="icon" href="/logo.png"/><script>window._CONFIG = {};</script><script type="module" crossorigin src="/js/index-CCWaWN5g.js"></script><link rel="modulepreload" crossorigin href="/js/vue-vendor-dy9k-Yad.js"><link rel="modulepreload" crossorigin href="/js/antd-vue-vendor-me9YkNVC.js"><link rel="modulepreload" crossorigin href="/js/vxe-table-vendor-B22HppNm.js"><link rel="stylesheet" crossorigin href="/assets/index-CEfKi2su.css"><link disabled="disabled" id="__VITE_PLUGIN_THEME-ANTD_DARK_THEME_LINK__" rel="alternate stylesheet" href="/assets/app-antd-dark-theme-style.e3b0c442.css"></head><body><script>(() => {
        var htmlRoot = document.getElementById('htmlRoot');
        var theme = window.localStorage.getItem('__APP__DARK__MODE__');
        if (htmlRoot && theme) {
          htmlRoot.setAttribute('data-theme', theme);
          theme = htmlRoot = null;
        }
      })();</script><div id="app"><style>html[data-theme=dark] .app-loading{background-color:#2c344a}html[data-theme=dark] .app-loading .app-loading-title{color:rgba(255,255,255,.85)}.app-loading{display:flex;width:100%;height:100%;justify-content:center;align-items:center;flex-direction:column;background-color:#f4f7f9}.app-loading .app-loading-wrap{position:absolute;top:50%;left:50%;display:flex;transform:translate3d(-50%,-50%,0);justify-content:center;align-items:center;flex-direction:column}.app-loading .dots{display:flex;padding:98px;justify-content:center;align-items:center}.app-loading .app-loading-title{display:flex;margin-top:30px;font-size:30px;color:rgba(0,0,0,.85);justify-content:center;align-items:center}.app-loading .app-loading-logo{display:block;width:90px;margin:0 auto;margin-bottom:20px}.dot{position:relative;display:inline-block;width:48px;height:48px;margin-top:30px;font-size:32px;transform:rotate(45deg);box-sizing:border-box;animation:antRotate 1.2s infinite linear}.dot i{position:absolute;display:block;width:20px;height:20px;background-color:#004c66;border-radius:100%;opacity:.3;transform:scale(.75);animation:antSpinMove 1s infinite linear alternate;transform-origin:50% 50%}.dot i:first-child{top:0;left:0}.dot i:nth-child(2){top:0;right:0;animation-delay:.4s}.dot i:nth-child(3){right:0;bottom:0;animation-delay:.8s}.dot i:nth-child(4){bottom:0;left:0;animation-delay:1.2s}@keyframes antRotate{to{transform:rotate(405deg)}}@keyframes antSpinMove{to{opacity:1}}</style><div class="app-loading"><div class="app-loading-wrap"><img src="/resource/img/logo.png" class="app-loading-logo" alt="Logo"/><div class="app-loading-dots"><span class="dot dot-spin"><i></i><i></i><i></i><i></i></span></div><div class="app-loading-title">灰谷云</div></div></div></div><script>var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement('script');
        hm.src = 'https://hm.baidu.com/hm.js?0febd9e3cacb3f627ddac64d52caac39';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
      })();</script></body></html>