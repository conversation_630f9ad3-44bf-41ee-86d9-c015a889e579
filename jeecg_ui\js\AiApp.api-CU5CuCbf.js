import{j as r}from"./index-CCWaWN5g.js";import{M as t}from"./antd-vue-vendor-me9YkNVC.js";const n=e=>r.get({url:"/airag/app/list",params:e},{isTransformResponse:!1}),p=e=>r.get({url:"/airag/knowledge/query/batch/byId",params:e},{isTransformResponse:!1}),l=e=>r.get({url:"/airag/app/queryById",params:e},{isTransformResponse:!1}),u=e=>r.put({url:"/airag/app/edit",params:e}),d=(e,a)=>{t.confirm({title:"确认删除",content:"是否删除名称为"+e.name+"的应用吗？",okText:"确认",cancelText:"取消",onOk:()=>r.delete({url:"/airag/app/delete",params:e},{joinParamsToUrl:!0}).then(()=>{a()})})},i=e=>r.get({url:"/airag/flow/queryById",params:e},{isTransformResponse:!1}),g=e=>r.get({url:"/airag/app/prompt/generate",params:e,timeout:5*60*1e3},{isTransformResponse:!1});export{n as a,p as b,i as c,d,g as p,l as q,u as s};
