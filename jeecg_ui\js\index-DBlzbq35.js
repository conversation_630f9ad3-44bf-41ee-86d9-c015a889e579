const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/index-C3kkzudb.js","js/index-CCWaWN5g.js","js/vue-vendor-dy9k-Yad.js","js/antd-vue-vendor-me9YkNVC.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/siteSetting-DoyCDlSB.js","js/useHeaderSetting-C-h5S52e.js","js/index-Diw57m_E.js","js/BasicModal-BLFvpBuk.js","js/ModalHeader-BJG9dHtK.js","js/useTimeout-CeTdFD_D.js","js/index-CImCetrx.js","assets/index-BObJM2Lc.css","assets/ModalHeader-HwQKX-UU.css","js/useWindowSizeFn-DDbrQbks.js","js/index-LCGLvkB3.js","js/index-De_W6s5g.js","js/index-D6l0IxOU.js","js/useIntersectionObserver-C4LVxQJW.js","assets/index-zj-Vfn3Q.css","assets/BasicModal-ByeTDAzn.css","js/CustomModal-BakuIxQv.js","assets/CustomModal-DWxHZmza.css","assets/index-yRxe3SQ1.css","js/header-OZa5fSDc.js","js/dict.api-BW6kWzU4.js","assets/index-ytBoeN8P.css","js/Breadcrumb-Bj_FSz4u.js","assets/Breadcrumb-CnC4y3jh.css","js/index-DbgqqQ70.js","js/index-CXHeQyuE.js","js/DetailModal-DbdGfGfj.js","assets/DetailModal-BhHds4L4.css","js/DynamicNotice-ISEJ4Nrz.js","js/useWebSocket-B-g1Ud0G.js","js/mynews.api-3wYWW1k0.js","js/SysMessageModal-Vow4XX6i.js","js/JSelectUser-COkExGbu.js","js/props-CCT78mKr.js","js/JSelectBiz-jOYRdMJf.js","assets/JSelectBiz-CYw1rOZ6.css","assets/JSelectUser-CQvjZTEr.css","assets/SysMessageModal-BMFO0gaC.css","assets/index-D3193Jox.css","js/ErrorAction-B0TndPT9.js","js/LockScreen-CzEK8OfF.js"])))=>i.map(i=>d[i]);
import{a6 as m,N as d,a as E,aj as t,_ as o}from"./index-CCWaWN5g.js";import{d as F,e as T,u as f,ag as r,aB as l,ar as a,aD as g,at as O}from"./vue-vendor-dy9k-Yad.js";import{T as y,aH as D,aI as L}from"./antd-vue-vendor-me9YkNVC.js";const v=F({name:"FullScreen",components:{FullscreenExitOutlined:L,FullscreenOutlined:D,Tooltip:y},setup(){const{t:e}=d(),{toggle:n,isFullscreen:s}=m();return{getTitle:T(()=>f(s)?e("layout.header.tooltipExitFull"):e("layout.header.tooltipEntryFull")),isFullscreen:s,toggle:n}}});function A(e,n,s,i,I,k){const u=r("FullscreenOutlined"),c=r("FullscreenExitOutlined"),_=r("Tooltip");return a(),l(_,{title:e.getTitle,placement:"bottom",mouseEnterDelay:.5},{default:g(()=>[O("span",{onClick:n[0]||(n[0]=(...p)=>e.toggle&&e.toggle(...p))},[e.isFullscreen?(a(),l(c,{key:1})):(a(),l(u,{key:0}))])]),_:1},8,["title"])}const C=E(v,[["render",A]]),R=t(()=>o(()=>import("./index-C3kkzudb.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27])),{loading:!0}),x=t(()=>o(()=>import("./Breadcrumb-Bj_FSz4u.js"),__vite__mapDeps([28,2,3,1,4,5,29]))),N=t(()=>o(()=>import("./index-DbgqqQ70.js"),__vite__mapDeps([30,2,3,1,4,5,31,32,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,33,34,35,36,37,38,39,40,41,42,43,44]))),$=t(()=>o(()=>import("./ErrorAction-B0TndPT9.js"),__vite__mapDeps([45,2,3,1,4,5]))),b=t(()=>o(()=>import("./LockScreen-CzEK8OfF.js"),__vite__mapDeps([46,1,2,3,4,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24])));export{$ as E,x as L,C as N,R as U,b as a,N as b};
