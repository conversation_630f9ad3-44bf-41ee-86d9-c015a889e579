import{d,aB as m,ar as t,u as e,aE as p,aD as s,aq as _,F as g,aC as k,k as a,at as n,au as C}from"./vue-vendor-dy9k-Yad.js";import{J as c}from"./antd-vue-vendor-me9YkNVC.js";import{c as h,u as x}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const B={class:"flex flex-col items-center"},b={class:"text-md mt-2"},G=d({__name:"QuickNav",setup(y){const i=c.Grid,r=x(),l=[{title:"业务受理",icon:"ion:home-outline",color:"#1fdaca"},{title:"业务管理",icon:"ion:grid-outline",color:"#bf0c2c"},{title:"文件管理",icon:"ion:layers-outline",color:"#e18525"},{title:"信息查询",icon:"ion:settings-outline",color:"#3fb27f"},{title:"通知公告",icon:"ion:notifications",color:"#13b0ff"},{title:"我的任务",icon:"ion:person-stalker",color:"#b27315"}];function u(){r.createMessage.success("根据业务自行处理跳转页面!")}return(f,N)=>(t(),m(e(c),p({title:"快捷导航"},f.$attrs),{default:s(()=>[(t(),_(g,null,k(l,o=>a(e(i),{key:o,onClick:u},{default:s(()=>[n("span",B,[a(e(h),{icon:o.icon,color:o.color,size:"20"},null,8,["icon","color"]),n("span",b,C(o.title),1)])]),_:2},1024)),64))]),_:1},16))}});export{G as default};
