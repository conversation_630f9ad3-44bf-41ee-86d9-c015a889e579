var l=(e,t,s)=>new Promise((d,o)=>{var i=a=>{try{r(s.next(a))}catch(c){o(c)}},y=a=>{try{r(s.throw(a))}catch(c){o(c)}},r=a=>a.done?d(a.value):Promise.resolve(a.value).then(i,y);r((s=s.apply(e,t)).next())});import{j as n,bo as T}from"./index-CCWaWN5g.js";import{M as u}from"./antd-vue-vendor-me9YkNVC.js";const L=e=>n.get({url:"/sys/tenant/customQueryPageList",params:e}),m=(e,t)=>{let s=t?"/sys/tenant/edit":"/sys/tenant/add";return n.post({url:s,params:e})},P=e=>n.get({url:"/sys/tenant/queryById",params:e}),v=(e,t)=>n.delete({url:"/sys/tenant/delete",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),B=(e,t)=>{u.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>n.delete({url:"/sys/tenant/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},f=e=>n.get({url:"/sys/tenant/getCurrentUserTenant",params:e}),h=e=>n.put({url:"/sys/tenant/invitationUserJoin",params:e},{joinParamsToUrl:!0}),j=e=>n.get({url:"/sys/tenant/getTenantUserList",params:e}),D=(e,t)=>{u.confirm({title:"请离",content:"是否请离该用户",okText:"确认",cancelText:"取消",onOk:()=>n.put({url:"/sys/tenant/leaveTenant",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},I=e=>n.get({url:"/sys/tenant/packList",params:e}),q=e=>n.post({url:"/sys/tenant/addPackPermission",params:e}),x=e=>n.put({url:"/sys/tenant/editPackPermission",params:e}),J=(e,t)=>n.delete({url:"/sys/tenant/deleteTenantPack",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),O=(e,t)=>n.post({url:"/sys/tenant/syncDefaultPack",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),b=e=>n.get({url:"/sys/tenant/recycleBinPageList",params:e}),C=(e,t)=>n.delete({url:"/sys/tenant/deleteLogicDeleted",params:e},{joinParamsToUrl:!0}).then(()=>{t()}).catch(()=>{t()}),w=(e,t)=>n.put({url:"/sys/tenant/revertTenantLogic",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),M=e=>n.get({url:"/sys/tenant/queryTenantPackUserList",params:e}),Q=e=>n.put({url:"/sys/tenant/deleteTenantPackUser",params:e}),H=e=>n.post({url:"/sys/tenant/addTenantPackUser",params:e}),N=e=>n.get({url:"/sys/tenant/getTenantPageListByUserId",params:e});function z(){return l(this,null,function*(){let e=T();if(e){let t=yield P({id:e});if(t)return t.name}return"空"})}const E=(e,t)=>{let s=t?"/sys/user/editTenantUser":"/sys/user/add";return n.post({url:s,params:e},{joinParamsToUrl:!0})};export{P as a,m as b,w as c,C as d,j as e,v as f,z as g,B as h,h as i,L as j,J as k,D as l,O as m,q as n,x as o,I as p,Q as q,b as r,E as s,H as t,M as u,N as v,f as w};
