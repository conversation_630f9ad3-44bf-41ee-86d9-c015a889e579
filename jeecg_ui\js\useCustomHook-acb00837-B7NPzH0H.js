import{aU as l}from"./vue-vendor-dy9k-Yad.js";import{cE as f,cF as m,cG as p,cH as g,bH as S}from"./index-CCWaWN5g.js";const x={vue:l,"@":{hooks:{useMessage:g,useUserStore:p},utils:{axios:m,cache:f}}};function _(u,s){const i=Object.assign({},x,u);function r(t){if(t!=null&&t!=""){let e=t.toString().split("/"),o=i[e[0]];for(let n=1;n<e.length;n++)o=o[e[n]];return o}return null}function c(){}function a(t,e){let o="__export_"+S(6);if(e){const n=`return function (row, customImport, ${o}) {"use strict"; ${t}}`;new Function(n)().call(s,e,r,c)}else{const n=`return function (customImport, ${o}) {"use strict"; ${t}}`;new Function(n)().call(s,r,c)}}return{executeJsEnhanced:a}}const w=/(?:\/\*[\s\S]*?\*\/|\/\/.*?\r?\n|[^{])+\{([\s\S]*)\}$/;export{_ as I,w as g};
