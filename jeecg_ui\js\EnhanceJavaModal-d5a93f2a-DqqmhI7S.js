import{d as H,f as T,r as Q,e as W,n as X,ag as l,aB as D,ar as A,aD as i,k as n,ah as P,G as f,aJ as Y,aK as Z,aq as ee}from"./vue-vendor-dy9k-Yad.js";import{B as oe}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./index-BkGZ5fiW.js";import{useListPage as te}from"./useListPage-Soxgnx9a.js";import{J as re}from"./useOnlineTest-e4bd8be3-hnhsV9Hd.js";import{d as ie,i as ne}from"./enhance.data-6601ff44-CPj6ao2j.js";import{w as ae,R as F,j as le}from"./enhance.api-138e6826-BOpOzAwu.js";import{cs as ce,ac as se,ad as me}from"./index-CCWaWN5g.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import{u as pe}from"./useForm-CgkFTrrO.js";import{Q as de}from"./componentMap-Bkie1n3v.js";import{B as ue}from"./BasicForm-DBcXiHk0.js";import fe from"./BasicTable-xCEZpGLb.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";var ge=Object.defineProperty,x=Object.getOwnPropertySymbols,ye=Object.prototype.hasOwnProperty,ve=Object.prototype.propertyIsEnumerable,S=(e,o,t)=>o in e?ge(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,be=(e,o)=>{for(var t in o||(o={}))ye.call(o,t)&&S(e,t,o[t]);if(x)for(var t of x(o))ve.call(o,t)&&S(e,t,o[t]);return e},g=(e,o,t)=>new Promise((y,u)=>{var v=a=>{try{c(t.next(a))}catch(m){u(m)}},s=a=>{try{c(t.throw(a))}catch(m){u(m)}},c=a=>a.done?y(a.value):Promise.resolve(a.value).then(v,s);c((t=t.apply(e,o)).next())});const he=H({name:"EnhanceJavaModal",components:{BasicModal:oe,BasicTable:fe,BasicForm:ue,TableAction:de},emits:["register"],setup(){const e=T(""),o=T([]),{columns:t}=ie(o),{doRequest:y,doDeleteRecord:u,tableContext:v}=te({tableProps:{api:r=>g(this,null,function*(){let{dataSource:b,btnList:z}=yield ae(e.value,r);return o.value=z,b}),columns:t,canResize:!1,useSearchForm:!1,beforeFetch(r){return Object.assign(r,{column:"orderNum",order:"asc"})}}}),[s,{reload:c},{rowSelection:a,selectedRowKeys:m}]=v,[h,{closeModal:w}]=se(r=>g(this,null,function*(){e.value=r.row.id,c()})),{aiTestMode:C,genEnhanceJavaData:M}=re(),[k,d]=me(),p=T(!1),B=Q({onRegister:k,title:W(()=>p!=null&&p.value?"修改":"新增"),width:800,confirmLoading:!1,bodyStyle:{height:"350px"},onOk:I,onCancel:d.closeModal});let R={};const{formSchemas:O}=ne(o),[j,{resetFields:E,setFieldsValue:J,validate:K}]=pe({schemas:O,showActionButtonGroup:!1,labelCol:{xs:24,sm:5},wrapperCol:{xs:24,sm:16}});function L(){w()}function _(r){return g(this,null,function*(){var b;p.value=r.isUpdate,R=be({},(b=r.record)!=null?b:{}),d.openModal(),yield X(),yield E(),J(R)})}function V(){_({isUpdate:!1})}function G(r){_({isUpdate:!0,record:r})}function N(){M(e.value)}function $(){return g(this,null,function*(){y(()=>F(m.value))})}function I(){return g(this,null,function*(){try{B.confirmLoading=!0;let r=yield K();r=Object.assign({},R,r),yield le(e.value,r,p.value),c(),d.closeModal()}finally{B.confirmLoading=!1}})}function U(r){return[{label:"编辑",onClick:()=>G(r)}]}function q(r){return[{label:"删除",popConfirm:{title:"确定删除吗？",placement:"left",confirm:()=>u(()=>F([r.id]))}}]}return{rowSelection:a,selectedRowKeys:m,aiTestMode:C,onCancel:L,onAdd:V,onGenEnhanceJavaData:N,onBatchDelete:$,getTableAction:U,getDropDownAction:q,formModalProps:B,registerModal:h,registerTable:s,registerForm:j}},computed:{tableScroll(){return{y:window.innerHeight-320}}}}),we={key:0,style:{float:"left"}};function Ce(e,o,t,y,u,v){const s=l("a-button"),c=l("a-icon"),a=l("a-menu-item"),m=l("a-menu"),h=l("a-dropdown"),w=l("TableAction"),C=l("BasicTable"),M=l("BasicForm"),k=l("a-spin"),d=l("BasicModal");return A(),D(d,{onRegister:e.registerModal,title:"JAVA增强",width:1200,defaultFullscreen:"",onCancel:e.onCancel},{footer:i(()=>[n(s,{onClick:e.onCancel},{default:i(()=>o[3]||(o[3]=[f("关闭")])),_:1},8,["onClick"]),e.aiTestMode?(A(),ee("div",we,[n(s,{onClick:e.onGenEnhanceJavaData},{default:i(()=>o[4]||(o[4]=[f("生成测试数据")])),_:1},8,["onClick"])])):P("",!0)]),default:i(()=>[n(C,{onRegister:e.registerTable,rowSelection:e.rowSelection},{tableTitle:i(()=>[n(s,{onClick:e.onAdd,type:"primary",preIcon:"ant-design:plus"},{default:i(()=>o[0]||(o[0]=[f("新增")])),_:1},8,["onClick"]),e.selectedRowKeys.length>0?(A(),D(h,{key:0},{overlay:i(()=>[n(m,null,{default:i(()=>[n(a,{key:"1",onClick:e.onBatchDelete},{default:i(()=>[n(c,{type:"delete"}),o[1]||(o[1]=f(" 删除 "))]),_:1},8,["onClick"])]),_:1})]),default:i(()=>[n(s,{style:{"margin-left":"8px"}},{default:i(()=>[o[2]||(o[2]=f(" 批量操作 ")),n(c,{type:"down"})]),_:1})]),_:1})):P("",!0)]),action:i(({record:p})=>[n(w,{actions:e.getTableAction(p),dropDownActions:e.getDropDownAction(p)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),n(d,Y(Z(e.formModalProps)),{default:i(()=>[n(k,{spinning:e.formModalProps.confirmLoading},{default:i(()=>[n(M,{onRegister:e.registerForm},null,8,["onRegister"])]),_:1},8,["spinning"])]),_:1},16)]),_:1},8,["onRegister","onCancel"])}const Fo=ce(he,[["render",Ce]]);export{Fo as default};
