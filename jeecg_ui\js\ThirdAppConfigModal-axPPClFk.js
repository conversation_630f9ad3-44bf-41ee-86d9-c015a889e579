var c=(e,l,o)=>new Promise((d,p)=>{var a=t=>{try{r(o.next(t))}catch(m){p(m)}},n=t=>{try{r(o.throw(t))}catch(m){p(m)}},r=t=>t.done?d(t.value):Promise.resolve(t.value).then(a,n);r((o=o.apply(e,l)).next())});import{d as h,f as g,ag as u,aB as I,ar as w,aD as C,k as B}from"./vue-vendor-dy9k-Yad.js";import{B as b}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{b as k,c as T}from"./ThirdApp.api-3uEF4_Sr.js";import{B as y}from"./BasicForm-DBcXiHk0.js";import{u as M}from"./useForm-CgkFTrrO.js";import{ac as F,a as _}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const v=[{label:"id",field:"id",component:"Input",show:!1},{label:"thirdType",field:"thirdType",component:"Input",show:!1},{label:"CorpId",field:"corpId",component:"Input",ifShow:({values:e})=>e.thirdType==="dingtalk",required:!0},{label:"Agentld",field:"agentId",component:"Input",required:!0},{label:"AppKey",field:"clientId",component:"Input",required:!0},{label:"AppSecret",field:"clientSecret",component:"Input",required:!0},{label:"启用",field:"status",component:"Switch",componentProps:{checkedChildren:"关闭",checkedValue:1,unCheckedChildren:"开启",unCheckedValue:0},defaultValue:1},{label:"租户id",field:"tenantId",component:"Input",show:!1}],A=h({name:"ThirdAppConfigModal",components:{BasicModal:b,BasicForm:y},setup(e,{emit:l}){const o=g("钉钉配置"),[d,{resetFields:p,setFieldsValue:a,validate:n}]=M({schemas:v,showActionButtonGroup:!1,labelCol:{span:24},wrapperCol:{span:24}}),[r,{setModalProps:t,closeModal:m}]=F(i=>c(null,null,function*(){t({confirmLoading:!0}),i.thirdType=="dingtalk"?o.value="钉钉配置":o.value="企业微信配置",yield p();let s=yield k({tenantId:i.tenantId,thirdType:i.thirdType});t({confirmLoading:!1}),s?yield a(s):yield a(i)}));function f(){return c(this,null,function*(){let i=yield n(),s=!1;i.id&&(s=!0),yield T(i,s),l("success"),m()})}return{title:o,registerForm:d,registerModal:r,handleSubmit:f}}});function S(e,l,o,d,p,a){const n=u("BasicForm"),r=u("BasicModal");return w(),I(r,{onRegister:e.registerModal,width:800,title:e.title,onOk:e.handleSubmit},{default:C(()=>[B(n,{onRegister:e.registerForm},null,8,["onRegister"])]),_:1},8,["onRegister","title","onOk"])}const Rt=_(A,[["render",S]]);export{Rt as default};
