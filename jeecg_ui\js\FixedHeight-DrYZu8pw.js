import{d as n,ag as r,aq as l,ar as c,k as i,aD as m,G as p,at as d}from"./vue-vendor-dy9k-Yad.js";import{u}from"./index-BkGZ5fiW.js";import{getCustomHeaderColumns as f}from"./tableData-B4J3mkj4.js";import{aw as _}from"./antd-vue-vendor-me9YkNVC.js";import{d as B}from"./table-BDFKJhHv.js";import{a as T}from"./index-CImCetrx.js";import g from"./BasicTable-xCEZpGLb.js";import{a as b}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const x=n({components:{BasicTable:g,FormOutlined:_,BasicHelp:T},setup(){const[t]=u({title:"定高/头部自定义",api:B,columns:f(),canResize:!1,scroll:{y:100}});return{registerTable:t}}}),C={class:"p-4"};function H(t,o,F,$,k,N){const e=r("BasicHelp"),s=r("FormOutlined"),a=r("BasicTable");return c(),l("div",C,[i(a,{onRegister:t.registerTable},{customTitle:m(()=>[d("span",null,[o[0]||(o[0]=p(" 姓名 ")),i(e,{class:"ml-2",text:"姓名"})])]),customAddress:m(()=>[o[1]||(o[1]=p(" 地址 ")),i(s,{class:"ml-2"})]),_:1},8,["onRegister"])])}const qo=b(x,[["render",H]]);export{qo as default};
