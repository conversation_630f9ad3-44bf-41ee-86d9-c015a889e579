var H=Object.defineProperty;var U=Object.getOwnPropertySymbols;var Q=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable;var j=(p,e,s)=>e in p?H(p,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):p[e]=s,K=(p,e)=>{for(var s in e||(e={}))Q.call(e,s)&&j(p,s,e[s]);if(U)for(var s of U(e))W.call(e,s)&&j(p,s,e[s]);return p};import{f,r as V,ag as i,aq as T,ar as y,at as l,k as t,aB as b,ah as E,aO as X,aD as a,G as z,F as Y,aC as Z,au as J,A as C}from"./vue-vendor-dy9k-Yad.js";import{I as $}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{ai as ee,a3 as oe,x as te}from"./antd-vue-vendor-me9YkNVC.js";import{c as ae,ad as O,u as ne,d as ie,a as le}from"./index-CCWaWN5g.js";import{d as re}from"./ailogo-DG2_TD5d.js";import se from"./AiAppModal-Bh7yirz7.js";import pe from"./AiAppSettingModal-DU38cg-V.js";import de from"./AiAppSendModal-Dlcu_1Bp.js";import{a as ce,d as me}from"./AiApp.api-CU5CuCbf.js";import{y as _e}from"./componentMap-Bkie1n3v.js";import{_ as ue}from"./JAddInput-CxJ-JBK-.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./AiApp.data-wU-aGD0q.js";import"./AiAppAddKnowledgeModal-CpdQzdSS.js";import"./AiKnowledgeBase.api-Dgaf5KfS.js";import"./knowledge-BXTupIwn.js";import"./AiAppParamsSettingModal-DyqbZfN8.js";import"./index-mbACBRQ9.js";import"./AiModelSeniorForm-DwUUzhZR.js";import"./AiAppGeneratedPromptModal-B4YmsZ0M.js";import"./AiAppQuickCommandModal-C4QHfvRT.js";import"./AiAppAddFlowModal-BuhG0aib.js";import"./chat-I63fsyMB.js";import"./chatMessage-DcTiQunt.js";import"./chatText-DGPEwQvb.js";import"./presetQuestion-CYEhQsHK.js";import"./JMarkdownEditor-CxtN1OHq.js";import"./vuedraggable.umd-DpmahwAM.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";const ge={name:"AiAppList",components:{JDictSelectTag:ue,JInput:_e,AiAppSendModal:de,Icon:ae,Pagination:te,Avatar:oe,LoadingOutlined:ee,BasicModal:$,AiAppModal:se,AiAppSettingModal:pe},emits:["success","register"],setup(p,{emit:e}){const s=f([]),o=f(1),A=f(10),S=f(0),I=f(["10","20","30"]),[w,{openModal:c}]=O(),[F,{openModal:v}]=O(),[x,{openModal:B}]=O(),{createMessage:m}=ne(),u=V({}),M=V({xs:24,sm:4,xl:6,xxl:6}),k=V({xs:24,sm:20}),h=f();d();function d(){let r={pageNo:o.value,pageSize:A.value,column:"createTime",order:"desc"};Object.assign(r,u),ce(r).then(_=>{_.success?(s.value=_.result.records,S.value=_.result.total):(s.value=[],S.value=0)})}function P(){c(!0,{})}function R(r,_){o.value=r,A.value=_,d()}function D(r){d(),v(!0,{isUpdate:!1,id:r})}function L(r){return r?ie(r):re}function q(r){v(!0,K({isUpdate:!0},r))}function N(r){window.open("/ai/app/chat/"+r,"_blank")}function n(r){me({id:r.id,name:r.name},d)}function g(r,_){B(!0,{type:_,data:r})}function G(){h.value.resetFields(),u.name="",d()}return{handleCreateApp:P,knowledgeAppDataList:s,pageNo:o,pageSize:A,total:S,pageSizeOptions:I,handlePageChange:R,cardBodyStyle:{textAlign:"left",width:"100%"},registerModal:w,handleSuccess:D,getImage:L,handleEditClick:q,handleViewClick:N,handleDeleteClick:n,registerSettingModal:F,reload:d,queryParam:u,labelCol:M,wrapperCol:k,handleSendClick:g,registerAiAppSendModal:x,searchReset:G,formRef:h}}},fe={class:"p-2 knowledge"},ye={class:"jeecg-basic-table-form-container"},ve={style:{float:"left",overflow:"hidden"},class:"table-page-search-submitButtons"},ke={class:"flex"},he=["src"],Ce={class:"header-text"},Ae={class:"header-text-top header-name ellipsis"},Se={class:"header-text-top header-create ellipsis"},we={class:"header-tag"},xe={class:"card-description"},Me={class:"card-footer"},be=["onClick"],ze=["onClick"];function Ie(p,e,s,o,A,S){const I=i("JInput"),w=i("a-form-item"),c=i("a-col"),F=i("j-dict-select-tag"),v=i("a-button"),x=i("a-row"),B=i("a-form"),m=i("Icon"),u=i("a-card"),M=i("a-tag"),k=i("a-tooltip"),h=i("a-divider"),d=i("a-menu-item"),P=i("a-menu"),R=i("a-dropdown"),D=i("Pagination"),L=i("AiAppModal"),q=i("AiAppSettingModal"),N=i("AiAppSendModal");return y(),T("div",fe,[l("div",ye,[t(B,{ref:"formRef",onKeyup:X(o.reload,["enter","native"]),model:o.queryParam,"label-col":o.labelCol,"wrapper-col":o.wrapperCol,style:{"background-color":"#f7f8fc"}},{default:a(()=>[t(x,{gutter:24},{default:a(()=>[t(c,{lg:6},{default:a(()=>[t(w,{name:"name",label:"应用名称"},{default:a(()=>[t(I,{value:o.queryParam.name,"onUpdate:value":e[0]||(e[0]=n=>o.queryParam.name=n),placeholder:"请输入应用名称"},null,8,["value"])]),_:1})]),_:1}),t(c,{lg:6},{default:a(()=>[t(w,{name:"type",label:"应用类型"},{default:a(()=>[t(F,{value:o.queryParam.type,"onUpdate:value":e[1]||(e[1]=n=>o.queryParam.type=n),"dict-code":"ai_app_type",placeholder:"请选择应用类型"},null,8,["value"])]),_:1})]),_:1}),t(c,{xl:6,lg:7,md:8,sm:24},{default:a(()=>[l("span",ve,[t(c,{lg:6},{default:a(()=>[t(v,{type:"primary",preIcon:"ant-design:search-outlined",onClick:o.reload},{default:a(()=>e[4]||(e[4]=[z("查询")])),_:1,__:[4]},8,["onClick"]),t(v,{type:"primary",preIcon:"ant-design:reload-outlined",onClick:o.searchReset,style:{"margin-left":"8px"}},{default:a(()=>e[5]||(e[5]=[z("重置")])),_:1,__:[5]},8,["onClick"])]),_:1})])]),_:1})]),_:1})]),_:1},8,["onKeyup","model","label-col","wrapper-col"])]),t(x,{span:24,class:"knowledge-row"},{default:a(()=>[t(c,{xxl:4,xl:6,lg:6,md:6,sm:12,xs:24},{default:a(()=>[t(u,{class:"add-knowledge-card",bodyStyle:o.cardBodyStyle},{default:a(()=>[e[7]||(e[7]=l("span",{style:{"line-height":"18px","font-weight":"500",color:"#676f83","font-size":"12px"}},"创建应用",-1)),l("div",{class:"add-knowledge-doc",onClick:e[2]||(e[2]=(...n)=>o.handleCreateApp&&o.handleCreateApp(...n))},[t(m,{icon:"ant-design:form-outlined",size:"13"}),e[6]||(e[6]=l("span",null,"创建空白应用",-1))])]),_:1,__:[7]},8,["bodyStyle"])]),_:1}),(y(!0),T(Y,null,Z(o.knowledgeAppDataList,n=>(y(),b(c,{xxl:4,xl:6,lg:6,md:6,sm:12,xs:24},{default:a(()=>[t(u,{class:"knowledge-card pointer",onClick:g=>o.handleEditClick(n)},{default:a(()=>[l("div",ke,[l("img",{class:"header-img",src:o.getImage(n.icon)},null,8,he),l("div",Ce,[l("span",Ae,J(n.name),1),l("span",Se," 创建者："+J(n.createBy),1)])]),l("div",we,[n.type==="chatSimple"?(y(),b(M,{key:0,color:"#EBF1FF",style:{"margin-right":"0"}},{default:a(()=>e[8]||(e[8]=[l("span",{style:{color:"#3370ff"}},"简单配置",-1)])),_:1,__:[8]})):E("",!0),n.type==="chatFLow"?(y(),b(M,{key:1,color:"#FDF6EC",style:{"margin-right":"0"}},{default:a(()=>e[9]||(e[9]=[l("span",{style:{color:"#e6a343"}},"高级编排",-1)])),_:1,__:[9]})):E("",!0)]),l("div",xe,[l("span",null,J(n.descr||"暂无描述"),1)]),l("div",Me,[t(k,{title:"演示"},{default:a(()=>[l("div",{class:"card-footer-icon",onClick:C(g=>o.handleViewClick(n.id),["prevent","stop"])},[t(m,{class:"operation",icon:"ant-design:youtube-outlined",size:"20",color:"#1F2329"})],8,be)]),_:2},1024),t(h,{type:"vertical",style:{float:"left"}}),t(k,{title:"删除"},{default:a(()=>[l("div",{class:"card-footer-icon",onClick:C(g=>o.handleDeleteClick(n),["prevent","stop"])},[t(m,{icon:"ant-design:delete-outlined",class:"operation",size:"20",color:"#1F2329"})],8,ze)]),_:2},1024),t(h,{type:"vertical",style:{float:"left"}}),t(k,{title:"发布"},{default:a(()=>[t(R,{class:"card-footer-icon",placement:"bottomRight",trigger:["click"]},{overlay:a(()=>[t(P,null,{default:a(()=>[t(d,{key:"web",onClick:C(g=>o.handleSendClick(n,"web"),["prevent","stop"])},{default:a(()=>[t(m,{icon:"ant-design:dribbble-outlined",size:"16"}),e[10]||(e[10]=z(" 嵌入网站 "))]),_:2,__:[10]},1032,["onClick"]),t(d,{key:"menu",onClick:C(g=>o.handleSendClick(n,"menu"),["prevent","stop"])},{default:a(()=>[t(m,{icon:"ant-design:menu-outlined",size:"16"}),e[11]||(e[11]=z(" 配置菜单 "))]),_:2,__:[11]},1032,["onClick"])]),_:2},1024)]),default:a(()=>[l("div",{onClick:e[3]||(e[3]=C(()=>{},["prevent","stop"]))},[t(m,{icon:"ant-design:send-outlined"})])]),_:2},1024)]),_:2},1024)])]),_:2},1032,["onClick"])]),_:2},1024))),256))]),_:1}),o.knowledgeAppDataList.length>0?(y(),b(D,{key:0,current:o.pageNo,"page-size":o.pageSize,"page-size-options":o.pageSizeOptions,total:o.total,showQuickJumper:!0,showSizeChanger:!0,onChange:o.handlePageChange,class:"list-footer",size:"small"},null,8,["current","page-size","page-size-options","total","onChange"])):E("",!0),t(L,{onRegister:o.registerModal,onSuccess:o.handleSuccess},null,8,["onRegister","onSuccess"]),t(q,{onRegister:o.registerSettingModal,onSuccess:o.reload},null,8,["onRegister","onSuccess"]),t(N,{onRegister:o.registerAiAppSendModal},null,8,["onRegister"])])}const $o=le(ge,[["render",Ie],["__scopeId","data-v-8aa9feda"]]);export{$o as default};
