import{d as e,f as r,aB as o,ar as s,u as a}from"./vue-vendor-dy9k-Yad.js";import n from"./Exception-MnIBXmKT.js";import{aV as _}from"./index-CCWaWN5g.js";const p=e({__name:"NotAccessException",setup(c){const t=r(_.PAGE_NOT_ACCESS);return(u,l)=>(s(),o(a(n),{status:t.value},null,8,["status"]))}}),b=Object.freeze(Object.defineProperty({__proto__:null,default:p},Symbol.toStringTag,{value:"Module"})),i=e({__name:"NotDataErrorException",setup(c){const t=r(_.PAGE_NOT_DATA);return(u,l)=>(s(),o(a(n),{status:t.value},null,8,["status"]))}}),O=Object.freeze(Object.defineProperty({__proto__:null,default:i},Symbol.toStringTag,{value:"Module"})),f=e({__name:"ServerErrorException",setup(c){const t=r(_.ERROR);return(u,l)=>(s(),o(a(n),{status:t.value},null,8,["status"]))}}),S=Object.freeze(Object.defineProperty({__proto__:null,default:f},Symbol.toStringTag,{value:"Module"})),E=e({__name:"NetworkErrorException",setup(c){const t=r(_.NET_WORK_ERROR);return(u,l)=>(s(),o(a(n),{status:t.value},null,8,["status"]))}}),v=Object.freeze(Object.defineProperty({__proto__:null,default:E},Symbol.toStringTag,{value:"Module"}));export{b as N,S,O as a,v as b};
