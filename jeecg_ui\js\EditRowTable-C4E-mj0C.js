var d=(i,o,n)=>new Promise((m,p)=>{var r=t=>{try{a(n.next(t))}catch(e){p(e)}},l=t=>{try{a(n.throw(t))}catch(e){p(e)}},a=t=>t.done?m(t.value):Promise.resolve(t.value).then(r,l);a((n=n.apply(i,o)).next())});import{d as h,f as C,ag as c,aq as b,ar as g,k as f,aD as R}from"./vue-vendor-dy9k-Yad.js";import{u as v}from"./index-BkGZ5fiW.js";import{o as x}from"./select-nVA4yav1.js";import{d as I}from"./table-BDFKJhHv.js";import{t as k}from"./tree-C9AW4Mg9.js";import{h as T}from"./antd-vue-vendor-me9YkNVC.js";import{u as _,a as A}from"./index-CCWaWN5g.js";import{Q as E}from"./componentMap-Bkie1n3v.js";import y from"./BasicTable-xCEZpGLb.js";import"./vxe-table-vendor-B22HppNm.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const M=[{title:"输入框",dataIndex:"name",editRow:!0,editComponentProps:{prefix:"$"},width:150},{title:"默认输入状态",dataIndex:"name7",editRow:!0,width:150},{title:"输入框校验",dataIndex:"name1",editRow:!0,align:"left",editRule:!0,width:150},{title:"输入框函数校验",dataIndex:"name2",editRow:!0,align:"right",editRule:i=>d(null,null,function*(){return i==="2"?"不能输入该值":""})},{title:"数字输入框",dataIndex:"id",editRow:!0,editRule:!0,editComponent:"InputNumber",width:150},{title:"下拉框",dataIndex:"name3",editRow:!0,editComponent:"Select",editComponentProps:{options:[{label:"Option1",value:"1"},{label:"Option2",value:"2"},{label:"Option3",value:"3"}]},width:200},{title:"远程下拉",dataIndex:"name4",editRow:!0,editComponent:"ApiSelect",editComponentProps:{api:x,resultField:"list",labelField:"name",valueField:"id"},width:200},{title:"远程下拉树",dataIndex:"name8",editRow:!0,editComponent:"ApiTreeSelect",editRule:!1,editComponentProps:{api:k,resultField:"list"},width:200},{title:"日期选择",dataIndex:"date",editRow:!0,editComponent:"DatePicker",editComponentProps:{valueFormat:"YYYY-MM-DD",format:"YYYY-MM-DD"},width:150},{title:"时间选择",dataIndex:"time",editRow:!0,editComponent:"TimePicker",editComponentProps:{valueFormat:"HH:mm",format:"HH:mm"},width:100},{title:"勾选框",dataIndex:"name5",editRow:!0,editComponent:"Checkbox",editValueMap:i=>i?"是":"否",width:100},{title:"开关",dataIndex:"name6",editRow:!0,editComponent:"Switch",editValueMap:i=>i?"开":"关",width:100}],P=h({components:{BasicTable:y,TableAction:E},setup(){const{createMessage:i}=_(),o=C(""),[n]=v({title:"可编辑行示例",titleHelpMessage:["本例中修改[数字输入框]这一列时，同一行的[远程下拉]列的当前编辑数据也会同步发生改变"],api:I,columns:M,showIndexColumn:!1,showTableSetting:!0,tableSetting:{fullScreen:!0},actionColumn:{width:160,title:"Action",dataIndex:"action",slots:{customRender:"action"}}});function m(t){var e;o.value=t.key,(e=t.onEdit)==null||e.call(t,!0)}function p(t){var e;o.value="",(e=t.onEdit)==null||e.call(t,!1,!1)}function r(t){return d(this,null,function*(){var s,u;if(i.loading({content:"正在保存...",duration:0,key:"saving"}),yield(s=t.onValid)==null?void 0:s.call(t))try{const w=T(t.editValueRefs);(yield(u=t.onEdit)==null?void 0:u.call(t,!1,!0))&&(o.value=""),i.success({content:"数据已保存",key:"saving"})}catch(w){i.error({content:"保存失败",key:"saving"})}else i.error({content:"请填写正确的数据",key:"saving"})})}function l(t,e){return t.editable?[{label:"保存",onClick:r.bind(null,t,e)},{label:"取消",popConfirm:{title:"是否取消编辑",confirm:p.bind(null,t,e)}}]:[{label:"编辑",disabled:o.value?o.value!==t.key:!1,onClick:m.bind(null,t)}]}function a({column:t,value:e,record:s}){t.dataIndex==="id"&&(s.editValueRefs.name4.value=`${e}`)}return{registerTable:n,handleEdit:m,createActions:l,onEditChange:a}}}),S={class:"p-4"};function Y(i,o,n,m,p,r){const l=c("TableAction"),a=c("BasicTable");return g(),b("div",S,[f(a,{onRegister:i.registerTable,onEditChange:i.onEditChange},{action:R(({record:t,column:e})=>[f(l,{actions:i.createActions(t,e)},null,8,["actions"])]),_:1},8,["onRegister","onEditChange"])])}const Kt=A(P,[["render",Y]]);export{Kt as default};
