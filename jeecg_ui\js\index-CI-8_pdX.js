const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/TypePicker-Dw1RDFSR.js","js/vue-vendor-dy9k-Yad.js","js/antd-vue-vendor-me9YkNVC.js","js/index-CCWaWN5g.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","assets/TypePicker-D240ZADL.css","js/ThemeColorPicker-DTmNyHfi.js","js/index-JbqXEynz.js","js/index-CImCetrx.js","assets/index-BObJM2Lc.css","js/index-LCGLvkB3.js","js/index-De_W6s5g.js","js/index-D6l0IxOU.js","js/useTimeout-CeTdFD_D.js","js/useIntersectionObserver-C4LVxQJW.js","assets/index-zj-Vfn3Q.css","assets/index-Yp9ECMoG.css","js/useHeaderSetting-C-h5S52e.js","js/useMultipleTabSetting-QBbnIi9J.js","assets/ThemeColorPicker-2JwCyMNE.css","js/SettingFooter-DvOnMUKB.js","assets/SettingFooter-D77v2k9f.css","js/SwitchItem-CYWyl1eh.js","assets/SwitchItem-8uYYhcZK.css","js/SelectItem-BYLKy2Pi.js","assets/SelectItem-wjFIEkuf.css","js/InputNumberItem-DWfK4DSG.js","assets/InputNumberItem-D8-ggNMa.css"])))=>i.map(i=>d[i]);
var Ue=Object.defineProperty;var Q=Object.getOwnPropertySymbols;var ye=Object.prototype.hasOwnProperty,he=Object.prototype.propertyIsEnumerable;var Z=(e,t,_)=>t in e?Ue(e,t,{enumerable:!0,configurable:!0,writable:!0,value:_}):e[t]=_,z=(e,t)=>{for(var _ in t||(t={}))ye.call(t,_)&&Z(e,_,t[_]);if(Q)for(var _ of Q(t))he.call(t,_)&&Z(e,_,t[_]);return e};import{d as te,e as be,u as i,k as n,aE as Be,F as G,ag as q,aq as Ge,ar as He}from"./vue-vendor-dy9k-Yad.js";import{B as Pe,a as me}from"./index-JbqXEynz.js";import{V as S}from"./antd-vue-vendor-me9YkNVC.js";import{aj as I,_ as p,N as oe,d6 as H,d8 as J,d9 as F,d3 as f,da as c,cR as O,M as l,db as v,k as w,B as k,ay as We,dc as U,dd as y,de as R,df as N,dg as P,D as ie,dh as Fe,di as we,dj as xe,dk as ke,dl as Xe,cQ as Ke,C as Ve,bX as $e,c as Ye,a as je}from"./index-CCWaWN5g.js";import{useHeaderSetting as Qe}from"./useHeaderSetting-C-h5S52e.js";import{useMultipleTabSetting as Ze}from"./useMultipleTabSetting-QBbnIi9J.js";const ze=I(()=>p(()=>import("./TypePicker-Dw1RDFSR.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),x=I(()=>p(()=>import("./ThemeColorPicker-DTmNyHfi.js"),__vite__mapDeps([7,1,2,3,4,5,8,9,10,11,12,13,14,15,16,17,18,19,20]))),qe=I(()=>p(()=>import("./SettingFooter-DvOnMUKB.js"),__vite__mapDeps([21,1,2,3,4,5,22]))),T=I(()=>p(()=>import("./SwitchItem-CYWyl1eh.js"),__vite__mapDeps([23,1,2,3,4,5,8,9,10,11,12,13,14,15,16,17,18,19,24]))),L=I(()=>p(()=>import("./SelectItem-BYLKy2Pi.js"),__vite__mapDeps([25,1,2,3,4,5,8,9,10,11,12,13,14,15,16,17,18,19,26]))),ee=I(()=>p(()=>import("./InputNumberItem-DWfK4DSG.js"),__vite__mapDeps([27,1,2,3,4,5,8,9,10,11,12,13,14,15,16,17,18,19,28]))),{t:E}=oe();var o=(e=>(e[e.CHANGE_LAYOUT=0]="CHANGE_LAYOUT",e[e.CHANGE_THEME_COLOR=1]="CHANGE_THEME_COLOR",e[e.CHANGE_THEME=2]="CHANGE_THEME",e[e.MENU_HAS_DRAG=3]="MENU_HAS_DRAG",e[e.MENU_ACCORDION=4]="MENU_ACCORDION",e[e.MENU_TRIGGER=5]="MENU_TRIGGER",e[e.MENU_TOP_ALIGN=6]="MENU_TOP_ALIGN",e[e.MENU_COLLAPSED=7]="MENU_COLLAPSED",e[e.MENU_COLLAPSED_SHOW_TITLE=8]="MENU_COLLAPSED_SHOW_TITLE",e[e.MENU_WIDTH=9]="MENU_WIDTH",e[e.MENU_SHOW_SIDEBAR=10]="MENU_SHOW_SIDEBAR",e[e.MENU_THEME=11]="MENU_THEME",e[e.MENU_SPLIT=12]="MENU_SPLIT",e[e.MENU_FIXED=13]="MENU_FIXED",e[e.MENU_CLOSE_MIX_SIDEBAR_ON_CHANGE=14]="MENU_CLOSE_MIX_SIDEBAR_ON_CHANGE",e[e.MENU_TRIGGER_MIX_SIDEBAR=15]="MENU_TRIGGER_MIX_SIDEBAR",e[e.MENU_FIXED_MIX_SIDEBAR=16]="MENU_FIXED_MIX_SIDEBAR",e[e.HEADER_SHOW=17]="HEADER_SHOW",e[e.HEADER_THEME=18]="HEADER_THEME",e[e.HEADER_FIXED=19]="HEADER_FIXED",e[e.HEADER_SEARCH=20]="HEADER_SEARCH",e[e.TABS_SHOW_QUICK=21]="TABS_SHOW_QUICK",e[e.TABS_SHOW_REDO=22]="TABS_SHOW_REDO",e[e.TABS_SHOW=23]="TABS_SHOW",e[e.TABS_SHOW_FOLD=24]="TABS_SHOW_FOLD",e[e.TABS_THEME=25]="TABS_THEME",e[e.LOCK_TIME=26]="LOCK_TIME",e[e.FULL_CONTENT=27]="FULL_CONTENT",e[e.CONTENT_MODE=28]="CONTENT_MODE",e[e.SHOW_BREADCRUMB=29]="SHOW_BREADCRUMB",e[e.SHOW_BREADCRUMB_ICON=30]="SHOW_BREADCRUMB_ICON",e[e.GRAY_MODE=31]="GRAY_MODE",e[e.COLOR_WEAK=32]="COLOR_WEAK",e[e.SHOW_LOGO=33]="SHOW_LOGO",e[e.SHOW_FOOTER=34]="SHOW_FOOTER",e[e.ROUTER_TRANSITION=35]="ROUTER_TRANSITION",e[e.OPEN_PROGRESS=36]="OPEN_PROGRESS",e[e.OPEN_PAGE_LOADING=37]="OPEN_PAGE_LOADING",e[e.OPEN_ROUTE_TRANSITION=38]="OPEN_ROUTE_TRANSITION",e))(o||{});const h=[{value:H.SMOOTH,label:E("layout.setting.tabsThemeSmooth")},{value:H.CARD,label:E("layout.setting.tabsThemeCard")},{value:H.SIMPLE,label:E("layout.setting.tabsThemeSimple")},{value:H.TAG,label:E("layout.setting.tabsThemeTag")}],Je=[{value:J.FULL,label:E("layout.setting.contentModeFull")},{value:J.FIXED,label:E("layout.setting.contentModeFixed")}],ve=[{value:F.CENTER,label:E("layout.setting.topMenuAlignRight")},{value:F.START,label:E("layout.setting.topMenuAlignLeft")},{value:F.END,label:E("layout.setting.topMenuAlignCenter")}],et=e=>[{value:f.NONE,label:E("layout.setting.menuTriggerNone")},{value:f.FOOTER,label:E("layout.setting.menuTriggerBottom")},...e?[]:[{value:f.HEADER,label:E("layout.setting.menuTriggerTop")},{value:f.RIGHT,label:E("layout.setting.menuTriggerRight")}]],tt=[c.ZOOM_FADE,c.FADE,c.ZOOM_OUT,c.FADE_SIDE,c.FADE_BOTTOM,c.FADE_SCALE].map(e=>({label:e,value:e})),ot=[{title:E("layout.setting.menuTypeSidebar"),mode:l.INLINE,type:O.SIDEBAR},{title:E("layout.setting.menuTypeMix"),mode:l.INLINE,type:O.MIX},{title:E("layout.setting.menuTypeTopMenu"),mode:l.HORIZONTAL,type:O.TOP_MENU},{title:E("layout.setting.menuTypeMixSidebar"),mode:l.INLINE,type:O.MIX_SIDEBAR}],Ut=[{value:v.HOVER,label:E("layout.setting.triggerHover")},{value:v.CLICK,label:E("layout.setting.triggerClick")}];function it(e,t){const _=w(t)&&t.type==O.TOP_MENU&&t.mode==l.HORIZONTAL,g=w(t)&&t.type==O.MIX&&t.mode==l.INLINE,M=w(t)&&t.type==O.MIX_SIDEBAR&&t.mode==l.INLINE,a=k().getDarkMode===We.DARK;_?(r(e,t),r(o.HEADER_THEME,U[2]),r(o.CHANGE_THEME_COLOR,y[2]),a&&(R(),N()),r(o.TABS_THEME,h[1].value)):g?(r(e,t),r(o.HEADER_THEME,U[4]),r(o.MENU_THEME,P[3]),a&&(R(),N()),r(o.CHANGE_THEME_COLOR,y[1]),r(o.TABS_THEME,h[1].value)):M?(r(e,t),r(o.CHANGE_THEME_COLOR,y[1]),r(o.HEADER_THEME,U[0]),r(o.MENU_THEME,P[0]),a&&(R(),N()),r(o.TABS_THEME,h[1].value)):(r(e,t),r(o.HEADER_THEME,U[4]),r(o.MENU_THEME,P[7]),a&&(R(),N()),r(o.CHANGE_THEME_COLOR,y[1]),r(o.TABS_THEME,h[1].value))}function r(e,t){const _=k(),g=nt(e,t);_.setProjectConfig(g),e===o.CHANGE_THEME&&(R(),N())}function nt(e,t){const _=k(),{getThemeColor:g,getDarkMode:M}=ie();switch(e){case o.CHANGE_LAYOUT:const{mode:b,type:a,split:D}=t;return{menuSetting:z({mode:b,type:a,collapsed:!1,show:!0,hidden:!1},D===void 0?{split:D}:{})};case o.CHANGE_THEME_COLOR:return g.value===t?{}:(localStorage.setItem(ke,t),Xe(t),{themeColor:t});case o.CHANGE_THEME:return M.value===t?{}:(xe(t),{});case o.MENU_HAS_DRAG:return{menuSetting:{canDrag:t}};case o.MENU_ACCORDION:return{menuSetting:{accordion:t}};case o.MENU_TRIGGER:return{menuSetting:{trigger:t}};case o.MENU_TOP_ALIGN:return{menuSetting:{topMenuAlign:t}};case o.MENU_COLLAPSED:return{menuSetting:{collapsed:t}};case o.MENU_WIDTH:return{menuSetting:{menuWidth:t}};case o.MENU_SHOW_SIDEBAR:return{menuSetting:{show:t}};case o.MENU_COLLAPSED_SHOW_TITLE:return{menuSetting:{collapsedShowTitle:t}};case o.MENU_THEME:return N(t),{menuSetting:{bgColor:t}};case o.MENU_SPLIT:return{menuSetting:{split:t}};case o.MENU_CLOSE_MIX_SIDEBAR_ON_CHANGE:return{menuSetting:{closeMixSidebarOnChange:t}};case o.MENU_FIXED:return{menuSetting:{fixed:t}};case o.MENU_TRIGGER_MIX_SIDEBAR:return{menuSetting:{mixSideTrigger:t}};case o.MENU_FIXED_MIX_SIDEBAR:return{menuSetting:{mixSideFixed:t}};case o.OPEN_PAGE_LOADING:return _.setPageLoading(!1),{transitionSetting:{openPageLoading:t}};case o.ROUTER_TRANSITION:return{transitionSetting:{basicTransition:t}};case o.OPEN_ROUTE_TRANSITION:return{transitionSetting:{enable:t}};case o.OPEN_PROGRESS:return{transitionSetting:{openNProgress:t}};case o.LOCK_TIME:return{lockTime:t};case o.FULL_CONTENT:return{fullContent:t};case o.CONTENT_MODE:return{contentMode:t};case o.SHOW_BREADCRUMB:return{showBreadCrumb:t};case o.SHOW_BREADCRUMB_ICON:return{showBreadCrumbIcon:t};case o.GRAY_MODE:return we(t),{grayMode:t};case o.SHOW_FOOTER:return{showFooter:t};case o.COLOR_WEAK:return Fe(t),{colorWeak:t};case o.SHOW_LOGO:return{showLogo:t};case o.TABS_SHOW_QUICK:return{multiTabsSetting:{showQuick:t}};case o.TABS_SHOW:return{multiTabsSetting:{show:t}};case o.TABS_SHOW_REDO:return{multiTabsSetting:{showRedo:t}};case o.TABS_SHOW_FOLD:return{multiTabsSetting:{showFold:t}};case o.TABS_THEME:return{multiTabsSetting:{theme:t}};case o.HEADER_THEME:return R(t),{headerSetting:{bgColor:t}};case o.HEADER_SEARCH:return{headerSetting:{showSearch:t}};case o.HEADER_FIXED:return{headerSetting:{fixed:t}};case o.HEADER_SHOW:return{headerSetting:{show:t}};default:return{}}}const{t:s}=oe(),st=te({name:"SettingDrawer",setup(e,{attrs:t}){const{getContentMode:_,getShowFooter:g,getShowBreadCrumb:M,getShowBreadCrumbIcon:b,getShowLogo:a,getFullContent:D,getColorWeak:m,getGrayMode:ne,getLockTime:se,getShowDarkModeToggle:X,getThemeColor:_e}=ie(),{getOpenPageLoading:re,getBasicTransition:Ee,getEnableTransition:K,getOpenNProgress:Te}=Ke(),{getIsHorizontal:V,getShowMenu:ae,getMenuType:$,getTrigger:ge,getCollapsedShowTitle:Oe,getMenuFixed:Tt,getCollapsed:Se,getCanDrag:le,getTopMenuAlign:Me,getAccordion:at,getMenuWidth:ue,getMenuBgColor:Ae,getIsTopMenu:ce,getSplit:B,getIsMixSidebar:W,getCloseMixSidebarOnChange:gt,getMixSideTrigger:Ot,getMixSideFixed:St}=Ve(),{getShowHeader:Y,getFixed:lt,getHeaderBgColor:Re,getShowSearch:Mt}=Qe(),{getShowMultipleTab:Ne,getShowQuick:ut,getShowRedo:At,getShowFold:ct,getTabsTheme:Ie}=Ze(),d=be(()=>i(ae)&&!i(V)),C=!1;function pe(){return n(G,null,[n(ze,{menuTypeList:ot,handler:u=>{it(o.CHANGE_LAYOUT,{mode:u.mode,type:u.type,split:i(V)?!1:void 0})},def:i($)},null)])}function De(){return n(x,{colorList:U,def:i(Re),event:o.HEADER_THEME},null)}function de(){return n(x,{colorList:P,def:i(Ae),event:o.MENU_THEME},null)}function Ce(){return n(x,{colorList:y,def:i(_e),event:o.CHANGE_THEME_COLOR},null)}function Le(){let u=i(ge);const j=et(i(B));return j.some(A=>A.value===u)||(u=f.FOOTER),n(G,null,[n(T,{title:s("layout.setting.splitMenu"),event:o.MENU_SPLIT,def:i(B),disabled:!i(d)||i($)!==O.MIX},null),n(L,{title:s("layout.setting.tabsTheme"),event:o.TABS_THEME,def:i(Ie),options:h},null),n(L,{title:s("layout.setting.topMenuLayout"),event:o.MENU_TOP_ALIGN,def:i(Me),options:ve,disabled:!i(Y)||i(B)||!i(ce)&&!i(B)||i(W)},null),n(L,{title:s("layout.setting.menuCollapseButton"),event:o.MENU_TRIGGER,def:u,options:j,disabled:!i(d)||i(W)},null),C&&n(L,{title:s("layout.setting.contentMode"),event:o.CONTENT_MODE,def:i(_),options:Je},null),C&&n(ee,{title:s("layout.setting.autoScreenLock"),min:0,event:o.LOCK_TIME,defaultValue:i(se),formatter:A=>parseInt(A)===0?`0(${s("layout.setting.notAutoScreenLock")})`:`${A}${s("layout.setting.minute")}`},null),C&&n(ee,{title:s("layout.setting.expandedMenuWidth"),max:600,min:100,step:10,event:o.MENU_WIDTH,disabled:!i(d),defaultValue:i(ue),formatter:A=>`${parseInt(A)}px`},null)])}function fe(){return n(G,null,[C&&n(T,{title:s("layout.setting.menuDrag"),event:o.MENU_HAS_DRAG,def:i(le),disabled:!i(d)},null),C&&n(T,{title:s("layout.setting.collapseMenuDisplayName"),event:o.MENU_COLLAPSED_SHOW_TITLE,def:i(Oe),disabled:!i(d)||!i(Se)||i(W)},null),n(T,{title:s("layout.setting.tabs"),event:o.TABS_SHOW,def:i(Ne)},null),n(T,{title:s("layout.setting.breadcrumb"),event:o.SHOW_BREADCRUMB,def:i(M),disabled:!i(Y)},null),n(T,{title:s("layout.setting.footer"),event:o.SHOW_FOOTER,def:i(g)},null),n(T,{title:s("layout.setting.grayMode"),event:o.GRAY_MODE,def:i(ne)},null),n(T,{title:s("layout.setting.colorWeak"),event:o.COLOR_WEAK,def:i(m)},null)])}function Rt(){return n(G,null,[n(T,{title:s("layout.setting.progress"),event:o.OPEN_PROGRESS,def:i(Te)},null),n(T,{title:s("layout.setting.switchLoading"),event:o.OPEN_PAGE_LOADING,def:i(re)},null),n(T,{title:s("layout.setting.switchAnimation"),event:o.OPEN_ROUTE_TRANSITION,def:i(K)},null),n(L,{title:s("layout.setting.animationType"),event:o.ROUTER_TRANSITION,def:i(Ee),options:tt,disabled:!i(K)},null)])}return()=>n(Pe,Be(t,{title:s("layout.setting.drawerTitle"),width:330,class:"setting-drawer"}),{default:()=>[i(X)&&n(S,null,{default:()=>s("layout.setting.darkMode")}),i(X)&&n($e,{class:"mx-auto"},null),n(S,null,{default:()=>s("layout.setting.navMode")}),pe(),n(S,null,{default:()=>s("layout.setting.sysTheme")}),Ce(),n(S,null,{default:()=>s("layout.setting.headerTheme")}),De(),n(S,null,{default:()=>s("layout.setting.sidebarTheme")}),de(),n(S,null,{default:()=>s("layout.setting.interfaceFunction")}),Le(),fe(),n(S,null,null),n(qe,null,null)]})}}),_t=te({name:"SettingButton",components:{SettingDrawer:st,Icon:Ye},setup(){const[e,{openDrawer:t}]=me();return{register:e,openDrawer:t}}});function rt(e,t,_,g,M,b){const a=q("Icon"),D=q("SettingDrawer");return He(),Ge("div",{onClick:t[0]||(t[0]=m=>e.openDrawer(!0))},[n(a,{icon:"ion:settings-outline"}),n(D,{onRegister:e.register},null,8,["onRegister"])])}const Et=je(_t,[["render",rt]]),yt=Object.freeze(Object.defineProperty({__proto__:null,default:Et},Symbol.toStringTag,{value:"Module"}));export{r as b,yt as i};
