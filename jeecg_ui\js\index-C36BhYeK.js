import{d as v,f as n,ag as o,aB as r,ar as i,aD as s,at as l,k as p,G as S,aQ as f,q as T,B as _}from"./vue-vendor-dy9k-Yad.js";import{w as R}from"./antd-vue-vendor-me9YkNVC.js";import{P as X}from"./index-CtJ0w2CP.js";import{E as Y,a as h,S as w,b as x,c as b,d as k,e as C,f as g,g as y,h as B,i as E,j as $,F}from"./index-De_W6s5g.js";import{a as P}from"./index-CCWaWN5g.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const D=["Fade","Scale","SlideY","ScrollY","SlideYReverse","ScrollYReverse","SlideX","ScrollX","SlideXReverse","ScrollXReverse","ScaleRotate","ExpandX","Expand"],N=D.map(e=>({label:e,value:e,key:e})),V=v({components:{Select:R,PageWrapper:X,FadeTransition:F,ScaleTransition:$,SlideYTransition:E,ScrollYTransition:B,SlideYReverseTransition:y,ScrollYReverseTransition:g,SlideXTransition:C,ScrollXTransition:k,SlideXReverseTransition:b,ScrollXReverseTransition:x,ScaleRotateTransition:w,ExpandXTransition:h,ExpandTransition:Y},setup(){const e=n("Fade"),a=n(!0);function t(){a.value=!1,setTimeout(()=>{a.value=!0},300)}return{options:N,value:e,start:t,show:a}}}),W={class:"flex"},j={class:"box"};function q(e,a,t,G,I,L){const c=o("Select"),d=o("a-button"),u=o("PageWrapper");return i(),r(u,{title:"动画组件示例"},{default:s(()=>[l("div",W,[p(c,{options:e.options,value:e.value,"onUpdate:value":a[0]||(a[0]=m=>e.value=m),placeholder:"选择动画",style:{width:"150px"}},null,8,["options","value"]),p(d,{type:"primary",class:"ml-4",onClick:e.start},{default:s(()=>a[1]||(a[1]=[S(" start ")])),_:1,__:[1]},8,["onClick"])]),(i(),r(f(`${e.value}Transition`),null,{default:s(()=>[T(l("div",j,null,512),[[_,e.show]])]),_:1}))]),_:1})}const ae=P(V,[["render",q],["__scopeId","data-v-b269e5a1"]]);export{ae as default};
