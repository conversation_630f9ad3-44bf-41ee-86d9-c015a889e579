import{d as m,aB as p,ar as a,u as o}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as n}from"./useForm-CgkFTrrO.js";import{B as l}from"./BasicForm-DBcXiHk0.js";import{a as s}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const c=m({__name:"BasicFormSearch",setup(u){const r=[{field:"name",label:"姓名",component:"Input"},{field:"hobby",label:"爱好",component:"Input"},{field:"birthday",label:"生日",component:"DatePicker"},{field:"joinTime",label:"入职时间",component:"RangePicker",componentProps:{valueType:"Date"}},{field:"workYears",label:"工作年份",component:"JRangeNumber"},{field:"sex",label:"性别",component:"Select",componentProps:{options:[{label:"男",value:"1"},{label:"女",value:"2"}]}},{field:"marital",label:"婚姻状况",component:"RadioGroup",componentProps:{options:[{label:"未婚",value:"1"},{label:"已婚",value:"2"}]}}],[t]=n({fieldMapToTime:[["joinTime",["joinTime_begin","joinTime_end"],"YYYY-MM-DD"]],schemas:r,showAdvancedButton:!0,autoAdvancedCol:3,alwaysShowLines:2,fieldMapToNumber:[["workYears",["workYears_begin","workYears_end"]]],baseColProps:{span:12}});function e(i){}return(i,d)=>(a(),p(o(l),{onRegister:o(t),onSubmit:e,style:{"margin-top":"20px"}},null,8,["onRegister"]))}}),fo=s(c,[["__scopeId","data-v-79fa214d"]]);export{fo as default};
