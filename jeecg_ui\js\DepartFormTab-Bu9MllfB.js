var S=Object.defineProperty;var x=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var B=(r,o,t)=>o in r?S(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,g=(r,o)=>{for(var t in o||(o={}))V.call(o,t)&&B(r,t,o[t]);if(x)for(var t of x(o))N.call(o,t)&&B(r,t,o[t]);return r};var m=(r,o,t)=>new Promise((u,a)=>{var n=i=>{try{s(t.next(i))}catch(p){a(p)}},d=i=>{try{s(t.throw(i))}catch(p){a(p)}},s=i=>i.done?u(i.value):Promise.resolve(i.value).then(n,d);s((t=t.apply(r,o)).next())});import{d as R,f as b,e as A,o as G,w as y,u as l,ag as D,aB as U,ar as z,aD as _,k as v,at as j,as as M,G as h}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{b as $}from"./depart.api-BoGnt_ZX.js";import{o as O,u as q}from"./depart.data-DDyAushI.js";import{F as E}from"./index-CCWaWN5g.js";import{u as H}from"./useForm-CgkFTrrO.js";import{B as J}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const K={class:"j-box-bottom-button offset-20",style:{"margin-top":"30px"}},Wt=R({__name:"DepartFormTab",props:{data:{type:Object,default:()=>({})},rootTreeData:{type:Array,default:()=>[]}},emits:["success"],setup(r,{emit:o}){const{prefixCls:t}=E("j-depart-form-content"),u=o,a=r,n=b(!1),d=b(!0),s=b({}),[i,{resetFields:p,setFieldsValue:w,validate:k,updateSchema:f}]=H({schemas:q().basicFormSchema,showActionButtonGroup:!1}),C=A(()=>{var e;return(e=a==null?void 0:a.data)!=null&&e.parentId?O.child:O.root});G(()=>{f([{field:"parentId",componentProps:{disabled:!0}},{field:"orgCode",componentProps:{disabled:!0}}]),y(()=>a.data,()=>m(null,null,function*(){let e=l(a.data);typeof e!="object"&&(e={}),s.value=e,yield p(),yield w(g({},e))}),{deep:!0,immediate:!0}),y(()=>a.rootTreeData,()=>m(null,null,function*(){f([{field:"parentId",componentProps:{treeData:a.rootTreeData}}])}),{deep:!0,immediate:!0}),y(C,()=>m(null,null,function*(){f([{field:"orgCategory",componentProps:{options:C.value}}])}),{immediate:!0})});function I(){return m(this,null,function*(){yield p(),yield w(g({},s.value))})}function T(){return m(this,null,function*(){try{n.value=!0;let e=yield k();e=Object.assign({},s.value,e),yield $(e,d.value),u("success"),Object.assign(s.value,e)}finally{n.value=!1}})}return(e,c)=>{const F=D("a-button"),P=D("a-spin");return z(),U(P,{spinning:n.value},{default:_(()=>[v(l(J),{onRegister:l(i)},null,8,["onRegister"]),j("div",K,[j("div",{class:M(["j-box-bottom-button-float",[`${l(t)}`]])},[v(F,{preIcon:"ant-design:sync-outlined",onClick:I},{default:_(()=>c[0]||(c[0]=[h("重置")])),_:1,__:[0]}),v(F,{type:"primary",preIcon:"ant-design:save-filled",onClick:T},{default:_(()=>c[1]||(c[1]=[h("保存")])),_:1,__:[1]})],2)])]),_:1},8,["spinning"])}}});export{Wt as default};
