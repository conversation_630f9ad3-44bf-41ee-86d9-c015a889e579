var M=(h,t,l)=>new Promise((e,v)=>{var n=a=>{try{s(l.next(a))}catch(y){v(y)}},p=a=>{try{s(l.throw(a))}catch(y){v(y)}},s=a=>a.done?e(a.value):Promise.resolve(a.value).then(n,p);s((l=l.apply(h,t)).next())});import{f as b,ag as g,aq as u,ar as _,k as r,aD as f,ah as C,A as B,G as N,at as i,as as U,au as D}from"./vue-vendor-dy9k-Yad.js";import{I as A}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{B as K}from"./BasicForm-DBcXiHk0.js";import{u as V,ac as E,$ as q,bI as j,bJ as R,a as z}from"./index-CCWaWN5g.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const F={name:"AiAppSendModal",components:{BasicForm:K,BasicModal:A},emits:["success","register"],setup(h,{emit:t}){const l=b("嵌入网站"),e=V(),v=b("web"),n=b({}),p=b("800px"),s=b(1),[a,{closeModal:y,setModalProps:w}]=E(o=>M(null,null,function*(){v.value=o.type,n.value=o.data,n.value.menu="/ai/chat/"+o.data.id,s.value=1;let c=220;o.type==="web"?(l.value="嵌入网站",p.value="640px",c=500):(l.value="配置菜单",p.value="500px"),w({height:c,bodyStyle:{padding:"10px"}})}));function k(){x(n.value.menu)}function m(){const o=`INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external)
                               VALUES ('${q()}', NULL, '${n.value.name}', '${n.value.menu}', '1', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 0, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', null, NULL, NULL, 0)`;x(o)}function I(o){let c=document.location.protocol+"//"+window.location.host;if(o===1)return`<iframe
   src="`+c+"/ai/app/chat/"+n.value.id+`"
   style="width: 100%; height: 100%;">
</iframe>`;{let L="/src/views/super/airag/aiapp/chat/js/chat.js";j()||(L="/chat/chat.js");let d="<script src="+c+L+' id="e7e007dd52f67fe36365eff636bbffbd"><\/script>';return d+=`
 <script>
`,d+=`    createAiChat({
       appId:"`+n.value.id+`",
`,d+=`       // 支持top-left左上, top-right右上, bottom-left左下, bottom-right右下
`,d+=`       iconPosition:"bottom-right"
`,d+=`    })
`,d+=" <\/script>",d}}function S(o){x(I(o))}function x(o){const c=R(o);return c?e.createMessage.success("复制成功！"):e.createMessage.error("复制失败！"),c}function T(o){s.value=o}return{registerModal:a,title:l,type:v,appData:n,copySql:m,copyMenu:k,width:p,copyIframe:S,getIframeText:I,activeKey:s,handleImageClick:T}}},O="/assets/webEmbedded-CZQ1vnZk.png",P="/assets/iconWebEmbedded-Lzlg_TVw.png",Q={class:"p-2"},Z={key:0},G={key:1,class:"web"},H={style:{display:"flex",margin:"0 auto"}},J={key:0,class:"web-title"},W={key:1,class:"web-title"},X={key:2,class:"web-code"},Y={class:"web-code-title"},$={class:"web-code-iframe"},ee={key:3,class:"web-code"},te={class:"web-code-title"},oe={class:"web-code-iframe"};function ie(h,t,l,e,v,n){const p=g("a-input"),s=g("a-form-item"),a=g("a-button"),y=g("a-form"),w=g("Icon"),k=g("BasicModal");return _(),u("div",Q,[r(k,{destroyOnClose:"",onRegister:e.registerModal,canFullscreen:!1,width:e.width,title:e.title,footer:null},{default:f(()=>[e.type==="menu"?(_(),u("div",Z,[r(y,{layout:"vertical",model:e.appData},{default:f(()=>[r(s,{label:"菜单名称"},{default:f(()=>[r(p,{value:e.appData.name,"onUpdate:value":t[0]||(t[0]=m=>e.appData.name=m),readonly:""},null,8,["value"])]),_:1}),r(s,{label:"菜单地址"},{default:f(()=>[r(p,{value:e.appData.menu,"onUpdate:value":t[1]||(t[1]=m=>e.appData.menu=m),readonly:""},null,8,["value"])]),_:1}),r(s,{style:{"text-align":"right"}},{default:f(()=>[r(a,{onClick:B(e.copyMenu,["prevent"])},{default:f(()=>t[6]||(t[6]=[N("复制菜单")])),_:1,__:[6]},8,["onClick"]),r(a,{type:"primary",style:{"margin-left":"10px"},onClick:e.copySql},{default:f(()=>t[7]||(t[7]=[N("复制SQL")])),_:1,__:[7]},8,["onClick"])]),_:1})]),_:1},8,["model"])])):e.type==="web"?(_(),u("div",G,[i("div",H,[i("div",{class:U([e.activeKey===1?"active":"","web-img"]),onClick:t[2]||(t[2]=m=>e.handleImageClick(1))},t[8]||(t[8]=[i("img",{src:O},null,-1)]),2),i("div",{style:{"margin-left":"10px"},class:U([e.activeKey===2?"active":"","web-img"]),onClick:t[3]||(t[3]=m=>e.handleImageClick(2))},t[9]||(t[9]=[i("img",{src:P},null,-1)]),2)]),e.activeKey===1?(_(),u("div",J," 将以下 iframe 嵌入到你的网站中的目标位置 ")):(_(),u("div",W," 将以下 script 添加到网页的body区域中 ")),e.activeKey===1?(_(),u("div",X,[i("div",Y,[t[10]||(t[10]=i("div",{class:"web-code-desc"}," html ",-1)),r(w,{class:"pointer",icon:"ant-design:copy-outlined",onClick:t[4]||(t[4]=m=>e.copyIframe(1))})]),i("div",$,[i("pre",null," "+D(e.getIframeText(1))+" ",1)])])):C("",!0),e.activeKey===2?(_(),u("div",ee,[i("div",te,[t[11]||(t[11]=i("div",{class:"web-code-desc"}," html ",-1)),r(w,{class:"pointer",icon:"ant-design:copy-outlined",onClick:t[5]||(t[5]=m=>e.copyIframe(2))})]),i("div",oe,[i("pre",null," "+D(e.getIframeText(2))+" ",1)])])):C("",!0)])):C("",!0)]),_:1},8,["onRegister","width","title"])])}const We=z(F,[["render",ie],["__scopeId","data-v-c3393a34"]]);export{We as default};
