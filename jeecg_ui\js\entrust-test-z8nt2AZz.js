import{d,ap as c,f as _,ag as v,aq as C,ar as b,at as t,k as l,aD as s,G as r,u as x}from"./vue-vendor-dy9k-Yad.js";import{f as e}from"./antd-vue-vendor-me9YkNVC.js";import{C as k}from"./index-Diw57m_E.js";import{a as E}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const V={class:"entrust-test"},w={class:"test-buttons"},B=d({__name:"entrust-test",setup(M){const N=c(),n=_(!1),i=()=>{n.value=!0},u=()=>{n.value=!1,e.success("已选择自主服务")},p=()=>{n.value=!1,e.info("已放弃自主服务，跳转到增值委托页面")},m=()=>{e.info("跳转到增值委托页面")};return(T,o)=>{const a=v("a-button");return b(),C("div",V,[o[4]||(o[4]=t("h2",null,"自主委托弹窗测试",-1)),t("div",w,[l(a,{type:"primary",onClick:i},{default:s(()=>o[1]||(o[1]=[r("模拟进入自主委托页面")])),_:1,__:[1]}),l(a,{onClick:m},{default:s(()=>o[2]||(o[2]=[r("跳转到增值委托")])),_:1,__:[2]})]),l(x(k),{open:n.value,"onUpdate:open":o[0]||(o[0]=f=>n.value=f),title:"自主委托",width:"600","cancel-text":"放弃","confirm-text":"确认","mask-closable":!1,onConfirm:u,onCancel:p},{default:s(()=>o[3]||(o[3]=[t("div",{class:"confirm-modal-content"},[t("p",null,"我方仅将客户资产信息在我网进行公示，我司不会对客户所发布信息做任何协助"),t("p",null,"请确认是否选择自主服务")],-1)])),_:1,__:[3]},8,["open"]),o[5]||(o[5]=t("div",{class:"test-info"},[t("h3",null,"测试说明"),t("ul",null,[t("li",null,'✅ 点击"模拟进入自主委托页面"按钮会弹出确认弹窗'),t("li",null,'✅ 弹窗标题为"自主委托"'),t("li",null,"✅ 弹窗内容包含提示信息"),t("li",null,'✅ 按钮文本为"放弃"和"确认"'),t("li",null,'✅ 点击"放弃"会跳转到增值委托页面'),t("li",null,'✅ 点击"确认"会关闭弹窗并显示成功消息'),t("li",null,"✅ 弹窗不能通过点击遮罩关闭")])],-1))])}}}),K=E(B,[["__scopeId","data-v-7ab99d1a"]]);export{K as default};
