import{d as x,f as C,r as P,o as R,ag as S,aq as s,ar as i,at as o,k as r,ah as w,au as p,as as D,u as l,F as E}from"./vue-vendor-dy9k-Yad.js";import{g as T}from"./UserSetting.api-BJ086Ekj.js";import{F as U,ah as B,ad as c}from"./index-CCWaWN5g.js";import $ from"./UserPhoneModal-CF_2UuDe.js";import F from"./UserEmailModal-8eAOV2Ud.js";import N from"./UserPasswordModal-DTQupZFv.js";import"./index-Diw57m_E.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";import"./CustomModal-BakuIxQv.js";const V={class:"account-row-item clearfix"},I={key:0,class:"gray"},W={class:"account-row-item clearfix"},j={class:"gray"},q={class:"account-row-item"},ye=x({__name:"AccountSetting",setup(z){const{prefixCls:m}=U("j-user-account-setting-container"),e=C([]),A=B(),[d,{openModal:u}]=c(),[f,{openModal:g}]=c(),[v,{openModal:h}]=c(),G=P({bindWechat:!1,name:"昵称"});function t(){T().then(a=>{a.success&&(e.value=a.result,a.result.phone&&(e.value.phoneText=a.result.phone.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2")))})}function _(){u(!0,{record:{phone:e.value.phone,username:e.value.username,id:e.value.id,phoneText:e.value.phoneText}})}function k(){u(!0,{record:{username:e.value.username,id:e.value.id}})}function y(){g(!0,{record:{email:e.value.email,id:e.value.id}})}function M(){h(!0,{record:{username:e.value.username}})}function H(){}function J(){}function K(){}function L(){}function O(){}return R(()=>{t()}),(a,n)=>{const b=S("Icon");return i(),s(E,null,[o("div",{class:D([`${l(m)}`])},[n[3]||(n[3]=o("div",{class:"my-account"},"账户",-1)),o("div",V,[n[0]||(n[0]=o("div",{class:"account-label gray-75"},"手机",-1)),e.value.phoneText?(i(),s("span",I,p(e.value.phoneText),1)):w("",!0),e.value.phone?(i(),s("span",{key:1,class:"pointer blue-e5 phone-margin",onClick:_},"修改")):(i(),s("span",{key:2,class:"pointer blue-e5 phone-margin",onClick:k},"绑定"))]),o("div",W,[n[1]||(n[1]=o("div",{class:"account-label gray-75"},"邮箱",-1)),o("span",j,p(e.value.email?e.value.email:"未填写"),1),o("span",{class:"pointer blue-e5 phone-margin",onClick:y},"修改")]),o("div",q,[n[2]||(n[2]=o("div",{class:"account-label gray-75"},"密码",-1)),r(b,{icon:"ant-design:lock-outlined",style:{color:"#9e9e9e"}}),o("span",{class:"pointer blue-e5",style:{"margin-left":"10px"},onClick:M},"修改")])],2),r($,{onRegister:l(d),onSuccess:t},null,8,["onRegister"]),r(F,{onRegister:l(f),onSuccess:t},null,8,["onRegister"]),r(N,{onRegister:l(v),onSuccess:t},null,8,["onRegister"])],64)}}});export{ye as default};
