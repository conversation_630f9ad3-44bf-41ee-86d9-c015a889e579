var uo=Object.defineProperty,mo=Object.defineProperties;var po=Object.getOwnPropertyDescriptors;var J=Object.getOwnPropertySymbols;var go=Object.prototype.hasOwnProperty,fo=Object.prototype.propertyIsEnumerable;var X=(u,l,t)=>l in u?uo(u,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[l]=t,Z=(u,l)=>{for(var t in l||(l={}))go.call(l,t)&&X(u,t,l[t]);if(J)for(var t of J(l))fo.call(l,t)&&X(u,t,l[t]);return u},Q=(u,l)=>mo(u,po(l));var M=(u,l,t)=>new Promise((C,f)=>{var I=v=>{try{R(t.next(v))}catch(p){f(p)}},e=v=>{try{R(t.throw(v))}catch(p){f(p)}},R=v=>v.done?C(v.value):Promise.resolve(v.value).then(I,e);R((t=t.apply(u,l)).next())});import{d as eo,r as K,f as m,o as vo,ag as D,aq as L,ar as y,k as a,q as F,u as s,B as E,at as o,as as b,au as _,aB as Y,aD as w,aO as oo,G as _o,J as ho}from"./vue-vendor-dy9k-Yad.js";import{F as yo,ah as Co,bV as ko,ap as xo,ad as bo,bW as wo,bX as So,N as To,bZ as Mo,u as Lo,b6 as Io,aV as Ro,bS as Bo,a as Do}from"./index-CCWaWN5g.js";import{i as Fo}from"./checkcode-DLY3GIII.js";import Eo from"./ThirdModal-1GWAIf70.js";import No from"./MiniForgotpad-C1sVJiZ9.js";import{a as Oo,M as Uo}from"./MiniRegister-DIKQ4chK.js";import Po from"./MiniCodelogin-PG-8tPlm.js";import{bF as $o}from"./antd-vue-vendor-me9YkNVC.js";import Ko from"./CaptchaModal-CFhSDiLW.js";import"./index-Diw57m_E.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./useThirdLogin-C4qxd7Ms.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-C1YxH9KC.js";const qo={class:"aui-content"},Ao={class:"aui-container"},Go={class:"aui-form"},Ho={class:"aui-image"},Vo={class:"aui-image-text"},jo=["src"],Wo={class:"aui-formBox"},zo={class:"aui-formWell"},Jo={class:"aui-flex aui-form-nav investment_title"},Xo={class:"aui-form-box",style:{height:"180px","margin-top":"50px"}},Zo={class:"aui-account"},Qo={class:"aui-inputClear"},Yo={class:"aui-inputClear"},oe={class:"aui-inputClear"},ee={class:"aui-code"},se=["src"],ie=["src"],te={class:"aui-flex"},ne={class:"aui-flex-box"},ae={class:"aui-choice"},le={style:{"margin-left":"5px"}},re={class:"aui-forget"},ce={class:"aui-account phone"},ue={class:"aui-inputClear phoneClear"},de={class:"aui-inputClear"},me={key:1,class:"aui-code"},pe={class:"aui-get-code code-shape"},ge={class:"aui-formButton"},fe={class:"aui-flex"},ve={class:"aui-flex"},_e=eo({name:"login-mini"}),he=eo(Q(Z({},_e),{props:{sessionTimeout:{type:Boolean}},setup(u){const l=$o({scriptUrl:"//at.alicdn.com/t/font_2316098_umqusozousr.js"}),{prefixCls:t}=yo("mini-login"),{notification:C,createMessage:f}=Lo(),I=Co(),{t:e}=To(),v=ko().getShowPicker,p=K({randCodeImage:"",requestCodeSuccess:!1,checkKey:null}),q=m("0"),h=m("accountLogin"),g=m("login"),r=K({inputCode:"",username:"",password:""}),d=K({mobile:"",smscode:""}),so=m(),A=m(),G=m(),N=m(!0),k=m(60),B=m(null),H=m(),V=m(),S=m(!1),{getIsMobile:ye}=xo(),[io,{openModal:to}]=bo();function T(){r.inputCode="",p.checkKey=new Date().getTime()+Math.random().toString(36).slice(-4),Mo(p.checkKey).then(n=>{p.randCodeImage=n,p.requestCodeSuccess=!0})}function j(n){h.value=n}function O(){return M(this,null,function*(){s(h)==="accountLogin"?no():ao()})}function no(){return M(this,null,function*(){if(!r.username){f.warn(e("sys.login.accountPlaceholder"));return}if(!r.password){f.warn(e("sys.login.passwordPlaceholder"));return}try{S.value=!0;const{userInfo:n}=yield I.login(ho({password:r.password,username:r.username,captcha:r.inputCode,checkKey:p.checkKey,loginSource:0,mode:"none"}));n&&C.success({message:e("sys.login.loginSuccessTitle"),description:`${e("sys.login.loginSuccessDesc")}: ${n.realname}`,duration:3})}catch(n){C.error({message:e("sys.api.errorTip"),description:n.message||e("sys.login.networkExceptionMsg"),duration:3}),T()}finally{S.value=!1}})}function ao(){return M(this,null,function*(){if(!d.mobile){f.warn(e("sys.login.mobilePlaceholder"));return}if(!d.smscode){f.warn(e("sys.login.smsPlaceholder"));return}try{S.value=!0;const{userInfo:n}=yield I.phoneLogin({mobile:d.mobile,captcha:d.smscode,loginSource:0,mode:"none"});n&&C.success({message:e("sys.login.loginSuccessTitle"),description:`${e("sys.login.loginSuccessDesc")}: ${n.realname}`,duration:3})}catch(n){C.error({message:e("sys.api.errorTip"),description:n.message||e("sys.login.networkExceptionMsg"),duration:3})}finally{S.value=!1}})}function W(){return M(this,null,function*(){if(!d.mobile){f.warn(e("sys.login.mobilePlaceholder"));return}(yield Io({mobile:d.mobile,smsmode:Bo.FORGET_PASSWORD}).catch(i=>{i.code===Ro.PHONE_SMS_FAIL_CODE&&to(!0,{})}))&&(s(B)||(k.value=60,N.value=!1,B.value=setInterval(()=>{s(k)>0&&s(k)<=60?k.value=k.value-1:(N.value=!0,clearInterval(s(B)),B.value=null)},1e3)))})}function Ce(n){A.value.onThirdLogin(n)}function lo(){g.value="forgot",setTimeout(()=>{H.value.initForm()},300)}function U(){h.value="accountLogin",g.value="login"}function P(n){Object.assign(r,n),Object.assign(d,{mobile:"",smscode:""}),g.value="login",h.value="accountLogin",T()}function ro(){g.value="register",setTimeout(()=>{V.value.initForm()},300)}function ke(){g.value="codeLogin",setTimeout(()=>{G.value.initFrom()},300)}return vo(()=>{T()}),(n,i)=>{const x=D("a-input"),$=D("a-form-item"),z=D("a-form"),co=D("a-button");return y(),L("div",{class:b([s(t),"login-background-img"])},[a(s(wo),{class:"absolute top-4 right-4 enter-x xl:text-gray-600",showText:!1}),a(s(So),{class:"absolute top-3 right-7 enter-x"}),F(o("div",null,[o("div",qo,[o("div",Ao,[o("div",Go,[o("div",Ho,[o("div",Vo,[o("img",{src:s(Oo)},null,8,jo)])]),o("div",Wo,[o("div",zo,[o("div",Jo,[o("div",{class:b(["aui-flex-box",h.value==="accountLogin"?"activeNav on":""]),onClick:i[0]||(i[0]=c=>j("accountLogin"))},_(s(e)("sys.login.signInFormTitle")),3),o("div",{class:b(["aui-flex-box",h.value==="phoneLogin"?"activeNav on":""]),onClick:i[1]||(i[1]=c=>j("phoneLogin"))},_(s(e)("sys.login.mobileSignInFormTitle")),3)]),o("div",Xo,[h.value==="accountLogin"?(y(),Y(z,{key:0,ref_key:"loginRef",ref:so,model:r,onKeyup:oo(O,["enter","native"])},{default:w(()=>[o("div",Zo,[o("div",Qo,[i[8]||(i[8]=o("i",{class:"icon icon-code"},null,-1)),a($,null,{default:w(()=>[a(x,{class:"fix-auto-fill",placeholder:s(e)("sys.login.userName"),value:r.username,"onUpdate:value":i[2]||(i[2]=c=>r.username=c)},null,8,["placeholder","value"])]),_:1})]),o("div",Yo,[i[9]||(i[9]=o("i",{class:"icon icon-password"},null,-1)),a($,null,{default:w(()=>[a(x,{class:"fix-auto-fill",type:"password",placeholder:s(e)("sys.login.password"),value:r.password,"onUpdate:value":i[3]||(i[3]=c=>r.password=c)},null,8,["placeholder","value"])]),_:1})]),o("div",oe,[i[10]||(i[10]=o("i",{class:"icon icon-code"},null,-1)),a($,null,{default:w(()=>[a(x,{class:"fix-auto-fill",type:"text",placeholder:s(e)("sys.login.inputCode"),value:r.inputCode,"onUpdate:value":i[4]||(i[4]=c=>r.inputCode=c)},null,8,["placeholder","value"])]),_:1}),o("div",ee,[p.requestCodeSuccess?(y(),L("img",{key:0,src:p.randCodeImage,onClick:T},null,8,se)):(y(),L("img",{key:1,style:{"margin-top":"2px","max-width":"initial"},src:s(Fo),onClick:T},null,8,ie))])]),o("div",te,[o("div",ne,[o("div",ae,[a(x,{class:"fix-auto-fill",type:"checkbox",value:q.value,"onUpdate:value":i[5]||(i[5]=c=>q.value=c)},null,8,["value"]),o("span",le,_(s(e)("sys.login.rememberMe")),1)])]),o("div",re,[o("a",{onClick:lo},_(s(e)("sys.login.forgetPassword")),1)])])])]),_:1},8,["model"])):(y(),Y(z,{key:1,ref:"phoneFormRef",model:d,onKeyup:oo(O,["enter","native"])},{default:w(()=>[o("div",ce,[o("div",ue,[a(x,{class:"fix-auto-fill",placeholder:s(e)("sys.login.mobile"),value:d.mobile,"onUpdate:value":i[6]||(i[6]=c=>d.mobile=c)},null,8,["placeholder","value"])]),o("div",de,[a(x,{class:"fix-auto-fill",maxlength:6,placeholder:s(e)("sys.login.smsCode"),value:d.smscode,"onUpdate:value":i[7]||(i[7]=c=>d.smscode=c)},null,8,["placeholder","value"]),N.value?(y(),L("div",{key:0,class:"aui-code",onClick:W},[o("a",null,_(s(e)("component.countdown.normalText")),1)])):(y(),L("div",me,[o("span",pe,_(s(e)("component.countdown.sendText",[s(k.value)])),1)]))])])]),_:1},8,["model"]))]),o("div",ge,[o("div",fe,[a(co,{loading:S.value,class:"aui-link-login",type:"primary",onClick:O},{default:w(()=>[_o(_(s(e)("sys.login.loginButton")),1)]),_:1},8,["loading"])]),o("div",ve,[o("a",{class:"aui-linek-code aui-flex-box",onClick:ro},_(s(e)("sys.login.registerButton")),1)])])])])])])])],512),[[E,g.value==="login"]]),F(o("div",{class:b(`${s(t)}-form`)},[a(No,{ref_key:"forgotRef",ref:H,onGoBack:U,onSuccess:P},null,512)],2),[[E,g.value==="forgot"]]),F(o("div",{class:b(`${s(t)}-form`)},[a(Uo,{ref_key:"registerRef",ref:V,onGoBack:U,onSuccess:P},null,512)],2),[[E,g.value==="register"]]),F(o("div",{class:b(`${s(t)}-form`)},[a(Po,{ref_key:"codeRef",ref:G,onGoBack:U,onSuccess:P},null,512)],2),[[E,g.value==="codeLogin"]]),a(Eo,{ref_key:"thirdModalRef",ref:A},null,512),a(Ko,{onRegister:s(io),onOk:W},null,8,["onRegister"])],2)}}})),Bs=Do(he,[["__scopeId","data-v-60877481"]]);export{Bs as default};
