var S=(w,l,p)=>new Promise((e,a)=>{var c=n=>{try{i(p.next(n))}catch(r){a(r)}},u=n=>{try{i(p.throw(n))}catch(r){a(r)}},i=n=>n.done?e(n.value):Promise.resolve(n.value).then(c,u);i((p=p.apply(w,l)).next())});import{f as d,ag as h,aq as I,ar as f,k,aD as _,at as g,ah as A,aB as B,F as T,aC as V,aA as F,au as D,A as L,G as U}from"./vue-vendor-dy9k-Yad.js";import{I as q}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{x as R,h as P}from"./antd-vue-vendor-me9YkNVC.js";import{l as j}from"./AiKnowledgeBase.api-Dgaf5KfS.js";import{_ as G}from"./knowledge-BXTupIwn.js";import{ac as H,a as J}from"./index-CCWaWN5g.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const Q={name:"AiAppAddKnowledgeModal",components:{Pagination:R,BasicModal:q},emits:["success","register"],setup(w,{emit:l}){const p=d("添加关联知识库"),e=d([]),a=d([]),c=d([]),u=d(1),i=d(10),n=d(0),r=d(""),C=d(["10","20","30"]),[x,{closeModal:y,setModalProps:s}]=H(o=>S(null,null,function*(){a.value=o.knowledgeIds?P(o.knowledgeIds.split(",")):[],c.value=o.knowledgeDataList?P(o.knowledgeDataList):[],s({minHeight:500,bodyStyle:{padding:"10px"}}),z()}));function v(){return S(this,null,function*(){l("success",a.value,c.value),O()})}function O(){y()}const K=o=>{let t=o.id;const m=e.value.find(M=>M.id===t);if(m&&(m.checked=!m.checked),a.value.length==0){a.value.push(t),c.value.push(o);return}let b=a.value.findIndex(M=>M===t);b===-1?(a.value.push(t),c.value.push(o)):(a.value.splice(b,1),c.value.splice(b,1))};function z(){let o={pageNo:u.value,pageSize:i.value,name:r.value};j(o).then(t=>{if(t.success){if(a.value.length>0){for(const m of t.result.records)a.value.includes(m.id)&&(m.checked=!0);e.value=t.result.records}else e.value=t.result.records;n.value=t.result.total}else e.value=[],n.value=0})}function N(o,t){u.value=o,i.value=t,z()}function E(){a.value=[],c.value=[],e.value.forEach(o=>{o.checked=!1})}return{registerModal:x,title:p,handleOk:v,handleCancel:O,appKnowledgeOption:e,knowledgeIds:a,handleSelect:K,pageNo:u,pageSize:i,pageSizeOptions:C,total:n,handlePageChange:N,knowledge:G,searchText:r,loadKnowledgeData:z,handleClearClick:E}}},W={class:"p-2"},X={class:"flex header"},Y={style:{display:"flex",width:"100%","justify-content":"space-between"}},Z=["src"],$={class:"checkbox-name"},ee={key:0,class:"use-select"};function ae(w,l,p,e,a,c){const u=h("a-input"),i=h("a-checkbox"),n=h("a-card"),r=h("a-col"),C=h("a-row"),x=h("Pagination"),y=h("BasicModal");return f(),I("div",W,[k(y,{destroyOnClose:"",onRegister:e.registerModal,canFullscreen:!1,width:"600px",title:e.title,onOk:e.handleOk,onCancel:e.handleCancel},{default:_(()=>[g("div",X,[l[3]||(l[3]=g("span",null,"所选知识库必须使用相同的 Embedding 模型",-1)),k(u,{onPressEnter:e.loadKnowledgeData,class:"header-search",size:"small",value:e.searchText,"onUpdate:value":l[0]||(l[0]=s=>e.searchText=s),placeholder:"请输入知识库名称，回车搜索"},null,8,["onPressEnter","value"])]),k(C,{span:24},{default:_(()=>[(f(!0),I(T,null,V(e.appKnowledgeOption,s=>(f(),B(r,{span:12,onClick:v=>e.handleSelect(s)},{default:_(()=>[k(n,{style:F(s.checked?{border:"1px solid #3370ff"}:{}),hoverable:"",class:"checkbox-card","body-style":{width:"100%"}},{default:_(()=>[g("div",Y,[g("div",null,[g("img",{class:"checkbox-img",src:e.knowledge},null,8,Z),g("span",$,D(s.name),1)]),k(i,{checked:s.checked,"onUpdate:checked":v=>s.checked=v,onClick:l[1]||(l[1]=L(()=>{},["stop"])),class:"quantum-checker"},null,8,["checked","onUpdate:checked"])])]),_:2},1032,["style"])]),_:2},1032,["onClick"]))),256))]),_:1}),e.knowledgeIds.length>0?(f(),I("div",ee,[U(" 已选择 "+D(e.knowledgeIds.length)+" 知识库 ",1),g("span",{style:{"margin-left":"8px",color:"#3d79fb",cursor:"pointer"},onClick:l[2]||(l[2]=(...s)=>e.handleClearClick&&e.handleClearClick(...s))},"清空")])):A("",!0),e.appKnowledgeOption.length>0?(f(),B(x,{key:1,current:e.pageNo,"page-size":e.pageSize,"page-size-options":e.pageSizeOptions,total:e.total,showQuickJumper:!0,showSizeChanger:!0,onChange:e.handlePageChange,class:"list-footer",size:"small"},null,8,["current","page-size","page-size-options","total","onChange"])):A("",!0)]),_:1},8,["onRegister","title","onOk","onCancel"])])}const we=J(Q,[["render",ae],["__scopeId","data-v-084840af"]]);export{we as default};
