var k=Object.defineProperty;var y=Object.getOwnPropertySymbols;var x=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var C=(e,r,o)=>r in e?k(e,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[r]=o,v=(e,r)=>{for(var o in r||(r={}))x.call(r,o)&&C(e,o,r[o]);if(y)for(var o of y(r))P.call(r,o)&&C(e,o,r[o]);return e};var d=(e,r,o)=>new Promise((c,f)=>{var m=i=>{try{a(o.next(i))}catch(l){f(l)}},u=i=>{try{a(o.throw(i))}catch(l){f(l)}},a=i=>i.done?c(i.value):Promise.resolve(i.value).then(m,u);a((o=o.apply(e,r)).next())});import{d as R,c as j,f as w,e as S,aB as U,ar as A,u as s,aE as I,aD as L,k as T}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{B as V}from"./index-Diw57m_E.js";import{b as E}from"./depart.api-BoGnt_ZX.js";import{u as G,o as B}from"./depart.data-DDyAushI.js";import{u as N}from"./useForm-CgkFTrrO.js";import{ac as $}from"./index-CCWaWN5g.js";import{B as q}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";const Ko=R({__name:"DepartFormModal",props:{rootTreeData:{type:Array,default:()=>[]}},emits:["success","register"],setup(e,{emit:r}){const o=r,c=e,f=j("prefixCls"),m=w(!1),u=w({}),a=S(()=>m.value?"编辑":"新增"),[i,{resetFields:l,setFieldsValue:F,validate:O,updateSchema:D}]=N({schemas:G().basicFormSchema,showActionButtonGroup:!1}),[M,{setModalProps:g,closeModal:_}]=$(t=>d(null,null,function*(){yield l(),m.value=s(t==null?void 0:t.isUpdate);let n=s(t==null?void 0:t.isChild),h=n?B.child:B.root;D([{field:"parentId",show:n,componentProps:{disabled:n,treeData:c.rootTreeData}},{field:"orgCode",show:!1},{field:"orgCategory",componentProps:{options:h}}]);let p=s(t==null?void 0:t.record);typeof p!="object"&&(p={}),p=Object.assign({departOrder:0,orgCategory:h[0].value},p),u.value=p,yield F(v({},p))}));function b(){return d(this,null,function*(){try{g({confirmLoading:!0});let t=yield O();yield E(t,m.value),_(),o("success")}finally{g({confirmLoading:!1})}})}return(t,n)=>(A(),U(s(V),I({title:a.value,width:800},t.$attrs,{onOk:b,onRegister:s(M)}),{default:L(()=>[T(s(q),{onRegister:s(i)},null,8,["onRegister"])]),_:1},16,["title","onRegister"]))}});export{Ko as default};
