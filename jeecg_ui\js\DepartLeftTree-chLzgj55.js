var m=(M,T,v)=>new Promise((U,u)=>{var K=l=>{try{k(v.next(l))}catch(a){u(a)}},b=l=>{try{k(v.throw(l))}catch(a){u(a)}},k=l=>l.done?U(l.value):Promise.resolve(l.value).then(K,b);k((v=v.apply(M,T)).next())});import{d as ge,c as ke,f as p,ag as r,aB as w,ar as c,aD as o,at as f,k as n,ah as G,G as y,aq as R,au as O,F as W,u as E,n as z}from"./vue-vendor-dy9k-Yad.js";import"./index-Diw57m_E.js";import{ad as he,u as xe,E as Ce}from"./index-CCWaWN5g.js";import{q as H,d as De,A as J}from"./depart.api-BoGnt_ZX.js";import{s as we}from"./depart.user.api-D_abnxSU.js";import Te from"./DepartFormModal-C0blK_iA.js";import{P as Ke}from"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./depart.data-DDyAushI.js";const be={class:"j-table-operator",style:{width:"100%"}},Ae={key:1},Et=ge({__name:"DepartLeftTree",emits:["select","rootTreeData"],setup(M,{expose:T,emit:v}){const U=ke("prefixCls"),u=v,{createMessage:K}=xe(),{handleImportXls:b,handleExportXls:k}=Ce(),l=p(!1),a=p([]),s=p([]),d=p([]),h=p([]),A=p(!1),Q=p(!0),I=p(null),S=p(null),Y=p(""),[Z,{openModal:$}]=he();function _(){return m(this,null,function*(){try{l.value=!0,a.value=[];const t=yield H();if(Array.isArray(t)&&(a.value=t),d.value.length===0)L();else if(h.value.length===0){let e=a.value[0];e&&x(e.id,e)}else u("select",I.value);u("rootTreeData",a.value)}finally{l.value=!1}})}_();function ee(t){return m(this,null,function*(){try{const e=yield H({pid:t.dataRef.id});if(e.length==0)t.dataRef.isLeaf=!0;else if(t.dataRef.children=e,d.value.length>0){let i=[];for(let C of d.value)e.findIndex(D=>D.id===C)!==-1&&i.push(C);i.length>0&&(d.value=[...d.value])}a.value=[...a.value],u("rootTreeData",a.value)}catch(e){}return Promise.resolve()})}function L(){let t=a.value[0];t?(t.isLeaf||(d.value=[t.key]),x(t.id,t),te()):u("select",null)}function te(){return m(this,null,function*(){yield z(),A.value=!0,yield z(),A.value=!1})}function x(t,e){h.value=[t],e&&(I.value=e,u("select",e))}function oe(){$(!0,{isUpdate:!1,isChild:!1})}function V(t=I.value){if(t==null){K.warning("请先选择一个部门");return}const e={parentId:t.id};$(!0,{isUpdate:!1,isChild:!0,record:e})}function ne(t){return m(this,null,function*(){if(t)try{l.value=!0,a.value=[];let e=yield we({keyWord:t});Array.isArray(e)&&(a.value=e),L()}finally{l.value=!1}else _();Y.value=t})}function ae(t){Array.isArray(t)?s.value=t:s.value=t.checked}function le(t,e){t.length>0&&h.value[0]!==t[0]?x(t[0],e.selectedNodes[0]):x(h.value[0])}function X(t,e=!0){return m(this,null,function*(){const i=E(t);if(i.length>0)try{l.value=!0,yield De({ids:i.join(",")},e),yield _()}finally{l.value=!1}})}function ie(t){return m(this,null,function*(){t&&(j(!1),X([t.id],!1))})}function re(){return m(this,null,function*(){try{yield X(s),s.value=[]}finally{}})}function j(t){t||(S.value=null)}function se(t){b(t,J.importExcelUrl,()=>{_()})}function pe(){let t={};s.value&&s.value.length>0&&(t.selections=s.value.join(",")),k("部门信息",J.exportXlsUrl,t)}return T({loadRootTreeData:_}),(t,e)=>{const i=r("a-button"),C=r("a-upload"),D=r("icon"),B=r("a-menu-item"),q=r("a-menu"),N=r("a-dropdown"),ue=r("a-divider"),de=r("a-alert"),me=r("a-input-search"),ce=r("a-tree"),fe=r("a-empty"),ye=r("a-spin"),ve=r("a-card");return c(),w(ve,{bordered:!1,style:{height:"100%"}},{default:o(()=>[f("div",be,[n(i,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:oe},{default:o(()=>e[4]||(e[4]=[y("新增")])),_:1,__:[4]}),n(i,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:e[0]||(e[0]=g=>V())},{default:o(()=>e[5]||(e[5]=[y("添加下级")])),_:1,__:[5]}),n(C,{name:"file",showUploadList:!1,customRequest:se},{default:o(()=>[n(i,{type:"primary",preIcon:"ant-design:import-outlined"},{default:o(()=>e[6]||(e[6]=[y("导入")])),_:1,__:[6]})]),_:1}),n(i,{type:"primary",preIcon:"ant-design:export-outlined",onClick:pe},{default:o(()=>e[7]||(e[7]=[y("导出")])),_:1,__:[7]}),n(i,{type:"primary",preIcon:"ant-design:sync-outlined"},{default:o(()=>e[8]||(e[8]=[y("同步企微?")])),_:1,__:[8]}),n(i,{type:"primary",preIcon:"ant-design:sync-outlined"},{default:o(()=>e[9]||(e[9]=[y("同步钉钉?")])),_:1,__:[9]}),s.value.length>0?(c(),w(N,{key:0},{overlay:o(()=>[n(q,null,{default:o(()=>[n(B,{key:"1",onClick:re},{default:o(()=>[n(D,{icon:"ant-design:delete-outlined"}),e[10]||(e[10]=f("span",null,"删除",-1))]),_:1,__:[10]})]),_:1})]),default:o(()=>[n(i,null,{default:o(()=>[e[11]||(e[11]=f("span",null,"批量操作 ",-1)),n(D,{icon:"akar-icons:chevron-down"})]),_:1,__:[11]})]),_:1})):G("",!0)]),n(de,{type:"info","show-icon":"",class:"alert",style:{"margin-bottom":"8px"}},{message:o(()=>[s.value.length>0?(c(),R(W,{key:0},[f("span",null,"已选中 "+O(s.value.length)+" 条记录",1),n(ue,{type:"vertical"}),f("a",{onClick:e[1]||(e[1]=g=>s.value=[])},"清空")],64)):(c(),R("span",Ae,"未选中任何数据"))]),_:1}),n(ye,{spinning:l.value},{default:o(()=>[n(me,{placeholder:"按部门名称搜索…",style:{"margin-bottom":"10px"},onSearch:ne}),a.value.length>0?(c(),R(W,{key:0},[A.value?G("",!0):(c(),w(ce,{key:0,checkable:"",clickRowToExpand:!1,treeData:a.value,selectedKeys:h.value,checkStrictly:Q.value,"load-data":ee,checkedKeys:s.value,expandedKeys:d.value,"onUpdate:expandedKeys":e[3]||(e[3]=g=>d.value=g),onCheck:ae,onSelect:le},{title:o(({key:g,title:_e,dataRef:P})=>[n(N,{trigger:["contextmenu"]},{overlay:o(()=>[n(q,{onClick:e[2]||(e[2]=()=>{})},{default:o(()=>[n(B,{key:"1",onClick:F=>V(P)},{default:o(()=>e[12]||(e[12]=[y("添加子级")])),_:2,__:[12]},1032,["onClick"]),n(B,{key:"2",onClick:F=>S.value=g},{default:o(()=>e[13]||(e[13]=[f("span",{style:{color:"red"}},"删除",-1)])),_:2,__:[13]},1032,["onClick"])]),_:2},1024)]),default:o(()=>[n(E(Ke),{open:S.value===g,title:"确定要删除吗？","ok-text":"确定","cancel-text":"取消",placement:"rightTop",onConfirm:F=>ie(P),onOpenChange:j},{default:o(()=>[f("span",null,O(_e),1)]),_:2},1032,["open","onConfirm"])]),_:2},1024)]),_:1},8,["treeData","selectedKeys","checkStrictly","checkedKeys","expandedKeys"]))],64)):(c(),w(fe,{key:1,description:"暂无数据"}))]),_:1},8,["spinning"]),n(Te,{rootTreeData:a.value,onRegister:E(Z),onSuccess:_},null,8,["rootTreeData","onRegister"])]),_:1})}}});export{Et as default};
