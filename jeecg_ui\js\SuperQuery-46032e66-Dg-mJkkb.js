import{f as D,r as Ce,J as we,aN as Fe,w as de,e as Pe,ag as f,aq as ve,ar as P,F as he,at as _,aB as q,k as u,aD as d,au as ce,E as xe,ah as De,as as ye,q as be,G as U,B as Se,aA as Ie,aC as ke}from"./vue-vendor-dy9k-Yad.js";import{B as je}from"./index-Diw57m_E.js";import{cs as Re,u as _e,ac as Ve,bH as pe,a1 as qe,ad as Be,F as Ne,ct as Le}from"./index-CCWaWN5g.js";import{h as fe,M as Qe,Y as Ee,P as Ye,V as Ae,c4 as Je,by as Ue,c5 as He,aO as $e,aP as Ge}from"./antd-vue-vendor-me9YkNVC.js";import{i as ze,F as Ke,j as ge}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{G as We}from"./SuperQueryValComponent.vue_vue_type_script_lang-8fe34917-DN8CXDRQ.js";import"./componentMap-Bkie1n3v.js";import"./index-L3cSIXth.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./JUpload-CRos0F1P.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";var Xe=Object.defineProperty,Ze=Object.defineProperties,et=Object.getOwnPropertyDescriptors,Oe=Object.getOwnPropertySymbols,tt=Object.prototype.hasOwnProperty,lt=Object.prototype.propertyIsEnumerable,Te=(m,n,g)=>n in m?Xe(m,n,{enumerable:!0,configurable:!0,writable:!0,value:g}):m[n]=g,se=(m,n)=>{for(var g in n||(n={}))tt.call(n,g)&&Te(m,g,n[g]);if(Oe)for(var g of Oe(n))lt.call(n,g)&&Te(m,g,n[g]);return m},ue=(m,n)=>Ze(m,et(n));const at={password:"text",file:"text",image:"text",textarea:"text",umeditor:"text",markdown:"text",checkbox:"list_multi",radio:"list"},nt="JSuperQuerySaved_";function it(m){const{linkTableCard2Select:n}=ze(),{createMessage:g}=_e(),a=D(),O=Ce({values:[]}),B=D("and"),R=D(!1),[W,{setModalProps:F}]=Ve(()=>{F({confirmLoading:!1})}),N=Object.assign({},{link_down:"text"},at);function Q(){}function X(){}function H(t,l,r){r.val=l}const I=D({}),A=D([]),V=t=>{const{properties:l={}}=t;Object.entries(l).forEach(([r,i])=>{i.view==="table"&&V(i),["link_down"].includes(i.originView||i.view)&&delete l[r]})};function E(t){var l;V(t);let{allFields:r,treeData:i}=ie(t);I.value=r;const h=(l=t.properties)!=null?l:{},s=[],v=t.table;if(Object.entries(h).forEach(([C,e])=>{e.view==="table"&&s.push(C)}),s.length){let C=[];C=i.filter(e=>!s.includes(e.value));for(let e=0,o=i.length;e<o;e++){const p=i[e];s.includes(p.value)||(i.splice(e,1),e--,o--)}i.unshift({title:"主表",value:v,disabled:!0,order:200,children:C,view:"table"})}A.value=i}function $(t){let l={field:void 0,rule:"eq",val:"",key:pe(16)};t===!1?(O.values=[],O.values.push(l)):t===!0?O.values.length==0&&O.values.push(l):O.values.splice(++t,0,l)}function Z(t){let l=we(O.values),r=-1;for(let i=0;i<l.length;i++)if(t.key==l[i].key){r=i;break}r!=-1&&O.values.splice(r,1)}const ee={field:"val",label:"测试",component:"Input"};function te(t,l){var r,i,h;let s=I.value[t.field];if(!s)return ee;N[s.view]&&(s.view=N[s.view]);let v=Ke.createFormSchema(t.field,s);v.noChange(),v.asSearchForm(),v.updateField(t.field+l);const C=o=>{t.val=o[t.field]};v.setFunctionForFieldValue(C);let e=v.getFormItemSchema();if(["empty","not_empty"].includes(t.rule)&&(e.componentProps=ue(se({},e.componentProps),{disabled:!0})),n(e),e.component==="LinkTableSelect"){let o=(r=e.componentProps)!=null?r:{};e.componentProps=ue(se({},o),{editBtnShow:!1})}if(e&&e.component==="InputNumber"&&(t.curLineAlign="start"),(e==null?void 0:e.component)==="JTreeSelect"){let o=e.componentProps;o?o.getPopupContainer=()=>document.body:e.componentProps={getPopupContainer:()=>document.body}}if((e==null?void 0:e.component)==="JSwitch"){const o=(i=e.componentProps)!=null?i:{};e.componentProps=ue(se({},o),{query:!0})}if((e==null?void 0:e.component)==="JSelectUser"){const o=(h=e.componentProps)!=null?h:{};e.componentProps=ue(se({},o),{showButton:!1})}return e}const J=D(""),L=qe(),M=Ce({visible:!1,title:"",content:"",saveCode:""}),le=D(!1),G=Fe();m.isCustomSave?de(m.saveSearchData,()=>{S.value=m.saveSearchData}):de(()=>G.fullPath,t=>{z()});const S=D([]);de(()=>S.value,t=>{let l=[];t&&t.length>0&&t.map(r=>{let i=pe(16);l.push({title:r.title,slots:{icon:"custom"},value:i})}),J.value=l},{immediate:!0,deep:!0});function z(){if(m.isCustomSave)S.value=fe(m.saveSearchData);else{let t=nt+G.fullPath;M.saveCode=t;let l=L.get(t);l&&l instanceof Array&&(S.value=l)}}z();function K(){let t=Y();if(!t){g.warning("空条件不能保存");return}let l=JSON.stringify(t);ae(l)}function ae(t){M.visible=!0,M.title="",M.content=t}function ne(){let{title:t,content:l,saveCode:r}=M,i=j(t);const h=s=>{const v=fe(S.value);R.value=!0,s?v.splice(i,1,{content:l,title:t,type:B.value}):v.push({content:l,title:t,type:B.value});const C=()=>{M.visible=!1,g.success("保存成功"),S.value=v,R.value=!1};m.isCustomSave?m.save(v,0).then(()=>{C()}).catch(e=>{R.value=!1}):(L.set(r,v,2592e3),C())};i>=0?Qe.confirm({title:"提示",content:`${t} 已存在，是否覆盖？`,okText:"确认",cancelText:"取消",onOk:()=>{h(1)}}):h(0)}function j(t){let l=S.value,r=-1;for(let i=0;i<l.length;i++)if(l[i].title==t){r=i;break}return r}function Y(t=!1){var l;let r=O.values;if(!r||r.length==0)return!1;let i=[],h=I.value;for(let s of r){let v=["empty","not_empty"].includes(s.rule);if(s.field&&(v||s.val||s.val===0)&&s.rule){let C=h[s.field],e=(l=C==null?void 0:C.formatValue)!=null?l:y=>y,o=we(s.val);o instanceof Array?o=o.map(y=>e(y)).join(","):o=e(o);let p={field:c(s),rule:s.rule,val:o,fileType:s.fileType};if(t===!0){let y=h[s.field];y&&(p.type=y.view,p.dbType=y.type)}i.push(p)}}return i.length==0?!1:i}function c(t){let l=t.field;return l.indexOf("@")>0&&(l=l.replace("@",",")),l}function x(t,{node:l}){let r=l.dataRef.title,i=S.value.filter(h=>h.title==r);if(i&&i.length>0){let{content:h,type:s}=i[0],v=JSON.parse(h),C=[];for(let e of v)e.field=e.field.replace(",","@"),C.push(Object.assign({},{key:pe(16)},e));O.values=C,B.value=s}}function k(t){let l=j(t);if(l>=0)if(m.isCustomSave){const r=fe(S.value);r.splice(l,1),m.save(r,1).then(()=>{S.value=r}).catch(i=>{})}else S.value.splice(l,1),L.set(M.saveCode,S.value)}function ie(t){let l={},r=1,i=[];return t.properties&&(t=t.properties),Object.keys(t).map(h=>{let s=t[h];if(s.view=="table"){let v=s.properties||s.fields,C=r*100,e={title:s.title,value:h,disabled:!0,children:[],order:C,fieldType:s.type};Object.keys(v).map(o=>{let p=v[o];p.order=C+p.order;let y=h+"@"+o;l[y]=p,e.children.push({title:p.title,value:y,isLeaf:!0,order:p.order,fieldType:p.type,view:p.view,originView:p.view})}),oe(e),i.push(e),r++}else{let v=h;l[v]=s,i.push({title:s.title,value:v,isLeaf:!0,order:s.order,fieldType:s.type,view:s.view,originView:s.view})}}),oe(i),{allFields:l,treeData:i}}function oe(t){(t.children||t).sort(function(l,r){return l.order-r.order})}function me(t){const{params:l,matchType:r}=t;if(l){let i=[];for(let h of l)i.push(Object.assign({},{key:pe(16)},h));O.values=i,r.value=r}}return{formRef:a,init:E,dynamicRowValues:O,matchType:B,registerModal:W,handleSubmit:Q,handleCancel:X,handleSave:K,doSaveQueryInfo:ne,saveInfo:M,saveTreeData:J,handleRemoveSaveInfo:k,handleTreeSelect:x,fieldTreeData:A,addOne:$,removeOne:Z,setFormModel:H,getSchema:te,loading:le,getQueryInfo:Y,initDefaultValues:me,saveModalLoading:R,fieldProperties:I}}const ot={name:"OnlineSuperQuery",props:{config:{type:Object,default:[]},status:{type:Boolean,default:!1},online:{type:Boolean,default:!1},isCustomSave:{type:Boolean,default:!1},saveSearchData:{type:Array,default:()=>[]},save:{type:Function},queryBtnCfg:{type:Object,default:()=>({buttonName:"高级查询",buttonIcon:"ant-design:filter-outlined"})}},components:{BasicModal:je,MinusCircleOutlined:Ge,PlusOutlined:$e,OnlineSuperQueryValComponent:We,FileTextOutlined:He,CloseCircleOutlined:Ue,AppstoreTwoTone:Je,Divider:Ae,Popconfirm:Ye},emits:["search"],setup(m,{emit:n}){const[g,a]=Be(),{createMessage:O}=_e(),B=D({}),R=D(180),{prefixCls:W}=Ne("super-query"),F=`${W}-tree-popup`,N=D(!0);let Q=null;const{filterCondition:X}=Le(),H=e=>{var o,p;const y=(w,T)=>{var b;const re=(b=w.find(Me=>Me.value===T))!=null?b:{};return X({view:re.originView||re.view,fieldType:re.fieldType})};if(((o=e.field)==null?void 0:o.indexOf("@"))==-1||!e.field)return y(j.value,e.field);{const w=e.field.split("@")[0],T=j.value.find(b=>b.value===w);if((p=T==null?void 0:T.children)!=null&&p.length)return y(T.children,e.field)}};function I(){a.closeModal()}function A(){if(m.online===!0){let e=oe(!0);e&&e.length>0?(V(e),n("search",e,M.value),I(),Q=e):O.warning("空条件无法查询！")}else{let e=oe(!0);if(e&&e.length>0){let o=E(e);n("search",o)}else O.warning("空条件无法查询！")}N.value=!0}const V=e=>{e.forEach(o=>{const p=o.val;if(o.type==="date"&&typeof p=="string"&&p!=""){const y=l.value[o.field];if(y){let w=y.fieldExtendJson;if(w&&(w=JSON.parse(w),w.picker&&w.picker!=="default")){const T=w.picker;T==="year"?o.val=ge(p).set("month",0).set("date",1).format("YYYY-MM-DD"):T==="month"?o.val=ge(p).set("date",1).format("YYYY-MM-DD"):T==="week"&&(o.val=ge(p).startOf("week").format("YYYY-MM-DD"))}}}})};function E(e){let o=[];for(let p of e){let y=p.field,w=p.val;w instanceof Array&&(w=w.join(",")),o.push(ue(se({},p),{field:y,val:w}))}return o.length>0?r.value=!0:r.value=!1,{superQueryMatchType:M.value,superQueryParams:encodeURI(JSON.stringify(o))}}function $(){let e=E([]);n("search",e)}function Z(){L.values=[],Y(!1);let e=E([]);Q=null,N.value=!0,n("search",e)}const ee=e=>{if(e){const o=document.documentElement.clientHeight-165;B.value={maxHeight:`${o}px`},R.value=o*.62}else B.value={},R.value=180},{formRef:te,init:J,dynamicRowValues:L,matchType:M,registerModal:le,handleSave:G,doSaveQueryInfo:S,saveInfo:z,saveTreeData:K,handleTreeSelect:ae,handleRemoveSaveInfo:ne,fieldTreeData:j,addOne:Y,removeOne:c,setFormModel:x,getSchema:k,loading:ie,getQueryInfo:oe,initDefaultValues:me,saveModalLoading:t,fieldProperties:l}=it(m),r=D(!1);de(()=>m.status,e=>{r.value=e},{immediate:!0});function i(){r.value&&Q&&(L.values=fe(Q)),a.openModal(),N.value=!0,Y(!0)}function h(){return document.getElementsByClassName("jee-super-query-form")[0]}function s(e){}function v(e){var o,p;e.val="";const y=(w,T)=>{const b=w.find(re=>re.value===T);(b==null?void 0:b.fieldType)==="string"&&["text"].includes((b==null?void 0:b.originView)||(b==null?void 0:b.view))?e.rule="like":["file","image","password"].includes((b==null?void 0:b.originView)||(b==null?void 0:b.view))?e.rule="empty":e.rule="eq"};if(((o=e.field)==null?void 0:o.indexOf("@"))==-1)y(j.value,e.field);else{const w=e.field.split("@")[0],T=j.value.find(b=>b.value===w);(p=T==null?void 0:T.children)!=null&&p.length&&y(T.children,e.field)}}de(()=>m.config,e=>{e&&J(e)},{immediate:!0});const C=Pe(()=>j.value.find(e=>e.children)?`${F} containTable`:`${F} noTable`);return{formRef:te,registerFormModal:g,init:J,handleChangeField:v,dynamicRowValues:L,matchType:M,historyCollapsed:N,registerModal:le,handleSubmit:A,handleCancel:I,handleSave:G,handleReset:Z,doSaveQueryInfo:S,saveInfo:z,saveTreeData:K,handleTreeSelect:ae,handleRemoveSaveInfo:ne,fieldTreeData:j,addOne:Y,removeOne:c,setFormModel:x,getSchema:k,loading:ie,onFinish:s,getPopupContainer:h,superQueryFlag:r,handleOpen:i,initDefaultValues:me,simpleImage:Ee.PRESENTED_IMAGE_SIMPLE,saveModalLoading:t,queryFormStyle:B,handleFullScreen:ee,fieldTreeSelectHeight:R,getTreePopupClass:C,handleStop:$,getQueryCondition:H}}},rt={class:"j-super-query-button"},st={style:{float:"left"}},ut={slot:"description"},dt=["title"],ct={style:{height:"80px","line-height":"75px",width:"100%","text-align":"center"}};function pt(m,n,g,a,O,B){const R=f("divider"),W=f("AppstoreTwoTone"),F=f("a-button"),N=f("a-button-group"),Q=f("a-tooltip"),X=f("a-divider"),H=f("a-empty"),I=f("a-select-option"),A=f("a-select"),V=f("a-form-item"),E=f("a-col"),$=f("a-row"),Z=f("a-tree-select"),ee=f("online-super-query-val-component"),te=f("PlusOutlined"),J=f("MinusCircleOutlined"),L=f("a-space"),M=f("a-form"),le=f("close-circle-outlined"),G=f("a-popconfirm"),S=f("file-text-outlined"),z=f("a-tree"),K=f("Icon"),ae=f("a-card"),ne=f("BasicModal"),j=f("a-input"),Y=f("a-modal");return P(),ve(he,null,[_("div",rt,[a.superQueryFlag?(P(),q(Q,{key:0,mouseLeaveDelay:.2},{title:d(()=>[n[7]||(n[7]=_("span",null,"执行查询中...",-1)),u(R,{type:"vertical",style:{"background-color":"#fff"}}),_("a",{onClick:n[0]||(n[0]=(...c)=>a.handleStop&&a.handleStop(...c))},"取消查询")]),default:d(()=>[u(N,null,{default:d(()=>[u(F,{type:"primary",onClick:a.handleOpen},{default:d(()=>[u(W,{spin:!0}),_("span",null,ce(g.queryBtnCfg.buttonName),1)]),_:1},8,["onClick"])]),_:1})]),_:1})):(P(),q(F,{key:1,type:"primary",preIcon:g.queryBtnCfg.buttonIcon,onClick:a.handleOpen},{default:d(()=>[_("span",null,ce(g.queryBtnCfg.buttonName),1)]),_:1},8,["preIcon","onClick"]))]),(P(),q(xe,{to:"body"},[u(ne,{title:g.queryBtnCfg.buttonName+"构造器","wrap-class-name":"j-super-query-modal",canFullscreen:!0,width:850,onRegister:a.registerFormModal,onOk:a.handleSubmit,onFullScreen:a.handleFullScreen},{footer:d(()=>[_("div",st,[u(F,{loading:a.loading,onClick:a.handleReset},{default:d(()=>n[8]||(n[8]=[U("清空")])),_:1},8,["loading","onClick"]),u(F,{loading:a.loading,onClick:a.handleSave},{default:d(()=>n[9]||(n[9]=[U("保存查询")])),_:1},8,["loading","onClick"])]),u(F,{key:"submit",type:"primary",onClick:a.handleSubmit},{default:d(()=>n[10]||(n[10]=[U("执行查询")])),_:1},8,["onClick"]),u(F,{key:"back",onClick:a.handleCancel},{default:d(()=>n[11]||(n[11]=[U("关闭")])),_:1},8,["onClick"])]),default:d(()=>[a.dynamicRowValues.values.length==0?(P(),q(H,{key:0},{default:d(()=>[_("div",ut,[n[12]||(n[12]=_("span",null,"没有任何查询条件",-1)),u(X,{type:"vertical"}),_("a",{onClick:n[1]||(n[1]=c=>a.addOne(-1))},"点击新增")])]),_:1})):De("",!0),u($,{class:ye("j-super-query-modal-content")},{default:d(()=>[u(E,{sm:24,md:24},{default:d(()=>[be(u($,null,{default:d(()=>[u(E,{md:12,xs:24},{default:d(()=>[u(V,{label:"匹配模式",labelCol:{md:6,xs:24},wrapperCol:{md:18,xs:24},style:{width:"100%"}},{default:d(()=>[u(A,{value:a.matchType,"onUpdate:value":n[2]||(n[2]=c=>a.matchType=c),getPopupContainer:c=>c==null?void 0:c.parentNode,style:{width:"100%"}},{default:d(()=>[u(I,{value:"and"},{default:d(()=>n[13]||(n[13]=[U("AND（所有条件匹配）")])),_:1}),u(I,{value:"or"},{default:d(()=>n[14]||(n[14]=[U("OR（任意一个匹配）")])),_:1})]),_:1},8,["value","getPopupContainer"])]),_:1})]),_:1})]),_:1},512),[[Se,a.dynamicRowValues.values.length>0]]),be(u(M,{ref:"formRef",class:ye("jee-super-query-form"),model:a.dynamicRowValues,onFinish:a.onFinish,style:Ie(a.queryFormStyle)},{default:d(()=>[(P(!0),ve(he,null,ke(a.dynamicRowValues.values,(c,x)=>(P(),q(L,{key:c.key,style:{display:"flex","margin-bottom":"8px"},align:c.curLineAlign?c.align:"baseline"},{default:d(()=>[u(V,{class:"field-clos",name:["values",x,"field"]},{default:d(()=>[u(Z,{popupClassName:a.getTreePopupClass,style:{width:"100%"},placeholder:"请选择字段",value:c.field,"onUpdate:value":k=>c.field=k,"show-search":"","tree-node-filter-prop":"title","allow-clear":"","tree-default-expand-all":"","dropdown-style":{maxHeight:`${a.fieldTreeSelectHeight}px`,overflow:"auto"},listHeight:a.fieldTreeSelectHeight-20,onChange:k=>a.handleChangeField(c),"tree-data":a.fieldTreeData},null,8,["popupClassName","value","onUpdate:value","dropdown-style","listHeight","onChange","tree-data"])]),_:2},1032,["name"]),u(V,{class:"rule-clos",name:["values",x,"rule"]},{default:d(()=>[u(A,{style:{width:"100%"},placeholder:"请选择匹配规则",value:c.rule,"onUpdate:value":k=>c.rule=k},{default:d(()=>[(P(!0),ve(he,null,ke(a.getQueryCondition(c),k=>(P(),q(I,{value:k.value,key:k.value},{default:d(()=>[U(ce(k.label),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value"])]),_:2},1032,["name"]),u(V,{class:"component-clos",name:["values",x,"val"]},{default:d(()=>[u(ee,{style:{width:"100%"},schema:a.getSchema(c,x),formModel:c,setFormModel:(k,ie)=>{a.setFormModel(k,ie,c)},onSubmit:a.handleSubmit},null,8,["schema","formModel","setFormModel","onSubmit"])]),_:2},1032,["name"]),u(V,null,{default:d(()=>[u(F,{onClick:k=>a.addOne(x),style:{"margin-right":"6px"}},{default:d(()=>[u(te)]),_:2},1032,["onClick"]),u(F,{onClick:k=>a.removeOne(c)},{default:d(()=>[u(J)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["align"]))),128))]),_:1},8,["model","onFinish","style"]),[[Se,a.dynamicRowValues.values.length>0]])]),_:1})]),_:1}),u(ae,{class:ye(["j-super-query-history-card",{collapsed:a.historyCollapsed}]),bordered:!1},{title:d(()=>n[15]||(n[15]=[_("div",null,"保存的查询",-1)])),default:d(()=>[a.saveTreeData.length===0?(P(),q(H,{key:0,class:"j-super-query-history-empty",image:a.simpleImage,description:"没有保存的查询"},null,8,["image"])):(P(),q(z,{key:1,class:"j-super-query-history-tree",treeData:a.saveTreeData,selectedKeys:[],"show-icon":!0,onSelect:a.handleTreeSelect},{title:d(({title:c})=>[_("div",null,[_("span",{title:c},ce(c.length>10?c.substring(0,10)+"...":c),9,dt),u(G,{title:"确定删除吗？",onConfirm:x=>a.handleRemoveSaveInfo(c)},{default:d(()=>[_("span",{class:"icon-cancle",onClick:n[3]||(n[3]=x=>x.stopPropagation())},[u(le)])]),_:2},1032,["onConfirm"])])]),custom:d(()=>[u(S)]),_:1},8,["treeData","onSelect"])),_("div",{class:"collapse-box",onClick:n[4]||(n[4]=c=>a.historyCollapsed=!a.historyCollapsed)},[a.historyCollapsed?(P(),q(K,{key:0,icon:"ant-design:caret-left"})):(P(),q(K,{key:1,icon:"ant-design:caret-right"}))])]),_:1},8,["class"])]),_:1},8,["title","onRegister","onOk","onFullScreen"])])),u(Y,{title:"请输入保存的名称",open:a.saveInfo.visible,onCancel:n[6]||(n[6]=c=>a.saveInfo.visible=!1),onOk:a.doSaveQueryInfo,confirmLoading:a.saveModalLoading},{default:d(()=>[_("div",ct,[u(j,{value:a.saveInfo.title,"onUpdate:value":n[5]||(n[5]=c=>a.saveInfo.title=c),style:{width:"90%"},placeholder:"请输入保存的名称"},null,8,["value"])])]),_:1},8,["open","onOk","confirmLoading"])],64)}const _l=Re(ot,[["render",pt],["__scopeId","data-v-c52e7338"]]);export{_l as default};
