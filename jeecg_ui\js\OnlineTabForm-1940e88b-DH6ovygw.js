import{cs as Rt,u as At,ap as jt,j as ee,cr as Vt,aZ as xt,cx as Dt,ad as Mt,bj as Et}from"./index-CCWaWN5g.js";import{f,r as It,u as ve,n as Se,J as A,e as _t,w as Nt,ag as T,aq as $,ar as k,as as Kt,k as j,aG as $t,aD as V,aB as J,ah as te,F as Jt,aC as qt,aA as Ut,at as Fe,au as Ce}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{d as Wt,e as Ht,u as Lt,l as q,g as zt,V as I,S as Gt,f as Qt,h as Te,O as Zt,c as Xt}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{p as Yt,o as ke,c7 as eo}from"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import{I as to,g as oo}from"./useCustomHook-acb00837-B7NPzH0H.js";import"./componentMap-Bkie1n3v.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import{u as lo}from"./useForm-CgkFTrrO.js";import{B as no}from"./BasicForm-DBcXiHk0.js";import"./vxe-table-vendor-B22HppNm.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";var ao=Object.defineProperty,ro=Object.defineProperties,io=Object.getOwnPropertyDescriptors,we=Object.getOwnPropertySymbols,so=Object.prototype.hasOwnProperty,uo=Object.prototype.propertyIsEnumerable,Oe=(i,u,r)=>u in i?ao(i,u,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[u]=r,x=(i,u)=>{for(var r in u||(u={}))so.call(u,r)&&Oe(i,r,u[r]);if(we)for(var r of we(u))uo.call(u,r)&&Oe(i,r,u[r]);return i},mo=(i,u)=>ro(i,io(u)),v=(i,u,r)=>new Promise((a,R)=>{var S=g=>{try{y(r.next(g))}catch(p){R(p)}},w=g=>{try{y(r.throw(g))}catch(p){R(p)}},y=g=>g.done?a(g.value):Promise.resolve(g.value).then(S,w);y((r=r.apply(i,u)).next())});const oe={optPre:"/online/cgform/api/form/",urlButtonAction:"/online/cgform/api/doButton"},po={name:"OnlineTabForm",components:{BasicForm:no,Loading:Et,OnlineSubForm:Xt,PrinterOutlined:eo,OnlinePopModal:Zt},props:{id:{type:String,default:""},formTemplate:{type:Number,default:1},disabled:{type:Boolean,default:!1},isTree:{type:Boolean,default:!1},pidField:{type:String,default:""},submitTip:{type:Boolean,default:!0},modalClass:{type:String,default:""},themeTemplate:{type:String,default:""},tabIndex:{type:String,default:""},cgBIBtnMap:Object,buttonSwitch:Object},emits:["success","rendered","toggleTab"],setup(i,{emit:u}){const{createMessage:r}=At(),a=f(null),R=f(!0),S=f(!1),w=f(1),y=f(""),g=f(!1),p=f(!1),{getIsMobile:U}=jt(),W=f(!U.value),O=It({reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:"",commentStatus:0}),{onlineFormContext:m,resetContext:d,getSubAddBtnCfg:H,getSubRemoveBtnCfg:b,getSubOpenAddBtnCfg:Be,getSubOpenEditBtnCfg:Pe}=Wt(i),{formSchemas:L,defaultValueFields:_,changeDataIfArray2String:le,tableName:B,dbData:F,checkOnlyFieldValue:Re,hasSubTable:Ae,subTabInfo:D,refMap:z,subDataSource:N,baseColProps:je,createFormSchemas:Ve,fieldDisplayStatus:M,labelCol:xe,wrapperCol:De,labelWidth:Me}=Ht(i,a);let{EnhanceJS:s,initCgEnhanceJs:Ee}=Lt(m,!1);const{executeJsEnhanced:Ie}=to({},m),[_e,{setProps:Ne,validate:ne,resetFields:ae,clearValidate:Ke,setFieldsValue:C,updateSchema:G,getFieldsValue:Q,scrollToField:re}]=lo({schemas:L,showActionButtonGroup:!1,baseColProps:je,labelWidth:Me,labelCol:xe,wrapperCol:De}),ie=f(!1);function $e(){let e=i.disabled;ie.value=e,Ne({disabled:e})}function Je(e,t,o){return v(this,null,function*(){yield qe(),y.value="",yield ae(),setTimeout(()=>{Ke()},0),F.value="";let l=ve(e);p.value=l,l?yield ue(t):de(),Se(()=>{!l&&o&&C(o),Ue(),Z("js","loaded"),$e()})})}function qe(){return v(this,null,function*(){if(i.isTree===!0){let e=i.pidField,t=L.value;t&&t.length>0&&t.filter(o=>o.field===e).length>0&&(yield G({field:e,componentProps:{reload:new Date().getTime(),hiddenNodeKey:""}}))}})}function Ue(){if(ve(p)===!1){let e=A(_[B.value]);q(e,t=>{C(t)})}}function se(e,t){let o=A(_[e.key]);q(o,l=>{const{row:n,target:c}=t;let P=[{rowKey:n.id,values:x({},l)}];c.setValues(P)})}function ue(e){return v(this,null,function*(){let t=yield He(e.id);F.value=Object.assign({},e,t);let o=We.value,l=Yt(t,...o);i.disabled&&Object.keys(l).map(n=>{!l[n]&&l[n]!==0&&l[n]!=="0"&&delete l[n]}),yield C(l),de(t)})}function de(e){e||(e={});let t=Object.keys(N.value);if(t&&t.length>0){let o={};for(let l of t)o[l]=e[l]||[];N.value=o}}let We=_t(()=>{let e=L.value,t=[];for(let o of e)t.push(o.field);return t});function He(e){let t=`${oe.optPre}${i.id}/${e}`;return new Promise((o,l)=>{ee.get({url:t},{isTransformResponse:!1}).then(n=>{n.success?o(n.result):(l(),r.warning(n.message))}).catch(()=>{l()})})}function Le(e){return v(this,null,function*(){w.value=e.head.tableType,B.value=e.head.tableName,R.value=e.head.tableType==1,Qe(e.head.extConfigJson),Ve(e.schema.properties,e.schema.required,Re,O),s=Ee(e.enhanceJs),u("rendered",O);let t=yield zt(a);t.$formValueChange=(o,l,n)=>{ct(o,l),n&&C(n),ze(o,l,n)},s&&s.setup&&fe(s.setup)})}function ze(e,t,o){m.changEvent(e,t,o)}function Ge(e){m.addObject2Context("changEvent",e)}function Qe(e){let t={reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:"",commentStatus:0,formLabelLength:null};e&&(t=JSON.parse(e)),Object.keys(t).map(o=>{O[o]=t[o]})}function Ze(){R.value===!0?ot():Xe()}function Xe(){Ye().then(e=>{me(e)})}function Ye(){let e={};return new Promise((t,o)=>{ne().then(l=>t(l),({errorFields:l})=>{o({code:I,key:B.value,scrollToField:()=>l[0]&&re(l[0].name,{behavior:"smooth",block:"center"})})})}).then(t=>(Object.assign(e,le(t)),tt())).then(t=>(Object.assign(e,t),Promise.resolve(e))).catch(t=>((t===I||(t==null?void 0:t.code)===I)&&(r.warning("校验未通过"),t.key&&(et(t.key),t.scrollToField&&setTimeout(()=>t.scrollToField(),150))),Promise.reject(null)))}function et(e){if(e===B.value){u("toggleTab","-1");return}let t=D.value;for(let o=0;o<t.length;o++)if(e==t[o].key){let l=o+"";if(K.value===l)break;if(u("toggleTab",l),t[o].relationType===0){let n=h(e);Vt(300,()=>n==null?void 0:n.validateTable())}break}}function tt(){return new Promise((e,t)=>v(this,null,function*(){let o={};try{let l=D.value;for(let n=0;n<l.length;n++){let c=l[n].key,P=h(c);if(l[n].relationType==1)try{let E=yield P.getAll();o[c]=[],o[c].push(E)}catch(E){return t(x({code:I,key:c},E))}else{if(yield P.fullValidateTable())return t({code:I,key:c});o[c]=P.getTableData()}}}catch(l){t(l)}e(o)}))}function ot(){return v(this,null,function*(){try{let e=yield ne();e=Object.assign({},F.value,e),e=le(e),S.value=!0,me(e)}catch(e){Array.isArray(e==null?void 0:e.errorFields)&&e.errorFields[0]&&re(e.errorFields[0].name,{behavior:"smooth",block:"center"})}finally{S.value=!1}})}function me(e){gt(ye,e).then(()=>{lt(e)}).catch(t=>{r.warning(t)})}function lt(e){Object.keys(e).map(n=>{Array.isArray(e[n])&&e[n].length==0&&(e[n]="")});let t=y.value,o=`${oe.optPre}${i.id}?tabletype=${w.value}`;t&&(o=`${t}?tabletype=${w.value}`),g.value===!0&&(e[Gt]=1);let l=p.value===!0?"put":"post";ee.request({url:o,method:l,params:e},{isTransformResponse:!1}).then(n=>{n.success?(n.result&&(e[Qt]=n.result),u("success",e),i.submitTip===!0&&r.success(n.message)):r.warning(n.message)}).finally(()=>{S.value=!1})}function nt(e,t,o){t&&o?o.vxeProps?o.setValues([{rowKey:t,values:e}]):o.setValues(e):C(e)}function at(e,t){let o={};o[e]=t,C(o)}const K=f("0"),pe=f("auto"),ce=f(340);function rt(e){if(p.value===!0){let t=F.value;return it(t,e)}return""}Nt(()=>i.tabIndex,e=>{K.value=e},{immediate:!0});function it(e,t){if(e){let o=e[t];return!o&&o!==0&&(o=e[t.toLowerCase()],!o&&o!==0&&(o=e[t.toUpperCase()])),o}return""}function st(e,t){if(s&&s[t+"_onlChange"]){let o=s[t+"_onlChange"](),l=Object.keys(e)[0];if(o[l]){let n=h(t).getFormEvent(),c=x({column:{key:l},value:e[l]},n);o[l].call(m,m,c)}}}function ut(e,t){if(s&&s[t+"_onlChange"]){let o=s[t+"_onlChange"](m);if(e.column==="all"){let l=Object.keys(o);if(l.length>0)for(let n of l)o[n].call(m,m,e)}else{let l=e.column.key||e.col.key;o[l]&&e.row&&e.row.id&&o[l].call(m,m,e)}}}function dt(e,t){var o;if(s&&s[t+"_onlChange"]){let l=s[t+"_onlChange"](m),n=Object.keys(l);if(n.length>0)for(let c of n)(o=l[c])==null||o.call(m,m,mo(x({},e),{row:e.deleteRows}))}}function mt(e,t){t.isModalData||se(e,t)}function pt(e){return"online_"+e+":"}function ct(e,t){return v(this,null,function*(){if(!s||!s.onlChange||!e)return!1;let o=s.onlChange();o[e]&&setTimeout(()=>v(this,null,function*(){let l={row:yield Q(),column:{key:e},value:t};o[e].call(m,m,l)}),0)})}function fe(e){let t=e.toLocaleString().match(oo);if(t.length>1){let o=t[1];Ie(o)}}function Z(e,t){if(e=="js"){let o=t+"_hook";s&&s[t]?s[t].call(m,m):s&&s[o]&&fe(s[o])}else if(e=="action"){let o=F.value,l={formId:i.id,buttonCode:t,dataId:o.id,uiFormData:Object.assign({},o)};ee.post({url:`${oe.urlButtonAction}`,params:l},{isTransformResponse:!1}).then(n=>{n.success?r.success("处理完成!"):r.warning("处理失败!")})}}function be(e){let t=h(e),o=[...t.getNewDataWithId(),...N.value[e]];if(!o||o.length==0)return!1;let l=[];for(let n of o)l.push(n.id);t.removeRowsById(l)}function ge(e,t){if(!t)return!1;let o=h(e);typeof t=="object"?o.addRows(t,!0):this.$message.error("添加子表数据,参数不识别!")}function ft(e,t){be(e),ge(e,t)}function bt(e,t){!t&&t.length<=0&&(t=[]),t.map(o=>{o.hasOwnProperty("label")||(o.label=o.text)}),G({field:e,componentProps:{options:t}})}function gt(e,t){return s&&s.beforeSubmit?s.beforeSubmit(e,t):Promise.resolve()}function ht(e,t){let o=A(M);e&&e.length>0?Object.keys(o).map(l=>{!l.endsWith("_load")&&e.indexOf(l)<0&&(M[l]=!1)}):t&&t.length>0&&Object.keys(o).map(l=>{t.indexOf(l)>=0&&(M[l]=!1)})}function yt(e,t){return v(this,null,function*(){y.value=t,yield ae(),F.value="",p.value=!0,yield ue(e),yield Se(()=>{Z("js","loaded")})})}function h(e){let t=z[e].value;if(t instanceof Array&&(t=t[0]),!t){r.warning("子表ref找不到:"+e);return}return t}function vt(){let e=O.reportPrintUrl,t=F.value.id,o=xt();Dt(e,t,o)}const[St,{openModal:he}]=Mt(),X=f(""),Y=f(!0);function Ft(e){X.value=e.id,Y.value=!1,he(!0,{isUpdate:!1,tableType:"3"})}function Ct(e){let t=h(e.key).getSelectedData();if(t.length!=1){r.warning("请选择一条数据");return}X.value=e.id,Y.value=!1,he(!0,{isUpdate:!0,record:t[0]})}function Tt(e){const t=e[Te];let o=ke(e,[Te]);if(o.id){let l=ke(x({},o),"id"),n=[{rowKey:o.id,values:l}];h(t).setValues(n)}else h(t).addRows(o,{isOnlineJS:!1,setActive:!1,emitChange:!0,isModalData:!0})}function kt(){let e=D.value;if(e&&e.length>0){for(let t of e)if(t.relationType!=1){let o=h(t.key);o&&o.clearSelection()}}}function wt(){let e=Q(),t=A(_[B.value]);q(t,o=>{C(o)},e)}function Ot(e,t){let o=D.value;if(o&&o.length>0){let l=o.filter(n=>n.key===e);if(l.length==0)return;if(l[0].relationType==1)h(e).executeFillRule();else{let n=A(_[e]),c=A(t.row);q(n,P=>{const{row:E,target:Bt}=t;let Pt=[{rowKey:E.id,values:x({},P)}];Bt.setValues(Pt)},c)}}}let ye={tableName:B,loading:S,subActiveKey:K,onlineFormRef:a,getFieldsValue:Q,setFieldsValue:C,submitFlowFlag:g,subFormHeight:pe,subTableHeight:ce,refMap:z,triggleChangeValues:nt,triggleChangeValue:at,sh:M,clearSubRows:be,addSubRows:ge,clearThenAddRows:ft,changeOptions:bt,isUpdate:p,getSubTableInstance:h,updateSchema:G,executeMainFillRule:wt,executeSubFillRule:Ot,changEvent:()=>{},onlineFormValueChange:Ge};return d(ye),{tableName:B,onlineFormRef:a,registerForm:_e,loading:S,subActiveKey:K,hasSubTable:Ae,subTabInfo:D,refMap:z,subFormHeight:pe,getSubTableForeignKeyValue:rt,isUpdate:p,handleSubFormChange:st,subTableHeight:ce,onlineFormDisabled:ie,subDataSource:N,getSubTableAuthPre:pt,handleAdded:mt,handleSubTableDefaultValue:se,handleValueChange:ut,openSubFormModalForAdd:Ft,openSubFormModalForEdit:Ct,handleRemoved:dt,show:Je,createRootProperties:Le,handleSubmit:Ze,sh:M,handleCgButtonClick:Z,handleCustomFormSh:ht,handleCustomFormEdit:yt,dbData:F,onOpenReportPrint:vt,onlineExtConfigJson:O,registerPopModal:St,popTableName:X,getPopFormData:Tt,popModalRequest:Y,onCloseModal:kt,rowNumber:W,getSubAddBtnCfg:H,getSubRemoveBtnCfg:b,getSubOpenAddBtnCfg:Be,getSubOpenEditBtnCfg:Pe}}},co=["id"],fo={key:1};function bo(i,u,r,a,R,S){const w=T("BasicForm"),y=T("a-tab-pane"),g=T("online-sub-form"),p=T("a-button"),U=T("JVxeTable"),W=T("a-tabs"),O=T("Loading"),m=T("online-pop-modal");return k(),$("div",{id:a.tableName+"_form",class:Kt(["onlineFormWrap",[`formTemplate_${r.formTemplate}`]])},[j(W,{class:"tabTheme",activeKey:a.subActiveKey,"onUpdate:activeKey":u[0]||(u[0]=d=>a.subActiveKey=d)},{default:V(()=>[(k(),J(y,{tab:"主表",key:"-1"},{default:V(()=>[j(w,{ref:"onlineFormRef",onRegister:a.registerForm},null,8,["onRegister"])]),_:1})),a.hasSubTable?(k(!0),$(Jt,{key:0},qt(a.subTabInfo,(d,H)=>(k(),J(y,{tab:d.describe,key:H+"",forceRender:!0},{default:V(()=>[d.relationType==1?(k(),$("div",{key:0,style:Ut({"overflow-y":"auto","overflow-x":"hidden","max-height":a.subFormHeight+"px"})},[j(g,{ref_for:!0,ref:a.refMap[d.key],table:d.key,disabled:a.onlineFormDisabled,"form-template":r.formTemplate,"main-id":a.getSubTableForeignKeyValue(d.foreignKey),properties:d.properties,"required-fields":d.requiredFields,"is-update":a.isUpdate,onFormChange:b=>a.handleSubFormChange(b,d.key)},null,8,["table","disabled","form-template","main-id","properties","required-fields","is-update","onFormChange"])],4)):(k(),$("div",fo,[j(U,{ref_for:!0,ref:a.refMap[d.key],toolbar:"","keep-source":"","row-number":a.rowNumber,"row-selection":"",height:a.subTableHeight,disabled:a.onlineFormDisabled,columns:d.columns,dataSource:a.subDataSource[d.key],addBtnCfg:a.getSubAddBtnCfg,removeBtnCfg:a.getSubRemoveBtnCfg,onValueChange:b=>a.handleValueChange(b,d.key),onRemoved:b=>a.handleRemoved(b,d.key),authPre:a.getSubTableAuthPre(d.key),onAdded:b=>a.handleAdded(d,b),onExecuteFillRule:b=>a.handleSubTableDefaultValue(d,b)},{toolbarSuffix:V(()=>[!a.onlineFormDisabled&&a.getSubOpenAddBtnCfg.enabled?(k(),J(p,{key:0,type:"primary",preIcon:a.getSubOpenAddBtnCfg.buttonIcon,onClick:b=>a.openSubFormModalForAdd(d)},{default:V(()=>[Fe("span",null,Ce(a.getSubOpenAddBtnCfg.buttonName),1)]),_:2},1032,["preIcon","onClick"])):te("",!0),!a.onlineFormDisabled&&a.getSubOpenEditBtnCfg.enabled?(k(),J(p,{key:1,type:"primary",preIcon:a.getSubOpenEditBtnCfg.buttonIcon,onClick:b=>a.openSubFormModalForEdit(d)},{default:V(()=>[Fe("span",null,Ce(a.getSubOpenEditBtnCfg.buttonName),1)]),_:2},1032,["preIcon","onClick"])):te("",!0)]),_:2},1032,["row-number","height","disabled","columns","dataSource","addBtnCfg","removeBtnCfg","onValueChange","onRemoved","authPre","onAdded","onExecuteFillRule"])]))]),_:2},1032,["tab"]))),128)):te("",!0)]),_:1},8,["activeKey"]),j(O,{loading:a.loading,absolute:!1},null,8,["loading"]),$t(i.$slots,"bottom",{},void 0,!0),j(m,{formTableType:"3",request:a.popModalRequest,id:a.popTableName,onRegister:a.registerPopModal,onSuccess:a.getPopFormData,topTip:"",isVxeTableData:""},null,8,["request","id","onRegister","onSuccess"])],10,co)}const Rl=Rt(po,[["render",bo],["__scopeId","data-v-ef315dba"]]);export{Rl as default};
