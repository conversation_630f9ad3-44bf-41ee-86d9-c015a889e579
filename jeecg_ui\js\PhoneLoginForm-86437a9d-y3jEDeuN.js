import{d as ee,f as s,r as D,e as u,u as o,J as oe,ag as y,aq as V,ar as k,F as te,k as p,aO as ie,aD as C,at as f,ah as ae,as as _,au as x,aB as q,G as ne}from"./vue-vendor-dy9k-Yad.js";import{cs as re,u as G,N as le,ah as se,ad as me,b6 as ue,bS as pe,aV as ce}from"./index-CCWaWN5g.js";import de from"./CaptchaModal-CFhSDiLW.js";import"./index-Diw57m_E.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./checkcode-DLY3GIII.js";import"./CustomModal-BakuIxQv.js";var E=(S,w,c)=>new Promise((t,n)=>{var v=r=>{try{i(c.next(r))}catch(m){n(m)}},g=r=>{try{i(c.throw(r))}catch(m){n(m)}},i=r=>r.done?t(r.value):Promise.resolve(r.value).then(v,g);i((c=c.apply(S,w)).next())});const ve=ee({__name:"PhoneLoginForm",props:{bindThirdAccount:{type:Boolean,default:!1}},emits:["login","login-success","bind-third-phone"],setup(S,{emit:w}){const{createMessage:c}=G(),{t}=le(),n=s(""),v=s(!1),g=s();D({randCodeImage:"",requestCodeSuccess:!1,checkKey:-1});const i=D({mobile:"",sms:""}),r=se(),m=w;s(!1);const K=u(()=>i.mobile!=""||o(n)==="mobile"?"current-active":""),z=u(()=>i.sms!=""||o(n)==="sms"?"current-active":""),{notification:H,createErrorModal:fe}=G(),B=s(!0),d=s(60),h=s(null),P=s(),I=s(),J=u(()=>M(t("sys.login.accountPlaceholder"))),W=u(()=>M(t("sys.login.smsPlaceholder"))),j=u(()=>({mobile:o(J),sms:o(W)})),R=u(()=>t("component.countdown.normalText")),T=u(()=>t("component.countdown.sendText",[o(d)])),F=S,[Q,{openModal:X}]=me();function M(a){return[{required:!0,message:a,trigger:"change"}]}function N(a){n.value=a,a==="sms"?P.value.focus():I.value.focus()}function O(){n.value=""}function L(){return E(this,null,function*(){g.value.validateFields().then(a=>E(this,null,function*(){if(F.bindThirdAccount)m("bind-third-phone",a);else try{v.value=!0;const{userInfo:e}=yield r.phoneLogin(oe({mobile:a.mobile,captcha:a.sms,mode:"none",goHome:!1}));e&&m("login-success",e.realname)}catch(e){H.error({message:t("sys.api.errorTip"),description:e.message||t("sys.api.networkExceptionMsg"),duration:3})}finally{v.value=!1}}))})}function U(){return E(this,null,function*(){if(!i.mobile){c.warn(t("sys.login.mobilePlaceholder"));return}(yield ue({mobile:i.mobile,smsmode:pe.FORGET_PASSWORD}).catch(a=>{a.code===ce.PHONE_SMS_FAIL_CODE&&X(!0,{})}))&&(o(h)||(d.value=60,B.value=!1,h.value=setInterval(()=>{o(d)>0&&o(d)<=60?d.value=d.value-1:(B.value=!0,clearInterval(o(h)),h.value=null)},1e3)))})}function Y(){m("login","accountLogin")}return(a,e)=>{const b=y("a-input"),A=y("a-form-item"),Z=y("a-button"),$=y("a-form");return k(),V(te,null,[p($,{ref_key:"loginPhoneRef",ref:g,model:i,rules:j.value,onKeyup:ie(L,["enter","native"])},{default:C(()=>[f("div",{class:_(["content-item",K.value]),onClick:e[1]||(e[1]=l=>N("mobile"))},[p(A,{name:"mobile"},{default:C(()=>[p(b,{ref_key:"mobileRef",ref:I,value:i.mobile,"onUpdate:value":e[0]||(e[0]=l=>i.mobile=l),style:{height:"40px"},onBlur:O},null,8,["value"]),f("div",{class:_(["form-title",n.value==="mobile"?"active-title":""])},x(o(t)("sys.login.mobile")),3)]),_:1})],2),f("div",{class:_(["content-item",z.value])},[p(A,{name:"sms",onClick:e[3]||(e[3]=l=>N("sms"))},{default:C(()=>[p(b,{ref_key:"smsCodeRef",ref:P,maxLength:6,value:i.sms,"onUpdate:value":e[2]||(e[2]=l=>i.sms=l),style:{height:"40px"},onBlur:O},null,8,["value"]),f("div",{class:_(["form-title",n.value==="sms"?"active-title":""])},x(o(t)("sys.login.smsCode")),3)]),_:1}),B.value?(k(),q(b,{key:0,type:"button",class:"aui-code-line pointer",bordered:!1,onClick:U,value:R.value,"onUpdate:value":e[4]||(e[4]=l=>R.value=l)},null,8,["value"])):(k(),q(b,{key:1,type:"button",class:"aui-code-line disabled-btn",bordered:!1,value:T.value,"onUpdate:value":e[5]||(e[5]=l=>T.value=l)},null,8,["value"]))],2),f("div",null,[p(Z,{type:"primary",onClick:L,loading:v.value,class:"login-btn"},{default:C(()=>[ne(x(o(t)("sys.login.loginButton")),1)]),_:1},8,["loading"])]),F.bindThirdAccount?ae("",!0):(k(),V("div",{key:0,class:"phone-login-btn pointer",onClick:Y},x(o(t)("sys.login.backSignIn")),1))]),_:1},8,["model","rules"]),p(de,{onRegister:o(Q),onOk:U},null,8,["onRegister"])],64)}}}),yo=re(ve,[["__scopeId","data-v-471134ff"]]);export{yo as default};
