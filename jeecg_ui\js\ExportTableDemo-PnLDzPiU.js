var z=Object.defineProperty,j=Object.defineProperties;var R=Object.getOwnPropertyDescriptors;var a=Object.getOwnPropertySymbols;var S=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable;var l=(o,t,e)=>t in o?z(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,s=(o,t)=>{for(var e in t||(t={}))S.call(t,e)&&l(o,e,t[e]);if(a)for(var e of a(t))T.call(t,e)&&l(o,e,t[e]);return o},d=(o,t)=>j(o,R(t));import{d as b,ag as u,aq as v,ar as D,k as p,aD as n,u as r,G as c}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as E}from"./useListPage-Soxgnx9a.js";import{j as A}from"./index-CCWaWN5g.js";import{Q as X}from"./componentMap-Bkie1n3v.js";import B from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const K={class:"p-4"},L=b({name:"basic-table-demo"}),Qt=b(d(s({},L),{setup(o){const t=[{title:"姓名",dataIndex:"name",width:170,align:"left",resizable:!0,sorter:{multiple:1}},{title:"关键词",dataIndex:"keyWord",width:130,resizable:!0},{title:"打卡时间",dataIndex:"punchTime",width:140,resizable:!0},{title:"工资",dataIndex:"salaryMoney",width:140,resizable:!0,sorter:{multiple:2}},{title:"奖金",dataIndex:"bonusMoney",width:140,resizable:!0},{title:"性别",dataIndex:"sex",sorter:{multiple:3},filters:[{text:"男",value:"1"},{text:"女",value:"2"}],customRender:({record:i})=>i.sex?i.sex=="1"?"男":"女":"",width:120,resizable:!0},{title:"生日",dataIndex:"birthday",width:120,resizable:!0},{title:"邮箱",dataIndex:"email",width:120,resizable:!0}],e=i=>A.get({url:"/test/jeecgDemo/list",params:i}),{tableContext:x,onExportXls:f,onImportXls:_}=E({designScope:"basic-table-demo-filter",tableProps:{title:"表单搜索",api:e,columns:t,showActionColumn:!1,useSearchForm:!1},exportConfig:{name:"示例列表",url:"/test/jeecgDemo/exportXls"},importConfig:{url:"/test/jeecgDemo/importExcel"}}),[g,{reload:M},{rowSelection:w,selectedRows:N,selectedRowKeys:P}]=x;function h(i){return[{label:"编辑",onClick:C.bind(null,i)}]}function C(i){}return(i,m)=>{const I=u("a-button"),y=u("j-upload-button");return D(),v("div",K,[p(r(B),{onRegister:r(g),rowSelection:r(w)},{tableTitle:n(()=>[p(I,{type:"primary",preIcon:"ant-design:export-outlined",onClick:r(f)},{default:n(()=>m[0]||(m[0]=[c(" 导出")])),_:1,__:[0]},8,["onClick"]),p(y,{type:"primary",preIcon:"ant-design:import-outlined",onClick:r(_)},{default:n(()=>m[1]||(m[1]=[c("导入")])),_:1,__:[1]},8,["onClick"])]),action:n(({record:k})=>[p(r(X),{actions:h(k)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"])])}}}));export{Qt as default};
