import{d as B,f as l,e as C,o as E,aq as r,ar as u,at as s,F as A,aC as k,as as h,au as i,aA as y}from"./vue-vendor-dy9k-Yad.js";import{a as M}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const R={class:"asset-ranking"},T={class:"ranking-content"},q={class:"ranking-section"},V={class:"ranking-list"},j={class:"rank-number"},z={class:"project-name"},G={class:"progress-container"},N={class:"progress-bar"},S={class:"progress-value"},I={class:"animated-number"},L={class:"ranking-section"},O={class:"ranking-list"},Q={class:"rank-number"},H={class:"project-name"},J={class:"progress-container"},K={class:"progress-bar"},U={class:"progress-value"},W={class:"animated-number"},X=B({__name:"AssetRanking",setup(Y){const o=l([{name:"资产处置项目A",value:5969},{name:"资产处置项目B",value:4569},{name:"资产处置项目C",value:3969},{name:"资产处置项目D",value:2969},{name:"资产处置项目E",value:1969},{name:"资产处置项目F",value:969},{name:"资产处置项目G",value:869}]),m=l([{name:"资产处置项目A",value:95.5},{name:"资产处置项目B",value:92.3},{name:"资产处置项目C",value:89.7},{name:"资产处置项目D",value:87.2},{name:"资产处置项目E",value:85.8},{name:"资产处置项目F",value:83.4},{name:"资产处置项目G",value:81.9}]),v=l(new Array(o.value.length).fill(!1)),d=l(new Array(m.value.length).fill(!1)),c=l(new Array(o.value.length).fill(0)),p=l(new Array(m.value.length).fill(0)),D=C(()=>Math.max(...o.value.map(n=>n.value))),g=(n,a,t,e)=>{const w=Date.now(),_=()=>{const P=Date.now()-w,f=Math.min(P/t,1),F=1-Math.pow(1-f,4),$=n+(a-n)*F;e(Math.round($*10)/10),f<1&&requestAnimationFrame(_)};requestAnimationFrame(_)},b=()=>{setTimeout(()=>{o.value.forEach((n,a)=>{setTimeout(()=>{v.value[a]=!0,g(0,n.value,2e3,t=>{c.value[a]=t})},a*200)}),m.value.forEach((n,a)=>{setTimeout(()=>{d.value[a]=!0,g(0,n.value,2e3,t=>{p.value[a]=t})},a*200)})},500)};return E(()=>{b()}),(n,a)=>(u(),r("div",R,[a[9]||(a[9]=s("div",{class:"ranking-bg"},null,-1)),s("div",T,[a[8]||(a[8]=s("div",{class:"main-title"},"资产处置数据排名",-1)),s("div",q,[a[3]||(a[3]=s("div",{class:"section-header"},[s("div",{class:"section-divider left"}),s("div",{class:"section-title"},"资产处置溢价额排名"),s("div",{class:"section-divider right"})],-1)),s("div",V,[(u(!0),r(A,null,k(o.value,(t,e)=>(u(),r("div",{key:e,class:"ranking-item"},[s("div",{class:h(["medal-icon",`rank-${e+1}`])},[a[0]||(a[0]=s("div",{class:"medal-bg"},null,-1)),s("span",j,i(e+1),1)],2),s("div",z,i(t.name),1),s("div",G,[s("div",N,[s("div",{class:"progress-fill",style:y({width:v.value[e]?`${t.value/D.value*100}%`:"0%",transitionDelay:`${e*200}ms`})},a[1]||(a[1]=[s("div",{class:"progress-diamond"},null,-1)]),4)]),s("div",S,[s("span",I,i(c.value[e]||0),1),a[2]||(a[2]=s("span",{class:"progress-unit"},"万",-1))])])]))),128))])]),s("div",L,[a[7]||(a[7]=s("div",{class:"section-header width-limit"},[s("div",{class:"section-divider left"}),s("div",{class:"section-title"},"资产处置溢价率排名"),s("div",{class:"section-divider right"})],-1)),s("div",O,[(u(!0),r(A,null,k(m.value,(t,e)=>(u(),r("div",{key:e,class:"ranking-item"},[s("div",{class:h(["medal-icon",`rank-${e+1}`])},[a[4]||(a[4]=s("div",{class:"medal-bg"},null,-1)),s("span",Q,i(e+1),1)],2),s("div",H,i(t.name),1),s("div",J,[s("div",K,[s("div",{class:"progress-fill",style:y({width:d.value[e]?`${t.value}%`:"0%",transitionDelay:`${e*200}ms`})},a[5]||(a[5]=[s("div",{class:"progress-diamond"},null,-1)]),4)]),s("div",U,[s("span",W,i(p.value[e]||0),1),a[6]||(a[6]=s("span",{class:"progress-unit"},"%",-1))])])]))),128))])])])]))}}),es=M(X,[["__scopeId","data-v-40815937"]]);export{es as default};
