import{ap as M,e as d,J as B,u as g,d as C,ag as h,aq as p,ah as y,ar as i,F,aC as D,q as b,aB as l,B as I,k as v,aD as K,aV as V,aQ as S,o as N,v as Q,as as A}from"./vue-vendor-dy9k-Yad.js";import E from"./index-CuNbEm2f.js";import{c5 as $,a as L,D as w,cQ as k,F as O,b5 as q}from"./index-CCWaWN5g.js";import{aM as z}from"./antd-vue-vendor-me9YkNVC.js";import{useMultipleTabSetting as R}from"./useMultipleTabSetting-QBbnIi9J.js";import{a as G}from"./useContentViewHeight-Md7r1NIg.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./usePageContext-CxiNGbPs.js";function H(){const e=M(),{currentRoute:s}=e,{getShowMultipleTab:a}=R(),r=$(),c=d(()=>n(B(e.getRoutes()))||[]),u=d(()=>r.getTabList.reduce((o,m)=>(m.meta&&Reflect.has(m.meta,"frameSrc")&&o.push(m.name),o),[]));function n(o){let m=[];for(const P of o){const{meta:{frameSrc:T}={},children:_}=P;T&&m.push(P),_&&_.length&&m.push(...n(_))}return m=z(m,"name"),m}function t(o){return o.name===g(s).name}function f(o){return g(a)?g(u).includes(o):e.currentRoute.value.name===o}return{hasRenderFrame:f,getFramePages:c,showIframe:t,getAllFramePages:n}}const J=C({name:"FrameLayout",components:{FramePage:E},setup(){const{getFramePages:e,hasRenderFrame:s,showIframe:a}=H(),r=d(()=>g(e).length>0);return{getFramePages:e,hasRenderFrame:s,showIframe:a,showFrame:r}}}),j={key:0};function U(e,s,a,r,c,u){const n=h("FramePage");return e.showFrame?(i(),p("div",j,[(i(!0),p(F,null,D(e.getFramePages,t=>(i(),p(F,{key:t.path},[t.meta.frameSrc&&e.hasRenderFrame(t.name)?b((i(),l(n,{key:0,frameSrc:t.meta.frameSrc},null,8,["frameSrc"])),[[I,e.showIframe(t)]]):y("",!0)],64))),128))])):y("",!0)}const W=L(J,[["render",U]]);function X({route:e,openCache:s,cacheTabs:a,enableTransition:r,def:c}){if(!r)return;const u=a.includes(e.name),n="fade-slide";let t=n;return s&&(t=u&&e.meta.loaded?n:void 0),t||e.meta.transitionName||c}const Y=C({name:"PageLayout",components:{FrameLayout:W},setup(){const{getShowMultipleTab:e}=R(),s=$(),{getOpenKeepAlive:a,getCanEmbedIFramePage:r}=w(),{getBasicTransition:c,getEnableTransition:u}=k(),n=d(()=>g(a)&&g(e)),t=d(()=>g(a)?s.getCachedTabList:[]);return{getTransitionName:X,openCache:n,getEnableTransition:u,getBasicTransition:c,getCaches:t,getCanEmbedIFramePage:r}}});function Z(e,s,a,r,c,u){const n=h("RouterView"),t=h("FrameLayout");return i(),p(F,null,[v(n,null,{default:K(({Component:f,route:o})=>[e.openCache?(i(),l(V,{key:0,include:e.getCaches},[(i(),l(S(f),{key:o.fullPath}))],1032,["include"])):(i(),l(S(f),{key:o.fullPath}))]),_:1}),e.getCanEmbedIFramePage?(i(),l(t,{key:0})):y("",!0)],64)}const x=L(Y,[["render",Z]]),ee=C({name:"LayoutContent",components:{PageLayout:x},setup(){const{prefixCls:e}=O("layout-content"),{getOpenPageLoading:s}=k(),{getLayoutContentMode:a,getPageLoading:r}=w(),u=q().openQianKun;return G(),N(()=>{}),{prefixCls:e,openQianKun:u,getOpenPageLoading:s,getLayoutContentMode:a,getPageLoading:r}}}),te={key:0,id:"content",class:"app-view-box"};function ne(e,s,a,r,c,u){const n=h("PageLayout"),t=Q("loading");return b((i(),p("div",{class:A([e.prefixCls,e.getLayoutContentMode])},[v(n),e.openQianKun=="true"?(i(),p("div",te)):y("",!0)],2)),[[t,e.getOpenPageLoading&&e.getPageLoading]])}const pe=L(ee,[["render",ne]]);export{pe as default};
