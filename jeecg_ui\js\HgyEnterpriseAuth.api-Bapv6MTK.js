import{u as a,j as r}from"./index-CCWaWN5g.js";const s=[{title:"创建/修改时的用户id",align:"center",dataIndex:"userId"},{title:"真实姓名",align:"center",dataIndex:"name"},{title:"审核状态（0待审核，1已通过，2，未通过）",align:"center",dataIndex:"review"},{title:"审核未通过说明",align:"center",dataIndex:"notes"},{title:"创建时间",align:"center",dataIndex:"createTime"},{title:"更新时间",align:"center",dataIndex:"updateTime"},{title:"创建人",align:"center",dataIndex:"createBy"},{title:"更新人",align:"center",dataIndex:"updateBy"},{title:"法人姓名",align:"center",dataIndex:"legalName"},{title:"企业名称",align:"center",dataIndex:"enterpriseName"},{title:"信用代码",align:"center",dataIndex:"creditCode"},{title:"联系人",align:"center",dataIndex:"relationUser"},{title:"联系电话",align:"center",dataIndex:"relationPhone"},{title:"法人证件类型(1:身份证,other:其他)",align:"center",dataIndex:"legalCartType"},{title:"证件id",align:"center",dataIndex:"cartId"},{title:"租户id",align:"center",dataIndex:"tenantId"},{title:"审核人",align:"center",dataIndex:"reviewUser"}],o=[{label:"企业名称",field:"enterpriseName",component:"JInput"},{label:"信用代码",field:"creditCode",component:"Input"},{label:"联系电话",field:"relationPhone",component:"Input"},{label:"法人证件类型(1:身份证,other:其他)",field:"legalCartType",component:"InputNumber"},{label:"证件id",field:"cartId",component:"Input"}],l=[{label:"真实姓名",field:"name",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入真实姓名!"}]},{label:"法人姓名",field:"legalName",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入法人姓名!"}]},{label:"企业名称",field:"enterpriseName",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入企业名称!"}]},{label:"信用代码",field:"creditCode",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入信用代码!"}]},{label:"联系人",field:"relationUser",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入联系人!"}]},{label:"联系电话",field:"relationPhone",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入联系电话!"}]},{label:"法人证件类型(1:身份证,other:其他)",field:"legalCartType",component:"InputNumber",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入法人证件类型(1:身份证,other:其他)!"}]},{label:"证件id",field:"cartId",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入证件id!"}]},{label:"",field:"id",component:"Input",show:!1}],p={userId:{title:"创建/修改时的用户id",order:0,view:"text",type:"string"},name:{title:"真实姓名",order:1,view:"text",type:"string"},review:{title:"审核状态（0待审核，1已通过，2，未通过）",order:2,view:"number",type:"number"},notes:{title:"审核未通过说明",order:3,view:"text",type:"string"},createTime:{title:"创建时间",order:4,view:"datetime",type:"string"},updateTime:{title:"更新时间",order:5,view:"datetime",type:"string"},createBy:{title:"创建人",order:7,view:"text",type:"string"},updateBy:{title:"更新人",order:8,view:"text",type:"string"},legalName:{title:"法人姓名",order:9,view:"text",type:"string"},enterpriseName:{title:"企业名称",order:10,view:"text",type:"string"},creditCode:{title:"信用代码",order:11,view:"text",type:"string"},relationUser:{title:"联系人",order:12,view:"text",type:"string"},relationPhone:{title:"联系电话",order:13,view:"text",type:"string"},legalCartType:{title:"法人证件类型(1:身份证,other:其他)",order:14,view:"number",type:"number"},cartId:{title:"证件id",order:15,view:"text",type:"string"},tenantId:{title:"租户id",order:16,view:"number",type:"number"},reviewUser:{title:"审核人",order:17,view:"text",type:"string"}};function u(e){return l}const{createConfirm:i}=a();const c="/hgy/personalCenter/hgyEnterpriseAuth/exportXls",m="/hgy/personalCenter/hgyEnterpriseAuth/importExcel",g=e=>r.get({url:"/hgy/personalCenter/hgyEnterpriseAuth/list",params:e}),h=(e,t)=>r.delete({url:"/hgy/personalCenter/hgyEnterpriseAuth/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),y=(e,t)=>{i({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>r.delete({url:"/hgy/personalCenter/hgyEnterpriseAuth/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},I=(e,t)=>{let n=t?"/hgy/personalCenter/hgyEnterpriseAuth/edit":"/hgy/personalCenter/hgyEnterpriseAuth/add";return r.post({url:n,params:e})};export{c as a,y as b,o as c,s as d,h as e,I as f,m as g,l as h,u as i,g as l,p as s};
