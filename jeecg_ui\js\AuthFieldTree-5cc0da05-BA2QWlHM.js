import{d as T,f,e as V,w as $,u as q,ag as h,aq as b,ar as x,aB as w,F as H,at as R,k as d,aD as y,G as k}from"./vue-vendor-dy9k-Yad.js";import{cs as G,u as J}from"./index-CCWaWN5g.js";import{D as j,B as L,C as Q}from"./auth.api-53df4c33-CWNFk1-w.js";import{aA as W,b9 as X,cb as Y,aK as Z,cc as ee}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var D=(e,l,i)=>new Promise((C,m)=>{var p=c=>{try{n(i.next(c))}catch(u){m(u)}},o=c=>{try{n(i.throw(c))}catch(u){m(u)}},n=c=>c.done?C(c.value):Promise.resolve(c.value).then(p,o);n((i=i.apply(e,l)).next())});const le=T({name:"AuthFieldTree",components:{DownCircleOutlined:ee,HomeOutlined:Z,UpCircleOutlined:Y,UndoOutlined:X,CheckOutlined:W},props:{cgformId:{type:String,required:!0}},setup(e){const{createMessage:l}=J(),i=f(""),C=f(1),m=f(!0),p=f([]),o=f([]),n=f([]),c=f([]),u=f(""),O=V(()=>!i.value);$(()=>e.cgformId,v,{immediate:!0});function v(){return D(this,null,function*(){if(!e.cgformId)return;let a=yield j(e.cgformId,C.value),t=[],r=[];a.forEach(s=>{r.includes(s.code)||(r.push(s.code),t.push({key:s.code,title:s.title}))});for(let s of t){let K=[];for(let E of a)if(s.key===E.code){let F=I(E);K.push({key:E.id,title:F})}s.children=K}c.value=t,p.value=[...r],n.value=r})}function I(a){let t="";return a.page==3?t+="列表":a.page==5&&(t+="表单"),a.control==3?t+="可编辑":a.control==5&&(t+="可见"),t}function g(a,t){return D(this,null,function*(){i.value=a,u.value=t,o.value=[],yield v();let r=yield L({roleId:a,cgformId:e.cgformId,type:C.value,authMode:t});o.value=r.map(s=>s.authId)})}function S(){i.value="",v()}function A(){v(),g(i.value,u.value)}function U(){return D(this,null,function*(){let a=o.value.filter(t=>n.value.indexOf(t)<0);yield Q(i.value,e.cgformId,{authId:JSON.stringify(a),authMode:u.value}),l.success("保存成功")})}function _(){p.value=[...n.value]}function z(){p.value=[]}function B(a){p.value=a,m.value=!1}function P(){i.value="",o.value=[]}function M(){o.value=[]}function N(){const a=function(t){for(let r of t)o.value.push(r.key),r.children&&r.children.length>0&&a.call(null,r.children)};o.value=[],a.call(null,q(c))}return{loadChecked:g,clear:P,expandedKeys:p,autoExpandParent:m,checkedKeys:o,treeData:c,disabled:O,onSave:U,onExpand:B,clearChecked:S,onCloseAll:z,onExpandAll:_,onRefresh:A,onClearSelected:M,onSelectAll:N}}}),ae={class:"onl-auth-tree-btns"};function te(e,l,i,C,m,p){const o=h("a-empty"),n=h("a-button"),c=h("DownCircleOutlined"),u=h("UpCircleOutlined"),O=h("CheckOutlined"),v=h("UndoOutlined"),I=h("a-tree");return x(),b("div",null,[e.disabled?(x(),w(o,{key:0,description:"请先选中左侧角色/部门/用户"})):e.treeData.length===0?(x(),w(o,{key:1,description:"无权限信息"})):(x(),b(H,{key:2},[R("div",ae,[d(n,{onClick:e.onRefresh,size:"small",type:"primary",preIcon:"ant-design:redo",ghost:""},{default:y(()=>l[1]||(l[1]=[k("刷新")])),_:1},8,["onClick"]),d(n,{onClick:e.onExpandAll,size:"small",type:"primary",ghost:""},{default:y(()=>[d(c),l[2]||(l[2]=k("展开"))]),_:1},8,["onClick"]),d(n,{onClick:e.onCloseAll,size:"small",type:"primary",ghost:""},{default:y(()=>[d(u),l[3]||(l[3]=k("折叠"))]),_:1},8,["onClick"]),d(n,{onClick:e.onSave,size:"small",type:"primary",preIcon:"ant-design:save",ghost:""},{default:y(()=>l[4]||(l[4]=[k("保存")])),_:1},8,["onClick"]),d(n,{onClick:e.onSelectAll,size:"small",type:"primary",ghost:""},{default:y(()=>[d(O),l[5]||(l[5]=k("全选"))]),_:1},8,["onClick"]),d(n,{onClick:e.onClearSelected,size:"small",type:"primary",ghost:""},{default:y(()=>[d(v),l[6]||(l[6]=k("重置"))]),_:1},8,["onClick"])]),d(I,{checkable:"",checkedKeys:e.checkedKeys,"onUpdate:checkedKeys":l[0]||(l[0]=g=>e.checkedKeys=g),expandedKeys:e.expandedKeys,autoExpandParent:e.autoExpandParent,treeData:e.treeData,onExpand:e.onExpand},null,8,["checkedKeys","expandedKeys","autoExpandParent","treeData","onExpand"])],64))])}const de=G(le,[["render",te],["__scopeId","data-v-e6dabb1d"]]);export{de as default};
