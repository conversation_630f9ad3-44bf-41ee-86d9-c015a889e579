import{e}from"./vue-vendor-dy9k-Yad.js";import{B as p}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function h(){const t=p(),o=e(()=>t.getMultiTabsSetting.show),i=e(()=>t.getMultiTabsSetting.showQuick),s=e(()=>t.getMultiTabsSetting.showRedo),n=e(()=>t.getMultiTabsSetting.showFold),g=e(()=>t.getMultiTabsSetting.theme);function u(l){t.setProjectConfig({multiTabsSetting:l})}return{setMultipleTabSetting:u,getShowMultipleTab:o,getShowQuick:i,getShowRedo:s,getShowFold:n,getTabsTheme:g}}export{h as useMultipleTabSetting};
