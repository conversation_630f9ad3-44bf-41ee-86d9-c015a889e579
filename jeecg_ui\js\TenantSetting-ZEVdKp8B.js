var fe=Object.defineProperty,ve=Object.defineProperties;var me=Object.getOwnPropertyDescriptors;var H=Object.getOwnPropertySymbols;var ge=Object.prototype.hasOwnProperty,_e=Object.prototype.propertyIsEnumerable;var K=(m,d,l)=>d in m?fe(m,d,{enumerable:!0,configurable:!0,writable:!0,value:l}):m[d]=l,Q=(m,d)=>{for(var l in d||(d={}))ge.call(d,l)&&K(m,l,d[l]);if(H)for(var l of H(d))_e.call(d,l)&&K(m,l,d[l]);return m},W=(m,d)=>ve(m,me(d));var V=(m,d,l)=>new Promise((b,u)=>{var A=i=>{try{U(l.next(i))}catch(k){u(k)}},N=i=>{try{U(l.throw(i))}catch(k){u(k)}},U=i=>i.done?b(i.value):Promise.resolve(i.value).then(A,N);U((l=l.apply(m,d)).next())});import{d as te,f as p,o as xe,ag as x,aq as f,ar as r,at as t,k as o,aB as M,G as y,ah as ye,au as v,F,aC as X,q as ke,A as P,B as we,as as Ce,u as g,aD as a}from"./vue-vendor-dy9k-Yad.js";import{e as Te,f as he,h as Ue,c as Ie,i as be}from"./UserSetting.api-BJ086Ekj.js";import{F as ze,ah as Ne,u as Se,ch as Y,r as Z,a as Oe}from"./index-CCWaWN5g.js";import{M as ee}from"./antd-vue-vendor-me9YkNVC.js";import{U as Pe}from"./index-Dyko68ZT.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const $e={class:"my-tenant"},Be={key:0,class:"approved-count"},De={key:0,class:"tenant-list"},Ve=["onClick"],Me={class:"tenant-title"},Ae={class:"item-left"},Ee={class:"item-name"},Le={class:"vip-message"},Re=["onClick"],Fe={class:"item-right"},je={key:0},qe=["onClick"],Ge={key:1},Je=["onClick"],He=["onClick"],Ke={key:2,style:{width:"75px"}},Qe={style:{"margin-left":"24px"}},We={class:"item-content"},Xe={class:"content-box"},Ye={class:"content-desc"},Ze={class:"flex-flow"},et={style:{"font-size":"13px",color:"#000000"}},tt={class:"flex-flow"},nt={style:{"font-size":"13px"}},st={class:"flex-flow"},ot={style:{"font-size":"13px"}},at={class:"footer-box"},lt=["onClick"],it={key:1,class:"font-color9e flex-center margin-right40 font-size13"},rt=["onClick"],dt={key:3,class:"font-color9e flex-center margin-right40 font-size13"},ut={style:{"margin-top":"24px","font-size":"13px",padding:"0 24px"}},pt={class:"margin-top6 margin-bottom-16"},ct={class:"margin-top6 margin-bottom-16"},ft={class:"margin-top6 margin-bottom-16"},vt={class:"margin-top6 margin-bottom-16"},mt={class:"margin-top6 margin-bottom-16"},gt={class:"cancellation"},_t={style:{padding:"20px"}},xt=["onClick"],yt=["onClick"],kt=te({name:"tenant-setting"}),wt=te(W(Q({},kt),{setup(m){const{prefixCls:d}=ze("j-user-tenant-setting-container"),l=p([]),b=Ne(),{createMessage:u}=Se(),A=p([]),N=p(!1),U=p([]),i=p({realname:b.getUserInfo.realname,workNo:b.getUserInfo.workNo,orgCodeTxt:b.getUserInfo.orgCodeTxt,postText:b.getUserInfo.postText});function k(){return V(this,null,function*(){Te({userTenantStatus:"1,3,5"}).then(n=>{if(n.success)if(n.result&&n.result.length>0){let e=n.result,c=[],O=[];for(let _=0;_<e.length;_++)e[_].userTenantStatus==="5"&&O.push(e[_]),c.push(e[_]);l.value=c,L.value=O,B.value=O.length}else j();else j()})})}function j(){l.value=[],L.value=[],B.value=0}function ne(n){const e=document.createElement("input");e.setAttribute("value",n),document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),u.success("复制成功")}function se(n){ee.confirm({title:"取消申请",content:"是否取消申请",okText:"确认",cancelText:"取消",onOk:()=>{he({tenantId:n}).then(e=>{e.success?(u.success("取消申请成功"),k()):u.warning(e.message)}).catch(e=>{u.warning(e.message)})}})}function oe(n){n.show?n.show=!1:n.show=!0}function Ct(n){let e=A.value.filter(c=>{c.value==n});return e&&e.length>0?e[0].label:"未填写"}function q(n,e){U.value=e,n==="editTenant"?N.value=!0:n==="exitTenant"&&(w.value={loginPassword:"",tenantName:""},S.value=!0,I.value=!0,T.value=e)}const I=p(!1),w=p({}),T=p({}),S=p(!0),$=p(""),E=p(!1);function ae(){let n=g(T).name,e=g(w).tenantName;n===e?S.value=!1:S.value=!0}function le(){return V(this,null,function*(){if(!g(w).loginPassword){u.warning("请输入登录密码");return}yield Ue({id:g(T).tenantUserId,loginPassword:g(w).loginPassword}).then(n=>{if(n.success)u.success(n.message),I.value=!1,k(),Y(g(T).tenantUserId);else if(n.message==="assignedOwen")E.value=!0,I.value=!1;else if(n.message==="cancelTenant"){I.value=!1;let e=Z.currentRoute.value.fullPath;ee.confirm({title:"您是该组织的拥有者",content:"该组织下没有其他成员，需要您前往注销",okText:"前往注销",okType:"danger",cancelText:"取消",onOk:()=>{e!=="/system/usersetting"&&Z.push("/myapps/settings/organization/organMessage/"+g(T).tenantUserId)}})}else u.warning(n.message)}).catch(n=>{u.warning(n.message)})})}function ie(){I.value=!1,S.value=!0}function re(){if(!g($)){u.warning("请选择变更拥有者");return}Ie({userId:g($),tenantId:g(T).tenantUserId}).then(n=>{n.success?(u.success(n.message),k(),Y(g(T).tenantUserId)):u.warning(n.message)})}const B=p(0),L=p([]),R=p(!1);function de(){R.value=!0}function D(n,e){return V(this,null,function*(){yield be({tenantId:Number.parseInt(n),status:e}),k()})}return xe(()=>{k()}),(n,e)=>{const c=x("Icon"),O=x("a-empty"),_=x("a-modal"),C=x("a-col"),ue=x("a-input"),z=x("a-row"),G=x("a-form-item"),pe=x("a-input-password"),ce=x("a-form"),J=x("a-button");return r(),f(F,null,[t("div",{class:Ce(["tenant-padding",[`${g(d)}`]])},[t("div",$e,[e[8]||(e[8]=t("span",{style:{flex:"1"}},"我的组织",-1)),t("span",{class:"invited",onClick:de},[e[7]||(e[7]=y("我的受邀信息")),B.value>0?(r(),f("span",Be,v(B.value),1)):ye("",!0)])]),l.value.length>0?(r(),f("div",De,[(r(!0),f(F,null,X(l.value,s=>(r(),f("div",{class:"tenant-list-item",onClick:h=>oe(s)},[t("div",Me,[t("div",Ae,[t("div",Ee,v(s.name),1),t("div",Le,[t("div",{class:"item-house",onClick:P(h=>ne(s.houseNumber),["stop"])},[t("span",null,[y(" 组织门牌号："+v(s.houseNumber)+" ",1),o(c,{icon:"ant-design:copy-outlined",style:{"font-size":"13px","margin-left":"2px"}})])],8,Re)])]),t("div",Fe,[s.userTenantStatus==="3"?(r(),f("span",je,[e[9]||(e[9]=t("span",{class:"pointer examine"},"待审核",-1)),t("span",{class:"pointer cancel-apply",onClick:P(h=>se(s.tenantUserId),["stop"])},"取消申请",8,qe)])):s.userTenantStatus==="5"?(r(),f("span",Ge,[t("span",{class:"pointer examine",onClick:h=>D(s.tenantUserId,"1")},"加入",8,Je),t("span",{class:"pointer cancel-apply",onClick:P(h=>D(s.tenantUserId,"4"),["stop"])},"拒绝",8,He)])):(r(),f("div",Ke)),t("span",Qe,[s.show?(r(),M(c,{key:0,icon:"ant-design:down-outlined",style:{"font-size":"13px",color:"#707070"}})):(r(),M(c,{key:1,icon:"ant-design:right-outlined",style:{"font-size":"13px",color:"#707070"}}))])])]),ke(t("div",We,[t("div",Xe,[e[13]||(e[13]=t("div",{class:"content-name"}," 组织名片 ",-1)),t("div",Ye,[t("div",Ze,[e[10]||(e[10]=t("div",{class:"content-des-text"},"姓名",-1)),t("div",et,v(i.value.realname),1)]),t("div",tt,[e[11]||(e[11]=t("div",{class:"content-des-text"},"部门",-1)),t("div",nt,v(i.value.orgCodeTxt?i.value.orgCodeTxt:"未填写"),1)]),t("div",st,[e[12]||(e[12]=t("div",{class:"content-des-text"},"职业",-1)),t("div",ot,v(i.value.postText?i.value.postText:"未填写"),1)])])]),t("div",at,[s.userTenantStatus!=="3"?(r(),f("span",{key:0,onClick:P(h=>q("editTenant",s),["stop"]),class:"font-color333 flex-center margin-right40 font-size13 pointer"},[o(c,{icon:"ant-design:edit-outlined",class:"footer-icon"}),e[14]||(e[14]=t("span",null,"查看租户名片",-1))],8,lt)):(r(),f("span",it,[o(c,{icon:"ant-design:edit-outlined",class:"footer-icon"}),e[15]||(e[15]=t("span",null,"查看租户名片",-1))])),s.userTenantStatus!=="3"?(r(),f("span",{key:2,onClick:P(h=>q("exitTenant",s),["stop"]),class:"font-color333 flex-center margin-right40 font-size13 pointer"},[o(c,{icon:"ant-design:export-outlined",class:"footer-icon"}),e[16]||(e[16]=t("span",null,"退出租户",-1))],8,rt)):(r(),f("span",dt,[o(c,{icon:"ant-design:export-outlined",class:"footer-icon"}),e[17]||(e[17]=t("span",null,"退出租户",-1))]))])],512),[[we,s.show]])],8,Ve))),256))])):(r(),M(O,{key:1,description:"暂无数据",style:{position:"relative",top:"50px"}}))],2),o(_,{open:N.value,"onUpdate:open":e[0]||(e[0]=s=>N.value=s),width:"400px",wrapClassName:"edit-tenant-setting"},{title:a(()=>e[18]||(e[18]=[t("div",{style:{"font-size":"17px","font-weight":"700"}},"查看名片",-1),t("div",{style:{color:"#9e9e9e","margin-top":"10px","font-size":"13px"}}," 名片是您在该组织下的个人信息，只在本组织中展示。 ",-1)])),default:a(()=>[t("div",ut,[e[19]||(e[19]=t("div",{class:"font-color75"},"姓名",-1)),t("div",pt,v(i.value.realname),1),e[20]||(e[20]=t("div",null,"部门",-1)),t("div",ct,v(i.value.orgCodeTxt?i.value.orgCodeTxt:"未填写"),1),e[21]||(e[21]=t("div",null,"职位",-1)),t("div",ft,v(i.value.postText?i.value.postText:"未填写"),1),e[22]||(e[22]=t("div",null,"工作地点",-1)),t("div",vt,v(U.value.workPlace?U.value.workPlace:"未填写"),1),e[23]||(e[23]=t("div",null,"工号",-1)),t("div",mt,v(i.value.workNo?i.value.workNo:"未填写"),1)])]),_:1},8,["open"]),o(_,{open:I.value,"onUpdate:open":e[3]||(e[3]=s=>I.value=s),width:"800","destroy-on-close":""},{title:a(()=>[t("div",gt,[o(c,{icon:"ant-design:warning-outlined",style:{"font-size":"20px",color:"red"}}),y(" 退出租户 "+v(T.value.name),1)])]),footer:a(()=>[o(J,{type:"primary",onClick:le,disabled:S.value},{default:a(()=>e[26]||(e[26]=[y("确定")])),_:1,__:[26]},8,["disabled"]),o(J,{onClick:ie},{default:a(()=>e[27]||(e[27]=[y("取消")])),_:1,__:[27]})]),default:a(()=>[o(ce,{model:w.value,ref:"cancelTenantRef"},{default:a(()=>[o(G,{name:"tenantName"},{default:a(()=>[o(z,{span:24,style:{padding:"20px 20px 0","font-size":"13px"}},{default:a(()=>[o(C,{span:24},{default:a(()=>e[24]||(e[24]=[y(" 请输入租户名称 ")])),_:1,__:[24]}),o(C,{span:24,style:{"margin-top":"10px"}},{default:a(()=>[o(ue,{value:w.value.tenantName,"onUpdate:value":e[1]||(e[1]=s=>w.value.tenantName=s),onChange:ae},null,8,["value"])]),_:1})]),_:1})]),_:1}),o(G,{name:"loginPassword"},{default:a(()=>[o(z,{span:24,style:{padding:"0 20px","font-size":"13px"}},{default:a(()=>[o(C,{span:24},{default:a(()=>e[25]||(e[25]=[y(" 请输入您的登录密码 ")])),_:1,__:[25]}),o(C,{span:24,style:{"margin-top":"10px"}},{default:a(()=>[o(pe,{value:w.value.loginPassword,"onUpdate:value":e[2]||(e[2]=s=>w.value.loginPassword=s)},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["open"]),o(_,{title:"变更拥有者",open:E.value,"onUpdate:open":e[5]||(e[5]=s=>E.value=s),width:"800","destroy-on-close":"",cancelButtonProps:{display:"none"},onOk:re},{default:a(()=>[t("div",_t,[o(z,{span:24},{default:a(()=>e[28]||(e[28]=[t("div",{class:"change-owen"}," 只有变更拥有着之后,才能退出 ",-1)])),_:1,__:[28]}),o(z,{span:24,style:{"margin-top":"10px"}},{default:a(()=>[o(Pe,{value:$.value,"onUpdate:value":e[4]||(e[4]=s=>$.value=s),izExcludeMy:""},null,8,["value"])]),_:1})])]),_:1},8,["open"]),o(_,{title:"我的受邀信息",open:R.value,"onUpdate:open":e[6]||(e[6]=s=>R.value=s),footer:null},{default:a(()=>[o(z,{span:24,class:"invited-row"},{default:a(()=>[o(C,{span:16},{default:a(()=>e[29]||(e[29]=[y(" 组织 ")])),_:1,__:[29]}),o(C,{span:8},{default:a(()=>e[30]||(e[30]=[y(" 操作 ")])),_:1,__:[30]})]),_:1}),(r(!0),f(F,null,X(L.value,s=>(r(),M(z,{span:24,class:"invited-row-list"},{default:a(()=>[o(C,{span:16},{default:a(()=>[y(v(s.name),1)]),_:2},1024),o(C,{span:8},{default:a(()=>[t("span",{class:"common",onClick:h=>D(s.tenantUserId,"1")},"加入",8,xt),t("span",{class:"common refuse",onClick:h=>D(s.tenantUserId,"4")},"拒绝",8,yt)]),_:2},1024)]),_:2},1024))),256)),e[31]||(e[31]=t("div",{style:{height:"20px"}},null,-1))]),_:1,__:[31]},8,["open"])],64)}}})),Rt=Oe(wt,[["__scopeId","data-v-e43d7f9f"]]);export{Rt as default};
