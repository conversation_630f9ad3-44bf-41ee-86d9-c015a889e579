import{d as B,f as H,e as f,ag as u,aq as K,ar as N,at as a,k as l,u as i,aD as r,H as d,aE as k}from"./vue-vendor-dy9k-Yad.js";import{j as R}from"./index-CCWaWN5g.js";import{b as D}from"./useSettings-4a774f12-DjycrGR6.js";import{_ as E}from"./VarPicker.vue_vue_type_script_setup_true_lang-5fb9829d-C8jb2Rkc.js";import"./antd-vue-vendor-me9YkNVC.js";import"./VarListPicker.vue_vue_type_style_index_0_scoped_9a10b0de_lang-4ed993c7-l0sNRNKZ.js";import"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./VarListEditor.vue_vue_type_style_index_0_scoped_407b7ab3_lang-4ed993c7-l0sNRNKZ.js";import{u as T}from"./VarListShow.vue_vue_type_script_setup_true_lang-9bf001aa-DcNRZKsU.js";import"./VarTextarea.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import"./VarEditable.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const $={class:"knowledge-setting"},A={class:"setting-item"},F={class:"setting-item"},G={style:{display:"flex"}},J={class:"setting-item"},L={class:"setting-item"},M={class:"label"},Q={class:"setting-item"},oe=B({__name:"KnowledgeSetting",props:{type:{type:String,required:!0},node:{type:Object,required:!0},properties:{type:Object,required:!0},setProperties:{type:Function,required:!0}},setup(_){const I=_,{inputParams:p,outputParams:q,prevVariables:z,createOptionRef:m}=D(I),h=m("knowIds"),o=m("topNumber"),b=H([]);function C(){R.get({url:"/sys/dict/getDictItems/airag_knowledge%20where%20status%20=%20'enable',name,id"}).then(e=>{b.value=e})}C();function U(t){h.value=t}const v=f({get(){return p.value[0]?p.value[0]:{field:"",nodeId:""}},set(t){p.value=[t]}});function P(t){t!=null&&t.nodeId?v.value={field:t.field,nodeId:t.nodeId}:v.value={field:"",nodeId:""}}const n=m("similarity"),c=f({get(){return n.value!==null},set(t){n.value=t?.75:null}}),y=f(()=>({min:0,max:1,step:.01,disabled:!c.value}));return(t,e)=>{const O=u("a-select"),x=u("a-input-number"),w=u("a-slider"),g=u("a-space"),S=u("a-switch"),V=u("a-button"),j=u("a-popover");return N(),K("div",$,[a("div",A,[e[6]||(e[6]=a("div",{class:"label"},"查询变量",-1)),l(i(E),{vars:i(z),item:v.value,onChange:e[0]||(e[0]=s=>P(s))},null,8,["vars","item"])]),a("div",F,[e[9]||(e[9]=a("div",{class:"label"},"知识库",-1)),a("div",G,[l(O,{value:i(h),options:b.value,mode:"multiple",placeholder:"请选择知识库",style:{flex:"1","margin-right":"8px"},onChange:U},null,8,["value","options"]),l(j,{trigger:"click",placement:"bottomRight"},{content:r(()=>[a("div",J,[e[7]||(e[7]=a("div",{class:"label"},"Top K",-1)),l(g,null,{default:r(()=>[l(x,{value:i(o),"onUpdate:value":e[1]||(e[1]=s=>d(o)?o.value=s:null),min:1,max:10,size:"small",style:{width:"80px"}},null,8,["value"]),l(w,{value:i(o),"onUpdate:value":e[2]||(e[2]=s=>d(o)?o.value=s:null),min:1,max:10,size:"small",style:{width:"240px"}},null,8,["value"])]),_:1})]),a("div",L,[a("div",M,[l(g,null,{default:r(()=>[l(S,{checked:c.value,"onUpdate:checked":e[3]||(e[3]=s=>c.value=s),size:"small"},null,8,["checked"]),e[8]||(e[8]=a("div",null,"Score 阈值",-1))]),_:1})]),l(g,null,{default:r(()=>[l(x,k({value:i(n),"onUpdate:value":e[4]||(e[4]=s=>d(n)?n.value=s:null)},y.value,{size:"small",style:{width:"80px"}}),null,16,["value"]),l(w,k({value:i(n),"onUpdate:value":e[5]||(e[5]=s=>d(n)?n.value=s:null)},y.value,{size:"small",style:{width:"240px"}}),null,16,["value"])]),_:1})])]),default:r(()=>[l(V,{preIcon:"ant-design:setting",style:{width:"40px"}})]),_:1})])]),a("div",Q,[e[10]||(e[10]=a("div",{class:"label"},"输出变量",-1)),l(i(T),{vars:i(q)},null,8,["vars"])])])}}});export{oe as m};
