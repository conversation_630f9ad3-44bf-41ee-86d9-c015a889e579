var H=Object.defineProperty;var L=Object.getOwnPropertySymbols;var J=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable;var q=(p,n,s)=>n in p?H(p,n,{enumerable:!0,configurable:!0,writable:!0,value:s}):p[n]=s,N=(p,n)=>{for(var s in n||(n={}))J.call(n,s)&&q(p,s,n[s]);if(L)for(var s of L(n))Q.call(n,s)&&q(p,s,n[s]);return p};var _=(p,n,s)=>new Promise((C,I)=>{var c=l=>{try{m(s.next(l))}catch(g){I(g)}},y=l=>{try{m(s.throw(l))}catch(g){I(g)}},m=l=>l.done?C(l.value):Promise.resolve(l.value).then(c,y);m((s=s.apply(p,n)).next())});import{d as K,f as u,ap as W,aN as X,r as B,o as Y,ag as Z,aq as h,ar as v,at as x,k as f,ah as F,u as M,H as R,aD as b,G as D,F as w,aB as ee}from"./vue-vendor-dy9k-Yad.js";import{f as V}from"./antd-vue-vendor-me9YkNVC.js";import{bF as te,a as ie}from"./index-CCWaWN5g.js";import ae from"./Step1-CRBQksQr.js";import oe from"./Step2-BV3U9LXN.js";import{b as re,u as se,s as ne}from"./SupplyDemand-DK00S9Ao.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const le={class:"publish-supply"},pe={class:"steps-wrapper"},ce={class:"step-content"},me={key:0},de={class:"step-actions"},ue={key:1},fe={class:"step-actions"},ye=K({__name:"index",setup(p){const n=u(),s=u(),C=W(),I=X(),c=u(0),y=u(!1),m=u(!1),l=u(!1),g=u(""),A=u(2),P=[{title:"编辑求购信息",description:"填写求购的基本信息"},{title:"发布求购信息",description:"填写联系人信息并发布"}];let i=B({materialType:"",basicInfo:{infoTitle:"",brand:"",model:"",depreciationDegree:9},specification:{province:"110000",city:"110100",area:"110101",storageMethod:1,quantity:"",unit:"台",price:""},display:{images:"",videos:"",highlights:"",materialDesc:""},attachmentList:[]}),d=B({validity:{validDate:""},contactInfo:{contactName:"",contactPhone:""}});const O=a=>{a<c.value&&(c.value=a)},E=a=>{},U=()=>{m.value=!0,navigator.geolocation?navigator.geolocation.getCurrentPosition(a=>{setTimeout(()=>{m.value=!1,V.success("位置获取成功")},1e3)},a=>{m.value=!1,V.error("位置获取失败，请手动输入地址")}):(m.value=!1,V.error("浏览器不支持地理位置获取"))},G=a=>_(null,null,function*(){var e,r;try{const t=yield re({id:a});if(t){const{hgySupplyDemand:o,hgyEntrustOrder:T,attachmentList:k}=t;o&&(i.materialType=o.materialType||"",i.basicInfo.infoTitle=o.infoTitle||"",i.basicInfo.brand=o.brand||"",i.basicInfo.model=o.model||"",i.basicInfo.depreciationDegree=o.depreciationDegree||9,i.specification.province=o.province||"",i.specification.city=o.city||"",i.specification.area=o.district||"",i.specification.storageMethod=o.storageMethod||1,i.specification.quantity=((e=o.quantity)==null?void 0:e.toString())||"",i.specification.unit=o.unit||"",i.specification.price=((r=o.price)==null?void 0:r.toString())||"",i.display.highlights=o.highlights||"",i.display.materialDesc=o.materialDesc||"",A.value=o.status||2),o&&(d.validity.validDate=o.validDate||"",d.contactInfo.contactName=o.relationUser||"",d.contactInfo.contactPhone=o.relationPhone||""),k&&Array.isArray(k)&&k.length>0&&(i.attachmentList=k)}}catch(t){}});Y(()=>_(null,null,function*(){const a=I.query.id;a&&(l.value=!0,g.value=a,G(a))}));const z=a=>{if(!a)return"other";switch(a.toLowerCase().split(".").pop()){case"jpg":case"jpeg":case"png":case"gif":case"bmp":case"webp":return"image";case"mp4":case"avi":case"mov":case"wmv":case"flv":case"webm":return"video";case"mp3":case"wav":case"flac":case"aac":return"mp3";case"pdf":return"pdf";case"doc":case"docx":return"word";case"xls":case"xlsx":return"excel";case"ppt":case"pptx":return"ppt";case"zip":case"rar":case"7z":return"zip";default:return"other"}},$=a=>[...(()=>{try{const e=typeof i.display.images=="string"?JSON.parse(i.display.images):i.display.images;return(Array.isArray(e)?e:[]).map(r=>({bizType:a,fileName:r.fileName,filePath:r.filePath,fileSize:r.fileSize||0,fileType:z(r.filePath)}))}catch(e){return[]}})(),...(()=>{try{const e=typeof i.display.videos=="string"?JSON.parse(i.display.videos):i.display.videos;return(Array.isArray(e)?e:[]).map(r=>({bizType:a,fileName:r.fileName,filePath:r.filePath,fileSize:r.fileSize,fileType:z(r.filePath)}))}catch(e){return[]}})()],j=()=>_(null,null,function*(){var a;c.value===0&&!(yield(a=n.value)==null?void 0:a.validateForm())||c.value<P.length-1&&c.value++}),S=a=>_(null,null,function*(){var r;if(yield(r=s.value)==null?void 0:r.validateForm()){y.value=!0;try{const t=N(N({},i),d),o={hgySupplyDemand:{id:l.value?g.value:void 0,type:"5",infoTitle:t.basicInfo.infoTitle,materialType:Array.isArray(t.materialType)?t.materialType[t.materialType.length-1]:t.materialType,brand:t.basicInfo.brand,model:t.basicInfo.model,depreciationDegree:t.basicInfo.depreciationDegree,province:t.specification.province,city:t.specification.city,district:t.specification.area,storageMethod:t.specification.storageMethod,quantity:Number(t.specification.quantity),unit:t.specification.unit,price:Number(t.specification.price),highlights:t.display.highlights,materialDesc:t.display.materialDesc,validDate:t.validity.validDate,relationUser:t.contactInfo.contactName,relationPhone:t.contactInfo.contactPhone,status:a,attachmentList:$("HGQG")},hgyEntrustOrder:{relationUser:t.contactInfo.contactName,relationPhone:t.contactInfo.contactPhone,entrustType:3,serviceType:5,status:a}};let T;l.value?T=yield se(o.hgySupplyDemand):T=yield ne(o),T&&a===2&&C.push("/supplyAndDemandInfo/supply")}catch(t){}finally{y.value=!1}}});return(a,e)=>{const r=Z("a-button");return v(),h("div",le,[x("div",pe,[f(te,{current:c.value,steps:P,onChange:O},null,8,["current"])]),x("div",ce,[c.value===0?(v(),h("div",me,[f(ae,{ref_key:"step1Ref",ref:n,modelValue:M(i),"onUpdate:modelValue":e[0]||(e[0]=t=>R(i)?i.value=t:i=t),"location-loading":m.value,"is-edit-mode":l.value,onAreaChange:E,onGetCurrentLocation:U},null,8,["modelValue","location-loading","is-edit-mode"]),x("div",de,[f(r,{type:"primary",size:"large",class:"next-btn",onClick:j},{default:b(()=>e[7]||(e[7]=[D(" 下一步 ")])),_:1,__:[7]})])])):F("",!0),c.value===1?(v(),h("div",ue,[f(oe,{ref_key:"step2Ref",ref:s,modelValue:M(d),"onUpdate:modelValue":e[1]||(e[1]=t=>R(d)?d.value=t:d=t)},null,8,["modelValue"]),x("div",fe,[l.value?(v(),h(w,{key:0},[A.value===1?(v(),h(w,{key:0},[f(r,{size:"large",class:"draft-btn",onClick:e[2]||(e[2]=t=>S(1))},{default:b(()=>e[8]||(e[8]=[D(" 保存至草稿 ")])),_:1,__:[8]}),f(r,{type:"primary",size:"large",class:"submit-btn",loading:y.value,onClick:e[3]||(e[3]=t=>S(2))},{default:b(()=>e[9]||(e[9]=[D(" 确认发布 ")])),_:1,__:[9]},8,["loading"])],64)):(v(),ee(r,{key:1,type:"primary",size:"large",class:"submit-btn",loading:y.value,onClick:e[4]||(e[4]=t=>S(2))},{default:b(()=>e[10]||(e[10]=[D(" 保存修改 ")])),_:1,__:[10]},8,["loading"]))],64)):(v(),h(w,{key:1},[f(r,{size:"large",class:"draft-btn",onClick:e[5]||(e[5]=t=>S(1))},{default:b(()=>e[11]||(e[11]=[D(" 保存至草稿 ")])),_:1,__:[11]}),f(r,{type:"primary",size:"large",class:"submit-btn",loading:y.value,onClick:e[6]||(e[6]=t=>S(2))},{default:b(()=>e[12]||(e[12]=[D(" 确认发布 ")])),_:1,__:[12]},8,["loading"])],64))])])):F("",!0)])])}}}),Dt=ie(ye,[["__scopeId","data-v-f44a8810"]]);export{Dt as default};
