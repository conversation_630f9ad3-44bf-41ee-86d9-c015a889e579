var $=Object.defineProperty,q=Object.defineProperties;var x=Object.getOwnPropertyDescriptors;var b=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var y=(r,o,t)=>o in r?$(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,k=(r,o)=>{for(var t in o||(o={}))N.call(o,t)&&y(r,t,o[t]);if(b)for(var t of b(o))D.call(o,t)&&y(r,t,o[t]);return r},w=(r,o)=>q(r,x(o));var v=(r,o,t)=>new Promise((f,l)=>{var c=i=>{try{a(t.next(i))}catch(p){l(p)}},_=i=>{try{a(t.throw(i))}catch(p){l(p)}},a=i=>i.done?f(i.value):Promise.resolve(i.value).then(c,_);a((t=t.apply(r,o)).next())});import{d as E,ag as V,aq as F,ar as J,at as R,k as m,aD as s,G as d,u as B}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as z}from"./index-CCWaWN5g.js";import{u as O}from"./useForm-CgkFTrrO.js";import{B as P}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const j={class:"p-4"},A={class:"p-4 bg-white"},G={title:"富文本 | Markdown",name:"MarkdownDemo"},Lt=E(w(k({},G),{setup(r){const{createMessage:o,createSuccessModal:t}=z(),f=[{field:"name",component:"Input",label:"姓名",required:!0,defaultValue:"zhangsan"},{field:"tinymce",component:"JEditor",label:"富文本",defaultValue:"defaultValue",required:!0},{field:"markdown",component:"JMarkdownEditor",label:"Markdown",defaultValue:"# 张三",required:!0,componentProps:{height:300}}],[l,{setProps:c,validate:_,setFieldsValue:a}]=O({labelWidth:120,schemas:f,actionColOptions:{span:24},compact:!0,showResetButton:!1,showSubmitButton:!1,showAdvancedButton:!1,disabled:!1});function i(n){}function p(n){c({disabled:!!n})}function C(){return v(this,null,function*(){try{const n=yield _();t({title:"校验通过",content:`${JSON.stringify(n)}`})}catch(n){o.warning("检验不通过")}})}function M(){a({name:"LiSi",markdown:"# 李四",tinymce:'<p><strong><span style="font-size: 18pt;">张<span style="color: #e03e2d;">三</span>丰</span></strong></p>'})}return(n,e)=>{const u=V("a-button"),S=V("a-button-group");return J(),F("div",j,[R("div",A,[m(S,{class:"j-table-operator"},{default:s(()=>[m(u,{type:"primary",onClick:e[0]||(e[0]=g=>p(0))},{default:s(()=>e[4]||(e[4]=[d("启用")])),_:1,__:[4]}),m(u,{type:"primary",onClick:e[1]||(e[1]=g=>p(1))},{default:s(()=>e[5]||(e[5]=[d("禁用")])),_:1,__:[5]}),m(u,{type:"primary",onClick:e[2]||(e[2]=g=>C())},{default:s(()=>e[6]||(e[6]=[d("校验表单并获取值")])),_:1,__:[6]}),m(u,{type:"primary",onClick:e[3]||(e[3]=g=>M())},{default:s(()=>e[7]||(e[7]=[d("设置值")])),_:1,__:[7]})]),_:1}),m(B(P),{onRegister:B(l),onSubmit:i},null,8,["onRegister"])])])}}}));export{Lt as default};
