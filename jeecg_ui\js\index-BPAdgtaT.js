var $e=Object.defineProperty,He=Object.defineProperties;var Ue=Object.getOwnPropertyDescriptors;var te=Object.getOwnPropertySymbols;var _e=Object.prototype.hasOwnProperty,je=Object.prototype.propertyIsEnumerable;var ee=(a,t,i)=>t in a?$e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):a[t]=i,yt=(a,t)=>{for(var i in t||(t={}))_e.call(t,i)&&ee(a,i,t[i]);if(te)for(var i of te(t))je.call(t,i)&&ee(a,i,t[i]);return a},ie=(a,t)=>He(a,Ue(t));var St=(a,t,i)=>new Promise((e,n)=>{var r=p=>{try{s(i.next(p))}catch(c){n(c)}},o=p=>{try{s(i.throw(p))}catch(c){n(c)}},s=p=>p.done?e(p.value):Promise.resolve(p.value).then(r,o);s((i=i.apply(a,t)).next())});import{F as Ht,x as Ve,a as Ut,ac as Ge,N as we,n as Fe,ad as qe,u as Ke,c as Qe,w as ye}from"./index-CCWaWN5g.js";import{d as _t,f as it,e as G,o as Ze,b as Je,u as Mt,aq as mt,ar as F,aA as nt,as as W,q as ti,at as _,B as ei,ag as X,aB as Rt,aE as De,aD as z,ah as ft,k as M,h as ii,w as ai,G as ri,au as ni}from"./vue-vendor-dy9k-Yad.js";import{T as oi,a3 as si,U as hi,a8 as li}from"./antd-vue-vendor-me9YkNVC.js";import{B as ci}from"./index-Diw57m_E.js";import{dataURLtoBlob as pi}from"./base64Conver-24EVOS6V.js";/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:05.335Z
 */function ae(a,t){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(a);t&&(e=e.filter(function(n){return Object.getOwnPropertyDescriptor(a,n).enumerable})),i.push.apply(i,e)}return i}function Ce(a){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?ae(Object(i),!0).forEach(function(e){gi(a,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):ae(Object(i)).forEach(function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(i,e))})}return a}function di(a,t){if(typeof a!="object"||!a)return a;var i=a[Symbol.toPrimitive];if(i!==void 0){var e=i.call(a,t||"default");if(typeof e!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(a)}function Me(a){var t=di(a,"string");return typeof t=="symbol"?t:t+""}function It(a){"@babel/helpers - typeof";return It=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},It(a)}function ui(a,t){if(!(a instanceof t))throw new TypeError("Cannot call a class as a function")}function re(a,t){for(var i=0;i<t.length;i++){var e=t[i];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(a,Me(e.key),e)}}function fi(a,t,i){return t&&re(a.prototype,t),i&&re(a,i),Object.defineProperty(a,"prototype",{writable:!1}),a}function gi(a,t,i){return t=Me(t),t in a?Object.defineProperty(a,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[t]=i,a}function xe(a){return mi(a)||vi(a)||bi(a)||wi()}function mi(a){if(Array.isArray(a))return kt(a)}function vi(a){if(typeof Symbol!="undefined"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function bi(a,t){if(a){if(typeof a=="string")return kt(a,t);var i=Object.prototype.toString.call(a).slice(8,-1);if(i==="Object"&&a.constructor&&(i=a.constructor.name),i==="Map"||i==="Set")return Array.from(a);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return kt(a,t)}}function kt(a,t){(t==null||t>a.length)&&(t=a.length);for(var i=0,e=new Array(t);i<t;i++)e[i]=a[i];return e}function wi(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var Et=typeof window!="undefined"&&typeof window.document!="undefined",H=Et?window:{},jt=Et&&H.document.documentElement?"ontouchstart"in H.document.documentElement:!1,Vt=Et?"PointerEvent"in H:!1,x="cropper",Gt="all",Ee="crop",Te="move",Oe="zoom",tt="e",et="w",rt="s",V="n",ct="ne",pt="nw",dt="se",ut="sw",Bt="".concat(x,"-crop"),ne="".concat(x,"-disabled"),k="".concat(x,"-hidden"),oe="".concat(x,"-hide"),yi="".concat(x,"-invisible"),xt="".concat(x,"-modal"),zt="".concat(x,"-move"),vt="".concat(x,"Action"),Dt="".concat(x,"Preview"),Ft="crop",Se="move",Ne="none",Lt="crop",Pt="cropend",Wt="cropmove",Yt="cropstart",se="dblclick",Di=jt?"touchstart":"mousedown",Ci=jt?"touchmove":"mousemove",Mi=jt?"touchend touchcancel":"mouseup",he=Vt?"pointerdown":Di,le=Vt?"pointermove":Ci,ce=Vt?"pointerup pointercancel":Mi,pe="ready",de="resize",ue="wheel",Xt="zoom",fe="image/jpeg",xi=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,Ei=/^data:/,Ti=/^data:image\/jpeg;base64,/,Oi=/^img|canvas$/i,Ae=200,Re=100,ge={viewMode:0,dragMode:Ft,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:Ae,minContainerHeight:Re,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},Si='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',Ni=Number.isNaN||H.isNaN;function y(a){return typeof a=="number"&&!Ni(a)}var me=function(t){return t>0&&t<1/0};function Nt(a){return typeof a=="undefined"}function at(a){return It(a)==="object"&&a!==null}var Ai=Object.prototype.hasOwnProperty;function ot(a){if(!at(a))return!1;try{var t=a.constructor,i=t.prototype;return t&&i&&Ai.call(i,"isPrototypeOf")}catch(e){return!1}}function I(a){return typeof a=="function"}var Ri=Array.prototype.slice;function Ie(a){return Array.from?Array.from(a):Ri.call(a)}function S(a,t){return a&&I(t)&&(Array.isArray(a)||y(a.length)?Ie(a).forEach(function(i,e){t.call(a,i,e,a)}):at(a)&&Object.keys(a).forEach(function(i){t.call(a,a[i],i,a)})),a}var E=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),n=1;n<i;n++)e[n-1]=arguments[n];return at(t)&&e.length>0&&e.forEach(function(r){at(r)&&Object.keys(r).forEach(function(o){t[o]=r[o]})}),t},Ii=/\.\d*(?:0|9){12}\d*$/;function ht(a){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return Ii.test(a)?Math.round(a*t)/t:a}var ki=/^width|height|left|top|marginLeft|marginTop$/;function q(a,t){var i=a.style;S(t,function(e,n){ki.test(n)&&y(e)&&(e="".concat(e,"px")),i[n]=e})}function Bi(a,t){return a.classList?a.classList.contains(t):a.className.indexOf(t)>-1}function A(a,t){if(t){if(y(a.length)){S(a,function(e){A(e,t)});return}if(a.classList){a.classList.add(t);return}var i=a.className.trim();i?i.indexOf(t)<0&&(a.className="".concat(i," ").concat(t)):a.className=t}}function $(a,t){if(t){if(y(a.length)){S(a,function(i){$(i,t)});return}if(a.classList){a.classList.remove(t);return}a.className.indexOf(t)>=0&&(a.className=a.className.replace(t,""))}}function st(a,t,i){if(t){if(y(a.length)){S(a,function(e){st(e,t,i)});return}i?A(a,t):$(a,t)}}var zi=/([a-z\d])([A-Z])/g;function qt(a){return a.replace(zi,"$1-$2").toLowerCase()}function $t(a,t){return at(a[t])?a[t]:a.dataset?a.dataset[t]:a.getAttribute("data-".concat(qt(t)))}function bt(a,t,i){at(i)?a[t]=i:a.dataset?a.dataset[t]=i:a.setAttribute("data-".concat(qt(t)),i)}function Li(a,t){if(at(a[t]))try{delete a[t]}catch(i){a[t]=void 0}else if(a.dataset)try{delete a.dataset[t]}catch(i){a.dataset[t]=void 0}else a.removeAttribute("data-".concat(qt(t)))}var ke=/\s\s*/,Be=function(){var a=!1;if(Et){var t=!1,i=function(){},e=Object.defineProperty({},"once",{get:function(){return a=!0,t},set:function(r){t=r}});H.addEventListener("test",i,e),H.removeEventListener("test",i,e)}return a}();function P(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},n=i;t.trim().split(ke).forEach(function(r){if(!Be){var o=a.listeners;o&&o[r]&&o[r][i]&&(n=o[r][i],delete o[r][i],Object.keys(o[r]).length===0&&delete o[r],Object.keys(o).length===0&&delete a.listeners)}a.removeEventListener(r,n,e)})}function L(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},n=i;t.trim().split(ke).forEach(function(r){if(e.once&&!Be){var o=a.listeners,s=o===void 0?{}:o;n=function(){delete s[r][i],a.removeEventListener(r,n,e);for(var c=arguments.length,l=new Array(c),h=0;h<c;h++)l[h]=arguments[h];i.apply(a,l)},s[r]||(s[r]={}),s[r][i]&&a.removeEventListener(r,s[r][i],e),s[r][i]=n,a.listeners=s}a.addEventListener(r,n,e)})}function lt(a,t,i){var e;return I(Event)&&I(CustomEvent)?e=new CustomEvent(t,{detail:i,bubbles:!0,cancelable:!0}):(e=document.createEvent("CustomEvent"),e.initCustomEvent(t,!0,!0,i)),a.dispatchEvent(e)}function ze(a){var t=a.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}var At=H.location,Pi=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function ve(a){var t=a.match(Pi);return t!==null&&(t[1]!==At.protocol||t[2]!==At.hostname||t[3]!==At.port)}function be(a){var t="timestamp=".concat(new Date().getTime());return a+(a.indexOf("?")===-1?"?":"&")+t}function gt(a){var t=a.rotate,i=a.scaleX,e=a.scaleY,n=a.translateX,r=a.translateY,o=[];y(n)&&n!==0&&o.push("translateX(".concat(n,"px)")),y(r)&&r!==0&&o.push("translateY(".concat(r,"px)")),y(t)&&t!==0&&o.push("rotate(".concat(t,"deg)")),y(i)&&i!==1&&o.push("scaleX(".concat(i,")")),y(e)&&e!==1&&o.push("scaleY(".concat(e,")"));var s=o.length?o.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Wi(a){var t=Ce({},a),i=0;return S(a,function(e,n){delete t[n],S(t,function(r){var o=Math.abs(e.startX-r.startX),s=Math.abs(e.startY-r.startY),p=Math.abs(e.endX-r.endX),c=Math.abs(e.endY-r.endY),l=Math.sqrt(o*o+s*s),h=Math.sqrt(p*p+c*c),d=(h-l)/l;Math.abs(d)>Math.abs(i)&&(i=d)})}),i}function Ct(a,t){var i=a.pageX,e=a.pageY,n={endX:i,endY:e};return t?n:Ce({startX:i,startY:e},n)}function Yi(a){var t=0,i=0,e=0;return S(a,function(n){var r=n.startX,o=n.startY;t+=r,i+=o,e+=1}),t/=e,i/=e,{pageX:t,pageY:i}}function K(a){var t=a.aspectRatio,i=a.height,e=a.width,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=me(e),o=me(i);if(r&&o){var s=i*t;n==="contain"&&s>e||n==="cover"&&s<e?i=e/t:e=i*t}else r?i=e/t:o&&(e=i*t);return{width:e,height:i}}function Xi(a){var t=a.width,i=a.height,e=a.degree;if(e=Math.abs(e)%180,e===90)return{width:i,height:t};var n=e%90*Math.PI/180,r=Math.sin(n),o=Math.cos(n),s=t*o+i*r,p=t*r+i*o;return e>90?{width:p,height:s}:{width:s,height:p}}function $i(a,t,i,e){var n=t.aspectRatio,r=t.naturalWidth,o=t.naturalHeight,s=t.rotate,p=s===void 0?0:s,c=t.scaleX,l=c===void 0?1:c,h=t.scaleY,d=h===void 0?1:h,f=i.aspectRatio,v=i.naturalWidth,b=i.naturalHeight,g=e.fillColor,D=g===void 0?"transparent":g,m=e.imageSmoothingEnabled,w=m===void 0?!0:m,T=e.imageSmoothingQuality,O=T===void 0?"low":T,u=e.maxWidth,C=u===void 0?1/0:u,N=e.maxHeight,B=N===void 0?1/0:N,U=e.minWidth,Q=U===void 0?0:U,Z=e.minHeight,j=Z===void 0?0:Z,Y=document.createElement("canvas"),R=Y.getContext("2d"),J=K({aspectRatio:f,width:C,height:B}),wt=K({aspectRatio:f,width:Q,height:j},"cover"),Tt=Math.min(J.width,Math.max(wt.width,v)),Ot=Math.min(J.height,Math.max(wt.height,b)),Kt=K({aspectRatio:n,width:C,height:B}),Qt=K({aspectRatio:n,width:Q,height:j},"cover"),Zt=Math.min(Kt.width,Math.max(Qt.width,r)),Jt=Math.min(Kt.height,Math.max(Qt.height,o)),Ye=[-Zt/2,-Jt/2,Zt,Jt];return Y.width=ht(Tt),Y.height=ht(Ot),R.fillStyle=D,R.fillRect(0,0,Tt,Ot),R.save(),R.translate(Tt/2,Ot/2),R.rotate(p*Math.PI/180),R.scale(l,d),R.imageSmoothingEnabled=w,R.imageSmoothingQuality=O,R.drawImage.apply(R,[a].concat(xe(Ye.map(function(Xe){return Math.floor(ht(Xe))})))),R.restore(),Y}var Le=String.fromCharCode;function Hi(a,t,i){var e="";i+=t;for(var n=t;n<i;n+=1)e+=Le(a.getUint8(n));return e}var Ui=/^data:.*,/;function _i(a){var t=a.replace(Ui,""),i=atob(t),e=new ArrayBuffer(i.length),n=new Uint8Array(e);return S(n,function(r,o){n[o]=i.charCodeAt(o)}),e}function ji(a,t){for(var i=[],e=8192,n=new Uint8Array(a);n.length>0;)i.push(Le.apply(null,Ie(n.subarray(0,e)))),n=n.subarray(e);return"data:".concat(t,";base64,").concat(btoa(i.join("")))}function Vi(a){var t=new DataView(a),i;try{var e,n,r;if(t.getUint8(0)===255&&t.getUint8(1)===216)for(var o=t.byteLength,s=2;s+1<o;){if(t.getUint8(s)===255&&t.getUint8(s+1)===225){n=s;break}s+=1}if(n){var p=n+4,c=n+10;if(Hi(t,p,4)==="Exif"){var l=t.getUint16(c);if(e=l===18761,(e||l===19789)&&t.getUint16(c+2,e)===42){var h=t.getUint32(c+4,e);h>=8&&(r=c+h)}}}if(r){var d=t.getUint16(r,e),f,v;for(v=0;v<d;v+=1)if(f=r+v*12+2,t.getUint16(f,e)===274){f+=8,i=t.getUint16(f,e),t.setUint16(f,1,e);break}}}catch(b){i=1}return i}function Gi(a){var t=0,i=1,e=1;switch(a){case 2:i=-1;break;case 3:t=-180;break;case 4:e=-1;break;case 5:t=90,e=-1;break;case 6:t=90;break;case 7:t=90,i=-1;break;case 8:t=-90;break}return{rotate:t,scaleX:i,scaleY:e}}var Fi={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,n=this.cropper,r=Number(i.minContainerWidth),o=Number(i.minContainerHeight);A(n,k),$(t,k);var s={width:Math.max(e.offsetWidth,r>=0?r:Ae),height:Math.max(e.offsetHeight,o>=0?o:Re)};this.containerData=s,q(n,{width:s.width,height:s.height}),A(t,k),$(n,k)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,n=Math.abs(i.rotate)%180===90,r=n?i.naturalHeight:i.naturalWidth,o=n?i.naturalWidth:i.naturalHeight,s=r/o,p=t.width,c=t.height;t.height*s>t.width?e===3?p=t.height*s:c=t.width/s:e===3?c=t.width/s:p=t.height*s;var l={aspectRatio:s,naturalWidth:r,naturalHeight:o,width:p,height:c};this.canvasData=l,this.limited=e===1||e===2,this.limitCanvas(!0,!0),l.width=Math.min(Math.max(l.width,l.minWidth),l.maxWidth),l.height=Math.min(Math.max(l.height,l.minHeight),l.maxHeight),l.left=(t.width-l.width)/2,l.top=(t.height-l.height)/2,l.oldLeft=l.left,l.oldTop=l.top,this.initialCanvasData=E({},l)},limitCanvas:function(t,i){var e=this.options,n=this.containerData,r=this.canvasData,o=this.cropBoxData,s=e.viewMode,p=r.aspectRatio,c=this.cropped&&o;if(t){var l=Number(e.minCanvasWidth)||0,h=Number(e.minCanvasHeight)||0;s>1?(l=Math.max(l,n.width),h=Math.max(h,n.height),s===3&&(h*p>l?l=h*p:h=l/p)):s>0&&(l?l=Math.max(l,c?o.width:0):h?h=Math.max(h,c?o.height:0):c&&(l=o.width,h=o.height,h*p>l?l=h*p:h=l/p));var d=K({aspectRatio:p,width:l,height:h});l=d.width,h=d.height,r.minWidth=l,r.minHeight=h,r.maxWidth=1/0,r.maxHeight=1/0}if(i)if(s>(c?0:1)){var f=n.width-r.width,v=n.height-r.height;r.minLeft=Math.min(0,f),r.minTop=Math.min(0,v),r.maxLeft=Math.max(0,f),r.maxTop=Math.max(0,v),c&&this.limited&&(r.minLeft=Math.min(o.left,o.left+(o.width-r.width)),r.minTop=Math.min(o.top,o.top+(o.height-r.height)),r.maxLeft=o.left,r.maxTop=o.top,s===2&&(r.width>=n.width&&(r.minLeft=Math.min(0,f),r.maxLeft=Math.max(0,f)),r.height>=n.height&&(r.minTop=Math.min(0,v),r.maxTop=Math.max(0,v))))}else r.minLeft=-r.width,r.minTop=-r.height,r.maxLeft=n.width,r.maxTop=n.height},renderCanvas:function(t,i){var e=this.canvasData,n=this.imageData;if(i){var r=Xi({width:n.naturalWidth*Math.abs(n.scaleX||1),height:n.naturalHeight*Math.abs(n.scaleY||1),degree:n.rotate||0}),o=r.width,s=r.height,p=e.width*(o/e.naturalWidth),c=e.height*(s/e.naturalHeight);e.left-=(p-e.width)/2,e.top-=(c-e.height)/2,e.width=p,e.height=c,e.aspectRatio=o/s,e.naturalWidth=o,e.naturalHeight=s,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,q(this.canvas,E({width:e.width,height:e.height},gt({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,n=e.naturalWidth*(i.width/i.naturalWidth),r=e.naturalHeight*(i.height/i.naturalHeight);E(e,{width:n,height:r,left:(i.width-n)/2,top:(i.height-r)/2}),q(this.image,E({width:e.width,height:e.height},gt(E({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,n=Number(t.autoCropArea)||.8,r={width:i.width,height:i.height};e&&(i.height*e>i.width?r.height=r.width/e:r.width=r.height*e),this.cropBoxData=r,this.limitCropBox(!0,!0),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),r.width=Math.max(r.minWidth,r.width*n),r.height=Math.max(r.minHeight,r.height*n),r.left=i.left+(i.width-r.width)/2,r.top=i.top+(i.height-r.height)/2,r.oldLeft=r.left,r.oldTop=r.top,this.initialCropBoxData=E({},r)},limitCropBox:function(t,i){var e=this.options,n=this.containerData,r=this.canvasData,o=this.cropBoxData,s=this.limited,p=e.aspectRatio;if(t){var c=Number(e.minCropBoxWidth)||0,l=Number(e.minCropBoxHeight)||0,h=s?Math.min(n.width,r.width,r.width+r.left,n.width-r.left):n.width,d=s?Math.min(n.height,r.height,r.height+r.top,n.height-r.top):n.height;c=Math.min(c,n.width),l=Math.min(l,n.height),p&&(c&&l?l*p>c?l=c/p:c=l*p:c?l=c/p:l&&(c=l*p),d*p>h?d=h/p:h=d*p),o.minWidth=Math.min(c,h),o.minHeight=Math.min(l,d),o.maxWidth=h,o.maxHeight=d}i&&(s?(o.minLeft=Math.max(0,r.left),o.minTop=Math.max(0,r.top),o.maxLeft=Math.min(n.width,r.left+r.width)-o.width,o.maxTop=Math.min(n.height,r.top+r.height)-o.height):(o.minLeft=0,o.minTop=0,o.maxLeft=n.width-o.width,o.maxTop=n.height-o.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&bt(this.face,vt,e.width>=i.width&&e.height>=i.height?Te:Gt),q(this.cropBox,E({width:e.width,height:e.height},gt({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),lt(this.element,Lt,this.getData())}},qi={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,n=i?this.crossOriginUrl:this.url,r=t.alt||"The image to preview",o=document.createElement("img");if(i&&(o.crossOrigin=i),o.src=n,o.alt=r,this.viewBox.appendChild(o),this.viewBoxImage=o,!!e){var s=e;typeof e=="string"?s=t.ownerDocument.querySelectorAll(e):e.querySelector&&(s=[e]),this.previews=s,S(s,function(p){var c=document.createElement("img");bt(p,Dt,{width:p.offsetWidth,height:p.offsetHeight,html:p.innerHTML}),i&&(c.crossOrigin=i),c.src=n,c.alt=r,c.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',p.innerHTML="",p.appendChild(c)})}},resetPreview:function(){S(this.previews,function(t){var i=$t(t,Dt);q(t,{width:i.width,height:i.height}),t.innerHTML=i.html,Li(t,Dt)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,n=e.width,r=e.height,o=t.width,s=t.height,p=e.left-i.left-t.left,c=e.top-i.top-t.top;!this.cropped||this.disabled||(q(this.viewBoxImage,E({width:o,height:s},gt(E({translateX:-p,translateY:-c},t)))),S(this.previews,function(l){var h=$t(l,Dt),d=h.width,f=h.height,v=d,b=f,g=1;n&&(g=d/n,b=r*g),r&&b>f&&(g=f/r,v=n*g,b=f),q(l,{width:v,height:b}),q(l.getElementsByTagName("img")[0],E({width:o*g,height:s*g},gt(E({translateX:-p*g,translateY:-c*g},t))))}))}},Ki={bind:function(){var t=this.element,i=this.options,e=this.cropper;I(i.cropstart)&&L(t,Yt,i.cropstart),I(i.cropmove)&&L(t,Wt,i.cropmove),I(i.cropend)&&L(t,Pt,i.cropend),I(i.crop)&&L(t,Lt,i.crop),I(i.zoom)&&L(t,Xt,i.zoom),L(e,he,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&L(e,ue,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&L(e,se,this.onDblclick=this.dblclick.bind(this)),L(t.ownerDocument,le,this.onCropMove=this.cropMove.bind(this)),L(t.ownerDocument,ce,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&L(window,de,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;I(i.cropstart)&&P(t,Yt,i.cropstart),I(i.cropmove)&&P(t,Wt,i.cropmove),I(i.cropend)&&P(t,Pt,i.cropend),I(i.crop)&&P(t,Lt,i.crop),I(i.zoom)&&P(t,Xt,i.zoom),P(e,he,this.onCropStart),i.zoomable&&i.zoomOnWheel&&P(e,ue,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&P(e,se,this.onDblclick),P(t.ownerDocument,le,this.onCropMove),P(t.ownerDocument,ce,this.onCropEnd),i.responsive&&P(window,de,this.onResize)}},Qi={resize:function(){if(!this.disabled){var t=this.options,i=this.container,e=this.containerData,n=i.offsetWidth/e.width,r=i.offsetHeight/e.height,o=Math.abs(n-1)>Math.abs(r-1)?n:r;if(o!==1){var s,p;t.restore&&(s=this.getCanvasData(),p=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(S(s,function(c,l){s[l]=c*o})),this.setCropBoxData(S(p,function(c,l){p[l]=c*o})))}}},dblclick:function(){this.disabled||this.options.dragMode===Ne||this.setDragMode(Bi(this.dragBox,Bt)?Se:Ft)},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,n=1;this.disabled||(t.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?n=t.deltaY>0?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=t.detail>0?1:-1),this.zoom(-n*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(y(i)&&i!==1||y(e)&&e!==0||t.ctrlKey))){var n=this.options,r=this.pointers,o;t.changedTouches?S(t.changedTouches,function(s){r[s.identifier]=Ct(s)}):r[t.pointerId||0]=Ct(t),Object.keys(r).length>1&&n.zoomable&&n.zoomOnTouch?o=Oe:o=$t(t.target,vt),xi.test(o)&&lt(this.element,Yt,{originalEvent:t,action:o})!==!1&&(t.preventDefault(),this.action=o,this.cropping=!1,o===Ee&&(this.cropping=!0,A(this.dragBox,xt)))}},cropMove:function(t){var i=this.action;if(!(this.disabled||!i)){var e=this.pointers;t.preventDefault(),lt(this.element,Wt,{originalEvent:t,action:i})!==!1&&(t.changedTouches?S(t.changedTouches,function(n){E(e[n.identifier]||{},Ct(n,!0))}):E(e[t.pointerId||0]||{},Ct(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?S(t.changedTouches,function(n){delete e[n.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,st(this.dragBox,xt,this.cropped&&this.options.modal)),lt(this.element,Pt,{originalEvent:t,action:i}))}}},Zi={change:function(t){var i=this.options,e=this.canvasData,n=this.containerData,r=this.cropBoxData,o=this.pointers,s=this.action,p=i.aspectRatio,c=r.left,l=r.top,h=r.width,d=r.height,f=c+h,v=l+d,b=0,g=0,D=n.width,m=n.height,w=!0,T;!p&&t.shiftKey&&(p=h&&d?h/d:1),this.limited&&(b=r.minLeft,g=r.minTop,D=b+Math.min(n.width,e.width,e.left+e.width),m=g+Math.min(n.height,e.height,e.top+e.height));var O=o[Object.keys(o)[0]],u={x:O.endX-O.startX,y:O.endY-O.startY},C=function(B){switch(B){case tt:f+u.x>D&&(u.x=D-f);break;case et:c+u.x<b&&(u.x=b-c);break;case V:l+u.y<g&&(u.y=g-l);break;case rt:v+u.y>m&&(u.y=m-v);break}};switch(s){case Gt:c+=u.x,l+=u.y;break;case tt:if(u.x>=0&&(f>=D||p&&(l<=g||v>=m))){w=!1;break}C(tt),h+=u.x,h<0&&(s=et,h=-h,c-=h),p&&(d=h/p,l+=(r.height-d)/2);break;case V:if(u.y<=0&&(l<=g||p&&(c<=b||f>=D))){w=!1;break}C(V),d-=u.y,l+=u.y,d<0&&(s=rt,d=-d,l-=d),p&&(h=d*p,c+=(r.width-h)/2);break;case et:if(u.x<=0&&(c<=b||p&&(l<=g||v>=m))){w=!1;break}C(et),h-=u.x,c+=u.x,h<0&&(s=tt,h=-h,c-=h),p&&(d=h/p,l+=(r.height-d)/2);break;case rt:if(u.y>=0&&(v>=m||p&&(c<=b||f>=D))){w=!1;break}C(rt),d+=u.y,d<0&&(s=V,d=-d,l-=d),p&&(h=d*p,c+=(r.width-h)/2);break;case ct:if(p){if(u.y<=0&&(l<=g||f>=D)){w=!1;break}C(V),d-=u.y,l+=u.y,h=d*p}else C(V),C(tt),u.x>=0?f<D?h+=u.x:u.y<=0&&l<=g&&(w=!1):h+=u.x,u.y<=0?l>g&&(d-=u.y,l+=u.y):(d-=u.y,l+=u.y);h<0&&d<0?(s=ut,d=-d,h=-h,l-=d,c-=h):h<0?(s=pt,h=-h,c-=h):d<0&&(s=dt,d=-d,l-=d);break;case pt:if(p){if(u.y<=0&&(l<=g||c<=b)){w=!1;break}C(V),d-=u.y,l+=u.y,h=d*p,c+=r.width-h}else C(V),C(et),u.x<=0?c>b?(h-=u.x,c+=u.x):u.y<=0&&l<=g&&(w=!1):(h-=u.x,c+=u.x),u.y<=0?l>g&&(d-=u.y,l+=u.y):(d-=u.y,l+=u.y);h<0&&d<0?(s=dt,d=-d,h=-h,l-=d,c-=h):h<0?(s=ct,h=-h,c-=h):d<0&&(s=ut,d=-d,l-=d);break;case ut:if(p){if(u.x<=0&&(c<=b||v>=m)){w=!1;break}C(et),h-=u.x,c+=u.x,d=h/p}else C(rt),C(et),u.x<=0?c>b?(h-=u.x,c+=u.x):u.y>=0&&v>=m&&(w=!1):(h-=u.x,c+=u.x),u.y>=0?v<m&&(d+=u.y):d+=u.y;h<0&&d<0?(s=ct,d=-d,h=-h,l-=d,c-=h):h<0?(s=dt,h=-h,c-=h):d<0&&(s=pt,d=-d,l-=d);break;case dt:if(p){if(u.x>=0&&(f>=D||v>=m)){w=!1;break}C(tt),h+=u.x,d=h/p}else C(rt),C(tt),u.x>=0?f<D?h+=u.x:u.y>=0&&v>=m&&(w=!1):h+=u.x,u.y>=0?v<m&&(d+=u.y):d+=u.y;h<0&&d<0?(s=pt,d=-d,h=-h,l-=d,c-=h):h<0?(s=ut,h=-h,c-=h):d<0&&(s=ct,d=-d,l-=d);break;case Te:this.move(u.x,u.y),w=!1;break;case Oe:this.zoom(Wi(o),t),w=!1;break;case Ee:if(!u.x||!u.y){w=!1;break}T=ze(this.cropper),c=O.startX-T.left,l=O.startY-T.top,h=r.minWidth,d=r.minHeight,u.x>0?s=u.y>0?dt:ct:u.x<0&&(c-=h,s=u.y>0?ut:pt),u.y<0&&(l-=d),this.cropped||($(this.cropBox,k),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}w&&(r.width=h,r.height=d,r.left=c,r.top=l,this.action=s,this.renderCropBox()),S(o,function(N){N.startX=N.endX,N.startY=N.endY})}},Ji={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&A(this.dragBox,xt),$(this.cropBox,k),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=E({},this.initialImageData),this.canvasData=E({},this.initialCanvasData),this.cropBoxData=E({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(E(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),$(this.dragBox,xt),A(this.cropBox,k)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,S(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,$(this.cropper,ne)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,A(this.cropper,ne)),this},destroy:function(){var t=this.element;return t[x]?(t[x]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=e.left,r=e.top;return this.moveTo(Nt(t)?t:n+Number(t),Nt(i)?i:r+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(y(t)&&(e.left=t,n=!0),y(i)&&(e.top=i,n=!0),n&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var n=this.options,r=this.canvasData,o=r.width,s=r.height,p=r.naturalWidth,c=r.naturalHeight;if(t=Number(t),t>=0&&this.ready&&!this.disabled&&n.zoomable){var l=p*t,h=c*t;if(lt(this.element,Xt,{ratio:t,oldRatio:o/p,originalEvent:e})===!1)return this;if(e){var d=this.pointers,f=ze(this.cropper),v=d&&Object.keys(d).length?Yi(d):{pageX:e.pageX,pageY:e.pageY};r.left-=(l-o)*((v.pageX-f.left-r.left)/o),r.top-=(h-s)*((v.pageY-f.top-r.top)/s)}else ot(i)&&y(i.x)&&y(i.y)?(r.left-=(l-o)*((i.x-r.left)/o),r.top-=(h-s)*((i.y-r.top)/s)):(r.left-=(l-o)/2,r.top-=(h-s)/2);r.width=l,r.height=h,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return t=Number(t),y(t)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,y(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(y(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(y(t)&&(e.scaleX=t,n=!0),y(i)&&(e.scaleY=i,n=!0),n&&this.renderCanvas(!0,!0)),this},getData:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.options,e=this.imageData,n=this.canvasData,r=this.cropBoxData,o;if(this.ready&&this.cropped){o={x:r.left-n.left,y:r.top-n.top,width:r.width,height:r.height};var s=e.width/e.naturalWidth;if(S(o,function(l,h){o[h]=l/s}),t){var p=Math.round(o.y+o.height),c=Math.round(o.x+o.width);o.x=Math.round(o.x),o.y=Math.round(o.y),o.width=c-o.x,o.height=p-o.y}}else o={x:0,y:0,width:0,height:0};return i.rotatable&&(o.rotate=e.rotate||0),i.scalable&&(o.scaleX=e.scaleX||1,o.scaleY=e.scaleY||1),o},setData:function(t){var i=this.options,e=this.imageData,n=this.canvasData,r={};if(this.ready&&!this.disabled&&ot(t)){var o=!1;i.rotatable&&y(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,o=!0),i.scalable&&(y(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,o=!0),y(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,o=!0)),o&&this.renderCanvas(!0,!0);var s=e.width/e.naturalWidth;y(t.x)&&(r.left=t.x*s+n.left),y(t.y)&&(r.top=t.y*s+n.top),y(t.width)&&(r.width=t.width*s),y(t.height)&&(r.height=t.height*s),this.setCropBoxData(r)}return this},getContainerData:function(){return this.ready?E({},this.containerData):{}},getImageData:function(){return this.sized?E({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&S(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&ot(t)&&(y(t.left)&&(i.left=t.left),y(t.top)&&(i.top=t.top),y(t.width)?(i.width=t.width,i.height=t.width/e):y(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t=this.cropBoxData,i;return this.ready&&this.cropped&&(i={left:t.left,top:t.top,width:t.width,height:t.height}),i||{}},setCropBoxData:function(t){var i=this.cropBoxData,e=this.options.aspectRatio,n,r;return this.ready&&this.cropped&&!this.disabled&&ot(t)&&(y(t.left)&&(i.left=t.left),y(t.top)&&(i.top=t.top),y(t.width)&&t.width!==i.width&&(n=!0,i.width=t.width),y(t.height)&&t.height!==i.height&&(r=!0,i.height=t.height),e&&(n?i.height=i.width/e:r&&(i.width=i.height*e)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=$i(this.image,this.imageData,i,t);if(!this.cropped)return e;var n=this.getData(t.rounded),r=n.x,o=n.y,s=n.width,p=n.height,c=e.width/Math.floor(i.naturalWidth);c!==1&&(r*=c,o*=c,s*=c,p*=c);var l=s/p,h=K({aspectRatio:l,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),d=K({aspectRatio:l,width:t.minWidth||0,height:t.minHeight||0},"cover"),f=K({aspectRatio:l,width:t.width||(c!==1?e.width:s),height:t.height||(c!==1?e.height:p)}),v=f.width,b=f.height;v=Math.min(h.width,Math.max(d.width,v)),b=Math.min(h.height,Math.max(d.height,b));var g=document.createElement("canvas"),D=g.getContext("2d");g.width=ht(v),g.height=ht(b),D.fillStyle=t.fillColor||"transparent",D.fillRect(0,0,v,b);var m=t.imageSmoothingEnabled,w=m===void 0?!0:m,T=t.imageSmoothingQuality;D.imageSmoothingEnabled=w,T&&(D.imageSmoothingQuality=T);var O=e.width,u=e.height,C=r,N=o,B,U,Q,Z,j,Y;C<=-s||C>O?(C=0,B=0,Q=0,j=0):C<=0?(Q=-C,C=0,B=Math.min(O,s+C),j=B):C<=O&&(Q=0,B=Math.min(s,O-C),j=B),B<=0||N<=-p||N>u?(N=0,U=0,Z=0,Y=0):N<=0?(Z=-N,N=0,U=Math.min(u,p+N),Y=U):N<=u&&(Z=0,U=Math.min(p,u-N),Y=U);var R=[C,N,B,U];if(j>0&&Y>0){var J=v/s;R.push(Q*J,Z*J,j*J,Y*J)}return D.drawImage.apply(D,[e].concat(xe(R.map(function(wt){return Math.floor(ht(wt))})))),g},setAspectRatio:function(t){var i=this.options;return!this.disabled&&!Nt(t)&&(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,n=this.face;if(this.ready&&!this.disabled){var r=t===Ft,o=i.movable&&t===Se;t=r||o?t:Ne,i.dragMode=t,bt(e,vt,t),st(e,Bt,r),st(e,zt,o),i.cropBoxMovable||(bt(n,vt,t),st(n,Bt,r),st(n,zt,o))}return this}},ta=H.Cropper,Pe=function(){function a(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(ui(this,a),!t||!Oi.test(t.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=E({},ge,ot(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return fi(a,[{key:"init",value:function(){var i=this.element,e=i.tagName.toLowerCase(),n;if(!i[x]){if(i[x]=this,e==="img"){if(this.isImg=!0,n=i.getAttribute("src")||"",this.originalUrl=n,!n)return;n=i.src}else e==="canvas"&&window.HTMLCanvasElement&&(n=i.toDataURL());this.load(n)}}},{key:"load",value:function(i){var e=this;if(i){this.url=i,this.imageData={};var n=this.element,r=this.options;if(!r.rotatable&&!r.scalable&&(r.checkOrientation=!1),!r.checkOrientation||!window.ArrayBuffer){this.clone();return}if(Ei.test(i)){Ti.test(i)?this.read(_i(i)):this.clone();return}var o=new XMLHttpRequest,s=this.clone.bind(this);this.reloading=!0,this.xhr=o,o.onabort=s,o.onerror=s,o.ontimeout=s,o.onprogress=function(){o.getResponseHeader("content-type")!==fe&&o.abort()},o.onload=function(){e.read(o.response)},o.onloadend=function(){e.reloading=!1,e.xhr=null},r.checkCrossOrigin&&ve(i)&&n.crossOrigin&&(i=be(i)),o.open("GET",i,!0),o.responseType="arraybuffer",o.withCredentials=n.crossOrigin==="use-credentials",o.send()}}},{key:"read",value:function(i){var e=this.options,n=this.imageData,r=Vi(i),o=0,s=1,p=1;if(r>1){this.url=ji(i,fe);var c=Gi(r);o=c.rotate,s=c.scaleX,p=c.scaleY}e.rotatable&&(n.rotate=o),e.scalable&&(n.scaleX=s,n.scaleY=p),this.clone()}},{key:"clone",value:function(){var i=this.element,e=this.url,n=i.crossOrigin,r=e;this.options.checkCrossOrigin&&ve(e)&&(n||(n="anonymous"),r=be(e)),this.crossOrigin=n,this.crossOriginUrl=r;var o=document.createElement("img");n&&(o.crossOrigin=n),o.src=r||e,o.alt=i.alt||"The image to crop",this.image=o,o.onload=this.start.bind(this),o.onerror=this.stop.bind(this),A(o,oe),i.parentNode.insertBefore(o,i.nextSibling)}},{key:"start",value:function(){var i=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var n=H.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(H.navigator.userAgent),r=function(c,l){E(i.imageData,{naturalWidth:c,naturalHeight:l,aspectRatio:c/l}),i.initialImageData=E({},i.imageData),i.sizing=!1,i.sized=!0,i.build()};if(e.naturalWidth&&!n){r(e.naturalWidth,e.naturalHeight);return}var o=document.createElement("img"),s=document.body||document.documentElement;this.sizingImage=o,o.onload=function(){r(o.width,o.height),n||s.removeChild(o)},o.src=e.src,n||(o.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",s.appendChild(o))}},{key:"stop",value:function(){var i=this.image;i.onload=null,i.onerror=null,i.parentNode.removeChild(i),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var i=this.element,e=this.options,n=this.image,r=i.parentNode,o=document.createElement("div");o.innerHTML=Si;var s=o.querySelector(".".concat(x,"-container")),p=s.querySelector(".".concat(x,"-canvas")),c=s.querySelector(".".concat(x,"-drag-box")),l=s.querySelector(".".concat(x,"-crop-box")),h=l.querySelector(".".concat(x,"-face"));this.container=r,this.cropper=s,this.canvas=p,this.dragBox=c,this.cropBox=l,this.viewBox=s.querySelector(".".concat(x,"-view-box")),this.face=h,p.appendChild(n),A(i,k),r.insertBefore(s,i.nextSibling),$(n,oe),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,A(l,k),e.guides||A(l.getElementsByClassName("".concat(x,"-dashed")),k),e.center||A(l.getElementsByClassName("".concat(x,"-center")),k),e.background&&A(s,"".concat(x,"-bg")),e.highlight||A(h,yi),e.cropBoxMovable&&(A(h,zt),bt(h,vt,Gt)),e.cropBoxResizable||(A(l.getElementsByClassName("".concat(x,"-line")),k),A(l.getElementsByClassName("".concat(x,"-point")),k)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),I(e.ready)&&L(i,pe,e.ready,{once:!0}),lt(i,pe)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var i=this.cropper.parentNode;i&&i.removeChild(this.cropper),$(this.element,k)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=ta,a}},{key:"setDefaults",value:function(i){E(ge,ot(i)&&i)}}])}();E(Pe.prototype,Fi,qi,Ki,Qi,Zi,Ji);const ea={aspectRatio:1,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,autoCrop:!0,background:!0,highlight:!0,center:!0,responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,scalable:!0,modal:!0,guides:!0,movable:!0,rotatable:!0},ia={src:{type:String,required:!0},alt:{type:String},circled:{type:Boolean,default:!1},realTimePreview:{type:Boolean,default:!0},height:{type:[String,Number],default:"360px"},crossorigin:{type:String,default:void 0},imageStyle:{type:Object,default:()=>({})},options:{type:Object,default:()=>({})}},aa=_t({name:"CropperImage",props:ia,emits:["cropend","ready","cropendError"],setup(a,{attrs:t,emit:i}){const e=it(),n=it(),r=it(!1),{prefixCls:o}=Ht("cropper-image"),s=Ve(d,80),p=G(()=>yt({height:a.height,maxWidth:"100%"},a.imageStyle)),c=G(()=>[o,t.class,{[`${o}--circled`]:a.circled}]),l=G(()=>({height:`${a.height}`.replace(/px/,"")+"px"}));Ze(h),Je(()=>{var b;(b=n.value)==null||b.destroy()});function h(){return St(this,null,function*(){const b=Mt(e);b&&(n.value=new Pe(b,yt(ie(yt({},ea),{ready:()=>{r.value=!0,d(),i("ready",n.value)},crop(){s()},zoom(){s()},cropmove(){s()}}),a.options)))})}function d(){a.realTimePreview&&f()}function f(){if(!n.value)return;let b=n.value.getData();(a.circled?v():n.value.getCroppedCanvas()).toBlob(D=>{if(!D)return;let m=new FileReader;m.readAsDataURL(D),m.onloadend=w=>{var T,O;i("cropend",{imgBase64:(O=(T=w.target)==null?void 0:T.result)!=null?O:"",imgInfo:b})},m.onerror=()=>{i("cropendError")}},"image/png")}function v(){const b=n.value.getCroppedCanvas(),g=document.createElement("canvas"),D=g.getContext("2d"),m=b.width,w=b.height;return g.width=m,g.height=w,D.imageSmoothingEnabled=!0,D.drawImage(b,0,0,m,w),D.globalCompositeOperation="destination-in",D.beginPath(),D.arc(m/2,w/2,Math.min(m,w)/2,0,2*Math.PI,!0),D.fill(),g}return{getClass:c,imgElRef:e,getWrapperStyle:l,getImageStyle:p,isReady:r,croppered:f}}}),ra=["src","alt","crossorigin"];function na(a,t,i,e,n,r){return F(),mt("div",{class:W(a.getClass),style:nt(a.getWrapperStyle)},[ti(_("img",{ref:"imgElRef",src:a.src,alt:a.alt,crossorigin:a.crossorigin,style:nt(a.getImageStyle)},null,12,ra),[[ei,a.isReady]])],6)}const We=Ut(aa,[["render",na]]),oa={circled:{type:Boolean,default:!0},uploadApi:{type:Function}},sa=_t({name:"CropperModal",components:{BasicModal:ci,Space:li,CropperImage:We,Upload:hi,Avatar:si,Tooltip:oi},props:oa,emits:["uploadSuccess","register"],setup(a,{emit:t}){let i="";const e=it(""),n=it(""),r=it();let o=1,s=1;const{prefixCls:p}=Ht("cropper-am"),[c,{closeModal:l,setModalProps:h}]=Ge(),{t:d}=we();function f(m){const w=new FileReader;return w.readAsDataURL(m),e.value="",n.value="",w.onload=function(T){var O,u;e.value=(u=(O=T.target)==null?void 0:O.result)!=null?u:"",i=m.name},!1}function v({imgBase64:m}){n.value=m}function b(m){r.value=m}function g(m,w){var T,O;m==="scaleX"&&(o=w=o===-1?1:-1),m==="scaleY"&&(s=w=s===-1?1:-1),(O=(T=r==null?void 0:r.value)==null?void 0:T[m])==null||O.call(T,w)}function D(){return St(this,null,function*(){const m=a.uploadApi;if(m&&Fe(m)){const w=pi(n.value);try{h({confirmLoading:!0});const T=yield m({name:"file",file:w,filename:i});t("uploadSuccess",{source:n.value,data:T.data||T.message}),l()}finally{h({confirmLoading:!1})}}})}return{t:d,prefixCls:p,src:e,register:c,previewSource:n,handleBeforeUpload:f,handleCropend:v,handleReady:b,handlerToolbar:g,handleOk:D}}}),ha=["src","alt"];function la(a,t,i,e,n,r){const o=X("CropperImage"),s=X("a-button"),p=X("Tooltip"),c=X("Upload"),l=X("Space"),h=X("Avatar"),d=X("BasicModal");return F(),Rt(d,De(a.$attrs,{onRegister:a.register,title:a.t("component.cropper.modalTitle"),width:"800px",canFullscreen:!1,onOk:a.handleOk,okText:a.t("component.cropper.okText")}),{default:z(()=>[_("div",{class:W(a.prefixCls)},[_("div",{class:W(`${a.prefixCls}-left`)},[_("div",{class:W(`${a.prefixCls}-cropper`)},[a.src?(F(),Rt(o,{key:0,src:a.src,height:"300px",circled:a.circled,onCropend:a.handleCropend,onReady:a.handleReady},null,8,["src","circled","onCropend","onReady"])):ft("",!0)],2),_("div",{class:W(`${a.prefixCls}-toolbar`)},[M(c,{fileList:[],accept:"image/*",beforeUpload:a.handleBeforeUpload},{default:z(()=>[M(p,{title:a.t("component.cropper.selectImage"),placement:"bottom"},{default:z(()=>[M(s,{size:"small",preIcon:"ant-design:upload-outlined",type:"primary"})]),_:1},8,["title"])]),_:1},8,["beforeUpload"]),M(l,null,{default:z(()=>[M(p,{title:a.t("component.cropper.btn_reset"),placement:"bottom"},{default:z(()=>[M(s,{type:"primary",preIcon:"ant-design:reload-outlined",size:"small",disabled:!a.src,onClick:t[0]||(t[0]=f=>a.handlerToolbar("reset"))},null,8,["disabled"])]),_:1},8,["title"]),M(p,{title:a.t("component.cropper.btn_rotate_left"),placement:"bottom"},{default:z(()=>[M(s,{type:"primary",preIcon:"ant-design:rotate-left-outlined",size:"small",disabled:!a.src,onClick:t[1]||(t[1]=f=>a.handlerToolbar("rotate",-45))},null,8,["disabled"])]),_:1},8,["title"]),M(p,{title:a.t("component.cropper.btn_rotate_right"),placement:"bottom"},{default:z(()=>[M(s,{type:"primary",preIcon:"ant-design:rotate-right-outlined",size:"small",disabled:!a.src,onClick:t[2]||(t[2]=f=>a.handlerToolbar("rotate",45))},null,8,["disabled"])]),_:1},8,["title"]),M(p,{title:a.t("component.cropper.btn_scale_x"),placement:"bottom"},{default:z(()=>[M(s,{type:"primary",preIcon:"vaadin:arrows-long-h",size:"small",disabled:!a.src,onClick:t[3]||(t[3]=f=>a.handlerToolbar("scaleX"))},null,8,["disabled"])]),_:1},8,["title"]),M(p,{title:a.t("component.cropper.btn_scale_y"),placement:"bottom"},{default:z(()=>[M(s,{type:"primary",preIcon:"vaadin:arrows-long-v",size:"small",disabled:!a.src,onClick:t[4]||(t[4]=f=>a.handlerToolbar("scaleY"))},null,8,["disabled"])]),_:1},8,["title"]),M(p,{title:a.t("component.cropper.btn_zoom_in"),placement:"bottom"},{default:z(()=>[M(s,{type:"primary",preIcon:"ant-design:zoom-in-outlined",size:"small",disabled:!a.src,onClick:t[5]||(t[5]=f=>a.handlerToolbar("zoom",.1))},null,8,["disabled"])]),_:1},8,["title"]),M(p,{title:a.t("component.cropper.btn_zoom_out"),placement:"bottom"},{default:z(()=>[M(s,{type:"primary",preIcon:"ant-design:zoom-out-outlined",size:"small",disabled:!a.src,onClick:t[6]||(t[6]=f=>a.handlerToolbar("zoom",-.1))},null,8,["disabled"])]),_:1},8,["title"])]),_:1})],2)],2),_("div",{class:W(`${a.prefixCls}-right`)},[_("div",{class:W(`${a.prefixCls}-preview`)},[a.previewSource?(F(),mt("img",{key:0,src:a.previewSource,alt:a.t("component.cropper.preview")},null,8,ha)):ft("",!0)],2),a.previewSource?(F(),mt("div",{key:0,class:W(`${a.prefixCls}-group`)},[M(h,{src:a.previewSource,size:"large"},null,8,["src"]),M(h,{src:a.previewSource,size:48},null,8,["src"]),M(h,{src:a.previewSource,size:64},null,8,["src"]),M(h,{src:a.previewSource,size:80},null,8,["src"])],2)):ft("",!0)],2)],2)]),_:1},16,["onRegister","title","onOk","okText"])}const ca=Ut(sa,[["render",la]]),pa={width:{type:[String,Number],default:"200px"},value:{type:String},showBtn:{type:Boolean,default:!0},btnProps:{type:Object},btnText:{type:String,default:""},uploadApi:{type:Function}},da=_t({name:"CropperAvatar",components:{CopperModal:ca,Icon:Qe},props:pa,emits:["update:value","change"],setup(a,{emit:t,expose:i}){const e=it(a.value||""),{prefixCls:n}=Ht("cropper-avatar"),[r,{openModal:o,closeModal:s}]=qe(),{createMessage:p}=Ke(),{t:c}=we(),l=G(()=>[n]),h=G(()=>`${a.width}`.replace(/px/,"")+"px"),d=G(()=>parseInt(`${a.width}`.replace(/px/,""))/2+"px"),f=G(()=>({width:Mt(h)})),v=G(()=>({width:Mt(h),height:Mt(h)}));ii(()=>{e.value=a.value||""}),ai(()=>e.value,g=>{t("update:value",g)});function b({source:g,data:D}){e.value=g,t("change",g,D),p.success(c("component.cropper.uploadSuccess"))}return i({openModal:o.bind(null,!0),closeModal:s}),{t:c,prefixCls:n,register:r,openModal:o,getIconWidth:d,sourceValue:e,getClass:l,getImageWrapperStyle:v,getStyle:f,handleUploadSuccess:b}}}),ua=["src"];function fa(a,t,i,e,n,r){const o=X("Icon"),s=X("a-button"),p=X("CopperModal");return F(),mt("div",{class:W(a.getClass),style:nt(a.getStyle)},[_("div",{class:W(`${a.prefixCls}-image-wrapper`),style:nt(a.getImageWrapperStyle),onClick:t[0]||(t[0]=(...c)=>a.openModal&&a.openModal(...c))},[_("div",{class:W(`${a.prefixCls}-image-mask`),style:nt(a.getImageWrapperStyle)},[M(o,{icon:"ant-design:cloud-upload-outlined",size:a.getIconWidth,style:nt(a.getImageWrapperStyle),color:"#d6d6d6"},null,8,["size","style"])],6),a.sourceValue?(F(),mt("img",{key:0,src:a.sourceValue,alt:"avatar"},null,8,ua)):ft("",!0)],6),a.showBtn?(F(),Rt(s,De({key:0,class:`${a.prefixCls}-upload-btn`,onClick:a.openModal},a.btnProps),{default:z(()=>[ri(ni(a.btnText?a.btnText:a.t("component.cropper.selectImage")),1)]),_:1},16,["class","onClick"])):ft("",!0),M(p,{onRegister:a.register,onUploadSuccess:a.handleUploadSuccess,uploadApi:a.uploadApi,src:a.sourceValue},null,8,["onRegister","onUploadSuccess","uploadApi","src"])],6)}const ga=Ut(da,[["render",fa],["__scopeId","data-v-0a6370f1"]]),Ca=ye(We),Ma=ye(ga);export{Ma as C,Ca as a};
