var q=Object.defineProperty,Q=Object.defineProperties;var U=Object.getOwnPropertyDescriptors;var B=Object.getOwnPropertySymbols;var K=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var L=(r,e,i)=>e in r?q(r,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):r[e]=i,M=(r,e)=>{for(var i in e||(e={}))K.call(e,i)&&L(r,i,e[i]);if(B)for(var i of B(e))X.call(e,i)&&L(r,i,e[i]);return r},z=(r,e)=>Q(r,U(e));var w=(r,e,i)=>new Promise((d,l)=>{var g=m=>{try{p(i.next(m))}catch(h){l(h)}},c=m=>{try{p(i.throw(m))}catch(h){l(h)}},p=m=>m.done?d(m.value):Promise.resolve(m.value).then(g,c);p((i=i.apply(r,e)).next())});import{f as v,d as $,e as D,o as Y,aq as Z,ar as G,at as b,G as C,k as o,u as t,au as I,aD as a,as as tt,aG as et,aE as ot,H as at,aB as it}from"./vue-vendor-dy9k-Yad.js";import{n as rt,aa as st,ar as x,H as O,w as nt,u as lt}from"./index-CCWaWN5g.js";import{L as P,J as E,bi as pt,a3 as mt,bj as ut,bk as dt,an as ct,T as F,bl as ft,ab as gt,aG as vt}from"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import{u as ht}from"./useForm-CgkFTrrO.js";import{B as _t}from"./BasicForm-DBcXiHk0.js";import{P as bt}from"./index-CtJ0w2CP.js";import{d as yt}from"./table-BDFKJhHv.js";import"./vxe-table-vendor-B22HppNm.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const y=v(12),Ct=(r=6,e=12)=>({min:r,max:e,marks:(()=>{const d={};for(let l=r;l<e+1;l++)d[l]={style:{color:"#fff"},label:l};return d})(),step:1}),wt={class:"p-2"},xt={class:"bg-white mb-2 p-4"},kt={class:"bg-white p-2"},Mt={class:"flex justify-end space-x-2"},St=$({__name:"CardList",props:{params:O.object.def({}),api:O.func},emits:["getMethod","delete"],setup(r,{emit:e}){const i=P.Item,d=E.Meta,l=pt.Text,g=D(()=>Ct(4)),c=r,p=e,m=v([]),h=D(()=>`h-${120-y.value*6}`),[R,{validate:j}]=ht({schemas:[{field:"type",component:"Input",label:"类型"}],labelWidth:80,baseColProps:{span:6},actionColOptions:{span:24},autoSubmitOnEnter:!0,submitFunc:N});function N(){return w(this,null,function*(){const n=yield j();yield f(n)})}function V(n){_.value=n*4,f()}Y(()=>{f(),p("getMethod",f)});function f(){return w(this,arguments,function*(n={}){const{api:s,params:u}=c;if(s&&rt(s)){const T=yield s(M(z(M({},u),{page:k.value,pageSize:_.value}),n));m.value=T.items,S.value=T.total}})}const k=v(1),_=v(36),S=v(0),A=v({showSizeChanger:!1,showQuickJumper:!0,pageSize:_,current:k,total:S,showTotal:n=>`总 ${n} 条`,onChange:H,onShowSizeChange:J});function H(n,s){k.value=n,_.value=s,f()}function J(n,s){_.value=s,f()}function W(n){return w(this,null,function*(){p("delete",n)})}return(n,s)=>(G(),Z("div",wt,[b("div",xt,[o(t(_t),{onRegister:t(R)},null,8,["onRegister"])]),C(" "+I(g.value.width)+" ",1),b("div",kt,[o(t(P),{grid:{gutter:5,xs:1,sm:2,md:4,lg:4,xl:6,xxl:t(y)},"data-source":m.value,pagination:A.value},{header:a(()=>[b("div",Mt,[et(n.$slots,"header"),o(t(F),null,{title:a(()=>[s[1]||(s[1]=b("div",{class:"w-50"},"每行显示数量",-1)),o(t(gt),ot({id:"slider"},g.value,{value:t(y),"onUpdate:value":s[0]||(s[0]=u=>at(y)?y.value=u:null),onChange:V}),null,16,["value"])]),default:a(()=>[o(t(x),null,{default:a(()=>[o(t(ft))]),_:1})]),_:1}),o(t(F),{onClick:f},{title:a(()=>s[2]||(s[2]=[C("刷新")])),default:a(()=>[o(t(x),null,{default:a(()=>[o(t(vt))]),_:1})]),_:1})])]),renderItem:a(({item:u})=>[o(t(i),null,{default:a(()=>[o(t(E),null,{title:a(()=>s[3]||(s[3]=[])),cover:a(()=>[b("div",{class:tt(h.value)},[o(t(ct),{src:u.imgs[0]},null,8,["src"])],2)]),actions:a(()=>[o(t(ut),{key:"edit"}),o(t(st),{trigger:["hover"],dropMenuList:[{text:"删除",event:"1",popConfirm:{title:"是否确认删除",confirm:W.bind(null,u.id)}}],popconfirm:""},{default:a(()=>[o(t(dt),{key:"ellipsis"})]),_:2},1032,["dropMenuList"])]),default:a(()=>[o(t(d),null,{title:a(()=>[o(t(l),{content:u.name,ellipsis:{tooltip:u.address}},null,8,["content","ellipsis"])]),avatar:a(()=>[o(t(mt),{src:u.avatar},null,8,["src"])]),description:a(()=>[C(I(u.time),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:3},8,["grid","data-source","pagination"])])]))}}),Tt=nt(St),Ee=$({__name:"index",setup(r){const{notification:e}=lt(),i={};let d=()=>{};function l(c){d=c}function g(c){e.success({message:`成功删除${c}`}),d()}return(c,p)=>(G(),it(t(bt),{title:"卡片列表示例",content:"基础封装"},{default:a(()=>[o(t(Tt),{params:i,api:t(yt),onGetMethod:l,onDelete:g},{header:a(()=>[o(t(x),{type:"primary",color:"error"},{default:a(()=>p[0]||(p[0]=[C(" 按钮1 ")])),_:1,__:[0]}),o(t(x),{type:"primary",color:"success"},{default:a(()=>p[1]||(p[1]=[C(" 按钮2 ")])),_:1,__:[1]})]),_:1},8,["api"])]),_:1}))}});export{Ee as default};
