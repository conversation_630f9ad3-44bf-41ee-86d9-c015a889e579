import{d,f as t,ap as f,o as v,ag as _,aq as a,ar as s,ah as i,k as c}from"./vue-vendor-dy9k-Yad.js";import h from"./AiChat-DA_7PGsT.js";import{a as C}from"./index-CCWaWN5g.js";import"./slide-Ds8o58gV.js";import"./ailogo-DG2_TD5d.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./chat-I63fsyMB.js";import"./chatMessage-DcTiQunt.js";import"./chatText-DGPEwQvb.js";import"./presetQuestion-CYEhQsHK.js";const k={class:"footer"},I={key:2,class:"ai-chat"},y=d({__name:"AiChatIcon",setup(R){const r=t(),l=t(""),e=t(!1),m=f(),n=t(!1);function u(){e.value=!e.value,e.value&&!n.value&&setTimeout(()=>{n.value=!0,r.value.initChat(l.value)},100)}return v(()=>{let o=m.currentRoute.value.params;l.value=o==null?void 0:o.appId,n.value=!1}),(o,g)=>{const p=_("Icon");return s(),a("div",k,[e.value?i("",!0):(s(),a("div",{key:0,class:"footer-icon",onClick:u},[c(p,{icon:"ant-design:comment-outlined",size:"22"})])),e.value?(s(),a("div",{key:1,class:"footer-close-icon",onClick:u},[c(p,{icon:"ant-design:close-outlined",size:"20"})])):i("",!0),e.value?(s(),a("div",I,[c(h,{ref_key:"aiChatRef",ref:r},null,512)])):i("",!0)])}}}),b=C(y,[["__scopeId","data-v-ad530dee"]]);export{b as default};
