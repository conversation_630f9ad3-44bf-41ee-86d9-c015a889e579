import{d as r,aq as l,ar as n,at as a,au as e,aA as i,F as p,aC as u,as as c}from"./vue-vendor-dy9k-Yad.js";import{a as d}from"./index-CCWaWN5g.js";const h=r({name:"RankList",props:{title:{type:String,default:""},list:{type:Array,default:null},height:{type:Number,default:null}}}),m={class:"rank"},f={class:"title"};function _(t,y,g,k,$,v){return n(),l("div",m,[a("h4",f,e(t.title),1),a("ul",{class:"list",style:i({height:t.height?`${t.height}px`:"auto",overflow:"auto"})},[(n(!0),l(p,null,u(t.list,(o,s)=>(n(),l("li",{key:s},[a("span",{class:c(s<3?"active":null)},e(s+1),3),a("span",null,e(o.name),1),a("span",null,e(o.total),1)]))),128))],4)])}const L=d(h,[["render",_],["__scopeId","data-v-c00a5402"]]);export{L as R};
