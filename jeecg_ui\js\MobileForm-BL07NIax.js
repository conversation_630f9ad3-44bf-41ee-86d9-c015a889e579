var S=(y,i,o)=>new Promise((d,m)=>{var f=s=>{try{c(o.next(s))}catch(r){m(r)}},u=s=>{try{c(o.throw(s))}catch(r){m(r)}},c=s=>s.done?d(s.value):Promise.resolve(s.value).then(f,u);c((o=o.apply(y,i)).next())});import{d as B,f as _,r as E,e as L,u as e,aq as R,ah as D,ar as M,F as z,k as t,aD as l,G as k,au as v,J as N}from"./vue-vendor-dy9k-Yad.js";import{a5 as x,j as O,B as h}from"./antd-vue-vendor-me9YkNVC.js";import{C as T}from"./index-DFrpKMGa.js";import U from"./LoginFormTitle-Bms3O5Qx.js";import{bO as V,bP as $,ah as A,bU as G,N as P,u as j,bQ as q,b6 as J,bS as Q}from"./index-CCWaWN5g.js";import"./useCountdown-CCWNeb_r.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFormItemSingle-Cw668yj5.js";const fe=B({__name:"MobileForm",setup(y){const i=x.Item,{t:o}=P(),{handleBackLogin:d,getLoginState:m}=V(),{getFormRules:f}=$(),{notification:u,createErrorModal:c}=j(),s=A(),r=_(),g=_(!1),n=E({mobile:"",sms:""}),{validForm:C}=q(r),F=L(()=>e(m)===G.MOBILE);function I(){return S(this,null,function*(){const p=yield C();if(p)try{g.value=!0;const a=yield s.phoneLogin(N({mobile:p.mobile,captcha:p.sms,mode:"none"}));a&&u.success({message:o("sys.login.loginSuccessTitle"),description:`${o("sys.login.loginSuccessDesc")}: ${a.realname}`,duration:3})}catch(a){u.error({message:o("sys.api.errorTip"),description:a.message||o("sys.api.networkExceptionMsg"),duration:3})}finally{g.value=!1}})}function w(){return J({mobile:n.mobile,smsmode:Q.FORGET_PASSWORD})}return(p,a)=>F.value?(M(),R(z,{key:0},[t(U,{class:"enter-x"}),t(e(x),{class:"p-4 enter-x",model:n,rules:e(f),ref_key:"formRef",ref:r},{default:l(()=>[t(e(i),{name:"mobile",class:"enter-x"},{default:l(()=>[t(e(O),{size:"large",value:n.mobile,"onUpdate:value":a[0]||(a[0]=b=>n.mobile=b),placeholder:e(o)("sys.login.mobile"),class:"fix-auto-fill"},null,8,["value","placeholder"])]),_:1}),t(e(i),{name:"sms",class:"enter-x"},{default:l(()=>[t(e(T),{size:"large",class:"fix-auto-fill",value:n.sms,"onUpdate:value":a[1]||(a[1]=b=>n.sms=b),placeholder:e(o)("sys.login.smsCode"),sendCodeApi:w},null,8,["value","placeholder"])]),_:1}),t(e(i),{class:"enter-x"},{default:l(()=>[t(e(h),{type:"primary",size:"large",block:"",onClick:I,loading:g.value},{default:l(()=>[k(v(e(o)("sys.login.loginButton")),1)]),_:1},8,["loading"]),t(e(h),{size:"large",block:"",class:"mt-4",onClick:e(d)},{default:l(()=>[k(v(e(o)("sys.login.backSignIn")),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])],64)):D("",!0)}});export{fe as default};
