import{f as b,e as g,ag as k,aq as c,ar as m,ah as f,at as i,k as t,aD as s,F,aC as M,aB as j,au as I,aE as d}from"./vue-vendor-dy9k-Yad.js";import{a as L}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const V={name:"AiModelSeniorForm",components:{},props:{modelParams:{type:Object,default:{}},type:{type:String,default:"model"}},emits:["success","register","updateModel"],setup(E,{emit:l}){const r=[{name:"创意",icon:"fxemoji:star",params:{temperature:.8,topP:.9,presencePenalty:.1,frequencyPenalty:.1,maxTokens:null}},{name:"平衡",icon:"noto:balance-scale",params:{temperature:.5,topP:.8,presencePenalty:.2,frequencyPenalty:.3,maxTokens:null}},{name:"精确",icon:"twemoji:direct-hit",params:{temperature:.2,topP:.7,presencePenalty:.5,frequencyPenalty:.5,maxTokens:null}}],e={temperature:"值越大，回复内容越赋有多样性创造性、随机性；设为0根据事实回答，希望得到精准答案应该降低该参数；日常聊天建议0.5-0.8。",topP:"值越小，Ai生成的内容越单调也越容易理解；值越大，Ai回复的词汇围越大，越多样化。",presencePenalty:"值越大，越能够让Ai更好地控制新话题的引入，建议微调或不变。",frequencyPenalty:"值越大，越能够让Ai更好地避免重复之前说过的话，建议微调或不变。",maxTokens:"设置Ai最大回复内容大小，会影响返回结果的长度。普通聊天建议500-800；短文生成建议800-2000；代码生成建议2000-3600；长文生成建议4000左右（或选择长回复模型)",topNumber:"用于筛选与用户问题相似度最高的文本片段。系统同时会根据选用模型上下文窗口大小动态调整分段数量。",similarity:"用于设置文本片段筛选的相似度阅值。"},U=b({min:.1,max:1,step:.1}),T=b({min:.1,max:1,step:.1}),u=b({min:-2,max:2,step:.1}),p=b({min:-2,max:2,step:.1}),q=b({min:1,max:16e3,step:1}),_=b({min:1,max:10,step:1}),y=b({min:.1,max:1,step:.1}),a=b(E.modelParams||{}),P=g({get:()=>a.value.temperature!=null,set:o=>a.value.temperature=o?.7:null}),v=g({get:()=>a.value.topP!=null,set:o=>a.value.topP=o?0:null}),n=g({get:()=>a.value.presencePenalty!=null,set:o=>a.value.presencePenalty=o?0:null}),x=g({get:()=>a.value.frequencyPenalty!=null,set:o=>a.value.frequencyPenalty=o?0:null}),z=g({get:()=>a.value.maxTokens!=null,set:o=>a.value.maxTokens=o?520:null}),w=g({get:()=>a.value.topNumber!=null,set:o=>a.value.topNumber=o?4:null}),A=g({get:()=>a.value.similarity!=null,set:o=>a.value.similarity=o?.74:null});function C(o){const N=r[o];N&&(a.value=N.params)}function B(){return a.value}function S(o){a.value=o}return{presets:r,onLoadPreset:C,tips:e,temperatureProps:U,topPProps:T,presencePenaltyProps:u,model:a,frequencyPenaltyProps:p,temperatureEnable:P,maxTokensProps:q,emitChange:B,topPEnable:v,presencePenaltyEnable:n,frequencyPenaltyEnable:x,maxTokensEnable:z,topNumberEnable:w,topNumberProps:_,similarityEnable:A,similarityProps:y,setModalParams:S}}},D={class:"model-params-popover"},K={key:0,class:"params"},O={key:1,class:"setting-item"},G={class:"label"},H={key:2,class:"setting-item"},J={class:"label"},Q={key:3,class:"setting-item"},R={class:"label"},W={key:4,class:"setting-item"},X={class:"label"},Y={key:5,class:"setting-item"},Z={class:"label"},h={key:6,class:"setting-item"},$={class:"label"},ee={key:7,class:"setting-item"},le={class:"label"};function ne(E,l,r,e,U,T){const u=k("Icon"),p=k("a-space"),q=k("a-select-option"),_=k("a-select"),y=k("a-tooltip"),a=k("a-switch"),P=k("a-slider"),v=k("a-input-number");return m(),c("div",D,[r.type==="model"?(m(),c("div",K,[l[21]||(l[21]=i("span",{style:{"font-size":"14px"}},"参数",-1)),t(_,{value:"加载预设",style:{width:"96px"},size:"small",onChange:e.onLoadPreset},{default:s(()=>[(m(!0),c(F,null,M(e.presets,(n,x)=>(m(),j(q,{value:x,key:x},{default:s(()=>[t(p,null,{default:s(()=>[t(u,{icon:n.icon},null,8,["icon"]),i("span",null,I(n.name),1)]),_:2},1024)]),_:2},1032,["value"]))),128))]),_:1},8,["onChange"])])):f("",!0),r.type==="model"?(m(),c("div",O,[i("div",G,[l[22]||(l[22]=i("span",null,"模型温度",-1)),t(y,{title:e.tips.temperature},{default:s(()=>[t(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),t(p,null,{default:s(()=>[t(a,{checked:e.temperatureEnable,"onUpdate:checked":l[0]||(l[0]=n=>e.temperatureEnable=n),size:"small"},null,8,["checked"]),t(P,d(e.temperatureProps,{value:e.model.temperature,"onUpdate:value":l[1]||(l[1]=n=>e.model.temperature=n),disabled:e.model.temperature===null}),null,16,["value","disabled"]),t(v,d(e.temperatureProps,{value:e.model.temperature,"onUpdate:value":l[2]||(l[2]=n=>e.model.temperature=n),disabled:e.model.temperature===null}),null,16,["value","disabled"])]),_:1})])):f("",!0),r.type==="model"?(m(),c("div",H,[i("div",J,[l[23]||(l[23]=i("span",null,"词汇属性",-1)),t(y,{title:e.tips.topP},{default:s(()=>[t(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),t(p,null,{default:s(()=>[t(a,{checked:e.topPEnable,"onUpdate:checked":l[3]||(l[3]=n=>e.topPEnable=n),size:"small"},null,8,["checked"]),t(P,d(e.topPProps,{value:e.model.topP,"onUpdate:value":l[4]||(l[4]=n=>e.model.topP=n),disabled:e.model.topP===null}),null,16,["value","disabled"]),t(v,d(e.topPProps,{value:e.model.topP,"onUpdate:value":l[5]||(l[5]=n=>e.model.topP=n),disabled:e.model.topP===null}),null,16,["value","disabled"])]),_:1})])):f("",!0),r.type==="model"?(m(),c("div",Q,[i("div",R,[l[24]||(l[24]=i("span",null,"话题属性",-1)),t(y,{title:e.tips.presencePenalty},{default:s(()=>[t(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),t(p,null,{default:s(()=>[t(a,{checked:e.presencePenaltyEnable,"onUpdate:checked":l[6]||(l[6]=n=>e.presencePenaltyEnable=n),size:"small"},null,8,["checked"]),t(P,d(e.presencePenaltyProps,{value:e.model.presencePenalty,"onUpdate:value":l[7]||(l[7]=n=>e.model.presencePenalty=n),disabled:e.model.presencePenalty===null}),null,16,["value","disabled"]),t(v,d(e.presencePenaltyProps,{value:e.model.presencePenalty,"onUpdate:value":l[8]||(l[8]=n=>e.model.presencePenalty=n),disabled:e.model.presencePenalty===null}),null,16,["value","disabled"])]),_:1})])):f("",!0),r.type==="model"?(m(),c("div",W,[i("div",X,[l[25]||(l[25]=i("span",null,"重复属性",-1)),t(y,{title:e.tips.frequencyPenalty},{default:s(()=>[t(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),t(p,null,{default:s(()=>[t(a,{checked:e.frequencyPenaltyEnable,"onUpdate:checked":l[9]||(l[9]=n=>e.frequencyPenaltyEnable=n),size:"small"},null,8,["checked"]),t(P,d(e.frequencyPenaltyProps,{value:e.model.frequencyPenalty,"onUpdate:value":l[10]||(l[10]=n=>e.model.frequencyPenalty=n),disabled:e.model.frequencyPenalty===null}),null,16,["value","disabled"]),t(v,d(e.frequencyPenaltyProps,{value:e.model.frequencyPenalty,"onUpdate:value":l[11]||(l[11]=n=>e.model.frequencyPenalty=n),disabled:e.model.frequencyPenalty===null}),null,16,["value","disabled"])]),_:1})])):f("",!0),r.type==="model"?(m(),c("div",Y,[i("div",Z,[l[26]||(l[26]=i("span",null,"最大回复",-1)),t(y,{title:e.tips.maxTokens},{default:s(()=>[t(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),t(p,null,{default:s(()=>[t(a,{checked:e.maxTokensEnable,"onUpdate:checked":l[12]||(l[12]=n=>e.maxTokensEnable=n),size:"small"},null,8,["checked"]),t(P,d(e.maxTokensProps,{value:e.model.maxTokens,"onUpdate:value":l[13]||(l[13]=n=>e.model.maxTokens=n),disabled:e.model.maxTokens===null}),null,16,["value","disabled"]),t(v,d(e.maxTokensProps,{value:e.model.maxTokens,"onUpdate:value":l[14]||(l[14]=n=>e.model.maxTokens=n),disabled:e.model.maxTokens===null}),null,16,["value","disabled"])]),_:1})])):f("",!0),r.type==="knowledge"?(m(),c("div",h,[i("div",$,[l[27]||(l[27]=i("span",null,"Top K",-1)),t(y,{title:e.tips.topNumber},{default:s(()=>[t(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),t(p,null,{default:s(()=>[t(a,{checked:e.topNumberEnable,"onUpdate:checked":l[15]||(l[15]=n=>e.topNumberEnable=n),size:"small"},null,8,["checked"]),t(P,d(e.topNumberProps,{value:e.model.topNumber,"onUpdate:value":l[16]||(l[16]=n=>e.model.topNumber=n),disabled:e.model.topNumber===null}),null,16,["value","disabled"]),t(v,d(e.topNumberProps,{value:e.model.topNumber,"onUpdate:value":l[17]||(l[17]=n=>e.model.topNumber=n),disabled:e.model.topNumber===null}),null,16,["value","disabled"])]),_:1})])):f("",!0),r.type==="knowledge"?(m(),c("div",ee,[i("div",le,[l[28]||(l[28]=i("span",null,"Score 阈值",-1)),t(y,{title:e.tips.similarity},{default:s(()=>[t(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),t(p,null,{default:s(()=>[t(a,{checked:e.similarityEnable,"onUpdate:checked":l[18]||(l[18]=n=>e.similarityEnable=n),size:"small"},null,8,["checked"]),t(P,d(e.similarityProps,{value:e.model.similarity,"onUpdate:value":l[19]||(l[19]=n=>e.model.similarity=n),disabled:e.model.similarity===null}),null,16,["value","disabled"]),t(v,d(e.similarityProps,{value:e.model.similarity,"onUpdate:value":l[20]||(l[20]=n=>e.model.similarity=n),disabled:e.model.similarity===null}),null,16,["value","disabled"])]),_:1})])):f("",!0)])}const ie=L(V,[["render",ne],["__scopeId","data-v-60e2f6e3"]]);export{ie as default};
