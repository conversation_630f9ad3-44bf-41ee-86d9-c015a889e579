var re=Object.defineProperty,pe=Object.defineProperties;var se=Object.getOwnPropertyDescriptors;var Y=Object.getOwnPropertySymbols;var de=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable;var A=(r,n,o)=>n in r?re(r,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[n]=o,N=(r,n)=>{for(var o in n||(n={}))de.call(n,o)&&A(r,o,n[o]);if(Y)for(var o of Y(n))ce.call(n,o)&&A(r,o,n[o]);return r},U=(r,n)=>pe(r,se(n));var c=(r,n,o)=>new Promise((k,u)=>{var L=d=>{try{g(o.next(d))}catch(y){u(y)}},v=d=>{try{g(o.throw(d))}catch(y){u(y)}},g=d=>d.done?k(d.value):Promise.resolve(d.value).then(L,v);g((o=o.apply(r,n)).next())});import{d as Q,r as j,f as me,ag as f,v as ue,aq as fe,ar as h,k as m,aD as p,u as a,q as T,aB as b,ah as _e,G as x}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{useListPage as he}from"./useListPage-Soxgnx9a.js";import ge from"./YpwMallCateModal-BPJedvIV.js";import{s as ye,b as we,l as q,g as Ce,a as K,c as be,d as xe,e as De,f as Me,h as Se}from"./YpwMallCate.api-BlqWuhnE.js";import{ad as Te,a as ke}from"./index-CCWaWN5g.js";import{Q as ve}from"./componentMap-Bkie1n3v.js";import Re from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./renderUtils-D7XVOFwj.js";const Ee=Q({name:"ypw-ypwMallCate"}),Ie=Q(U(N({},Ee),{setup(r){const n=j({}),o=me([]),[k,{openModal:u}]=Te(),{prefixCls:L,tableContext:v,onExportXls:g,onImportXls:d}=he({tableProps:{api:q,title:"拍卖商品分类",columns:Me,canResize:!1,isTreeTable:!0,formConfig:{schemas:De,autoSubmitOnEnter:!0,showAdvancedButton:!0,fieldMapToNumber:[],fieldMapToTime:[["createTime",["createTime_begin","createTime_end"],"YYYY-MM-DD HH:mm:ss"]]},actionColumn:{width:240,fixed:"right"},beforeFetch:t=>(t.hasQuery="true",Object.assign(t,n))},exportConfig:{name:"拍卖商品分类",url:xe,params:n},importConfig:{url:be,success:I}}),[y,{reload:D,collapseAll:Be,updateTableDataRecord:R,findTableDataRecord:F,getDataSource:O},{rowSelection:H,selectedRowKeys:E}]=v,P=j(ye);function V(t){Object.keys(t).map(e=>{n[e]=t[e]}),D()}function X(){u(!0,{isUpdate:!1})}function z(t){return c(this,null,function*(){u(!0,{record:t,isUpdate:!0})})}function G(t){return c(this,null,function*(){u(!0,{record:t,isUpdate:!0,hideFooter:!0})})}function J(t){return c(this,null,function*(){yield Se({id:t.id},I)})}function W(){return c(this,null,function*(){const t=E.value.filter(e=>!e.includes("loadChild"));yield we({id:t},I)})}function I(){(E.value=[])&&D()}function Z(t){u(!0,{record:t,isUpdate:!1})}function $(C){return c(this,arguments,function*({isUpdate:t,values:e,expandedArr:i,changeParent:w}){if(t)if(w)D();else{let l=yield q({id:e.id,pageSize:1,pageNo:1,pid:e.pid});l&&l.records&&l.records.length>0?R(e.id,l.records[0]):R(e.id,e)}else if(!e.id||!e.pid)D();else{o.value=[];for(let l of a(i))yield ie(l)}})}function ee(t){M(t.items)&&te()}function te(){return c(this,null,function*(){if(a(o).length>0){const t=yield Ce({parentIds:a(o).join(",")});if(t.success&&t.result.records.length>0){let e=t.result.records;const i=new Map;for(let l of e){let s=l.pid;if(a(o).includes(s)){let _=i.get(s);_==null&&(_=[]),_.push(l),i.set(s,_)}}let w=i,C=l=>{l&&l.forEach(s=>{a(o).includes(s.id)&&(s.children=M(w.get(s.id)),C(s.children))})};C(O())}}})}function M(t){if(t&&t.length>0)return t.map(e=>{if(e.hasChild=="1"){let i={id:e.id+"_loadChild",name:"loading...",isLoading:!0};e.children=[i]}return e})}function oe(t,e){return c(this,null,function*(){if(t){if(o.value.push(e.id),e.children.length>0&&e.children[0].isLoading){let i=yield K({pid:e.id});i=i.records?i.records:i,i&&i.length>0?e.children=M(i):(e.children=null,e.hasChild="0")}}else{let i=o.value.indexOf(e.id);i>=0&&o.value.splice(i,1)}})}function ie(t){return c(this,null,function*(){let e=F(t);o.value.push(t);let i=yield K({pid:t});i&&i.length>0?e.children=M(i):(e.children=null,e.hasChild="0"),R(t,e)})}function ne(t){return[{label:"编辑",onClick:z.bind(null,t),auth:"ypw:ypw_mall_cate:edit"},{label:"添加下级",onClick:Z.bind(null,{pid:t.id})}]}function le(t){return[{label:"详情",onClick:G.bind(null,t)},{label:"删除",popConfirm:{title:"确定删除吗?",confirm:J.bind(null,t),placement:"topLeft"},auth:"ypw:ypw_mall_cate:delete"}]}return(t,e)=>{const i=f("a-button"),w=f("j-upload-button"),C=f("Icon"),l=f("a-menu-item"),s=f("a-menu"),_=f("a-dropdown"),ae=f("super-query"),S=ue("auth");return h(),fe("div",null,[m(a(Re),{onRegister:a(y),rowSelection:a(H),expandedRowKeys:o.value,onExpand:oe,onFetchSuccess:ee},{tableTitle:p(()=>[T((h(),b(i,{type:"primary",onClick:X,preIcon:"ant-design:plus-outlined"},{default:p(()=>e[0]||(e[0]=[x(" 新增")])),_:1,__:[0]})),[[S,"ypw:ypw_mall_cate:add"]]),T((h(),b(i,{type:"primary",preIcon:"ant-design:export-outlined",onClick:a(g)},{default:p(()=>e[1]||(e[1]=[x(" 导出")])),_:1,__:[1]},8,["onClick"])),[[S,"ypw:ypw_mall_cate:exportXls"]]),T((h(),b(w,{type:"primary",preIcon:"ant-design:import-outlined",onClick:a(d)},{default:p(()=>e[2]||(e[2]=[x("导入")])),_:1,__:[2]},8,["onClick"])),[[S,"ypw:ypw_mall_cate:importExcel"]]),a(E).length>0?(h(),b(_,{key:0},{overlay:p(()=>[m(s,null,{default:p(()=>[m(l,{key:"1",onClick:W},{default:p(()=>[m(C,{icon:"ant-design:delete-outlined"}),e[3]||(e[3]=x(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:p(()=>[T((h(),b(i,null,{default:p(()=>[e[4]||(e[4]=x("批量操作 ")),m(C,{icon:"ant-design:down-outlined"})]),_:1,__:[4]})),[[S,"ypw:ypw_mall_cate:deleteBatch"]])]),_:1})):_e("",!0),m(ae,{config:P,onSearch:V},null,8,["config"])]),action:p(({record:B})=>[m(a(ve),{actions:ne(B),dropDownActions:le(B)},null,8,["actions","dropDownActions"])]),bodyCell:p(({column:B,record:Le,index:Ye,text:Ae})=>e[5]||(e[5]=[])),_:1},8,["onRegister","rowSelection","expandedRowKeys"]),m(ge,{onRegister:a(k),onSuccess:$},null,8,["onRegister"])])}}})),Ht=ke(Ie,[["__scopeId","data-v-0013f618"]]);export{Ht as default};
