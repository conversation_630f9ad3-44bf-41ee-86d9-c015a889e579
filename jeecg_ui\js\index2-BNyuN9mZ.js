import{d as m,f as l,ag as n,aq as p,ar as _,k as e,aD as s,G as x}from"./vue-vendor-dy9k-Yad.js";import{C as c}from"./index-LCGLvkB3.js";import{a as f}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";const g=m({components:{CollapseContainer:c},setup(){const a=l(null),t=l([{id:10001,name:"Test1",role:"Develop",sex:"Man",age:28,address:"vxe-table 从入门到放弃"},{id:10002,name:"Test2",role:"Test",sex:"Women",age:22,address:"Guangzhou"},{id:10003,name:"Test3",role:"PM",sex:"Man",age:32,address:"Shanghai"},{id:10004,name:"Test4",role:"Designer",sex:"Women",age:24,address:"Shanghai"}]);return{allAlign:a,tableData1:t}}}),u={style:{padding:"5px"}};function v(a,t,b,C,T,h){const r=n("vxe-button"),i=n("vxe-toolbar"),o=n("vxe-table-column"),d=n("vxe-table");return _(),p("div",u,[e(i,null,{buttons:s(()=>[e(r,{onClick:t[0]||(t[0]=D=>a.allAlign="left")},{default:s(()=>t[1]||(t[1]=[x("新增")])),_:1,__:[1]})]),_:1}),e(d,{align:a.allAlign,data:a.tableData1},{default:s(()=>[e(o,{type:"seq",width:"60"}),e(o,{field:"name",title:"Name"}),e(o,{field:"sex",title:"Sex"}),e(o,{field:"age",title:"Age"})]),_:1},8,["align","data"])])}const G=f(g,[["render",v]]);export{G as default};
