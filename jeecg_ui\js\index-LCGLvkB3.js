var te=Object.defineProperty;var F=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,oe=Object.prototype.propertyIsEnumerable;var O=(e,t,n)=>t in e?te(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,L=(e,t)=>{for(var n in t||(t={}))ne.call(t,n)&&O(e,n,t[n]);if(F)for(var n of F(t))oe.call(t,n)&&O(e,n,t[n]);return e};import{a as T,F as se,aN as D,aO as x,aP as j,w as I}from"./index-CCWaWN5g.js";import{d as $,ag as z,aq as g,ar as u,as as S,k as C,at as E,aD as w,aG as m,F as K,G as ae,au as re,aB as b,ah as M,f as d,u as l,aE as N,q as le,B as ie,g as ce,c as ue,e as Q,b as fe,l as V,p as de,o as Z,n as R,j as pe,aA as X,aQ as me,r as ve,z as Y,I as ge,O as ye}from"./vue-vendor-dy9k-Yad.js";import{ae as ee}from"./antd-vue-vendor-me9YkNVC.js";import{C as he}from"./index-De_W6s5g.js";import{B as Se,b as we}from"./index-CImCetrx.js";import{triggerWindowResize as be,addResizeListener as G,removeResizeListener as U}from"./index-D6l0IxOU.js";import{useTimeoutFn as H}from"./useTimeout-CeTdFD_D.js";import{useIntersectionObserver as $e}from"./useIntersectionObserver-C4LVxQJW.js";const ze={prefixCls:{type:String},helpMessage:{type:[Array,String],default:""},title:{type:String},show:{type:Boolean},canExpan:{type:Boolean}},Ce=$({components:{BasicArrow:we,BasicTitle:Se},inheritAttrs:!1,props:ze,emits:["expand"]});function ke(e,t,n,a,o,s){const r=z("BasicTitle"),i=z("BasicArrow");return u(),g("div",{class:S([`${e.prefixCls}__header px-2 py-5`,e.$attrs.class])},[C(r,{helpMessage:e.helpMessage,normal:""},{default:w(()=>[e.title?(u(),g(K,{key:0},[ae(re(e.title),1)],64)):m(e.$slots,"title",{key:1})]),_:3},8,["helpMessage"]),E("div",{class:S(`${e.prefixCls}__action`)},[m(e.$slots,"action"),e.canExpan?(u(),b(i,{key:0,up:"",expand:e.show,onClick:t[0]||(t[0]=y=>e.$emit("expand"))},null,8,["expand"])):M("",!0)],2)],2)}const Be=T(Ce,[["render",ke]]),Te={class:"p-2"},_e=$({__name:"CollapseContainer",props:{title:{type:String,default:""},defaultExpan:{type:Boolean,default:!0},loading:{type:Boolean},canExpan:{type:Boolean,default:!0},helpMessage:{type:[Array,String],default:""},triggerWindowResize:{type:Boolean},lazyTime:{type:Number,default:0}},setup(e){const t=e,n=d(t.defaultExpan),{prefixCls:a}=se("collapse-container");function o(){n.value=!n.value,t.triggerWindowResize&&H(be,200)}return(s,r)=>(u(),g("div",{class:S(l(a))},[C(Be,N(s.$props,{prefixCls:l(a),show:n.value,onExpand:o}),{title:w(()=>[m(s.$slots,"title")]),action:w(()=>[m(s.$slots,"action")]),_:3},16,["prefixCls","show"]),E("div",Te,[C(l(he),{enable:e.canExpan},{default:w(()=>[e.loading?(u(),b(l(ee),{key:0,active:e.loading},null,8,["active"])):le((u(),g("div",{key:1,class:S(`${l(a)}__body`)},[m(s.$slots,"default")],2)),[[ie,n.value]])]),_:3},8,["enable"])]),s.$slots.footer?(u(),g("div",{key:0,class:S(`${l(a)}__footer`)},[m(s.$slots,"footer")],2)):M("",!0)],2))}}),Ae={table:{fetchSetting:{pageField:"pageNo",sizeField:"pageSize",listField:"records",totalField:"total"},pageSizeOptions:["10","50","80","100"],defaultSize:"middle",defaultPageSize:10,defaultSortFn:e=>{if(e instanceof Array){let t=[];for(let n of e){let a=q(n);a&&t.push(a)}return{sortInfoString:JSON.stringify(t)}}else return q(e)||{}},defaultFilterFn:e=>e,scrollToFirstRowOnChange:!1},scrollbar:{native:!1},form:{labelCol:{xs:{span:24},sm:{span:4},xl:{span:6},xxl:{span:4}},wrapperCol:{xs:{span:24},sm:{span:18}},colon:!0}};function q(e){const{field:t,order:n}=e;if(t&&n){let a=n=="ascend"?"asc":"desc";return{column:t,order:a}}return""}const Re={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}};function He({move:e,size:t,bar:n}){const a={},o=`translate${n.axis}(${e}%)`;return a[n.size]=t,a.transform=o,a.msTransform=o,a.webkitTransform=o,a}function Ie(e,t){return Object.assign(e,t)}function Ee(e){const t={};for(let n=0;n<e.length;n++)e[n]&&Ie(t,e[n]);return t}const Me=$({name:"Bar",props:{vertical:Boolean,size:String,move:Number},setup(e){const t=ce(),n=d(),a=ue("scroll-bar-wrap",{}),o=Q(()=>Re[e.vertical?"vertical":"horizontal"]),s=d({}),r=d(),i=c=>{var p;c.ctrlKey||c.button===2||((p=window.getSelection())==null||p.removeAllRanges(),f(c),s.value[o.value.axis]=c.currentTarget[o.value.offset]-(c[o.value.client]-c.currentTarget.getBoundingClientRect()[o.value.direction]))},y=c=>{var B;const p=Math.abs(c.target.getBoundingClientRect()[o.value.direction]-c[o.value.client]),k=n.value[o.value.offset]/2,_=(p-k)*100/((B=t==null?void 0:t.vnode.el)==null?void 0:B[o.value.offset]);a.value[o.value.scroll]=_*a.value[o.value.scrollSize]/100},f=c=>{c.stopImmediatePropagation(),r.value=!0,D(document,"mousemove",v),D(document,"mouseup",h),document.onselectstart=()=>!1},v=c=>{var P,W;if(r.value===!1)return;const p=s.value[o.value.axis];if(!p)return;const k=(((P=t==null?void 0:t.vnode.el)==null?void 0:P.getBoundingClientRect()[o.value.direction])-c[o.value.client])*-1,_=n.value[o.value.offset]-p,B=(k-_)*100/((W=t==null?void 0:t.vnode.el)==null?void 0:W[o.value.offset]);a.value[o.value.scroll]=B*a.value[o.value.scrollSize]/100};function h(){r.value=!1,s.value[o.value.axis]=0,x(document,"mousemove",v),document.onselectstart=null}return fe(()=>{x(document,"mouseup",h)}),()=>V("div",{class:["scrollbar__bar","is-"+o.value.key],onMousedown:y},V("div",{ref:n,class:"scrollbar__thumb",onMousedown:i,style:He({size:e.size,move:e.move,bar:o.value})}))}}),{scrollbar:A}=Ae;var J;const Ne=$({name:"Scrollbar",components:{Bar:Me},props:{native:{type:Boolean,default:(J=A==null?void 0:A.native)!=null?J:!1},wrapStyle:{type:[String,Array],default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array],default:""},noresize:Boolean,tag:{type:String,default:"div"}},setup(e){const t=d("0"),n=d("0"),a=d(0),o=d(0),s=d(),r=d();de("scroll-bar-wrap",s);const i=Q(()=>Array.isArray(e.wrapStyle)?Ee(e.wrapStyle):e.wrapStyle),y=()=>{e.native||(o.value=l(s).scrollTop*100/l(s).clientHeight,a.value=l(s).scrollLeft*100/l(s).clientWidth)},f=()=>{if(!l(s))return;const v=l(s).clientHeight*100/l(s).scrollHeight,h=l(s).clientWidth*100/l(s).scrollWidth;n.value=v<100?v+"%":"",t.value=h<100?h+"%":""};return Z(()=>{e.native||(R(f),e.noresize||(G(l(r),f),G(l(s),f),addEventListener("resize",f)))}),pe(()=>{e.native||e.noresize||(U(l(r),f),U(l(s),f),removeEventListener("resize",f))}),{moveX:a,moveY:o,sizeWidth:t,sizeHeight:n,style:i,wrap:s,resize:r,update:f,handleScroll:y}}}),Pe={class:"scrollbar"};function We(e,t,n,a,o,s){const r=z("bar");return u(),g("div",Pe,[E("div",{ref:"wrap",class:S([e.wrapClass,"scrollbar__wrap",e.native?"":"scrollbar__wrap--hidden-default"]),style:X(e.style),onScroll:t[0]||(t[0]=(...i)=>e.handleScroll&&e.handleScroll(...i))},[(u(),b(me(e.tag),{ref:"resize",class:S(["scrollbar__view",e.viewClass]),style:X(e.viewStyle)},{default:w(()=>[m(e.$slots,"default")]),_:3},8,["class","style"]))],38),e.native?M("",!0):(u(),g(K,{key:0},[C(r,{move:e.moveX,size:e.sizeWidth},null,8,["move","size"]),C(r,{vertical:"",move:e.moveY,size:e.sizeHeight},null,8,["move","size"])],64))])}const Fe=T(Ne,[["render",We]]),Oe=$({name:"ScrollContainer",components:{Scrollbar:Fe},setup(){const e=d(null);function t(o,s=500){const r=l(e);r&&R(()=>{const i=l(r.wrap);if(!i)return;const{start:y}=j({el:i,to:o,duration:s});y()})}function n(){const o=l(e);return o?o.wrap:null}function a(){const o=l(e);o&&R(()=>{const s=l(o.wrap);if(!s)return;const r=s.scrollHeight,{start:i}=j({el:s,to:r});i()})}return{scrollbarRef:e,scrollTo:t,scrollBottom:a,getScrollWrap:n}}});function Le(e,t,n,a,o,s){const r=z("Scrollbar");return u(),b(r,N({ref:"scrollbarRef",class:"scroll-container"},e.$attrs),{default:w(()=>[m(e.$slots,"default")]),_:3},16)}const De=T(Oe,[["render",Le]]),xe={timeout:{type:Number},viewport:{type:typeof window!="undefined"?window.HTMLElement:Object,default:()=>null},threshold:{type:String,default:"0px"},direction:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},tag:{type:String,default:"div"},maxWaitingTime:{type:Number,default:80},transitionName:{type:String,default:"lazy-container"}},je=$({name:"LazyContainer",components:{Skeleton:ee},inheritAttrs:!1,props:xe,emits:["init"],setup(e,{emit:t}){const n=d(),a=ve({isInit:!1,loading:!1,intersectionObserverInstance:null});Z(()=>{o(),r()});function o(){const{timeout:i}=e;i&&H(()=>{s()},i)}function s(){a.loading=!0,H(()=>{a.isInit||(a.isInit=!0,t("init"))},e.maxWaitingTime||80)}function r(){const{timeout:i,direction:y,threshold:f}=e;if(i)return;let v="0px";switch(y){case"vertical":v=`${f} 0px`;break;case"horizontal":v=`0px ${f}`;break}try{const{stop:h,observer:c}=$e({rootMargin:v,target:Y(n.value,"$el"),onIntersect:p=>{(p[0].isIntersecting||p[0].intersectionRatio)&&(s(),c&&h())},root:Y(e,"viewport")})}catch(h){s()}}return L({elRef:n},ge(a))}}),Ve={key:"component"},Xe={key:"skeleton"};function Ye(e,t,n,a,o,s){const r=z("Skeleton");return u(),b(ye,N({class:"h-full w-full"},e.$attrs,{ref:"elRef",name:e.transitionName,tag:e.tag,mode:"out-in"}),{default:w(()=>[e.isInit?(u(),g("div",Ve,[m(e.$slots,"default",{loading:e.loading})])):(u(),g("div",Xe,[e.$slots.skeleton?m(e.$slots,"skeleton",{key:0}):(u(),b(r,{key:1}))]))]),_:3},16,["name","tag"])}const Ge=T(je,[["render",Ye]]),ot=I(_e),st=I(De),at=I(Ge);export{ot as C,at as L,st as S,Ae as c};
