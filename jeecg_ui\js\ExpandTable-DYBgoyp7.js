import{d as c,ag as i,aB as d,ar as u,aD as r,k as m,at as f,au as b}from"./vue-vendor-dy9k-Yad.js";import{u as g}from"./index-BkGZ5fiW.js";import{P as B}from"./index-CtJ0w2CP.js";import{getBasicColumns as _}from"./tableData-B4J3mkj4.js";import{d as T}from"./table-BDFKJhHv.js";import{Q as C}from"./componentMap-Bkie1n3v.js";import w from"./BasicTable-xCEZpGLb.js";import{a as R}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const A=c({components:{BasicTable:w,TableAction:C,PageWrapper:B},setup(){const[o]=g({api:T,title:"可展开表格演示",titleHelpMessage:["已启用expandRowByClick","已启用stopButtonPropagation"],columns:_(),rowKey:"id",canResize:!1,expandRowByClick:!0,actionColumn:{width:160,title:"Action",dataIndex:"action",slots:{customRender:"action"}}});function e(n){}function p(n){}return{registerTable:o,handleDelete:e,handleOpen:p}}});function h(o,e,p,n,k,x){const a=i("TableAction"),s=i("BasicTable"),l=i("PageWrapper");return u(),d(l,{title:"可展开表格",content:"不可与scroll共用。TableAction组件可配置stopButtonPropagation来阻止操作按钮的点击事件冒泡，以便配合Table组件的expandRowByClick"},{default:r(()=>[m(s,{onRegister:o.registerTable},{expandedRowRender:r(({record:t})=>[f("span",null,"No: "+b(t.no),1)]),action:r(({record:t})=>[m(a,{stopButtonPropagation:"",actions:[{label:"删除",icon:"ic:outline-delete-outline",onClick:o.handleDelete.bind(null,t)}],dropDownActions:[{label:"启用",popConfirm:{title:"是否启用？",confirm:o.handleOpen.bind(null,t)}}]},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister"])]),_:1})}const zo=R(A,[["render",h]]);export{zo as default};
