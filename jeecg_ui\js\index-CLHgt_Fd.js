import{d as v,f as c,u as f,ag as i,aB as q,ar as b,aD as l,at as s,k as e,G as g}from"./vue-vendor-dy9k-Yad.js";import{Q as _}from"./index-C1YxH9KC.js";import{bl as w,a as D}from"./index-CCWaWN5g.js";import{C as k}from"./index-LCGLvkB3.js";import{P as U}from"./index-CtJ0w2CP.js";import"./antd-vue-vendor-me9YkNVC.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const y="https://www.vvbin.cn",Q=v({components:{CollapseContainer:k,QrCode:_,PageWrapper:U},setup(){const o=c(null),r=c(null);function d(){const t=f(o);t&&t.download("文件名")}function m(){const t=f(r);t&&t.download("Qrcode")}function p({ctx:t}){t instanceof CanvasRenderingContext2D&&(t.fillStyle="black",t.font='16px "微软雅黑"',t.textBaseline="bottom",t.textAlign="center",t.fillText("你帅你先扫",100,195,200))}return{onQrcodeDone:p,qrCodeUrl:y,LogoImg:w,download:d,downloadDiy:m,qrRef:o,qrDiyRef:r}}}),R={class:"flex flex-wrap"};function B(o,r,d,m,p,t){const n=i("QrCode"),a=i("CollapseContainer"),u=i("a-button"),C=i("PageWrapper");return b(),q(C,{title:"二维码组件使用示例"},{default:l(()=>[s("div",R,[e(a,{title:"基础示例",canExpan:!0,class:"text-center mb-6 qrcode-demo-item"},{default:l(()=>[e(n,{value:o.qrCodeUrl},null,8,["value"])]),_:1}),e(a,{title:"渲染成img标签示例",class:"text-center mb-6 qrcode-demo-item"},{default:l(()=>[e(n,{value:o.qrCodeUrl,tag:"img"},null,8,["value"])]),_:1}),e(a,{title:"配置样式示例",class:"text-center mb-6 qrcode-demo-item"},{default:l(()=>[e(n,{value:o.qrCodeUrl,options:{color:{dark:"#55D187"}}},null,8,["value"])]),_:1}),e(a,{title:"本地logo示例",class:"text-center mb-6 qrcode-demo-item"},{default:l(()=>[e(n,{value:o.qrCodeUrl,logo:o.LogoImg},null,8,["value","logo"])]),_:1}),e(a,{title:"在线logo示例",class:"text-center mb-6 qrcode-demo-item"},{default:l(()=>[e(n,{value:o.qrCodeUrl,logo:"http://jeecg.com/images/logo.png",options:{color:{dark:"#55D187"}}},null,8,["value"])]),_:1}),e(a,{title:"logo配置示例",class:"text-center mb-6 qrcode-demo-item"},{default:l(()=>[e(n,{value:o.qrCodeUrl,logo:{src:"http://jeecg.com/images/logo.png",logoSize:.2,borderSize:.05,borderRadius:50,bgColor:"blue"}},null,8,["value"])]),_:1}),e(a,{title:"下载示例",class:"text-center qrcode-demo-item"},{default:l(()=>[e(n,{value:o.qrCodeUrl,ref:"qrRef",logo:o.LogoImg},null,8,["value","logo"]),e(u,{class:"mb-2",type:"primary",onClick:o.download},{default:l(()=>r[0]||(r[0]=[g(" 下载 ")])),_:1,__:[0]},8,["onClick"]),r[1]||(r[1]=s("div",{class:"msg"}," (在线logo会导致图片跨域，需要下载图片需要自行解决跨域问题) ",-1))]),_:1,__:[1]}),e(a,{title:"配置大小示例",class:"text-center qrcode-demo-item"},{default:l(()=>[e(n,{value:o.qrCodeUrl,width:300},null,8,["value"])]),_:1}),e(a,{title:"扩展绘制示例",class:"text-center qrcode-demo-item"},{default:l(()=>[e(n,{value:o.qrCodeUrl,width:200,options:{margin:5},ref:"qrDiyRef",logo:o.LogoImg,onDone:o.onQrcodeDone},null,8,["value","logo","onDone"]),e(u,{class:"mb-2",type:"primary",onClick:o.downloadDiy},{default:l(()=>r[2]||(r[2]=[g(" 下载 ")])),_:1,__:[2]},8,["onClick"]),r[3]||(r[3]=s("div",{class:"msg"}," 要进行扩展绘制则不能将tag设为img ",-1))]),_:1,__:[3]})])]),_:1})}const K=D(Q,[["render",B],["__scopeId","data-v-539d04c0"]]);export{K as default};
