var H=Object.defineProperty,Q=Object.defineProperties;var Z=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,J=Object.prototype.propertyIsEnumerable;var D=(n,e,o)=>e in n?H(n,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[e]=o,R=(n,e)=>{for(var o in e||(e={}))j.call(e,o)&&D(n,o,e[o]);if(y)for(var o of y(e))J.call(e,o)&&D(n,o,e[o]);return n},x=(n,e)=>Q(n,Z(e));var m=(n,e,o)=>new Promise((u,c)=>{var b=a=>{try{f(o.next(a))}catch(_){c(_)}},d=a=>{try{f(o.throw(a))}catch(_){c(_)}},f=a=>a.done?u(a.value):Promise.resolve(a.value).then(b,d);f((o=o.apply(n,e)).next())});import{d as A,f as O,ag as p,aq as W,ar as v,k as i,aD as r,u as s,aB as Y,ah as tt,G as w}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{_ as ot,b as et,g as nt,a as it,s as at,c as rt,d as st,e as lt,f as mt,h as pt}from"./NoticeModal-DsujWd57.js";import ut from"./DetailModal-WtFPsX4L.js";import{b5 as ct,ad as U,aZ as dt}from"./index-CCWaWN5g.js";import{useListPage as ft}from"./useListPage-Soxgnx9a.js";import{Q as _t}from"./componentMap-Bkie1n3v.js";import gt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./renderUtils-D7XVOFwj.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const bt=A({name:"system-notice"}),vo=A(x(R({},bt),{setup(n){const e=ct(),[o,{openModal:u}]=U(),[c,{openModal:b}]=U(),d=O(""),{prefixCls:f,onExportXls:a,onImportXls:_,tableContext:I,doRequest:M}=ft({designScope:"notice-template",tableProps:{title:"消息通知",api:st,columns:rt,formConfig:{schemas:at}},exportConfig:{name:"消息通知列表",url:it},importConfig:{url:nt}}),[N,{reload:g},{rowSelection:B,selectedRowKeys:S}]=I;function E(){u(!0,{isUpdate:!1})}function T(t){u(!0,{record:t,isUpdate:!0})}function $(t){return m(this,null,function*(){yield lt({id:t.id},g)})}function V(){return m(this,null,function*(){M(()=>et({ids:S.value}))})}function q(t){return m(this,null,function*(){yield mt({id:t}),g()})}function G(t){return m(this,null,function*(){yield pt({id:t}),g()})}function K(t){d.value=`${e.uploadUrl}/sys/annountCement/show/${t.id}?token=${dt()}`,b(!0)}function L(t){return[{label:"编辑",onClick:T.bind(null,t),ifShow:t.sendStatus==0||t.sendStatus=="2"}]}function P(t){return[{label:"删除",ifShow:t.sendStatus!=1,popConfirm:{title:"是否确认删除",confirm:$.bind(null,t)}},{label:"发布",ifShow:t.sendStatus==0,onClick:q.bind(null,t.id)},{label:"撤销",ifShow:t.sendStatus==1,popConfirm:{title:"确定要撤销吗？",confirm:G.bind(null,t.id)}},{label:"查看",onClick:K.bind(null,t)}]}return(t,l)=>{const C=p("a-button"),h=p("Icon"),X=p("a-menu-item"),z=p("a-menu"),F=p("a-dropdown");return v(),W("div",null,[i(s(gt),{onRegister:s(N),rowSelection:s(B)},{tableTitle:r(()=>[i(C,{preIcon:"ant-design:plus-outlined",type:"primary",onClick:E},{default:r(()=>l[0]||(l[0]=[w("新建")])),_:1,__:[0]}),s(S).length>0?(v(),Y(F,{key:0},{overlay:r(()=>[i(z,null,{default:r(()=>[i(X,{key:"1",onClick:V},{default:r(()=>[i(h,{icon:"ant-design:delete-outlined"}),l[1]||(l[1]=w(" 删除 "))]),_:1,__:[1]})]),_:1})]),default:r(()=>[i(C,null,{default:r(()=>[l[2]||(l[2]=w("批量操作 ")),i(h,{style:{fontsize:"12px"},icon:"ant-design:down-outlined"})]),_:1,__:[2]})]),_:1})):tt("",!0)]),action:r(({record:k})=>[i(s(_t),{actions:L(k),dropDownActions:P(k)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),i(ot,{onRegister:s(o),onSuccess:s(g)},null,8,["onRegister","onSuccess"]),i(ut,{onRegister:s(c),frameSrc:d.value},null,8,["onRegister","frameSrc"])])}}}));export{vo as default};
