import{d as r,f as c,o as k,aq as f,ar as l,aG as C,ag as a,aB as _,aD as s,k as O,at as S,au as v}from"./vue-vendor-dy9k-Yad.js";import{bq as $,w as g,a as w}from"./index-CCWaWN5g.js";import{P as x}from"./index-CtJ0w2CP.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const B=r({__name:"ClickOutSide",emits:["mounted","clickOutside"],setup(e,{emit:t}){const o=t,i=c(null);return $(i,()=>{o("clickOutside")}),k(()=>{o("mounted")}),(n,p)=>(l(),f("div",{ref_key:"wrap",ref:i},[C(n.$slots,"default")],512))}}),h=g(B),P=r({components:{ClickOutSide:h,PageWrapper:x},setup(){const e=c("Click");function t(){e.value="Click Out Side"}function o(){e.value="Click Inner"}return{innerClick:o,handleClickOutside:t,text:e}}});function y(e,t,o,i,n,p){const d=a("ClickOutSide"),u=a("PageWrapper");return l(),_(u,{title:"点内外部触发事件"},{default:s(()=>[O(d,{onClickOutside:e.handleClickOutside,class:"flex justify-center"},{default:s(()=>[S("div",{onClick:t[0]||(t[0]=(...m)=>e.innerClick&&e.innerClick(...m)),class:"demo-box"},v(e.text),1)]),_:1},8,["onClickOutside"])]),_:1})}const M=w(P,[["render",y],["__scopeId","data-v-ee5392f0"]]);export{M as default};
