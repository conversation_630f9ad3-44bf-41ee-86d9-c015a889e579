var v=Object.defineProperty;var g=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var w=(e,a,t)=>a in e?v(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,b=(e,a)=>{for(var t in a||(a={}))k.call(a,t)&&w(e,t,a[t]);if(g)for(var t of g(a))I.call(a,t)&&w(e,t,a[t]);return e};var d=(e,a,t)=>new Promise((n,l)=>{var u=s=>{try{r(t.next(s))}catch(o){l(o)}},i=s=>{try{r(t.throw(s))}catch(o){l(o)}},r=s=>s.done?n(s.value):Promise.resolve(s.value).then(u,i);r((t=t.apply(e,a)).next())});import{l as R,d as F,f as _,e as y,u as f,ag as h,aB as T,ar as C,aE as P,aD as B,k as D}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{H as $}from"./antd-vue-vendor-me9YkNVC.js";import{s as x,c as N}from"./system-bqUZCbh5.js";import{u as V,a as L}from"./index-CCWaWN5g.js";import{B as M,u as O}from"./index-JbqXEynz.js";import{_ as U}from"./index-BtIdS_Qz.js";import{B as j}from"./BasicForm-DBcXiHk0.js";import{u as q}from"./useForm-CgkFTrrO.js";const ae=[{title:"角色名称",dataIndex:"roleName",width:200},{title:"角色值",dataIndex:"roleValue",width:180},{title:"排序",dataIndex:"orderNo",width:50},{title:"状态",dataIndex:"status",width:120,customRender:({record:e})=>(Reflect.has(e,"pendingStatus")||(e.pendingStatus=!1),R($,{checked:e.status==="1",checkedChildren:"已启用",unCheckedChildren:"已禁用",loading:e.pendingStatus,onChange(a){e.pendingStatus=!0;const t=a?"1":"0",{createMessage:n}=V();x(e.id,t).then(()=>{e.status=t,n.success("已成功修改角色状态")}).catch(()=>{n.error("修改角色状态失败")}).finally(()=>{e.pendingStatus=!1})}}))},{title:"创建时间",dataIndex:"createTime",width:180},{title:"备注",dataIndex:"remark"}],se=[{field:"roleNme",label:"角色名称",component:"Input",colProps:{span:8}},{field:"status",label:"状态",component:"Select",componentProps:{options:[{label:"启用",value:"0"},{label:"停用",value:"1"}]},colProps:{span:8}}],A=[{field:"roleName",label:"角色名称",required:!0,component:"Input"},{field:"roleValue",label:"角色值",required:!0,component:"Input"},{field:"status",label:"状态",component:"RadioButtonGroup",defaultValue:"0",componentProps:{options:[{label:"启用",value:"0"},{label:"停用",value:"1"}]}},{label:"备注",field:"remark",component:"InputTextArea"},{label:" ",field:"menu",slot:"menu",component:"Input"}],G=F({name:"RoleDrawer",components:{BasicDrawer:M,BasicForm:j,BasicTree:U},emits:["success","register"],setup(e,{emit:a}){const t=_(!0),n=_([]),[l,{resetFields:u,setFieldsValue:i,validate:r}]=q({labelWidth:90,schemas:A,showActionButtonGroup:!1}),[s,{setDrawerProps:o,closeDrawer:m}]=O(c=>d(null,null,function*(){u(),o({confirmLoading:!1}),f(n).length===0&&(n.value=yield N()),t.value=!!(c!=null&&c.isUpdate),f(t)&&i(b({},c.record))})),p=y(()=>f(t)?"编辑角色":"新增角色");function S(){return d(this,null,function*(){try{const c=yield r();o({confirmLoading:!0}),m(),a("success")}finally{o({confirmLoading:!1})}})}return{registerDrawer:s,registerForm:l,getTitle:p,handleSubmit:S,treeData:n}}});function z(e,a,t,n,l,u){const i=h("BasicTree"),r=h("BasicForm"),s=h("BasicDrawer");return C(),T(s,P(e.$attrs,{onRegister:e.registerDrawer,showFooter:"",title:e.getTitle,width:"500px",onOk:e.handleSubmit}),{default:B(()=>[D(r,{onRegister:e.registerForm},{menu:B(({model:o,field:m})=>[D(i,{value:o[m],"onUpdate:value":p=>o[m]=p,treeData:e.treeData,replaceFields:{title:"menuName",key:"id"},checkable:"",toolbar:"",title:"菜单分配"},null,8,["value","onUpdate:value","treeData"])]),_:1},8,["onRegister"])]),_:1},16,["onRegister","title","onOk"])}const E=L(G,[["render",z]]),oe=Object.freeze(Object.defineProperty({__proto__:null,default:E},Symbol.toStringTag,{value:"Module"}));export{E as R,oe as a,ae as c,se as s};
