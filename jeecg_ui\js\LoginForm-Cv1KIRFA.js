var B=(T,l,u)=>new Promise((i,v)=>{var _=d=>{try{f(u.next(d))}catch(y){v(y)}},a=d=>{try{f(u.throw(d))}catch(y){v(y)}},f=d=>d.done?i(d.value):Promise.resolve(d.value).then(_,a);f((u=u.apply(T,l)).next())});import{d as j,f as b,r as q,e as A,u as e,o as Q,aq as I,ar as R,F as W,q as K,k as t,B as U,aO as J,aD as o,at as C,G as p,au as g,as as Z,J as H}from"./vue-vendor-dy9k-Yad.js";import{i as X}from"./checkcode-DLY3GIII.js";import{bF as Y,a5 as N,j as D,a6 as ee,a7 as te,z as se,B as k,V as oe,bv as ae,bG as ne,bt as le}from"./antd-vue-vendor-me9YkNVC.js";import ie from"./LoginFormTitle-Bms3O5Qx.js";import re from"./ThirdModal-1GWAIf70.js";import{F as ue,ah as de,bO as me,bP as ce,bU as x,N as pe,u as ge,bQ as fe,bZ as ye}from"./index-CCWaWN5g.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFormItemSingle-Cw668yj5.js";import"./useThirdLogin-C4qxd7Ms.js";const Ce=["src"],Oe=j({__name:"LoginForm",setup(T){const l=te,u=ee,i=N.Item,v=D.Password,_=Y({scriptUrl:"//at.alicdn.com/t/font_2316098_umqusozousr.js"}),{t:a}=pe(),{notification:f,createErrorModal:d}=ge(),{prefixCls:y}=ue("login"),O=de(),{setLoginState:w,getLoginState:G}=me(),{getFormRules:P}=ce(),$=b(),M=b(),F=b(!1),E=b(!1),m=q({account:"",password:"",inputCode:""}),c=q({randCodeImage:"",requestCodeSuccess:!1,checkKey:null}),{validForm:V}=fe($),L=A(()=>e(G)===x.LOGIN);function z(){return B(this,null,function*(){const r=yield V();if(r)try{F.value=!0;const{userInfo:s}=yield O.login(H({password:r.password,username:r.account,captcha:r.inputCode,checkKey:c.checkKey,mode:"none"}));s&&f.success({message:a("sys.login.loginSuccessTitle"),description:`${a("sys.login.loginSuccessDesc")}: ${s.realname}`,duration:3})}catch(s){f.error({message:a("sys.api.errorTip"),description:s.message||a("sys.api.networkExceptionMsg"),duration:3}),F.value=!1,h()}})}function h(){m.inputCode="",c.checkKey=new Date().getTime()+Math.random().toString(36).slice(-4),ye(c.checkKey).then(r=>{c.randCodeImage=r,c.requestCodeSuccess=!0})}function S(r){M.value.onThirdLogin(r)}return Q(()=>{h()}),(r,s)=>(R(),I(W,null,[K(t(ie,{class:"enter-x"},null,512),[[U,L.value]]),K(t(e(N),{class:"p-4 enter-x",model:m,rules:e(P),ref_key:"formRef",ref:$,onKeypress:J(z,["enter"])},{default:o(()=>[t(e(i),{name:"account",class:"enter-x"},{default:o(()=>[t(e(D),{size:"large",value:m.account,"onUpdate:value":s[0]||(s[0]=n=>m.account=n),placeholder:e(a)("sys.login.userName"),class:"fix-auto-fill"},null,8,["value","placeholder"])]),_:1}),t(e(i),{name:"password",class:"enter-x"},{default:o(()=>[t(e(v),{size:"large",visibilityToggle:"",value:m.password,"onUpdate:value":s[1]||(s[1]=n=>m.password=n),placeholder:e(a)("sys.login.password")},null,8,["value","placeholder"])]),_:1}),t(e(u),{class:"enter-x"},{default:o(()=>[t(e(l),{span:12},{default:o(()=>[t(e(i),{name:"inputCode",class:"enter-x"},{default:o(()=>[t(e(D),{size:"large",value:m.inputCode,"onUpdate:value":s[2]||(s[2]=n=>m.inputCode=n),placeholder:e(a)("sys.login.inputCode"),style:{"min-width":"100px"}},null,8,["value","placeholder"])]),_:1})]),_:1}),t(e(l),{span:8},{default:o(()=>[t(e(i),{style:{"text-align":"right","margin-left":"20px"},class:"enter-x"},{default:o(()=>[c.requestCodeSuccess?(R(),I("img",{key:0,style:{"margin-top":"2px","max-width":"initial"},src:c.randCodeImage,onClick:h},null,8,Ce)):(R(),I("img",{key:1,style:{"margin-top":"2px","max-width":"initial"},src:X,onClick:h}))]),_:1})]),_:1})]),_:1}),t(e(u),{class:"enter-x"},{default:o(()=>[t(e(l),{span:12},{default:o(()=>[t(e(i),null,{default:o(()=>[t(e(se),{checked:E.value,"onUpdate:checked":s[3]||(s[3]=n=>E.value=n),size:"small"},{default:o(()=>[p(g(e(a)("sys.login.rememberMe")),1)]),_:1},8,["checked"])]),_:1})]),_:1}),t(e(l),{span:12},{default:o(()=>[t(e(i),{style:{"text-align":"right"}},{default:o(()=>[t(e(k),{type:"link",size:"small",onClick:s[4]||(s[4]=n=>e(w)(e(x).RESET_PASSWORD))},{default:o(()=>[p(g(e(a)("sys.login.forgetPassword")),1)]),_:1})]),_:1})]),_:1})]),_:1}),t(e(i),{class:"enter-x"},{default:o(()=>[t(e(k),{type:"primary",size:"large",block:"",onClick:z,loading:F.value},{default:o(()=>[p(g(e(a)("sys.login.loginButton")),1)]),_:1},8,["loading"])]),_:1}),t(e(u),{class:"enter-x"},{default:o(()=>[t(e(l),{md:8,xs:24},{default:o(()=>[t(e(k),{block:"",onClick:s[5]||(s[5]=n=>e(w)(e(x).MOBILE))},{default:o(()=>[p(g(e(a)("sys.login.mobileSignInFormTitle")),1)]),_:1})]),_:1}),t(e(l),{md:8,xs:24,class:"!my-2 !md:my-0 xs:mx-0 md:mx-2"},{default:o(()=>[t(e(k),{block:"",onClick:s[6]||(s[6]=n=>e(w)(e(x).QR_CODE))},{default:o(()=>[p(g(e(a)("sys.login.qrSignInFormTitle")),1)]),_:1})]),_:1}),t(e(l),{md:7,xs:24},{default:o(()=>[t(e(k),{block:"",onClick:s[7]||(s[7]=n=>e(w)(e(x).REGISTER))},{default:o(()=>[p(g(e(a)("sys.login.registerButton")),1)]),_:1})]),_:1})]),_:1}),t(e(oe),{class:"enter-x"},{default:o(()=>[p(g(e(a)("sys.login.otherSignIn")),1)]),_:1}),C("div",{class:Z(["flex justify-evenly enter-x",`${e(y)}-sign-in-way`])},[C("a",{onClick:s[8]||(s[8]=n=>S("github")),title:"github"},[t(e(ae))]),C("a",{onClick:s[9]||(s[9]=n=>S("wechat_enterprise")),title:"企业微信"},[t(e(_),{class:"item-icon",type:"icon-qiyeweixin3"})]),C("a",{onClick:s[10]||(s[10]=n=>S("dingtalk")),title:"钉钉"},[t(e(ne))]),C("a",{onClick:s[11]||(s[11]=n=>S("wechat_open")),title:"微信"},[t(e(le))])],2)]),_:1},8,["model","rules"]),[[U,L.value]]),t(re,{ref_key:"thirdModalRef",ref:M},null,512)],64))}});export{Oe as default};
