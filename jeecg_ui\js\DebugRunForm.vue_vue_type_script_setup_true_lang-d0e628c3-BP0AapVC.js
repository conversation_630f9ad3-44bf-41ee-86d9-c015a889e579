import{d as u,aq as m,ar as d,k as f,u as t,aD as n,at as s}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as b}from"./useForm-CgkFTrrO.js";import{B as y}from"./BasicForm-DBcXiHk0.js";var v=Object.defineProperty,p=Object.getOwnPropertySymbols,g=Object.prototype.hasOwnProperty,w=Object.prototype.propertyIsEnumerable,c=(o,r,e)=>r in o?v(o,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[r]=e,h=(o,r)=>{for(var e in r||(r={}))g.call(r,e)&&c(o,e,r[e]);if(p)for(var e of p(r))w.call(r,e)&&c(o,e,r[e]);return o};const x={style:{border:"1px solid #cccccc",padding:"8px","background-color":"#f9f9f9","margin-top":"12px"}},F=u({__name:"DebugRunForm",props:{schemas:{type:Array,required:!0}},setup(o,{expose:r}){const e=o,[l,i]=b({schemas:e.schemas,labelCol:{span:24},wrapperCol:{span:24},showActionButtonGroup:!1});return r(h({},i)),(B,a)=>(d(),m("div",x,[f(t(y),{onRegister:t(l)},{unKnown:n(()=>a[0]||(a[0]=[s("div",null,"未知类型",-1)])),picture:n(()=>a[1]||(a[1]=[s("div",null,"【图片类型】暂不支持",-1)])),_:1},8,["onRegister"])]))}});export{F as y};
