import{k as m}from"./antd-vue-vendor-me9YkNVC.js";import{d as p,g as l,e as u,ag as a,aB as d,ar as f,aD as y,at as r,k as g,au as I}from"./vue-vendor-dy9k-Yad.js";import{cs as v,H as s,c as x}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const k=p({name:"DropdownMenuItem",components:{MenuItem:m.Item,Icon:x},props:{itemKey:s.string,text:s.string,icon:s.string},setup(e){const t=l();return{itemKey:u(()=>{var n,o;return e.itemKey||((o=(n=t==null?void 0:t.vnode)==null?void 0:n.props)==null?void 0:o.itemKey)})}}}),C={class:"flex items-center"};function K(e,t,n,o,B,M){const i=a("Icon"),c=a("MenuItem");return f(),d(c,{key:e.itemKey},{default:y(()=>[r("span",C,[g(i,{icon:e.icon,class:"mr-1"},null,8,["icon"]),r("span",null,I(e.text),1)])]),_:1})}const S=v(k,[["render",K]]);export{S as default};
