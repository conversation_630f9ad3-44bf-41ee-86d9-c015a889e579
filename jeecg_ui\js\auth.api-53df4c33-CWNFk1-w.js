import{j as t}from"./index-CCWaWN5g.js";const l=(a,r)=>t.get({url:`/online/cgform/api/authColumn/${a}`,params:r}),e=a=>t.put({url:"/online/cgform/api/authColumn",params:a}),p=a=>t.post({url:"/online/cgform/api/authColumn",params:a}),m=a=>t.put({url:"/online/cgform/api/authColumn/batch",params:a}),i=a=>t.post({url:"/online/cgform/api/authColumn/batch",params:a}),s=(a,r)=>t.get({url:`/online/cgform/api/authButton/${a}`,params:r}),g=a=>t.post({url:"/online/cgform/api/authButton",params:a}),c=(a,r)=>t.put({url:`/online/cgform/api/authButton/${a}`,params:r}),f=(a,r)=>t.get({url:`/online/cgform/api/authData/${a}`,params:r}),h=a=>t.put({url:"/online/cgform/api/authData",params:a}),$=(a,r)=>r?t.put({url:"/online/cgform/api/authData",params:a}):t.post({url:"/online/cgform/api/authData",params:a}),D=(a,r)=>t.delete({url:`/online/cgform/api/authData/${a}`,params:r}),C=(a,r,o)=>{let u=`/online/cgform/api/authPage/${a}/${r}`;return t.get({url:u,params:o})},A=(a,r)=>{let o=`/online/cgform/api/validAuthData/${a}`;return t.get({url:o,params:r})},d=(a,r,o)=>{let u=`/online/cgform/api/authPage/${a}/${r}`;return t.get({url:u,params:o})},B=a=>t.get({url:"/online/cgform/api/roleAuth",params:a}),b=(a,r,o)=>{let u=`/online/cgform/api/roleColumnAuth/${a}/${r}`;return t.post({url:u,params:o})},M=(a,r,o)=>{let u=`/online/cgform/api/roleDataAuth/${a}/${r}`;return t.post({url:u,params:o})},P=(a,r,o)=>{let u=`/online/cgform/api/roleButtonAuth/${a}/${r}`;return t.post({url:u,params:o},{successMessageMode:"none",isTransformResponse:!1})};export{h as $,A,B,b as C,C as D,M as F,P as L,d as b,i as c,$ as d,D as f,c as g,s as h,m as i,l,f as m,g as p,e as r,p as s};
