var $=Object.defineProperty,q=Object.defineProperties;var A=Object.getOwnPropertyDescriptors;var C=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var k=(r,o,t)=>o in r?$(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,S=(r,o)=>{for(var t in o||(o={}))E.call(o,t)&&k(r,t,o[t]);if(C)for(var t of C(o))O.call(o,t)&&k(r,t,o[t]);return r},v=(r,o)=>q(r,A(o));var u=(r,o,t)=>new Promise((f,_)=>{var l=i=>{try{c(t.next(i))}catch(d){_(d)}},g=i=>{try{c(t.throw(i))}catch(d){_(d)}},c=i=>i.done?f(i.value):Promise.resolve(i.value).then(l,g);c((t=t.apply(r,o)).next())});import{d as x,e as P,u as m,p as G,ag as p,aq as H,ar as R,at as L,k as e,aD as n,aB as Q,ah as j,G as b,F as J}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as W}from"./useListPage-Soxgnx9a.js";import"./index-Diw57m_E.js";import X from"./JeecgOrderModal-Dt5o7Blv.js";import Y from"./JeecgOrderCustomerList-C4TPLHmE.js";import Z from"./JeecgOrderTicketList-BE5UkS9z.js";import{m as tt,n as ot,o as et,p as it,q as rt}from"./erplist.api-wW14l8Z-.js";import{ad as nt}from"./index-CCWaWN5g.js";import{Q as at}from"./componentMap-Bkie1n3v.js";import mt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./renderUtils-D7XVOFwj.js";import"./JeecgOrderCustomerModal-CMKzDElh.js";import"./JeecgOrderTicketModal-By3ivJRp.js";const pt=x({name:"tab-list"}),So=x(v(S({},pt),{setup(r){const[o,{openModal:t}]=nt(),{tableContext:f}=W({tableProps:{api:it,tableSetting:{cacheKey:"erp_main"},columns:et,canResize:!1,rowSelection:{type:"radio"},formConfig:{schemas:ot},actionColumn:{width:180},pagination:{current:1,pageSize:5,pageSizeOptions:["5","10","20"]}}}),[_,{reload:l,updateTableDataRecord:g},{rowSelection:c,selectedRowKeys:i}]=f,d=P(()=>m(i).length>0?m(i)[0]:"");G("orderId",d);function D(){t(!0,{isUpdate:!1,showFooter:!0})}function F(a){return u(this,null,function*(){t(!0,{record:a,isUpdate:!0,showFooter:!0})})}function st(a){return u(this,null,function*(){t(!0,{record:a,isUpdate:!0,showFooter:!1})})}function B(a){return u(this,null,function*(){yield rt({id:a.id},l)})}function I(){return u(this,null,function*(){yield tt({ids:i.value},()=>{i.value=[],l()})})}function T(){l()}function K(a){return[{label:"编辑",onClick:F.bind(null,a)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:B.bind(null,a)}}]}return(a,s)=>{const y=p("a-button"),h=p("Icon"),N=p("a-menu-item"),V=p("a-menu"),z=p("a-dropdown"),w=p("a-tab-pane"),M=p("a-tabs");return R(),H(J,null,[L("div",null,[e(m(mt),{onRegister:m(_),rowSelection:m(c)},{tableTitle:n(()=>[e(y,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:D},{default:n(()=>s[0]||(s[0]=[b(" 新增")])),_:1,__:[0]}),m(i).length>0?(R(),Q(z,{key:0},{overlay:n(()=>[e(V,null,{default:n(()=>[e(N,{key:"1",onClick:I},{default:n(()=>[e(h,{icon:"ant-design:delete-outlined"}),s[1]||(s[1]=b(" 删除 "))]),_:1,__:[1]})]),_:1})]),default:n(()=>[e(y,null,{default:n(()=>[s[2]||(s[2]=b("批量操作 ")),e(h,{icon:"mdi:chevron-down"})]),_:1,__:[2]})]),_:1})):j("",!0)]),action:n(({record:U})=>[e(m(at),{actions:K(U)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"]),e(M,{defaultActiveKey:"1",style:{margin:"10px"}},{default:n(()=>[e(w,{tab:"客户信息",key:"1"},{default:n(()=>[e(Y)]),_:1}),e(w,{tab:"机票信息",key:"2",forceRender:""},{default:n(()=>[e(Z)]),_:1})]),_:1})]),e(X,{onRegister:m(o),onSuccess:T},null,8,["onRegister"])],64)}}}));export{So as default};
