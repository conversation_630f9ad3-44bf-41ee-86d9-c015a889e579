import{d as $,ag as p,aq as v,ar as _,as as V,at as y,ah as T,au as C,aA as I,G as w,k as n,f as a,w as k,aD as f}from"./vue-vendor-dy9k-Yad.js";import E from"./ChartGroupCard-BvAE9ozT.js";import L from"./SaleTabCard-BWBo62rR.js";import{L as M}from"./LineMulti-Bb6oGGI0.js";import{a as b,j as S,D as N}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./SingleLine-kPL5Z-yi.js";import"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";import"./Bar-Tb_Evzfu.js";import"./data-BpJ37qIE.js";import"./RankList-gPMCy9y-.js";const R=$({name:"HeadInfo",props:{title:{type:String,default:""},content:{type:String,default:""},bordered:{type:Boolean,default:!1},center:{type:Boolean,default:!0},icon:{type:String,default:!1},iconColor:{type:String,default:"#2b99ff"}}}),z={key:0};function H(o,l,c,s,r,g){const i=p("a-icon");return _(),v("div",{class:V(["head-info",o.center&&"center"])},[y("span",null,C(o.title),1),y("p",{style:I({color:`${o.$props.iconColor}`})},[w(C(o.content)+" ",1),n(i,{type:o.icon,style:I({fontSize:"24px",color:`${o.$props.iconColor}`})},null,8,["type","style"])],4),o.bordered?(_(),v("em",z)):T("",!0)],2)}const m=b(R,[["render",H],["__scopeId","data-v-8d4f1d34"]]);const j=o=>S.get({url:"/sys/loginfo",params:o},{isTransformResponse:!1}),x=o=>S.get({url:"/sys/visitInfo",params:o},{isTransformResponse:!1}),A={class:"p-4"},q={class:"infoArea"},F=$({__name:"IndexChart",setup(o){const l=a(!0),{getThemeColor:c}=N();setTimeout(()=>{l.value=!1},500);const s=a({}),r=a([]);function g(){j(null).then(e=>{e.success&&(Object.keys(e.result).forEach(t=>{e.result[t]=e.result[t]+""}),s.value=e.result)}),x(null).then(e=>{e.success&&(r.value=[],e.result.forEach(t=>{r.value.push({name:t.type,type:"ip",value:t.ip,color:i.value}),r.value.push({name:t.type,type:"visit",value:t.visit,color:d.value})}))})}const i=a(),d=a(),h=a();k(()=>c.value,()=>{h.value=c.value,d.value="#67B962",i.value=c.value,g()},{immediate:!0});function G(){for(var e="0123456789ABCDEF",t="#",u=0;u<6;u++)t+=e[Math.floor(Math.random()*16)];return t}return(e,t)=>{const u=p("a-card"),B=p("a-col"),D=p("a-row");return _(),v("div",A,[n(E,{class:"enter-y",loading:l.value,type:"chart"},null,8,["loading"]),n(L,{class:"!my-4 enter-y",loading:l.value},null,8,["loading"]),n(D,null,{default:f(()=>[n(B,{span:24},{default:f(()=>[n(u,{loading:l.value,bordered:!1,title:"最近一周访问量统计"},{default:f(()=>[y("div",q,[n(m,{title:"今日IP",iconColor:i.value,content:s.value.todayIp,icon:"environment"},null,8,["iconColor","content"]),n(m,{title:"今日访问",iconColor:d.value,content:s.value.todayVisitCount,icon:"team"},null,8,["iconColor","content"]),n(m,{title:"总访问量",iconColor:h.value,content:s.value.totalVisitCount,icon:"rise"},null,8,["iconColor","content"])]),n(M,{chartData:r.value,height:"33vh",type:"line",option:{legend:{top:"bottom"}}},null,8,["chartData"])]),_:1},8,["loading"])]),_:1})]),_:1})])}}}),lo=b(F,[["__scopeId","data-v-d6ebe9bd"]]);export{lo as default};
