import{d as l,l as s,ag as c,aB as m,ar as u,aD as f,at as d,k as o}from"./vue-vendor-dy9k-Yad.js";import{_ as h}from"./index-BtIdS_Qz.js";import{t as g}from"./data-B6s_JPbS.js";import{aO as b,bz as D}from"./antd-vue-vendor-me9YkNVC.js";import{P as _}from"./index-CtJ0w2CP.js";import{a as k}from"./index-CCWaWN5g.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./vxe-table-vendor-B22HppNm.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const C=l({components:{BasicTree:h,PageWrapper:_},setup(){function e(t){}function a(t){return[{label:"新增",handler:()=>{},icon:"bi:plus"},{label:"删除",handler:()=>{},icon:"bx:bxs-folder-open"}]}const i=[{render:t=>s(b,{class:"ml-2",onClick:()=>{}})},{render:()=>s(D)}];function n({level:t}){return t===1?"ion:git-compare-outline":t===2?"ion:home":t===3?"ion:airplane":""}return{treeData:g,actionList:i,getRightMenuList:a,createIcon:n}}}),B={class:"flex"};function L(e,a,i,n,t,R){const r=c("BasicTree"),p=c("PageWrapper");return u(),m(p,{title:"Tree函数操作示例"},{default:f(()=>[d("div",B,[o(r,{class:"w-1/3",title:"右侧操作按钮/自定义图标",helpMessage:"帮助信息",treeData:e.treeData,actionList:e.actionList,renderIcon:e.createIcon},null,8,["treeData","actionList","renderIcon"]),o(r,{class:"w-1/3 mx-4",title:"右键菜单",treeData:e.treeData,beforeRightClick:e.getRightMenuList},null,8,["treeData","beforeRightClick"]),o(r,{class:"w-1/3",title:"工具栏使用",toolbar:"",checkable:"",search:"",treeData:e.treeData,beforeRightClick:e.getRightMenuList},null,8,["treeData","beforeRightClick"])])]),_:1})}const J=k(C,[["render",L]]);export{J as default};
