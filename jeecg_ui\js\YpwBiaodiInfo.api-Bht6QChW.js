import{render as a}from"./renderUtils-D7XVOFwj.js";import{u as r,j as n}from"./index-CCWaWN5g.js";const s=[{title:"详细地址",align:"center",dataIndex:"address"},{title:"评估价格",align:"center",dataIndex:"bdPinggujiage"},{title:"保留价",align:"center",dataIndex:"bdBaoliujia"},{title:"保证金",align:"center",dataIndex:"bdBaozhengjin"},{title:"加价幅度",align:"center",dataIndex:"bdJiajiafudu"},{title:"标的介绍",align:"center",dataIndex:"bd<PERSON><PERSON><PERSON>"},{title:"照片",align:"center",dataIndex:"bdImages"},{title:"数量",align:"center",dataIndex:"bdNum"},{title:"bdD<PERSON><PERSON>",align:"center",dataIndex:"bdDanwei"},{title:"拍卖方式:0总价、1单价",align:"center",dataIndex:"bdPmtype",customRender:({text:e})=>a.renderSwitch(e,[{text:"是",value:"Y"},{text:"否",value:"N"}])},{title:"自由竞价时间",align:"center",dataIndex:"ziyouTime"},{title:"限时竞价时间",align:"center",dataIndex:"xianshiTime"},{title:"bmIds",align:"center",dataIndex:"bmIds"},{title:"佣金",align:"center",dataIndex:"yongjin"}],u=[{label:"保留价",field:"bdBaoliujia",component:"JRangeNumber"},{label:"保证金",field:"bdBaozhengjin",component:"JRangeNumber"},{label:"拍卖方式:0总价、1单价",field:"bdPmtype",component:"JSwitch",componentProps:{query:!0}},{label:"自由竞价时间",field:"ziyouTime",component:"Input"},{label:"限时竞价时间",field:"xianshiTime",component:"Input"}],d=[{label:"详细地址",field:"address",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入详细地址!"}]},{label:"评估价格",field:"bdPinggujiage",component:"InputNumber"},{label:"保留价",field:"bdBaoliujia",component:"InputNumber",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入保留价!"},{pattern:/^-?\d+\.?\d*$/,message:"请输入数字!"}]},{label:"保证金",field:"bdBaozhengjin",component:"InputNumber",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入保证金!"},{pattern:/^-?\d+\.?\d*$/,message:"请输入数字!"}]},{label:"加价幅度",field:"bdJiajiafudu",component:"InputNumber",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入加价幅度!"}]},{label:"标的介绍",field:"bdJieshao",component:"InputTextArea"},{label:"照片",field:"bdImages",component:"InputTextArea"},{label:"数量",field:"bdNum",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入数量!"}]},{label:"bdDanwei",field:"bdDanwei",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入bdDanwei!"}]},{label:"拍卖方式:0总价、1单价",field:"bdPmtype",component:"JSwitch",componentProps:{},dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入拍卖方式:0总价、1单价!"}]},{label:"自由竞价时间",field:"ziyouTime",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入自由竞价时间!"}]},{label:"限时竞价时间",field:"xianshiTime",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入限时竞价时间!"}]},{label:"bmIds",field:"bmIds",component:"InputTextArea"},{label:"佣金",field:"yongjin",component:"Input"},{label:"",field:"id",component:"Input",show:!1}],p={address:{title:"详细地址",order:0,view:"text",type:"string"},bdPinggujiage:{title:"评估价格",order:1,view:"number",type:"number"},bdBaoliujia:{title:"保留价",order:2,view:"number",type:"number"},bdBaozhengjin:{title:"保证金",order:3,view:"number",type:"number"},bdJiajiafudu:{title:"加价幅度",order:4,view:"number",type:"number"},bdJieshao:{title:"标的介绍",order:5,view:"textarea",type:"string"},bdImages:{title:"照片",order:6,view:"textarea",type:"string"},bdNum:{title:"数量",order:7,view:"text",type:"string"},bdDanwei:{title:"bdDanwei",order:8,view:"text",type:"string"},bdPmtype:{title:"拍卖方式:0总价、1单价",order:9,view:"number",type:"number"},ziyouTime:{title:"自由竞价时间",order:10,view:"text",type:"string"},xianshiTime:{title:"限时竞价时间",order:11,view:"text",type:"string"},bmIds:{title:"bmIds",order:12,view:"textarea",type:"string"},yongjin:{title:"佣金",order:13,view:"text",type:"string"}};function c(e){return d}const{createConfirm:l}=r();const b="/ypw/ypwBiaodiInfo/exportXls",y="/ypw/ypwBiaodiInfo/importExcel",g=e=>n.get({url:"/ypw/ypwBiaodiInfo/list",params:e}),w=(e,t)=>n.delete({url:"/ypw/ypwBiaodiInfo/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),I=(e,t)=>{l({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>n.delete({url:"/ypw/ypwBiaodiInfo/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},f=(e,t)=>{let i=t?"/ypw/ypwBiaodiInfo/edit":"/ypw/ypwBiaodiInfo/add";return n.post({url:i,params:e})};export{b as a,I as b,u as c,s as d,w as e,f,y as g,d as h,c as i,g as l,p as s};
