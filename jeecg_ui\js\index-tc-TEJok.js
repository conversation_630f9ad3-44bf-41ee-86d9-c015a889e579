import{d as l,ag as n,aB as c,ar as g,aD as o,k as a,G as r}from"./vue-vendor-dy9k-Yad.js";import{P as f}from"./index-CtJ0w2CP.js";import{C as u}from"./index-LCGLvkB3.js";import{$ as p}from"./print-CZHAHgn6.js";import{a as _}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";const d=l({name:"AppLogo",components:{PageWrapper:f,CollapseContainer:u},setup(){function e(){p({printable:[{name:"ll",email:"<EMAIL>",phone:"123"},{name:"qq",email:"<EMAIL>",phone:"456"}],properties:["name","email","phone"],type:"json"})}function t(){p({printable:["https://anncwb.github.io/anncwb/images/preview1.png","https://anncwb.github.io/anncwb/images/preview2.png"],type:"image",header:"Multiple Images",imageStyle:"width:100%;"})}return{jsonPrint:e,imagePrint:t}}});function C(e,t,b,P,w,h){const i=n("a-button"),m=n("CollapseContainer"),s=n("PageWrapper");return g(),c(s,{title:"打印示例"},{default:o(()=>[a(m,{title:"json打印表格"},{default:o(()=>[a(i,{type:"primary",onClick:e.jsonPrint},{default:o(()=>t[0]||(t[0]=[r("打印")])),_:1,__:[0]},8,["onClick"])]),_:1}),a(i,{type:"primary",class:"mt-5",onClick:e.imagePrint},{default:o(()=>t[1]||(t[1]=[r("Image Print")])),_:1,__:[1]},8,["onClick"])]),_:1})}const L=_(d,[["render",C]]);export{L as default};
