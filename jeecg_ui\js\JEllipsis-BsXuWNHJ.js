import{d as x,e as d,ag as b,aB as C,ar as _,aD as h,G as k,au as y,at as B}from"./vue-vendor-dy9k-Yad.js";import{H as i}from"./index-CCWaWN5g.js";import{a as p}from"./areaDataUtil-BXVjRArW.js";const V=x({__name:"JElli<PERSON>",props:{value:i.oneOfType([i.string,i.number,i.array]),length:i.number.def(25),isAreaCode:i.bool.def(!1)},setup(v){const t=v;function m(e){if(!e||!Array.isArray(e)||e.length===0)return[];const r=[];for(let a=0;a<e.length;a++){const u=e[a];let c=!1;if(a===0){for(const o of p)if(o.value===u){r.push(o.label),c=!0;break}}else if(a===1){const o=e[0],l=p.find(n=>n.value===o);if(l&&l.children){for(const n of l.children)if(n.value===u){r.push(n.label),c=!0;break}}}else if(a===2){const o=e[0],l=e[1],n=p.find(s=>s.value===o);if(n&&n.children){const s=n.children.find(f=>f.value===l);if(s&&s.children){for(const f of s.children)if(f.value===u){r.push(f.label),c=!0;break}}}}c||r.push(u)}return r}const g=d(()=>t.value?t.isAreaCode&&Array.isArray(t.value)?m(t.value).join(" / "):Array.isArray(t.value)?t.value.join(", "):t.value:t.value),A=d(()=>{const e=g.value;if(!e)return e;const r=String(e);return r.length>t.length?r.slice(0,t.length)+"...":r});return(e,r)=>{const a=b("a-tooltip");return _(),C(a,{placement:"topLeft"},{title:h(()=>[B("span",null,y(v.value),1)]),default:h(()=>[k(" "+y(A.value),1)]),_:1})}}});export{V as _};
