import{d,ag as p,aq as c,ar as u,k as a,aD as f}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{d as h}from"./table-BDFKJhHv.js";import{useListPage as b}from"./useListPage-Soxgnx9a.js";import{Q as w}from"./componentMap-Bkie1n3v.js";import C from"./BasicTable-xCEZpGLb.js";import{a as _}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const T=[{title:"编号",dataIndex:"no",width:100},{title:"姓名",dataIndex:"name",auth:"demo:field:show"},{title:"状态",dataIndex:"status"},{title:"地址",dataIndex:"address",auth:"super",ifShow:t=>!0},{title:"开始时间",dataIndex:"beginTime"},{title:"结束时间",dataIndex:"endTime",width:200}],A=d({components:{BasicTable:C,TableAction:w},setup(){const{tableContext:t}=b({tableProps:{title:"权限列",api:h,columns:T,bordered:!0,useSearchForm:!1,actionColumn:{width:250,title:"Action",dataIndex:"action",slots:{customRender:"action"}}}}),[e]=t;function n(i){}function r(i){}function m(i){}return{registerTable:e,handleEdit:n,handleDelete:r,handleOpen:m}}}),g={class:"p-4"};function I(t,e,n,r,m,i){const l=p("TableAction"),s=p("BasicTable");return u(),c("div",g,[a(s,{onRegister:t.registerTable},{action:f(({record:o})=>[a(l,{actions:[{label:"编辑",onClick:t.handleEdit.bind(null,o),auth:"demo:btn:show"},{label:"删除",icon:"ic:outline-delete-outline",onClick:t.handleDelete.bind(null,o),auth:"super"}],dropDownActions:[{label:"启用",popConfirm:{title:"是否启用？",confirm:t.handleOpen.bind(null,o)},ifShow:x=>o.status!=="enable"},{label:"禁用",popConfirm:{title:"是否禁用？",confirm:t.handleOpen.bind(null,o)},ifShow:()=>o.status==="enable"},{label:"同时控制",popConfirm:{title:"是否动态显示？",confirm:t.handleOpen.bind(null,o)},auth:"super",ifShow:()=>!0}]},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister"])])}const $t=_(A,[["render",I]]);export{$t as default};
