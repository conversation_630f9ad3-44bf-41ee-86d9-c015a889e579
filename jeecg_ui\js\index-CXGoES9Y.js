import{ag as s,aq as r,ar as a,at as n,k as l,aD as i,G as f}from"./vue-vendor-dy9k-Yad.js";import{a as c}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const m={},d={class:"fixed h-full w-full flex flex-col justify-center items-center text-4xl"};function p(e,t){const o=s("a-button");return a(),r("div",d,[t[2]||(t[2]=n("div",{class:""}," 位于主框架外的页面 ",-1)),l(o,{onClick:t[0]||(t[0]=u=>e.$router.go(-1)),class:"mt-10",type:"primary"},{default:i(()=>t[1]||(t[1]=[f("Back")])),_:1,__:[1]})])}const B=c(m,[["render",p]]);export{B as default};
