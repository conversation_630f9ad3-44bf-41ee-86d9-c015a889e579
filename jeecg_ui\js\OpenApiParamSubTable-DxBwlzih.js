import{d as s,f as p,h as l,aq as u,ar as d,k as c,aD as f,u as e}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{e as v}from"./OpenApi.data-CeXef7sc.js";import{f as g}from"./OpenApi.api-DrT-pxFg.js";import x from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const zo=s({__name:"OpenApiParamSubTable",props:{id:{type:String,default:""}},setup(a){const i=a,t=p(!1),r=p([]);l(()=>{i.id&&n(i.id)});function n(m){r.value=[],t.value=!0,g({id:m}).then(o=>{o.success&&(r.value=o.result.records)}).finally(()=>{t.value=!1})}return(m,o)=>(d(),u("div",null,[c(e(x),{bordered:"",size:"middle",loading:t.value,rowKey:"id",canResize:!1,columns:e(v),dataSource:r.value,pagination:!1},{bodyCell:f(({column:S,record:_,index:b,text:y})=>o[0]||(o[0]=[])),_:1},8,["loading","columns","dataSource"])]))}});export{zo as default};
