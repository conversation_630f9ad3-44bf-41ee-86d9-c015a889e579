import{d as I,f,e as g,u as t,w as Q,aq as w,ah as y,ar as B,F as L,k as o,at as T,aD as p,G as _,au as v}from"./vue-vendor-dy9k-Yad.js";import b from"./LoginFormTitle-Bms3O5Qx.js";import{V as q,B as N}from"./antd-vue-vendor-me9YkNVC.js";import{Q as V}from"./index-C1YxH9KC.js";import{ah as D,bO as E,bU as F,N as U,b$ as j,c0 as O}from"./index-CCWaWN5g.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./vxe-table-vendor-B22HppNm.js";const $={class:"enter-x min-w-64 min-h-64"},W=I({__name:"QrCodeForm",setup(z){const c=f("");let a;const{t:n}=U(),h=D(),{handleBackLogin:k,getLoginState:C}=E(),s=f("0"),i=g(()=>t(C)===F.QR_CODE),x=g(()=>t(s)==="0"?n("sys.login.scanSign"):n("sys.login.scanSuccess"));function l(){s.value="0",j().then(e=>{c.value=e.qrcodeId,e.qrcodeId&&S(e.qrcodeId)})}function u(e){O({qrcodeId:e}).then(r=>{let d=r.token;d=="-2"&&(l(),clearInterval(a)),r.success&&(s.value="2",clearInterval(a),setTimeout(()=>{h.qrCodeLogin(d)},500))})}function S(e){u(e),m(),a=setInterval(()=>{u(e)},1500)}function m(){a&&clearInterval(a)}return Q(i,e=>{e?l():m()}),(e,r)=>i.value?(B(),w(L,{key:0},[o(b,{class:"enter-x"}),T("div",$,[o(t(V),{value:c.value,class:"enter-x flex justify-center xl:justify-start",width:280},null,8,["value"]),o(t(q),{class:"enter-x"},{default:p(()=>[_(v(x.value),1)]),_:1}),o(t(N),{size:"large",block:"",class:"mt-4 enter-x",onClick:t(k)},{default:p(()=>[_(v(t(n)("sys.login.backSignIn")),1)]),_:1},8,["onClick"])])],64)):y("",!0)}});export{W as default};
