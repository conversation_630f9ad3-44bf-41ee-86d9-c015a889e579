var f=(o,l,r)=>new Promise((c,s)=>{var d=e=>{try{t(r.next(e))}catch(i){s(i)}},a=e=>{try{t(r.throw(e))}catch(i){s(i)}},t=e=>e.done?c(e.value):Promise.resolve(e.value).then(d,a);t((r=r.apply(o,l)).next())});import{d as v,e as g,ag as u,aB as F,ar as $,aE as y,aD as h,at as m,as as n,k,au as _,G as S}from"./vue-vendor-dy9k-Yad.js";import{F as I,ah as L,ac as M,N as b,a as N}from"./index-CCWaWN5g.js";import{B as R}from"./index-Diw57m_E.js";import{B as P}from"./BasicForm-DBcXiHk0.js";import{u as D}from"./useForm-CgkFTrrO.js";import{u as U}from"./lock-CRalNsZJ.js";import{h as V}from"./header-OZa5fSDc.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const A=v({name:"LockModal",components:{BasicModal:R,BasicForm:P},setup(){const{t:o}=b(),{prefixCls:l}=I("header-lock-modal"),r=L(),c=U(),s=g(()=>{var p;return(p=r.getUserInfo)==null?void 0:p.realname}),[d,{closeModal:a}]=M(),[t,{validateFields:e,resetFields:i}]=D({labelWidth:74,labelAlign:"left",wrapperCol:{},showActionButtonGroup:!1,schemas:[{field:"password",label:o("layout.header.lockScreenPassword"),component:"InputPassword",componentProps:{autocomplete:"new-password"}}]});function B(){return f(this,null,function*(){const w=(yield e()).password;a(),c.setLockInfo({isLock:!0,pwd:w}),yield i()})}const C=g(()=>{const{avatar:p}=r.getUserInfo;return p||V});return{t:o,prefixCls:l,getRealName:s,register:d,registerForm:t,handleLock:B,avatar:C}}}),G=["src"];function z(o,l,r,c,s,d){const a=u("BasicForm"),t=u("a-button"),e=u("BasicModal");return $(),F(e,y({footer:null,title:o.t("layout.header.lockScreen")},o.$attrs,{class:o.prefixCls,onRegister:o.register,canFullscreen:!1}),{default:h(()=>[m("div",{class:n(`${o.prefixCls}__entry`)},[m("div",{class:n(`${o.prefixCls}__header`)},[m("img",{src:o.avatar,class:n(`${o.prefixCls}__header-img`)},null,10,G),m("p",{class:n(`${o.prefixCls}__header-name`)},_(o.getRealName),3)],2),k(a,{onRegister:o.registerForm},null,8,["onRegister"]),m("div",{class:n(`${o.prefixCls}__footer`)},[k(t,{type:"primary",block:"",class:"mt-2",onClick:o.handleLock},{default:h(()=>[S(_(o.t("layout.header.lockScreenBtn")),1)]),_:1},8,["onClick"])],2)],2)]),_:1},16,["title","class","onRegister"])}const Vo=N(A,[["render",z]]);export{Vo as default};
