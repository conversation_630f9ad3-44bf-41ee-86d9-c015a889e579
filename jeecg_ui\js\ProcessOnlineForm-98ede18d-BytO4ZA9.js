import S from"./OnlineForm-58282699-DaLYL1I2.js";import{d as N,f as n,w as T,n as x,ag as f,aq as v,ar as y,k as h,aD as g,ah as w,G as B}from"./vue-vendor-dy9k-Yad.js";import{cs as L,j as P}from"./index-CCWaWN5g.js";import{g as b}from"./useExtendComponent-bb98e568-B7LlULaY.js";import"./index-L3cSIXth.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import"./useCustomHook-acb00837-B7NPzH0H.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./OnlineForm.vue_vue_type_style_index_0_scoped_3f26e7bd_lang-4ed993c7-l0sNRNKZ.js";import"./componentMap-Bkie1n3v.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";var u=(t,e,i)=>new Promise((l,p)=>{var s=o=>{try{a(i.next(o))}catch(r){p(r)}},m=o=>{try{a(i.throw(o))}catch(r){p(r)}},a=o=>o.done?l(o.value):Promise.resolve(o.value).then(s,m);a((i=i.apply(t,e)).next())});const R=N({name:"ProcessOnlineForm",inheritAttrs:!1,components:{OnlineForm:S},props:{dataId:{type:String,default:""},tableName:{type:String,default:""},taskId:{type:String,default:""},disabled:{type:Boolean,default:!1}},setup(t){const e=n(),i=n(""),l=n(1),p=n(!1),s=n(""),m=n(!1);T(()=>t.tableName,c=>{c&&a()},{immediate:!0});function a(){return u(this,null,function*(){m.value=!0;const c=`/online/cgform/api/getFormItemBytbname/${t.tableName}`,C={taskId:t.taskId};try{let d=yield P.get({url:c,params:C});i.value=d.head.id,l.value=Number(d.head.formTemplate||1),p.value=d.head.isTree==="Y",s.value=d.head.treeParentIdField||"",yield x(()=>u(this,null,function*(){(yield b(e)).createRootProperties(d)}))}catch(d){}})}function o(){return u(this,null,function*(){let c=yield b(e);m.value=!1,c.show(!0,{id:t.dataId})})}const r=n(!1);function k(){return u(this,null,function*(){r.value=!0,e.value.handleSubmit()})}function I(){r.value=!1}function F(){r.value=!1}return{onlineFormCompRef:e,formId:i,formTemplate:l,isTreeForm:p,pidFieldName:s,renderSuccess:o,handleSuccess:I,handleClose:F,handleSubmit:k,buttonLoading:r,spinLoading:m}}}),_={class:"cust-onl-form"},V={key:0,style:{width:"100%","text-align":"center","margin-top":"5px"}};function O(t,e,i,l,p,s){const m=f("a-button"),a=f("online-form"),o=f("a-spin");return y(),v("div",_,[h(o,{spinning:t.spinLoading},{default:g(()=>[h(a,{ref:"onlineFormCompRef",id:t.formId,disabled:t.disabled,"form-template":t.formTemplate,isTree:t.isTreeForm,pidField:t.pidFieldName,taskId:t.taskId,onRendered:t.renderSuccess,onSuccess:t.handleSuccess,onClose:t.handleClose},{bottom:g(()=>[!t.disabled&&!t.spinLoading?(y(),v("div",V,[h(m,{preIcon:"ant-design:check",style:{width:"126px"},type:"primary",onClick:t.handleSubmit,loading:t.buttonLoading},{default:g(()=>e[0]||(e[0]=[B(" 提 交 ")])),_:1},8,["onClick","loading"])])):w("",!0)]),_:1},8,["id","disabled","form-template","isTree","pidField","taskId","onRendered","onSuccess","onClose"])]),_:1},8,["spinning"])])}const Wt=L(R,[["render",O],["__scopeId","data-v-a4356798"]]);export{Wt as default};
