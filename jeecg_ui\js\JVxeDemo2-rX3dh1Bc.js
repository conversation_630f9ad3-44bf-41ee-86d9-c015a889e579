import{d as w,f as s,r as S,ag as y,aB as C,ar as v,aD as _,k as T,G as D}from"./vue-vendor-dy9k-Yad.js";import{bx as e,aE as J,$ as P,u as V}from"./index-CCWaWN5g.js";import{q as i}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const R=w({__name:"JVxeDemo2",setup(z){const p=s(),{createMessage:h}=V(),l=s(!1),x=s([{title:"下拉框_字典表搜索",key:"select_dict_search",type:e.selectDictSearch,width:200,async:!0,dict:"sys_user,realname,username",tipsContent:"请输入查询条件"},{title:"JPopup",key:"popup",type:e.popup,width:180,popupCode:"demo",fieldConfig:[{source:"name",target:"popup"},{source:"sex",target:"popup_sex"},{source:"age",target:"popup_age"}]},{title:"JP-性别",key:"popup_sex",type:e.select,dictCode:"sex",disabled:!0,width:100},{title:"JP-年龄",key:"popup_age",type:e.normal,width:80},{title:"用户选择",key:"userSelect",type:e.userSelect,width:240},{title:"部门选择",key:"departSelect",type:e.departSelect,width:240},{title:"单选",key:"radio",type:e.radio,width:130,options:[{text:"男",value:"1"},{text:"女",value:"2"}],allowClear:!1},{title:"上传",key:"upload",type:e.upload,width:180,btnText:"点击上传",token:!0,responseName:"message",action:J},{title:"图片上传",key:"image",type:e.image,width:180,maxCount:6},{title:"文件上传",key:"file",type:e.file,width:180,maxCount:2},{title:"进度条",key:"progress",type:e.progress,minWidth:320}]),u=s([]),o=S({current:1,pageSize:10,pageSizeOptions:["10","20","30","100","200"],total:1e3});c(o.current,o.pageSize,!0);function k(t){o.current=t.current,o.pageSize=t.pageSize,c(t.current,t.pageSize,!0)}function b(){const t=p.value.getTableData();h.success("获取值成功，请看控制台输出")}function c(t,a,r=!1){r&&(l.value=!0);let n=Date.now(),d=[];for(let m=0;m<a;m++){let f=i(0,2);d.push({id:P(),select_dict_search:["admin","","jeecg"][i(0,2)],progress:i(0,100),radio:f?f.toString():null})}u.value=d;let g=Date.now()-n;r&&g<a&&setTimeout(()=>l.value=!1,a-g)}return(t,a)=>{const r=y("a-button"),n=y("JVxeTable");return v(),C(n,{ref_key:"tableRef",ref:p,toolbar:"","row-number":"","row-selection":"","keep-source":"",height:492,loading:l.value,dataSource:u.value,columns:x.value,pagination:o,style:{"margin-top":"8px"},onPageChange:k},{toolbarSuffix:_(()=>[T(r,{onClick:b},{default:_(()=>a[0]||(a[0]=[D("获取数据")])),_:1,__:[0]})]),_:1},8,["loading","dataSource","columns","pagination"])}}});export{R as default};
