import{d as V,f as B,r as D,e as c,u as i,w as E,k as o,o as M,n as P,ag as S,aB as L,ar as F,aD as f,at as _,G as R,au as T}from"./vue-vendor-dy9k-Yad.js";import{a7 as q,z,a as $,w as A}from"./index-CCWaWN5g.js";import{V as G}from"./antd-vue-vendor-me9YkNVC.js";import{P as U}from"./index-CtJ0w2CP.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const j={height:[Number,String],maxHeight:[Number,String],maxWidth:[Number,String],minHeight:[Number,String],minWidth:[Number,String],width:[Number,String],bench:{type:[Number,String],default:0},itemHeight:{type:[Number,String],required:!0},items:{type:Array,default:()=>[]}},w="virtual-scroll";function a(t,n="px"){if(!(t==null||t===""))return isNaN(+t)?String(t):`${Number(t)}${n}`}const J=V({name:"VirtualScroll",props:j,setup(t,{slots:n}){const m=B(null),s=D({first:0,last:0,scrollTop:0}),p=c(()=>parseInt(t.bench,10)),u=c(()=>parseInt(t.itemHeight,10)),h=c(()=>Math.max(0,s.first-i(p))),g=c(()=>Math.min((t.items||[]).length,s.last+i(p))),v=c(()=>({height:a((t.items||[]).length*i(u))})),d=c(()=>{const e={},r=a(t.height),l=a(t.minHeight),b=a(t.minWidth),x=a(t.maxHeight),y=a(t.maxWidth),W=a(t.width);return r&&(e.height=r),l&&(e.minHeight=l),b&&(e.minWidth=b),x&&(e.maxHeight=x),y&&(e.maxWidth=y),W&&(e.width=W),e});E([()=>t.itemHeight,()=>t.height],()=>{N()});function H(e){const r=i(m);if(!r)return 0;const l=parseInt(t.height||0,10)||r.clientHeight;return e+Math.ceil(l/i(u))}function C(){return Math.floor(s.scrollTop/i(u))}function N(){const e=i(m);e&&(s.scrollTop=e.scrollTop,s.first=C(),s.last=H(s.first))}function I(){const{items:e=[]}=t;return e.slice(i(h),i(g)).map(k)}function k(e,r){r+=i(h);const l=a(r*i(u));return o("div",{class:`${w}__item`,style:{top:l},key:r},[q(n,"default",{index:r,item:e})])}return M(()=>{s.last=H(0),P(()=>{const e=i(m);e&&z({el:e,name:"scroll",listener:N,wait:0})})}),()=>o("div",{class:w,style:i(d),ref:m},[o("div",{class:`${w}__container`,style:i(v)},[I()])])}}),K=$(J,[["__scopeId","data-v-33845336"]]),O=A(K),Q=(()=>{const t=[];for(let n=1;n<2e4;n++)t.push({title:"列表项"+n});return t})(),X=V({components:{VScroll:O,Divider:G,PageWrapper:U},setup(){return{data:Q}}}),Y={class:"virtual-scroll-demo-wrap"},Z={class:"virtual-scroll-demo__item"},tt={class:"virtual-scroll-demo-wrap"},et={class:"virtual-scroll-demo__item"};function it(t,n,m,s,p,u){const h=S("Divider"),g=S("VScroll"),v=S("PageWrapper");return F(),L(v,{class:"virtual-scroll-demo"},{default:f(()=>[o(h,null,{default:f(()=>n[0]||(n[0]=[R("基础滚动示例")])),_:1,__:[0]}),_("div",Y,[o(g,{itemHeight:41,items:t.data,height:300,width:300},{default:f(({item:d})=>[_("div",Z,T(d.title),1)]),_:1},8,["items"])]),o(h,null,{default:f(()=>n[1]||(n[1]=[R("即使不可见，也预先加载50条数据，防止空白")])),_:1,__:[1]}),_("div",tt,[o(g,{itemHeight:41,items:t.data,height:300,width:300,bench:50},{default:f(({item:d})=>[_("div",et,T(d.title),1)]),_:1},8,["items"])])]),_:1})}const dt=$(X,[["render",it],["__scopeId","data-v-caa40fb6"]]);export{dt as default};
