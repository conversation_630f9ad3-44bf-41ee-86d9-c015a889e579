import{d as n,ag as e,aB as c,ar as r,aD as a,at as o,k as i,aq as l,F as m,aC as _,au as d}from"./vue-vendor-dy9k-Yad.js";import{S as f}from"./index-LCGLvkB3.js";import{P as u}from"./index-CtJ0w2CP.js";import{a as C}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const g=n({components:{ScrollContainer:f,PageWrapper:u}}),k={class:"scroll-wrap"},x={class:"p-3"};function B(S,h,P,$,b,v){const s=e("ScrollContainer"),p=e("PageWrapper");return r(),c(p,{title:"滚动组件示例",content:"基于el-scrollbar"},{default:a(()=>[o("div",k,[i(s,{class:"mt-4"},{default:a(()=>[o("ul",x,[(r(),l(m,null,_(100,t=>o("li",{key:t,class:"p-2",style:{border:"1px solid #eee"}},d(t),1)),64))])]),_:1})])]),_:1})}const J=C(g,[["render",B],["__scopeId","data-v-8c8db749"]]);export{J as default};
