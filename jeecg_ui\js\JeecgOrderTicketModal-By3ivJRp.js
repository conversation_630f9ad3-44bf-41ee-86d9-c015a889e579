var w=Object.defineProperty;var f=Object.getOwnPropertySymbols;var v=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable;var g=(r,o,t)=>o in r?w(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,h=(r,o)=>{for(var t in o||(o={}))v.call(o,t)&&g(r,t,o[t]);if(f)for(var t of f(o))F.call(o,t)&&g(r,t,o[t]);return r};var u=(r,o,t)=>new Promise((n,p)=>{var c=i=>{try{s(t.next(i))}catch(a){p(a)}},l=i=>{try{s(t.throw(i))}catch(a){p(a)}},s=i=>i.done?n(i.value):Promise.resolve(i.value).then(c,l);s((t=t.apply(r,o)).next())});import{d as M,c as y,f as I,e as R,u as m,aB as x,ar as L,aD as O,k as T,aE as U}from"./vue-vendor-dy9k-Yad.js";import{B as b}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{i as j,j as C}from"./erplist.api-wW14l8Z-.js";import{u as H}from"./useForm-CgkFTrrO.js";import{ac as P}from"./index-CCWaWN5g.js";import{B as S}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./renderUtils-D7XVOFwj.js";const Gt=M({__name:"JeecgOrderTicketModal",emits:["success","register"],setup(r,{emit:o}){const t=y("orderId"),n=o,p=I(!0),[c,{resetFields:l,setFieldsValue:s,validate:i}]=H({labelWidth:150,schemas:j,showActionButtonGroup:!1}),[a,{setModalProps:d,closeModal:B}]=P(e=>u(null,null,function*(){yield l(),d({confirmLoading:!1}),p.value=!!(e!=null&&e.isUpdate),m(p)&&(yield s(h({},e.record)))})),_=R(()=>m(p)?"编辑":"新增");function k(){return u(this,null,function*(){try{const e=yield i();d({confirmLoading:!0}),m(t)&&(e.orderId=m(t)),yield C(e,p.value),B(),n("success")}finally{d({confirmLoading:!1})}})}return(e,V)=>(L(),x(m(b),U(e.$attrs,{onRegister:m(a),title:_.value,onOk:k,width:500,minHeight:20,maxHeight:20}),{default:O(()=>[T(m(S),{onRegister:m(c)},null,8,["onRegister"])]),_:1},16,["onRegister","title"]))}});export{Gt as default};
