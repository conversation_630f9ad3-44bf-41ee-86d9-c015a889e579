var O=(D,S,b)=>new Promise((y,T)=>{var N=a=>{try{_(b.next(a))}catch(c){T(c)}},k=a=>{try{_(b.throw(a))}catch(c){T(c)}},_=a=>a.done?y(a.value):Promise.resolve(a.value).then(N,k);_((b=b.apply(D,S)).next())});import{d as W,c as z,z as Z,f as V,w as U,e as h,ag as f,aq as m,ar as v,k as t,u as q,aD as l,at as o,ah as g,F as ee,aC as ae,aB as te,G as i,au as M}from"./vue-vendor-dy9k-Yad.js";import{bB as le,i as E}from"./antd-vue-vendor-me9YkNVC.js";import{i as Y}from"./JAreaSelect-Db7Nhhc_.js";import{_ as L}from"./JUpload-CRos0F1P.js";import{a as se}from"./index-CCWaWN5g.js";import"./useFormItem-CHvpjy4o.js";import"./areaDataUtil-BXVjRArW.js";import"./vxe-table-vendor-B22HppNm.js";const ie={class:"step1"},oe={class:"form-section"},re={class:"form-row service-type-row"},ne={key:0,class:"edit-mode-tip"},ue={key:0,class:"form-row"},de={key:0,class:"form-section"},ve={class:"section-content"},me={class:"form-row asset-row"},fe={class:"form-row asset-row"},pe={class:"form-row asset-row"},ce={key:1,class:"form-section"},ge={class:"section-content"},_e={class:"form-row asset-detail-row"},be={class:"form-row asset-detail-row"},ye={class:"form-row asset-detail-row"},Ie={key:2,class:"form-section"},qe={class:"section-title"},Te={key:0,class:"form-row"},we={class:"location-selects"},xe={key:1,class:"form-row location-row"},Ue={class:"location-selects"},De={class:"detail-address"},Se={key:3,class:"form-section"},Ne={key:0},ke={class:"form-row"},ze={class:"upload-container"},Le={key:1},Ce={class:"form-row"},Oe={class:"upload-container"},Ve={class:"form-row"},Me={class:"upload-container"},Ee={key:4,class:"form-section"},Ye={class:"form-row"},Fe=W({__name:"Step1",props:{modelValue:{},locationLoading:{type:Boolean,default:!1},isEditMode:{type:Boolean,default:!1}},emits:["update:modelValue","area-change","get-current-location","service-type-change"],setup(D,{expose:S,emit:b}){const y=D,T=b,N=z("entrustOrderList",[]),k=z("entrustOrderLoading",!1),_=z("entrustDetailLoading",!1),a=Z(y,"modelValue"),c=V(null),F=()=>{a.value.entrustInfo.title||(a.value.entrustInfo.title="灰谷科技有限公司"),a.value.entrustInfo.titleValue||(a.value.entrustInfo.titleValue="1003")},P=()=>{a.value.basicInfo.disposalStartTime&&a.value.basicInfo.disposalEndTime?c.value=[E(a.value.basicInfo.disposalStartTime),E(a.value.basicInfo.disposalEndTime)]:c.value=null};U(()=>[a.value.basicInfo.disposalStartTime,a.value.basicInfo.disposalEndTime],()=>{P()},{immediate:!0}),U(()=>a.value,()=>{F()},{immediate:!0,deep:!0}),U(()=>a.value.serviceType,(u,e)=>{});const B=u=>{u?(a.value.basicInfo.disposalStartTime=u[0].format("YYYY-MM-DD HH:mm:ss"),a.value.basicInfo.disposalEndTime=u[1].format("YYYY-MM-DD HH:mm:ss")):(a.value.basicInfo.disposalStartTime="",a.value.basicInfo.disposalEndTime="")},R=()=>{T("get-current-location")},X=u=>{T("service-type-change",u)},H=(u,e)=>e.label.toLowerCase().indexOf(u.toLowerCase())>=0,A=u=>{const e=[],r=[];if(u.forEach(d=>{const n={fileName:d.fileName,filePath:d.filePath,fileSize:d.fileSize,fileType:d.fileType};d.fileType==="image"?e.push(n):r.push(n)}),e.length>0){const d=JSON.stringify(e);a.value.materials.images=d,a.value.other.images=d}if(r.length>0){const d=JSON.stringify(r);a.value.materials.attachments=d,a.value.other.attachments=d}};U(()=>a.value.hgyAttachmentList,u=>{u&&Array.isArray(u)&&u.length>0&&A(u)},{immediate:!0,deep:!0});const J=h(()=>{const u={serviceType:[{required:!0,message:"请选择服务类型",trigger:"change"}],entrustOrderId:[{required:!0,message:"请选择关联委托单号",trigger:"change"}],location:{province:[{required:!0,message:"请选择省份",trigger:"change"}],city:[{required:!0,message:"请选择城市",trigger:"change"}],area:[{required:!0,message:"请选择区域",trigger:"change"}],detailAddress:[{required:!0,message:"请输入详细地址",trigger:"blur"}]}};return a.value.serviceType===3&&(u.entrustInfo={noticeName:[{required:!0,message:"请输入公告名称",trigger:"blur"}]}),a.value.serviceType===2&&(u.basicInfo={assetName:[{required:!0,message:"请输入资产名称",trigger:"blur"}],assetType:[{required:!0,message:"请选择资产类型",trigger:"change"}],quantity:[{required:!0,message:"请输入资产数量",trigger:"blur"}],unit:[{required:!0,message:"请选择计量单位",trigger:"change"}],quantityFlag:[{required:!0,message:"请选择是否展示实际数量",trigger:"change"}],serviceLife:[{required:!0,message:"请输入使用年限",trigger:"blur"}],depreciationDegree:[{required:!0,message:"请选择新旧程度",trigger:"change"}],currentStatus:[{required:!0,message:"请选择当前状态",trigger:"change"}],appraisalValue:[{required:!0,message:"请输入评估价值",trigger:"blur"}],disposalPrice:[{required:!0,message:"请输入处置底价",trigger:"blur"}],disposalStartTime:[{required:!0,message:"请选择处置开始时间",trigger:"change"}],disposalEndTime:[{required:!0,message:"请选择处置结束时间",trigger:"change"}],paymentMethod:[{required:!0,message:"请选择付款方式",trigger:"change"}],isTaxIncluded:[{required:!0,message:"请选择是否含税",trigger:"change"}]}),u}),C=V();return S({validateForm:()=>O(null,null,function*(){var u;try{return yield new Promise(e=>setTimeout(e,100)),yield(u=C.value)==null?void 0:u.validate(),!0}catch(e){const r=document.querySelector(".ant-form-item-has-error");return r&&r.scrollIntoView({behavior:"smooth",block:"center"}),!1}})}),(u,e)=>{const r=f("a-select-option"),d=f("a-select"),n=f("a-form-item"),I=f("a-input"),p=f("a-radio"),w=f("a-radio-group"),x=f("a-input-number"),G=f("a-range-picker"),j=f("a-button"),$=f("a-textarea"),K=f("a-form"),Q=f("a-spin");return v(),m("div",ie,[t(Q,{spinning:q(_),tip:"正在加载委托详情..."},{default:l(()=>[t(K,{model:a.value,rules:J.value,ref_key:"formRef",ref:C,"scroll-to-first-error":!0},{default:l(()=>[o("div",oe,[e[34]||(e[34]=o("h3",{class:"section-title"},"服务类型",-1)),o("div",re,[t(n,{label:"关联委托单号",name:"entrustOrderId",required:"",class:"entrust-order-item"},{default:l(()=>[t(d,{value:a.value.entrustOrderId,"onUpdate:value":e[0]||(e[0]=s=>a.value.entrustOrderId=s),placeholder:"请选择关联委托单号",size:"large",class:"entrust-order-select",loading:q(k)||q(_),"show-search":"","filter-option":H,disabled:q(_)},{default:l(()=>[(v(!0),m(ee,null,ae(q(N),s=>(v(),te(r,{key:s,value:s},{default:l(()=>[i(M(s),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","loading","disabled"])]),_:1}),t(n,{label:"服务类型",name:"serviceType",required:"",class:"service-type-item"},{default:l(()=>[t(d,{value:a.value.serviceType,"onUpdate:value":e[1]||(e[1]=s=>a.value.serviceType=s),onChange:X,placeholder:"请选择服务类型",size:"large",class:"service-type-select",disabled:y.isEditMode},{default:l(()=>[t(r,{value:2},{default:l(()=>e[30]||(e[30]=[i("发布资产处置")])),_:1,__:[30]}),t(r,{value:3},{default:l(()=>e[31]||(e[31]=[i("发布采购信息")])),_:1,__:[31]})]),_:1},8,["value","disabled"]),y.isEditMode?(v(),m("div",ne,e[32]||(e[32]=[o("span",{class:"tip-text"},"编辑模式下不允许修改服务类型",-1)]))):g("",!0)]),_:1}),e[33]||(e[33]=o("div",{class:"entrust-placeholder"},null,-1))]),a.value.serviceType===3?(v(),m("div",ue,[t(n,{label:"公告名称",name:["entrustInfo","noticeName"],required:"",class:"full-width-item"},{default:l(()=>[t(I,{value:a.value.entrustInfo.noticeName,"onUpdate:value":e[2]||(e[2]=s=>a.value.entrustInfo.noticeName=s),placeholder:"请输入公告名称",size:"large"},null,8,["value"])]),_:1})])):g("",!0)]),a.value.serviceType===2?(v(),m("div",de,[e[48]||(e[48]=o("div",{class:"section-title"},"基本信息",-1)),o("div",ve,[o("div",me,[t(n,{label:"处置单位",name:["entrustInfo","title"],required:"",class:"asset-item"},{default:l(()=>[t(I,{value:a.value.entrustInfo.title,"onUpdate:value":e[3]||(e[3]=s=>a.value.entrustInfo.title=s),placeholder:"处置单位",size:"large",disabled:"",readonly:""},null,8,["value"])]),_:1}),t(n,{label:"资产名称",name:["basicInfo","assetName"],required:"",class:"asset-item"},{default:l(()=>[t(I,{value:a.value.basicInfo.assetName,"onUpdate:value":e[4]||(e[4]=s=>a.value.basicInfo.assetName=s),placeholder:"请输入资产名称",size:"large"},null,8,["value"])]),_:1}),t(n,{label:"资产编号",name:["basicInfo","assetNo"],class:"asset-item"},{default:l(()=>[t(I,{value:a.value.basicInfo.assetNo,"onUpdate:value":e[5]||(e[5]=s=>a.value.basicInfo.assetNo=s),placeholder:"资产编号由系统生成",size:"large",disabled:"",readonly:""},null,8,["value"])]),_:1})]),o("div",fe,[t(n,{label:"资产类型",name:["basicInfo","assetType"],required:"",class:"asset-item"},{default:l(()=>[t(d,{value:a.value.basicInfo.assetType,"onUpdate:value":e[6]||(e[6]=s=>a.value.basicInfo.assetType=s),placeholder:"请选择资产类型",size:"large"},{default:l(()=>[t(r,{value:1},{default:l(()=>e[35]||(e[35]=[i("设备类")])),_:1,__:[35]}),t(r,{value:2},{default:l(()=>e[36]||(e[36]=[i("车辆类")])),_:1,__:[36]}),t(r,{value:3},{default:l(()=>e[37]||(e[37]=[i("房产类")])),_:1,__:[37]}),t(r,{value:4},{default:l(()=>e[38]||(e[38]=[i("其他")])),_:1,__:[38]})]),_:1},8,["value"])]),_:1}),t(n,{label:"资产数量",name:["basicInfo","quantity"],required:"",class:"asset-item"},{default:l(()=>[t(I,{value:a.value.basicInfo.quantity,"onUpdate:value":e[7]||(e[7]=s=>a.value.basicInfo.quantity=s),placeholder:"请输入资产数量",size:"large"},null,8,["value"])]),_:1}),t(n,{label:"计量单位",name:["basicInfo","unit"],required:"",class:"asset-item"},{default:l(()=>[t(d,{value:a.value.basicInfo.unit,"onUpdate:value":e[8]||(e[8]=s=>a.value.basicInfo.unit=s),placeholder:"请选择计量单位",size:"large"},{default:l(()=>[t(r,{value:"台"},{default:l(()=>e[39]||(e[39]=[i("台")])),_:1,__:[39]}),t(r,{value:"辆"},{default:l(()=>e[40]||(e[40]=[i("辆")])),_:1,__:[40]}),t(r,{value:"套"},{default:l(()=>e[41]||(e[41]=[i("套")])),_:1,__:[41]}),t(r,{value:"个"},{default:l(()=>e[42]||(e[42]=[i("个")])),_:1,__:[42]}),t(r,{value:"件"},{default:l(()=>e[43]||(e[43]=[i("件")])),_:1,__:[43]}),t(r,{value:"批"},{default:l(()=>e[44]||(e[44]=[i("批")])),_:1,__:[44]}),t(r,{value:"米"},{default:l(()=>e[45]||(e[45]=[i("米")])),_:1,__:[45]})]),_:1},8,["value"])]),_:1})]),o("div",pe,[t(n,{label:"是否展示实际数量",name:["basicInfo","quantityFlag"],required:"",class:"asset-detail-item"},{default:l(()=>[t(w,{value:a.value.basicInfo.quantityFlag,"onUpdate:value":e[9]||(e[9]=s=>a.value.basicInfo.quantityFlag=s)},{default:l(()=>[t(p,{value:1},{default:l(()=>e[46]||(e[46]=[i("是")])),_:1,__:[46]}),t(p,{value:0},{default:l(()=>e[47]||(e[47]=[i("否")])),_:1,__:[47]})]),_:1},8,["value"])]),_:1})])])])):g("",!0),a.value.serviceType===2?(v(),m("div",ce,[e[65]||(e[65]=o("div",{class:"section-title"},"资产详情",-1)),o("div",ge,[o("div",_e,[t(n,{label:"使用年限",name:["basicInfo","serviceLife"],required:"",class:"asset-detail-item"},{default:l(()=>[t(x,{value:a.value.basicInfo.serviceLife,"onUpdate:value":e[10]||(e[10]=s=>a.value.basicInfo.serviceLife=s),placeholder:"请输入使用年限",size:"large",style:{width:"100%"},min:0,"addon-after":"年"},null,8,["value"])]),_:1}),t(n,{label:"新旧程度",name:["basicInfo","depreciationDegree"],required:"",class:"asset-detail-item"},{default:l(()=>[t(d,{value:a.value.basicInfo.depreciationDegree,"onUpdate:value":e[11]||(e[11]=s=>a.value.basicInfo.depreciationDegree=s),placeholder:"请选择新旧程度",size:"large"},{default:l(()=>[t(r,{value:1},{default:l(()=>e[49]||(e[49]=[i("九成新")])),_:1,__:[49]}),t(r,{value:2},{default:l(()=>e[50]||(e[50]=[i("八成新")])),_:1,__:[50]}),t(r,{value:3},{default:l(()=>e[51]||(e[51]=[i("七成新")])),_:1,__:[51]}),t(r,{value:4},{default:l(()=>e[52]||(e[52]=[i("六成新")])),_:1,__:[52]}),t(r,{value:5},{default:l(()=>e[53]||(e[53]=[i("五成新")])),_:1,__:[53]}),t(r,{value:6},{default:l(()=>e[54]||(e[54]=[i("四成新")])),_:1,__:[54]}),t(r,{value:7},{default:l(()=>e[55]||(e[55]=[i("三成新")])),_:1,__:[55]}),t(r,{value:8},{default:l(()=>e[56]||(e[56]=[i("二成新")])),_:1,__:[56]}),t(r,{value:9},{default:l(()=>e[57]||(e[57]=[i("一成新")])),_:1,__:[57]})]),_:1},8,["value"])]),_:1}),t(n,{label:"当前状态",name:["basicInfo","currentStatus"],required:"",class:"asset-detail-item"},{default:l(()=>[t(w,{value:a.value.basicInfo.currentStatus,"onUpdate:value":e[12]||(e[12]=s=>a.value.basicInfo.currentStatus=s)},{default:l(()=>[t(p,{value:1},{default:l(()=>e[58]||(e[58]=[i("在用")])),_:1,__:[58]}),t(p,{value:2},{default:l(()=>e[59]||(e[59]=[i("闲置")])),_:1,__:[59]}),t(p,{value:3},{default:l(()=>e[60]||(e[60]=[i("报废")])),_:1,__:[60]})]),_:1},8,["value"])]),_:1})]),o("div",be,[t(n,{label:"评估价值",name:["basicInfo","appraisalValue"],required:"",class:"asset-detail-item"},{default:l(()=>[t(x,{value:a.value.basicInfo.appraisalValue,"onUpdate:value":e[13]||(e[13]=s=>a.value.basicInfo.appraisalValue=s),placeholder:"请输入评估价值",size:"large",style:{width:"100%"},min:0,precision:2,"addon-after":"元"},null,8,["value"])]),_:1}),t(n,{label:"处置底价",name:["basicInfo","disposalPrice"],required:"",class:"asset-detail-item"},{default:l(()=>[t(x,{value:a.value.basicInfo.disposalPrice,"onUpdate:value":e[14]||(e[14]=s=>a.value.basicInfo.disposalPrice=s),placeholder:"请输入处置底价",size:"large",style:{width:"100%"},min:0,precision:2,"addon-after":"元"},null,8,["value"])]),_:1}),t(n,{label:"处置时间",name:["basicInfo","disposalStartTime"],required:"",class:"asset-detail-item"},{default:l(()=>[t(G,{value:c.value,"onUpdate:value":e[15]||(e[15]=s=>c.value=s),"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:["开始时间","结束时间"],size:"large",style:{width:"100%"},onChange:B},null,8,["value"])]),_:1})]),o("div",ye,[t(n,{label:"付款方式",name:["basicInfo","paymentMethod"],required:"",class:"asset-detail-item"},{default:l(()=>[t(w,{value:a.value.basicInfo.paymentMethod,"onUpdate:value":e[16]||(e[16]=s=>a.value.basicInfo.paymentMethod=s)},{default:l(()=>[t(p,{value:1},{default:l(()=>e[61]||(e[61]=[i("全款")])),_:1,__:[61]}),t(p,{value:2},{default:l(()=>e[62]||(e[62]=[i("分期")])),_:1,__:[62]})]),_:1},8,["value"])]),_:1}),t(n,{label:"是否含税",name:["basicInfo","isTaxIncluded"],required:"",class:"asset-detail-item"},{default:l(()=>[t(w,{value:a.value.basicInfo.isTaxIncluded,"onUpdate:value":e[17]||(e[17]=s=>a.value.basicInfo.isTaxIncluded=s)},{default:l(()=>[t(p,{value:"0"},{default:l(()=>e[63]||(e[63]=[i("否")])),_:1,__:[63]}),t(p,{value:"1"},{default:l(()=>e[64]||(e[64]=[i("是")])),_:1,__:[64]})]),_:1},8,["value"])]),_:1}),t(n,{label:"税点",name:["basicInfo","taxRate"],required:a.value.basicInfo.isTaxIncluded!=="0",class:"asset-detail-item"},{default:l(()=>[t(x,{value:a.value.basicInfo.taxRate,"onUpdate:value":e[18]||(e[18]=s=>a.value.basicInfo.taxRate=s),placeholder:"请输入税点",size:"large",style:{width:"100%"},min:0,max:100,precision:2,"addon-after":"%",disabled:a.value.basicInfo.isTaxIncluded==="0"},null,8,["value","disabled"])]),_:1},8,["required"])])])])):g("",!0),a.value.serviceType!==1?(v(),m("div",Ie,[o("h3",qe,M(a.value.serviceType===3?"所属地区":"存放位置"),1),a.value.serviceType===3?(v(),m("div",Te,[o("div",we,[t(n,{name:["location","province"],class:"location-area-item"},{default:l(()=>[t(Y,{province:a.value.location.province,"onUpdate:province":e[19]||(e[19]=s=>a.value.location.province=s),city:a.value.location.city,"onUpdate:city":e[20]||(e[20]=s=>a.value.location.city=s),area:a.value.location.area,"onUpdate:area":e[21]||(e[21]=s=>a.value.location.area=s),placeholder:"请选择所属地区",level:3},null,8,["province","city","area"])]),_:1})])])):(v(),m("div",xe,[o("div",Ue,[t(n,{name:["location","province"],class:"location-area-item"},{default:l(()=>[t(Y,{province:a.value.location.province,"onUpdate:province":e[22]||(e[22]=s=>a.value.location.province=s),city:a.value.location.city,"onUpdate:city":e[23]||(e[23]=s=>a.value.location.city=s),area:a.value.location.area,"onUpdate:area":e[24]||(e[24]=s=>a.value.location.area=s),placeholder:"请选择存放位置",level:3},null,8,["province","city","area"])]),_:1})]),o("div",De,[t(n,{label:"详细地址",name:["location","detailAddress"],required:"",class:"detail-address-item"},{default:l(()=>[t(I,{value:a.value.location.detailAddress,"onUpdate:value":e[25]||(e[25]=s=>a.value.location.detailAddress=s),placeholder:"请输入详细地址",size:"large"},{suffix:l(()=>[t(j,{type:"text",onClick:R,loading:y.locationLoading,class:"location-btn",size:"small"},{icon:l(()=>[t(q(le))]),_:1},8,["loading"])]),_:1},8,["value"])]),_:1})])]))])):g("",!0),a.value.serviceType!==1?(v(),m("div",Se,[e[69]||(e[69]=o("h3",{class:"section-title"},"资料上传",-1)),a.value.serviceType===3?(v(),m("div",Ne,[o("div",ke,[t(n,{label:"采购附件",name:["materials","attachments"],required:"",class:"upload-item"},{default:l(()=>[o("div",ze,[t(L,{value:a.value.materials.attachments,"onUpdate:value":e[26]||(e[26]=s=>a.value.materials.attachments=s),multiple:!0,"max-count":5,accept:".pdf,.doc,.docx,.xls,.xlsx","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[66]||(e[66]=o("div",{class:"upload-tip"},"支持PDF、DOC、DOCX、XLS、XLSX格式",-1))])]),_:1})])])):a.value.serviceType===2?(v(),m("div",Le,[o("div",Ce,[t(n,{label:"标的图片",name:["materials","images"],class:"upload-item"},{default:l(()=>[o("div",Oe,[t(L,{value:a.value.materials.images,"onUpdate:value":e[27]||(e[27]=s=>a.value.materials.images=s),multiple:!0,"max-count":10,accept:"image/*","list-type":"picture-card","file-type":"image","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[67]||(e[67]=o("div",{class:"upload-tip"},"最多可上传10张图片，支持JPG、PNG格式，单个文件不超过5MB",-1))])]),_:1})]),o("div",Ve,[t(n,{label:"附件上传",name:["materials","attachments"],class:"upload-item"},{default:l(()=>[o("div",Me,[t(L,{value:a.value.materials.attachments,"onUpdate:value":e[28]||(e[28]=s=>a.value.materials.attachments=s),multiple:!0,"max-count":5,accept:".pdf,.doc,.docx,.xls,.xlsx","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[68]||(e[68]=o("div",{class:"upload-tip"},"支持PDF、DOC、DOCX、XLS、XLSX格式",-1))])]),_:1})])])):g("",!0)])):g("",!0),a.value.serviceType===2?(v(),m("div",Ee,[e[70]||(e[70]=o("h3",{class:"section-title"},"特殊说明",-1)),o("div",Ye,[t(n,{label:"特殊说明",name:["materials","specialNote"],class:"upload-item"},{default:l(()=>[t($,{value:a.value.materials.specialNote,"onUpdate:value":e[29]||(e[29]=s=>a.value.materials.specialNote=s),placeholder:"请输入特殊说明（如有）",rows:3,size:"large"},null,8,["value"])]),_:1})])])):g("",!0)]),_:1},8,["model","rules"])]),_:1},8,["spinning"])])}}}),Ke=se(Fe,[["__scopeId","data-v-5c0ed537"]]);export{Ke as default};
