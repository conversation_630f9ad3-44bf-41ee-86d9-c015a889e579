var x=(h,d,c)=>new Promise((f,p)=>{var r=t=>{try{o(c.next(t))}catch(i){p(i)}},n=t=>{try{o(c.throw(t))}catch(i){p(i)}},o=t=>t.done?f(t.value):Promise.resolve(t.value).then(r,n);o((c=c.apply(h,d)).next())});import{d as N,c as P,f as l,ag as k,aq as w,ar as u,k as b,u as s,aD as M,aB as D,F as q,ah as F,n as E}from"./vue-vendor-dy9k-Yad.js";import{u as I,a as L}from"./index-CCWaWN5g.js";import{_ as R}from"./index-BtIdS_Qz.js";import{l as V,s as j}from"./depart.user.api-D_abnxSU.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";const W={class:"bg-white m-4 mr-0 overflow-hidden"},$=N({__name:"DepartTree",emits:["select"],setup(h,{emit:d}){const c=P("prefixCls"),f=d,{createMessage:p}=I();let r=l(!1),n=l([]),o=l([]),t=l([]),i=l(!0),v=l("2"),m=l(!1);function g(){r.value=!0,n.value=[],V().then(e=>{e.success?Array.isArray(e.result)&&(n.value=e.result,v.value=e.message,T()):p.warning(e.message)}).finally(()=>r.value=!1)}g();function T(){let e=[];n.value.forEach((a,_)=>{a.children&&a.children.length>0&&e.push(a.key),_===0&&y(a.id,a)}),e.length>0&&(C(),o.value=e)}function C(){return x(this,null,function*(){yield E(),m.value=!0,yield E(),m.value=!1})}function y(e,a){t.value=[e],a&&f("select",a)}function K(e){e?(r.value=!0,j({keyWord:e,myDeptSearch:"1"}).then(a=>{Array.isArray(a)?n.value=a:(p.warning("未查询到部门信息"),n.value=[])}).finally(()=>r.value=!1)):g()}function B(e,a){e.length>0&&t.value[0]!==e[0]?y(e[0],a.selectedNodes[0]):y(t.value[0])}function S(e){o.value=e,i.value=!1}return(e,a)=>{const _=k("a-empty"),A=k("a-spin");return u(),w("div",W,[b(A,{spinning:s(r)},{default:M(()=>[s(v)==="2"?(u(),w(q,{key:0},[s(m)?F("",!0):(u(),D(s(R),{key:0,title:"部门列表",toolbar:"",search:"",showLine:"",checkStrictly:!0,clickRowToExpand:!1,treeData:s(n),selectedKeys:s(t),expandedKeys:s(o),autoExpandParent:s(i),onSelect:B,onExpand:S,onSearch:K},null,8,["treeData","selectedKeys","expandedKeys","autoExpandParent"]))],64)):(u(),D(_,{key:1,description:"普通员工无此权限"}))]),_:1},8,["spinning"])])}}}),oe=L($,[["__scopeId","data-v-892b3d18"]]);export{oe as default};
