import{d as l,f as e,aq as m,ar as t,k as a,at as i}from"./vue-vendor-dy9k-Yad.js";import r from"./GrowCard-di6_Wans.js";import n from"./SiteAnalysis-BTdEGkmJ.js";import s from"./VisitSource-CJ88VLm9.js";import d from"./VisitRadar-D0zzxDQN.js";import c from"./SalesProductPie-kiTmsLZX.js";import"./index-CWWAi5V9.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./data-BpJ37qIE.js";import"./VisitAnalysis-DX-4-4QD.js";import"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";import"./props-BGjQktHt.js";import"./VisitAnalysisBar-EwDB5Y_K.js";const p={class:"p-4"},f={class:"md:flex enter-y"},z=l({__name:"IndexDef",setup(_){const o=e(!0);return setTimeout(()=>{o.value=!1},500),(u,g)=>(t(),m("div",p,[a(r,{loading:o.value,class:"enter-y"},null,8,["loading"]),a(n,{class:"!my-4 enter-y",loading:o.value},null,8,["loading"]),i("div",f,[a(d,{class:"md:w-1/3 w-full",loading:o.value},null,8,["loading"]),a(s,{class:"md:w-1/3 !md:mx-4 !md:my-0 !my-4 w-full",loading:o.value},null,8,["loading"]),a(c,{class:"md:w-1/3 w-full",loading:o.value},null,8,["loading"])])]))}});export{z as default};
