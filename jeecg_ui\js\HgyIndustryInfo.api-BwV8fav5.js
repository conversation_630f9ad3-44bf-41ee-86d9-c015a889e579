import{u as a,j as n}from"./index-CCWaWN5g.js";const o=[{title:"物资种类",align:"center",dataIndex:"materialType_dictText"},{title:"品类/细类",align:"center",dataIndex:"category"},{title:"省份",align:"center",dataIndex:"province"},{title:"城市",align:"center",dataIndex:"city"},{title:"区县",align:"center",dataIndex:"district"},{title:"高价",align:"center",dataIndex:"highPrice"},{title:"低价",align:"center",dataIndex:"lowPrice"},{title:"均价",align:"center",dataIndex:"avgPrice"},{title:"涨跌额",align:"center",dataIndex:"changeAmount"},{title:"涨跌幅",align:"center",dataIndex:"changeRate"},{title:"资讯日期",align:"center",dataIndex:"infoDate",customRender:({text:e})=>(e=e?e.length>10?e.substr(0,10):e:"",e)},{title:"创建时间",align:"center",dataIndex:"createTime"},{title:"更新时间",align:"center",dataIndex:"updateTime"},{title:"创建人",align:"center",dataIndex:"createBy"},{title:"更新人",align:"center",dataIndex:"updateBy"}],d=[{label:"物资种类",labelWidth:64,field:"materialType",component:"JSelectMultiple",componentProps:{dictCode:"wasteMaterialsDict"},colProps:{span:5}},{label:"城市",labelWidth:64,field:"city",component:"Input",colProps:{span:5}},{label:"时间",labelWidth:64,field:"infoDate",component:"RangePicker",componentProps:{valueType:"Date"},colProps:{span:7}}],i=[{label:"物资种类",field:"materialType",component:"JDictSelectTag",componentProps:{dictCode:"wasteMaterialsDict"},dynamicRules:()=>[{required:!0,message:"请输入物资种类!"}]},{label:"品类/细类",field:"category",component:"Input"},{label:"省份",field:"province",component:"Input"},{label:"城市",field:"city",component:"Input"},{label:"区县",field:"district",component:"Input"},{label:"高价",field:"highPrice",component:"InputNumber",dynamicRules:()=>[{required:!0,message:"请输入高价!"}]},{label:"低价",field:"lowPrice",component:"InputNumber",dynamicRules:()=>[{required:!0,message:"请输入低价!"}]},{label:"均价",field:"avgPrice",component:"InputNumber",dynamicRules:()=>[{required:!0,message:"请输入均价!"}]},{label:"涨跌额",field:"changeAmount",component:"InputNumber"},{label:"涨跌幅",field:"changeRate",component:"InputNumber"},{label:"资讯日期",field:"infoDate",component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD"},dynamicRules:()=>[{required:!0,message:"请输入资讯日期!"}]},{label:"",field:"id",component:"Input",show:!1}];function c(e){return i}const{createConfirm:s}=a();const u="/hgy/waste/hgyIndustryInfo/exportXls",g="/hgy/waste/hgyIndustryInfo/importExcel",m=e=>n.get({url:"/hgy/waste/hgyIndustryInfo/list",params:e}),y=(e,t)=>n.delete({url:"/hgy/waste/hgyIndustryInfo/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()});const p=(e,t)=>{let r=t?"/hgy/waste/hgyIndustryInfo/edit":"/hgy/waste/hgyIndustryInfo/add";return n.post({url:r,params:e})};export{u as a,p as b,o as c,y as d,c as e,i as f,g,m as l,d as s};
