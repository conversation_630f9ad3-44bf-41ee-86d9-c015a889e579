import{d as D,f as k,r as w,h as S,aq as _,ar as b,aA as v}from"./vue-vendor-dy9k-Yad.js";import{useECharts as C}from"./useECharts-BU6FzBZi.js";import{h as E}from"./antd-vue-vendor-me9YkNVC.js";import{a as $}from"./index-CCWaWN5g.js";const j=D({name:"LineMulti",props:{chartData:{type:Array,default:()=>[],required:!0},option:{type:Object,default:()=>({})},type:{type:String,default:"line"},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"}},emits:["click"],setup(t,{emit:h}){const r=k(null),{setOptions:p,getInstance:i}=C(r),a=w({tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0,backgroundColor:"#333"}}},legend:{top:30},grid:{top:60},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[]});S(()=>{t.chartData&&A()});function A(){var y,g;t.option&&Object.assign(a,E(t.option));let n=Array.from(new Set(t.chartData.map(e=>e.type))),d=Array.from(new Set(t.chartData.map(e=>e.name))),m=[];n.forEach(e=>{let o={name:e,type:t.type};const c=t.chartData.find(s=>s.type==e);c&&c.color&&(o.color=c.color);let l=[];d.forEach(s=>{let f=t.chartData.filter(x=>e===x.type&&x.name==s);f&&f.length>0?l.push(f[0].value):l.push(null)}),o.data=l,m.push(o)}),a.series=m,a.xAxis.data=d,p(a),(y=i())==null||y.off("click",u),(g=i())==null||g.on("click",u)}function u(n){h("click",n)}return{chartRef:r}}});function L(t,h,r,p,i,a){return b(),_("div",{ref:"chartRef",style:v({height:t.height,width:t.width})},null,4)}const M=$(j,[["render",L]]);export{M as L};
