import{d as T,f as v,ag as i,aB as P,ar as g,aD as s,k as e,at as $,G as l,aq as B,F as D,aC as y,au as A}from"./vue-vendor-dy9k-Yad.js";import{C as w}from"./index-LCGLvkB3.js";import{bu as L,bt as N,u as V,a as W}from"./index-CCWaWN5g.js";import{P as F}from"./index-CtJ0w2CP.js";import{A as _,j as b}from"./antd-vue-vendor-me9YkNVC.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const G=T({name:"TabsDemo",components:{CollapseContainer:w,PageWrapper:F,[b.name]:b,[_.name]:_},setup(){const o=L(),t=v(""),{closeAll:f,closeLeft:d,closeRight:C,closeOther:k,closeCurrent:m,refreshPage:a,setTitle:p}=N(),{createMessage:n}=V();function u(){t.value?p(t.value):n.error("请输入要设置的Tab标题！")}function r(c){o(`/comp/basic/tabs/detail/${c}`)}return{closeAll:f,closeLeft:d,closeRight:C,closeOther:k,closeCurrent:m,toDetail:r,refreshPage:a,setTabTitle:u,title:t}}}),M={class:"mt-2 flex flex-grow-0"};function O(o,t,f,d,C,k){const m=i("a-alert"),a=i("a-button"),p=i("a-input"),n=i("CollapseContainer"),u=i("PageWrapper");return g(),P(u,{title:"标签页操作示例"},{default:s(()=>[e(n,{title:"在下面输入框输入文本,切换后回来内容会保存"},{default:s(()=>[e(m,{banner:"",message:"该操作不会影响页面标题，仅修改Tab标题"}),$("div",M,[e(a,{class:"mr-2",onClick:o.setTabTitle,type:"primary"},{default:s(()=>t[5]||(t[5]=[l(" 设置Tab标题 ")])),_:1,__:[5]},8,["onClick"]),e(p,{placeholder:"请输入",value:o.title,"onUpdate:value":t[0]||(t[0]=r=>o.title=r),class:"mr-4 w-12"},null,8,["value"])])]),_:1}),e(n,{class:"mt-4",title:"标签页操作"},{default:s(()=>[e(a,{class:"mr-2",onClick:t[1]||(t[1]=()=>o.closeAll())},{default:s(()=>t[6]||(t[6]=[l(" 关闭所有 ")])),_:1,__:[6]}),e(a,{class:"mr-2",onClick:t[2]||(t[2]=()=>o.closeLeft())},{default:s(()=>t[7]||(t[7]=[l(" 关闭左侧 ")])),_:1,__:[7]}),e(a,{class:"mr-2",onClick:t[3]||(t[3]=()=>o.closeRight())},{default:s(()=>t[8]||(t[8]=[l(" 关闭右侧 ")])),_:1,__:[8]}),e(a,{class:"mr-2",onClick:t[4]||(t[4]=()=>o.closeOther())},{default:s(()=>t[9]||(t[9]=[l(" 关闭其他 ")])),_:1,__:[9]}),e(a,{class:"mr-2",onClick:o.closeCurrent},{default:s(()=>t[10]||(t[10]=[l(" 关闭当前 ")])),_:1,__:[10]},8,["onClick"]),e(a,{class:"mr-2",onClick:o.refreshPage},{default:s(()=>t[11]||(t[11]=[l(" 刷新当前 ")])),_:1,__:[11]},8,["onClick"])]),_:1}),e(n,{class:"mt-4",title:"标签页复用超出限制自动关闭(使用场景: 动态路由)"},{default:s(()=>[(g(),B(D,null,y(6,r=>e(a,{key:r,class:"mr-2",onClick:c=>o.toDetail(r)},{default:s(()=>[l(" 打开"+A(r)+"详情页 ",1)]),_:2},1032,["onClick"])),64))]),_:1})]),_:1})}const h=W(G,[["render",O]]);export{h as default};
