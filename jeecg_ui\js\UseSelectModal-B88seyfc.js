import{d,f,aB as h,ar as g,u as t,aE as _,aD as b,k as C,J as S}from"./vue-vendor-dy9k-Yad.js";import{B as w}from"./index-Diw57m_E.js";import{u as x}from"./index-BkGZ5fiW.js";import{b as R,u as M}from"./role.data-BepCB_2a.js";import{l as k}from"./user.api-mLAlJze4.js";import{ac as y,a as B}from"./index-CCWaWN5g.js";import O from"./BasicTable-xCEZpGLb.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./role.api-BvRyEQIC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const K=d({__name:"UseSelectModal",emits:["select","register"],setup(v,{emit:i}){const m=i,e=f([]),[p,{setModalProps:r,closeModal:s}]=y(),[a,{reload:I}]=x({api:k,rowKey:"id",columns:M,maxHeight:420,formConfig:{labelWidth:60,schemas:R,baseRowStyle:{maxHeight:"20px"},autoSubmitOnEnter:!0},striped:!0,useSearchForm:!0,showTableSetting:!1,bordered:!0,showIndexColumn:!1,canResize:!0}),n={type:"checkbox",columnWidth:50,selectedRowKeys:e,onChange:l};function l(o){e.value=o}const c=o=>{o&&(e.value=[])};function u(){r({confirmLoading:!0}),s(),m("select",S(t(e))),r({confirmLoading:!1})}return(o,T)=>(g(),h(t(w),_(o.$attrs,{onRegister:t(p),title:"用户选择列表",width:"1000px",height:600,onOk:u,destroyOnClose:"",onOpenChange:c}),{default:b(()=>[C(t(O),{onRegister:t(a),rowSelection:n},null,8,["onRegister"])]),_:1},16,["onRegister"]))}}),zo=B(K,[["__scopeId","data-v-2febf1f4"]]);export{zo as default};
