var W=Object.defineProperty,Y=Object.defineProperties;var Z=Object.getOwnPropertyDescriptors;var E=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,tt=Object.prototype.propertyIsEnumerable;var I=(n,t,e)=>t in n?W(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e,T=(n,t)=>{for(var e in t||(t={}))$.call(t,e)&&I(n,e,t[e]);if(E)for(var e of E(t))tt.call(t,e)&&I(n,e,t[e]);return n},B=(n,t)=>Y(n,Z(t));var S=(n,t,e)=>new Promise((x,g)=>{var u=s=>{try{c(e.next(s))}catch(d){g(d)}},k=s=>{try{c(e.throw(s))}catch(d){g(d)}},c=s=>s.done?x(s.value):Promise.resolve(s.value).then(u,k);c((e=e.apply(n,t)).next())});import{d as R,r as M,f as et,ag as m,v as ot,aq as rt,ar as l,k as a,aD as i,u as p,q as b,aB as _,ah as nt,G as f}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{useListPage as it}from"./useListPage-Soxgnx9a.js";import st from"./HgyAssetEntrustModal-DsRNxghG.js";import{s as at,b as pt,g as mt,a as lt,c as ut,d as ct,l as dt,e as _t}from"./HgyAssetEntrust.api-DEvpd7GX.js";import{ah as ft,ad as gt,a as yt}from"./index-CCWaWN5g.js";import{Q as ht}from"./componentMap-Bkie1n3v.js";import bt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const vt=R({name:"hgy.entrustService-hgyAssetEntrust"}),Ct=R(B(T({},vt),{setup(n){const t=M({}),e=et([]),x=ft(),[g,{openModal:u}]=gt(),{prefixCls:k,tableContext:c,onExportXls:s,onImportXls:d}=it({tableProps:{title:"资产处置",api:dt,columns:ct,canResize:!1,formConfig:{schemas:ut,autoSubmitOnEnter:!0,showAdvancedButton:!0,fieldMapToNumber:[],fieldMapToTime:[]},actionColumn:{width:120,fixed:"right"},beforeFetch:o=>Object.assign(o,t)},exportConfig:{name:"资产处置",url:lt,params:t},importConfig:{url:mt,success:y}}),[U,{reload:D},{rowSelection:q,selectedRowKeys:v}]=c,F=M(at);function j(o){Object.keys(o).map(r=>{t[r]=o[r]}),D()}function N(){u(!0,{isUpdate:!1,showFooter:!0})}function O(o){u(!0,{record:o,isUpdate:!0,showFooter:!0})}function Q(o){u(!0,{record:o,isUpdate:!0,showFooter:!1})}function H(o){return S(this,null,function*(){yield _t({id:o.id},y)})}function K(){return S(this,null,function*(){yield pt({ids:v.value},y)})}function y(){(v.value=[])&&D()}function L(o){return[{label:"编辑",onClick:O.bind(null,o),auth:"hgy.entrustService:hgy_asset_entrust:edit"}]}function P(o){return[{label:"详情",onClick:Q.bind(null,o)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:H.bind(null,o),placement:"topLeft"},auth:"hgy.entrustService:hgy_asset_entrust:delete"}]}return(o,r)=>{const C=m("a-button"),V=m("j-upload-button"),A=m("Icon"),X=m("a-menu-item"),z=m("a-menu"),G=m("a-dropdown"),J=m("super-query"),h=ot("auth");return l(),rt("div",null,[a(p(bt),{onRegister:p(U),rowSelection:p(q)},{tableTitle:i(()=>[b((l(),_(C,{type:"primary",onClick:N,preIcon:"ant-design:plus-outlined"},{default:i(()=>r[0]||(r[0]=[f(" 新增")])),_:1,__:[0]})),[[h,"hgy.entrustService:hgy_asset_entrust:add"]]),b((l(),_(C,{type:"primary",preIcon:"ant-design:export-outlined",onClick:p(s)},{default:i(()=>r[1]||(r[1]=[f(" 导出")])),_:1,__:[1]},8,["onClick"])),[[h,"hgy.entrustService:hgy_asset_entrust:exportXls"]]),b((l(),_(V,{type:"primary",preIcon:"ant-design:import-outlined",onClick:p(d)},{default:i(()=>r[2]||(r[2]=[f("导入")])),_:1,__:[2]},8,["onClick"])),[[h,"hgy.entrustService:hgy_asset_entrust:importExcel"]]),p(v).length>0?(l(),_(G,{key:0},{overlay:i(()=>[a(z,null,{default:i(()=>[a(X,{key:"1",onClick:K},{default:i(()=>[a(A,{icon:"ant-design:delete-outlined"}),r[3]||(r[3]=f(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:i(()=>[b((l(),_(C,null,{default:i(()=>[r[4]||(r[4]=f("批量操作 ")),a(A,{icon:"mdi:chevron-down"})]),_:1,__:[4]})),[[h,"hgy.entrustService:hgy_asset_entrust:deleteBatch"]])]),_:1})):nt("",!0),a(J,{config:F,onSearch:j},null,8,["config"])]),action:i(({record:w})=>[a(p(ht),{actions:L(w),dropDownActions:P(w)},null,8,["actions","dropDownActions"])]),bodyCell:i(({column:w,record:wt,index:St,text:xt})=>r[5]||(r[5]=[])),_:1},8,["onRegister","rowSelection"]),a(st,{onRegister:p(g),onSuccess:y},null,8,["onRegister"])])}}})),Me=yt(Ct,[["__scopeId","data-v-99bc9c9a"]]);export{Me as default};
