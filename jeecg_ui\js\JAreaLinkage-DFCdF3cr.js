import{d as m,f as r,e as A,h as w,ag as y,aB as b,ar as p,aE as $}from"./vue-vendor-dy9k-Yad.js";import{af as _}from"./antd-vue-vendor-me9YkNVC.js";import{r as D,a as d,p as O,b as k}from"./areaDataUtil-BXVjRArW.js";import{H as t,a9 as B,R as P,a as V}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const j=m({name:"JAreaLinkage",components:{Cascader:_},inheritAttrs:!1,props:{value:t.oneOfType([t.object,t.array,t.string]),showArea:t.bool.def(!0),showAll:t.bool.def(!1),saveCode:t.oneOf(["province","city","region","all"]).def("all")},emits:["options-change","change","update:value"],setup(n,{emit:l,refs:v}){const g=r([]),c=B(),o=r([]),i=A(()=>{if(n.showArea&&n.showAll)return D;if(n.showArea&&!n.showAll)return d;if(!n.showArea&&!n.showAll)return O;if(!n.showArea&&n.showAll)return k});w(()=>{n.value?h():o.value=[]});function h(){let e=n.value?n.value:[];if(e&&typeof e=="string"&&e!="null"&&e!="undefined"){const s=e.split(",");o.value=u(s)}else P(e)&&(e.length?o.value=u(e):o.value=[])}function u(e){let s=[];if(n.saveCode==="region"){const a=e[0];s=[`${a.substring(0,2)}0000`,`${a.substring(0,2)}${a.substring(2,4)}00`,a]}else if(n.saveCode==="city"){const a=e[0];s=[`${a.substring(0,2)}0000`,a]}else n.saveCode==="province"?s=[e[0]]:s=e;return s}const f=e=>{let s=e;s&&(n.saveCode==="all"||(s=e.join(","))),l("change",s),l("update:value",s)};function C(e,...s){if(e!=null&&e.length){let a=[];n.saveCode==="region"?a=[e[e.length-1]]:n.saveCode==="city"?a=[e[1]]:n.saveCode==="province"?a=[e[0]]:a=e,f(a)}else f(e)}return{cascaderValue:o,attrs:c,regionData:d,getOptions:i,handleChange:C}}});function E(n,l,v,g,c,o){const i=y("Cascader");return p(),b(i,$(n.attrs,{value:n.cascaderValue,options:n.getOptions,onChange:n.handleChange}),null,16,["value","options","onChange"])}const q=V(j,[["render",E]]);export{q as default};
