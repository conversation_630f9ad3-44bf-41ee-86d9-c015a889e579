import{d as x,ag as r,aq as i,ar as m,k as a,aD as o,ah as p,G as s,at as d}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as I}from"./useListPage-Soxgnx9a.js";import N from"./BasicTable-xCEZpGLb.js";import{a as C}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const c=[{title:"Name",dataIndex:"name",key:"name"},{title:"Platform",dataIndex:"platform",key:"platform"},{title:"Version",dataIndex:"version",key:"version"},{title:"Upgraded",dataIndex:"upgradeNum",key:"upgradeNum"},{title:"Creator",dataIndex:"creator",key:"creator"},{title:"Date",dataIndex:"createdAt",key:"createdAt"},{title:"Action",key:"operation"}],l=[];for(let e=0;e<3;++e)l.push({key:e,name:"Screem",platform:"iOS",version:"10.3.4.5654",upgradeNum:500,creator:"Jack",createdAt:"2014-12-24 23:12:00"});const A=[{title:"Date",dataIndex:"date",key:"date"},{title:"Name",dataIndex:"name",key:"name"},{title:"Status",dataIndex:"state",key:"state"},{title:"Upgrade Status",dataIndex:"upgradeNum",key:"upgradeNum"},{title:"Action",dataIndex:"operation",key:"operation"}],_=[];for(let e=0;e<3;++e)_.push({key:e,date:"2014-12-24 23:12:00",name:"This is production name",upgradeNum:"Upgraded: 56"});const T=x({components:{BasicTable:N},setup(){const{tableContext:e}=I({tableProps:{title:"内嵌表格",dataSource:l,columns:c,showActionColumn:!1,rowKey:"key",useSearchForm:!1}}),[t]=e;return{data:l,columns:c,innerColumns:A,innerData:_,registerTable:t}}}),S={class:"p-4"},v={key:0},w={key:0},B={key:1,class:"table-operation"};function D(e,t,P,V,R,$){const f=r("a-badge"),u=r("a-menu-item"),k=r("a-menu"),y=r("a-dropdown"),g=r("a-table"),b=r("BasicTable");return m(),i("div",S,[a(b,{onRegister:e.registerTable,class:"components-table-demo-nested"},{bodyCell:o(({column:n})=>[n.key==="operation"?(m(),i("a",v,"Publish")):p("",!0)]),expandedRowRender:o(()=>[a(g,{columns:e.innerColumns,"data-source":e.innerData,pagination:!1},{bodyCell:o(({column:n})=>[n.dataIndex==="state"?(m(),i("span",w,[a(f,{status:"success"}),t[0]||(t[0]=s(" Finished "))])):p("",!0),n.dataIndex==="operation"?(m(),i("span",B,[t[4]||(t[4]=d("a",null,"Pause",-1)),t[5]||(t[5]=d("a",null,"Stop",-1)),a(y,null,{overlay:o(()=>[a(k,null,{default:o(()=>[a(u,null,{default:o(()=>t[1]||(t[1]=[s("Action 1")])),_:1,__:[1]}),a(u,null,{default:o(()=>t[2]||(t[2]=[s("Action 2")])),_:1,__:[2]})]),_:1})]),default:o(()=>[t[3]||(t[3]=d("a",null," More ",-1))]),_:1,__:[3]})])):p("",!0)]),_:1},8,["columns","data-source"])]),_:1},8,["onRegister"])])}const qt=C(T,[["render",D]]);export{qt as default};
