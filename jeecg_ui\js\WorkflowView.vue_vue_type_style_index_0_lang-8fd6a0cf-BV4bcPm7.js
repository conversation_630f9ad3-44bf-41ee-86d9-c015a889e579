import{d as Te,u as b,r as ct,f as it,p as Rt,o as Ae,n as nt,aq as be,b as Pe,ar as Wt,F as $e,at as Ft,k as pt,aB as Le,ah as De,as as Oe,aQ as He,aJ as Me,aE as Re}from"./vue-vendor-dy9k-Yad.js";import{w as We,Y as jt,N as Fe,Q as U,f as Be,z as Ge,k as _e,H as je,X as qe,a as Ue}from"./NodeStyle-59980363-DzErCCzZ.js";import{h as Ve,aM as ze,al as Je}from"./antd-vue-vendor-me9YkNVC.js";import{a as Bt}from"./index-JbqXEynz.js";import{F as qt}from"./index-CCWaWN5g.js";import Ke from"./DebugRunDrawer-0300c531-CyLRv4cG.js";import Ye from"./NodeSettingDrawer-6b9eaf9c-B37V3ysV.js";import{S as u,C as Tt}from"./useSettings-4a774f12-DjycrGR6.js";import{S as Ut,b as Vt,g as ht,a as Qe}from"./SwitchSetting-846f3c7c-bOv0eqNm.js";import{C as zt,b as Jt,g as ut,a as Xe}from"./ClassifierSetting-4b0b2595-BH1h6Izc.js";import{r as Z,a as Kt,t as Yt,n as K}from"./index-9c51646a-BviQbLw-.js";import{x as Ze}from"./runStore-e1feeee0-BrPjIiN4.js";import{U as to}from"./ControlRender.vue_vue_type_script_setup_true_lang-56b36276-qzsRzezO.js";import"./index-D6l0IxOU.js";import"./AddNodeDialog.vue_vue_type_style_index_0_lang-675acbfd-CytYr8Kw.js";import"./NodeContainer.vue_vue_type_style_index_0_lang-60bcf3b8-DFhy0o3x.js";import"./NodeKV.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import{p as eo}from"./StartIcon.vue_vue_type_script_setup_true_lang-f47767c0-bqHmKEo8.js";import{w as oo}from"./StartSetting.vue_vue_type_script_setup_true_lang-05719d67-Cut55Vca.js";import{l as io}from"./StartNode.vue_vue_type_script_setup_true_lang-5f12561d-Bhwj3TLb.js";import{m as no}from"./EndIcon.vue_vue_type_script_setup_true_lang-ddad4c62-DlgawK5P.js";import{A as so}from"./EndSetting.vue_vue_type_script_setup_true_lang-5dcb77c3-DNJrt_CE.js";import{b as ro}from"./EndNode.vue_vue_type_script_setup_true_lang-5dfd02b2-BsSZzrtn.js";import{m as ao}from"./LLMIcon.vue_vue_type_script_setup_true_lang-c9e9375d-2VqWfkdq.js";import lo from"./LLMSetting-00827864-9YfQt7Ds.js";import{R as po}from"./LLMNode.vue_vue_type_script_setup_true_lang-be22c101-AxUxvVN3.js";import{a as co}from"./ClassifierIcon.vue_vue_type_script_setup_true_lang-6d4bddc4-C-0mReBq.js";import{D as ho}from"./ClassifierNode.vue_vue_type_style_index_0_lang-c06eb121-CYBvd-33.js";import{i as uo}from"./SwitchIcon.vue_vue_type_script_setup_true_lang-f4c7e2c8-D6zXxRfa.js";import{V as go}from"./SwitchNode.vue_vue_type_style_index_0_lang-d52268fe-DbsYQqKG.js";import{p as mo}from"./KnowledgeIcon.vue_vue_type_script_setup_true_lang-8cffec78-BLmVybP2.js";import{m as fo}from"./KnowledgeSetting.vue_vue_type_script_setup_true_lang-d64568da-gf-lZ2e6.js";import{g as yo}from"./KnowledgeNode.vue_vue_type_script_setup_true_lang-95a89862-zp0ZzPwk.js";import{p as Eo}from"./CodeIcon.vue_vue_type_script_setup_true_lang-2e494340-BCVbeygB.js";import No from"./CodeSetting-9d3bac85-CSjhcCqC.js";import{b as vo}from"./CodeNode.vue_vue_type_script_setup_true_lang-1ed03759-CK38AKNO.js";import{p as xo}from"./SubflowIcon.vue_vue_type_script_setup_true_lang-fb79224c-B4BFA4ei.js";import Io from"./SubflowSetting-44ce51e7-BRKLM1S-.js";import{b as So}from"./SubflowNode.vue_vue_type_script_setup_true_lang-78c8949a-BZTb6exK.js";import{s as wo}from"./EnhanceJavaIcon.vue_vue_type_script_setup_true_lang-a90305be-CfsMlpVI.js";import Co from"./EnhanceJavaSetting-a24f23f6-C12fTNTe.js";import{h as ko}from"./EnhanceJavaNode.vue_vue_type_script_setup_true_lang-936c4858-CpD8Yh4q.js";import{i as To}from"./HTTPIcon.vue_vue_type_style_index_0_lang-b429a5fd-C_60E2ss.js";import Ao from"./HTTPSetting-94ab4f56-DHb3RR1u.js";import{P as bo}from"./HTTPNode.vue_vue_type_script_setup_true_lang-08b835bc-CxE4eAKt.js";import{a as Po}from"./ReplyIcon.vue_vue_type_script_setup_true_lang-dd94e1af-Dy8VhvuP.js";import{$ as $o}from"./ReplySetting.vue_vue_type_script_setup_true_lang-f85cce4a-Dhdms1rN.js";import{y as Lo}from"./ReplyNode.vue_vue_type_script_setup_true_lang-baab753b-sNJVb8TM.js";var Do=Object.defineProperty,Oo=Object.defineProperties,Ho=Object.getOwnPropertyDescriptors,Gt=Object.getOwnPropertySymbols,Mo=Object.prototype.hasOwnProperty,Ro=Object.prototype.propertyIsEnumerable,_t=(o,t,e)=>t in o?Do(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,X=(o,t)=>{for(var e in t||(t={}))Mo.call(t,e)&&_t(o,e,t[e]);if(Gt)for(var e of Gt(t))Ro.call(t,e)&&_t(o,e,t[e]);return o},at=(o,t)=>Oo(o,Ho(t)),st=(o,t,e)=>new Promise((n,s)=>{var i=a=>{try{l(e.next(a))}catch(c){s(c)}},d=a=>{try{l(e.throw(a))}catch(c){s(c)}},l=a=>a.done?n(a.value):Promise.resolve(a.value).then(i,d);l((e=e.apply(o,t)).next())}),m=(o=>(o.ID="ID",o.WHEN="WHEN",o.THEN="THEN",o.SWITCH="SWITCH",o.IF="IF",o.GROUP="GROUP",o.BREAK="BREAK",o.OR="OR",o.AND="AND",o.NOT="NOT",o))(m||{}),Y=(o=>(o.CATCH="CATCH",o.LOGIC="LOGIC",o.CONFIG="CONFIG",o.FOR="FOR",o.WHILE="WHILE",o.ITERATOR="ITERATOR",o))(Y||{});class B{constructor(t=""){this.originType="",this.anchorsNextIds=[],t&&(this.id=t),this.child=[],this.maxLineNum=25}addChild(t){this.child||(this.child=[]),this.child.push(t)}get childFirstId(){if(!Array.isArray(this.child)||this.child.length===0)return"";const t=this.child[0];switch(t.type){case m.WHEN:case m.THEN:return t.childFirstId}return t.id}getElString(){var t,e,n,s;const i=(t=this.properties)!=null&&t.tag?`.tag('${(e=this.properties)==null?void 0:e.tag}')`:"",d=(n=this.properties)!=null&&n.data?`.data('${(s=this.properties)==null?void 0:s.data}')`:"";return`${this.getElStringRaw()}${i}${d}`}getElStringRaw(){if(this.type===m.ID)return this.nodeId;if(this.elString)return this.elString;switch(this.type){case m.IF:return this.getELString_IF();case m.GROUP:return this.getELString_Group();case m.SWITCH:return this.getELString_SWITCH();case m.WHEN:case m.THEN:return this.getElString_WHEN_THEN();case u.CODE:return this.id;default:return this.type}}insertSpace(t){return`    ${t.split(`
`).join(`
    `)}`}getElString_WHEN_THEN(){const t=this.child||[];let e="",n="";this.aliasId&&(e=this.aliasId.startsWith("tag:")?`.tag("${this.aliasId.substring(4)}")`:`.id("${this.aliasId}")`),this.childFirstId&&(n=`.tag("${this.childFirstId}")`);const s=t.map(l=>l.getElString()),i=s.join(",");if(t.length===1&&(!this.aliasId||t[0].type===m.THEN||t[0].type===m.WHEN))return`${i}${e}`;if(i.length<this.maxLineNum-6)return`${this.type}(${i})${n||e}`;const d=this.insertSpace(s.join(`,
`));return`${this.type}(
${d}
)${n||e}`}getELString_Group(){var t,e,n,s,i;const d=this.child||[],l=d.map(N=>N.getElString()).join(",");if(this.groupType===Y.CONFIG){const N=(t=this.properties)!=null&&t.ignoreError?".ignoreError(true)":"",C=(e=this.properties)!=null&&e.any?"any(true)":"",k=(n=this.properties)!=null&&n.must?`must(${(s=this.properties)==null?void 0:s.must})`:"";return`${l}${N}${C}${k}`}if(this.groupType===Y.LOGIC)return this.elString;const a=this.groupType;if(this.groupType===Y.CATCH){const N=this.exceptionNode.getElString();return`${a}(${l}).DO(${N})`}const c=this.breakNode?`.BREAK(${this.breakNode.getElString()})`:"",v=(i=this.startNode)!=null&&i.nodeId?this.startNode.getElString():this.startNum;return d.length===1?`${a}(${v}).DO(${l})${c}`:`${a}(${v}).DO(WHEN(${l}))${c}`}getELString_IF(){const t=(this.child||[]).map(n=>n.getElString()),e=t.join(",");return e.length<this.maxLineNum-4?`IF(${e})`:`IF(
${this.insertSpace(t.join(`,
`))}
)`}getELString_SWITCH(){this.parseSwitchChildren();const t=(this.child||[]).map(l=>l.getElString()),e=t.join(","),n=this.originType,s=`${n||u.SWITCH}.tag('${this.nodeId}')`,i=`.tag('${this.id}')`;if(e.length<this.maxLineNum-13-this.nodeId.length)return`SWITCH(${s}).to(${e})${i}`;const d=this.insertSpace(t.join(`,
`));return`SWITCH(${s}).to(
${d}
)${i}`}parseSwitchChildren(){if(this.anchorsNextIds.length===0)return;const t=this.child||[];if(!(t.length<=this.anchorsNextIds.length)){for(const{nextIds:e}of this.anchorsNextIds){if(e.length<=1)continue;const n=new B;n.type=m.WHEN;for(let s=0;s<e.length;s++){const i=e[s],d=t.findIndex(l=>{let a=l.id;return!a&&l.child.length>0&&(a=l.child[0].id),a===i||l.childFirstId===i});d!==-1&&(s===0?n.addChild(t.splice(d,1,n)[0]):n.addChild(t.splice(d,1)[0]))}}this.child=t}}}class Wo{constructor(){this.stack=[],this.ebpStack=[]}peek(t){return t[t.length-1]}pop(){return this.stack.pop()}push(t){this.stack.push(t)}clear(){this.stack=[]}resolve(){const t=this.peek(this.ebpStack),e=this.stack[t+1];for(let n=t+2;n<this.stack.length;n++){const s=this.stack[n];e.addChild(s)}return e}quit(){const t=this.resolve(),e=this.stack[this.peek(this.ebpStack)];for(;this.stack.length>this.peek(this.ebpStack);)this.stack.pop();return this.stack.push(t),this.ebpStack.pop(),e}create(){this.ebpStack.push(this.stack.length),this.stack.push(new B)}addEndPoint(t){this.stack[this.peek(this.ebpStack)]=t}getEndPoint(){return this.stack[this.peek(this.ebpStack)]}}class Fo{constructor(t){this.endPoints={},this.sourceNum={},this.nodeMap={},this.inGrooupNode={},this.elStack=new Wo,this.startId="start",this.endId="end",this.anchorNextIdsMap=t}isEnd(t){return this.endId===t}isStart(t){return this.startId===t}getNodeById(t){return this.nodeMap[t]}initEdge(t,e,n=""){if(this.inGrooupNode[t]||this.inGrooupNode[e])return;this.endPoints[t]||(this.endPoints[t]=[]);const s=this.nodeMap[e];s.comingEdgeText=n,this.endPoints[t].push(s),this.sourceNum[e]||(this.sourceNum[e]=0),this.sourceNum[e]++}init(t){const e=[];t.nodes.forEach(i=>{if(i.id){if(i.type===m.GROUP){e.push(i);return}this.initELNode(i)}}),e.forEach(i=>{this.initLoopELNode(i)});const n=new B;n.id=this.startId,this.nodeMap[this.startId]=n;const s=new B;s.id=this.endId,this.nodeMap[this.endId]=s,t.edges.forEach(i=>{var d,l;const a=i.sourceNodeId,c=i.targetNodeId;!a||!c||(this.nodeMap[a].type===m.IF?this.parseIFEdge((d=i.text)==null?void 0:d.value,a,c):this.initEdge(a,c,(l=i.text)==null?void 0:l.value))}),t.nodes.forEach(i=>{!i.id||this.inGrooupNode[i.id]||(this.endPoints[i.id]||this.initEdge(i.id,this.endId),this.sourceNum[i.id]||this.initEdge(this.startId,i.id))}),t.nodes.length===0&&this.initEdge(this.startId,this.endId)}parseIFEdge(t,e,n){const s=this.nodeMap[n];let i=this.endPoints[e];i||(i=[void 0,void 0]),this.isTrueText(t)?(i[0]&&(i[1]=i[0]),i[0]=s):this.isFalseText(t)?(i[1]&&(i[0]=i[1]),i[1]=s):i[0]?i[1]=s:i[0]=s,this.endPoints[e]=i,this.sourceNum[n]||(this.sourceNum[n]=0),this.sourceNum[n]++}isTrueText(t){return!!["是","true","True","TRUE"].find(e=>e===t)}isFalseText(t){return!!["否","false","False","FALSE"].find(e=>e===t)}initELNode(t){var e,n,s,i,d,l,a;t.properties=X({},t.properties),t.properties.nodeId=t.id,t.properties.name=t.properties.text,t.properties.tag=t.id;const c=new B;return c.id=t.id,c.type=this.typeFormat(t.type),c.originType=t.type,this.handleSwitchNode(c,t),c.properties=t.properties,c.nodeId=(e=t.properties)==null?void 0:e.nodeId,c.name=(n=t.properties)==null?void 0:n.name,c.groupType=(s=t.properties)==null?void 0:s.groupType,c.data=(i=t.properties)==null?void 0:i.data,c.aliasId=(d=t.properties)==null?void 0:d.aliasId,c.tag=(l=t.properties)==null?void 0:l.tag,c.startNum=(a=t.properties)==null?void 0:a.startNum,this.nodeMap[c.id]=c,c}handleSwitchNode(t,e){if(t.type!==m.SWITCH)return;const n=t.originType===u.SWITCH?Ut:zt,s=n.getCaseList(e);for(let i=0;i<s.length;i++){const d=s[i],l=n.getAnchorId(e.id,d.type,i+1),a=this.anchorNextIdsMap.get(l);a&&a.length>0&&t.anchorsNextIds.push({anchorId:l,nextIds:a})}}initLoopELNode(t){var e,n;const s=this.initELNode(t);s.groupType==Y.LOGIC?s.elString=this.getLogicStr(t.flowData):s.addChild(new Qt(t.flowData).parse()),this.nodeMap[s.id]=s,t.flowData.nodes.forEach(i=>{const d=i.id;this.inGrooupNode[d]=!0}),(e=t.sourceNodeIds)==null||e.forEach(i=>{this.initEdge(i,s.id)}),(n=t.targetNodeIds)==null||n.forEach(i=>{this.initEdge(s.id,i)})}typeFormat(t){return t===u.SWITCH||t===u.CLASSIFIER?m.SWITCH:t==="IF"?m.IF:t==="SWITCH"?m.SWITCH:t==="GROUP"?m.GROUP:t==="AND"?m.AND:t==="NOT"?m.NOT:t==="OR"?m.OR:t}setSourceNum(t,e){this.sourceNum[t.id]=e}getSourceNum(t){return this.sourceNum[t.id]||0}getEndNum(t){const e=this.endPoints[t.id];return e?e.length:0}getEndList(t){return this.endPoints[t.id]||[]}push(t){this.elStack.push(t)}pop(){return this.elStack.pop()}createStackEnv(t){this.elStack.create();const e=new B;e.id=t.id,e.type=t.type,e.originType=t.originType,e.groupType=t.groupType,e.aliasId=t.aliasId,e.name=t.name,e.data=t.data,e.tag=t.tag,e.nodeId=t.nodeId,e.child=t.child,e.comingEdgeText=t.comingEdgeText,e.elString=t.elString,e.anchorsNextIds=t.anchorsNextIds,e.startNode=t.startNode,e.breakNode=t.breakNode,e.exceptionNode=t.exceptionNode,e.startNum=t.startNum,this.elStack.push(e)}quitStackEnv(){return this.elStack.quit()}setStackEndPoint(t){this.elStack.addEndPoint(t)}getLogicStr(t){const e={};t.nodes.forEach(d=>e[d.id]=d);const n={};t.edges.forEach(d=>{n[d.sourceNodeId]?n[d.sourceNodeId]++:n[d.sourceNodeId]=1});const s=t.nodes.map(d=>d.id).find(d=>!n[d]),i=d=>{const l=e[d],a=t.edges.filter(c=>c.targetNodeId===d).map(c=>c.sourceNodeId).map(c=>{var v;const N=e[c];if(N.type===m.ID)return(v=N.properties)==null?void 0:v.nodeId;if(N.type===m.AND||N.type===m.OR||N.type===m.NOT)return i(N.id);throw new Error("未知的节点类型")}).join(",");return`${l.type}(${a})`};return i(s)}}class Qt{constructor(t,e){this.logicFlow=t,this.context=new Fo(e),this.context.init(this.logicFlow)}parse(){const t=this.context.startId,e=new B;return e.id=t,this.parseThenChain(e),this.context.pop()}parseThenChain(t,e,n,s=""){return this.context.createStackEnv({type:m.THEN,aliasId:s}),n&&this.context.push(n),this.parseSingleNode(t,{targetNode:e}),this.context.quitStackEnv()}parseSingleNode(t,e){const n=t.id,s=this.context.getEndNum(t),i={targetNode:e.targetNode};if(this.context.isEnd(n))return this.context.setStackEndPoint(t),null;if(e.targetNode&&e.targetNode.id===t.id)return this.context.setStackEndPoint(t),null;if(this.context.isStart(n)||this.context.push(t),m.IF===t.type){const l=this.parseIF(this.context.pop());return this.parseSingleNode(l,i)}if(m.SWITCH===t.type){const l=this.parseWhich(this.context.pop());return this.parseSingleNode(l,i)}if(m.GROUP===t.type){let l=this.parseGroup(this.context.pop());if(t.groupType===Y.LOGIC&&s>0&&!this.context.isEnd(this.context.getEndList(t)[0].id)){const a=this.context.pop();l=this.parseIF(a)}return this.parseSingleNode(l,i)}if(s===1)return this.parseSingleNode(this.context.getEndList(t)[0],i);const d=this.parseWhenChain(t);return this.parseSingleNode(d,i)}parseBranch(t){const e=this.context.getEndList(t);let n=this.getBranchEnd(t);return e.forEach(s=>{s.id!=n.id&&this.parseThenChain(s,n,void 0,t.type===m.SWITCH?s.comingEdgeText:"")}),n}parseIF(t){const e=new B;e.type=m.IF,this.context.createStackEnv(e),t.type===m.IF&&(t.type=m.ID),this.context.push(t);const n=this.context.getEndList(t).filter(d=>!!d),s=n.length;if(s==1){let d,l;try{if(d=n[0],l=this.context.endPoints[d.id][0],!this.context.isEnd(l.id))throw new Error}catch(a){throw new Error("IF 判断节点的分支数必须为2")}return this.context.push(d),l}if(s!=2)throw new Error("IF 判断节点的分支数必须为2");const i=this.parseBranch(t);return this.context.setStackEndPoint(i),this.context.quitStackEnv()}parseWhich(t){if(this.context.getEndList(t).length<=1)throw new Error("WHICH 分支节点的分支数必须大于1");this.context.createStackEnv(t);const e=this.parseBranch(t);return this.context.setStackEndPoint(e),this.context.quitStackEnv()}parseWhenBase(t,e,n){return this.context.createStackEnv({type:m.WHEN}),t.forEach((s,i)=>{s.id!=e.id&&this.parseThenChain(s,e,n[i])}),this.context.quitStackEnv(),this.context.pop()}parseWhenChain(t){this.context.createStackEnv({type:m.WHEN});const e=this.context.getEndList(t),n=this.getBranchEnd(t),s=this.getBranchEnd(t,n),i={},d={};return Object.entries(s).sort(([,l],[,a])=>l.length-a.length).forEach(([l,a])=>{const c=this.context.getNodeById(l);if(a.length==1)return;const v=[];a.forEach(k=>{const D=e[k];i[D.id]?v.push(i[D.id]):v.push(D.id)}),a.forEach(k=>{const D=e[k];i[D.id]=l});const N=Array.from(new Set(v)).map(k=>this.context.getNodeById(k)),C=this.parseWhenBase(N,c,N.map(k=>d[k.id]));d[l]=C}),this.context.push(d[n.id]),this.context.setStackEndPoint(n),this.context.quitStackEnv()}parseGroup(t){const e=this.context.getEndList(t);let n,s=!0;return e.forEach(i=>{if(i.comingEdgeText){this.parseThenChain(i);const d=this.context.elStack.pop();switch(i.comingEdgeText){case"START":t.startNode=d.child[0];break;case"BREAK":t.breakNode=d.child[0];break;case"EXCEPTION":t.exceptionNode=d.child[0];break}}else n=i,s=!1}),this.context.elStack.push(t),s?new B(this.context.endId):n}getBranchEnd(t,e){const n={};let s=this.context.getEndNum(t);const i=this.context.getEndList(t).map(l=>l.id),d={};for(;;)for(let l=0;l<i.length;l++){const a=i[l];if(!a)continue;const c=this.context.getNodeById(a);if(d[c.id]||(d[c.id]=0),d[c.id]++,n[c.id]||(n[c.id]=[]),n[c.id].push(l),d[c.id]===s)return t.type===m.SWITCH&&this.context.getEndList(t).some(v=>v.id===c.id)?this.getBranchEnd(c):e?n:c;this.context.isEnd(c.id)||c.id===(e==null?void 0:e.id)?i[l]="":this.context.getEndNum(c)>1?i[l]=this.getBranchEnd(c).id:i[l]=this.context.getEndList(c)[0].id}}}function Bo(o,t){return o=Ve(o),new Qt(o,t).parse().getElString()}const gt="j-airag-vue-node",rt=20,L=332,$=62;function Go(o,t){const e=()=>b(o);function n(){const d=e();if(!d)return;d.focusOn("start-node");const l=-(d.graphModel.width/2-L/2)+20;d.translate(l,0)}function s(){t.transformModel.zoom(!0),t.transformModel.zoom(!1)}function i(d){const l=[],a=c=>{const v=t.edges.filter(C=>C.targetNodeId===c.id);if(v.length===0)return;const N=v.flatMap(C=>{const k=t.nodes.find(D=>D.id===C.sourceNodeId);return k?[k]:[]});N.length!==0&&(l.push(...N),N.forEach(C=>a(C)))};return a(d),ze(l,"id")}return{focusOnStartNode:n,repaintGraph:s,getAllPrevNodes:i}}class dt{constructor({lf:t,options:e}){this.lf=t;const{prefixCls:n}=qt("aiflow-plugin-control");this.prefixCls=n;const{use:{componentProps:s}}=e;s.visible=!0}render(t,e){this.destroy();const n=this.getControlTool();this.toolEl=n,e.appendChild(n),this.domContainer=e}destroy(){this.domContainer&&this.toolEl&&this.domContainer.contains(this.toolEl)&&this.domContainer.removeChild(this.toolEl)}getControlTool(){const t=document.createElement("div");return t.className=this.prefixCls,t}register(t){t.mount(this.lf,this.toolEl,this)}}dt.pluginName="super-control";function _o(o){const t=ct({visible:!1}),e=ct({onRegister:n,onResetGraph:s});function n(i){if(!o.value)return;const d=o.value.extension[dt.pluginName];d&&d.register(i)}function s(){if(!o.value)return;const i=o.value;i.resetZoom(),i.graphModel.$J.focusOnStartNode()}return{SuperControl:{pluginName:dt.pluginName,componentProps:t,componentIs:to,superControlProps:e}}}function jo(){return{plugins:[dt]}}class O extends We{get nodeConfig(){return Z.get(this.type)}initNodeData(t){var e,n;if(t.id||(t.id=jt()),delete t.text,super.initNodeData(t),!this.nodeConfig)return;const{params:s,methods:i}=this.nodeConfig;this.width=(e=s==null?void 0:s.width)!=null?e:L,this.height=(n=s==null?void 0:s.height)!=null?n:$,typeof(i==null?void 0:i.initNodeData)=="function"&&i.initNodeData.call(this,t);const d=(l,a)=>(l==null?void 0:l.id)===(a==null?void 0:a.id);this.sourceRules.push({validate:(l,a,c,v)=>d(l,a)||c==null||v==null?!1:c.type==="right"&&v.type==="left",message:"右侧锚点只能连接左侧锚点！"}),this.targetRules.push({message:"左侧锚点只能连接右侧锚点！",validate:(l,a,c,v)=>{if(d(l,a)||c==null||v==null)return!1;if(v.type==="left"&&c.type==="right"){const N=this.graphModel.getNodeIncomingEdge(a.id);return N&&N.length>0?!N.some(C=>C.sourceAnchorId===c.id):!0}return!1}}),this.targetRules.push({message:"禁止循环连接！",validate:(l,a)=>!this.graphModel.$J.getAllPrevNodes(l).some(c=>c.id===a.id)})}getTextStyle(){const t=super.getTextStyle();return t.color="transparent",t}getNodeStyle(){const t=super.getNodeStyle();return t.overflow="visible",t}getOutlineStyle(){const t=super.getOutlineStyle();return t.stroke="none",t.hover&&(t.hover.stroke="none"),t}getDefaultAnchor(){var t;if(!this.nodeConfig)return;const e=this.width/2,n=this.height/2,s=this.y-n+$/2,i=[{x:this.x-e,y:s,id:`${this.id}_input`,type:"left"},{x:this.x+e,y:s,id:`${this.id}_output`,type:"right"}];return typeof((t=this.nodeConfig.methods)==null?void 0:t.getDefaultAnchor)=="function"?this.nodeConfig.methods.getDefaultAnchor.call(this,i):i}getAnchorLineStyle(t){const e=super.getAnchorLineStyle();return e.stroke="#999999",e}}const Xt="2 2 20 20",Zt=`
<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="${Xt}">
    <path fill="currentColor" d="M17 13h-4v4h-2v-4H7v-2h4V7h2v4h4m-5-9A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2"/>
</svg>
`,qo=`
<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="${Xt}">
    <path fill="currentColor" d="M12 20a8 8 0 0 1-8-8a8 8 0 0 1 8-8a8 8 0 0 1 8 8a8 8 0 0 1-8 8m0-18A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2" />
</svg>
`,mt=18,Uo=mt/2;class H extends Fe{constructor(t){super(t)}setHtml(t){const e=document.createElement("div");e.className=`${gt}-content`,e.dataset.nodeId=this.props.model.id,this.root=e,t.appendChild(e),super.renderVueComponent()}confirmUpdate(t){}getAnchorShape(t){const{x:e,y:n,type:s}=t,i=s==="left",d=s==="right",l=(()=>{const a=i?"targetAnchorId":"sourceAnchorId";return this.props.graphModel.edges.some(c=>c[a]===t.id)})();return U("foreignObject",at(X({},t),{width:mt,height:mt,x:i?e-10:e-8,y:n-Uo,style:{"pointer-events":i?"none":void 0}}),[U("div",{className:`${gt}-anchor ${i?"left":"right"} ${l?"has-edge":""}`,onClick:a=>{a.preventDefault(),a.stopPropagation(),d&&this.props.graphModel.$J.doAction("toggle-add-node-dialog",{node:this.props.model,anchor:t})},dangerouslySetInnerHTML:{__html:`${qo}${d?Zt:""}`}})])}}class Vo extends Be{getEdge(){const t=super.getEdge(),e=this.props.model.isHovered,{isSilentMode:n}=this.props.graphModel.editConfigModel;return!n&&e&&(t.props.stroke="#1890ff"),t}getAppendWidth(){return U("g",{},super.getAppendWidth(),this.getActionShape())}getActionShape(){const{startPoint:t,endPoint:e}=this.props.model;return U("foreignObject",{style:{},x:(t.x+e.x-rt)/2+5,y:(t.y+e.y-rt)/2+5,width:rt,height:rt},this.getActionRender())}getActionRender(){const{isSilentMode:t}=this.props.graphModel.editConfigModel,e=t?!1:this.props.model.isHovered;return U("div",{className:`${gt}-edge-action ${e?"hover":""}`,onClick:n=>{n.preventDefault(),n.stopPropagation(),this.props.graphModel.deleteEdgeById(this.props.model.id),this.props.graphModel.$J.repaintGraph()},dangerouslySetInnerHTML:{__html:Zt}})}getStartArrow(){return U("g",{})}getEndArrow(){return U("g",{})}toFront(){}}class zo extends Ge{initEdgeData(t){t.id||(t.id=jt()),super.initEdgeData(t)}getData(){return{id:this.id,type:this.type,sourceNodeId:this.sourceNodeId,targetNodeId:this.targetNodeId,sourceAnchorId:this.sourceAnchorId,targetAnchorId:this.targetAnchorId,pointsList:this.pointsList}}getEdgeStyle(){const t=super.getEdgeStyle();return t.stroke="#afafaf",t.strokeWidth=2,t}getOutlineStyle(){const t=super.getOutlineStyle();return t.stroke="none",t.hover&&(t.hover.stroke="none"),t}getEdgeAnimationStyle(){var t;const e=super.getEdgeAnimationStyle(),n=(t=this.properties)==null?void 0:t.runStatus;return n==="running"?e.stroke="#67b7ff":n==="success"?e.stroke="#52c41a":n==="fail"&&(e.stroke="#f5222d"),e}}const te={type:"base-edge",view:Vo,model:zo},ft={type:u.START,label:"开始",components:{icon:eo,setting:oo},lfNode:{type:u.START,view:H,model:O,component:io},params:{width:L,height:$},methods:{createNode:Jo,updateNodeSetting:Ko,getDefaultAnchor(o){return o.filter(t=>t.type==="right")}}},ee=()=>({inputParams:[{field:"content",name:"用户问题",type:"string",required:!0},{field:"history",name:"历史记录",type:"string[]",required:!0}]});function Jo(o){const t=ee();return{id:"start-node",type:ft.type,x:o.x,y:o.y,properties:{text:ft.label,remarks:"",options:{},inputParams:t.inputParams,outputParams:[]}}}function Ko(o){const{mergeIOParams:t}=Tt(o,ee);t()}const yt={type:u.END,label:"结束",components:{icon:no,setting:so},lfNode:{type:u.END,view:H,model:O,component:ro},params:{width:L,height:$},methods:{createNode:Yo,getDefaultAnchor(o){return o.filter(t=>t.type==="left")}}};function Yo(o){return{id:o.id,remarks:"",type:yt.type,x:o.x,y:o.y,properties:{text:yt.label,options:{outputText:!1,outputContent:""},inputParams:[],outputParams:[]}}}const Et={type:u.LLM,label:"LLM",components:{icon:ao,setting:lo},lfNode:{type:u.LLM,view:H,model:O,component:po},params:{width:L,height:$},methods:{createNode:Qo}};function Qo(o){var t;return{id:o.id,remarks:"",type:Et.type,x:o.x,y:o.y,properties:{text:(t=o.text)!=null?t:Et.label,options:{model:{modeId:"",params:{model:"",temperature:.7}},history:3,messages:[{role:"system",content:`**人物设定：**
- 你是：李白
- 人称：第一人称
- 出生地：安西都护府碎叶城
- 性格特点：豪放不羁、自信豁达、重情重义、浪漫洒脱
- 语言风格：富有想象力、优美流畅
- 经典台词：
  - “仰天大笑出门去，我辈岂是蓬蒿人。”
  - “天生我材必有用，千金散尽还复来。”`},{role:"user",content:""}]},inputParams:[],outputParams:[{field:"text",name:"回复内容",type:"string"}]}}}class Xo extends O{get $caseList(){return Jt(this)}}const Nt={type:u.CLASSIFIER,label:"分类器",components:{icon:co,setting:Xe},lfNode:{type:u.CLASSIFIER,view:H,model:Xo,component:ho},params:{width:L,height:$},methods:{createNode:Zo,getAnchorId:ut,getDefaultAnchor(o){const[t,e]=o,n=[t];if(!this.$caseList.length)return n;for(let s=0;s<this.$caseList.length;s++){const i=this.$caseList[s];n.push({id:ut(this.id,i.type,s+1),x:e.x,y:e.y+34+26*s,type:"right"})}return n}}};function Zo(o){var t;return{id:o.id,remarks:"",type:Nt.type,x:o.x,y:o.y,properties:{text:(t=o.text)!=null?t:Nt.label,options:{model:{modeId:"",params:{model:"",temperature:.7}},categories:[{category:"",next:""},{category:"",next:""}],else:{next:""}},inputParams:[],outputParams:[{field:"index",name:"分类索引",type:"number"},{field:"content",name:"分类内容",type:"string"}]}}}class ti extends O{get $caseList(){return Vt(this)}}const vt={type:u.SWITCH,label:"条件分支",components:{icon:uo,setting:Qe},lfNode:{type:u.SWITCH,view:H,model:ti,component:go},params:{width:L,height:$},methods:{createNode:ei,updateNodeSetting:oi,getAnchorId:ht,getDefaultAnchor(o){const[t,e]=o,n=[t];if(!this.$caseList.length)return n;for(let s=0;s<this.$caseList.length;s++){const i=this.$caseList[s];n.push({id:ht(this.id,i.type,s+1),x:e.x,y:e.y+34+26*s,type:"right"})}return n}}},oe=()=>({outputParams:[{field:"index",name:"分支索引",type:"number"}]});function ei(o){var t;const e=oe();return{id:o.id,remarks:"",type:vt.type,x:o.x,y:o.y,properties:{text:(t=o.text)!=null?t:vt.label,options:{if:[{logic:"AND",conditions:[{nodeId:"",field:"",operator:"EQUALS",value:""}],next:""}],else:{next:""}},inputParams:[],outputParams:e.outputParams}}}function oi(o){const{mergeIOParams:t}=Tt(o,oe);t()}const xt={type:u.KNOWLEDGE,label:"知识库",components:{icon:mo,setting:fo},lfNode:{type:u.KNOWLEDGE,view:H,model:O,component:yo},params:{width:L,height:$},methods:{createNode:ii}};function ii(o){var t;return{id:o.id,remarks:"",type:xt.type,x:o.x,y:o.y,properties:{text:(t=o.text)!=null?t:xt.label,options:{knowIds:[],topNumber:5,similarity:.7},inputParams:[],outputParams:[{field:"documents",name:"文档列表",type:"object[]"},{field:"data",name:"文档内容",type:"string"}]}}}class ni extends O{initNodeData(t){super.initNodeData(t),this.id.startsWith(u.CODE+"_")||(this.id=u.CODE+"_"+this.id)}}const It={type:u.CODE,label:"脚本执行",components:{icon:Eo,setting:No},lfNode:{type:u.CODE,view:H,model:ni,component:vo},params:{width:L,height:$},methods:{createNode:si}};function si(o){var t;return{id:o.id,remarks:"",type:It.type,x:o.x,y:o.y,properties:{text:(t=o.text)!=null?t:It.label,options:{codeType:"javascript",code:`
function main(params) {
  return {
    result: params.arg1 + '_拼接_' + params.arg2,
  }
}
`.trim()},inputParams:[{field:"",name:"arg1",nodeId:""},{field:"",name:"arg2",nodeId:""}],outputParams:[{field:"result",name:"返回结果",type:"string"}]}}}const St={type:u.SUBFLOW,label:"子流程",components:{icon:xo,setting:Io},lfNode:{type:u.SUBFLOW,view:H,model:O,component:So},params:{width:L,height:$},methods:{createNode:ri}};function ri(o){var t;return{id:o.id,remarks:"",type:St.type,x:o.x,y:o.y,properties:{text:(t=o.text)!=null?t:St.label,options:{subflowId:""},inputParams:[],outputParams:[]}}}const wt={type:u.ENHANCE_JAVA,label:"Java 增强",components:{icon:wo,setting:Co},lfNode:{type:u.ENHANCE_JAVA,view:H,model:O,component:ko},params:{width:L,height:$},methods:{createNode:ai}};function ai(o){var t;return{id:o.id,remarks:"",type:wt.type,x:o.x,y:o.y,properties:{text:(t=o.text)!=null?t:wt.label,options:{enhance:{type:"class",path:""}},inputParams:[{field:"",name:"arg1",nodeId:""},{field:"",name:"arg2",nodeId:""}],outputParams:[{field:"result",name:"返回结果",type:"string"}]}}}const Ct={type:u.HTTP,label:"HTTP 请求",components:{icon:To,setting:Ao},lfNode:{type:u.HTTP,view:H,model:O,component:bo},params:{width:L,height:$},methods:{createNode:di,updateNodeSetting:li}},ie=()=>({inputParams:[],outputParams:[{field:"body",name:"回复内容",type:"string"},{field:"statusCode",name:"状态码",type:"number"}]});function di(o){var t;const e=ie();return{id:o.id,remarks:"",type:Ct.type,x:o.x,y:o.y,properties:{text:(t=o.text)!=null?t:Ct.label,options:{http:{url:"",method:"GET",headers:{},requestBody:{type:"none",body:""},requestParams:{},timeout:120}},inputParams:e.inputParams,outputParams:e.outputParams}}}function li(o){const{mergeIOParams:t}=Tt(o,ie);t();const{requestBody:e}=o.properties.options.http;(!e||typeof e=="string")&&(o.properties.options.http.requestBody={type:"none",body:e||""})}const kt={type:u.REPLY,label:"直接回复",components:{icon:Po,setting:$o},lfNode:{type:u.REPLY,view:H,model:O,component:Lo},params:{width:L,height:$},methods:{createNode:pi}};function pi(o){var t;return{id:o.id,remarks:"",type:kt.type,x:o.x,y:o.y,properties:{text:(t=o.text)!=null?t:kt.label,options:{content:""},inputParams:[],outputParams:[]}}}const ci=te.type;function hi(o){Kt.clear(),Yt.clear(),Z.clear(),K.clear(),o.register(te);const t=ui(o);t(ft,!0),t(Et),t(Nt),t(kt),K.addDivider(),t(xt),K.addDivider(),t(vt),t(It),t(wt),t(Ct),K.addDivider(),t(St),K.addDivider(),t(yt)}function ui(o){return(t,e=!1)=>{Z.set(t.type,t),_e(t.lfNode,o),t.components.icon&&Kt.set(t.type,t.components.icon),t.components.setting&&Yt.set(t.type,t.components.setting),e||K.add(t.type)}}const ln=Te({__name:"WorkflowView",props:{data:{type:Object,default:()=>({})},width:{type:Number,default:2048},height:{type:Number,default:800}},emits:["save","close"],setup(o,{expose:t,emit:e}){const n=je(),s=o,i=e,d=Ze(),{prefixCls:l}=qt("airag-work-flow-box"),a=it(null),c=it(null),v=it(null),N=it(""),C=ct({isSilentMode:!1});Rt("lfRef",a),Rt("editConfigModel",C);const[k,D]=Bt(),[ne,M]=Bt(),At=()=>{b(M.getOpen)&&M.closeDrawer(),D.openDrawer(!0,{})},{SuperControl:Q}=_o(a);Ae(()=>{v.value&&(a.value=se({container:v.value}),window.addEventListener("keydown",Mt))});function se({container:r}){var h,p,g;a.value&&a.value.destroy();const f=new qe(at(X({container:r,width:s.width,height:s.height,edgeType:ci,textEdit:!1,adjustEdge:!1,adjustEdgeStartAndEnd:!1,background:{backgroundColor:"#f5f6f7"},grid:{size:10,type:"dot",config:{color:"#E0E0E0",thickness:1}}},jo()),{pluginsOptions:{[Q.pluginName]:{use:Q,debugRun:At,save:()=>i("save"),close:()=>i("close")}}}));if(hi(f),ae(f),(p=(h=s.data)==null?void 0:h.nodes)!=null&&p.length)for(const y of s.data.nodes){const E=Z.get(y.type);typeof((g=E==null?void 0:E.methods)==null?void 0:g.updateNodeSetting)=="function"&&E.methods.updateNodeSetting(y)}return f.render(s.data),nt(()=>le()),nt(()=>f.graphModel.$J.focusOnStartNode()),f}const j=new Map,lt=Je(()=>j.forEach(r=>r.events.onNodeUpdated()),50),re=function(r){const h=j.get(r);h&&h.events.onNodeUpdated()};function ae(r){r.on("graph:rendered",({graphModel:p})=>{N.value=p.flowId,me(p)}),r.on("node:click",p=>{r.graphModel.editConfigModel.isSilentMode||bt(p)});let h=null;r.on("node:dragstart",p=>{r.graphModel.editConfigModel.isSilentMode||(h=p.data)}),r.on("node:drop",p=>{if(r.graphModel.editConfigModel.isSilentMode||h==null)return;if(pe(p.e,E=>(E==null?void 0:E.dataset)&&E.dataset.hasOwnProperty("isNodeAction"))!=null){p.e.preventDefault(),p.e.stopPropagation();return}const g=Math.abs(p.data.x-h.x),f=Math.abs(p.data.y-h.y);h=null;const y=1;if(g<=y&&f<=y){p.e.preventDefault(),p.e.stopPropagation(),r.graphModel.selectNodeById(p.data.id),lt(),bt(p);return}}),r.on("node:delete",p=>ce(p)),r.on("node:properties-change",p=>{const g=j.get(p.id);if(!g)return;const f=g.node;Ue[f.type]&&p.id===f.id&&g.events.onPropertiesChange(p)}),r.on("graph:updated",p=>de())}function de(r){lt()}function le(r){const h=v.value.querySelector("div.lf-graph svg.lf-canvas-overlay");h&&h.addEventListener("click",function(p){if(p.target===h){he();return}})}function pe(r,h){if(typeof(r==null?void 0:r.composedPath)=="function"&&(r=r.composedPath()),!Array.isArray(r))return null;for(let p=0;p<r.length;p++){const g=r[p];if(g.classList.contains("lf-canvas-overlay"))return null;if(h(g))return g}return null}function bt(r){const h=j.get(r.data.id);h&&(Pt(h),h.events.onNodeClick(r))}let tt=null;function ce(r){const{model:h}=r;j.delete(h.id),b(M.getOpen)&&tt===h.id&&M.closeDrawer()}function Pt(r){return st(this,null,function*(){b(M.getOpen)&&tt===r.node.id||(b(D.getOpen)&&D.closeDrawer(),tt=r.node.id,M.closeDrawer(),yield nt(),M.openDrawer(!0,{node:r.node}))})}function he(){tt=null,b(M.getOpen)&&M.closeDrawer()}function ue(){const r=a.value;r&&r.graphModel.clearSelectElements()}function ge(r){return new Promise(h=>{function p(){const g=j.get(r);g?h(g):setTimeout(p,50)}p()})}function me(r){r.$J=at(X({},Go(a,r)),{on(...h){},register(h,p){j.set(h.id,{node:h,events:p})},doAction(h,p){if(!a.value)return;const g=a.value;if(h==="copy"){ue();const f=g.cloneNode(p.node.id);nt(()=>st(this,null,function*(){if(b(M.getOpen)){const y=yield ge(f.id);Pt(y)}lt()}))}else if(h==="delete")$t(p.node);else if(h==="toggle-add-node-dialog"){const f=j.get(p.node.id);if(!f)return;g.selectElementById(p.node.id),f.events.toggleAddNodeDialog(p)}else h==="add-node"&&Dt(p.nodeType,p.prevData)},updateEditConfig(h){a.value&&(Object.assign(C,h),a.value.updateEditConfig(h))},setEdgeRunStatus(h,p){var g;if(!a.value||h.type===u.START)return;const f=r.getNodeIncomingEdge(h.id);if(!(!f||f.length===0))for(const y of f)if(y.setProperties({runStatus:p}),p&&p!=="waiting"){const E=d.getStep(y.sourceNodeId);if((E==null?void 0:E.status)==="success"){const I=E.node.type===u.SWITCH?Ut:E.node.type===u.CLASSIFIER?zt:null;if(I){const w=(g=E.outputParams)==null?void 0:g[`${E.node.id}.index`];if(typeof w=="number"&&I.getAnchorIdByChooseIndex(E.node.id,w)!==y.sourceAnchorId)continue}y.openEdgeAnimation()}}else y.closeEdgeAnimation()}})}function $t(r){return st(this,null,function*(){fe(r.id)})}function fe(r){r!=="start-node"&&a.value&&a.value.deleteNode(r)}function ye(r){r!=null&&r.id&&re(r.id)}function Ee(r,h){if(!a.value)return;const p=a.value.getNodeModelById(r);p&&p.setProperties({text:h})}function Lt(r,h,p,g){return{x:r,y:h,width:p,height:g,left:r-p/2,right:r+p/2,top:h-g/2,bottom:h+g/2}}function Dt(r,h){var p,g;if(!a.value)return;const f=a.value,y=Z.get(r);if(!y)return;const E={};let I=null;if(h!=null&&h.node){const S=h.node,x=h.anchor,V=(p=x==null?void 0:x.x)!=null?p:S.x,G=(g=x==null?void 0:x.y)!=null?g:S.y,W=x?120:S.width;E.x=V+W+200,E.y=G,I={type:"base-edge",sourceNodeId:S.id,targetNodeId:""},x!=null&&x.id&&(I.sourceAnchorId=x.id)}else{const S=f.getTransform(),x=f.container,V=x.clientWidth,G=x.clientHeight,W=V/2,_=G/2,P=(W-S.TRANSLATE_X)/S.SCALE_X,z=(_-S.TRANSLATE_Y)/S.SCALE_Y;E.x=P,E.y=z}const{methods:w}=y,T=w.createNode(E);let q={up:{y:0},down:{y:0}};const A=(S,x)=>{if(x>999)return;const V=[...f.graphModel.nodes],G=20,W=x%2===0?"up":"down";let _=q[W].y;if(_=_||S.y,V.length>0){let P=G*1.5;y.type===u.CLASSIFIER?P=W==="up"?P+80:P:y.type===u.SWITCH?P=W==="up"?P+60:P:y.type===u.LLM&&(P=W==="up"?P+40:P);const z=Lt(S.x,_,y.params.width,y.params.height),Ie=V.map(F=>Lt(F.x,F.y,F.width,F.height));let J=null;for(const F of Ie){const Se=Math.abs(F.left-z.left),we=Math.abs(F.right-z.right);if(!(Se<G||we<G))continue;const Ce=Math.abs(F.top-z.top),ke=Math.abs(F.bottom-z.bottom);(Ce<G||ke<G)&&(J=F)}J?(W==="up"?_=J.y-J.height-P:_=J.y+J.height+P,q[W].y=_,A(S,x+1)):S.y=_}};A(T,0);const R=f.addNode(T);return I&&f.addEdge(at(X({},I),{targetNodeId:R.id})),R}function Ot(){if(a.value)return a.value.getGraphData()}const et=new Map;function Ne(){if(!a.value)return"";const r=a.value.getGraphRawData();return Bo(r,et)}function ve(){const r=Ot();if(!r)throw new Error("尚未初始化");const{nodes:h,edges:p}=r;if(h.length===0)throw new Error("请添加节点");et.clear();const g=[u.END,u.SWITCH,u.CLASSIFIER],f=new Map;for(const I of h)if(g.includes(I.type)){let w=f.get(I.type);Array.isArray(w)||(w=[]),w.push(I),f.set(I.type,w)}if(!f.has(u.END))throw new Error("请添加结束节点");const y=f.get(u.SWITCH);if(Array.isArray(y)&&y.length>0)for(const I of y){const{text:w,options:T}=I.properties;if(!Array.isArray(T==null?void 0:T.if)||T.if.length===0)throw new Error(`${w} 节点的“IF”分支条件不能为空`);const q=Vt(I);for(let A=0;A<q.length;A++){const R=q[A],S=ht(I.id,R.type,A+1),x=Ht(S,p);if(x.length===0)throw new Error(`${w} 节点的“${R.label}”分支未连接下一个节点`);R.type==="ELSE"?T.else.next=x[0]:T.if[A].next=x[0],et.set(S,x)}}const E=f.get(u.CLASSIFIER);if(Array.isArray(E)&&E.length>0)for(const I of E){const{text:w,options:T}=I.properties;if(!Array.isArray(T==null?void 0:T.categories)||T.categories.length===0)throw new Error(`${w} 节点的分类不能为空`);const q=Jt(I);for(let A=0;A<q.length;A++){const R=q[A],S=ut(I.id,R.type,A+1);let x=Ht(S,p);if(x.length===0)throw new Error(`${w} 节点的“${R.label}”未连接下一个节点`);R.type==="ELSE"?T.else.next=x[0]:T.categories[A].next=x[0],et.set(S,x)}}return r}function Ht(r,h){const p=h.filter(g=>g.sourceAnchorId===r);return!p||p.length===0?[]:p.map(g=>g.targetNodeId)}function xe(r){return st(this,null,function*(){a.value&&(yield r(a.value))})}function Mt(r){var h;if(r.key==="s"&&r.ctrlKey)return i("save"),ot(r);const p=r.target,g=(h=p==null?void 0:p.tagName)==null?void 0:h.toLowerCase(),f=g==="input"||g==="textarea"||(p==null?void 0:p.contentEditable)==="true";xe(y=>{if(!f){if(r.key==="z"&&r.ctrlKey)return y.undo(),ot(r);if(r.key==="y"&&r.ctrlKey)return y.redo(),ot(r);if(r.key==="Delete"){const E=y.graphModel.selectNodes;return E.length>0&&$t(E[0]),ot(r)}}})}function ot(r){r.preventDefault(),r.stopPropagation()}return Pe(()=>{a.value&&a.value.destroy(),window.removeEventListener("keydown",Mt)}),t({getLogicFlow:()=>a.value,doDebugRun:At,updateText:Ee,addNode:Dt,getGraphData:Ot,validateData:ve,getLiteFlowData:Ne}),(r,h)=>(Wt(),be($e,null,[Ft("div",{ref_key:"containerRef",ref:c,class:Oe([b(l)])},[Ft("div",{ref_key:"renderRef",ref:v},null,512),pt(b(n),{"flow-id":N.value},null,8,["flow-id"])],2),pt(Ke,{onRegister:b(k)},null,8,["onRegister"]),pt(Ye,{onRegister:b(ne),onUpdate:ye},null,8,["onRegister"]),b(Q).componentProps.visible?(Wt(),Le(He(b(Q).componentIs),Me(Re({key:0},b(Q).superControlProps)),null,16)):De("",!0)],64))}});export{ln as p,Jo as z};
