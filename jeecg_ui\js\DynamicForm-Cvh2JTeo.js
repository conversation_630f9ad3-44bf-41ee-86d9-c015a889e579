var u=(e,o,l)=>new Promise((m,s)=>{var c=t=>{try{r(l.next(t))}catch(n){s(n)}},i=t=>{try{r(l.throw(t))}catch(n){s(n)}},r=t=>t.done?m(t.value):Promise.resolve(t.value).then(c,i);r((l=l.apply(e,o)).next())});import{d as h,ag as d,aB as C,ar as g,aD as a,at as _,k as p,G as f}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{C as k}from"./index-LCGLvkB3.js";import{P as F}from"./index-CtJ0w2CP.js";import{X as w,a as W}from"./index-CCWaWN5g.js";import{B}from"./BasicForm-DBcXiHk0.js";import{u as b}from"./useForm-CgkFTrrO.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const{hasPermission:S}=w(),P=[{field:"field5",component:"Switch",label:"是否显示字段1(css控制)",defaultValue:!0,colProps:{span:12},labelWidth:200},{field:"field1",component:"Input",label:"字段1",colProps:{span:12},show:({values:e})=>S("test001")},{field:"field6",component:"Switch",label:"是否显示字段2(dom控制)",defaultValue:!0,colProps:{span:12},labelWidth:200},{field:"field2",component:"Input",label:"字段2",colProps:{span:12},ifShow:({values:e})=>!!e.field6},{field:"field7",component:"Switch",label:"是否禁用字段3",colProps:{span:12},labelWidth:200},{field:"field3",component:"DatePicker",label:"字段3",colProps:{span:12},dynamicDisabled:({values:e})=>!!e.field7},{field:"field8",component:"Switch",label:"字段4是否必填",colProps:{span:12},labelWidth:200},{field:"field4",component:"Select",label:"字段4",colProps:{span:12},dynamicRules:({values:e})=>e.field8?[{required:!0,message:"字段必填"}]:[],componentProps:{options:[{label:"选项1",value:"1",key:"1"},{label:"选项2",value:"2",key:"2"}]}},{field:"field11",component:"DatePicker",label:"字段11",colProps:{span:8}}],v=[{field:"f1",component:"Input",label:"F1",colProps:{span:12},labelWidth:200,componentProps:({formModel:e})=>({placeholder:"同步f2的值为f1",onChange:o=>{e.f2=o.target.value}})},{field:"f2",component:"Input",label:"F2",colProps:{span:12},labelWidth:200,componentProps:{disabled:!0}},{field:"f3",component:"Input",label:"F3",colProps:{span:12},labelWidth:200,componentProps:({formActionType:e})=>({placeholder:"值改变时执行查询,查看控制台",onChange:()=>u(null,null,function*(){const{validate:o}=e,l=yield o()})})}],y=h({components:{BasicForm:B,CollapseContainer:k,PageWrapper:F},setup(){const[e,{setProps:o,updateSchema:l,appendSchemaByField:m,removeSchemaByFiled:s}]=b({labelWidth:120,schemas:P,disabled:!0,labelCol:{xs:{span:24},sm:{span:6}},wrapperCol:{xs:{span:24},sm:{span:18}},actionColOptions:{span:24}}),[c]=b({labelWidth:120,schemas:v,actionColOptions:{span:24}});function i(){l({field:"field3",label:"字段3 New"})}function r(){l([{field:"field3",label:"字段3 New++"},{field:"field4",label:"字段4 New++"}])}function t(){m({field:"field10",label:"字段10",component:"Input",colProps:{span:8}},"field3")}function n(){s("field11")}return{register:e,register1:c,schemas:P,setProps:o,changeLabel3:i,changeLabel34:r,appendField:t,deleteField:n}}}),I={class:"mb-4"};function N(e,o,l,m,s,c){const i=d("a-button"),r=d("BasicForm"),t=d("CollapseContainer"),n=d("PageWrapper");return g(),C(n,{title:"动态表单示例"},{default:a(()=>[_("div",I,[p(i,{onClick:e.changeLabel3,class:"mr-2"},{default:a(()=>o[0]||(o[0]=[f(" 更改字段3label ")])),_:1,__:[0]},8,["onClick"]),p(i,{onClick:e.changeLabel34,class:"mr-2"},{default:a(()=>o[1]||(o[1]=[f(" 同时更改字段3,4label ")])),_:1,__:[1]},8,["onClick"]),p(i,{onClick:e.appendField,class:"mr-2"},{default:a(()=>o[2]||(o[2]=[f(" 往字段3后面插入字段10 ")])),_:1,__:[2]},8,["onClick"]),p(i,{onClick:e.deleteField,class:"mr-2"},{default:a(()=>o[3]||(o[3]=[f(" 删除字段11 ")])),_:1,__:[3]},8,["onClick"])]),p(t,{title:"动态表单示例,动态根据表单内其他值改变"},{default:a(()=>[p(r,{onRegister:e.register},null,8,["onRegister"])]),_:1}),p(t,{class:"mt-5",title:"componentProps动态改变"},{default:a(()=>[p(r,{onRegister:e.register1},null,8,["onRegister"])]),_:1})]),_:1})}const qe=W(y,[["render",N]]);export{qe as default};
