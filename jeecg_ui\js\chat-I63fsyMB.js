var be=Object.defineProperty;var ue=Object.getOwnPropertySymbols;var Be=Object.prototype.hasOwnProperty,Le=Object.prototype.propertyIsEnumerable;var ce=(n,i,a)=>i in n?be(n,i,{enumerable:!0,configurable:!0,writable:!0,value:a}):n[i]=a,m=(n,i)=>{for(var a in i||(i={}))Be.call(i,a)&&ce(n,a,i[a]);if(ue)for(var a of ue(i))Le.call(i,a)&&ce(n,a,i[a]);return n};var T=(n,i,a)=>new Promise((U,Q)=>{var _=y=>{try{d(a.next(y))}catch(w){Q(w)}},S=y=>{try{d(a.throw(y))}catch(w){Q(w)}},d=y=>y.done?U(y.value):Promise.resolve(y.value).then(_,S);d((a=a.apply(n,i)).next())});import{f as c,n as X,d as Oe,e as de,b as Me,w as W,o as Ee,ag as q,aq as M,ar as h,at as r,ah as C,au as He,F as pe,aC as ve,aB as z,k,aD as E,as as fe,u as he}from"./vue-vendor-dy9k-Yad.js";import Re from"./chatMessage-DcTiQunt.js";import Ve from"./presetQuestion-CYEhQsHK.js";import{f as I,h as Ae,i as qe,M as ze,bh as Qe}from"./antd-vue-vendor-me9YkNVC.js";import"./chatText-DGPEwQvb.js";import{aC as Ne,aE as Ue,j as Z,d as Fe,aL as je,a as We}from"./index-CCWaWN5g.js";import"./ailogo-DG2_TD5d.js";import"./vxe-table-vendor-B22HppNm.js";function $e(){const n=c(null);return{scrollRef:n,scrollToBottom:()=>T(null,null,function*(){yield X(),n.value&&(n.value.scrollTop=n.value.scrollHeight)}),scrollToTop:()=>T(null,null,function*(){yield X(),n.value&&(n.value.scrollTop=0)}),scrollToBottomIfAtBottom:()=>T(null,null,function*(){yield X(),n.value&&n.value.scrollHeight-n.value.scrollTop-n.value.clientHeight<=100&&(n.value.scrollTop=n.value.scrollHeight)})}}const Je={class:"chatWrap"},Ye={class:"content"},Pe={key:0,class:"header-title"},Ge={class:"main"},Ke={key:0,class:"chatContentArea"},Xe={class:"footer"},Ze={class:"topArea"},et={class:"bottomArea"},tt={key:0,class:"textarea-top"},at=["src","onClick"],ot=["onClick"],nt={class:"textarea-bottom"},lt=Oe({__name:"chat",props:["uuid","prologue","formState","url","type","historyData","chatTitle","presetQuestion","quickCommandData"],emits:["save","reload-message-title"],setup(n,{emit:i}){I.config({prefixCls:"ai-chat-message"});const a=n,U=i,{scrollRef:Q,scrollToBottom:_}=$e(),S=c(""),d=c(!1),y=c(null),w=c(a.chatTitle),l=c([]),g=c({}),H=c(!0),u=c(a.uuid),D=c(""),F=c(""),ee=de(()=>l.value.filter(e=>e.inversion!="user"&&!!e.conversationOptions)),ge=de(()=>"来说点什么吧...（Shift + Enter = 换行）"),me=Ne(),$=c(!1);function ye(e){e.key==="Enter"&&!e.shiftKey&&(e.preventDefault(),te())}function te(){let e=S.value;!e||e.trim()===""||(S.value="",oe(e))}const ae=e=>{oe(e)};function oe(e){return T(this,null,function*(){var v;if(!a.type&&a.type!="view"){if(g.value.type&&g.value.type=="chatSimple"&&!g.value.modelId){P("请选择AI模型");return}if(g.value.type&&g.value.type=="chatFLow"&&!g.value.flowId){P("请选择关联流程");return}if(!g.value.name){P("请填写应用名称");return}}if(d.value)return;d.value=!0,ne(u.value,{dateTime:new Date().toLocaleString(),content:e,images:p.value?p.value:[],inversion:"user",error:!1,conversationOptions:null,requestOptions:{prompt:e,options:null}}),_();let t={};const o=(v=ee.value[ee.value.length-1])==null?void 0:v.conversationOptions;o&&H.value&&(t=m({},o)),ne(u.value,{dateTime:new Date().toLocaleString(),content:"思考中...",loading:!0,inversion:"ai",error:!1,conversationOptions:null,requestOptions:{prompt:e,options:m({},t)}}),_(),Te(e,t)})}Me(()=>{le(u.value,l.value.length-1,{loading:!1})});const ne=(e,t)=>{l.value.push(m({},t))},J=(e,t,o)=>T(null,null,function*(){l.value.splice(t,1,o),yield _()}),j=e=>{let t={content:e,key:"prologue",loading:!1,dateTime:qe().format("YYYY/MM/DD HH:mm:ss"),inversion:"ai",presetQuestion:a.presetQuestion?JSON.parse(a.presetQuestion):""};if(l.value&&l.value.length>0&&l.value[0].key==="prologue"){l.value[0]=m({},t);return}l.value.unshift(m({},t))},le=(e,t,o)=>{l.value[t]=m(m({},l.value[t]),o)},Y=(e,t,o)=>{J(e.value,l.value.length-1,{dateTime:new Date().toLocaleString(),content:o,inversion:"ai",error:!1,loading:!0,conversationOptions:null,requestOptions:null}),_()};function we(){ze.confirm({title:"清空会话",icon:k(Qe),content:"是否清空会话?",closable:!0,okText:"确定",cancelText:"取消",wrapClassName:"ai-chat-modal",onOk(){return T(this,null,function*(){try{Z.get({url:"/airag/chat/messages/clear/"+u.value},{isTransformResponse:!1}).then(t=>{t.success&&(l.value=[],D.value="",a.prologue&&j(a.prologue))})}catch(t){return}})}})}const b=()=>{d.value&&(d.value=!1),le(u,l.value.length-1,{loading:!1})};b();function xe(){F.value&&Z.get({url:"/airag/chat/stop/"+F.value},{isTransformResponse:!1}),b()}function Te(e,t){return T(this,null,function*(){let o={};!a.type&&a.type!="view"?o={content:e,images:p.value?p.value:[],topicId:D.value,app:g.value,responseMode:"streaming"}:(o={content:e,topicId:H.value?D.value:"",images:p.value?p.value:[],appId:g.value.id,responseMode:"streaming",conversationId:u.value==="1002"?"":u.value},w.value=="新建聊天"&&(w.value=e.length>5?e.substring(0,5):e),U("reload-message-title",e.length>5?e.substring(0,5):e)),p.value=[],R.value=[];const B=(yield Z.post({url:a.url,params:o,adapter:"fetch",responseType:"stream",timeout:5*60*1e3},{isTransformResponse:!1}).catch(L=>{Y(u,l.value.length-1,"服务器错误，请稍后重试！"),b()})).getReader(),f=new TextDecoder("UTF-8");let V="",x="",s="";for(;;){const{done:L,value:G}=yield B.read();if(L)break;let K=f.decode(G,{stream:!0});K=x+K;const De=K.split(`

`);for(const O of De)if(O.startsWith("data:")){const A=O.replace("data:","").trim();if(!A)continue;if(!A.endsWith("}")){x=x+O;continue}x="";try{let N=JSON.parse(A);yield se(N,V,s,t).then(ie=>{s=ie.returnText,V=ie.conversationId})}catch(N){}}else{if(!O)continue;if(!O.endsWith("}")){x=x+O;continue}x="";try{let A=JSON.parse(O);yield se(A,V,s,t).then(N=>{s=N.returnText,V=N.conversationId})}catch(A){}}}})}const _e=()=>{H.value=!H.value,H.value?I.success("当前模式下, 发送消息会携带之前的聊天记录"):I.warning("当前模式下, 发送消息不会携带之前的聊天记录")};function P(e){I.warning(e)}function se(e,t,o,v){return T(this,null,function*(){let B="";if(e.event=="MESSAGE"&&(o=o+e.data.message,B=o,J(u.value,l.value.length-1,{dateTime:new Date().toLocaleString(),content:o,inversion:"ai",error:!1,loading:!0,conversationOptions:{conversationId:t,parentMessageId:D.value},requestOptions:{prompt:I,options:m({},v)}})),e.event=="MESSAGE_END"&&(D.value=e.topicId,t=e.conversationId,u.value=e.conversationId,F.value=e.requestId,b()),e.event=="FLOW_FINISHED"){if(e.data&&!e.data.success)return Y(u,l.value.length-1,e.data.message?e.data.message:"请求出错，请稍后重试！"),b(),"";D.value=e.topicId,t=e.conversationId,u.value=e.conversationId,F.value=e.requestId,b()}if(e.event=="ERROR")return Y(u,l.value.length-1,e.data.message?e.data.message:"请求出错，请稍后重试！"),b(),"";if(e.event==="NODE_STARTED"){let f="";e.data.type==="llm"&&(f="正在构建响应内容"),e.data.type==="knowledge"&&(f="正在对知识库进行深度检索"),e.data.type==="classifier"&&(f="正在分类"),e.data.type==="code"&&(f="正在实施代码运行操作"),e.data.type==="subflow"&&(f="正在运行子流程"),e.data.type==="enhanceJava"&&(f="正在执行java增强"),e.data.type==="http"&&(f="正在发送http请求"),J(u.value,l.value.length-1,{dateTime:new Date().toLocaleString(),content:f,inversion:"ai",error:!1,loading:!0,conversationOptions:null,requestOptions:{prompt:I,options:m({},v)}})}return{returnText:B,conversationId:t}})}const p=c([]),R=c([]);function Ce(e){var v;let{fileList:t,file:o}=e;R.value=t,o.status==="error"&&I.error(((v=o.response)==null?void 0:v.message)||`${o.name} 上传失败,请查看服务端日志`),o.status==="done"&&p.value.push(o.response.message)}function re(e){return Fe(e)}function ke(e){var t=e.type;return t==="image"&&t.indexOf("image")<0?(I.warning("请上传图片"),!1):!0}function Ie(e){p.value.splice(e,1),R.value.splice(e,1)}function Se(e){const t=({index:v,url:B,dom:f})=>{};let o=[re(e)];je({imageList:o,defaultWidth:700,rememberState:!0,onImgLoad:t})}return W(()=>a.prologue,e=>{try{e&&j(e)}catch(t){}}),W(()=>a.presetQuestion,e=>{j(a.prologue)}),W(()=>a.formState,e=>{try{e&&(g.value=e)}catch(t){}},{deep:!0,immediate:!0}),W(()=>a.historyData,e=>{try{e&&e.length>0?(l.value=Ae(e),l.value[0]&&(D.value=l.value[0].topicId)):(l.value=[],w.value=a.chatTitle),a.prologue&&a.chatTitle&&j(a.prologue)}catch(t){}},{deep:!0,immediate:!0}),Ee(()=>{_(),p.value=[],R.value=[]}),(e,t)=>{const o=q("a-button"),v=q("Icon"),B=q("a-textarea"),f=q("a-tooltip"),V=q("a-upload"),x=q("a-divider");return h(),M("div",Je,[r("div",Ye,[n.type==="view"&&w.value?(h(),M("div",Pe,He(w.value),1)):C("",!0),r("div",Ge,[r("div",{id:"scrollRef",ref_key:"scrollRef",ref:Q,class:"scrollArea"},[l.value.length>0?(h(),M("div",Ke,[(h(!0),M(pe,null,ve(l.value,(s,L)=>(h(),z(Re,{key:L,"date-time":s.dateTime||s.datetime,text:s.content,inversion:s.inversion||s.role,error:s.error,loading:s.loading,appData:g.value,presetQuestion:s.presetQuestion,images:s.images,onSend:ae},null,8,["date-time","text","inversion","error","loading","appData","presetQuestion","images"]))),128))])):C("",!0)],512)]),r("div",Xe,[r("div",Ze,[k(Ve,{onOutQuestion:ae,quickCommandData:n.quickCommandData},null,8,["quickCommandData"])]),r("div",et,[k(o,{type:"text",class:"delBtn",onClick:t[0]||(t[0]=s=>we())},{default:E(()=>t[6]||(t[6]=[r("svg",{t:"1706504908534",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1584",width:"18",height:"18"},[r("path",{d:"M816.872727 158.254545h-181.527272V139.636364c0-39.563636-30.254545-69.818182-69.818182-69.818182h-107.054546c-39.563636 0-69.818182 30.254545-69.818182 69.818182v18.618181H207.127273c-48.872727 0-90.763636 41.890909-90.763637 93.09091s41.890909 90.763636 90.763637 90.763636h609.745454c51.2 0 90.763636-41.890909 90.763637-90.763636 0-51.2-41.890909-93.090909-90.763637-93.09091zM435.2 139.636364c0-13.963636 9.309091-23.272727 23.272727-23.272728h107.054546c13.963636 0 23.272727 9.309091 23.272727 23.272728v18.618181h-153.6V139.636364z m381.672727 155.927272H207.127273c-25.6 0-44.218182-20.945455-44.218182-44.218181 0-25.6 20.945455-44.218182 44.218182-44.218182h609.745454c25.6 0 44.218182 20.945455 44.218182 44.218182 0 23.272727-20.945455 44.218182-44.218182 44.218181zM835.490909 407.272727h-121.018182c-13.963636 0-23.272727 9.309091-23.272727 23.272728s9.309091 23.272727 23.272727 23.272727h97.745455V837.818182c0 39.563636-30.254545 69.818182-69.818182 69.818182h-37.236364V602.763636c0-13.963636-9.309091-23.272727-23.272727-23.272727s-23.272727 9.309091-23.272727 23.272727V907.636364h-118.690909V602.763636c0-13.963636-9.309091-23.272727-23.272728-23.272727s-23.272727 9.309091-23.272727 23.272727V907.636364H372.363636V602.763636c0-13.963636-9.309091-23.272727-23.272727-23.272727s-23.272727 9.309091-23.272727 23.272727V907.636364h-34.909091c-39.563636 0-69.818182-30.254545-69.818182-69.818182V453.818182H558.545455c13.963636 0 23.272727-9.309091 23.272727-23.272727s-9.309091-23.272727-23.272727-23.272728H197.818182c-13.963636 0-23.272727 9.309091-23.272727 23.272728V837.818182c0 65.163636 51.2 116.363636 116.363636 116.363636h451.490909c65.163636 0 116.363636-51.2 116.363636-116.363636V430.545455c0-13.963636-11.636364-23.272727-23.272727-23.272728z",fill:"currentColor","p-id":"1585"})],-1)])),_:1,__:[6]}),n.type==="view"?(h(),z(o,{key:0,type:"text",class:fe(["contextBtn",[H.value&&"enabled"]]),onClick:_e},{default:E(()=>t[7]||(t[7]=[r("svg",{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","aria-hidden":"true",role:"img",class:"iconify iconify--ri",width:"20",height:"20",viewBox:"0 0 24 24"},[r("path",{fill:"currentColor",d:"M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10a9.956 9.956 0 0 1-4.708-1.175L2 22l1.176-5.29A9.956 9.956 0 0 1 2 12C2 6.477 6.477 2 12 2m0 2a8 8 0 0 0-8 8c0 1.335.326 2.618.94 3.766l.35.654l-.656 2.946l2.948-.654l.653.349A7.955 7.955 0 0 0 12 20a8 8 0 1 0 0-16m1 3v5h4v2h-6V7z"})],-1)])),_:1,__:[7]},8,["class"])):C("",!0),r("div",{class:fe(["chat-textarea",$.value?"textarea-active":""])},[p.value&&p.value.length>0?(h(),M("div",tt,[(h(!0),M(pe,null,ve(p.value,(s,L)=>(h(),M("div",{class:"top-image",key:L},[r("img",{src:re(s),onClick:G=>Se(s)},null,8,at),r("div",{class:"upload-icon",onClick:G=>Ie(L)},[k(v,{icon:"ant-design:close-outlined",size:"12px"})],8,ot)]))),128))])):C("",!0),r("div",nt,[k(B,{ref_key:"inputRef",ref:y,value:S.value,"onUpdate:value":t[1]||(t[1]=s=>S.value=s),autoSize:{minRows:1,maxRows:6},placeholder:ge.value,onPressEnter:ye,onFocus:t[2]||(t[2]=s=>$.value=!0),onBlur:t[3]||(t[3]=s=>$.value=!1),autofocus:"",readonly:d.value,style:{"border-color":"#ffffff !important","box-shadow":"none"}},null,8,["value","placeholder","readonly"]),d.value?(h(),z(o,{key:0,type:"primary",danger:"",onClick:xe,class:"stopBtn"},{default:E(()=>t[8]||(t[8]=[r("svg",{t:"1706148514627",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"5214",width:"18",height:"18"},[r("path",{d:"M512 967.111111c-250.311111 0-455.111111-204.8-455.111111-455.111111s204.8-455.111111 455.111111-455.111111 455.111111 204.8 455.111111 455.111111-204.8 455.111111-455.111111 455.111111z m0-56.888889c221.866667 0 398.222222-176.355556 398.222222-398.222222s-176.355556-398.222222-398.222222-398.222222-398.222222 176.355556-398.222222 398.222222 176.355556 398.222222 398.222222 398.222222z",fill:"currentColor","p-id":"5215"}),r("path",{d:"M341.333333 341.333333h341.333334v341.333334H341.333333z",fill:"currentColor","p-id":"5216"})],-1)])),_:1,__:[8]})):C("",!0),d.value?C("",!0):(h(),z(V,{key:1,accept:".jpg,.jpeg,.png",name:"file","file-list":R.value,"onUpdate:fileList":t[4]||(t[4]=s=>R.value=s),showUploadList:!1,headers:he(me),beforeUpload:ke,onChange:Ce,multiple:!0,action:he(Ue),"max-count":3},{default:E(()=>[k(f,{title:"图片上传，支持jpg/jpeg/png"},{default:E(()=>[k(o,{class:"sendBtn",type:"text"},{default:E(()=>[k(v,{icon:"ant-design:picture-outlined",style:{color:"rgba(15,21,40,0.8)"}})]),_:1})]),_:1})]),_:1},8,["file-list","headers","action"])),d.value?C("",!0):(h(),z(x,{key:2,type:"vertical",style:{"border-color":"#38374314"}})),d.value?C("",!0):(h(),z(o,{key:3,onClick:t[5]||(t[5]=()=>{te()}),disabled:!S.value,class:"sendBtn",type:"text"},{default:E(()=>t[9]||(t[9]=[r("svg",{t:"1706147858151",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"4237",width:"1em",height:"1em"},[r("path",{d:"M865.28 202.5472c-17.1008-15.2576-41.0624-19.6608-62.5664-11.5712L177.7664 427.1104c-23.2448 8.8064-38.5024 29.696-39.6288 54.5792-1.1264 24.8832 11.9808 47.104 34.4064 58.0608l97.5872 47.7184c4.5056 2.2528 8.0896 6.0416 9.9328 10.6496l65.4336 161.1776c7.7824 19.1488 24.4736 32.9728 44.7488 37.0688 20.2752 4.096 41.0624-2.1504 55.6032-16.7936l36.352-36.352c6.4512-6.4512 16.5888-7.8848 24.576-3.3792l156.5696 88.8832c9.4208 5.3248 19.8656 8.0896 30.3104 8.0896 8.192 0 16.4864-1.6384 24.2688-5.0176 17.8176-7.68 30.72-22.8352 35.4304-41.6768l130.7648-527.1552c5.5296-22.016-1.7408-45.2608-18.8416-60.416z m-20.8896 50.7904L713.5232 780.4928c-1.536 6.2464-5.8368 11.3664-11.776 13.9264s-12.5952 2.1504-18.2272-1.024L526.9504 704.512c-9.4208-5.3248-19.8656-7.9872-30.208-7.9872-15.9744 0-31.744 6.144-43.52 17.92l-36.352 36.352c-3.8912 3.8912-8.9088 5.9392-14.2336 6.0416l55.6032-152.1664c0.512-1.3312 1.2288-2.56 2.2528-3.6864l240.3328-246.1696c8.2944-8.4992-2.048-21.9136-12.3904-16.0768L301.6704 559.8208c-4.096-3.584-8.704-6.656-13.6192-9.1136L190.464 502.9888c-11.264-5.5296-11.5712-16.1792-11.4688-19.3536 0.1024-3.1744 1.536-13.824 13.2096-18.2272L817.152 229.2736c10.4448-3.9936 18.0224 1.3312 20.8896 3.8912 2.8672 2.4576 9.0112 9.3184 6.3488 20.1728z","p-id":"4238",fill:"currentColor"})],-1)])),_:1,__:[9]},8,["disabled"]))])],2)])])])])}}}),ht=We(lt,[["__scopeId","data-v-63a0517d"]]);export{ht as default};
