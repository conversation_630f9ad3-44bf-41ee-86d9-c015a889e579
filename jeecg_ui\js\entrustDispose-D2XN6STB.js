import{u as r,j as e}from"./index-CCWaWN5g.js";const{createConfirm:n}=r();const u=t=>e.post({url:"/hgy/entrustService/hgyAssetEntrust/queryPageAll",params:t}),y=t=>e.get({url:"/hgy/entrustService/hgyAssetEntrust/queryEntrustById",params:{id:t}});const g=t=>e.delete({url:`/hgy/entrustService/hgyAssetEntrust/customEntrustDelete?id=${t}`});const h="/hgy/entrustService/hgyAssetEntrust/exportXls";export{u as a,g as c,h as g,y as q};
