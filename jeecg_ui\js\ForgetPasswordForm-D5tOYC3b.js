import{d as S,f as m,r as u,e as g,u as b,ag as l,aq as k,ah as o,ar as a,F as h,k as r,at as x,aD as F,aB as p}from"./vue-vendor-dy9k-Yad.js";import{bO as B,bP as y,bU as N}from"./index-CCWaWN5g.js";import P from"./step1-CkQhLHRg.js";import R from"./step2-tEElob-Y.js";import C from"./step3-dZT9Navo.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CBCjSSNZ.js";const nt=S({__name:"ForgetPasswordForm",setup(E){const{handleBackLogin:L,getLoginState:f}=B(),{getFormRules:j}=y(),w=m(),D=m(!1),t=m(0),V=u({account:"",mobile:"",sms:""}),v=g(()=>b(f)===N.RESET_PASSWORD),n=u({obj:{username:"",phone:"",smscode:""}});function i(e){n.obj=e,t.value<4&&(t.value+=1)}function c(e){n.obj=e,t.value>0&&(t.value-=1)}function _(){t.value=0}return(e,I)=>{const s=l("a-step"),d=l("a-steps");return v.value?(a(),k(h,{key:0},[r(d,{style:{"margin-bottom":"20px"},current:t.value},{default:F(()=>[r(s,{title:"手机验证"}),r(s,{title:"更改密码"}),r(s,{title:"完成"})]),_:1},8,["current"]),x("div",null,[t.value===0?(a(),p(P,{key:0,onNextStep:i})):o("",!0),t.value===1?(a(),p(R,{key:1,onNextStep:i,onPrevStep:c,accountInfo:n},null,8,["accountInfo"])):o("",!0),t.value===2?(a(),p(C,{key:2,onPrevStep:c,onFinish:_})):o("",!0)])],64)):o("",!0)}}});export{nt as default};
