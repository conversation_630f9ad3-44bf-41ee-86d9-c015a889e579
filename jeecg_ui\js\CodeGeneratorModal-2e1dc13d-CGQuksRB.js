import{d as ne,f as T,e as I,r as k,n as me,ag as n,aq as pe,ar as B,F as ce,k as m,aD as s,aB as V,ah as G,G as J,at as K,aE as de}from"./vue-vendor-dy9k-Yad.js";import{cs as ue,F as fe,bx as x,ac as ge,j as A,cK as q,ad as U}from"./index-CCWaWN5g.js";import"./index-L3cSIXth.js";import{B as be}from"./index-Diw57m_E.js";import{N as ve}from"./useSchemas-b074f3a1-CF0HohIK.js";import he from"./CodeFileListModal-a924902a-_rW1W1u5.js";import ye from"./FileSelectModal-ffc69d4a-nPiUdbMv.js";import{f as Ne}from"./antd-vue-vendor-me9YkNVC.js";import"./validator-B_KkcUnu.js";import"./CodeFileViewModal-405e2b58-DtLwgNIA.js";import"./download-CZ-9H9a3.js";import{u as Te}from"./useForm-CgkFTrrO.js";import{B as Me}from"./BasicForm-DBcXiHk0.js";import"./vxe-table-vendor-B22HppNm.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./base64Conver-24EVOS6V.js";import"./user.api-mLAlJze4.js";var L=(t,a,p)=>new Promise((b,d)=>{var S=r=>{try{i(p.next(r))}catch(f){d(f)}},u=r=>{try{i(p.throw(r))}catch(f){d(f)}},i=r=>r.done?b(r.value):Promise.resolve(r.value).then(S,u);i((p=p.apply(t,a)).next())});const Ce=ne({name:"CodeGenerator",components:{BasicForm:Me,BasicModal:be,FileSelectModal:ye,CodeFileListModal:he},emits:["register"],setup(t){const a="JEECG_ONL_PROJECT_PATH",p="JEECG_ONL_PROJECT_NAME",b=T(!0),d=T(),S=I(()=>b.value?800:1200),u=T("代码生成"),i=T(!1),{prefixCls:r}=fe("code-generator-modal"),f=r,M=T(""),j=k({projectPath:"",packageStyle:"service",jspMode:"",jformType:"1",tableName_tmp:"",ftlDescription:"",entityName:"",codeTypes:"controller,service,dao,mapper,entity,vue"}),o=k({}),C=T([]),v=k({dataSource:[],columns:[{title:"子表名",key:"tableName",type:x.input,disabled:!0,validateRules:[{required:!0,message:"请输入${title}"}]},{title:"子表实体",key:"entityName",type:x.input,validateRules:[{required:!0,message:"请输入${title}"}]},{title:"功能说明",key:"ftlDescription",type:x.input,validateRules:[{required:!0,message:"请输入${title}"}]}]}),w=I(()=>v.dataSource.length>0),{formSchemas:h}=ve(t,{onProjectPathChange:le,onProjectPathSearch:ae,jspModeOptions:C},b),[F,{resetFields:P,setFieldsValue:R,validate:$}]=Te({schemas:h,showActionButtonGroup:!1,labelAlign:"right"}),[H,{closeModal:E}]=ge(e=>L(this,null,function*(){yield P(),M.value=e.code,i.value=!1,v.dataSource=[],C.value=[],re(),Object.assign(o,j),W()}));function W(){return L(this,null,function*(){let{main:e,sub:l,jspModeList:y,projectPath:c}=yield A.get({url:"/online/cgform/head/tableInfo",params:{code:M.value}}),N=[];for(let g of y){const{code:D,note:se}=g;D=="many"||N.push({label:se,value:D})}C.value=N,e.isTree=="Y"?o.jspMode="tree":N.find(g=>g.value===e.themeTemplate)?o.jspMode=e.themeTemplate:o.jspMode=N[0].value,b.value=e.tableType==1,u.value="代码生成【"+e.tableName+"】",o.projectPath||(o.projectPath=c,window.localStorage.setItem(a,c));const _=localStorage.getItem(p);_&&(o.entityPackage=_),o.jformType=e.tableType+"",o.tableName_tmp=e.tableName,o.ftlDescription=e.tableTxt;let O=q(e.tableName);o.entityName=O.substring(0,1).toUpperCase()+O.substring(1),yield me(),R(o),l&&l.length>0&&(v.dataSource=l.map(g=>({tableName:g.tableName,entityName:ie(g.tableName),ftlDescription:g.tableTxt})))})}const[z,{openModal:Y}]=U();function Q(){return L(this,null,function*(){try{const e=yield $();let l=Object.assign({},e,{code:M.value,tableName:e.tableName_tmp});if(w.value){if(yield d.value.validateTable())return;l.subList=d.value.getTableData()}i.value=!0;let y=yield X(l);Y(!0,{codeList:y.codeList,pathKey:y.pathKey,tableName:e.tableName_tmp}),E(),localStorage.setItem(p,e.entityPackage)}catch(e){}finally{i.value=!1}})}function X(e){return new Promise((l,y)=>{A.post({url:"/online/cgform/api/codeGenerate",params:e},{isTransformResponse:!1}).then(c=>{if(c.success){let N=c.result,_=c.message;l({codeList:N,pathKey:_})}else Ne.error(c.message),y(c.message)})})}function Z(){E()}const[ee,te]=U();function ae(){te.openModal(!0,{})}function oe(e){window.localStorage.setItem(a,e),R({projectPath:e})}function ie(e){let l=q(e);return l.substring(0,1).toUpperCase()+l.substring(1)}function re(){let e=window.localStorage.getItem(a);e&&(j.projectPath=e)}function le(e){e.target.value&&window.localStorage.setItem(a,e.target.value)}return{title:u,modalWidth:S,confirmLoading:i,subTable:v,showSubTable:w,onSubmit:Q,onCancel:Z,onFileSelect:oe,registerFileSelectModal:ee,subTableRef:d,registerForm:F,registerModal:H,registerCodeFileListModal:z,wrapClassName:f}}});function Se(t,a,p,b,d,S){const u=n("a-radio"),i=n("a-tooltip"),r=n("a-radio-group"),f=n("BasicForm"),M=n("JVxeTable"),j=n("a-card"),o=n("a-spin"),C=n("BasicModal"),v=n("FileSelectModal"),w=n("code-file-list-modal");return B(),pe(ce,null,[m(C,{onRegister:t.registerModal,title:t.title,width:t.modalWidth,confirmLoading:t.confirmLoading,okText:"开始生成",cancelText:"取消",onOk:t.onSubmit,onCancel:t.onCancel,wrapClassName:t.wrapClassName},{default:s(()=>[m(o,{spinning:t.confirmLoading},{default:s(()=>[m(f,{onRegister:t.registerForm},{pageCode:s(({model:h,field:F})=>[m(r,{value:h[F],"onUpdate:value":P=>h[F]=P},{default:s(()=>[m(i,{placement:"top"},{title:s(()=>a[0]||(a[0]=[K("span",null,"深度封装表单，用户只需定义字段json即可渲染表单，优点简单便捷，缺点扩展有难度",-1)])),default:s(()=>[m(u,{value:"vue3"},{default:s(()=>a[1]||(a[1]=[J("封装表单(BasicForm)")])),_:1})]),_:1}),m(i,{placement:"top"},{title:s(()=>a[2]||(a[2]=[K("span",null,"antd的原生表单，所有字段都需要硬编码，缺点编码繁琐，优点扩展容易",-1)])),default:s(()=>[h.jspMode=="innerTable"||h.jspMode=="tab"?G("",!0):(B(),V(u,{key:0,value:"vue3Native"},{default:s(()=>a[3]||(a[3]=[J("原生表单(a-form)")])),_:1}))]),_:2},1024)]),_:2},1032,["value","onUpdate:value"])]),_:1},8,["onRegister"]),t.showSubTable?(B(),V(j,{key:0,title:"子表信息",size:"small"},{default:s(()=>[m(M,de({ref:"subTableRef",rowNumber:"",maxHeight:580},t.subTable),null,16)]),_:1})):G("",!0)]),_:1},8,["spinning"])]),_:1},8,["onRegister","title","width","confirmLoading","onOk","onCancel","wrapClassName"]),m(v,{onRegister:t.registerFileSelectModal,onSelect:t.onFileSelect},null,8,["onRegister","onSelect"]),m(w,{onRegister:t.registerCodeFileListModal},null,8,["onRegister"])],64)}const Bt=ue(Ce,[["render",Se]]);export{Bt as default};
