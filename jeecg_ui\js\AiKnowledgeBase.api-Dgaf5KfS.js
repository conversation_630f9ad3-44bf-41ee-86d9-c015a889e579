import{j as r}from"./index-CCWaWN5g.js";import{M as t}from"./antd-vue-vendor-me9YkNVC.js";const n=e=>r.get({url:"/airag/knowledge/list",params:e},{isTransformResponse:!1}),l=e=>r.get({url:"/airag/knowledge/queryById",params:e},{isTransformResponse:!1}),s=e=>r.post({url:"/airag/knowledge/add",params:e}),g=e=>r.put({url:"/airag/knowledge/edit",params:e}),i=(e,o)=>{t.confirm({title:"确认删除",content:"是否删除名称为"+e.name+"的知识库吗？",okText:"确认",cancelText:"取消",onOk:()=>r.delete({url:"/airag/knowledge/delete",params:e},{joinParamsToUrl:!0}).then(()=>{o()})})},u=e=>r.get({url:"/airag/knowledge/doc/list",params:e},{isTransformResponse:!1}),c=e=>r.put({url:"/airag/knowledge/rebuild",params:e,timeout:2*60*1e3},{joinParamsToUrl:!0,isTransformResponse:!1}),k=e=>r.post({url:"/airag/knowledge/doc/edit",params:e}),w=(e,o)=>r.put({url:"/airag/knowledge/doc/rebuild",params:e},{joinParamsToUrl:!0}).then(()=>{o()}),m=(e,o)=>r.delete({url:"/airag/knowledge/doc/deleteBatch",params:e},{joinParamsToUrl:!0}).then(()=>{o()}),f=e=>{let o="/airag/knowledge/embedding/hitTest/"+e.knowId;return r.get({url:o,params:e},{isTransformResponse:!1})};export{w as a,f as b,u as c,i as d,g as e,k as f,m as k,n as l,l as q,c as r,s};
