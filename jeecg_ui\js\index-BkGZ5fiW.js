var w=(a,o,n)=>new Promise((u,s)=>{var m=l=>{try{i(n.next(l))}catch(e){s(e)}},t=l=>{try{i(n.throw(l))}catch(e){s(e)}},i=l=>l.done?u(l.value):Promise.resolve(l.value).then(m,t);i((n=n.apply(a,o)).next())});import"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import{d as L,e as k,ag as h,aq as y,ah as K,ar as d,aA as R,as as z,aB as p,aD as b,at as I,k as A,F as T,aC as D,f as S,b as $,u as f,w as F,J as c}from"./vue-vendor-dy9k-Yad.js";import{H as g,F as G,a as _,I as P,J as C,K as H}from"./index-CCWaWN5g.js";import{Z as N,an as v}from"./antd-vue-vendor-me9YkNVC.js";const V=L({name:"TableImage",components:{Image:v,PreviewGroup:v.PreviewGroup,Badge:N},props:{imgList:g.arrayOf(g.string),size:g.number.def(40),simpleShow:g.bool,showBadge:g.bool.def(!0),margin:g.number.def(4),srcPrefix:g.string.def("")},setup(a){const o=k(()=>{const{size:u}=a,s=`${u}px`;return{height:s,width:s}}),{prefixCls:n}=G("basic-table-img");return{prefixCls:n,getWrapStyle:o}}}),J={class:"img-div"};function q(a,o,n,u,s,m){const t=h("Image"),i=h("PreviewGroup"),l=h("Badge");return a.imgList&&a.imgList.length?(d(),y("div",{key:0,class:z([a.prefixCls,"flex items-center mx-auto"]),style:R(a.getWrapStyle)},[a.simpleShow?(d(),p(l,{key:0,count:!a.showBadge||a.imgList.length==1?0:a.imgList.length},{default:b(()=>[I("div",J,[A(i,null,{default:b(()=>[(d(!0),y(T,null,D(a.imgList,(e,r)=>(d(),p(t,{key:e,width:a.size,style:R({display:r===0?"":"none !important"}),src:a.srcPrefix+e},null,8,["width","style","src"]))),128))]),_:1})])]),_:1},8,["count"])):(d(),p(i,{key:1},{default:b(()=>[(d(!0),y(T,null,D(a.imgList,(e,r)=>(d(),p(t,{key:e,width:a.size,style:R({marginLeft:r===0?0:a.margin}),src:a.srcPrefix+e},null,8,["width","style","src"]))),128))]),_:1}))],6)):K("",!0)}const j=_(V,[["render",q]]);function Q(a){const o=S(null),n=S(!1),u=S(null);let s;function m(e,r){P()&&$(()=>{o.value=null,n.value=null}),!(f(n)&&P()&&e===f(o))&&(o.value=e,u.value=r,a&&e.setProps(C(a)),n.value=!0,s==null||s(),s=F(()=>a,()=>{a&&e.setProps(C(a))},{immediate:!0,deep:!0}))}function t(){const e=f(o);return e||H("The table instance has not been obtained yet, please make sure the table is presented when performing the table operation!"),e}function i(){return o}return[m,{reload:e=>w(null,null,function*(){return yield t().reload(e)}),setProps:e=>{t().setProps(e)},redoHeight:()=>{t().redoHeight()},setLoading:e=>{t().setLoading(e)},getDataSource:()=>t().getDataSource(),getRawDataSource:()=>t().getRawDataSource(),getColumns:({ignoreIndex:e=!1}={})=>{const r=t().getColumns({ignoreIndex:e})||[];return c(r)},setColumns:e=>{t().setColumns(e)},setTableData:e=>t().setTableData(e),setPagination:e=>t().setPagination(e),deleteSelectRowByKey:e=>{t().deleteSelectRowByKey(e)},getSelectRowKeys:()=>c(t().getSelectRowKeys()),getSelectRows:()=>c(t().getSelectRows()),clearSelectedRowKeys:()=>{t().clearSelectedRowKeys()},setSelectedRowKeys:e=>{t().setSelectedRowKeys(e)},getPaginationRef:()=>t().getPaginationRef(),getSize:()=>c(t().getSize()),updateTableData:(e,r,B)=>t().updateTableData(e,r,B),deleteTableDataRecord:e=>t().deleteTableDataRecord(e),insertTableDataRecord:(e,r)=>t().insertTableDataRecord(e,r),updateTableDataRecord:(e,r)=>t().updateTableDataRecord(e,r),findTableDataRecord:e=>t().findTableDataRecord(e),getRowSelection:()=>c(t().getRowSelection()),getCacheColumns:()=>c(t().getCacheColumns()),getForm:()=>f(u),setShowPagination:e=>w(null,null,function*(){t().setShowPagination(e)}),getShowPagination:()=>c(t().getShowPagination()),expandAll:()=>{t().expandAll()},collapseAll:()=>{t().collapseAll()},getTableRef:()=>i()}]}export{j as T,Q as u};
