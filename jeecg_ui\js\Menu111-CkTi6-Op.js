import{d as t,ag as n,aq as s,ar as a,G as r,at as p,k as m}from"./vue-vendor-dy9k-Yad.js";import{j as c}from"./antd-vue-vendor-me9YkNVC.js";import{a as d}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const u=t({name:"Menu111Demo",components:{Input:c}}),i={class:"p-5"};function l(f,e,_,x,$,k){const o=n("Input");return a(),s("div",i,[e[0]||(e[0]=r(" 多层级缓存-页面1-1-1 ")),e[1]||(e[1]=p("br",null,null,-1)),m(o)])}const v=d(u,[["render",l]]);export{v as default};
