var se=Object.defineProperty,ie=Object.defineProperties;var ce=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var ue=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable;var Q=(e,t,n)=>t in e?se(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,W=(e,t)=>{for(var n in t||(t={}))ue.call(t,n)&&Q(e,n,t[n]);if(U)for(var n of U(t))re.call(t,n)&&Q(e,n,t[n]);return e},X=(e,t)=>ie(e,ce(t));var H=(e,t,n)=>new Promise((w,m)=>{var v=a=>{try{s(n.next(a))}catch(i){m(i)}},c=a=>{try{s(n.throw(a))}catch(i){m(i)}},s=a=>a.done?w(a.value):Promise.resolve(a.value).then(v,c);s((n=n.apply(e,t)).next())});import{c as z,f as h,r as te,e as de,w as P,u as D,n as Y,d as le,ag as C,aq as ne,ar as j,k as S,aE as J,aD as k,G as R,aB as Z,ah as x,p as F,h as fe,J as ee}from"./vue-vendor-dy9k-Yad.js";import{B as he}from"./index-Diw57m_E.js";import{aw as me,H as _,ac as pe,a9 as ae,ax as ve,af as ge,a as oe,ad as ye}from"./index-CCWaWN5g.js";import{t as ke}from"./props-CCT78mKr.js";import{_ as Se}from"./index-BtIdS_Qz.js";import{o as Ce,h as G}from"./antd-vue-vendor-me9YkNVC.js";import{J as we}from"./JSelectBiz-jOYRdMJf.js";function Ke(e,t,n,w,m){const v=z("selectOptions",h([])),c=z("selectValues",te({})),s=z("loadingEcho",h(!1)),a=h([]),i=h([]),d=h([]),g=h(!1),B=de(()=>w.multiple?n.checkStrictly:!0);let y=!0;P(c,({value:l})=>{l&&g.value==!1&&l.length>0&&(s.value=y,y=!1,M(null,l.join(",")).finally(()=>{s.value=!1}))},{immediate:!0});function T(){const l=D(e);if(!l)throw new Error("tree is null!");return l}function L(){Y(()=>{n.defaultExpandLevel&&n.defaultExpandLevel>0&&T().filterByLevel(n.defaultExpandLevel),i.value=c.value}).then()}function V(l,o){if(n.checkable==!1){i.value=n.checkStrictly?l.checked:l;const{selectedNodes:r}=o;let f=[];r.forEach(p=>{f.push(p)}),d.value=f}}function $(l,o){if(n.checkable==!0){if(!w.multiple){if(o.checked){i.value=[o.node.eventKey];let p=n.rowKey,N=o.checkedNodes.find(E=>E[p]===o.node.eventKey);d.value=[N]}else i.value=[],d.value=[];return}i.value=n.checkStrictly?l.checked:l;const{checkedNodes:r}=o;let f=[];r.forEach(p=>{f.push(p)}),d.value=f}}function b(l){return H(this,null,function*(){T().checkAll(l),yield Y(),i.value=T().getCheckedKeys(),l?u():d.value=[]})}function u(){let l="";D(i).length>0&&(l=i.value.join(",")),t({ids:l}).then(o=>{d.value=o})}function K(l){T().expandAll(l)}function M(l,o){return H(this,null,function*(){let r={},f="";l&&(f=l.eventKey,r.pid=l.value),o&&(f="",r.ids=o);let p=yield t(r),N=p;if(n.serverTreeData||(p=me(p,n,f),p.length==0&&l&&A(f,a.value)),g.value==!0){if(!l)a.value=p;else return new Promise(E=>{if(!l.children){E();return}const O=D(e);O&&(O.updateNodeByKey(l.eventKey,{children:p}),O.setExpandedKeys([l.eventKey,...O.getExpandedKeys()])),E()});L()}else{const E=[];N.forEach(O=>{E.push({label:O[n.labelKey],value:O[n.rowKey]})}),v.value=E}})}function A(l,o){if(o&&o.length>0)for(let r of o)if(r.key==l){r.child||(r.isLeaf=!0);break}else A(l,r.children)}function q(l){const o=[],r=[];d.value.forEach(f=>{o.push({label:f[n.labelKey],value:f[n.rowKey]})}),i.value.forEach(f=>{r.push(f)}),v.value=o,l&&l(o,r)}function I(l){return H(this,null,function*(){l?(g.value=!0,yield M(null,null)):(g.value=!1,m==null||m("close"))})}return[{visibleChange:I,selectOptions:v,selectValues:c,onLoadData:M,onCheck:$,onSelect:V,checkALL:b,expandAll:K,checkedKeys:i,selectRows:d,treeData:a,getCheckStrictly:B,getSelectTreeData:q}]}const De=le({name:"DeptSelectModal",components:{BasicModal:he,BasicTree:Se},props:X(W({},ke),{modalTitle:{type:String,default:"部门选择"},maxHeight:{type:Number,default:500},value:_.oneOfType([_.string,_.array])}),emits:["register","getSelectResult","close"],setup(e,{emit:t,refs:n}){const[w,{closeModal:m}]=pe(),v=ae(),c=h(null);let s=e.value===""?[]:e.value,a=Object.assign({},D(e),D(v),{value:s},{disabled:!1});const i=Ce(a,"multiple"),d=l(),[{visibleChange:g,checkedKeys:B,getCheckStrictly:y,getSelectTreeData:T,onCheck:L,onLoadData:V,treeData:$,checkALL:b,expandAll:u,onSelect:K}]=Ke(c,d,i,e,t),M=h(e.params),A=h([]),q={key:e.rowKey};function I(){T((o,r)=>{t("getSelectResult",o,r),m()})}function l(){let o=e.sync?ve:ge;return r=>o(Object.assign({},r,{primaryKey:e.rowKey}))}return{tree:A,handleOk:I,searchInfo:M,treeRef:c,treeData:$,onCheck:L,onSelect:K,checkALL:b,expandAll:u,fieldNames:q,checkedKeys:B,register:w,getBindValue:i,getCheckStrictly:y,visibleChange:g,onLoadData:V}}});function Te(e,t,n,w,m,v){const c=C("BasicTree"),s=C("a-menu-item"),a=C("a-menu"),i=C("Icon"),d=C("a-button"),g=C("a-dropdown"),B=C("BasicModal");return j(),ne("div",null,[S(B,J(e.$attrs,{onRegister:e.register,title:e.modalTitle,width:"500px",maxHeight:e.maxHeight,onOk:e.handleOk,destroyOnClose:"",onVisibleChange:e.visibleChange}),{insertFooter:k(()=>[S(g,{placement:"top"},{overlay:k(()=>[S(a,null,{default:k(()=>[e.multiple?(j(),Z(s,{key:"1",onClick:t[0]||(t[0]=y=>e.checkALL(!0))},{default:k(()=>t[4]||(t[4]=[R("全部勾选")])),_:1,__:[4]})):x("",!0),e.multiple?(j(),Z(s,{key:"2",onClick:t[1]||(t[1]=y=>e.checkALL(!1))},{default:k(()=>t[5]||(t[5]=[R("取消全选")])),_:1,__:[5]})):x("",!0),S(s,{key:"3",onClick:t[2]||(t[2]=y=>e.expandAll(!0))},{default:k(()=>t[6]||(t[6]=[R("展开全部")])),_:1,__:[6]}),S(s,{key:"4",onClick:t[3]||(t[3]=y=>e.expandAll(!1))},{default:k(()=>t[7]||(t[7]=[R("折叠全部")])),_:1,__:[7]})]),_:1})]),default:k(()=>[S(d,{style:{float:"left"}},{default:k(()=>[t[8]||(t[8]=R(" 树操作 ")),S(i,{icon:"ant-design:up-outlined"})]),_:1,__:[8]})]),_:1})]),default:k(()=>[S(c,J({ref:"treeRef",treeData:e.treeData,"load-data":e.sync==!1?null:e.onLoadData},e.getBindValue,{onSelect:e.onSelect,onCheck:e.onCheck,fieldNames:e.fieldNames,checkedKeys:e.checkedKeys,multiple:e.multiple,checkStrictly:e.getCheckStrictly}),null,16,["treeData","load-data","onSelect","onCheck","fieldNames","checkedKeys","multiple","checkStrictly"])]),_:1},16,["onRegister","title","maxHeight","onOk","onVisibleChange"])])}const Be=oe(De,[["render",Te]]),be=le({name:"JSelectDept",components:{DeptSelectModal:Be,JSelectBiz:we},inheritAttrs:!1,props:{value:_.oneOfType([_.string,_.array]),multiple:_.bool.def(!0)},emits:["options-change","change","select","update:value"],setup(e,{emit:t,refs:n}){const w=h(),[m,{openModal:v}]=ye(),c=h([]);let s=te({value:[]}),a=[];const i=h(!1);F("selectOptions",c),F("selectValues",s),F("loadingEcho",i);const d=h(!1),g=ae();fe(()=>{a=[],e.value&&y()}),P(()=>e.value,()=>{y()}),P(c,()=>{c&&t("select",ee(D(c)),ee(D(s)))});function B(){d.value=!0,v(!0,{isUpdate:!1})}function y(){let u=e.value?e.value:[];u&&typeof u=="string"?(s.value=u.split(","),a=u.split(",")):(s.value=u,a=G(u))}function T(u,K){c.value=u,s.value=K,b(K)}const L=Object.assign({},D(e),D(g)),V=()=>{a.length?s.value=G(a):b(a)},$=u=>{a=G(u),b(a)},b=u=>{let K=typeof e.value=="string"?u.join(","):u;t("update:value",K),t("change",K),(!u||u.length==0)&&t("select",null,null)};return{attrs:g,selectOptions:c,selectValues:s,loadingEcho:i,getBindValue:L,tag:d,regModal:m,setValue:T,handleOpen:B,handleClose:V,handleSelectChange:$}}}),Ee={class:"JSelectDept"};function Oe(e,t,n,w,m,v){const c=C("JSelectBiz"),s=C("DeptSelectModal"),a=C("a-form-item");return j(),ne("div",Ee,[S(c,J({onChange:e.handleSelectChange,onHandleOpen:e.handleOpen,loading:e.loadingEcho},e.attrs),null,16,["onChange","onHandleOpen","loading"]),S(a,null,{default:k(()=>[S(s,J({onRegister:e.regModal,onGetSelectResult:e.setValue},e.getBindValue,{multiple:e.multiple,onClose:e.handleClose}),null,16,["onRegister","onGetSelectResult","multiple","onClose"])]),_:1})])}const je=oe(be,[["render",Oe],["__scopeId","data-v-1ec52d81"]]);export{je as a};
