import{j as s,k as l}from"./index-CCWaWN5g.js";import{M as o}from"./antd-vue-vendor-me9YkNVC.js";const i="/sys/user/exportXls",g="/sys/user/importExcel",d=e=>s.get({url:"/sys/user/customQueryPageList",params:e});const U=e=>s.get({url:"/sys/user/queryUserRole",params:e},{errorMessageMode:"none"}),m=(e,t)=>s.delete({url:"/sys/user/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),L=(e,t)=>{o.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>s.delete({url:"/sys/user/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},T=(e,t)=>{let n=t?"/sys/user/edit":"/sys/user/customUserAdd";return s.post({url:n,params:e})},h=e=>s.get({url:"/sys/duplicate/check",params:e},{isTransformResponse:!1}),a={},B=e=>new Promise((t,n)=>{let r;l(e)?r=`${e.tableName}_${e.fieldName}`:r=e,clearTimeout(a[r]),a[r]=setTimeout(()=>{s.get({url:"/sys/duplicate/check",params:e},{isTransformResponse:!1}).then(u=>{t(u)}).catch(u=>{n(u)}),delete a[r]},500)}),R=e=>s.get({url:"/sys/role/queryall",params:e}),f=e=>s.get({url:"/sys/role/customQueryallNoByTenant",params:e});const p=e=>s.get({url:"/sys/user/userDepartList",params:e},{successMessageMode:"none"});const P=e=>s.get({url:"/sys/user/recycleBin",params:e}),Q=(e,t)=>s.put({url:"/sys/user/putRecycleBin",params:e}).then(()=>{t()}),k=(e,t)=>s.delete({url:"/sys/user/deleteRecycleBin",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),x=e=>s.put({url:"/sys/user/changePassword",params:e}),N=(e,t)=>s.put({url:"/sys/user/frozenBatch",params:e}).then(()=>{t()}),q=e=>s.get({url:"/sys/sysUserAgent/queryByUserName",params:e},{isTransformResponse:!1}),C=e=>{let t=e.id?"/sys/sysUserAgent/edit":"/sys/sysUserAgent/add";return s.post({url:t,params:e})},j=e=>s.put({url:"/sys/user/userQuitAgent",params:e}),w=e=>s.get({url:"/sys/user/getQuitList",params:e}),v=(e,t)=>s.put({url:"/sys/user/putCancelQuit",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),D=e=>s.get({url:"/sys/tenant/getUserTenantPageList",params:e}),M=e=>s.put({url:"/sys/tenant/updateUserTenantStatus",params:e},{joinParamsToUrl:!0,isTransformResponse:!1});export{B as a,R as b,f as c,h as d,D as e,q as f,U as g,j as h,w as i,p as j,x as k,d as l,C as m,P as n,Q as o,v as p,k as q,L as r,T as s,N as t,M as u,g as v,i as w,m as x};
