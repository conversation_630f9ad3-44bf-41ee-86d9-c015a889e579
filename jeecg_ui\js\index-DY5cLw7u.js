var D=Object.defineProperty,N=Object.defineProperties;var O=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var j=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;var b=(s,e,r)=>e in s?D(s,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[e]=r,w=(s,e)=>{for(var r in e||(e={}))j.call(e,r)&&b(s,r,e[r]);if(R)for(var r of R(e))G.call(e,r)&&b(s,r,e[r]);return s},k=(s,e)=>N(s,O(e));import{d as T,f as d,r as z,o as q,b as P,ag as _,aq as U,ar as F,k as c,aD as u,at as A,u as g}from"./vue-vendor-dy9k-Yad.js";import{u as J}from"./index-BkGZ5fiW.js";import{j as f,u as L}from"./index-CCWaWN5g.js";import{i as I}from"./antd-vue-vendor-me9YkNVC.js";import{useECharts as M}from"./useECharts-BU6FzBZi.js";import Q from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const W=()=>f.get({url:"/sys/actuator/redis/keysSize"},{isTransformResponse:!1}),X=()=>f.get({url:"/sys/actuator/redis/memoryInfo"},{isTransformResponse:!1}),C=()=>f.get({url:"/sys/actuator/redis/info"}),Y=()=>f.get({url:"/sys/actuator/redis/metrics/history"}),Z=()=>Promise.all([W(),X()]),$=[{title:"Key",dataIndex:"key",width:100},{title:"Description",dataIndex:"description",width:80},{title:"Value",dataIndex:"value",width:80}],tt={class:"p-4"},et=T({name:"monitor-redis"}),he=T(k(w({},et),{setup(s){const e=d([]),r=d(null),v=d(null),{setOptions:y,echarts:rt}=M(r),{setOptions:h,echarts:ot}=M(v),st=d(!1);let x=null;const{createMessage:it}=L(),i=z({title:{text:"Redis Key 实时数量（个）"},xAxis:{type:"category",boundaryGap:!1,data:[]},yAxis:{type:"value"},series:[{data:[],type:"line",areaStyle:{color:"#ff6987"},lineStyle:{color:"#dc143c",width:10,type:"solid"}}]}),n=z({title:{text:"Redis 内存实时占用情况（KB）"},xAxis:{type:"category",boundaryGap:!1,data:[]},yAxis:{type:"value"},series:[{data:[],type:"line",areaStyle:{color:"#74bcff"},lineStyle:{color:"#1890ff",width:10,type:"solid"}}]}),[E,{reload:at}]=J({columns:$,showIndexColumn:!1,pagination:!1,bordered:!0});function nt(a,m){let o=null,t=null;return a.forEach(p=>{let l=Number.parseInt(p[m]);(o==null||l>o)&&(o=l),(t==null||l<t)&&(t=l)}),[o,t]}function mt(){C().then(a=>{e.value=a.result})}function K(){y(n),h(i)}function V(){B(),S(),x=setInterval(()=>{H()},15e3)}function S(){x&&clearInterval(x)}function B(){Y().then(a=>{let m=a.dbSize,o=a.memory;m.forEach(t=>{i.xAxis.data.push(I(t.create_time).format("hh:mm:ss")),i.series[0].data.push(t.dbSize)}),o.forEach(t=>{n.xAxis.data.push(I(t.create_time).format("hh:mm:ss")),n.series[0].data.push(t.used_memory/1e3)}),y(n,!1),h(i,!1)})}function H(){Z().then(a=>{let m=I().format("hh:mm:ss"),[{dbSize:o},t]=a,p=t.used_memory/1e3;i.xAxis.data.push(m),i.series[0].data.push(o),n.xAxis.data.push(m),n.series[0].data.push(p),i.series[0].data.length>80&&(i.xAxis.data.splice(0,1),i.series[0].data.splice(0,1),n.xAxis.data.splice(0,1),n.series[0].data.splice(0,1)),y(n,!1),h(i,!1)}).catch(a=>{})}return q(()=>{K(),V()}),P(()=>{S()}),(a,m)=>{const o=_("a-col"),t=_("a-row"),p=_("a-card");return F(),U("div",tt,[c(p,null,{default:u(()=>[c(t,{gutter:8},{default:u(()=>[c(o,{sm:24,xl:12},{default:u(()=>[A("div",{ref_key:"chartRef",ref:r,style:{width:"100%",height:"300px"}},null,512)]),_:1}),c(o,{sm:24,xl:12},{default:u(()=>[A("div",{ref_key:"chartRef2",ref:v,style:{width:"100%",height:"300px"}},null,512)]),_:1})]),_:1})]),_:1}),c(g(Q),{onRegister:g(E),api:g(C)},null,8,["onRegister","api"])])}}}));export{he as default};
