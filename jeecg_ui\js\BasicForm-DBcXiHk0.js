var Ue=Object.defineProperty,Xe=Object.defineProperties;var Qe=Object.getOwnPropertyDescriptors;var ke=Object.getOwnPropertySymbols;var Ze=Object.prototype.hasOwnProperty,et=Object.prototype.propertyIsEnumerable;var Ve=(e,o,a)=>o in e?Ue(e,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[o]=a,O=(e,o)=>{for(var a in o||(o={}))Ze.call(o,a)&&Ve(e,a,o[a]);if(ke)for(var a of ke(o))et.call(o,a)&&Ve(e,a,o[a]);return e},De=(e,o)=>Xe(e,Qe(o));var _=(e,o,a)=>new Promise((u,C)=>{var A=m=>{try{i(a.next(m))}catch(d){C(d)}},f=m=>{try{i(a.throw(m))}catch(d){C(d)}},i=m=>m.done?u(m.value):Promise.resolve(m.value).then(A,f);i((a=a.apply(e,o)).next())});import{e as W,u as n,f as se,aq as qe,ar as U,aG as G,d as Pe,I as tt,q as He,B as nt,k as M,i as ot,G as pe,aE as Z,ag as X,aB as ae,ah as de,aJ as Ce,aD as Y,at as st,aA as at,au as Ae,w as he,J as Me,h as rt,n as Ke,r as Ne,o as lt,v as it,aO as ct,aK as Le,F as ut,aC as ve,aH as $e}from"./vue-vendor-dy9k-Yad.js";import{a7 as ye,h as be,u as dt,V as ft,a5 as Ie,i as Be,s as mt,aM as pt,a6 as ht}from"./antd-vue-vendor-me9YkNVC.js";import{j as Ee}from"./componentMap-Bkie1n3v.js";import{a as bt,b as yt}from"./index-CImCetrx.js";import{N as Te,h as Q,k as ce,P as Se,a as je,a2 as gt,n as N,O as le,ap as At,X as vt,aq as wt,a7 as xe,l as Ft,m as Ct,H as T,ar as Bt,R as ge,Q as ie,as as Ot,at as Pt,x as ze,K as _e,au as Ye,e as It,F as Tt}from"./index-CCWaWN5g.js";import"./index-Diw57m_E.js";import{c as Oe}from"./index-LCGLvkB3.js";import{u as St}from"./BasicModal-BLFvpBuk.js";const{t:we}=Te();function We(e){return e.includes("Input")||e.includes("Complete")?we("common.inputText"):e.includes("Picker")||e.includes("Select")||e.includes("Cascader")||e.includes("Checkbox")||e.includes("Radio")||e.includes("Switch")?we("common.chooseText"):""}const jt=["DatePicker","MonthPicker","WeekPicker","TimePicker"];function Rt(){return[...jt,"RangePicker"]}function kt(e,o,a){Reflect.has(e,"type")||(["DatePicker","MonthPicker","WeekPicker","TimePicker"].includes(o)?e.type=a?"string":"object":["RangePicker","Upload","CheckboxGroup","TimePicker"].includes(o)?e.type="array":["InputNumber"].includes(o)&&(e.type="number"))}function Vt(e,o){return e&&["Input","InputPassword","InputSearch","InputTextArea"].includes(e)&&o&&Se(o)?`${o}`:o}function Dt(e,o){return e&&["InputNumber"].includes(e)&&typeof o=="string"&&o!=""?Number(o):o}const Ge=Rt();function Mt(e,o){return W(()=>{const a=n(e),{labelCol:u={},wrapperCol:C={}}=a.itemProps||{},{labelWidth:A,disabledLabelWidth:f}=a,{labelWidth:i,labelCol:m,wrapperCol:d,layout:D}=n(o);if(f)return{labelCol:u,wrapperCol:C};if(!i&&!A&&!m)return u.style={textAlign:"left"},{labelCol:u,wrapperCol:C};let F=A||i,v=O(O({},m),u);const E=O(O({},d),C);return F&&(F=Se(F)?`${F}px`:F,v={}),{labelCol:O({style:{width:F||"100%"}},v),wrapperCol:O({style:{width:D==="vertical"?"100%":`calc(100% - ${F})`}},E)}})}const Nt=["id"],Lt={__name:"Middleware",props:["formName","fieldName"],setup(e){const o=se(null),a=e;return a.formName&&a.fieldName&&(o.value=`${a.formName}_${a.fieldName}`),(u,C)=>(U(),qe("div",{id:o.value,style:{flex:"1",width:"100%"}},[G(u.$slots,"default",{},void 0,!0)],8,Nt))}},$t=je(Lt,[["__scopeId","data-v-a1d963a0"]]);function fe(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!ot(e)}const Et=Pe({name:"BasicFormItem",inheritAttrs:!1,props:{schema:{type:Object,default:()=>({})},formProps:{type:Object,default:()=>({})},allDefaultValues:{type:Object,default:()=>({})},formModel:{type:Object,default:()=>({})},setFormModel:{type:Function,default:null},validateFields:{type:Function,default:null},tableAction:{type:Object},formActionType:{type:Object},clearValidate:{type:Function,default:null},formName:{type:String,default:""}},setup(e,{slots:o}){const{t:a}=Te(),u=gt(),{schema:C,formProps:A}=tt(e),f=Mt(C,A),i=W(()=>{const{allDefaultValues:c,formModel:r,schema:p}=e,{mergeDynamicData:w}=e.formProps;return{field:p.field,model:r,values:O(O(O({},w),c),r),schema:p}}),m=W(()=>{var s;const{schema:c,tableAction:r,formModel:p,formActionType:w}=e;let{componentProps:t={}}=c;return N(t)&&(t=(s=t({schema:c,tableAction:r,formModel:p,formActionType:w}))!=null?s:{}),c.component==="Divider"&&(t=Object.assign({type:"horizontal",orientation:"left",plain:!0},t)),t}),d=W(()=>{const{disabled:c}=e.formProps;if(c)return c;const{dynamicDisabled:r}=e.schema,{disabled:p=!1}=n(m);let w=!!c||p;return le(r)&&(w=r),N(r)&&(w=r(n(i))),w}),D=W(()=>{const{dynamicPropsVal:c,dynamicPropskey:r}=e.schema;if(r==null)return null;{const{[r]:p}=n(m);let w=p;if(N(c))return w=c(n(i)),w}});function F(){const{show:c,ifShow:r}=e.schema,{showAdvancedButton:p}=e.formProps,w=p&&le(e.schema.isAdvanced)?e.schema.isAdvanced:!0;let t=!0,s=!0;return le(c)&&(t=c),le(r)&&(s=r),N(c)&&(t=c(n(i))),N(r)&&(s=r(n(i))),t=t&&w,{isShow:t,isIfShow:s}}let v=[],E=[];const I=c=>{v=[],E=[],c.forEach((r,p)=>{const w=r.validator;v.push(!0),E.push(null),N(w)&&(r.validator=(t,s,h)=>{if(v[p]){v[p]=!1,setTimeout(()=>{v[p]=!0},100);const l=w(t,s,h);return E[p]=l,l}else return E[p]})})};function k(){var V;const{rules:c=[],component:r,rulesMessageJoinLabel:p,label:w,dynamicRules:t,required:s,auth:h,field:l}=e.schema,{disabled:B}=e.formProps,{disabled:b=!1}=n(m);if(B||b)return e.clearValidate(l),[];const{hasPermission:R}=vt(),{isShow:$}=F();if(h&&!R(h)||!$)return[];if(N(t)){const g=t(n(i));return s&&g.unshift({required:!0}),I(g),g}let P=be(c);const{rulesMessageJoinLabel:ne}=e.formProps,re=Reflect.has(e.schema,"rulesMessageJoinLabel")?p:ne,oe=We(r)+`${re?w:""}`;function K(g,S){const j=g.message||oe;return S===void 0||wt(S)||Array.isArray(S)&&S.length===0||typeof S=="string"&&S.trim()===""||typeof S=="object"&&Reflect.has(S,"checked")&&Reflect.has(S,"halfChecked")&&Array.isArray(S.checked)&&Array.isArray(S.halfChecked)&&S.checked.length===0&&S.halfChecked.length===0?Promise.reject(j):Promise.resolve()}const ee=N(s)?s(n(i)):s;(!P||P.length===0)&&ee&&(P=[{required:ee,validator:K}]);const te=P.findIndex(g=>Reflect.has(g,"required")&&!Reflect.has(g,"validator"));if(te!==-1){const g=P[te],{isShow:S}=F();if(S||(g.required=!1),r){g.message=g.message||oe,(r.includes("Input")||r.includes("Textarea"))&&(g.whitespace=!0);const j=(V=n(m))==null?void 0:V.valueFormat;kt(g,r,j)}}const y=P.findIndex(g=>g.max);return y!==-1&&!P[y].validator&&(P[y].message=P[y].message||a("component.form.maxTip",[P[y].max])),P.forEach(g=>{if(typeof g.pattern=="string")try{const S=new Function("item",`return ${g.pattern}`)(g);Object.prototype.toString.call(S)==="[object RegExp]"?g.pattern=S:g.pattern=new RegExp(g.pattern)}catch(S){g.pattern=new RegExp(g.pattern)}}),I(P),P}function L(){var S;const{renderComponentContent:c,component:r,field:p,changeEvent:w="change",valueField:t,componentProps:s,dynamicRules:h,rules:l=[]}=e.schema,B=r&&["Switch","Checkbox"].includes(r);let b=!1;r==="Input"&&s&&s.trim&&(b=!0);const R=`on${dt(w)}`,$=()=>[...N(h)?h(n(i)):[],...l],P={[R]:(...j)=>{const[H]=j;K[R]&&K[R](...j);const J=H?H.target:null;let z;J?B?z=J.checked:z=b?J.value.trim():J.value:z=H,e.setFormModel(p,z),$().find(ue=>(ue==null?void 0:ue.trigger)==="blur")||e.validateFields([p]).catch(ue=>{})}},ne=Ee.get(r),{autoSetPlaceHolder:re,size:oe}=e.formProps,K=De(O({allowClear:!0,getPopupContainer:j=>j==null?void 0:j.parentNode,size:oe},n(m)),{disabled:n(d)}),ee=e.schema.dynamicPropskey;if(ee&&(K[ee]=n(D)),!!re&&r!=="RangePicker"&&r){let j=N(e.schema.label)?e.schema.label():e.schema.label;u.getLocale==="en"&&!/^\s/.test(j)&&(j=" "+j),K.placeholder=((S=n(m))==null?void 0:S.placeholder)||We(r)+j}K.codeField=p,K.formValues=n(i);const y={[t||(B?"checked":"value")]:e.formModel[p]},V=O(O(O({},K),P),y);if(!c)return M(ne,V,null);const g=N(c)?O({},c(n(i))):{default:()=>c};return M(ne,V,fe(g)?g:{default:()=>[g]})}function q(){const{label:c,helpMessage:r,helpComponentProps:p,subLabel:w,labelLength:t}=e.schema,s=N(c)?c():c;let h=s+"";t&&(h=h.substr(0,t));const B=w?M("span",null,[s,pe(" "),M("span",{class:"text-secondary"},[w])]):t?M("label",{title:s},[h]):s,b=N(r)?r(n(i)):r;return!b||Array.isArray(b)&&b.length===0?B:M("span",null,[B,M(bt,Z({placement:"top",class:"mx-1",text:b},p),null)])}function x(){const{itemProps:c,slot:r,render:p,field:w,suffix:t,component:s}=e.schema,{labelCol:h,wrapperCol:l}=n(f),{colon:B}=e.formProps;if(s==="Divider"){let b;return M(ye,{span:24},{default:()=>[M(ft,n(m),fe(b=q())?b:{default:()=>[b]})]})}else{let b;const R=()=>r?xe(o,r,n(i)):p?p(n(i)):L(),$=!!t,P=N(t)?t(n(i)):t;return M(Ie.Item,Z({name:w,colon:B,class:{"suffix-item":$}},c,{label:q(),rules:k(),validateFirst:!0,labelCol:h,wrapperCol:l}),{default:()=>[M("div",{style:"display:flex"},[M($t,{formName:e.formName,fieldName:w},fe(b=R())?b:{default:()=>[b]}),$&&M("span",{class:"suffix"},[P])])]})}}return()=>{let c;const{colProps:r={},colSlot:p,renderColContent:w,component:t}=e.schema;if(!Ee.has(t))return null;const{baseColProps:s={}}=e.formProps,{getIsMobile:h}=At();let l;l=O(O({},s),r),r.span&&!n(h)&&["xs","sm","md","lg","xl","xxl"].forEach(P=>delete l[P]);const{isIfShow:B,isShow:b}=F(),R=n(i);return B&&He(M(ye,l,fe(c=p?xe(o,p,R):w?w(R):x())?c:{default:()=>[c]}),[[nt,b]])}}}),Je=Symbol();function xt(e){return Ft(e,Je)}function _t(){return Ct(Je)}const Wt=Pe({name:"BasicFormAction",components:{FormItem:Ie.Item,Button:Bt,BasicArrow:yt,[ye.name]:ye},props:{showActionButtonGroup:T.bool.def(!0),showResetButton:T.bool.def(!0),showSubmitButton:T.bool.def(!0),showAdvancedButton:T.bool.def(!0),resetButtonOptions:{type:Object,default:()=>({})},submitButtonOptions:{type:Object,default:()=>({})},actionColOptions:{type:Object,default:()=>({})},actionSpan:T.number.def(6),isAdvanced:T.bool,hideAdvanceBtn:T.bool,layout:T.oneOf(["horizontal","vertical","inline"]).def("horizontal")},emits:["toggle-advanced"],setup(e,{emit:o}){const{t:a}=Te(),u=W(()=>{const{showAdvancedButton:i,actionSpan:m,actionColOptions:d}=e,D=24-m,F=i?{span:D<6?24:D}:{},v=e.layout=="inline"?{}:{span:i?6:4};return O(O(O({style:{textAlign:"right"}},v),F),d)}),C=W(()=>Object.assign({text:a("common.resetText"),preIcon:"ic:baseline-restart-alt"},e.resetButtonOptions)),A=W(()=>Object.assign({},{text:a("common.queryText"),preIcon:"ant-design:search-outlined"},e.submitButtonOptions));function f(){o("toggle-advanced")}return O({t:a,actionColOpt:u,getResetBtnOptions:C,getSubmitBtnOptions:A,toggleAdvanced:f},_t())}});function qt(e,o,a,u,C,A){const f=X("Button"),i=X("BasicArrow"),m=X("FormItem"),d=X("a-col");return e.showActionButtonGroup?(U(),ae(d,Ce(Z({key:0},e.actionColOpt)),{default:Y(()=>[st("div",{class:"btnArea",style:at([{width:"100%"},{textAlign:e.actionColOpt.style.textAlign}])},[M(m,null,{default:Y(()=>[G(e.$slots,"submitBefore",{},void 0,!0),e.showSubmitButton?(U(),ae(f,Z({key:0,type:"primary",class:"mr-2"},e.getSubmitBtnOptions,{onClick:e.submitAction}),{default:Y(()=>[pe(Ae(e.getSubmitBtnOptions.text),1)]),_:1},16,["onClick"])):de("",!0),G(e.$slots,"resetBefore",{},void 0,!0),e.showResetButton?(U(),ae(f,Z({key:1,type:"default",class:"mr-2"},e.getResetBtnOptions,{onClick:e.resetAction}),{default:Y(()=>[pe(Ae(e.getResetBtnOptions.text),1)]),_:1},16,["onClick"])):de("",!0),G(e.$slots,"advanceBefore",{},void 0,!0),e.showAdvancedButton&&!e.hideAdvanceBtn?(U(),ae(f,{key:2,type:"link",size:"small",onClick:e.toggleAdvanced},{default:Y(()=>[pe(Ae(e.isAdvanced?e.t("component.form.putAway"):e.t("component.form.unfold"))+" ",1),M(i,{class:"ml-1",expand:!e.isAdvanced,up:""},null,8,["expand"])]),_:1},8,["onClick"])):de("",!0),G(e.$slots,"advanceAfter",{},void 0,!0)]),_:3})],4)]),_:3},16)):de("",!0)}const Ht=je(Wt,[["render",qt],["__scopeId","data-v-8803d71f"]]);function Kt(e,o){var C,A;return((C=n(e))==null?void 0:C.fieldMapToTime)&&(o=zt(e,o)),((A=n(e))==null?void 0:A.fieldMapToNumber)&&(o=Yt(e,o)),o}function zt(e,o){const a=n(e).fieldMapToTime;if(!a||!Array.isArray(a))return o;for(const[u,[C,A],f="YYYY-MM-DD"]of a){if(!u||!C||!A||!o[u])continue;let i=o[u];Array.isArray(i)||(i=i.split(","));const[m,d]=i;m&&(o[C]=Q(m).format(f)),d&&(o[A]=Q(d).format(f)),Reflect.deleteProperty(o,u)}return o}function Yt(e,o){const a=n(e).fieldMapToNumber;if(!a||!Array.isArray(a))return o;for(const[u,[C,A]]of a){if(!u||!C||!A||!o[u])continue;let f=o[u];typeof f=="string"&&(f=f.split(","));const[i,m]=f;o[C]=i,o[A]=m,Reflect.deleteProperty(o,u)}return o}function Gt({defaultValueRef:e,getSchema:o,formModel:a,getProps:u}){function C(f){if(!ce(f))return{};const i={};for(const m of Object.entries(f)){let[,d]=m;const[D]=m;if(!D||ge(d)&&d.length===0||N(d))continue;const F=n(u).transformDateFunc;ce(d)&&(d=F==null?void 0:F(d)),ge(d)&&Be.isDayjs(d[0])&&Be.isDayjs(d[1])&&(d=d.map(v=>F==null?void 0:F(v))),ie(d)&&(d=d.trim()),mt(i,D,d)}return Kt(u,i)}function A(){const f=n(o),i={};f.forEach(m=>{const{defaultValue:d}=m;Ot(d)||(i[m.field]=d,a[m.field]=d)}),e.value=i}return{handleFormValues:C,initDefault:A}}const me=24;function Jt({advanceState:e,emit:o,getProps:a,getSchema:u,formModel:C,defaultValueRef:A}){const{realWidthRef:f,screenEnum:i,screenRef:m}=Pt(),d=W(()=>{if(!e.isAdvanced)return 0;const I=n(a).emptySpan||0;if(Se(I))return I;if(ce(I)){const{span:k=0}=I,L=n(m);return I[L.toLowerCase()]||k||0}return 0}),D=ze(v,30);he([()=>n(u),()=>e.isAdvanced,()=>n(f)],()=>{const{showAdvancedButton:I}=n(a);I&&D()},{immediate:!0});function F(I,k=0,L=!1,q=0){var s;const x=n(f),c=parseInt(I.md)||parseInt(I.xs)||parseInt(I.sm)||I.span||me,r=parseInt(I.lg)||c,p=parseInt(I.xl)||r,w=parseInt(I.xxl)||p;x<=i.LG?k+=c:x<i.XL?k+=r:x<i.XXL?k+=p:k+=w;let t=(s=n(a).autoAdvancedCol)!=null?s:3;return L?(e.hideAdvanceBtn=n(u).length<=t,e.isLoad||(e.isLoad=!0,e.isAdvanced=!e.isAdvanced,n(u).length>t&&(e.hideAdvanceBtn=!1,e.isAdvanced=!1)),{isAdvanced:e.isAdvanced,itemColSum:k}):k>me*(n(a).alwaysShowLines||1)?{isAdvanced:e.isAdvanced,itemColSum:k}:!e.isAdvanced&&q+1>t?{isAdvanced:!1,itemColSum:k}:{isAdvanced:!0,itemColSum:k}}function v(){let I=0,k=0;const{baseColProps:L={}}=n(a),q=n(u);for(let x=0;x<q.length;x++){const c=q[x],{show:r,colProps:p}=c;let w=!0;if(le(r)&&(w=r),N(r)&&(w=r({schema:c,model:C,field:c.field,values:O(O({},n(A)),C)})),w&&(p||L)){const{itemColSum:t,isAdvanced:s}=F(O(O({},L),p),I,!1,x);I=t||0,s&&(k=I),c.isAdvanced=s}}e.actionSpan=k%me+n(d),F(n(a).actionColOptions||{span:me},I,!0),o("advanced-change")}function E(){e.isAdvanced=!e.isAdvanced}return{handleToggleAdvanced:E}}function Ut({emit:e,getProps:o,formModel:a,getSchema:u,defaultValueRef:C,formElRef:A,schemaRef:f,handleFormValues:i}){function m(){return _(this,null,function*(){const{resetFunc:t,submitOnReset:s}=n(o);t&&N(t)&&(yield t()),n(A)&&(Object.keys(a).forEach(l=>{a[l]=C.value[l]}),r(),e("reset",Me(a)),s&&w())})}function d(t){return _(this,null,function*(){const s=n(u).map(l=>l.field).filter(Boolean),h=[];Object.keys(t).forEach(l=>{const B=n(u).find($=>$.field===l);let b=t[l];if(!(t instanceof Object))return;const R=Reflect.has(t,l);if(b=Vt(B==null?void 0:B.component,b),b=Dt(B==null?void 0:B.component,b),R&&s.includes(l)){if(q(l))if(Array.isArray(b)){const $=[];for(const P of b)$.push(P?Q(P):null);a[l]=$}else{const{componentProps:$}=B||{};let P=$;typeof $=="function"&&(P=P({formModel:a})),a[l]=b?P!=null&&P.valueFormat?b:Q(b):null}else a[l]=b;h.push(l)}}),x(h).catch(l=>{})})}function D(t){if(!ie(t))return null;const s=n(u),h=s.findIndex(l=>l.field===t);return h!==-1?be(s[h]):null}function F(t){return _(this,null,function*(){const s=be(n(u));if(!t)return;let h=ie(t)?[t]:t;ie(t)&&(h=[t]);for(const l of h)v(l,s);f.value=s})}function v(t,s){if(ie(t)){const h=s.findIndex(l=>l.field===t);h!==-1&&(delete a[t],s.splice(h,1))}}function E(t,s,h=!1){return _(this,null,function*(){const l=be(n(u)),B=l.findIndex(R=>R.field===s);if(l.some(R=>R.field===s||t.field)){if(!s||B===-1||h){h?l.unshift(t):l.push(t),f.value=l;return}B!==-1&&l.splice(B+1,0,t),f.value=l}})}function I(t){return _(this,null,function*(){let s=[];if(ce(t)&&s.push(t),ge(t)&&(s=[...t]),!s.every(l=>l.component==="Divider"||Reflect.has(l,"field")&&l.field)){_e("All children of the form Schema array that need to be updated must contain the `field` field");return}f.value=s})}function k(t){return _(this,null,function*(){let s=[];if(ce(t)&&s.push(t),ge(t)&&(s=[...t]),!s.every(B=>B.component==="Divider"||Reflect.has(B,"field")&&B.field)){_e("All children of the form Schema array that need to be updated must contain the `field` field");return}const l=[];s.forEach(B=>{n(u).forEach(b=>{if(b.field===B.field){const R=Ye(b,B);l.push(R)}else l.push(b)})}),f.value=pt(l,"field")})}function L(){return n(A)?i(Me(n(a))):{}}function q(t){return n(u).some(s=>s.field===t?Ge.includes(s.component):!1)}function x(t,s){return _(this,null,function*(){var h;return(h=n(A))==null?void 0:h.validateFields(t,s)})}function c(t){return _(this,null,function*(){var s;return yield(s=n(A))==null?void 0:s.validate(t)})}function r(t){return _(this,null,function*(){var s;yield(s=n(A))==null?void 0:s.clearValidate(t)})}function p(t,s){return _(this,null,function*(){var h;yield(h=n(A))==null?void 0:h.scrollToField(t,s)})}function w(t){return _(this,null,function*(){t&&t.preventDefault();const{submitFunc:s}=n(o);if(s&&N(s)){yield s();return}if(n(A))try{const l=yield c();for(let b in l)l[b]instanceof Array&&It(o,b)==="string"&&(l[b]=l[b].join(","));const B=i(l);e("submit",B)}catch(l){e("submit",{})}})}return{handleSubmit:w,clearValidate:r,validate:c,validateFields:x,getFieldsValue:L,updateSchema:k,resetSchema:I,getSchemaByField:D,appendSchemaByField:E,removeSchemaByFiled:F,resetFields:m,setFieldsValue:d,scrollToField:p}}function Xt(C){return _(this,arguments,function*({getSchema:e,getProps:o,formElRef:a,isInitedDefault:u}){rt(()=>_(null,null,function*(){if(n(u)||!n(o).autoFocusFirstItem)return;yield Ke();const A=n(e),f=n(a),i=f==null?void 0:f.$el;if(!f||!i||!A||A.length===0||!A[0].component.includes("Input"))return;const d=i.querySelector(".ant-row:first-child input");d&&(d==null||d.focus())}))})}const{form:Fe}=Oe,Qt={model:{type:Object,default:{}},labelWidth:{type:[Number,String],default:0},fieldMapToTime:{type:Array,default:()=>[]},fieldMapToNumber:{type:Array,default:()=>[]},compact:T.bool,schemas:{type:[Array],default:()=>[]},mergeDynamicData:{type:Object,default:null},baseRowStyle:{type:Object},baseColProps:{type:Object},autoSetPlaceHolder:T.bool.def(!0),autoSubmitOnEnter:T.bool.def(!1),submitOnReset:T.bool,size:T.oneOf(["default","small","large"]).def("default"),disabled:T.bool,emptySpan:{type:[Number,Object],default:0},showAdvancedButton:T.bool,transformDateFunc:{type:Function,default:e=>Be.isDayjs(e)?e==null?void 0:e.format("YYYY-MM-DD HH:mm:ss"):e},rulesMessageJoinLabel:T.bool.def(!0),autoAdvancedCol:T.number.def(3),alwaysShowLines:T.number.def(1),showActionButtonGroup:T.bool.def(!0),actionColOptions:Object,showResetButton:T.bool.def(!0),autoFocusFirstItem:T.bool,resetButtonOptions:Object,showSubmitButton:T.bool.def(!0),submitButtonOptions:Object,resetFunc:Function,submitFunc:Function,hideRequiredMark:T.bool,labelCol:{type:Object,default:Fe.labelCol},layout:T.oneOf(["horizontal","vertical","inline"]).def("horizontal"),tableAction:{type:Object},wrapperCol:{type:Object,default:Fe.wrapperCol},colon:T.bool.def(Fe.colon),labelAlign:T.string,rowProps:Object,autoSearch:T.bool.def(!1)},Zt=Pe({name:"BasicForm",components:{FormItem:Et,Form:Ie,Row:ht,FormAction:Ht},props:Qt,emits:["advanced-change","reset","submit","register"],setup(e,{emit:o,attrs:a}){const u=Ne({}),C=St(),A=Ne({isAdvanced:!1,hideAdvanceBtn:!0,isLoad:!1,actionSpan:6}),f=se({}),i=se(!1),m=se({}),d=se(null),D=se(null),{prefixCls:F}=Tt("basic-form"),v=W(()=>{let y=O(O({},e),n(m));return y.labelWidth&&(y.labelCol=void 0),y.layout==="inline"&&(y.labelCol===Oe.form.labelCol&&(y.labelCol=void 0),y.wrapperCol===Oe.form.wrapperCol&&(y.wrapperCol=void 0)),y}),E=W(()=>[F,{[`${F}--compact`]:n(v).compact,"jeecg-form-detail-effect":n(v).disabled}]),I=W(()=>{const{baseRowStyle:y={},rowProps:V}=n(v);return O({style:y},V)}),k=W(()=>O(O(O({},a),e),n(v))),L=W(()=>{const y=n(d)||n(v).schemas;for(const V of y){const{defaultValue:g,component:S,componentProps:j}=V;if(g&&Ge.includes(S)){let H="";if(j&&(H=j==null?void 0:j.valueFormat),!Array.isArray(g))H?V.defaultValue=Q(g,H).format(H):V.defaultValue=Q(g);else{const J=[];g.forEach(z=>{H?J.push(Q(z,H).format(H)):J.push(Q(z))}),J.forEach((z,Re)=>{g[Re]=z})}}}return n(v).showAdvancedButton?y.filter(V=>V.component!=="Divider"):y}),{handleToggleAdvanced:q}=Jt({advanceState:A,emit:o,getProps:v,getSchema:L,formModel:u,defaultValueRef:f}),{handleFormValues:x,initDefault:c}=Gt({getProps:v,defaultValueRef:f,getSchema:L,formModel:u});Xt({getSchema:L,getProps:v,isInitedDefault:i,formElRef:D});const{handleSubmit:r,setFieldsValue:p,clearValidate:w,validate:t,validateFields:s,getFieldsValue:h,updateSchema:l,resetSchema:B,getSchemaByField:b,appendSchemaByField:R,removeSchemaByFiled:$,resetFields:P,scrollToField:ne}=Ut({emit:o,getProps:v,formModel:u,getSchema:L,defaultValueRef:f,formElRef:D,schemaRef:d,handleFormValues:x});xt({resetAction:P,submitAction:r}),he(()=>n(v).model,()=>{const{model:y}=n(v);y&&p(y)},{immediate:!0}),he(()=>n(v).schemas,y=>{B(y!=null?y:[])}),he(()=>L.value,y=>{Ke(()=>{var V;(V=C==null?void 0:C.redoModalHeight)==null||V.call(C)}),!n(i)&&y!=null&&y.length&&(c(),i.value=!0)});function re(y){return _(this,null,function*(){m.value=Ye(n(m)||{},y)})}const oe=ze(r,300);function K(y,V){u[y]=V,e.autoSearch===!0&&oe()}function ee(y){const{autoSubmitOnEnter:V}=n(v);if(V&&y.key==="Enter"&&y.target&&y.target instanceof HTMLElement){const g=y.target;g&&g.tagName&&g.tagName.toUpperCase()=="INPUT"&&r()}}const te={getFieldsValue:h,setFieldsValue:p,resetFields:P,updateSchema:l,resetSchema:B,setProps:re,getProps:v,getSchemaByField:b,removeSchemaByFiled:$,appendSchemaByField:R,clearValidate:w,validateFields:s,validate:t,submit:r,scrollToField:ne};return lt(()=>{c(),o("register",te)}),O({getBindValue:k,handleToggleAdvanced:q,handleEnterPress:ee,formModel:u,defaultValueRef:f,advanceState:A,getRow:I,getProps:v,formElRef:D,getSchema:L,formActionType:te,setFormModel:K,getFormClass:E,getFormActionBindProps:W(()=>O(O({},v.value),A))},te)}});function en(e,o,a,u,C,A){const f=X("FormItem"),i=X("FormAction"),m=X("Row"),d=X("Form"),D=it("auth");return U(),ae(d,Z(e.getBindValue,{class:e.getFormClass,ref:"formElRef",model:e.formModel,onKeypress:ct(e.handleEnterPress,["enter"])}),{default:Y(()=>[M(m,Ce(Le(e.getRow)),{default:Y(()=>[G(e.$slots,"formHeader"),(U(!0),qe(ut,null,ve(e.getSchema,F=>He((U(),ae(f,{key:F.field,tableAction:e.tableAction,formActionType:e.formActionType,schema:F,formProps:e.getProps,allDefaultValues:e.defaultValueRef,formModel:e.formModel,formName:e.getBindValue.name,setFormModel:e.setFormModel,validateFields:e.validateFields,clearValidate:e.clearValidate},$e({_:2},[ve(Object.keys(e.$slots),v=>({name:v,fn:Y(E=>[G(e.$slots,v,Z({ref_for:!0},E||{}))])}))]),1032,["tableAction","formActionType","schema","formProps","allDefaultValues","formModel","formName","setFormModel","validateFields","clearValidate"])),[[D,F.auth]])),128)),M(i,Z(e.getFormActionBindProps,{onToggleAdvanced:e.handleToggleAdvanced}),$e({_:2},[ve(["resetBefore","submitBefore","advanceBefore","advanceAfter"],F=>({name:F,fn:Y(v=>[G(e.$slots,F,Ce(Le(v||{})))])}))]),1040,["onToggleAdvanced"]),G(e.$slots,"formFooter")]),_:3},16)]),_:3},16,["class","model","onKeypress"])}const dn=je(Zt,[["render",en]]);export{dn as B,Kt as h};
