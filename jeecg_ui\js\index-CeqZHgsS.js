var R=Object.defineProperty;var f=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,$=Object.prototype.propertyIsEnumerable;var S=(t,e,o)=>e in t?R(t,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[e]=o,_=(t,e)=>{for(var o in e||(e={}))V.call(e,o)&&S(t,o,e[o]);if(f)for(var o of f(e))$.call(e,o)&&S(t,o,e[o]);return t};import{d as w,f as x,r as W,I as y,ag as r,aB as m,ar as a,aD as h,at as v,k as i,q as s,ah as N,B as c}from"./vue-vendor-dy9k-Yad.js";import D from"./Step1-Cpitpl8W.js";import I from"./Step2-B_1jemRy.js";import b from"./Step3-Bl4Q-mwl.js";import{P as q}from"./index-CtJ0w2CP.js";import{a0 as n}from"./antd-vue-vendor-me9YkNVC.js";import{a as F}from"./index-CCWaWN5g.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./data-tH9hseJm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const j=w({name:"FormStepPage",components:{Step1:D,Step2:I,Step3:b,PageWrapper:q,[n.name]:n,[n.Step.name]:n.Step},setup(){const t=x(0),e=W({initSetp2:!1,initSetp3:!1});function o(p){t.value++,e.initSetp2=!0}function l(){t.value--}function u(p){t.value++,e.initSetp3=!0}function d(){t.value=0,e.initSetp2=!1,e.initSetp3=!1}return _({current:t,handleStep1Next:o,handleStep2Next:u,handleRedo:d,handleStepPrev:l},y(e))}}),z={class:"step-form-form"},A={class:"mt-5"};function E(t,e,o,l,u,d){const p=r("a-step"),P=r("a-steps"),g=r("Step1"),k=r("Step2"),B=r("Step3"),C=r("PageWrapper");return a(),m(C,{title:"分步表单",contentBackground:"",content:" 将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。",contentClass:"p-4"},{default:h(()=>[v("div",z,[i(P,{current:t.current},{default:h(()=>[i(p,{title:"填写转账信息"}),i(p,{title:"确认转账信息"}),i(p,{title:"完成"})]),_:1},8,["current"])]),v("div",A,[s(i(g,{onNext:t.handleStep1Next},null,8,["onNext"]),[[c,t.current===0]]),t.initSetp2?s((a(),m(k,{key:0,onPrev:t.handleStepPrev,onNext:t.handleStep2Next},null,8,["onPrev","onNext"])),[[c,t.current===1]]):N("",!0),t.initSetp3?s((a(),m(B,{key:1,onRedo:t.handleRedo},null,8,["onRedo"])),[[c,t.current===2]]):N("",!0)])]),_:1})}const Xt=F(j,[["render",E],["__scopeId","data-v-74ebc618"]]);export{Xt as default};
