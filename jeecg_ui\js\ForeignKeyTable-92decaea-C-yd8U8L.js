import{d as n,f as d,ag as s,aB as p,ar as c,aE as u}from"./vue-vendor-dy9k-Yad.js";import{cs as m,bx as r}from"./index-CCWaWN5g.js";import{L as b}from"./useTableSync-075826a1-CL-4GwR8.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var f=Object.defineProperty,y=Object.defineProperties,g=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,h=Object.prototype.hasOwnProperty,w=Object.prototype.propertyIsEnumerable,i=(e,t,a)=>t in e?f(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,v=(e,t)=>{for(var a in t||(t={}))h.call(t,a)&&i(e,a,t[a]);if(o)for(var a of o(t))w.call(t,a)&&i(e,a,t[a]);return e},O=(e,t)=>y(e,g(t));const k=n({name:"ForeignKeyTable",props:{actionButton:{type:Boolean,default:!0}},setup(){const e=d([{title:"字段名称",key:"dbFieldName",width:160},{title:"字段备注",key:"dbFieldTxt",width:160},{title:"主表名",key:"mainTable",width:280,type:r.input,defaultValue:""},{title:"主表字段",key:"mainField",width:280,type:r.input,defaultValue:""}]),t=b(e);return O(v({},t),{columns:e})}});function x(e,t,a,B,P,j){const l=s("JVxeTable");return c(),p(l,u({ref:"tableRef",rowNumber:"",keyboardEdit:"",maxHeight:e.tableHeight.noToolbar,loading:e.loading,columns:e.columns,dataSource:e.dataSource,disabled:!e.actionButton,disabledRows:{dbFieldName:["id","has_child"]}},e.tableProps),null,16,["maxHeight","loading","columns","dataSource","disabled"])}const N=m(k,[["render",x]]);export{N as default};
