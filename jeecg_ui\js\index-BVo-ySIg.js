import{d as c,ag as n,aq as f,ar as b,k as e,at as p,aD as s,G as o}from"./vue-vendor-dy9k-Yad.js";import{K as a,a0 as _,a2 as D}from"./antd-vue-vendor-me9YkNVC.js";import{a as x}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const y=c({components:{Result:D,Steps:_,Step:_.Step,Descriptions:a,DescriptionItem:a.Item}}),S={class:"m-5 result-success"},k={class:"result-success__content"};function I(g,t,v,C,$,B){const i=n("a-button"),d=n("Result"),r=n("DescriptionItem"),u=n("Descriptions"),l=n("Step"),m=n("Steps");return b(),f("div",S,[e(d,{status:"success",title:"提交成功","sub-title":"提交结果页用于反馈一系列操作任务的处理结果， 如果仅是简单操作，使用 Message 全局提示反馈即可。 本文字区域可以展示简单的补充说明，如果有类似展示 “单据”的需求，下面这个灰色区域可以呈现比较复杂的内容。"},{extra:s(()=>[e(i,{key:"console",type:"primary"},{default:s(()=>t[0]||(t[0]=[o(" 返回列表 ")])),_:1,__:[0]}),e(i,{key:"buy"},{default:s(()=>t[1]||(t[1]=[o(" 查看项目 ")])),_:1,__:[1]}),e(i,{key:"buy"},{default:s(()=>t[2]||(t[2]=[o(" 打印 ")])),_:1,__:[2]})]),_:1}),p("div",k,[e(u,{title:"项目名称"},{default:s(()=>[e(r,{label:"项目 ID"},{default:s(()=>t[3]||(t[3]=[o(" 111222 ")])),_:1,__:[3]}),e(r,{label:"负责人"},{default:s(()=>t[4]||(t[4]=[o(" Jeecg ")])),_:1,__:[4]}),e(r,{label:"生效时间"},{default:s(()=>t[5]||(t[5]=[o(" 2016-12-12 ~ 2017-12-12 ")])),_:1,__:[5]})]),_:1}),e(m,{current:1,"progress-dot":"",size:"small"},{default:s(()=>[e(l,{title:"创建项目"},{description:s(()=>t[6]||(t[6]=[p("div",null,"Jeecg",-1),o(),p("p",null,"2016-12-12 12:32",-1)])),_:1}),e(l,{title:"部门初审"},{description:s(()=>t[7]||(t[7]=[p("p",null,"Chad",-1)])),_:1}),e(l,{title:"财务复核"}),e(l,{title:"完成"})]),_:1})])])}const q=x(y,[["render",I],["__scopeId","data-v-45f5d344"]]);export{q as default};
