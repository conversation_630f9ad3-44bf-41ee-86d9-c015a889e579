var A=Object.defineProperty,E=Object.defineProperties;var I=Object.getOwnPropertyDescriptors;var T=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var O=(e,o,t)=>o in e?A(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,w=(e,o)=>{for(var t in o||(o={}))M.call(o,t)&&O(e,t,o[t]);if(T)for(var t of T(o))X.call(o,t)&&O(e,t,o[t]);return e},S=(e,o)=>E(e,I(o));var v=(e,o,t)=>new Promise((g,f)=>{var s=l=>{try{a(t.next(l))}catch(p){f(p)}},m=l=>{try{a(t.throw(l))}catch(p){f(p)}},a=l=>l.done?g(l.value):Promise.resolve(l.value).then(s,m);a((t=t.apply(e,o)).next())});import{u as k,f as j,r as K}from"./vue-vendor-dy9k-Yad.js";import{v as L}from"./antd-vue-vendor-me9YkNVC.js";import{u as P}from"./index-BkGZ5fiW.js";import{E as D,u as U,F as W,n as $,G as q}from"./index-CCWaWN5g.js";import"./BasicTable-xCEZpGLb.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const{handleExportXls:z,handleImportXls:B}=D();function Ve(e){const o=U();let t={};e.designScope&&(t=W(e.designScope));const g=G(e.tableProps),[,{getForm:f,reload:s,setLoading:m},{selectedRowKeys:a}]=g;function l(){return v(this,null,function*(){var d,x;let{url:r,name:i,params:n}=(d=e==null?void 0:e.exportConfig)!=null?d:{},u=typeof r=="function"?r():r;if(u){let b=typeof i=="function"?i():i,c={};try{((x=e==null?void 0:e.tableProps)==null?void 0:x.useSearchForm)!==!1&&(c=yield f().validate())}catch(h){}if(c!=null&&c.column||Object.assign(c,{column:"createTime",order:"desc"}),n){const h=$(n)?yield n():w({},n||{});Object.keys(h).map(F=>{let R=h[F];R&&(c[F]=k(R))})}return a.value&&a.value.length>0&&(c.selections=a.value.join(",")),z(b,u,q(c))}else return o.createMessage.warn("没有传递 exportConfig.url 参数"),Promise.reject()})}function p(r){var d;let{url:i,success:n}=(d=e==null?void 0:e.importConfig)!=null?d:{},u=typeof i=="function"?i():i;return u?B(r,u,n||s):(o.createMessage.warn("没有传递 importConfig.url 参数"),Promise.reject())}function C(r,i){return new Promise((n,u)=>{var x;const d=()=>v(null,null,function*(){var b,c;try{m(!0);const h=yield r();((b=i==null?void 0:i.reload)==null||b)&&s(),((c=i==null?void 0:i.clearSelection)==null||c)&&(a.value=[]),n(h)}catch(h){u(h)}finally{m(!1)}});(x=i==null?void 0:i.confirm)==null||x?o.createConfirm({iconType:"warning",title:"删除",content:"确定要删除吗？",onOk:()=>d(),onCancel:()=>u()}):d()})}function y(r){return C(r,{confirm:!1,clearSelection:!1})}return S(w(w({},t),o),{onExportXls:l,onImportXls:p,doRequest:C,doDeleteRecord:y,tableContext:g})}function G(e){var p,C,y;const o={xs:24,sm:12,md:12,lg:8,xl:8,xxl:6},t={rowKey:"id",useSearchForm:!0,formConfig:{compact:!0,autoSubmitOnEnter:!0,rowProps:{gutter:8},baseColProps:w({},o),labelCol:{xs:24,sm:8,md:6,lg:8,xl:6,xxl:6},wrapperCol:{},showAdvancedButton:!0,autoAdvancedCol:3,actionColOptions:S(w({},o),{style:{textAlign:"left"}})},striped:!1,canResize:!0,minHeight:300,clickToRowSelect:!1,bordered:!0,showIndexColumn:!1,showTableSetting:!0,tableSetting:{fullScreen:!1},showActionColumn:!0,actionColumn:{width:120,title:"操作",fixed:!1,dataIndex:"action",slots:{customRender:"action"}}};e&&(e.formConfig&&l(e.formConfig),L(t,e));function g(r){return Object.assign({column:"createTime",order:"desc"},r)}Object.assign(t,{beforeFetch:g}),typeof e.beforeFetch=="function"&&(t.beforeFetch=function(r){return r=g(r),e.beforeFetch(r),r});const f=j([]),s=j([]),m=(p=e==null?void 0:e.rowSelection)!=null?p:{},a=K(S(w({},m),{type:(C=m.type)!=null?C:"checkbox",columnWidth:(y=m.columnWidth)!=null?y:50,selectedRows:s,selectedRowKeys:f,onChange(...r){f.value=r[0],s.value=r[1],typeof m.onChange=="function"&&m.onChange(...r)}}));delete t.rowSelection;function l(r){const i=["baseColProps","labelCol"];for(let n of i)if(r&&r[n]){if(t.formConfig){let u=t.formConfig;u[n]=r[n]}r[n]={}}}return[...P(t),{selectedRows:s,selectedRowKeys:f,rowSelection:a}]}export{Ve as useListPage,G as useListTable};
