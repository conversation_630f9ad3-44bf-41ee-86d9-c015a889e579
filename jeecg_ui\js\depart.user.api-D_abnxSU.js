import{u as y}from"./vue-vendor-dy9k-Yad.js";import{j as s,u as n}from"./index-CCWaWN5g.js";const{createConfirm:l}=n();const p=e=>s.get({url:"/sys/sysDepart/queryMyDeptTreeList",params:e},{isTransformResponse:!1}),D=e=>s.get({url:"/sys/sysDepart/queryIdTree",params:e}),d=e=>s.get({url:"/sys/sysDepart/searchBy",params:e}),c=e=>s.get({url:"/sys/user/departUserList",params:e}),R=(e,t)=>s.post({url:"/sys/user/editSysDepartWithUser",params:{depId:e,userIdList:t}}),m=(e,t=!1)=>new Promise((a,o)=>{const r=()=>{a(s.delete({url:"/sys/user/deleteUserInDepartBatch",params:e},{joinParamsToUrl:!0}))};t?l({iconType:"warning",title:"取消关联",content:"确定要取消关联吗？",onOk:()=>r(),onCancel:()=>o()}):r()}),g=e=>s.get({url:"/sys/sysDepartRole/list",params:e}),q=(e,t)=>t?s.put({url:"/sys/sysDepartRole/edit",params:e}):s.post({url:"/sys/sysDepartRole/add",params:e}),U=(e,t=!1)=>new Promise((a,o)=>{const r=()=>{a(s.delete({url:"/sys/sysDepartRole/deleteBatch",params:e},{joinParamsToUrl:!0}))};t?l({iconType:"warning",title:"删除",content:"确定要删除吗？",onOk:()=>r(),onCancel:()=>o()}):r()}),P=e=>s.get({url:"/sys/sysDepartPermission/queryTreeListForDeptRole",params:e}),B=e=>s.get({url:"/sys/sysDepartPermission/queryDeptRolePermission",params:e}),L=e=>s.post({url:"/sys/sysDepartPermission/saveDeptRolePermission",params:e}),T=(e,t,a,o)=>{let r=`/sys/sysDepartRole/datarule/${y(e)}/${y(t)}/${y(a)}`;return s.get({url:r,params:o})},h=e=>s.post({url:"/sys/sysDepartRole/datarule",params:e}),f=e=>s.get({url:"/sys/sysDepartRole/getDeptRoleList",params:e}),v=e=>s.get({url:"/sys/sysDepartRole/getDeptRoleByUserId",params:e}),I=e=>s.post({url:"/sys/sysDepartRole/deptRoleUserAdd",params:e});export{P as a,B as b,L as c,T as d,h as e,g as f,U as g,q as h,f as i,v as j,I as k,p as l,c as m,R as n,D as q,d as s,m as u};
