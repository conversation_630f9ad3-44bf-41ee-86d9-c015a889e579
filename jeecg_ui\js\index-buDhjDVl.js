import{d as v,ag as t,aB as m,ar as p,as as r,aD as n,at as a,k as s,aq as I,F as N,aC as w,ah as y,G as d,au as B}from"./vue-vendor-dy9k-Yad.js";import{c as b,a as D}from"./index-CCWaWN5g.js";import{cardList as L}from"./data-BCknVrL5.js";import{P}from"./index-CtJ0w2CP.js";import{a7 as u,a6 as _,L as c,J as f}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const S=v({components:{Icon:b,PageWrapper:P,[f.name]:f,[c.name]:c,[c.Item.name]:c.Item,[_.name]:_,[u.name]:u},setup(){return{prefixCls:"list-card",list:L}}});function T(o,e,W,A,F,q){const i=t("Icon"),C=t("a-card"),x=t("a-list-item"),g=t("a-col"),$=t("a-row"),V=t("a-list"),k=t("PageWrapper");return p(),m(k,{class:r(o.prefixCls),title:"卡片列表"},{headerContent:n(()=>[e[3]||(e[3]=d(" 基于Vue Next, TypeScript, Ant Design Vue实现的一套完整的企业级后台管理系统。 ")),a("div",{class:r(`${o.prefixCls}__link`)},[a("a",null,[s(i,{icon:"bx:bx-paper-plane",color:"#1890ff"}),e[0]||(e[0]=a("span",null,"开始",-1))]),a("a",null,[s(i,{icon:"carbon:warning",color:"#1890ff"}),e[1]||(e[1]=a("span",null,"简介",-1))]),a("a",null,[s(i,{icon:"ion:document-text-outline",color:"#1890ff"}),e[2]||(e[2]=a("span",null,"文档",-1))])],2)]),default:n(()=>[a("div",{class:r(`${o.prefixCls}__content`)},[s(V,null,{default:n(()=>[s($,{gutter:16},{default:n(()=>[(p(!0),I(N,null,w(o.list,l=>(p(),m(g,{key:l.title,span:6},{default:n(()=>[s(x,null,{default:n(()=>[s(C,{hoverable:!0,class:r(`${o.prefixCls}__card`)},{default:n(()=>[a("div",{class:r(`${o.prefixCls}__card-title`)},[l.icon?(p(),m(i,{key:0,class:"icon",icon:l.icon,color:l.color},null,8,["icon","color"])):y("",!0),d(" "+B(l.title),1)],2),a("div",{class:r(`${o.prefixCls}__card-detail`)}," 基于Vue Next, TypeScript, Ant Design Vue实现的一套完整的企业级后台管理系统 ",2)]),_:2},1032,["class"])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})],2)]),_:1},8,["class"])}const U=D(S,[["render",T],["__scopeId","data-v-a86992d8"]]);export{U as default};
