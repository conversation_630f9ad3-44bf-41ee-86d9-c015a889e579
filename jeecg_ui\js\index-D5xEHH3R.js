var K=Object.defineProperty,z=Object.defineProperties;var Q=Object.getOwnPropertyDescriptors;var T=Object.getOwnPropertySymbols;var U=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable;var y=(r,o,t)=>o in r?K(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,w=(r,o)=>{for(var t in o||(o={}))U.call(o,t)&&y(r,t,o[t]);if(T)for(var t of T(o))E.call(o,t)&&y(r,t,o[t]);return r},I=(r,o)=>z(r,Q(o));var R=(r,o,t)=>new Promise((_,p)=>{var g=a=>{try{s(t.next(a))}catch(u){p(u)}},l=a=>{try{s(t.throw(a))}catch(u){p(u)}},s=a=>a.done?_(a.value):Promise.resolve(a.value).then(g,l);s((t=t.apply(r,o)).next())});import{d as k,f as c,ag as F,aq as d,ar as f,k as v,aD as m,au as b,u as h,at as G,G as H}from"./vue-vendor-dy9k-Yad.js";import{u as W}from"./index-BkGZ5fiW.js";import{aX as C,u as X,a as j}from"./index-CCWaWN5g.js";import{q as J}from"./certificationAudit-CpD5ybP4.js";import L from"./AuditModal-wcsKCDaN.js";import O from"./DetailModal-CTc3CxWd.js";import{Q as Y}from"./componentMap-Bkie1n3v.js";import Z from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./CustomModal-BakuIxQv.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const $={class:"p-4"},ee={key:0},te={key:1},oe={key:0},ie={key:1},ae=k({name:"CertificationAudit"}),re=k(I(w({},ae),{setup(r){const{createMessage:o}=X(),t=[{title:"序号",dataIndex:"index",width:60,customRender:({index:e})=>e+1},{title:"ID",dataIndex:"id",width:150,customRender:({text:e})=>e||"-"},{title:"姓名",dataIndex:"name",width:100,customRender:({text:e})=>e||"-"},{title:"手机号",dataIndex:"phone",width:120,customRender:({text:e})=>e||"-"},{title:"认证类型",dataIndex:"authType",width:100,slots:{customRender:"authType"}},{title:"审核状态",dataIndex:"review",width:100,slots:{customRender:"reviewStatus"}},{title:"提交时间",dataIndex:"submitTime",width:160,slots:{customRender:"submitTime"}},{title:"审核时间",dataIndex:"reviewTime",width:160,slots:{customRender:"reviewTime"}}],_=[{key:"all",label:"全部",icon:""},{key:"1",label:"未审核",icon:""},{key:"2",label:"已通过",icon:""},{key:"3",label:"未通过",icon:""}],p=c("all"),g=c({}),l=c(!1),s=c(!1),a=c(null);function u(e){p.value=e;let n={};e!=="all"&&(n={review:Number(e)}),g.value=n,x()}function N(e){return R(this,null,function*(){const n=w(w({},e),g.value);return J(n)})}const[P,{reload:x}]=W({api:N,columns:t,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,showNavigation:!0,navigationItems:_,activeNavigationKey:p.value,inset:!0,maxHeight:458,actionColumn:{width:200,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"},formConfig:{labelWidth:80,size:"large",labelAlign:"left",schemas:[{field:"phone",label:"手机号",component:"Input",colProps:{span:6},componentProps:{placeholder:"请输入手机号"}},{field:"name",label:"姓名",component:"Input",colProps:{span:6},componentProps:{placeholder:"请输入姓名"}},{field:"enterpriseName",label:"企业名称",component:"Input",colProps:{span:6},componentProps:{placeholder:"请输入企业名称"}}]}});function S(e){const n=[{label:"查看",onClick:D.bind(null,e)}];return e.review===1&&n.unshift({label:"审核",onClick:A.bind(null,e)}),n}function A(e){a.value=e,l.value=!0}function D(e){a.value=e,s.value=!0}function V(){l.value=!1,a.value=null,x(),o.success("审核操作成功")}function M(e){switch(e){case 1:return"orange";case 2:return"green";case 3:return"red";default:return"default"}}function q(e){switch(e){case 1:return"未审核";case 2:return"已通过";case 3:return"未通过";default:return"-"}}return(e,n)=>{const B=F("a-tag");return f(),d("div",$,[v(h(Z),{onRegister:h(P),onNavigationChange:u},{action:m(({record:i})=>[v(h(Y),{actions:S(i)},null,8,["actions"])]),reviewStatus:m(({record:i})=>[v(B,{color:M(i.review)},{default:m(()=>[H(b(q(i.review)),1)]),_:2},1032,["color"])]),authType:m(({record:i})=>[G("span",null,b(i.authType||"-"),1)]),submitTime:m(({text:i})=>[i?(f(),d("span",ee,b(h(C)(i)),1)):(f(),d("span",te,"-"))]),reviewTime:m(({text:i})=>[i?(f(),d("span",oe,b(h(C)(i)),1)):(f(),d("span",ie,"-"))]),_:1},8,["onRegister"]),v(L,{open:l.value,"onUpdate:open":n[0]||(n[0]=i=>l.value=i),record:a.value,onSuccess:V},null,8,["open","record"]),v(O,{open:s.value,"onUpdate:open":n[1]||(n[1]=i=>s.value=i),record:a.value},null,8,["open","record"])])}}})),dt=j(re,[["__scopeId","data-v-f7029cb6"]]);export{dt as default};
