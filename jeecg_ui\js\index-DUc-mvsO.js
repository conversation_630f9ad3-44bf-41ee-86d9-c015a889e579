var f=(t,o,s)=>new Promise((d,n)=>{var c=e=>{try{i(s.next(e))}catch(a){n(a)}},r=e=>{try{i(s.throw(e))}catch(a){n(a)}},i=e=>e.done?d(e.value):Promise.resolve(e.value).then(c,r);i((s=s.apply(t,o)).next())});import{d as P,e as C,ag as p,aB as b,ar as g,aD as u,k as m,at as B,G as l}from"./vue-vendor-dy9k-Yad.js";import M from"./CurrentPermissionMode-DzGbOpfp.js";import{X as A,ah as T,B as $,bC as v,bE as w,a as S}from"./index-CCWaWN5g.js";import{P as y}from"./index-CtJ0w2CP.js";import{A as E}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const N=P({components:{Alert:E,CurrentPermissionMode:M,PageWrapper:y},setup(){const{refreshMenu:t}=A(),o=T(),s=$(),d=C(()=>s.getProjectConfig.permissionMode===v.BACK);function n(c){return f(this,null,function*(){const r="fakeToken"+c;o.setToken(r),o.getUserInfoAction(),t()})}return{RoleEnum:w,refreshMenu:t,switchToken:n,isBackPremissionMode:d}}}),V={class:"mt-4"};function W(t,o,s,d,n,c){const r=p("CurrentPermissionMode"),i=p("Alert"),e=p("a-button"),a=p("a-button-group"),_=p("PageWrapper");return g(),b(_,{title:"后台权限示例",contentBackground:"",contentClass:"p-4",content:"目前mock了两组数据， id为1 和 2 具体返回的菜单可以在mock/sys/menu.ts内查看"},{default:u(()=>[m(r),m(i,{class:"mt-4",type:"info",message:"点击后请查看左侧菜单变化","show-icon":""}),B("div",V,[o[4]||(o[4]=l(" 权限切换(请先切换权限模式为后台权限模式): ")),m(a,null,{default:u(()=>[m(e,{onClick:o[0]||(o[0]=k=>t.switchToken(1)),disabled:!t.isBackPremissionMode},{default:u(()=>o[2]||(o[2]=[l(" 获取用户id为1的菜单 ")])),_:1,__:[2]},8,["disabled"]),m(e,{onClick:o[1]||(o[1]=k=>t.switchToken(2)),disabled:!t.isBackPremissionMode},{default:u(()=>o[3]||(o[3]=[l(" 获取用户id为2的菜单 ")])),_:1,__:[3]},8,["disabled"])]),_:1})])]),_:1})}const H=S(N,[["render",W],["__scopeId","data-v-e4c6395b"]]);export{H as default};
