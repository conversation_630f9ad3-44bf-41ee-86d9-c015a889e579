var A=Object.defineProperty;var j=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable;var B=(e,r,t)=>r in e?A(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,_=(e,r)=>{for(var t in r||(r={}))M.call(r,t)&&B(e,t,r[t]);if(j)for(var t of j(r))U.call(r,t)&&B(e,t,r[t]);return e};var b=(e,r,t)=>new Promise((f,c)=>{var F=o=>{try{a(t.next(o))}catch(i){c(i)}},p=o=>{try{a(t.throw(o))}catch(i){c(i)}},a=o=>o.done?f(o.value):Promise.resolve(o.value).then(F,p);a((t=t.apply(e,r)).next())});import"./index-L3cSIXth.js";import{d as $,e as H,f as y,r as L,ag as l,aq as N,ar as v,k as u,aD as D,aB as P,ah as E}from"./vue-vendor-dy9k-Yad.js";import{a as G,j as T,H as w}from"./index-CCWaWN5g.js";import{a as W,j as z}from"./data-BTFJZHwa.js";import Q from"./JeecgOrderCustomerForm-C9lZkYS1.js";import{u as X}from"./useForm-CgkFTrrO.js";import{B as Y}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const Z=$({name:"JeecgOrderMainForm",components:{BasicForm:Y,JeecgOrderCustomerForm:Q},props:{formData:w.object.def({}),formBpm:w.bool.def(!0)},setup(e){const[r,{setFieldsValue:t,setProps:f,getFieldsValue:c,updateSchema:F}]=X({labelWidth:150,schemas:W(e.formData),showActionButtonGroup:!1,baseColProps:{span:8}}),p=H(()=>e.formData.disabled!==!1),a=y(),o=y(),i=y(!1);let d={};const h="/test/jeecgOrderMain/queryById";function S(){return b(this,null,function*(){let n={id:e.formData.dataId};const s=yield T.get({url:h,params:n});d=_({},s),yield t(d),yield f({disabled:p.value}),yield a.value.initFormData(e.formData.dataId),yield K(e.formData.dataId),i.value=!0})}function R(){return b(this,null,function*(){let n=c(),s=Object.assign({},d,n);yield saveOrUpdate(s,!0)})}S();const I=y("jeecgOrderCustomerForm");function J(){}const O=L({loading:!1,dataSource:[],columns:V(z,"order:")});function K(n){return b(this,null,function*(){const s="/test/jeecgOrderMain/queryOrderTicketListByMainId";let C={id:n};O.dataSource=[];const g=yield T.get({url:s,params:C});g&&g.length>0&&(O.dataSource=[...g])})}function V(n,s){let C=e.formData.permissionList;return n.filter(k=>{let m=C.find(q=>q.action===s+k.key);return m?(m instanceof Array&&(m=m[0]),m.type=="2"&&!m.isAuth?(k.disabled=!0,!0):!(m.type=="1"&&!m.isAuth)):!0})}return{registerForm:r,formDisabled:p,submitForm:R,jeecgOrderCustomerFormRef:a,activeKey:I,handleChangeTabs:J,table2:O,jeecgOrderTicketRef:o,ok:i}}}),x={class:"jeecg-flow-demo"};function ee(e,r,t,f,c,F){const p=l("BasicForm"),a=l("JeecgOrderCustomerForm"),o=l("a-tab-pane"),i=l("JVxeTable"),d=l("a-tabs");return v(),N("div",x,[u(p,{onRegister:e.registerForm},null,8,["onRegister"]),u(d,{activeKey:e.activeKey,"onUpdate:activeKey":r[0]||(r[0]=h=>e.activeKey=h),onChange:e.handleChangeTabs},{default:D(()=>[u(o,{tab:"客户信息",key:"jeecgOrderCustomerForm",forceRender:!0},{default:D(()=>[u(a,{ref:"jeecgOrderCustomerFormRef",formData:e.formData},null,8,["formData"])]),_:1}),u(o,{tab:"机票信息",key:"jeecgOrderTicket",forceRender:!0},{default:D(()=>[e.ok?(v(),P(i,{key:0,ref:"jeecgOrderTicketRef",stripe:"",rowSelection:"",keepSource:"",maxHeight:300,loading:e.table2.loading,columns:e.table2.columns,dataSource:e.table2.dataSource},null,8,["loading","columns","dataSource"])):E("",!0)]),_:1})]),_:1},8,["activeKey","onChange"])])}const at=G(Z,[["render",ee]]);export{at as default};
