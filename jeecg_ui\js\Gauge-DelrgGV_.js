import{d as x,f as S,r as v,h as A,aq as b,ar as C,aA as D}from"./vue-vendor-dy9k-Yad.js";import{useECharts as _}from"./useECharts-BU6FzBZi.js";import{h as $}from"./antd-vue-vendor-me9YkNVC.js";import{a as k}from"./index-CCWaWN5g.js";import"./echarts-D8q0NfgS.js";import{an as j}from"./renderers-CGMjx3X9.js";const B=x({name:"BarMulti",props:{chartData:{type:Array,default:()=>[],required:!0},option:{type:Object,default:()=>({})},type:{type:String,default:"bar"},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"},seriesColor:{type:Array,default:()=>[]}},emits:["click"],setup(t,{emit:i}){const o=S(null),{setOptions:s,getInstance:e}=_(o),a=v({tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0,backgroundColor:"#333"}}},legend:{top:30},grid:{top:60},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[]});A(()=>{t.chartData&&O()});function O(){var y,m;t.option&&Object.assign(a,$(t.option));let c=Array.from(new Set(t.chartData.map(r=>r.type))),d=Array.from(new Set(t.chartData.map(r=>r.name))),p=[];c.forEach(r=>{var g;let h={name:r,type:t.type},f=[];if(d.forEach(n=>{let l=t.chartData.filter(w=>r===w.type&&w.name==n);l&&l.length>0?f.push(l[0].value):f.push(null)}),h.data=f,(g=t.seriesColor)!=null&&g.length){const n=t.seriesColor.find(l=>l.type===r);n!=null&&n.color&&(h.color=n.color)}p.push(h)}),a.series=p,a.xAxis.data=d,s(a),(y=e())==null||y.off("click",u),(m=e())==null||m.on("click",u)}function u(c){i("click",c)}return{chartRef:o}}});function E(t,i,o,s,e,a){return C(),b("div",{ref:"chartRef",style:D({height:t.height,width:t.width})},null,4)}const W=k(B,[["render",E]]),z=x({name:"Gauge",props:{chartData:{type:Object,default:()=>[]},option:{type:Object,default:()=>({})},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"},seriesColor:{type:String,default:"#1890ff"}},setup(t){const i=S(null),{setOptions:o,echarts:s}=_(i),e=v({series:[{type:"gauge",progress:{show:!0,width:12},axisLine:{lineStyle:{width:12}},axisTick:{show:!1},splitLine:{length:12,lineStyle:{width:1,color:"#999"}},axisLabel:{distance:25,color:"#999",fontSize:12},anchor:{show:!0,showAbove:!0,size:20,itemStyle:{borderWidth:5}},title:{},detail:{valueAnimation:!0,fontSize:25,formatter:"{value}%",offsetCenter:[0,"80%"]},data:[{value:70,name:"本地磁盘"}]}]});A(()=>{t.chartData&&a()});function a(){s.use(j),t.option&&Object.assign(e,$(t.option)),e.series[0].data[0].name=t.chartData.name,e.series[0].data[0].value=t.chartData.value,e.series[0].color=t.seriesColor,o(e)}return{chartRef:i}}});function R(t,i,o,s,e,a){return C(),b("div",{ref:"chartRef",style:D({height:t.height,width:t.width})},null,4)}const F=k(z,[["render",R]]);export{W as B,F as G};
