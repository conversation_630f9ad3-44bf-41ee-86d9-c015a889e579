var b=(k,t,g)=>new Promise((e,C)=>{var w=i=>{try{c(g.next(i))}catch(d){C(d)}},_=i=>{try{c(g.throw(i))}catch(d){C(d)}},c=i=>i.done?e(i.value):Promise.resolve(i.value).then(w,_);c((g=g.apply(k,t)).next())});import{f as v,r as S,ag as l,aq as N,ar as M,at as a,k as o,aB as A,ah as J,aO as O,aD as n,G as x,F as Q,aC as T,au as B,A as K}from"./vue-vendor-dy9k-Yad.js";import"./index-Diw57m_E.js";import{l as E,d as F,r as U}from"./AiKnowledgeBase.api-Dgaf5KfS.js";import{x as j}from"./antd-vue-vendor-me9YkNVC.js";import{y as G}from"./componentMap-Bkie1n3v.js";import H from"./AiKnowledgeBaseModal-VLfDTh1H.js";import{l as W}from"./JSelectUser-COkExGbu.js";import{_ as X}from"./JAddInput-CxJ-JBK-.js";import Y from"./AiragKnowledgeDocListModal-CxywO6FL.js";import{c as Z,ad as V,u as $,a as ee}from"./index-CCWaWN5g.js";import{_ as oe}from"./knowledge-BXTupIwn.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectDept-I-NqkbOH.js";import"./props-CCT78mKr.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./AiKnowledgeBase.data-BCjuIj-H.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./index-BkGZ5fiW.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./AiragKnowledgeDocTextModal-BlzkYcnY.js";import"./AiTextDescModal-LVvvAihi.js";import"./index-mbACBRQ9.js";import"./header-OZa5fSDc.js";const te={name:"KnowledgeBaseList",components:{Icon:Z,AiragKnowledgeDocListModal:Y,KnowledgeBaseModal:H,JDictSelectTag:X,JSelectUser:W,JInput:G,Pagination:j},setup(){const k=v([]),[t,{openModal:g}]=V(),[e,{openModal:C}]=V(),w=v(1),_=v(10),c=v(0),i=v(["10","20","30"]),d=S({}),z=S({xs:24,sm:4,xl:6,xxl:6}),L=S({xs:24,sm:20}),m=v(),{createMessage:h}=$();p();function y(){return b(this,null,function*(){g(!0,{})})}function D(r){g(!0,{id:r.id,isUpdate:!0})}function p(){let r={pageNo:w.value,pageSize:_.value,column:"createTime",order:"desc"};Object.assign(r,d),E(r).then(u=>{u.success?(k.value=u.result.records,c.value=u.result.total):(k.value=[],c.value=0)})}function I(r,u){w.value=r,_.value=u,p()}function P(r){return b(this,null,function*(){yield F({id:r.id,name:r.name},p)})}function R(){p()}function s(){m.value.resetFields(),d.createBy="",p()}function f(r){C(!0,{id:r})}function q(r){return b(this,null,function*(){U({knowIds:r}).then(u=>{u.success?(h.success("向量化成功！"),p()):h.warning("向量化失败！")}).catch(u=>{h.warning("向量化失败！")})})}return{handleAddKnowled:y,handleEditClick:D,registerModal:t,knowledgeList:k,reload:p,pageNo:w,pageSize:_,pageSizeOptions:i,total:c,handlePageChange:I,handleDelete:P,searchQuery:R,searchReset:s,queryParam:d,labelCol:z,wrapperCol:L,formRef:m,handleDocClick:f,docListRegister:e,handleVectorization:q}}},ne={class:"knowledge"},ae={class:"jeecg-basic-table-form-container"},ie={style:{float:"left",overflow:"hidden"},class:"table-page-search-submitButtons"},le={class:"flex"},se={class:"knowledge-header"},re={class:"flex"},de={class:"header-text"},ce=["title"],me={class:"header-text-top"},pe={class:"mt-10 text-desc"},ge={class:"knowledge-footer"},ue={class:"knowledge-btn"};function _e(k,t,g,e,C,w){const _=l("JInput"),c=l("a-form-item"),i=l("a-col"),d=l("a-button"),z=l("a-row"),L=l("a-form"),m=l("Icon"),h=l("a-card"),y=l("a-menu-item"),D=l("a-menu"),p=l("a-dropdown"),I=l("Pagination"),P=l("KnowledgeBaseModal"),R=l("AiragKnowledgeDocListModal");return M(),N("div",ne,[a("div",ae,[o(L,{ref:"formRef",onKeyup:O(e.searchQuery,["enter","native"]),model:e.queryParam,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,style:{"background-color":"#f7f8fc"}},{default:n(()=>[o(z,{gutter:24},{default:n(()=>[o(i,{xl:7,lg:7,md:8,sm:24},{default:n(()=>[o(c,{name:"name",label:"知识库名称"},{default:n(()=>[o(_,{value:e.queryParam.name,"onUpdate:value":t[0]||(t[0]=s=>e.queryParam.name=s),placeholder:"请输入知识库名称"},null,8,["value"])]),_:1})]),_:1}),o(i,{xl:6,lg:7,md:8,sm:24},{default:n(()=>[a("span",ie,[o(i,{lg:6},{default:n(()=>[o(d,{type:"primary",preIcon:"ant-design:search-outlined",onClick:e.searchQuery},{default:n(()=>t[2]||(t[2]=[x("查询")])),_:1,__:[2]},8,["onClick"]),o(d,{type:"primary",preIcon:"ant-design:reload-outlined",onClick:e.searchReset,style:{"margin-left":"8px"}},{default:n(()=>t[3]||(t[3]=[x("重置")])),_:1,__:[3]},8,["onClick"])]),_:1})])]),_:1})]),_:1})]),_:1},8,["onKeyup","model","label-col","wrapper-col"])]),o(z,{span:24,class:"knowledge-row"},{default:n(()=>[o(i,{xxl:4,xl:6,lg:6,md:6,sm:12,xs:24},{default:n(()=>[o(h,{class:"add-knowledge-card",onClick:e.handleAddKnowled},{default:n(()=>[a("div",le,[o(m,{icon:"ant-design:plus-outlined",class:"add-knowledge-card-icon",size:"20"}),t[4]||(t[4]=a("span",{class:"add-knowledge-card-title"},"创建知识库",-1))])]),_:1},8,["onClick"])]),_:1}),e.knowledgeList&&e.knowledgeList.length>0?(M(!0),N(Q,{key:0},T(e.knowledgeList,s=>(M(),A(i,{xxl:4,xl:6,lg:6,md:6,sm:12,xs:24},{default:n(()=>[o(h,{class:"knowledge-card pointer",onClick:f=>e.handleDocClick(s.id)},{default:n(()=>[a("div",se,[a("div",re,[t[5]||(t[5]=a("img",{class:"header-img",src:oe},null,-1)),a("div",de,[a("span",{class:"header-text-top header-name ellipsis",title:s.name},B(s.name),9,ce),a("span",me," 创建者："+B(s.createBy),1)])])]),a("div",pe,[a("span",null,B(s.descr||"暂无描述"),1)]),a("div",ge,[o(m,{class:"knowledge-footer-icon",icon:"ant-design:deployment-unit-outlined",size:"14"}),a("span",null,B(s.embedId_dictText),1)]),a("div",ue,[o(p,{placement:"bottomRight",trigger:["click"],getPopupContainer:f=>f.parentNode},{overlay:n(()=>[o(D,null,{default:n(()=>[o(y,{key:"vectorization",onClick:K(f=>e.handleVectorization(s.id),["prevent","stop"])},{default:n(()=>[o(m,{icon:"ant-design:retweet-outlined",size:"16"}),t[6]||(t[6]=x(" 向量化 "))]),_:2,__:[6]},1032,["onClick"]),o(y,{key:"text",onClick:K(f=>e.handleEditClick(s),["prevent","stop"])},{default:n(()=>[o(m,{class:"pointer",icon:"ant-design:edit-outlined",size:"16"}),t[7]||(t[7]=x(" 编辑 "))]),_:2,__:[7]},1032,["onClick"]),o(y,{key:"file",onClick:K(f=>e.handleDelete(s),["prevent","stop"])},{default:n(()=>[o(m,{class:"pointer",icon:"ant-design:delete-outlined",size:"16"}),t[8]||(t[8]=x(" 删除 "))]),_:2,__:[8]},1032,["onClick"])]),_:2},1024)]),default:n(()=>[a("div",{class:"ant-dropdown-link pointer model-icon",onClick:t[1]||(t[1]=K(()=>{},["prevent","stop"]))},[o(m,{icon:"ant-design:ellipsis-outlined",size:"16"})])]),_:2},1032,["getPopupContainer"])])]),_:2},1032,["onClick"])]),_:2},1024))),256)):J("",!0)]),_:1}),e.knowledgeList.length>0?(M(),A(I,{key:0,current:e.pageNo,"page-size":e.pageSize,"page-size-options":e.pageSizeOptions,total:e.total,showQuickJumper:!0,showSizeChanger:!0,onChange:e.handlePageChange,class:"list-footer",size:"small"},null,8,["current","page-size","page-size-options","total","onChange"])):J("",!0),o(P,{onRegister:e.registerModal,onSuccess:e.reload},null,8,["onRegister","onSuccess"]),o(R,{onRegister:e.docListRegister},null,8,["onRegister"])])}const Do=ee(te,[["render",_e],["__scopeId","data-v-c086fc6c"]]);export{Do as default};
