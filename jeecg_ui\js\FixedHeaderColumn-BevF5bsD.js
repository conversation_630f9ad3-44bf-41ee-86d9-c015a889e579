import{d as s,ag as p,aq as d,ar as c,k as n,aD as f}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{d as u}from"./table-BDFKJhHv.js";import{useListPage as b}from"./useListPage-Soxgnx9a.js";import{Q as h}from"./componentMap-Bkie1n3v.js";import _ from"./BasicTable-xCEZpGLb.js";import{a as x}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const T=[{title:"ID",dataIndex:"id",fixed:"left",width:280},{title:"姓名",dataIndex:"name",width:260},{title:"地址",dataIndex:"address"},{title:"编号",dataIndex:"no",width:300},{title:"开始时间",width:200,dataIndex:"beginTime"},{title:"结束时间",dataIndex:"endTime",width:200}],w=s({components:{BasicTable:_,TableAction:h},setup(){const{tableContext:t}=b({tableProps:{title:"固定头和列示例",api:u,columns:T,canResize:!1,scroll:{y:200},actionColumn:{width:160,title:"Action",dataIndex:"action"},useSearchForm:!1}}),[o]=t;function i(r){}function e(r){}return{registerTable:o,handleDelete:i,handleOpen:e}}}),C={class:"p-4"};function I(t,o,i,e,r,g){const a=p("TableAction"),l=p("BasicTable");return c(),d("div",C,[n(l,{onRegister:t.registerTable},{action:f(({record:m})=>[n(a,{actions:[{label:"删除",icon:"ic:outline-delete-outline",onClick:t.handleDelete.bind(null,m)}],dropDownActions:[{label:"启用",popConfirm:{title:"是否启用？",confirm:t.handleOpen.bind(null,m)}}]},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister"])])}const Rt=x(w,[["render",I]]);export{Rt as default};
