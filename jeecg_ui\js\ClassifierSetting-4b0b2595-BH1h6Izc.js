import{d as U,e as V,ag as E,aq as k,ar as B,at as t,k as m,u as g,H as D,F,aC as R,au as z,aD as P}from"./vue-vendor-dy9k-Yad.js";import{h as G}from"./antd-vue-vendor-me9YkNVC.js";import{u as H}from"./index-CCWaWN5g.js";import{b as J}from"./useSettings-4a774f12-DjycrGR6.js";import{_ as Y}from"./VarPicker.vue_vue_type_script_setup_true_lang-5fb9829d-C8jb2Rkc.js";import"./VarListPicker.vue_vue_type_style_index_0_scoped_9a10b0de_lang-4ed993c7-l0sNRNKZ.js";import"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./VarListEditor.vue_vue_type_style_index_0_scoped_407b7ab3_lang-4ed993c7-l0sNRNKZ.js";import{u as Z}from"./VarListShow.vue_vue_type_script_setup_true_lang-9bf001aa-DcNRZKsU.js";import"./VarTextarea.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import"./VarEditable.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import K from"./LLMModelSelect-d7162bae-CKJ000_J.js";import{s as Q}from"./_plugin-vue_export-helper-dad06003-lGy7RumW.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";function W(p){var i,d;const a=[],l=(d=(i=p.properties)==null?void 0:i.options)!=null?d:{};return Array.isArray(l.categories)&&a.push(...l.categories.map((I,S)=>({type:"CASE",label:`分类 ${S+1}`,value:I}))),l.else&&a.push({type:"ELSE",label:"ELSE",value:l.else}),a}function f(p,i,d){const a=`case_${i==="ELSE"?"else":d}`;return`${p}_${a}`}function X(p,i){return f(p,i===-1?"ELSE":"CASE",i+1)}const bt=Object.freeze(Object.defineProperty({__proto__:null,getAnchorId:f,getAnchorIdByChooseIndex:X,getCaseList:W},Symbol.toStringTag,{value:"Module"})),ee={class:"classifier-setting"},te={class:"setting-item"},oe={class:"setting-item"},re={class:"setting-item"},ie={class:"case-item"},ae={class:"case-header"},le={class:"case-label"},se={class:"setting-item"},ne=U({__name:"ClassifierSetting",props:{type:{type:String,required:!0},node:{type:Object,required:!0},properties:{type:Object,required:!0},setProperties:{type:Function,required:!0}},setup(p){const{createMessage:i}=H(),d=p,{lfRef:a,inputParams:l,outputParams:I,prevVariables:S,createOptionRef:j}=J(d),_=j("model"),c=j("categories"),A=V({get(){return l.value[0]?l.value[0]:{field:"",nodeId:""}},set(r){l.value=[r]}});function q(r){r!=null&&r.nodeId?A.value={field:r.field,nodeId:r.nodeId}:A.value={field:"",nodeId:""}}function w(){c.value=[...c.value,{category:"",next:""}]}function x(r){var e,y;const{node:u}=d;if(c.value.length===1){i.warning("请至少保留一个分类");return}if(!a.value)return;const s=a.value.graphModel,{$caseList:v}=u,n=v[r],{type:h}=n,b=f(u.id,h,r+1),C=s.getAnchorOutgoingEdge(b);(e=C==null?void 0:C[0])!=null&&e.id&&s.deleteEdgeById(C[0].id);const L=[];for(let o=r+1;o<v.length;o++){const O=v[o],{type:M}=O;if(M==="ELSE")continue;const T=f(u.id,M,o+1),$=s.getAnchorOutgoingEdge(T);(y=$==null?void 0:$[0])!=null&&y.id&&L.push({edge:$[0],newSourceAnchorId:f(u.id,"CASE",o)})}c.value.splice(r,1),N(),L.length&&setTimeout(()=>{for(const o of L){const O={id:o.edge.id,type:o.edge.type,sourceNodeId:o.edge.sourceNodeId,targetNodeId:o.edge.targetNodeId,sourceAnchorId:o.newSourceAnchorId,targetAnchorId:o.edge.targetAnchorId};s.deleteEdgeById(o.edge.id),s.addEdge(O)}s.$J.repaintGraph()},10)}function N(){c.value=G(c.value)}return(r,e)=>{const y=E("Icon"),u=E("a-space"),s=E("a-textarea"),v=E("a-button");return B(),k("div",ee,[t("div",te,[e[3]||(e[3]=t("div",{class:"label"},"输入变量",-1)),m(g(Y),{vars:g(S),item:A.value,onChange:e[0]||(e[0]=n=>q(n))},null,8,["vars","item"])]),t("div",oe,[e[4]||(e[4]=t("div",{class:"label"},"模型",-1)),m(K,{model:g(_),"onUpdate:model":e[1]||(e[1]=n=>D(_)?_.value=n:null)},null,8,["model"])]),t("div",re,[e[6]||(e[6]=t("div",{class:"label"},"分类",-1)),(B(!0),k(F,null,R(g(c),(n,h)=>(B(),k("div",ie,[t("div",ae,[t("div",le,[t("span",null,"分类 "+z(h+1),1)]),m(u,{class:"case-action"},{default:P(()=>[m(y,{class:"delete",icon:"ant-design:delete",onClick:b=>x(h)},null,8,["onClick"])]),_:2},1024)]),m(s,{class:"case-input",value:n.category,"onUpdate:value":b=>n.category=b,placeholder:"请输入你的分类主题内容",onBlur:e[2]||(e[2]=()=>N())},null,8,["value","onUpdate:value"])]))),256)),m(v,{block:"",preIcon:"ant-design:plus",onClick:w},{default:P(()=>e[5]||(e[5]=[t("span",null,"添加分类",-1)])),_:1})]),e[8]||(e[8]=t("div",{class:"setting-item"},[t("div",{class:"label case-label"},[t("div",{class:"c-type"},"ELSE")]),t("div",{style:{color:"#aaaaaa"}},[t("span",null,"当以上分类都不满足时，执行此分支")])],-1)),t("div",se,[e[7]||(e[7]=t("div",{class:"label"},"输出变量",-1)),m(g(Z),{vars:g(I)},null,8,["vars"])])])}}}),pe=Q(ne,[["__scopeId","data-v-89999304"]]),Et=Object.freeze(Object.defineProperty({__proto__:null,default:pe},Symbol.toStringTag,{value:"Module"}));export{bt as C,pe as a,W as b,Et as c,f as g};
