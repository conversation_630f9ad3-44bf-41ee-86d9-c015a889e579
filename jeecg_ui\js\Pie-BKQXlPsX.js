import{d as h,f as p,r as d,h as u,w as m,aq as y,ar as w,aA as b}from"./vue-vendor-dy9k-Yad.js";import{useECharts as g}from"./useECharts-BU6FzBZi.js";import{h as k}from"./antd-vue-vendor-me9YkNVC.js";import{a as _}from"./index-CCWaWN5g.js";const D=h({name:"Pie",props:{chartData:{type:Array,default:()=>[]},size:{type:Object,default:()=>{}},option:{type:Object,default:()=>({})},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"}},emits:["click"],setup(t,{emit:c}){const a=p(null),{setOptions:n,getInstance:i,resize:r}=g(a),o=d({tooltip:{formatter:"{b} ({c})"},series:[{type:"pie",radius:"72%",center:["50%","55%"],data:[],labelLine:{show:!0},label:{show:!0,formatter:`{b} 
 ({d}%)`,color:"#B1B9D3"}}]});u(()=>{t.chartData&&l()}),m(()=>t.size,()=>{r()},{immediate:!0});function l(){var e,f;t.option&&Object.assign(o,k(t.option)),o.series[0].data=t.chartData,n(o),r(),(e=i())==null||e.off("click",s),(f=i())==null||f.on("click",s)}function s(e){c("click",e)}return{chartRef:a}}});function z(t,c,a,n,i,r){return w(),y("div",{ref:"chartRef",style:b({height:t.height,width:t.width})},null,4)}const j=_(D,[["render",z]]);export{j as P};
