import{d as C,f as e,ag as g,aq as b,ar as k,k as r,aD as a,G as m,au as d}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{getBasicData as v,getBasicColumns as B}from"./tableData-B4J3mkj4.js";import y from"./BasicTable-xCEZpGLb.js";import{a as h}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const z=C({components:{BasicTable:y},setup(){const o=e(!1),t=e(!1),p=e(!0),n=e(!0),s=e(!1);function u(){o.value=!o.value}function i(){p.value=!p.value}function l(){t.value=!0,setTimeout(()=>{t.value=!1,s.value={pageSize:20}},3e3)}function c(){n.value=!n.value}function f(T){}return{columns:B(),data:v(),canResize:o,loading:t,striped:p,border:n,toggleStriped:i,toggleCanResize:u,toggleLoading:l,toggleBorder:c,pagination:s,handleColumnChange:f}}}),R={class:"p-4"};function S(o,t,p,n,s,u){const i=g("a-button"),l=g("BasicTable");return k(),b("div",R,[r(l,{title:"基础示例",titleHelpMessage:"温馨提醒",columns:o.columns,dataSource:o.data,canResize:o.canResize,loading:o.loading,striped:o.striped,bordered:o.border,showTableSetting:"",pagination:o.pagination,onColumnsChange:o.handleColumnChange},{toolbar:a(()=>[r(i,{type:"primary",onClick:o.toggleCanResize},{default:a(()=>[m(d(o.canResize?"取消自适应":"自适应高度"),1)]),_:1},8,["onClick"]),r(i,{type:"primary",onClick:o.toggleBorder},{default:a(()=>[m(d(o.border?"隐藏边框":"显示边框"),1)]),_:1},8,["onClick"]),r(i,{type:"primary",onClick:o.toggleLoading},{default:a(()=>t[0]||(t[0]=[m(" 开启loading ")])),_:1,__:[0]},8,["onClick"]),r(i,{type:"primary",onClick:o.toggleStriped},{default:a(()=>[m(d(o.striped?"隐藏斑马纹":"显示斑马纹"),1)]),_:1},8,["onClick"])]),_:1},8,["columns","dataSource","canResize","loading","striped","bordered","pagination","onColumnsChange"])])}const No=h(z,[["render",S]]);export{No as default};
