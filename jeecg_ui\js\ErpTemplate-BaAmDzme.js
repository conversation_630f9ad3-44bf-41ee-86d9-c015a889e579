import{l,ag as r,aB as x,ar as m,aD as u,k as p,q as f,as as T,at as i,B as y}from"./vue-vendor-dy9k-Yad.js";import{a as S,j as b,bx as n}from"./index-CCWaWN5g.js";import{B as _,bx as C,aW as k,z as R}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const D={name:"ErpTemplate",data(){return{toolbarConfig:{slot:["prefix","suffix"],btn:["add","remove","clearSelection"]},expandConfig:{accordion:!0},subTabs:{show:!1,expand:!0,autoExpand:!0},table1:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0,showTotal:(e,a)=>{let o=l("span",`${a[0]}-${a[1]} 共 ${e} 条`);return this.subTabs.show?[l("span",{},[l(_,{type:"link",onClick:this.handleToggleTabs},()=>[this.subTabs.expand?l(C):l(k),l("span",{},this.subTabs.expand?"收起":"展开")]),l(R,{checked:this.subTabs.autoExpand,"onUpdate:checked":t=>this.subTabs.autoExpand=t},()=>"自动展开")]),o]:o}},selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:n.input},{key:"call",title:"呼叫",width:"990px",type:n.input},{key:"len",title:"长",width:"80px",type:n.inputNumber},{key:"ton",title:"吨",width:"120px",type:n.inputNumber},{key:"payer",title:"付款方",width:"120px",type:n.input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:n.input},{key:"trend",title:"动向",width:"120px",type:n.input}]},table2:{currentRowId:null,loading:!1,pagination:{current:1,pageSize:10,pageSizeOptions:["5","10","20","30"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:n.input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:n.input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:n.input},{key:"type",title:"船舶分类",width:"120px",type:n.input},{key:"port_area",title:"所属港区",width:"120px",type:n.input}]},currentSubRow:null,url:{getData:"/mock/vxe/getData"}}},computed:{tableHeight(){let{show:e,expand:a}=this.subTabs;return e&&a?350:482}},created(){this.loadTable1Data()},methods:{loadTable1Data(){let e={pageNo:this.table1.pagination.current,pageSize:this.table1.pagination.pageSize};this.table1.loading=!0,b.get({url:this.url.getData,params:e}).then(a=>{this.table1.pagination.total=a.total,this.table1.dataSource=a.records,this.table1.selectedRows=[]}).finally(()=>{this.table1.loading=!1})},loadSubData(e){return e?(this.table2.currentRowId===e.id||(this.table2.currentRowId=e.id,this.loadTable2Data()),!0):!1},loadTable2Data(){let e=this.table2,a={parentId:e.currentRowId,pageNo:this.table2.pagination.current,pageSize:this.table2.pagination.pageSize};e.loading=!0,b.get({url:this.url.getData,params:a}).then(o=>{e.selectedRows=[],e.dataSource=o.records,e.pagination.total=o.total}).finally(()=>{e.loading=!1})},handleTable1SelectRowChange(e){this.table1.selectedRows=e.selectedRows,this.subTabs.show=!0,this.subTabs.autoExpand&&(this.subTabs.expand=!0),this.loadSubData(e.selectedRows[0])},handleTable2SelectRowChange(e){this.table2.selectedRows=e.selectedRows},handleTable1PageChange(e){this.table1.pagination.current=e.current,this.table1.pagination.pageSize=e.pageSize,this.loadTable1Data()},handleTable2PageChange(e){this.table2.pagination.current=e.current,this.table2.pagination.pageSize=e.pageSize,this.loadTable2Data()},handleToggleTabs(){this.subTabs.expand=!this.subTabs.expand}}};function z(e,a,o,c,t,s){const d=r("JVxeTable"),h=r("a-tab-pane"),g=r("a-tabs"),w=r("a-card");return m(),x(w,{bordered:!1},{default:u(()=>[p(d,{toolbar:"",toolbarConfig:t.toolbarConfig,rowNumber:"",rowSelection:"",rowSelectionType:"radio",clickSelectRow:"",highlightCurrentRow:"",height:s.tableHeight,loading:t.table1.loading,columns:t.table1.columns,dataSource:t.table1.dataSource,pagination:t.table1.pagination,expandConfig:t.expandConfig,style:{"margin-bottom":"8px"},onPageChange:s.handleTable1PageChange,onSelectRowChange:s.handleTable1SelectRowChange},null,8,["toolbarConfig","height","loading","columns","dataSource","pagination","expandConfig","onPageChange","onSelectRowChange"]),f(p(g,{class:T({"sub-tabs":!0,"un-expand":!t.subTabs.expand})},{default:u(()=>[p(h,{tab:"子表1",key:"1"},{default:u(()=>[p(d,{toolbar:"","row-number":"","row-selection":"",height:"auto",maxHeight:350,loading:t.table2.loading,columns:t.table2.columns,dataSource:t.table2.dataSource,pagination:t.table2.pagination,onPageChange:s.handleTable2PageChange,onSelectRowChange:s.handleTable2SelectRowChange},null,8,["loading","columns","dataSource","pagination","onPageChange","onSelectRowChange"])]),_:1}),p(h,{tab:"子表2",key:"2"},{default:u(()=>a[0]||(a[0]=[i("h1",null,"这里是子表2",-1),i("h1",null,"这里是子表2",-1),i("h1",null,"这里是子表2",-1),i("h1",null,"这里是子表2",-1),i("h1",null,"这里是子表2",-1),i("h1",null,"这里是子表2",-1)])),_:1,__:[0]})]),_:1},8,["class"]),[[y,t.subTabs.show]])]),_:1})}const I=S(D,[["render",z],["__scopeId","data-v-caadbbcf"]]);export{I as default};
