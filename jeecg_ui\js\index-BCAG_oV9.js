import{aq as t,ar as o,at as e,k as r}from"./vue-vendor-dy9k-Yad.js";import a from"./AiChat-DA_7PGsT.js";import{a as s}from"./index-CCWaWN5g.js";import"./slide-Ds8o58gV.js";import"./ailogo-DG2_TD5d.js";import"./chat-I63fsyMB.js";import"./chatMessage-DcTiQunt.js";import"./chatText-DGPEwQvb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./presetQuestion-CYEhQsHK.js";import"./vxe-table-vendor-B22HppNm.js";const c={class:"wrap"},i={class:"content"},p={__name:"index",setup(m){return(_,n)=>(o(),t("div",c,[e("div",i,[r(a)])]))}},V=s(p,[["__scopeId","data-v-4cb19ba1"]]);export{V as default};
