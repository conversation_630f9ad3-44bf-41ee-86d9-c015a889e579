import{d as k,f as t,aB as b,ar as h,aD as _,k as B,u as x,n as p}from"./vue-vendor-dy9k-Yad.js";import C from"./OneNativeForm-Bd1nD0Af.js";import{B as O}from"./index-Diw57m_E.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./JAddInput-CxJ-JBK-.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./useFormItem-CHvpjy4o.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./JPopup-CeU6ry6r.js";import"./JMarkdownEditor-CxtN1OHq.js";import"./index-mbACBRQ9.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./CustomModal-BakuIxQv.js";const ct=k({__name:"OneNativeModal",emits:["register","ok"],setup(w,{expose:l,emit:n}){const r=t(""),s=t(800),o=t(!1),i=t(!1),e=t(),u=n;function c(){r.value="新增",o.value=!0,p(()=>{e.value.add()})}function d(m){r.value=i.value?"详情":"编辑",o.value=!0,p(()=>{e.value.edit(m)})}function f(){e.value.submitForm()}function v(){a(),u("ok")}function a(){o.value=!1}return l({add:c,edit:d,disableSubmit:i}),(m,F)=>(h(),b(x(O),{title:r.value,width:s.value,visible:o.value,height:600,onOk:f,okButtonProps:{class:{"jee-hidden":i.value}},onCancel:a,cancelText:"关闭"},{default:_(()=>[B(C,{ref_key:"realForm",ref:e,onOk:v,disabled:i.value},null,8,["disabled"])]),_:1},8,["title","width","visible","okButtonProps"]))}});export{ct as default};
