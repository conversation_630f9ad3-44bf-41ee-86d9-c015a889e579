var k=Object.defineProperty,b=Object.defineProperties;var I=Object.getOwnPropertyDescriptors;var s=Object.getOwnPropertySymbols;var y=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var a=(t,e,o)=>e in t?k(t,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[e]=o,p=(t,e)=>{for(var o in e||(e={}))y.call(e,o)&&a(t,o,e[o]);if(s)for(var o of s(e))C.call(e,o)&&a(t,o,e[o]);return t},l=(t,e)=>b(t,I(e));import{d as u,aB as R,ar as S,aD as L,k as T,u as i}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{render as c}from"./renderUtils-D7XVOFwj.js";import{aZ as v,j as d,u as B}from"./index-CCWaWN5g.js";import{useListPage as K}from"./useListPage-Soxgnx9a.js";import{Q as M}from"./componentMap-Bkie1n3v.js";import P from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-QxsVJqiT.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const D=[{title:"用户账号",align:"center",dataIndex:"username",customRender:({text:t,record:e})=>{let o=v();return e.token===o?t+"（我）":t}},{title:"用户姓名",align:"center",dataIndex:"realname"},{title:"头像",align:"center",width:120,dataIndex:"avatar",customRender:c.renderAvatar},{title:"生日",align:"center",dataIndex:"birthday"},{title:"性别",align:"center",dataIndex:"sex",customRender:({text:t})=>c.renderDict(t,"sex")},{title:"手机号",align:"center",dataIndex:"phone"}],F=[{field:"username",label:"用户账号",component:"Input",colProps:{span:6}}];const X=t=>d.get({url:"/sys/online/list",params:t}),j=t=>d.post({url:"/sys/online/forceLogout",params:t},{isTransformResponse:!1}),A=u({name:"online-user"}),Gt=u(l(p({},A),{setup(t){const{prefixCls:e,tableContext:o,onImportXls:E,onExportXls:H}=K({designScope:"online-user",tableProps:{rowKey:"token",title:"在线用户",api:X,columns:D,formConfig:{schemas:F},actionColumn:{width:120},rowSelection:null}}),[f,{reload:g},{rowSelection:x,selectedRowKeys:N}]=o,m=B();function h(r){return[{label:"强退",popConfirm:{title:"强制退出用户？",confirm:_.bind(null,r)}}]}function _(r){j({token:r.token}).then(n=>{n.success?(g(),m.createMessage.success("强制退出用户”"+r.realname+"“成功！")):m.createMessage.warn(n.message)})}return(r,n)=>(S(),R(i(P),{onRegister:i(f),rowSelection:i(x)},{action:L(({record:w})=>[T(i(M),{actions:h(w)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"]))}}));export{Gt as default};
