var et=Object.defineProperty;var ee=Object.getOwnPropertySymbols;var tt=Object.prototype.hasOwnProperty,ot=Object.prototype.propertyIsEnumerable;var te=(h,o,u)=>o in h?et(h,o,{enumerable:!0,configurable:!0,writable:!0,value:u}):h[o]=u,oe=(h,o)=>{for(var u in o||(o={}))tt.call(o,u)&&te(h,u,o[u]);if(ee)for(var u of ee(o))ot.call(o,u)&&te(h,u,o[u]);return h};var X=(h,o,u)=>new Promise((e,N)=>{var P=v=>{try{a(u.next(v))}catch(g){N(g)}},c=v=>{try{a(u.throw(v))}catch(g){N(g)}},a=v=>v.done?e(v.value):Promise.resolve(v.value).then(P,c);a((u=u.apply(h,o)).next())});import{f as p,r as lt,e as nt,ag as r,aq as _,ar as m,k as l,aD as i,at as n,aB as O,ah as Q,aE as S,au as J,F as le,aC as ne,G as R}from"./vue-vendor-dy9k-Yad.js";import{I as it}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{a5 as at,h as ie}from"./antd-vue-vendor-me9YkNVC.js";import{bH as st,u as dt,ac as rt,ad as D,aF as pt,d as ae,a as mt}from"./index-CCWaWN5g.js";import{q as ct,s as ut,b as gt,c as ft}from"./AiApp.api-CU5CuCbf.js";import{_ as Ct,b as vt}from"./JAddInput-CxJ-JBK-.js";import wt from"./AiAppAddKnowledgeModal-CpdQzdSS.js";import kt from"./AiAppParamsSettingModal-DyqbZfN8.js";import _t from"./AiAppGeneratedPromptModal-B4YmsZ0M.js";import yt from"./AiAppQuickCommandModal-C4QHfvRT.js";import{J as ht,d as At}from"./AiAppAddFlowModal-BuhG0aib.js";import bt from"./AiAppModal-Bh7yirz7.js";import Lt from"./chat-I63fsyMB.js";import{_ as It}from"./knowledge-BXTupIwn.js";import{d as St}from"./ailogo-DG2_TD5d.js";import{J as xt}from"./JSearchSelect-c_lfTydU.js";import Mt from"./JMarkdownEditor-CxtN1OHq.js";import{d as Ot}from"./vuedraggable.umd-DpmahwAM.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./useFormItem-CHvpjy4o.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./AiKnowledgeBase.api-Dgaf5KfS.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-mbACBRQ9.js";import"./AiModelSeniorForm-DwUUzhZR.js";import"./index-L3cSIXth.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./AiApp.data-wU-aGD0q.js";import"./chatMessage-DcTiQunt.js";import"./chatText-DGPEwQvb.js";import"./presetQuestion-CYEhQsHK.js";const Ft=`# 角色
你是一个犀利的电影解说员，可以使用尖锐幽默的语言，向用户讲解电影剧情、介绍最新上映的电影，还可以用普通人都可以理解的语言讲解电影相关知识。

## 技能
### 技能 1: 推荐最新上映的电影
1. 当用户请你推荐最新电影时，需要先了解用户喜欢哪种类型片。如果你已经知道了，请跳过这一步，在询问时可以用“请问您喜欢什么类型的电影呢亲”。
2. 如果你并不知道用户所说的电影，可以使用 工具搜索电影，了解电影类型。
3. 根据用户的电影偏好，推荐几部正在上映和即将上映的电影，在推荐开头可以说“好的亲，以下是为您推荐的电影”。
===回复示例===
   -  🎬 电影名: <电影名>
   -  🕐 上映时间: <电影在中国大陆的上映的日期>
   -  💡 电影简介: <100字总结这部电影的剧情摘要>
===示例结束===

### 技能 2: 介绍电影
1. 当用户说介绍某一部电影，请使用工具 搜索电影介绍的链接，在收到需求时可以回应“好嘞亲，马上为您查找相关电影介绍”。
2. 如果此时获取的信息不够全面，可以继续使用 工具 打开搜索结果中的相关链接，以了解电影详情。
3. 根据搜索和浏览结果，生成电影介绍
### 技能 3: 介绍电影概念
- 你可以使用数据集中的知识，调用 知识库 搜索相关知识，并向用户介绍基础概念，介绍前可以说“亲，下面为您介绍一下这个电影概念”。
- 使用用户熟悉的电影，举一个实际的场景解释概念

## 限制:
- 只讨论与电影有关的内容，拒绝回答与电影无关的话题，拒绝时可以说“不好意思亲，这边只讨论电影相关话题哦”。
- 所输出的内容必须按照给定的格式进行组织，不能偏离框架要求，在表述中合理运用常用语。
- 总结部分不能超过 100 字。
- 只会输出知识库中已有内容, 不在知识库中的书籍, 通过 工具去了解。
- 请使用 Markdown 的 ^^ 形式说明引用来源。”`,qt="嘿，亲！我对电影那可是门儿清，能给你带来超棒的电影体验。",Qt=[{key:1,descr:"有啥好看的动作片推荐不？"},{key:2,descr:"介绍下《流浪地球 3》呗。"},{key:3,descr:"啥是电影蒙太奇呀？"}],z={prompt:Ft,prologue:qt,presetQuestion:Qt},Dt={name:"AiAppSettingModal",components:{draggable:Ot,JMarkdownEditor:Mt,JSearchSelect:xt,JImageUpload:vt,JDictSelectTag:Ct,BasicModal:it,AiAppAddKnowledgeModal:wt,AiAppParamsSettingModal:kt,AiAppAddFlowModal:ht,AiAppModal:bt,chat:Lt,AiAppGeneratedPromptModal:_t,AiAppQuickCommandModal:yt},emits:["success","register"],setup(h,{emit:o}){const u=p("设置"),e=p(!1),N=p(st(16)),P=p([]),c=p("chatSimple"),a=lt({name:"",descr:"",msgNum:1,prompt:"",prologue:null,knowledgeIds:"",id:"",type:"",modelId:"",icon:"",presetQuestion:""}),v=p({name:[{required:!0,message:"请输入应用名称!"}],modelId:[{required:!0,message:"请选择AI模型!"}],flowId:[{required:!0,message:"请选择AI流程!"}]}),g=p(),F=at.useForm,{resetFields:j,validate:U,validateInfos:b,validateField:Y}=F(a,v,{immediate:!1}),K=p({span:24}),V=p({span:24}),y=p(""),I=p([]),x=p(""),E=p(""),q=p({}),k=p(""),f=p([{key:1,sort:1,descr:""}]),C=p([]),L=p(""),{createMessage:T}=dt(),[G,{closeModal:H,setModalProps:s}]=rt(t=>X(null,null,function*(){E.value=t.id,e.value=!!(t!=null&&t.isUpdate),Ke(),e.value?setTimeout(()=>{Ve(t)},300):ct({id:t.id}).then(d=>{d.success&&(Object.assign(a,d.result),a.prompt=z.prompt,a.prologue=z.prologue,a.presetQuestion=JSON.stringify(z.presetQuestion),x.value=z.prologue,k.value=a.presetQuestion,f.value=z.presetQuestion,$(d.result.type))}),s({bodyStyle:{padding:"10px"}})})),[A,{openModal:se}]=D(),[de,{openModal:re}]=D(),[pe,{openModal:me}]=D(),[ce,{openModal:ue}]=D(),[ge,{openModal:fe}]=D(),[Ce,{openModal:Z}]=D();function ve(){return X(this,null,function*(){try{let t=yield U();s({confirmLoading:!0}),a.knowledgeIds=y.value,yield ut(a)}finally{s({confirmLoading:!1})}})}we();function we(){pt("ai_app_type").then(t=>{if(t&&t.length>0)for(const d of t)d.value==="chatSimple"?d.desc="适合新手创建小助手":d.value==="chatFLow"&&(d.desc="适合高级用户自定义小助手的工作流");P.value=t})}function ke(){H()}function _e(t){c.value=t}function ye(){se(!0,{knowledgeIds:y.value,knowledgeDataList:I.value})}function he(t,d){y.value=ie(t.join(",")),I.value=ie(d),a.knowledgeIds=y.value}function Ae(t){let d=y.value.split(","),w=d.findIndex(W=>W===t);w!=-1&&(d.splice(w,1),y.value=d?d.join(","):"",I.value.splice(w,1),a.knowledgeIds=y.value)}function be(t){gt({ids:t}).then(d=>{d.success&&(I.value=d.result,y.value=t)})}function Le(t){x.value=t}function Ie(t){t||o("success")}function $(t){t==="chatSimple"?v.value={name:[{required:!0,message:"请输入应用名称!"}],modelId:[{required:!0,message:"请选择AI模型!"}]}:t==="chatFLow"&&(v.value={name:[{required:!0,message:"请输入应用名称!"}],flowId:[{required:!0,message:"请选择AI流程!"}]})}function Se(t){me(!0,{type:t,metadata:q.value})}function xe(t){q.value=t,t&&(a.metadata=JSON.stringify(t))}const B=p(""),M=p(null);function Me(t){ft({id:t}).then(d=>{if(d.success&&(M.value=d.result,B.value=d.result.id,d.result.metadata)){let w=JSON.parse(d.result.metadata);w.inputs&&(M.value.metadata=w.inputs)}})}function Oe(){re(!0,{flowId:B.value,flowData:M.value})}function Fe(t){B.value=t.flowId,a.flowId=t.flowId,M.value=t.flowData}function qe(){B.value="",a.flowId="",M.value=null}function Qe(){return a.icon?ae(a.icon):St}function De(t){return t?ae(t):At}function Ne(){ue(!0,{isUpdate:!0,record:a})}function Pe(t){a.icon=t.icon?t.icon:"",a.name=t.name?t.name:""}const Be=nt(()=>{let t=f.value;return!!(t&&t.length>0&&t.filter(w=>w.update==!0).length>0)});function Je(){k.value=JSON.stringify(f.value),a.presetQuestion=k.value}function Re(t){if(f.value.find(W=>W.descr==""))return;const w=f.value.length;f.value.push({key:w+1,sort:w+1,descr:""})}function ze(t){f.value=f.value.filter(d=>d.key!==t),k.value=JSON.stringify(f.value),a.presetQuestion=k.value}function Ue(t){t.update=!0}function Ee(t){t.update=!1}function je(){f.value&&f.value.length>0?(k.value=JSON.stringify(f.value),a.presetQuestion=k.value):(k.value="",a.presetQuestion="")}function Ke(){y.value="",I.value=[],x.value="",B.value="",M.value=null,k.value="",f.value=[],C.value=[],L.value=""}function Ve(t){j(),$(t.type),t.prologue&&(x.value=t.prologue?t.prologue:""),t.msgNum=t.msgNum?t.msgNum:1,t.metadata&&(q.value=JSON.parse(t.metadata)),t.presetQuestion&&(k.value=t.presetQuestion,f.value=JSON.parse(t.presetQuestion)),t.quickCommand&&(C.value=JSON.parse(t.quickCommand)),Object.assign(a,t),t.type==="chatSimple"&&t.knowledgeIds&&be(t.knowledgeIds),t.type==="chatFLow"&&t.flowId&&Me(t.flowId)}function Te(){fe(!0,{})}function Ge(t){a.prompt=t}function He(){L.value=JSON.stringify(C.value),a.quickCommand=L.value}function We(){if(C.value&&C.value.length>4){T.warning("最多只能添加5个！");return}Z(!0,{})}function Xe(t){Z(!0,{isUpdate:!0,record:t})}function Ye(t){C.value.push(oe({key:C.value.length+1},t)),L.value=JSON.stringify(C.value),a.quickCommand=L.value}function Ze(t){let d=C.value.findIndex(w=>w.key===t.key);d>-1&&(C.value[d]=t)}function $e(t){let d=C.value.findIndex(w=>w.key===t);d>-1&&(C.value.splice(d,1),L.value=JSON.stringify(C.value),a.quickCommand=L.value)}return{registerModal:G,title:u,handleOk:ve,handleCancel:ke,appTypeOption:P,type:c,handleTypeClick:_e,formState:a,validatorRules:v,labelCol:K,wrapperCol:V,validateInfos:b,handleAddKnowledgeIdClick:ye,registerKnowledgeModal:A,knowledgeDataList:I,knowledge:It,handleSuccess:he,handleDeleteKnowledge:Ae,uuid:N,prologueTextAreaBlur:Le,prologue:x,appId:E,visibleChange:Ie,handleParamSettingClick:Se,registerParamsSettingModal:pe,handleParamsSettingOk:xe,registerFlowModal:de,handleAddFlowSuccess:Fe,handleAddFlowClick:Oe,flowData:M,handleDeleteFlow:qe,getImage:Qe,handleEdit:Ne,registerAiAppModal:ce,handelEditSuccess:Pe,presetQuestionEnd:Je,presetQuestionList:f,presetQuestionAddClick:Re,deleteQuestionClick:ze,onBlur:Ee,onFocus:Ue,disabledDrag:Be,questionChange:je,presetQuestion:k,generatedPrompt:Te,registerAiAppPromptModal:ge,handleAiAppPromptOk:Ge,quickCommandList:C,quickCommandEnd:He,registerAiAppCommandModal:Ce,quickCommandAddClick:We,handleAiAppCommandOk:Ye,editCommandClick:Xe,handleAiAppCommandUpdateOk:Ze,deleteCommandClick:$e,quickCommand:L,getFlowImage:De,metadata:q}}},Nt={class:"p-2"},Pt={style:{display:"flex",width:"100%","justify-content":"space-between","align-items":"center"}},Bt={style:{display:"flex"}},Jt=["src"],Rt={class:"header-name"},zt={style:{display:"flex"}},Ut={style:{height:"100%",width:"100%"}},Et={class:"prologue-chunk"},jt={style:{display:"flex","justify-content":"space-between",width:"100%"}},Kt={style:{display:"flex",width:"100%","justify-content":"space-between"}},Vt={style:{width:"100%",display:"flex"}},Tt=["src"],Gt={style:{display:"grid","margin-left":"5px","align-items":"center",width:"calc(100% - 20px)"}},Ht={class:"flow-name ellipsis align-items: center;"},Wt={key:0,class:"flex text-status"},Xt={key:0,class:"tag-text"},Yt={key:1,class:"data-empty-text"},Zt={class:"prompt-back"},$t={class:"prompt-title-padding item-title space-between"},eo={class:"prologue-chunk-edit"},to={class:"prologue-chunk-edit"},oo={class:"prologue-chunk-edit"},lo={class:"prompt-title-padding item-title space-between"},no={key:0,style:{padding:"0 10px"}},io={style:{display:"flex",width:"100%","margin-top":"10px"}},ao={key:1,class:"data-empty-text"},so={class:"prologue-chunk-edit"},ro={class:"prompt-title-padding item-title space-between"},po={key:0,style:{padding:"0 10px"}},mo={class:"quick-command"},co={style:{display:"flex","align-items":"center"}},uo={key:1,width:"14px",height:"14px",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},go={style:{"max-width":"400px","margin-left":"4px"},class:"ellipsis"},fo={style:{"align-items":"center"},class:"quick-command-icon"},Co={key:1,class:"data-empty-text"},vo={class:"prologue-chunk"},wo={style:{display:"flex","justify-content":"space-between",width:"100%","margin-right":"2px"}},ko={class:"prologue-chunk"},_o={style:{display:"flex","justify-content":"space-between",width:"100%","margin-left":"2px"}},yo={style:{display:"flex",width:"100%","justify-content":"space-between"}},ho=["src"],Ao={class:"knowledge-name"},bo={key:1,class:"data-empty-text"},Lo={class:"prologue-chunk"};function Io(h,o,u,e,N,P){const c=r("Icon"),a=r("a-tooltip"),v=r("a-button"),g=r("a-col"),F=r("a-row"),j=r("a-tag"),U=r("a-card"),b=r("a-form-item"),Y=r("a-textarea"),K=r("j-markdown-editor"),V=r("a-input"),y=r("draggable"),I=r("JDictSelectTag"),x=r("a-input-number"),E=r("a-form"),q=r("chat"),k=r("BasicModal"),f=r("AiAppAddKnowledgeModal"),C=r("AiAppAddFlowModal"),L=r("AiAppParamsSettingModal"),T=r("AiAppModal"),G=r("AiAppGeneratedPromptModal"),H=r("AiAppQuickCommandModal");return m(),_("div",Nt,[l(k,{wrapClassName:"ai-app-edit-modal",destroyOnClose:"",onRegister:e.registerModal,canFullscreen:!1,defaultFullscreen:"",width:"800px",footer:null,onVisibleChange:e.visibleChange},{title:i(()=>[n("div",Pt,[n("div",Bt,[n("img",{src:e.getImage(),class:"header-img"},null,8,Jt),n("div",Rt,J(e.formState.name),1),l(a,{title:"编辑"},{default:i(()=>[l(c,{icon:"ant-design:edit-outlined",style:{"margin-left":"4px",cursor:"pointer"},color:"#354052",size:"20",onClick:e.handleEdit},null,8,["onClick"])]),_:1})]),o[11]||(o[11]=n("div",null,"应用编排",-1)),n("div",zt,[l(v,{onClick:e.handleOk,style:{"margin-right":"30px"},type:"primary"},{default:i(()=>o[10]||(o[10]=[R("保存")])),_:1,__:[10]},8,["onClick"])])])]),default:i(()=>[n("div",Ut,[l(F,{span:24},{default:i(()=>[l(g,{span:10},{default:i(()=>o[12]||(o[12]=[n("div",{class:"orchestration"},"编排",-1)])),_:1,__:[12]}),l(g,{span:14},{default:i(()=>o[13]||(o[13]=[n("div",{class:"view"},"预览",-1)])),_:1,__:[13]})]),_:1}),l(F,{span:24},{default:i(()=>[l(g,{span:10,class:"setting-left"},{default:i(()=>[l(E,{class:"antd-modal-form",ref:"formRef",model:e.formState,rules:e.validatorRules},{default:i(()=>[l(F,null,{default:i(()=>[e.formState.type==="chatFLow"?(m(),O(g,{key:0,span:24,class:"mt-10"},{default:i(()=>[n("div",Et,[l(b,S({labelCol:e.labelCol,wrapperCol:e.wrapperCol},e.validateInfos.flowId),{label:i(()=>[n("div",jt,[o[15]||(o[15]=n("span",null,"关联流程",-1)),n("span",{onClick:o[0]||(o[0]=(...s)=>e.handleAddFlowClick&&e.handleAddFlowClick(...s)),class:"knowledge-txt"},[l(c,{icon:"ant-design:plus-outlined",size:"13",style:{"margin-right":"2px"}}),o[14]||(o[14]=R("添加 "))])])]),default:i(()=>[e.flowData?(m(),O(U,{key:0,hoverable:"",class:"knowledge-card","body-style":{width:"100%"}},{default:i(()=>[n("div",Kt,[n("div",Vt,[n("img",{src:e.getFlowImage(e.flowData.icon),class:"flow-icon"},null,8,Tt),n("div",Gt,[n("span",Ht,J(e.flowData.name),1),e.flowData.metadata&&e.flowData.metadata.length>0?(m(),_("div",Wt,[o[16]||(o[16]=n("span",{class:"tag-input"},"输入",-1)),(m(!0),_(le,null,ne(e.flowData.metadata,(s,A)=>(m(),_("div",null,[l(j,{color:"rgba(87,104,161,0.08)",class:"tags-meadata"},{default:i(()=>[A<5?(m(),_("span",Xt,J(s.field),1)):Q("",!0)]),_:2},1024)]))),256))])):Q("",!0)])]),l(c,{onClick:e.handleDeleteFlow,icon:"ant-design:close-outlined",size:"20",class:"knowledge-icon"},null,8,["onClick"])])]),_:1})):(m(),_("div",Yt," 工作流支持通过可视化的方式，对大语言模型、脚本、增强等功能进行组合，从而实现复杂、稳定的业务流程编排，例如旅行规划、报告分析。 "))]),_:1},16,["labelCol","wrapperCol"])])]),_:1})):Q("",!0),e.formState.type==="chatSimple"?(m(),O(g,{key:1,span:24},{default:i(()=>[n("div",Zt,[l(b,S({labelCol:e.labelCol,wrapperCol:e.wrapperCol},e.validateInfos.prompt,{style:{"margin-bottom":"0"}}),{label:i(()=>[n("div",$t,[o[18]||(o[18]=n("span",null,"提示词",-1)),l(v,{size:"middle",onClick:e.generatedPrompt,ghost:""},{default:i(()=>o[17]||(o[17]=[n("span",{style:{"align-items":"center",display:"flex"}},[n("svg",{width:"1em",height:"1em",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M18.9839 1.85931C19.1612 1.38023 19.8388 1.38023 20.0161 1.85931L20.5021 3.17278C20.5578 3.3234 20.6766 3.44216 20.8272 3.49789L22.1407 3.98392C22.6198 4.1612 22.6198 4.8388 22.1407 5.01608L20.8272 5.50211C20.6766 5.55784 20.5578 5.6766 20.5021 5.82722L20.0161 7.14069C19.8388 7.61977 19.1612 7.61977 18.9839 7.14069L18.4979 5.82722C18.4422 5.6766 18.3234 5.55784 18.1728 5.50211L16.8593 5.01608C16.3802 4.8388 16.3802 4.1612 16.8593 3.98392L18.1728 3.49789C18.3234 3.44216 18.4422 3.3234 18.4979 3.17278L18.9839 1.85931zM13.5482 4.07793C13.0164 2.64069 10.9836 2.64069 10.4518 4.07793L8.99368 8.01834C8.82648 8.47021 8.47021 8.82648 8.01834 8.99368L4.07793 10.4518C2.64069 10.9836 2.64069 13.0164 4.07793 13.5482L8.01834 15.0063C8.47021 15.1735 8.82648 15.5298 8.99368 15.9817L10.4518 19.9221C10.9836 21.3593 13.0164 21.3593 13.5482 19.9221L15.0063 15.9817C15.1735 15.5298 15.5298 15.1735 15.9817 15.0063L19.9221 13.5482C21.3593 13.0164 21.3593 10.9836 19.9221 10.4518L15.9817 8.99368C15.5298 8.82648 15.1735 8.47021 15.0063 8.01834L13.5482 4.07793zM5.01608 16.8593C4.8388 16.3802 4.1612 16.3802 3.98392 16.8593L3.49789 18.1728C3.44216 18.3234 3.3234 18.4422 3.17278 18.4979L1.85931 18.9839C1.38023 19.1612 1.38023 19.8388 1.85931 20.0161L3.17278 20.5021C3.3234 20.5578 3.44216 20.6766 3.49789 20.8272L3.98392 22.1407C4.1612 22.6198 4.8388 22.6198 5.01608 22.1407L5.50211 20.8272C5.55784 20.6766 5.6766 20.5578 5.82722 20.5021L7.14069 20.0161C7.61977 19.8388 7.61977 19.1612 7.14069 18.9839L5.82722 18.4979C5.6766 18.4422 5.55784 18.3234 5.50211 18.1728L5.01608 16.8593z"})]),n("span",{style:{"margin-left":"4px"}},"生成")],-1)])),_:1,__:[17]},8,["onClick"])])]),default:i(()=>[l(Y,{rows:8,value:e.formState.prompt,"onUpdate:value":o[1]||(o[1]=s=>e.formState.prompt=s),placeholder:"请输入提示词"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])])]),_:1})):Q("",!0),l(g,{span:24,class:"mt-10"},{default:i(()=>[n("div",eo,[l(b,S({labelCol:e.labelCol,wrapperCol:e.wrapperCol},e.validateInfos.prologue,{style:{"margin-bottom":"0"}}),{label:i(()=>o[19]||(o[19]=[n("div",{class:"prompt-title-padding item-title"},"开场白",-1)])),default:i(()=>[n("div",to,[l(K,{height:166,value:e.formState.prologue,"onUpdate:value":o[2]||(o[2]=s=>e.formState.prologue=s),onChange:e.prologueTextAreaBlur,preview:{mode:"view",action:[]}},null,8,["value","onChange"])])]),_:1},16,["labelCol","wrapperCol"])])]),_:1}),l(g,{span:24,class:"mt-10"},{default:i(()=>[n("div",oo,[l(b,S({labelCol:e.labelCol,wrapperCol:e.wrapperCol},e.validateInfos.presetQuestion,{style:{"margin-bottom":"0"}}),{label:i(()=>[n("div",lo,[o[20]||(o[20]=n("div",{class:"item-title"},"预设问题",-1)),l(a,{title:"添加预设问题"},{default:i(()=>[l(c,{icon:"ant-design:plus-outlined",size:"13",style:{"margin-right":"16px",cursor:"pointer"},onClick:e.presetQuestionAddClick},null,8,["onClick"])]),_:1})])]),default:i(()=>[e.presetQuestionList.length>0?(m(),_("div",no,[l(y,{disabled:e.disabledDrag,"item-key":"key",modelValue:e.presetQuestionList,"onUpdate:modelValue":o[3]||(o[3]=s=>e.presetQuestionList=s),onEnd:e.presetQuestionEnd},{item:i(({element:s})=>[n("div",io,[l(c,{icon:"ant-design:holder-outlined",size:"20"}),l(V,{placeholder:"输入预设问题",value:s.descr,"onUpdate:value":A=>s.descr=A,style:{"margin-left":"10px"},onBlur:A=>e.onBlur(s),onFocus:A=>e.onFocus(s),onChange:e.questionChange},null,8,["value","onUpdate:value","onBlur","onFocus","onChange"]),l(c,{style:{cursor:"pointer","margin-left":"10px"},icon:"ant-design:delete-outlined",onClick:A=>e.deleteQuestionClick(s.key)},null,8,["onClick"])])]),_:1},8,["disabled","modelValue","onEnd"])])):(m(),_("div",ao," 预设问题问题是新对话的初始引导，用户可以快速发起预设对话 "))]),_:1},16,["labelCol","wrapperCol"])])]),_:1}),l(g,{span:24,class:"mt-10"},{default:i(()=>[n("div",so,[l(b,S({labelCol:e.labelCol,wrapperCol:e.wrapperCol},e.validateInfos.presetQuestion,{style:{"margin-bottom":"0"}}),{label:i(()=>[n("div",ro,[o[21]||(o[21]=n("div",{class:"item-title"},"快捷指令",-1)),l(a,{title:"添加快捷指令"},{default:i(()=>[l(c,{icon:"ant-design:plus-outlined",size:"13",style:{"margin-right":"16px",cursor:"pointer"},onClick:e.quickCommandAddClick},null,8,["onClick"])]),_:1})])]),default:i(()=>[e.quickCommandList.length>0?(m(),_("div",po,[l(y,{"item-key":"key",modelValue:e.quickCommandList,"onUpdate:modelValue":o[4]||(o[4]=s=>e.quickCommandList=s),onEnd:e.quickCommandEnd},{item:i(({element:s})=>[n("div",mo,[n("div",co,[s.icon?(m(),O(c,{key:0,icon:s.icon,size:"20"},null,8,["icon"])):(m(),_("svg",uo,o[22]||(o[22]=[n("path",{d:"M18.9839 1.85931C19.1612 1.38023 19.8388 1.38023 20.0161 1.85931L20.5021 3.17278C20.5578 3.3234 20.6766 3.44216 20.8272 3.49789L22.1407 3.98392C22.6198 4.1612 22.6198 4.8388 22.1407 5.01608L20.8272 5.50211C20.6766 5.55784 20.5578 5.6766 20.5021 5.82722L20.0161 7.14069C19.8388 7.61977 19.1612 7.61977 18.9839 7.14069L18.4979 5.82722C18.4422 5.6766 18.3234 5.55784 18.1728 5.50211L16.8593 5.01608C16.3802 4.8388 16.3802 4.1612 16.8593 3.98392L18.1728 3.49789C18.3234 3.44216 18.4422 3.3234 18.4979 3.17278L18.9839 1.85931zM13.5482 4.07793C13.0164 2.64069 10.9836 2.64069 10.4518 4.07793L8.99368 8.01834C8.82648 8.47021 8.47021 8.82648 8.01834 8.99368L4.07793 10.4518C2.64069 10.9836 2.64069 13.0164 4.07793 13.5482L8.01834 15.0063C8.47021 15.1735 8.82648 15.5298 8.99368 15.9817L10.4518 19.9221C10.9836 21.3593 13.0164 21.3593 13.5482 19.9221L15.0063 15.9817C15.1735 15.5298 15.5298 15.1735 15.9817 15.0063L19.9221 13.5482C21.3593 13.0164 21.3593 10.9836 19.9221 10.4518L15.9817 8.99368C15.5298 8.82648 15.1735 8.47021 15.0063 8.01834L13.5482 4.07793zM5.01608 16.8593C4.8388 16.3802 4.1612 16.3802 3.98392 16.8593L3.49789 18.1728C3.44216 18.3234 3.3234 18.4422 3.17278 18.4979L1.85931 18.9839C1.38023 19.1612 1.38023 19.8388 1.85931 20.0161L3.17278 20.5021C3.3234 20.5578 3.44216 20.6766 3.49789 20.8272L3.98392 22.1407C4.1612 22.6198 4.8388 22.6198 5.01608 22.1407L5.50211 20.8272C5.55784 20.6766 5.6766 20.5578 5.82722 20.5021L7.14069 20.0161C7.61977 19.8388 7.61977 19.1612 7.14069 18.9839L5.82722 18.4979C5.6766 18.4422 5.55784 18.3234 5.50211 18.1728L5.01608 16.8593z"},null,-1)]))),n("div",go,J(s.name),1)]),n("div",fo,[l(a,{title:"编辑"},{default:i(()=>[l(c,{style:{cursor:"pointer","margin-left":"10px"},icon:"ant-design:edit-outlined",onClick:A=>e.editCommandClick(s)},null,8,["onClick"])]),_:2},1024),l(a,{title:"删除"},{default:i(()=>[l(c,{style:{cursor:"pointer","margin-left":"10px"},icon:"ant-design:delete-outlined",onClick:A=>e.deleteCommandClick(s.key)},null,8,["onClick"])]),_:2},1024)])])]),_:1},8,["modelValue","onEnd"])])):(m(),_("div",Co," 快捷指令是对话输入框上方的按钮，配置完成后，用户可以快速发起预设对话 "))]),_:1},16,["labelCol","wrapperCol"])])]),_:1}),e.formState.type==="chatSimple"?(m(),O(g,{key:2,span:24,class:"mt-10"},{default:i(()=>[n("div",vo,[l(b,S({labelCol:e.labelCol,wrapperCol:e.wrapperCol},e.validateInfos.modelId),{label:i(()=>[n("div",wo,[o[24]||(o[24]=n("div",{class:"item-title"},"AI模型",-1)),n("div",{onClick:o[5]||(o[5]=s=>e.handleParamSettingClick("model")),class:"knowledge-txt"},[l(c,{icon:"ant-design:setting-outlined",size:"13",style:{"margin-right":"2px"}}),o[23]||(o[23]=R("参数配置 "))])])]),default:i(()=>[l(I,{value:e.formState.modelId,"onUpdate:value":o[6]||(o[6]=s=>e.formState.modelId=s),placeholder:"请选择AI模型","dict-code":"airag_model where model_type = 'LLM',name,id",style:{width:"100%"}},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])])]),_:1})):Q("",!0),e.formState.type==="chatSimple"?(m(),O(g,{key:3,span:24,class:"mt-10"},{default:i(()=>[n("div",ko,[l(b,S({class:"knowledgeId",style:{width:"100%"},labelCol:e.labelCol,wrapperCol:e.wrapperCol},e.validateInfos.knowledgeIds),{label:i(()=>[n("div",_o,[o[27]||(o[27]=n("div",{class:"item-title"},"知识库",-1)),n("div",null,[n("span",{onClick:o[7]||(o[7]=s=>e.handleParamSettingClick("knowledge")),class:"knowledge-txt"},[l(c,{icon:"ant-design:setting-outlined",size:"13",style:{"margin-right":"2px"}}),o[25]||(o[25]=R("参数配置 "))]),n("span",{onClick:o[8]||(o[8]=(...s)=>e.handleAddKnowledgeIdClick&&e.handleAddKnowledgeIdClick(...s)),class:"knowledge-txt"},[l(c,{icon:"ant-design:plus-outlined",size:"13",style:{"margin-right":"2px"}}),o[26]||(o[26]=R("添加 "))])])])]),default:i(()=>[l(F,{span:24},{default:i(()=>[e.knowledgeDataList&&e.knowledgeDataList.length>0?(m(!0),_(le,{key:0},ne(e.knowledgeDataList,s=>(m(),O(g,{span:12},{default:i(()=>[l(U,{hoverable:"",class:"knowledge-card","body-style":{width:"100%"}},{default:i(()=>[n("div",yo,[n("div",null,[n("img",{class:"knowledge-img",src:e.knowledge},null,8,ho),n("span",Ao,J(s.name),1)]),l(c,{onClick:A=>e.handleDeleteKnowledge(s.id),icon:"ant-design:close-outlined",size:"20",class:"knowledge-icon"},null,8,["onClick"])])]),_:2},1024)]),_:2},1024))),256)):(m(),_("div",bo," 添加知识库后，用户发送消息时，智能体能够引用文本知识中的内容回答用户问题。 "))]),_:1})]),_:1},16,["labelCol","wrapperCol"])])]),_:1})):Q("",!0),l(g,{span:24,class:"mt-10"},{default:i(()=>[n("div",Lo,[l(b,S({labelCol:e.labelCol,wrapperCol:e.wrapperCol},e.validateInfos.msgNum),{label:i(()=>o[28]||(o[28]=[n("div",{style:{"margin-left":"2px"}},"历史聊天记录",-1)])),default:i(()=>[l(x,{value:e.formState.msgNum,"onUpdate:value":o[9]||(o[9]=s=>e.formState.msgNum=s)},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])])]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1}),l(g,{span:14,class:"setting-right"},{default:i(()=>[l(q,{uuid:e.uuid,prologue:e.prologue,appId:e.appId,formState:e.formState,url:"/airag/app/debug",presetQuestion:e.presetQuestion,quickCommandData:e.quickCommandList},null,8,["uuid","prologue","appId","formState","presetQuestion","quickCommandData"])]),_:1})]),_:1})])]),_:1},8,["onRegister","onVisibleChange"]),l(f,{onRegister:e.registerKnowledgeModal,onSuccess:e.handleSuccess},null,8,["onRegister","onSuccess"]),l(C,{onRegister:e.registerFlowModal,onSuccess:e.handleAddFlowSuccess},null,8,["onRegister","onSuccess"]),l(L,{onRegister:e.registerParamsSettingModal,onOk:e.handleParamsSettingOk},null,8,["onRegister","onOk"]),l(T,{onRegister:e.registerAiAppModal,onSuccess:e.handelEditSuccess},null,8,["onRegister","onSuccess"]),l(G,{onRegister:e.registerAiAppPromptModal,onOk:e.handleAiAppPromptOk},null,8,["onRegister","onOk"]),l(H,{onRegister:e.registerAiAppCommandModal,onOk:e.handleAiAppCommandOk,onUpdateOk:e.handleAiAppCommandUpdateOk},null,8,["onRegister","onOk","onUpdateOk"])])}const Gl=mt(Dt,[["render",Io],["__scopeId","data-v-f3c74553"]]);export{Gl as default};
