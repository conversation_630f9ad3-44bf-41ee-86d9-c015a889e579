var h=(g,r,o)=>new Promise((p,a)=>{var c=t=>{try{n(o.next(t))}catch(s){a(s)}},d=t=>{try{n(o.throw(t))}catch(s){a(s)}},n=t=>t.done?p(t.value):Promise.resolve(t.value).then(c,d);n((o=o.apply(g,r)).next())});import{d as x,aq as v,ar as _,k as u,aD as f,u as m,G as T,au as w}from"./vue-vendor-dy9k-Yad.js";import{u as I}from"./index-BkGZ5fiW.js";import{u as P,a as M}from"./index-CCWaWN5g.js";import{X as C}from"./antd-vue-vendor-me9YkNVC.js";import{Q as R}from"./componentMap-Bkie1n3v.js";import k from"./BasicTable-xCEZpGLb.js";import"./vxe-table-vendor-B22HppNm.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const z={class:"p-4"},D=x({__name:"index",setup(g){const{createMessage:r}=P(),o=[{title:"接收者",dataIndex:"receiver",width:120,resizable:!0},{title:"关联产品信息",dataIndex:"productInfo",width:200,resizable:!0},{title:"留言内容",dataIndex:"content",width:300,resizable:!0,ellipsis:!0},{title:"发送状态",dataIndex:"sendStatus",width:120,resizable:!0,slots:{customRender:"sendStatus"}},{title:"发送时间",dataIndex:"sendTime",width:180,resizable:!0}],p=[{field:"sendStatus",label:"发送状态",component:"Select",componentProps:{placeholder:"请选择发送状态",options:[{label:"发送成功",value:"success"},{label:"发送失败",value:"failed"},{label:"发送中",value:"sending"}]},colProps:{span:6}},{field:"messageStatus",label:"消息状态",component:"Select",componentProps:{placeholder:"请选择消息状态",options:[{label:"未读",value:"unread"},{label:"已读",value:"read"},{label:"已回复",value:"replied"}]},colProps:{span:6}},{field:"timeRange",label:"时间区间",component:"RangePicker",componentProps:{showTime:!0,format:"YYYY-MM-DD HH:mm:ss",placeholder:["开始时间","结束时间"]},colProps:{span:6}}],a=[{id:"1",receiver:"客服小王",productInfo:"智能手机 iPhone 15",content:"您好，我想了解一下这款手机的详细配置和价格信息。",sendStatus:"success",messageStatus:"read",sendTime:"2024-01-15 10:30:00"},{id:"2",receiver:"技术支持",productInfo:"笔记本电脑 MacBook Pro",content:"电脑出现了一些问题，希望能得到技术支持。",sendStatus:"failed",messageStatus:"unread",sendTime:"2024-01-15 14:20:00"},{id:"3",receiver:"销售顾问",productInfo:"无线耳机 AirPods Pro",content:"想要购买这款耳机，请问有什么优惠活动吗？",sendStatus:"success",messageStatus:"replied",sendTime:"2024-01-16 09:15:00"},{id:"4",receiver:"客服小李",productInfo:"智能手表 Apple Watch",content:"手表的保修政策是怎样的？",sendStatus:"sending",messageStatus:"unread",sendTime:"2024-01-16 16:45:00"},{id:"5",receiver:"产品专员",productInfo:"平板电脑 iPad Air",content:"请问这款平板适合学生使用吗？有教育优惠吗？",sendStatus:"success",messageStatus:"read",sendTime:"2024-01-17 11:30:00"}];function c(e){return{success:"green",failed:"red",sending:"blue"}[e]||"default"}function d(e){return{success:"发送成功",failed:"发送失败",sending:"发送中"}[e]||e}const n=e=>h(null,null,function*(){return yield new Promise(l=>setTimeout(l,500)),{items:a,total:a.length}}),[t,{reload:s}]=I({api:n,columns:o,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,inset:!0,maxHeight:478,actionColumn:{width:160,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"},rowKey:"id",formConfig:{labelWidth:64,size:"large",schemas:p}});function S(e){r.info(`正在重发给 ${e.receiver} 的留言...`),setTimeout(()=>{r.success("留言重发成功"),s()},1e3)}function b(e){r.success(`已删除发给 ${e.receiver} 的留言`),s()}return(e,l)=>(_(),v("div",z,[u(m(k),{onRegister:m(t)},{action:f(({record:i})=>[u(m(R),{actions:[{label:"重发",icon:"ant-design:redo-outlined",onClick:S.bind(null,i),ifShow:()=>i.sendStatus==="failed"},{label:"删除",icon:"ant-design:delete-outlined",color:"error",popConfirm:{title:"确定要删除这条留言吗？",confirm:b.bind(null,i)}}]},null,8,["actions"])]),sendStatus:f(({record:i})=>[u(m(C),{color:c(i.sendStatus)},{default:f(()=>[T(w(d(i.sendStatus)),1)]),_:2},1032,["color"])]),_:1},8,["onRegister"])]))}}),He=M(D,[["__scopeId","data-v-5dc7f457"]]);export{He as default};
