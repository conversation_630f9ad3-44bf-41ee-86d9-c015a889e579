import{cs as ae,u as ie,ap as ne,j as le,aZ as se,cx as me,bj as pe}from"./index-CCWaWN5g.js";import{n as ue,f,r as J,w as ce,ag as b,aq as P,ar as c,ah as N,k as x,aB as T,aG as de,aD as B,F as fe,aC as be,aA as he}from"./vue-vendor-dy9k-Yad.js";import{c7 as ge}from"./antd-vue-vendor-me9YkNVC.js";import ye from"./DetailForm-c592b8d8-BIx7KBmJ.js";import Se from"./OnlineSubFormDetail-8be879b9-LBHMpLKz.js";import{m as ve,u as we}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{s as Pe}from"./constant-fa63bd66-Ddbq-fz2.js";import"./index-mbACBRQ9.js";import"./index-L3cSIXth.js";import"./componentMap-Bkie1n3v.js";import"./index-Diw57m_E.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./JUpload-CRos0F1P.js";import"./useForm-CgkFTrrO.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";var ke=Object.defineProperty,M=Object.getOwnPropertySymbols,xe=Object.prototype.hasOwnProperty,Te=Object.prototype.propertyIsEnumerable,R=(i,n,r)=>n in i?ke(i,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[n]=r,_=(i,n)=>{for(var r in n||(n={}))xe.call(n,r)&&R(i,r,n[r]);if(M)for(var r of M(n))Te.call(n,r)&&R(i,r,n[r]);return i},V=(i,n,r)=>new Promise((o,d)=>{var p=s=>{try{h(r.next(s))}catch(g){d(g)}},m=s=>{try{h(r.throw(s))}catch(g){d(g)}},h=s=>s.done?o(s.value):Promise.resolve(s.value).then(p,m);h((r=r.apply(i,n)).next())});function Oe(){const i={},n={setFieldsValue:"<m> 设置表单控件的值",getFieldsValue:"<m> 获取表单控件的值",sh:"<p> 表单控件的显示隐藏状态"},r=new Proxy(n,{get(p,m){return Reflect.get(i,m)}});function o(p,m){i[p]=m}function d(p){Object.keys(p).map(m=>{i[m]=p[m]})}return o("$nextTick",ue),o("addObject2Context",o),{onlineFormDetailContext:r,addObject2Context:o,resetContext:d}}const Ce={name:"OnlineFormDetail",components:{DetailForm:ye,Loading:pe,PrinterOutlined:ge,OnlineSubFormDetail:Se},props:{id:{type:String,default:""},formTemplate:{type:Number,default:1},disabled:{type:Boolean,default:!1},isTree:{type:Boolean,default:!1},pidField:{type:String,default:""},submitTip:{type:Boolean,default:!0},showSub:{type:Boolean,default:!0},themeTemplate:{type:String,default:""}},emits:["success","rendered"],setup(i,{emit:n}){const{createMessage:r}=ie(),{getIsMobile:o}=ne(),d=f(""),p=f(!0),m=f(!1),h=f(1),s=f({}),g=f(o.value?"auto":300),O=f(340),C=f(!o.value);let y={};const S=J({}),l=J({reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:""}),{detailFormSchemas:v,hasSubTable:H,subTabInfo:I,refMap:U,showStatus:F,subDataSource:j,createFormSchemas:$,formSpan:A}=ve(i);function L(e){let t={reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:1,modalMinWidth:""};e&&(t=JSON.parse(e)),Object.keys(t).map(a=>{l[a]=t[a]})}const{onlineFormDetailContext:D,resetContext:z}=Oe();let{EnhanceJS:k,initCgEnhanceJs:K}=we(D,!1);function Q(e){return V(this,null,function*(){h.value=e.head.tableType,d.value=e.head.tableName,p.value=e.head.tableType==1,L(e.head.extConfigJson),$(e.schema.properties),k=K(e.enhanceJs),n("rendered",l)})}function W(e,t){return V(this,null,function*(){yield Z(t),E(!0)})}function q(e){let t=`/online/cgform/api/detail/${i.id}/${e}`;return new Promise((a,u)=>{le.get({url:t},{isTransformResponse:!1}).then(w=>{w.success?a(w.result):(u(),r.warning(w.message))}).catch(()=>{u()})})}function E(e){Object.keys(F).map(t=>{F[t]=e})}function G(){E(!1),setTimeout(()=>{E(!0)},300)}function Z(e){return V(this,null,function*(){y=yield q(e.id),v.value.filter(t=>t.hidden).forEach(t=>t.hidden=!1),Object.keys(S).forEach(function(t){delete S[t]}),re({buttonCode:"loaded"}),s.value=_({},y),X(y)})}function X(e){e||(e={});let t=Object.keys(j.value);if(t&&t.length>0){let a={};for(let u of t)a[u]=e[u]||[];j.value=a}}function Y(e){return"online_"+e+":"}function ee(){let e=l.reportPrintUrl,t=s.value;if(t){let a=t.id,u=se();me(e,a,u)}}function te(e){let t=s.value;return oe(t,e)}function oe(e,t){if(e){let a=e[t];return!a&&a!==0&&(a=e[t.toLowerCase()],!a&&a!==0&&(a=e[t.toUpperCase()])),a}return""}function re({buttonCode:e}){k&&k[e]&&k[e].call(D,D)}return ce(S,e=>{Object.entries(e).forEach(([t,a])=>{if(a==!1){const u=v.value.find(w=>w.field===t);u&&(u.hidden=!0)}})}),z({setFieldsValue:e=>{Object.entries(e).forEach(([t,a])=>{y[t]=a})},getFieldsValue:()=>_({},y),sh:S}),{detailFormSchemas:v,formData:s,formSpan:A,tableName:d,loading:m,hasSubTable:H,subTabInfo:I,subFormHeight:g,subTableHeight:O,refMap:U,onTabChange:G,subDataSource:j,getSubTableAuthPre:Y,show:W,createRootProperties:Q,onOpenReportPrint:ee,onlineExtConfigJson:l,getSubTableForeignKeyValue:te,showStatus:F,ERP:Pe,rowNumber:C}}},Fe=["id"],je={key:0,style:{"text-align":"right",position:"absolute",top:"15px",right:"20px","z-index":"999"}},De={key:1};function Ee(i,n,r,o,d,p){const m=b("PrinterOutlined"),h=b("detail-form"),s=b("online-sub-form-detail"),g=b("JVxeTable"),O=b("a-spin"),C=b("a-tab-pane"),y=b("a-tabs"),S=b("Loading");return c(),P("div",{id:o.tableName+"_form"},[o.formData.id&&o.onlineExtConfigJson.reportPrintShow?(c(),P("div",je,[x(m,{title:"打印",onClick:o.onOpenReportPrint,style:{"font-size":"16px"}},null,8,["onClick"])])):N("",!0),x(h,{schemas:o.detailFormSchemas,data:o.formData,span:o.formSpan},null,8,["schemas","data","span"]),r.themeTemplate!==o.ERP&&o.hasSubTable&&r.showSub?(c(),T(y,{key:1,onChange:o.onTabChange},{default:B(()=>[(c(!0),P(fe,null,be(o.subTabInfo,(l,v)=>(c(),T(C,{tab:l.describe,key:v+"",forceRender:!0},{default:B(()=>[l.relationType==1?(c(),P("div",{key:0,style:he({"overflow-y":"auto","overflow-x":"hidden","max-height":o.subFormHeight+"px"})},[x(s,{table:l.key,"form-template":r.formTemplate,"main-id":o.getSubTableForeignKeyValue(l.foreignKey),properties:l.properties},null,8,["table","form-template","main-id","properties"])],4)):(c(),P("div",De,[o.showStatus[l.key]?(c(),T(g,{key:0,ref_for:!0,ref:o.refMap[l.key],"keep-source":"","row-number":o.rowNumber,"row-selection":"",height:o.subTableHeight,disabled:!0,columns:l.columns,dataSource:o.subDataSource[l.key],authPre:o.getSubTableAuthPre(l.key)},null,8,["row-number","height","columns","dataSource","authPre"])):(c(),T(O,{key:1,spinning:!0}))]))]),_:2},1032,["tab"]))),128))]),_:1},8,["onChange"])):N("",!0),x(S,{loading:o.loading,absolute:!1},null,8,["loading"]),de(i.$slots,"bottom")],8,Fe)}const Qt=ae(Ce,[["render",Ee]]);export{Qt as default};
