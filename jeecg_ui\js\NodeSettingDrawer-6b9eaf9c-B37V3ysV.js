import{d as q,f as D,e as v,u as l,ag as g,aB as C,ar as c,aD as h,aq as P,ah as F,F as V,k as s,at as k,au as I,aJ as J,aK as K}from"./vue-vendor-dy9k-Yad.js";import{h as _}from"./antd-vue-vendor-me9YkNVC.js";import{u as U,B as j}from"./index-JbqXEynz.js";import{r as z}from"./index-9c51646a-BviQbLw-.js";import{d as A}from"./NodeIcon.vue_vue_type_script_setup_true_lang-0fd734b2-CEYYkV5U.js";import{T as L}from"./TitleEditor.vue_vue_type_style_index_0_lang-f6eddb57-D8gWOKwz.js";import{A as O}from"./NodeSetting.vue_vue_type_style_index_0_lang-7010fa43-D-YRIXXb.js";import{s as Q}from"./_plugin-vue_export-helper-dad06003-lGy7RumW.js";import"./index-BOX6--gq.js";import"./index-CCWaWN5g.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";var T=(w,d,i)=>new Promise((o,r)=>{var f=t=>{try{m(i.next(t))}catch(a){r(a)}},y=t=>{try{m(i.throw(t))}catch(a){r(a)}},m=t=>t.done?o(t.value):Promise.resolve(t.value).then(f,y);m((i=i.apply(w,d)).next())});const $={class:"airag-node-label"},G={class:"remarks"},H={class:"content"},M=q({__name:"NodeSettingDrawer",props:{},emits:["register","update"],setup(w,{emit:d}){const i=d,o=D(),r=D({}),[f,{getVisible:y,closeDrawer:m}]=U(e=>T(this,null,function*(){o.value=e.node,r.value=_(e.node.properties)})),t=e=>{Object.entries(e).forEach(([p,u])=>{r.value[p]=u}),o.value.properties=_(r.value),i("update",o.value)},a=v({get:()=>l(r).text,set:e=>t({text:e})}),x=v({get:()=>l(r).remarks,set:e=>t({remarks:e})}),n=v(()=>{var e;const p=(e=o.value)==null?void 0:e.type;return p?z.get(p)||{}:{}}),b=v(()=>({title:a.value,"onUpdate:title":e=>a.value=e,promptProps:{title:"修改节点名称",defaultValue:a.value,placeholder:"请输入节点名称",rules:[{required:!0,message:"请输入节点名称！"},{max:12,message:"节点名称不能超过12个字符！"}]}}));function N(){m()}return(e,p)=>{const u=g("a-space"),E=g("a-input"),R=g("a-empty");return c(),C(l(j),{onRegister:l(f),width:600,mask:!1,getContainer:!1,onClose:N},{title:h(()=>[s(L,J(K(b.value)),null,16)]),default:h(()=>{var B;return[l(y)?(c(),P(V,{key:0},[(B=n.value)!=null&&B.type?(c(),P(V,{key:0},[s(u,{style:{"margin-bottom":"8px"}},{default:h(()=>[s(A,{type:n.value.type},null,8,["type"]),k("span",$,I(n.value.label),1)]),_:1}),k("div",G,[s(E,{value:x.value,"onUpdate:value":p[0]||(p[0]=S=>x.value=S),placeholder:"描述",style:{width:"100%"}},null,8,["value"])]),k("div",H,[s(O,{type:n.value.type,node:o.value,properties:r.value,setProperties:t},null,8,["type","node","properties"])])],64)):(c(),C(R,{key:1,description:"未知节点类型"}))],64)):F("",!0)]}),_:1},8,["onRegister"])}}}),ot=Q(M,[["__scopeId","data-v-f4f57a64"]]);export{ot as default};
