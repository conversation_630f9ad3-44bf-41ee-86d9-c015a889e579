import{w as u,f}from"./vue-vendor-dy9k-Yad.js";import{n as a,q as m}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function T(e,o,r=!1){if(!a(e))throw new Error("handle is not Function!");const{readyRef:t,stop:n,start:i}=c(o);return r?e():u(t,s=>{s&&e()},{immediate:!1}),{readyRef:t,stop:n,start:i}}function c(e){const o=f(!1);let r;function t(){o.value=!1,r&&window.clearTimeout(r)}function n(){t(),r=setTimeout(()=>{o.value=!0},e)}return n(),m(t),{readyRef:o,stop:t,start:n}}export{T as useTimeoutFn,c as useTimeoutRef};
