var p=(_,c,o)=>new Promise((u,i)=>{var m=e=>{try{a(o.next(e))}catch(n){i(n)}},l=e=>{try{a(o.throw(e))}catch(n){i(n)}},a=e=>e.done?u(e.value):Promise.resolve(e.value).then(m,l);a((o=o.apply(_,c)).next())});import{d as x,f as B,ag as C,aB as f,ar as g,aD as r,k as h,u as s,ah as y,G as w,aE as k}from"./vue-vendor-dy9k-Yad.js";import{B as M}from"./index-Diw57m_E.js";import{u as S}from"./index-BkGZ5fiW.js";import{d as T,p as v,a as D}from"./route.api-DBvzaHKw.js";import{ac as N}from"./index-CCWaWN5g.js";import O from"./BasicTable-xCEZpGLb.js";import{Q as K}from"./componentMap-Bkie1n3v.js";const V=[{title:"路由ID",dataIndex:"routerId",width:200,align:"left"},{title:"路由名称",dataIndex:"name",width:200},{title:"路由URI",dataIndex:"uri",width:200},{title:"状态",dataIndex:"status",slots:{customRender:"status"},width:150}];const j=x({__name:"RouteRecycleBinModal",emits:["success","register"],setup(_,{emit:c}){const o=c,u=B([]),[i]=N(()=>{u.value=[]}),[m,{reload:l}]=S({rowKey:"id",api:T,columns:V,striped:!0,useSearchForm:!1,bordered:!0,showIndexColumn:!1,pagination:!1,tableSetting:{fullScreen:!0},canResize:!1,actionColumn:{width:150,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"}});function a(t){return p(this,null,function*(){yield v({ids:t.id},l),o("success")})}function e(t){return p(this,null,function*(){yield D({ids:t.id},l)})}function n(t){return[{label:"取回",icon:"ant-design:redo-outlined",popConfirm:{title:"是否确认取回",confirm:a.bind(null,t)}},{label:"彻底删除",icon:"ant-design:scissor-outlined",color:"error",popConfirm:{title:"是否确认删除",confirm:e.bind(null,t)}}]}return(t,d)=>{const R=C("a-tag");return g(),f(s(M),k(t.$attrs,{onRegister:s(i),title:"路由回收站",showOkBtn:!1,width:"1000px",destroyOnClose:""}),{default:r(()=>[h(s(O),{onRegister:s(m)},{status:r(({record:b,text:I})=>[I==0?(g(),f(R,{key:0,color:"pink"},{default:r(()=>d[0]||(d[0]=[w("禁用")])),_:1,__:[0]})):y("",!0),I==1?(g(),f(R,{key:1,color:"#87d068"},{default:r(()=>d[1]||(d[1]=[w("正常")])),_:1,__:[1]})):y("",!0)]),action:r(({record:b})=>[h(s(K),{actions:n(b)},null,8,["actions"])]),_:1},8,["onRegister"])]),_:1},16,["onRegister"])}}}),L=Object.freeze(Object.defineProperty({__proto__:null,default:j},Symbol.toStringTag,{value:"Module"}));export{L as R,j as _,V as c};
