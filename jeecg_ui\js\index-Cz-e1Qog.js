var I=Object.defineProperty,z=Object.defineProperties;var N=Object.getOwnPropertyDescriptors;var b=Object.getOwnPropertySymbols;var Y=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable;var _=(t,s,r)=>s in t?I(t,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[s]=r,w=(t,s)=>{for(var r in s||(s={}))Y.call(s,r)&&_(t,r,s[r]);if(b)for(var r of b(s))E.call(s,r)&&_(t,r,s[r]);return t},S=(t,s)=>z(t,N(s));import{d as k,f,o as K,ag as x,aq as L,ar as J,k as c,aD as i,q as H,aB as F,ah as $,B as O,u as l,G as M,au as y,at as B}from"./vue-vendor-dy9k-Yad.js";import{u as Q}from"./index-BkGZ5fiW.js";import W from"./DiskInfo-Dd85MGuH.js";import{j as e,u as X}from"./index-CCWaWN5g.js";import{i as U}from"./antd-vue-vendor-me9YkNVC.js";import Z from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./gauge-Dhn25c70.js";import"./useECharts-BU6FzBZi.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const A=()=>e.get({url:"/actuator/metrics/system.cpu.count"},{isTransformResponse:!1}),ee=()=>e.get({url:"/actuator/metrics/system.cpu.usage"},{isTransformResponse:!1}),te=()=>e.get({url:"/actuator/metrics/process.start.time"},{isTransformResponse:!1}),re=()=>e.get({url:"/actuator/metrics/process.uptime"},{isTransformResponse:!1}),se=()=>e.get({url:"/actuator/metrics/process.cpu.usage"},{isTransformResponse:!1}),oe=()=>e.get({url:"/actuator/metrics/jvm.memory.max"},{isTransformResponse:!1}),ae=()=>e.get({url:"/actuator/metrics/jvm.memory.committed"},{isTransformResponse:!1}),me=()=>e.get({url:"/actuator/metrics/jvm.memory.used"},{isTransformResponse:!1}),ce=()=>e.get({url:"/actuator/metrics/jvm.buffer.memory.used"},{isTransformResponse:!1}),ne=()=>e.get({url:"/actuator/metrics/jvm.buffer.count"},{isTransformResponse:!1}),ue=()=>e.get({url:"/actuator/metrics/jvm.threads.daemon"},{isTransformResponse:!1}),ie=()=>e.get({url:"/actuator/metrics/jvm.threads.live"},{isTransformResponse:!1}),le=()=>e.get({url:"/actuator/metrics/jvm.threads.peak"},{isTransformResponse:!1}),pe=()=>e.get({url:"/actuator/metrics/jvm.classes.loaded"},{isTransformResponse:!1}),de=()=>e.get({url:"/actuator/metrics/jvm.classes.unloaded"},{isTransformResponse:!1}),ve=()=>e.get({url:"/actuator/metrics/jvm.gc.memory.allocated"},{isTransformResponse:!1}),fe=()=>e.get({url:"/actuator/metrics/jvm.gc.memory.promoted"},{isTransformResponse:!1}),ge=()=>e.get({url:"/actuator/metrics/jvm.gc.max.data.size"},{isTransformResponse:!1}),xe=()=>e.get({url:"/actuator/metrics/jvm.gc.live.data.size"},{isTransformResponse:!1}),ye=()=>e.get({url:"/actuator/metrics/jvm.gc.pause"},{isTransformResponse:!1}),Me=()=>e.get({url:"/actuator/metrics/tomcat.sessions.created"},{isTransformResponse:!1}),je=()=>e.get({url:"/actuator/metrics/tomcat.sessions.expired"},{isTransformResponse:!1}),Te=()=>e.get({url:"/actuator/metrics/tomcat.sessions.active.current"},{isTransformResponse:!1}),Re=()=>e.get({url:"/actuator/metrics/tomcat.sessions.active.max"},{isTransformResponse:!1}),he=()=>e.get({url:"/actuator/metrics/tomcat.sessions.rejected"},{isTransformResponse:!1}),Ce=()=>e.get({url:"/actuator/metrics/undertow.sessions.created"},{isTransformResponse:!1}),be=()=>e.get({url:"/actuator/metrics/undertow.sessions.expired"},{isTransformResponse:!1}),_e=()=>e.get({url:"/actuator/metrics/undertow.sessions.active.current"},{isTransformResponse:!1}),we=()=>e.get({url:"/actuator/metrics/undertow.sessions.active.max"},{isTransformResponse:!1}),Se=()=>e.get({url:"/sys/actuator/memory/info"},{isTransformResponse:!1}),Je=t=>{if(t=="1")return{};if(t=="2")return{"jvm.gc.pause":[".count",".totalTime"]};if(t=="3")return{"tomcat.global.request":[".count",".totalTime"],"tomcat.servlet.request":[".count",".totalTime"]};if(t=="5")return{};if(t=="6")return{}},Be=t=>{if(t=="1")return{"system.cpu.count":{color:"green",text:"CPU 数量",unit:"核"},"system.cpu.usage":{color:"green",text:"系统 CPU 使用率",unit:"%",valueType:"Number"},"process.start.time":{color:"purple",text:"应用启动时间点",unit:"",valueType:"Date"},"process.uptime":{color:"purple",text:"应用已运行时间",unit:"秒"},"process.cpu.usage":{color:"purple",text:"当前应用 CPU 使用率",unit:"%",valueType:"Number"}};if(t=="2")return{"jvm.memory.max":{color:"purple",text:"JVM 最大内存",unit:"MB",valueType:"RAM"},"jvm.memory.committed":{color:"purple",text:"JVM 可用内存",unit:"MB",valueType:"RAM"},"jvm.memory.used":{color:"purple",text:"JVM 已用内存",unit:"MB",valueType:"RAM"},"jvm.buffer.memory.used":{color:"cyan",text:"JVM 缓冲区已用内存",unit:"MB",valueType:"RAM"},"jvm.buffer.count":{color:"cyan",text:"当前缓冲区数量",unit:"个"},"jvm.threads.daemon":{color:"green",text:"JVM 守护线程数量",unit:"个"},"jvm.threads.live":{color:"green",text:"JVM 当前活跃线程数量",unit:"个"},"jvm.threads.peak":{color:"green",text:"JVM 峰值线程数量",unit:"个"},"jvm.classes.loaded":{color:"orange",text:"JVM 已加载 Class 数量",unit:"个"},"jvm.classes.unloaded":{color:"orange",text:"JVM 未加载 Class 数量",unit:"个"},"jvm.gc.memory.allocated":{color:"pink",text:"GC 时, 年轻代分配的内存空间",unit:"MB",valueType:"RAM"},"jvm.gc.memory.promoted":{color:"pink",text:"GC 时, 老年代分配的内存空间",unit:"MB",valueType:"RAM"},"jvm.gc.max.data.size":{color:"pink",text:"GC 时, 老年代的最大内存空间",unit:"MB",valueType:"RAM"},"jvm.gc.live.data.size":{color:"pink",text:"FullGC 时, 老年代的内存空间",unit:"MB",valueType:"RAM"},"jvm.gc.pause.count":{color:"blue",text:"系统启动以来GC 次数",unit:"次"},"jvm.gc.pause.totalTime":{color:"blue",text:"系统启动以来GC 总耗时",unit:"秒"}};if(t=="3")return{"tomcat.sessions.created":{color:"green",text:"tomcat 已创建 session 数",unit:"个"},"tomcat.sessions.expired":{color:"green",text:"tomcat 已过期 session 数",unit:"个"},"tomcat.sessions.active.current":{color:"green",text:"tomcat 当前活跃 session 数",unit:"个"},"tomcat.sessions.active.max":{color:"green",text:"tomcat 活跃 session 数峰值",unit:"个"},"tomcat.sessions.rejected":{color:"green",text:"超过session 最大配置后，拒绝的 session 个数",unit:"个"},"tomcat.global.sent":{color:"purple",text:"发送的字节数",unit:"bytes"},"tomcat.global.request.max":{color:"purple",text:"request 请求最长耗时",unit:"秒"},"tomcat.global.request.count":{color:"purple",text:"全局 request 请求次数",unit:"次"},"tomcat.global.request.totalTime":{color:"purple",text:"全局 request 请求总耗时",unit:"秒"},"tomcat.servlet.request.max":{color:"cyan",text:"servlet 请求最长耗时",unit:"秒"},"tomcat.servlet.request.count":{color:"cyan",text:"servlet 总请求次数",unit:"次"},"tomcat.servlet.request.totalTime":{color:"cyan",text:"servlet 请求总耗时",unit:"秒"},"tomcat.threads.current":{color:"pink",text:"tomcat 当前线程数（包括守护线程）",unit:"个"},"tomcat.threads.config.max":{color:"pink",text:"tomcat 配置的线程最大数",unit:"个"}};if(t=="5")return{"memory.physical.total":{color:"green",text:"总物理内存",unit:"MB",valueType:"RAM"},"memory.physical.used":{color:"green",text:"已使用物理内存",unit:"MB",valueType:"RAM"},"memory.physical.free":{color:"green",text:"可用物理内存",unit:"MB",valueType:"RAM"},"memory.physical.usage":{color:"green",text:"物理内存使用率",unit:"%",valueType:"Number"},"memory.runtime.total":{color:"purple",text:"JVM总内存",unit:"MB",valueType:"RAM"},"memory.runtime.used":{color:"purple",text:"JVM已使用内存",unit:"MB",valueType:"RAM"},"memory.runtime.max":{color:"purple",text:"JVM最大内存",unit:"MB",valueType:"RAM"},"memory.runtime.free":{color:"purple",text:"JVM可用内存",unit:"MB",valueType:"RAM"},"memory.runtime.usage":{color:"purple",text:"JVM内存使用率",unit:"%",valueType:"Number"}};if(t=="6")return{"undertow.sessions.created":{color:"green",text:"undertow 已创建 session 数",unit:"个"},"undertow.sessions.expired":{color:"green",text:"undertow 已过期 session 数",unit:"个"},"undertow.sessions.active.current":{color:"green",text:"undertow 当前活跃 session 数",unit:"个"},"undertow.sessions.active.max":{color:"green",text:"undertow 活跃 session 数峰值",unit:"个"},"undertow.sessions.rejected":{color:"green",text:"超过session 最大配置后，拒绝的 session 个数",unit:"个"}}},Ue=t=>{if(t=="1")return Promise.all([A(),ee(),te(),re(),se()]);if(t=="2")return Promise.all([oe(),ae(),me(),ne(),ce(),ue(),ie(),le(),pe(),de(),xe(),ge(),ve(),fe(),ye()]);if(t=="3")return Promise.all([Te(),Re(),Me(),je(),he()]);if(t=="5")return Promise.all([Se()]);if(t=="6")return Promise.all([_e(),we(),Ce(),be()])},ke=[{title:"参数",dataIndex:"param",width:80,align:"left",slots:{customRender:"param"}},{title:"描述",dataIndex:"text",slots:{customRender:"text"},width:80},{title:"当前值",dataIndex:"value",slots:{customRender:"value"},width:80}],Ve={class:"p-4"},Ge={slot:"message"},Pe=k({name:"monitor-server"}),Ot=k(S(w({},Pe),{setup(t){const s=f([]),r=f("1"),R=f({}),h=f({});let v=f({});const{createMessage:De}=X(),qe=f([]),V={logType:"1"},[G,{reload:Ie}]=Q({columns:ke,showIndexColumn:!1,bordered:!0,pagination:!1,canResize:!1,tableSetting:{fullScreen:!0},rowKey:"id"});function P(o){o!=4&&j(o)}function j(o){h.value=U().format("YYYY年MM月DD日 HH时mm分ss秒"),Ue(o).then(a=>{v.value=Be(o),R.value=Je(o);let n=[];if(o==="5")for(let m in a[0].result){let g=a[0].result[m],p=C(g,l(v)[m].valueType);n.push({id:m,param:m,text:"false value",value:p})}else a.forEach((m,g)=>{let p=l(R)[m.name];p instanceof Array||(p=[""]),p.forEach((T,u)=>{let d=m.name+T,q=C(m.measurements[u].value,l(v)[d].valueType);n.push({id:d+g,param:d,text:"false value",value:q})})});s.value=n})}function D(){j(r.value)}function C(o,a){return a==="Number"?Number(o*100).toFixed(2):a==="Date"?U(o*1e3).format("YYYY-MM-DD HH:mm:ss"):a==="RAM"?Number(o/1048576).toFixed(3):o}return K(()=>{j(r.value)}),(o,a)=>{const n=x("a-tab-pane"),m=x("a-tabs"),g=x("a-divider"),p=x("a-tag"),T=x("a-card");return J(),L("div",Ve,[c(T,{bordered:!1,style:{height:"100%"}},{default:i(()=>[c(m,{activeKey:r.value,"onUpdate:activeKey":a[0]||(a[0]=u=>r.value=u),onChange:P},{default:i(()=>[c(n,{key:"1",tab:"服务器信息"}),c(n,{key:"2",tab:"JVM信息","force-render":""}),c(n,{key:"6",tab:"Undertow信息"}),c(n,{key:"4",tab:"磁盘监控"},{default:i(()=>[r.value==4?(J(),F(W,{key:0,style:{height:"100%"}})):$("",!0)]),_:1}),c(n,{key:"5",tab:"内存信息"})]),_:1},8,["activeKey"]),H(c(l(Z),{onRegister:l(G),searchInfo:V,dataSource:s.value},{tableTitle:i(()=>[B("div",Ge,[M("上次更新时间："+y(h.value)+" ",1),c(g,{type:"vertical"}),B("a",{onClick:D},"立即更新")])]),param:i(({record:u,text:d})=>[c(p,{color:l(v)[u.param].color},{default:i(()=>[M(y(d),1)]),_:2},1032,["color"])]),text:i(({record:u})=>[M(y(l(v)[u.param].text),1)]),value:i(({record:u,text:d})=>[M(y(d)+" "+y(l(v)[u.param].unit),1)]),_:1},8,["onRegister","dataSource"]),[[O,r.value!=4]])]),_:1})])}}}));export{Ot as default};
