var m=(e,t,n)=>new Promise((r,i)=>{var u=a=>{try{p(n.next(a))}catch(s){i(s)}},l=a=>{try{p(n.throw(a))}catch(s){i(s)}},p=a=>a.done?r(a.value):Promise.resolve(a.value).then(u,l);p((n=n.apply(e,t)).next())});import{d as b,f as c,o as y,n as h,ag as k,aq as x,ar as w,at as d,k as V}from"./vue-vendor-dy9k-Yad.js";import{P as f}from"./antd-vue-vendor-me9YkNVC.js";import{bx as o,a as v}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const S=b({name:"JVxeDemo5",components:{[f.name]:f},setup(){const e=c(),t=c([{title:"单行文本",key:"input",type:o.input,width:220,defaultValue:"",placeholder:"请输入${title}"},{title:"多行文本",key:"textarea",type:o.textarea,width:240},{title:"数字",key:"number",type:o.inputNumber,width:120,defaultValue:32},{title:"日期时间",key:"datetime",type:o.datetime,width:240,defaultValue:"2019-04-30 14:51:22",placeholder:"请选择"},{title:"时间",key:"time",type:o.time,width:220,defaultValue:"14:52:22",placeholder:"请选择"},{title:"下拉框",key:"select",type:o.select,width:220,options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}],allowSearch:!0,placeholder:"请选择"},{title:"复选框",key:"checkbox",type:o.checkbox,customValue:["Y","N"],defaultChecked:!1}]),n=c([]);function r(u){}function i({row:u}){var l;(l=e.value)==null||l.removeRows(u)}return y(()=>m(null,null,function*(){yield h(),e.value.addRows([{input:"input_1"},{input:"input_2"},{input:"input_3"},{input:"input_4"},{input:"input_5"}],{setActive:!1})})),{tableRef:e,columns:t,dataSource:n,handleView:r,handleDelete:i}}});function _(e,t,n,r,i,u){const l=k("JVxeTable");return w(),x("div",null,[t[0]||(t[0]=d("b",null,"键盘操作快捷键：",-1)),t[1]||(t[1]=d("div",{style:{border:"1px solid #cccccc",padding:"8px",width:"740px"}},[d("pre",null,`             F2 | 如果存在，激活单元格为编辑状态
            Esc | 如果存在，取消单元格编辑状态
              ↑ | 如果存在，则移动到上面的单元格
              ↓ | 如果存在，则移动到下面的单元格
              ← | 如果存在，则移动到左边的单元格
              → | 如果存在，则移动到右边的单元格
            Tab | 如果存在，则移动到右边单元格；如果移动到最后一列，则从下一行开始移到，以此循环
    Shift + Tab | 如果存在，则移动到左边单元格，如果移动到第一列，则从上一行开始移到，以此循环
          Enter | 如果存在，取消单元格编辑并移动到下面的单元格
  Shift + Enter | 如果存在，取消单元格编辑并移动到上面的单元格`)],-1)),V(l,{ref:"tableRef",stripe:"",toolbar:"",rowNumber:"",rowSelection:"",keyboardEdit:"",columns:e.columns,dataSource:e.dataSource},null,8,["columns","dataSource"])])}const $=v(S,[["render",_]]);export{$ as default};
