import{Q as xi,h as Fi}from"./componentMap-Bkie1n3v.js";import{cs as Ee,cu as Ti,R as vt,k as Ot,cv as Oi,ah as ki,cw as at,j as me,bD as Ii,ap as kt,u as Re,bj as It,ad as Pe,aZ as Pt,cx as Pi,ac as Dt,f as Rt,d as Ve,x as Di,H as se,cy as Ri,cz as Mi,aM as yt,cA as Ei,cB as Bi,aL as ji}from"./index-CCWaWN5g.js";import{e as pe,w as ge,u as Ae,f as I,r as Ce,J as be,n as Ne,ag as le,aB as ke,ar as ie,aq as de,as as Ue,k as ce,d as Mt,l as _e,aE as Et,aD as ae,G as Fe,ah as Te,au as Oe,h as _i,c as Qi,o as Bt,j as Ni,at as fe,F as He,aC as lt,ap as Ji,ak as Li,aL as $i}from"./vue-vendor-dy9k-Yad.js";import{p as Ye,aw as Vi,c6 as Ui,c7 as Hi,B as Ze,bj as Yi,aO as rt,aU as qi}from"./antd-vue-vendor-me9YkNVC.js";import{B as jt}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{o as ot}from"./constant-fa63bd66-Ddbq-fz2.js";import"./index-B4ez5KWV.js";import{d as Wi}from"./user.api-mLAlJze4.js";import{$ as _t}from"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import{useListPage as Ki}from"./useListPage-Soxgnx9a.js";import zi from"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import Xi from"./OnlineSelectCascade-d631ed72-DF6fP885.js";import Gi from"./JModalTip-a927f85d-DAi05z-f.js";import{U as Zi}from"./JUpload-CRos0F1P.js";import{u as Qt}from"./useForm-CgkFTrrO.js";import{B as Nt}from"./BasicForm-DBcXiHk0.js";import en from"./BasicTable-xCEZpGLb.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const qe="data:image/png;base64,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";var tn=Object.defineProperty,nn=Object.defineProperties,ln=Object.getOwnPropertyDescriptors,At=Object.getOwnPropertySymbols,on=Object.prototype.hasOwnProperty,sn=Object.prototype.propertyIsEnumerable,Ct=(i,e,t)=>e in i?tn(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,he=(i,e)=>{for(var t in e||(e={}))on.call(e,t)&&Ct(i,t,e[t]);if(At)for(var t of At(e))sn.call(e,t)&&Ct(i,t,e[t]);return i},Le=(i,e)=>nn(i,ln(e)),K=(i,e,t)=>new Promise((n,u)=>{var s=p=>{try{y(t.next(p))}catch(A){u(A)}},c=p=>{try{y(t.throw(p))}catch(A){u(A)}},y=p=>p.done?n(p.value):Promise.resolve(p.value).then(s,c);y((t=t.apply(i,e)).next())});const an="jeecg_submit_form_and_flow",cs="flow_submit_id",rn="online_form_table_name",je="validate-failed",ps="setup",hs="EnhanceJS",fs={password:"text",file:"text",image:"text",textarea:"text",umeditor:"text",markdown:"text",checkbox:"list_multi",radio:"list"},un=".jeecg-online-modal .ant-modal-content",dn="online_";class Z{constructor(e,t){this._data=t,this.field=e,this.label=t.title,this.hidden=!1,this.order=t.order||999,this.required=!1,this.onlyValidator="",this.setFieldsValue="",this.hasChange=!0,e.indexOf("@")>0?this.pre=e.substring(0,e.indexOf("@")+1):this.pre="",this.schemaProp={},this.searchForm=!1,this.disabled=!1,this.popContainer="",this.handleWidgetAttr(t),this.inPopover=!1,this.labelLength=ot,this.initLabelLength()}getFormItemSchema(){let e=this.getItem();return this.addDefaultChangeEvent(e),e}getItem(){let e={field:this.field,label:this.label,labelLength:this.labelLength,component:"Input",itemProps:{labelCol:{class:"online-form-label"}}},t=this.getRule();return t.length>0&&this.onlyValidator&&(e.rules=t),this.hidden===!0&&(e.show=!1),e}setFormRef(e){this.formRef=e}isHidden(){return this.hidden=!0,this}isRequired(e){return e&&e.length>0&&e.indexOf(this.field)>=0&&(this.required=!0),this}initLabelLength(){let e=this.getExtendData();e&&e.labelLength&&(this.labelLength=e.labelLength)}getExtendData(){let e={},{fieldExtendJson:t}=this._data;if(t&&typeof t=="string")try{let n=JSON.parse(t);e=he({},n)}catch(n){}return e}getRelatedHideFields(){return[]}getPlaceholder(e){let t="请输入";return["list","radio","checkbox","date","datetime","time","list_multi","sel_search","popup","cat_tree","sel_depart","sel_user","pca","link_down","sel_tree","switch","link_table","link_table_field","popup_dict","LinkTableForQuery","CascaderPcaForQuery","select_user2","rangeDate","rangeTime","rangeNumber"].includes(e)?t="请选择":["file","image"].includes(e)&&(t="请上传"),t+this.label}setOnlyValidateFun(e){e&&(this.onlyValidator=(t,n)=>K(this,null,function*(){let u=yield e(t,n);return u?Promise.reject(u):Promise.resolve()}))}getRule(){let e=[];const{view:t,errorInfo:n,pattern:u,type:s,fieldExtendJson:c}=this._data;if(this.required===!0){let y=this.getPlaceholder(t);if(c){const p=JSON.parse(c);p.validateError&&(y=p.validateError)}n&&(y=n),t=="sel_depart"||t=="sel_user"?(this.schemaProp.required=!0,e.push({required:!0,message:y})):e.push({required:!0,message:y})}if(t=="sel_user"&&u==="only"&&this.onlyValidator&&e.push({validator:this.onlyValidator}),t==="list"||t==="radio"||t==="markdown"||t==="pca"||t.indexOf("sel")>=0||t==="time"||t.indexOf("upload")>=0||t.indexOf("file")>=0||t.indexOf("image")>=0)return e;if(u)if(u==="only")this.onlyValidator&&e.push({validator:this.onlyValidator});else if(u==="z")s=="number"||s=="integer"||e.push({pattern:/^-?\d+$/,message:"请输入整数"});else{let y=n||"正则校验失败",p;try{p=new RegExp(u),p||(p=u)}catch(A){p=u}e.push({pattern:p,message:y})}return e}addDefaultChangeEvent(e){this.hasChange&&(e.componentProps||(e.componentProps={}),this.disabled==!0&&(e.componentProps.disabled=!0),e.componentProps.hasOwnProperty("onChange")||(e.componentProps.onChange=(t,n)=>{t instanceof Event&&(t=t.target.value),t instanceof Array&&(t=t.join(",")),!this.formRef||!this.formRef.value||!this.formRef.value.$formValueChange||this.formRef.value.$formValueChange(this.field,t,n)})),Object.keys(this.schemaProp).map(t=>{e[t]=this.schemaProp[t]})}noChange(){this.hasChange=!1}updateField(e){this.field=e}setFunctionForFieldValue(e){e&&(this.setFieldsValue=e)}asSearchForm(){this.searchForm=!0}getModalAsContainer(){let e=this.getPopContainer();if(e!="body"){const t=document.querySelectorAll(e);if(t&&t.length>1){const n=[];if(t.forEach(u=>{u.offsetWidth==0&&u.offsetHeight==0||n.push(u)}),n.length===1)return n[0]}}return document.querySelector(e)}getPopContainer(){return this.searchForm===!0?"body":this.inPopover===!0?`.${this.popContainer}`:this.popContainer?`.${this.popContainer} .ant-modal-content`:un}handleWidgetAttr(e){e.ui&&e.ui.widgetattrs&&e.ui.widgetattrs.disabled==!0&&(this.disabled=!0)}setCustomPopContainer(e){this.popContainer=e}getLinkFieldInfo(){return""}setOtherInfo(e){}isInPopover(){this.inPopover=!0}handleDictTableParams(){if(!this.formRef.value)return;const e=this._data.dictTable;if(!e)return;const t=e.match(/\${([^}]+)}/g);if(!t||t.length==0)return;const n=t.map(c=>c.replace("${","").replace("}","")),u=pe(()=>{const c=this.formRef.value.formModel;return n.map(y=>c[y]).join("")});let s=null;ge(u,()=>{s&&clearTimeout(s),s=setTimeout(()=>{const c=this.formRef.value.formModel;let y=e.replace(/\${([^}]+)}/g,(p,A)=>c[A]==null?"":c[A]);this.updateDictTable(y)},150)},{immediate:!0})}updateDictTable(e){}genDictTableCode(e,t,n){return e=Ti(e),encodeURI(`${e},${t},${n}`)}}class wt extends Z{getItem(){let e=super.getItem();return this.hidden===!0&&(e.show=!1),e}}var Jt=(i=>(i.datetime="YYYY-MM-DD HH:mm:ss",i.date="YYYY-MM-DD",i))(Jt||{});class cn extends Z{constructor(e,t,n){super(e,t),this.format=Jt[t.view],this.showTime=t.view!="date";let u=t.fieldExtendJson;t.view=="date"&&u&&(u=JSON.parse(u),u.picker&&u.picker!="default"?this.picker=u.picker:this.picker=void 0),this.allowSelectRange=["eq","ne"].includes(n==null?void 0:n.rule)}getItem(){let e=super.getItem();return Object.assign({},e,{component:"DatePickerInFilter",componentProps:{placeholder:`请选择${this.label}`,showTime:this.showTime,valueFormat:this.format,allowSelectRange:this.allowSelectRange,picker:this.picker,style:{width:"100%"},getPopupContainer:t=>this.getModalAsContainer()}})}}class pn extends Z{constructor(e,t){super(e,t),this.schema=t,this.options=this.getOptions(t.enum,t.type),this.dictTable=t.dictTable,this.dictText=t.dictText,this.dictCode=t.dictCode,this.multi=t.multi||!1}getItem(){let e=super.getItem(),t=this.getFormComponent(),n=this.getComponentProps();return Object.assign({},e,{component:t,componentProps:n})}getFormComponent(){return this.options.length>0?"Select":"JDictSelectTag"}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{dictCode:this.genDictTableCode(e,this.dictText,this.dictCode)}})}getComponentProps(){let e={allowClear:!0,mode:this.multi===!0?"multiple":"combobox",style:{width:"100%"},getPopupContainer:t=>this.getModalAsContainer(),onDropdownVisibleChange:t=>{t&&typeof this.schema.updateOptions=="function"&&this.schema.updateOptions()}};return this.options.length>0?e.options=this.options:this.dictTable?e.dictCode=this.genDictTableCode(this.dictTable,this.dictText,this.dictCode):(e.dictCode=this.dictCode,e.useDicColor=!0),e}getOptions(e,t){if(!e||e.length==0)return[];let n=t=="number",u=[];for(let s of e){if(s==null)break;let c=s.value;n&&(c=parseInt(c)),u.push(Le(he({},s),{value:c,label:s.title}))}return u}}class hn extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"InputPassword"})}}class fn extends Z{getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JUpload",componentProps:t})}getComponentProps(){let e=this.getExtendData();return e&&e.uploadnum?{maxCount:Number(e.uploadnum)}:{}}}class mn extends Z{getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JUpload",componentProps:t})}getComponentProps(){let e={fileType:Zi.image},t=this.getExtendData();return t&&t.uploadnum&&(e.maxCount=Number(t.uploadnum)),e}}class gn extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"InputTextArea",componentProps:{autoSize:{minRows:4,maxRows:10}}})}}class bn extends Z{constructor(e,t){super(e,t),this.dictTable=t.dictTable,this.dictText=t.dictText,this.dictCode=t.dictCode}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{dictCode:this.genDictTableCode(e,this.dictText,this.dictCode)}})}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JSelectMultiple",componentProps:t})}getComponentProps(){if(!this.dictTable&&!this.dictCode)return{};{let e={};return this.dictTable?e.dictCode=this.genDictTableCode(this.dictTable,this.dictText,this.dictCode):(e.dictCode=this.dictCode,e.useDicColor=!0),e.triggerChange=!0,e.popContainer=this.getPopContainer(),e}}}class vn extends Z{constructor(e,t){super(e,t),t.dictTable&&t.dictText&&t.dictCode?(this.dict=this.genDictTableCode(t.dictTable,t.dictText,t.dictCode),this.type=1):(this.dict=encodeURI(`${t.dictCode}`),this.type=0)}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{dict:this.genDictTableCode(e,this._data.dictText,this._data.dictCode)}})}getItem(){let e=super.getItem(),t=this.getPopContainer();return Object.assign({},e,{component:"JSearchSelect",componentProps:{dict:this.dict,pageSize:10,async:!!this.type,popContainer:t}})}}class yn extends Z{constructor(e,t){super(e,t),this.code=t.code,this.multi=t.popupMulti,this.fieldConfig=this.getFieldConfig(t)}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JPopup",componentProps:t})}getComponentProps(){let e={code:this.code,multi:this.multi,fieldConfig:this.fieldConfig};return this.formRef?e.formElRef=this.formRef:e.setFieldsValue=this.setFieldsValue,this.inPopover===!0&&(e.getContainer=()=>this.getModalAsContainer()),e.getFormValues=()=>Ae(this.formRef).getFieldsValue(),e}getFieldConfig(e){let{destFields:t,orgFields:n,dictText:u}=e;if(!t||t.length==0)return[];let s=t.split(","),c=n.split(","),y=u?u.split(","):null,p=[];const A=this.pre;for(let T=0;T<s.length;T++)p.push({target:A+s[T],source:c[T],label:y?y[T]:void 0});return p}}class An extends Z{constructor(e,t){super(e,t),this.dictCode=`${t.code},${t.destFields},${t.orgFields}`,this.multi=t.popupMulti}getItem(){const e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JPopupDict",componentProps:t})}getComponentProps(){const e={dictCode:this.dictCode,multi:this.multi};return this.inPopover&&(e.getContainer=()=>this.getModalAsContainer()),e.getFormValues=()=>Ae(this.formRef).getFieldsValue(),e}}class Cn extends Z{constructor(e,t){super(e,t),this.multi=!1,this.pid=t.pidValue,this.pcode=t.pcode,this.textField=t.textField}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{componentProps:t,component:"JCategorySelect"})}getComponentProps(){let e={placeholder:"请选择"+this.label};if(this.pcode)e.pcode=this.pcode;else{let t=this.pid||"EMPTY_PID";e.pid=t}return this.textField?Le(he({loadTriggleChange:!0,multiple:this.multi},e),{back:this.textField,onChange:(t,n)=>{this.formRef&&(this.formRef.value.setFieldsValue(n),this.formRef.value.$formValueChange(this.field,t))}}):he({multiple:this.multi},e)}getRelatedHideFields(){let e=[];return this.textField&&e.push(this.textField),e}}class wn extends Z{getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JSelectDept",componentProps:t})}getComponentProps(){let e=this.getExtendData(),t={checkStrictly:!0,showButton:!1};return e.text&&(t.labelKey=e.text),e.store&&(t.rowKey=e.store),e.multiSelect===!1&&(t.multiple=!1),e.multiSelect===!0&&(t.multiple=!0),t.maxTagCount=3,this.inPopover===!0&&(t.getContainer=()=>this.getModalAsContainer()),t}}class Sn extends Z{constructor(e,t){super(e,t),this.showButton=t.showButton!==!1}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JSelectUser",componentProps:t})}getComponentProps(){let e=this.getExtendData(),t={showSelected:!1,allowClear:!0,isRadioSelection:!1,showButton:this.showButton};return e.text&&(t.labelKey=e.text),e.store&&(t.rowKey=e.store),e.multiSelect===!1&&(t.isRadioSelection=!0),t.maxTagCount=3,this.inPopover===!0&&(t.getContainer=()=>this.getModalAsContainer()),t}}class xn extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"JEditor",componentProps:{options:{auto_focus:!1}}})}}class Fn extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"JMarkdownEditor",componentProps:{}})}}class Tn extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"JAreaLinkage",componentProps:{saveCode:"region",getPopupContainer:()=>document.querySelector("body")}})}}class On extends Z{constructor(e,t){super(e,t),this.dict=t.dict,this.pidField=t.pidField,this.pidValue=t.pidValue,this.hasChildField=t.hasChildField}getItem(){let e=super.getItem();return Object.assign({},e,{component:"JTreeSelect",componentProps:{dict:this.dict,pidField:this.pidField,pidValue:this.pidValue,hasChildField:this.hasChildField}})}}class kn extends Z{constructor(e,t){super(e,t),this.dictTable=t.dictTable,this.dictText=t.dictText,this.dictCode=t.dictCode}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{dictCode:this.genDictTableCode(e,this.dictText,this.dictCode)}})}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JDictSelectTag",componentProps:t})}getComponentProps(){return!this.dictTable&&!this.dictCode?{}:this.dictTable?{dictCode:this.genDictTableCode(this.dictTable,this.dictText,this.dictCode),type:"radio"}:{useDicColor:!0,dictCode:this.dictCode,type:"radio"}}}class In extends Z{constructor(e,t){super(e,t),this.options=this.getOptions(t.enum)}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{options:[],dictCode:this.genDictTableCode(e,this._data.dictText,this._data.dictCode)}})}getItem(){let e=super.getItem();return Object.assign({},e,{component:"JCheckbox",componentProps:{options:this.options,triggerChange:!0,useDicColor:!0}})}getOptions(e){if(!e||e.length==0)return[];let t=[];for(let n of e)t.push({value:n.value,label:n.title,color:n.color});return t}}class Pn extends Z{constructor(e,t){super(e,t)}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JSwitch",componentProps:t})}getComponentProps(){let{fieldExtendJson:e}=this._data,t=["Y","N"];if(e&&typeof e=="string"){const n=JSON.parse(e);vt(n)&&n.length==2?t=n:Ot(n)&&vt(n.switchOptions)&&(t=n.switchOptions)}return{options:t}}}class Dn extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"TimePicker",componentProps:{placeholder:`请选择${this.label}`,valueFormat:"HH:mm:ss",getPopupContainer:t=>this.getModalAsContainer(),style:{width:"100%"}}})}}class Rn extends Z{constructor(e,t){super(e,t);const{dictTable:n,dictText:u,dictCode:s,pidField:c,idField:y,origin:p,condition:A}=t;this.table=n,this.txt=u,this.store=s,this.idField=y,this.pidField=c,this.origin=p,this.condition=A,this.options=[],this.next=t.next||"",this.type=t.type}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"OnlineSelectCascade",componentProps:t})}getComponentProps(){let e={table:this.table,txt:this.txt,store:this.store,pidField:this.pidField,idField:this.idField,origin:this.origin,pidValue:"-1",style:{width:"100%"},onChange:t=>{this.valueChange(t)},onNext:t=>{this.nextOptionsChange(t)}};return this._data.origin===!0&&(e.condition=this.condition),e}nextOptionsChange(e){return K(this,null,function*(){!this.formRef||!this.next||(yield this.formRef.value.updateSchema({field:this.next,componentProps:{pidValue:e}}))})}valueChange(e){return K(this,null,function*(){if(!this.formRef)return;let t=this.formRef.value;t.$formValueChange(this.field,e),this.next&&(yield t.setFieldsValue({[this.next]:""}))})}}class St extends Z{constructor(e,t){super(e,t),this.slot="";let n=t.fieldExtendJson;t.view=="date"&&n&&(n=JSON.parse(n),n.picker&&n.picker!="default"?this.picker=n.picker:this.picker=void 0),this.precision=t.dbPointLength}getItem(){let e=super.getItem(),t=this.slot;const n={};return this.picker&&(n.picker=this.picker),this.precision&&(n.precision=this.precision),Object.assign({},e,{slot:t,componentProps:n})}groupDate(){return this.slot="groupDate",this}groupDatetime(){return this.slot="groupDatetime",this}groupTime(){return this.slot="groupTime",this}groupNumber(){return this.slot="groupNumber",this}}class Mn extends Z{constructor(e,t){super(e,t),this.dbPointLength=t.dbPointLength}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"InputNumber",componentProps:t})}getComponentProps(){const e={style:{width:"100%"}};return this.dbPointLength>=0&&(e.precision=this.dbPointLength),e}}class En extends Z{constructor(e,t){super(e,t),this.dictTable=t.dictTable,this.dictText=t.dictText,this.dictCode=t.dictCode,this.view=t.view,this.componentString="",this.linkFields=[]}getItem(){let e=super.getItem();const t=this.getComponentProps();return Object.assign({},e,{component:this.componentString,componentProps:t})}getComponentProps(){let e={textField:this.dictText,tableName:this.dictTable,valueField:this.dictCode},t=this.getExtendData();if(t.multiSelect?e.multi=!0:e.multi=!1,t.imageField?e.imageField=t.imageField:e.imageField="",t.showType=="select"){this.componentString="LinkTableSelect";let n=this.getPopContainer();e.popContainer=n}else this.componentString="LinkTableCard";return this.linkFields.length>0&&(e.linkFields=this.linkFields),e}setOtherInfo(e){this.linkFields=e}}class Bn extends Z{constructor(e,t){super(e,t),this.dictTable=t.dictTable,this.dictText=t.dictText}getItem(){let e=super.getItem();return Object.assign({},e,{componentProps:{readOnly:!0,allowClear:!1,disabled:!0,style:{background:"none",color:"rgba(0, 0, 0, 0.85)",border:"none"}}})}getLinkFieldInfo(){return[this.dictTable,`${this.field},${this.dictText}`]}}class jn extends Z{constructor(e,t){super(e,t),this.code=t.code,this.titleField=t.titleField,this.multi=t.multi||!1}getItem(){let e=super.getItem();return Object.assign({},e,{component:"LinkTableForQuery",componentProps:{code:this.code,multi:this.multi,field:this.titleField,style:{width:"100%"}}})}}class _n extends Z{constructor(e,t,n){var u;super(e,t),this.schema=t,this.areaLevel=(u=t.areaLevel)!=null?u:3,this.allowChangeLevel=["eq","ne"].includes(n==null?void 0:n.rule)}getItem(){let e=super.getItem();return Object.assign({},e,{component:"CascaderPcaInFilter",componentProps:{areaLevel:this.areaLevel,allowChangeLevel:this.allowChangeLevel,placeholder:"请选择…",style:{width:"100%"}}})}}class Qn extends Z{constructor(e,t){super(e,t),this.multi=t.multi===!0,this.store=t.store||"",this.query=t.query||!1}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"UserSelect",componentProps:t})}getComponentProps(){let e={multi:this.multi,store:this.store,query:this.query};return this.inPopover===!0&&(e.getContainer=()=>this.getModalAsContainer()),e}}class Nn extends Z{constructor(e,t){super(e,t);let n=t.view;this.format=t.format,this.datetime=!1,n==="rangeNumber"?this.componentType="JRangeNumber":n==="rangeTime"?this.componentType="RangeTime":(this.componentType="RangeDate",t.datetime===!0&&(this.datetime=!0))}getItem(){let e=super.getItem();return Object.assign({},e,{component:this.componentType,componentProps:{datetime:this.datetime,format:this.format,getPopupContainer:t=>this.getModalAsContainer()}})}}class et{static createFormSchema(e,t,n){switch(t.view){case"password":return new hn(e,t);case"list":return new pn(e,t);case"radio":return new kn(e,t);case"checkbox":return new In(e,t);case"date":case"datetime":return new cn(e,t,n);case"time":return new Dn(e,t);case"file":return new fn(e,t);case"image":return new mn(e,t);case"textarea":return new gn(e,t);case"list_multi":return new bn(e,t);case"sel_search":return new vn(e,t);case"popup":return new yn(e,t);case"cat_tree":return new Cn(e,t);case"sel_depart":return new wn(e,t);case"sel_user":return new Sn(e,t);case"umeditor":return new xn(e,t);case"markdown":return new Fn(e,t);case"pca":return new Tn(e,t);case"link_down":return new Rn(e,t);case"sel_tree":return new On(e,t);case"switch":return new Pn(e,t);case"link_table":return new En(e,t);case"link_table_field":return new Bn(e,t);case"popup_dict":return new An(e,t);case"slot":return new St(e,t);case"LinkTableForQuery":return new jn(e,t);case"CascaderPcaForQuery":return new _n(e,t,n);case"select_user2":return new Qn(e,t);case"rangeDate":case"rangeTime":case"rangeNumber":return new Nn(e,t);case"hidden":return new wt(e,t).isHidden();default:return t.type=="number"?new Mn(e,t):new wt(e,t)}}static createSlotFormSchema(e,t){let n=new St(e,t),u=t.view;if(u=="date")n.groupDate();else if(u=="datetime")n.groupDatetime();else if(u=="time")n.groupTime();else{let s=t.type;(s=="number"||s=="integer")&&n.groupNumber()}return n}static createIdField(){return{label:"",field:"id",component:"Input",show:!1}}}var ut=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function dt(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Lt={exports:{}};(function(i,e){(function(t,n){i.exports=n()})(ut,function(){var t=1e3,n=6e4,u=36e5,s="millisecond",c="second",y="minute",p="hour",A="day",T="week",b="month",O="quarter",C="year",N="date",E="Invalid Date",H=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,J=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,B={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(d){var l=["th","st","nd","rd"],r=d%100;return"["+d+(l[(r-20)%10]||l[r]||l[0])+"]"}},j=function(d,l,r){var a=String(d);return!a||a.length>=l?d:""+Array(l+1-a.length).join(r)+d},V={s:j,z:function(d){var l=-d.utcOffset(),r=Math.abs(l),a=Math.floor(r/60),o=r%60;return(l<=0?"+":"-")+j(a,2,"0")+":"+j(o,2,"0")},m:function d(l,r){if(l.date()<r.date())return-d(r,l);var a=12*(r.year()-l.year())+(r.month()-l.month()),o=l.clone().add(a,b),m=r-o<0,v=l.clone().add(a+(m?-1:1),b);return+(-(a+(r-o)/(m?o-v:v-o))||0)},a:function(d){return d<0?Math.ceil(d)||0:Math.floor(d)},p:function(d){return{M:b,y:C,w:T,d:A,D:N,h:p,m:y,s:c,ms:s,Q:O}[d]||String(d||"").toLowerCase().replace(/s$/,"")},u:function(d){return d===void 0}},z="en",q={};q[z]=B;var X="$isDayjsObject",x=function(d){return d instanceof P||!(!d||!d[X])},k=function d(l,r,a){var o;if(!l)return z;if(typeof l=="string"){var m=l.toLowerCase();q[m]&&(o=m),r&&(q[m]=r,o=m);var v=l.split("-");if(!o&&v.length>1)return d(v[0])}else{var F=l.name;q[F]=l,o=F}return!a&&o&&(z=o),o||!a&&z},w=function(d,l){if(x(d))return d.clone();var r=typeof l=="object"?l:{};return r.date=d,r.args=arguments,new P(r)},S=V;S.l=k,S.i=x,S.w=function(d,l){return w(d,{locale:l.$L,utc:l.$u,x:l.$x,$offset:l.$offset})};var P=function(){function d(r){this.$L=k(r.locale,null,!0),this.parse(r),this.$x=this.$x||r.x||{},this[X]=!0}var l=d.prototype;return l.parse=function(r){this.$d=function(a){var o=a.date,m=a.utc;if(o===null)return new Date(NaN);if(S.u(o))return new Date;if(o instanceof Date)return new Date(o);if(typeof o=="string"&&!/Z$/i.test(o)){var v=o.match(H);if(v){var F=v[2]-1||0,h=(v[7]||"0").substring(0,3);return m?new Date(Date.UTC(v[1],F,v[3]||1,v[4]||0,v[5]||0,v[6]||0,h)):new Date(v[1],F,v[3]||1,v[4]||0,v[5]||0,v[6]||0,h)}}return new Date(o)}(r),this.init()},l.init=function(){var r=this.$d;this.$y=r.getFullYear(),this.$M=r.getMonth(),this.$D=r.getDate(),this.$W=r.getDay(),this.$H=r.getHours(),this.$m=r.getMinutes(),this.$s=r.getSeconds(),this.$ms=r.getMilliseconds()},l.$utils=function(){return S},l.isValid=function(){return this.$d.toString()!==E},l.isSame=function(r,a){var o=w(r);return this.startOf(a)<=o&&o<=this.endOf(a)},l.isAfter=function(r,a){return w(r)<this.startOf(a)},l.isBefore=function(r,a){return this.endOf(a)<w(r)},l.$g=function(r,a,o){return S.u(r)?this[a]:this.set(o,r)},l.unix=function(){return Math.floor(this.valueOf()/1e3)},l.valueOf=function(){return this.$d.getTime()},l.startOf=function(r,a){var o=this,m=!!S.u(a)||a,v=S.p(r),F=function(W,G){var te=S.w(o.$u?Date.UTC(o.$y,G,W):new Date(o.$y,G,W),o);return m?te:te.endOf(A)},h=function(W,G){return S.w(o.toDate()[W].apply(o.toDate("s"),(m?[0,0,0,0]:[23,59,59,999]).slice(G)),o)},R=this.$W,D=this.$M,U=this.$D,L="set"+(this.$u?"UTC":"");switch(v){case C:return m?F(1,0):F(31,11);case b:return m?F(1,D):F(0,D+1);case T:var ee=this.$locale().weekStart||0,Q=(R<ee?R+7:R)-ee;return F(m?U-Q:U+(6-Q),D);case A:case N:return h(L+"Hours",0);case p:return h(L+"Minutes",1);case y:return h(L+"Seconds",2);case c:return h(L+"Milliseconds",3);default:return this.clone()}},l.endOf=function(r){return this.startOf(r,!1)},l.$set=function(r,a){var o,m=S.p(r),v="set"+(this.$u?"UTC":""),F=(o={},o[A]=v+"Date",o[N]=v+"Date",o[b]=v+"Month",o[C]=v+"FullYear",o[p]=v+"Hours",o[y]=v+"Minutes",o[c]=v+"Seconds",o[s]=v+"Milliseconds",o)[m],h=m===A?this.$D+(a-this.$W):a;if(m===b||m===C){var R=this.clone().set(N,1);R.$d[F](h),R.init(),this.$d=R.set(N,Math.min(this.$D,R.daysInMonth())).$d}else F&&this.$d[F](h);return this.init(),this},l.set=function(r,a){return this.clone().$set(r,a)},l.get=function(r){return this[S.p(r)]()},l.add=function(r,a){var o,m=this;r=Number(r);var v=S.p(a),F=function(D){var U=w(m);return S.w(U.date(U.date()+Math.round(D*r)),m)};if(v===b)return this.set(b,this.$M+r);if(v===C)return this.set(C,this.$y+r);if(v===A)return F(1);if(v===T)return F(7);var h=(o={},o[y]=n,o[p]=u,o[c]=t,o)[v]||1,R=this.$d.getTime()+r*h;return S.w(R,this)},l.subtract=function(r,a){return this.add(-1*r,a)},l.format=function(r){var a=this,o=this.$locale();if(!this.isValid())return o.invalidDate||E;var m=r||"YYYY-MM-DDTHH:mm:ssZ",v=S.z(this),F=this.$H,h=this.$m,R=this.$M,D=o.weekdays,U=o.months,L=o.meridiem,ee=function(G,te,ne,we){return G&&(G[te]||G(a,m))||ne[te].slice(0,we)},Q=function(G){return S.s(F%12||12,G,"0")},W=L||function(G,te,ne){var we=G<12?"AM":"PM";return ne?we.toLowerCase():we};return m.replace(J,function(G,te){return te||function(ne){switch(ne){case"YY":return String(a.$y).slice(-2);case"YYYY":return S.s(a.$y,4,"0");case"M":return R+1;case"MM":return S.s(R+1,2,"0");case"MMM":return ee(o.monthsShort,R,U,3);case"MMMM":return ee(U,R);case"D":return a.$D;case"DD":return S.s(a.$D,2,"0");case"d":return String(a.$W);case"dd":return ee(o.weekdaysMin,a.$W,D,2);case"ddd":return ee(o.weekdaysShort,a.$W,D,3);case"dddd":return D[a.$W];case"H":return String(F);case"HH":return S.s(F,2,"0");case"h":return Q(1);case"hh":return Q(2);case"a":return W(F,h,!0);case"A":return W(F,h,!1);case"m":return String(h);case"mm":return S.s(h,2,"0");case"s":return String(a.$s);case"ss":return S.s(a.$s,2,"0");case"SSS":return S.s(a.$ms,3,"0");case"Z":return v}return null}(G)||v.replace(":","")})},l.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},l.diff=function(r,a,o){var m,v=this,F=S.p(a),h=w(r),R=(h.utcOffset()-this.utcOffset())*n,D=this-h,U=function(){return S.m(v,h)};switch(F){case C:m=U()/12;break;case b:m=U();break;case O:m=U()/3;break;case T:m=(D-R)/6048e5;break;case A:m=(D-R)/864e5;break;case p:m=D/u;break;case y:m=D/n;break;case c:m=D/t;break;default:m=D}return o?m:S.a(m)},l.daysInMonth=function(){return this.endOf(b).$D},l.$locale=function(){return q[this.$L]},l.locale=function(r,a){if(!r)return this.$L;var o=this.clone(),m=k(r,a,!0);return m&&(o.$L=m),o},l.clone=function(){return S.w(this.$d,this)},l.toDate=function(){return new Date(this.valueOf())},l.toJSON=function(){return this.isValid()?this.toISOString():null},l.toISOString=function(){return this.$d.toISOString()},l.toString=function(){return this.$d.toUTCString()},d}(),_=P.prototype;return w.prototype=_,[["$ms",s],["$s",c],["$m",y],["$H",p],["$W",A],["$M",b],["$y",C],["$D",N]].forEach(function(d){_[d[1]]=function(l){return this.$g(l,d[0],d[1])}}),w.extend=function(d,l){return d.$i||(d(l,P,w),d.$i=!0),w},w.locale=k,w.isDayjs=x,w.unix=function(d){return w(1e3*d)},w.en=q[z],w.Ls=q,w.p={},w})})(Lt);var Jn=Lt.exports;const ye=dt(Jn);var $t={exports:{}};(function(i,e){(function(t,n){i.exports=n()})(ut,function(){var t="week",n="year";return function(u,s,c){var y=s.prototype;y.week=function(p){if(p===void 0&&(p=null),p!==null)return this.add(7*(p-this.week()),"day");var A=this.$locale().yearStart||1;if(this.month()===11&&this.date()>25){var T=c(this).startOf(n).add(1,n).date(A),b=c(this).endOf(t);if(T.isBefore(b))return 1}var O=c(this).startOf(n).date(A).startOf(t).subtract(1,"millisecond"),C=this.diff(O,t,!0);return C<0?c(this).startOf("week").week():Math.ceil(C)},y.weeks=function(p){return p===void 0&&(p=null),this.week(p)}}})})($t);var Ln=$t.exports;const $n=dt(Ln);var Vt={exports:{}};(function(i,e){(function(t,n){i.exports=n()})(ut,function(){var t="month",n="quarter";return function(u,s){var c=s.prototype;c.quarter=function(A){return this.$utils().u(A)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(A-1))};var y=c.add;c.add=function(A,T){return A=Number(A),this.$utils().p(T)===n?this.add(3*A,t):y.bind(this)(A,T)};var p=c.startOf;c.startOf=function(A,T){var b=this.$utils(),O=!!b.u(T)||T;if(b.p(A)===n){var C=this.quarter()-1;return O?this.month(3*C).startOf(t).startOf("day"):this.month(3*C+2).endOf(t).endOf("day")}return p.bind(this)(A,T)}}})})(Vt);var Vn=Vt.exports;const Un=dt(Vn);ye.extend($n);ye.extend(Un);const Ut=Object.keys(_t),Hn=Ut.join(","),Yn=Ut.map(i=>_t[i]),Ht=/#{([^}]+)?}/g,Yt=/{{([^}]+)?}}/g,Je=/\${([^}]+)?}/g,Qe={ADD:"add",EDIT:"edit",DETAIL:"detail",RELOAD:"reload"};function tt(i,e,t){if(ct(e.defVal)){const n={field:i,type:e.type,value:e.defVal,view:e.view,fieldExtendJson:e.fieldExtendJson},u=t.findIndex(s=>s.field===i);u===-1?t.push(n):t[u]=n}}function qn(i,e){ct(i.fieldDefaultValue)&&e.push({field:i.key,type:i.type,value:i.fieldDefaultValue})}function st(i,e,t){return K(this,null,function*(){if(Array.isArray(i)&&i.length>0){let n={};for(let u of i){let{value:s,type:c,field:y}=u;s=yield qt(s,Qe.ADD,t||{}),c==="number"&&s&&(s=Number.parseFloat(s)),s=Wn(u,s),n[y]=s}e(n)}})}function Wn(i,e){const{type:t,field:n,view:u,fieldExtendJson:s}=i;if(u=="date"&&s){const c=JSON.parse(s),{picker:y}=c;if(y&&y!="default"&&e){let p;try{if(y==="year"){const A=e.split("-")[0];p=ye().year(A).format("YYYY-MM-DD")}if(y==="month"){const A=e.split("-"),T=A[0],b=+A[1]+1;p=ye().year(T).month(b).format("YYYY-MM-DD")}if(y==="week"){const A=e.split("-"),T=A[0],b=A[1].match(/^(\d+)周$/)[1];p=ye().year(T).week(b).format("YYYY-MM-DD")}if(y==="quarter"){const A=e.split("-"),T=A[0],b=A[1].match(/^[Qq](\d)$/)[1];p=ye().year(T).quarter(b).format("YYYY-MM-DD")}}catch(A){p=e}return p}return e}return e}function ms(i,e,t){return K(this,null,function*(){let{defVal:n,type:u}=e;if(ct(n)){let s=yield qt(n,Qe.ADD,{});if(u==="number"&&s)if(e.mode=="group"&&typeof s=="string"&&s.indexOf(",")!=-1){const c=s.split(",");s=[],c[0]&&s.push(Number.parseFloat(c[0])),c[1]&&s.push(Number.parseFloat(c[1]))}else s=Number.parseFloat(s);t[i]=s}})}function ct(i){return!!(i||i===0)}function qt(i,e,t){return K(this,null,function*(){if(i!=null&&Wt(i)){let n=yield Kn(i,e,t);if(n!=null)return n}return i})}function Wt(i){let e=0,t=0,n=0;if(i.replace(Je,()=>n++),n>1)return!1;i.replace(Ht,()=>e++),i.replace(Yt,()=>t++);let u=e+t;return!(n>0&&u>0)}function Kt(i,e){let t=new Map;return i.replace(e,function(n,u){return t.set(n,u.trim()),n}),t}function Kn(i,e,t){return K(this,null,function*(){return(e===Qe.ADD||e===Qe.RELOAD)&&Je.test(i)?yield it(i,Je,Zn,[t]):e===Qe.ADD?(i=yield it(i,Ht,zn),i=yield it(i,Yt,Gn),i):null})}function it(i,e,t){return K(this,arguments,function*(n,u,s,c=[]){let y=Kt(n,u);for(let p of y.keys()){let A=y.get(p),T=yield s.apply(null,[A,p,...c]);if(p===n)return T;n=Oi(n,p,T)}return n})}function zn(i,e){return K(this,null,function*(){switch(i){case"date":return ye().format("YYYY-MM-DD");case"time":return ye().format("HH:mm:ss");case"datetime":return ye().format("YYYY-MM-DD HH:mm:ss");default:let t=Xn(i);return t!=null?t:e}})}function Xn(i){let e=ki().getUserInfo;if(e)switch(i){case"sysUserId":return e.id;case"sysUserCode":case"sys_user_code":return e.username;case"sysUserName":return e.realname;case"sysOrgCode":case"sys_org_code":return e.orgCode}return null}function Gn(i,e){return K(this,null,function*(){let t=at(`(function (${Hn}){ return ${i} })`);try{return t.apply(null,Yn)}catch(n){return e}})}function Zn(i,e,t){return K(this,null,function*(){let n={};typeof t=="function"?n=t():t&&(n=he({},t)),i=zt(i).exp;let u=`/sys/fillRule/executeRuleByCode/${i}`,{success:s,message:c,result:y}=yield me.put({url:u,params:n},{isTransformResponse:!1});return s?y:e})}function zt(i){let e=i.split("?");if(e.length>1){let t="",n=[],u=e[1].split("&");return u.forEach((s,c)=>{let[y,p]=s.split("=");p=p.trim(),y==="onl_watch"?n=p.split(","):(t+=`${y}=${p}`,c<u.length-1&&(t+="&"))}),{exp:e[0]+(t===""?"":"?"+t),watchFields:n}}return{exp:i,watchFields:[]}}function gs(i){const e=new Map;if(Array.isArray(i)&&i.length>0)for(let t of i){let{value:n,field:u}=t;if(!(n==null||n=="")&&Wt(n)&&Je.test(n)){let s=Kt(n,Je);for(let c of s.keys()){let y=s.get(c);const{watchFields:p}=zt(y);for(const A of p){let T=e.get(A);Array.isArray(T)||(T=[],e.set(A,T)),!T.includes(u)&&T.push(u)}}}}return e}const pt="link_down",el="link_table_field",tl="link_table";function Xt(i,e){Gl();const t=i.modalClass,n=I([]),u=I(""),s=I({}),c=Ce({}),y=I(!1),p=I([]),A=I({}),T={},b=I([]),O=Ce({}),C=I("");C.value={sm:24,xs:24,md:12,lg:12,xl:12,xxl:12};const N=I({xs:{span:24},sm:{span:4},md:{span:4},lg:{span:4},xl:{span:4},xxl:{span:4}}),E=I(null),H=I(6*14+10);function J(x,k,w,S={}){var P;ll(O),O[u.value]=[];let _=[],d=[],l=[],r={},a={};Object.keys(x).map(h=>{var R;const D=x[h];if(D.view=="tab"){y.value=!0,O[h]=[];let U={key:h,foreignKey:D.foreignKey,describe:D.describe,relationType:D.relationType,requiredFields:D.required||[],order:D.order,id:D.id};D.relationType==1?(T[h]=I(null),U.properties=D.properties):(B(D),T[h]=I(),U.columns=D.columns,r[h]=[]),_.push(U),sl(h,D)}else if(tt(h,D,O[u.value]),D.view===pt){let U=nl(D,h);for(let L of U){const ee=L.key==h?D:(R=D.others)==null?void 0:R.find(G=>G.field===L.key);ee&&tt(L.key,ee,O[u.value]),c[L.key]=!0,c[L.key+"_load"]=!0,c[L.key+"_disabled"]=!1,j(S,L);let Q=et.createFormSchema(L.key,L);w&&Q.setOnlyValidateFun(w),Q.isRequired(k),Q.setFormRef(e),Q.handleWidgetAttr(D);let W=xt(d,L.key);W==-1?d.push(Q):d[W]=Q}}else if(tt(h,D,O[u.value]),c[h]=!0,c[h+"_load"]=!0,c[h+"_disabled"]=!1,xt(d,h)==-1){j(S,D);let U=et.createFormSchema(h,D);if(w&&U.setOnlyValidateFun(w),U.isRequired(k),U.setFormRef(e),d.push(U),l.push(...U.getRelatedHideFields()),D.view===el){let L=U.getLinkFieldInfo();L&&(a[L[0]]?a[L[0]].push(L[1]):a[L[0]]=[L[1]])}}}),d.sort(function(h,R){return h.order-R.order});const o=[];(()=>{for(let h=0,R=d.length;h<R;h++){const D=d[h];V(D==null?void 0:D._data,"isOneRow")&&(o.push(d.splice(h,1)[0]),h--,R--)}})(),d=[...d,...o];let m=[];m.push(et.createIdField());let v=null,F=!1;for(let h of d){const R=h.label.length;v?(v.label.length<R||v.label.length===R&&!v.required&&h.required)&&(v=h):v=h,h.required&&(F=!0),h.view&&h.view==tl&&a[h.field]&&h.setOtherInfo(a[h.field]),l.indexOf(h.field)>=0&&h.isHidden(),t&&h.setCustomPopContainer(t);const D=h.getFormItemSchema();if(D.component==="JDictSelectTag"&&((P=h==null?void 0:h._data)==null?void 0:P.type)==="number"&&(D.componentProps.stringToNumber=!0),i.formTemplate>1&&V(h==null?void 0:h._data,"isOneRow")){D.colProps={span:24};const U=z(),{labelCol:L={}}=U,ee={},Q={};Object.keys(L).forEach(W=>{if(["xs","sm","md","lg","xl","xxl"].includes(W)){const G=L[W].span,te=Math.round(G/i.formTemplate);ee[W]={span:te},Q[W]={span:24-te-1}}}),D.itemProps={labelCol:ee,wrapperCol:Q}}m.push(D)}if(n.value=m,_.sort(function(h,R){return h.order-R.order}),_.forEach(h=>{const R=h.columns;h.columns&&R.forEach(D=>{var U;if(h.relationType==0&&["popup","popup_dict"].includes(D.type)){let L=!0;D.fieldExtendJson&&(L=JSON.parse(D.fieldExtendJson).popupMulti);const ee=(U=D.props)!=null?U:{};D.props=Le(he({},ee),{multi:L})}if(D.type==="date"&&D.fieldExtendJson){const L=JSON.parse(D.fieldExtendJson);L.picker&&L.picker!="default"&&Object.assign(D,{picker:L.picker})}})}),p.value=_,A.value=r,S.formLabelLengthShow&&S.formLabelLength)H.value=S.formLabelLength*14+10+ +`${F?13:0}`,E.value=null;else if(v){let h=v.label.length;h=h>ot?ot:h;const R=v.required,D=h*14+10+ +`${R?13:0}`;H.value=D}}ge(c,x=>{let k=e.value,w=[],S=be(x);Object.keys(S).map(P=>{if(!P.endsWith("_load")){let _={field:P,show:S[P]},d=P+"_load";S.hasOwnProperty(d)&&(_.ifShow=S[d]);let l=P+"_disabled";S.hasOwnProperty(l)&&(_.dynamicDisabled=()=>S[l]),w.push(_)}}),k&&k.updateSchema(w)},{immediate:!1});function B(x){Gt(x,k=>{qn(k,O[x.key])})}function j(x,k,w="labelLength"){const{formLabelLengthShow:S,formLabelLength:P}=x;if(S&&P){let _=k==null?void 0:k.fieldExtendJson;_?(_=JSON.parse(_),_[w]=P):_={[w]:P},k.fieldExtendJson=JSON.stringify(_)}}function V(x={},k){let w=x==null?void 0:x.fieldExtendJson;if(w)return w=JSON.parse(w),w[k]}ge(()=>i.formTemplate,()=>{const x=z();C.value=x.baseColProps,N.value=x.labelCol,E.value=x.wrapperCol},{immediate:!0});function z(){let x=i.formTemplate;return x==2?{baseColProps:{sm:24,xs:24,md:12,lg:12,xl:12,xxl:12}}:x==3?{baseColProps:{sm:24,xs:24,md:8,lg:8,xl:8,xxl:8}}:x==4?{baseColProps:{sm:24,xs:24,md:6,lg:6,xl:6,xxl:6}}:{baseColProps:{sm:24,xs:24,md:24,lg:24,xl:24,xxl:24}}}function q(x,k){return new Promise(w=>{k||w("");let S={tableName:u.value.replace(/\$\d+/,""),fieldName:x.field,fieldVal:k},P=s.value;P.id&&(S.dataId=P.id),Wi(S).then(_=>{_.success?w(""):w(_.message)}).catch(_=>{w(_)})})}function X(x){return Object.keys(x).map(k=>{x[k]&&x[k]instanceof Array&&(x[k]=x[k].join(","))}),x}return{formSchemas:n,defaultValueFields:O,tableName:u,dbData:s,checkOnlyFieldValue:q,createFormSchemas:J,fieldDisplayStatus:c,subTabInfo:p,hasSubTable:y,subDataSource:A,baseColProps:C,changeDataIfArray2String:X,linkDownList:b,refMap:T,labelCol:N,wrapperCol:E,labelWidth:H}}function Gt(i,e){const t={inputNumber:"input-number",sel_depart:"depart-select",sel_user:"user-select",list_multi:"select-multiple",input_pop:"textarea",sel_search:"select-search","select-dict-search":"selectDictSearch"};i.columns.forEach(s=>{s.type==="radio"?s.type="select":t[s.type]?s.type=t[s.type]:s.type==="popup"&&n(s),s.type==="depart-select"&&(s.checkStrictly=!0),s.type==="user-select"&&u(s),s.type==="pca"&&(s.width="230px"),(s.width==120||s.width=="120px")&&(s.type=="image"||s.type=="file")&&(s.width="130px"),s.width||(s.width="200px"),e&&e(s)});function n(s){let{destFields:c,orgFields:y}=s,p=[];if(!(!c||c.length==0)){let A=c.split(","),T=y.split(",");for(let b=0;b<A.length;b++)p.push({target:A[b],source:T[b]})}s.fieldConfig=p}function u(s){let c=s.fieldExtendJson,y=!1;if(c)try{JSON.parse(c).multiSelect===!1&&(y=!0)}catch(p){}s.isRadioSelection=y}}function il(i){let e={};const t={addSubRows:"<m> 一对多子表，新增自定义行",changeOptions:"<m> 改变下拉框选项",clearSubRows:"<m> 清空一对多子表行",clearThenAddRows:"<m> 清空一对多子表行，然后新增自定义行",executeMainFillRule:"<m> 刷新主表的增值规制值",executeSubFillRule:"<m> 刷新子表的增值规制值",getFieldsValue:"<m> 获取表单控件的值",getSubTableInstance:"<m> 获取子表实例",isUpdate:"<p> 判断是否为编辑模式",loading:"<p> 页面加载状态",onlineFormRef:"<p> 当前表单ref对象",refMap:"<p> 子表ref对象map",setFieldsValue:"<m> 设置表单控件的值",sh:"<p> 表单控件的显示隐藏状态",subActiveKey:"<p> 子表激活tab，对应子表表名",subFormHeight:"<p> 一对一子表表单高度",submitFlowFlag:"<p> 是否提交流程状态",subTableHeight:"<p> 一对多子表表格高度",tableName:"<p> 当前表名",triggleChangeValues:"<m> 修改多个表单值",triggleChangeValue:"<m> 修改表单值",updateSchema:"<m> 修改表单控件配置",changeSubTableOptions:"<m> 改变一对多子表下拉框选项",changeSubFormbleOptions:"<m> 改变一对一子表下拉框选项",changeRemoteOptions:"<m> 改变远程下拉框选项",submitFormAndFlow:"<m> 提交表单且发起流程"},n=new Proxy(t,{get(b,O){return Reflect.get(e,O)}});function u(b,O){e[b]=O}function s(b){Object.keys(b).map(O=>{e[O]=b[O]})}u("$nextTick",Ne),u("addObject2Context",u);const c=(b,O)=>pe(()=>{const{buttonSwitch:C}=i,N={enabled:!0,buttonIcon:O[0],buttonName:O[1]};if((C==null?void 0:C[b])===!1)return N.enabled=!1,N;const{cgBIBtnMap:E}=i;return E!=null&&E[b]?E[b]:N}),y=c("form_sub_add",["ant-design:plus-outlined","新增"]),p=c("form_sub_batch_delete",["ant-design:minus-outlined","删除"]),A=c("form_sub_open_add",["ant-design:expand-alt-outlined","新增"]),T=c("form_sub_open_edit",["ant-design:form-outlined",""]);return{onlineFormContext:n,addObject2Context:u,resetContext:s,getSubAddBtnCfg:y,getSubRemoveBtnCfg:p,getSubOpenAddBtnCfg:A,getSubOpenEditBtnCfg:T}}function nl(i,e){const{config:{table:t,key:n,txt:u,linkField:s,idField:c,pidField:y,condition:p},others:A,order:T,title:b}=i;let O={dictTable:t,dictText:u,dictCode:n,pidField:y,idField:c,view:pt,type:i.type},C=[],N=he({key:e,title:b,order:T,condition:p,origin:!0},O);if(s&&s.length>0){let E=s.split(",");N.next=E[0];for(let H=0;H<E.length;H++)for(let J of A)if(J.field==E[H]){let B=he({key:J.field,title:J.title,order:J.order,origin:!1},O);H+1<E.length&&(B.next=E[H+1]),C.push(B)}}return C.push(N),C}function xt(i,e){let t=-1;for(let n=0;n<i.length;n++)if(i[n].field===e){t=n;break}return t}function Me(i){return new Promise(e=>{(function t(){let n=i.value;n?e(n):setTimeout(()=>{t()},100)})()})}function ll(i){Object.keys(i).map(e=>{delete i[e]})}const ol=Ii();function sl(i,e){let t=e.hideButtons,n=dn+i+":";t||(t=[]),ol.setOnlineSubTableAuth(n,t)}function bs(i){const e=I([]),t={},n=Ce({}),u=I(!1),s=I([]),c=I({}),{getIsMobile:y}=kt(),p=pe(()=>{let C=i.formTemplate;return y.value?24:C=="2"?12:C=="3"?8:C=="4"?6:24});function A(C){let N=[],E=[],H={};Object.keys(C).map(J=>{const B=C[J];if(B.view=="tab"){u.value=!0;let j={key:J,foreignKey:B.foreignKey,describe:B.describe,relationType:B.relationType,requiredFields:B.required||[],order:B.order};B.relationType==1?(t[J]=I(null),j.properties=B.properties):(T(B),t[J]=I(),j.columns=B.columns,H[J]=[],n[J]=!1),N.push(j)}else if(B.view===pt){let j=O(B,J);for(let V of j){let z=b(E,V.key),q={field:V.key,label:V.title,view:V.view,order:V.order,dictTable:V.dictTable,linkField:V.linkField||""};z==-1?E.push(q):E[z]=q}}else if(B.view!="hidden"&&b(E,J)==-1){let j=Object.assign({field:J,label:B.title},Ye(B,["view","order","fieldExtendJson","dictTable","dictText","dictCode","dict"]));if(B.view=="file"&&(j.span=24,j.isFile=!0),B.view=="image"&&(j.span=24,j.isImage=!0),B.view=="link_table"&&B.fieldExtendJson)try{let V=JSON.parse(B.fieldExtendJson);V.showType!="select"&&(j.isCard=!0),V.multiSelect==!0&&(j.multi=!0)}catch(V){}(B.view=="umeditor"||B.view=="markdown")&&(j.isHtml=!0,j.span=24),E.push(j)}}),E.sort(function(J,B){return J.order-B.order}),N.sort(function(J,B){return J.order-B.order}),s.value=N;for(let J=0;J<E.length;J++){let B=E[J];if((B.isFile===!0||B.isImage===!0||B.isHtml===!0)&&J>0){let j=E[J-1],V=j.span||p.value;j.span=V}}e.value=E,c.value=H}function T(C){Gt(C)}function b(C,N){let E=-1;for(let H=0;H<C.length;H++)if(C[H].field===N){E=H;break}return E}function O(C,N){let E=[];const{config:{table:H,key:J,txt:B,linkField:j},order:V,title:z,others:q}=C;let X={view:"link_down",order:V,title:z,dictTable:JSON.stringify({table:H,key:J,txt:B})};if(E.push(Object.assign({},{linkField:j,key:N},X)),j){let x=j.split(",");for(let k of x){let w="";for(let S of q)S.field==k&&(w=S.title);E.push(Object.assign({},{key:k},X,{title:w}))}}return E}return{detailFormSchemas:e,hasSubTable:u,subTabInfo:s,refMap:t,showStatus:n,createFormSchemas:A,formSpan:p,subDataSource:c}}function al(i,e=!0){let t=Ce({});const n=(b,O)=>me.get({url:b,params:O},{isTransformResponse:!1}),u=(b,O)=>me.post({url:b,params:O},{isTransformResponse:!1}),s=(b,O)=>me.put({url:b,params:O},{isTransformResponse:!1}),c=(b,O)=>me.delete({url:b,params:O},{isTransformResponse:!1});e===!0?(i._getAction=n,i._postAction=u,i._putAction=s,i._deleteAction=c,i._useMessage=Re):(i.addObject2Context("_getAction",n),i.addObject2Context("_postAction",u),i.addObject2Context("_putAction",s),i.addObject2Context("_deleteAction",c),i.addObject2Context("_useMessage",Re));function y(b){if(b){let O,C;try{O=at(b),C=new O(n,u,c)}catch(N){C={};const{createMessage:E}=Re();E.warning(`js增强代码有语法错误，请检查代码~ ${N}`)}return C}else return{}}function p(b,O){t&&t[O]&&t[O](b)}function A(b,O){return t&&t.beforeSubmit?t.beforeSubmit(b,O):Promise.resolve()}function T(b,O){return t&&t.beforeDelete?t.beforeDelete(b,O):Promise.resolve()}return e===!0&&i&&(i.beforeDelete=b=>{const O=i.EnhanceJS;return O&&O.beforeDelete?O.beforeDelete(i,b):Promise.resolve()},i.beforeEdit=b=>{const O=i.EnhanceJS;return O&&O.beforeEdit?O.beforeEdit(i,b):Promise.resolve()}),{EnhanceJS:t,initCgEnhanceJs:y,customBeforeSubmit:A,beforeDelete:T,triggerJsFun:p}}const rl="/online/cgform/api/subform",ul={name:"OnlineSubForm",components:{BasicForm:Nt,Loading:It},props:{properties:{type:Object,required:!0},mainId:{type:String,default:""},table:{type:String,default:""},formTemplate:{type:Number,default:1},requiredFields:{type:Array,default:[]},isUpdate:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},emits:["formChange"],setup(i,{emit:e}){const t=I(null),n=I(!1);Re();const{formSchemas:u,defaultValueFields:s,changeDataIfArray2String:c,tableName:y,dbData:p,checkOnlyFieldValue:A,fieldDisplayStatus:T,createFormSchemas:b,baseColProps:O,labelCol:C,wrapperCol:N,labelWidth:E}=Xt(i,t),[H,{setProps:J,validate:B,resetFields:j,setFieldsValue:V,getFieldsValue:z,updateSchema:q,scrollToField:X}]=Qt({schemas:u,showActionButtonGroup:!1,baseColProps:O,labelWidth:E,labelCol:C,wrapperCol:N});ge(()=>i.table,()=>{y.value=i.table},{immediate:!0}),ge(()=>i.properties,a=>{n.value=!1,x(),b(i.properties,i.requiredFields,A),n.value=!0},{deep:!0,immediate:!0}),ge(()=>i.mainId,a=>{setTimeout(()=>{w()},100)},{immediate:!0}),ge(()=>i.disabled,a=>{J({disabled:a})});function x(){return K(this,null,function*(){let a=yield Me(t);a.$formValueChange=(o,m)=>{let v={[o]:m};e("formChange",v)}})}function k(){if(Ae(i.isUpdate)===!1){let a=be(s[y.value]);st(a,o=>{V(o)})}}function w(){return K(this,null,function*(){yield Me(n),yield j(),k();const{table:a,mainId:o}=i;if(!a||!o)return;let m=yield S(a,o);p.value=m,yield V(m)})}function S(a,o){let m=`${rl}/${a}/${o}`;return new Promise((v,F)=>{me.get({url:m},{isTransformResponse:!1}).then(h=>{h.success?v(h.result):F()})}).finally(()=>{p.value=""})}function P(){return new Promise((a,o)=>{B().then(()=>{let m=z();m=c(m),a(m)}).catch(m=>{m.errorFields&&(m.scrollToField=()=>m.errorFields[0]&&X(m.errorFields[0].name,{behavior:"smooth",block:"center"})),o(m)})})}function _(){let a=z();return a.id||(a.id="sub-change-temp-id"),{row:a,target:r}}function d(a){V(a)}function l(){let a=z(),o=be(s[y.value]);st(o,m=>{V(m)},a)}const r={onlineFormRef:t,baseColProps:O,formSchemas:u,registerForm:H,setFieldsValue:V,getFieldsValue:z,getFormEvent:_,setValues:d,getAll:P,executeFillRule:l,sh:T,resetFields:j,updateSchema:q};return r}};function dl(i,e,t,n,u,s){const c=le("BasicForm");return ie(),ke(c,{ref:"onlineFormRef",onRegister:i.registerForm},null,8,["onRegister"])}const Zt=Ee(ul,[["render",dl],["__scopeId","data-v-196bf574"]]),vs=Object.freeze(Object.defineProperty({__proto__:null,default:Zt},Symbol.toStringTag,{value:"Module"})),nt={optPre:"/online/cgform/api/form/",urlButtonAction:"/online/cgform/api/doButton"},cl={name:"OnlinePopForm",components:{BasicForm:Nt,Loading:It,OnlineSubForm:Zt,PrinterOutlined:Hi,DiffOutlined:Ui,FormOutlined:Vi},props:{id:{type:String,default:""},formTemplate:{type:Number,default:1},disabled:{type:Boolean,default:!1},isTree:{type:Boolean,default:!1},pidField:{type:String,default:""},submitTip:{type:Boolean,default:!0},modalClass:{type:String,default:""},request:{type:Boolean,default:!0},isVxeTableData:{type:Boolean,default:!1}},emits:["success","rendered","dataChange"],setup(i,{emit:e}){const{createMessage:t}=Re(),[n,{openModal:u}]=Pe(),s=I(""),c=I(null),y=I(!0),p=I(!1),A=I(1),T=I(!1),b=I(!1),O=Ce({reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:""}),{onlineFormContext:C,resetContext:N}=il(),{formSchemas:E,defaultValueFields:H,changeDataIfArray2String:J,tableName:B,dbData:j,checkOnlyFieldValue:V,hasSubTable:z,subTabInfo:q,refMap:X,subDataSource:x,baseColProps:k,createFormSchemas:w,fieldDisplayStatus:S,labelCol:P,wrapperCol:_,labelWidth:d}=Xt(i,c);let{EnhanceJS:l,initCgEnhanceJs:r}=al(C,!1);const[a,{setProps:o,validate:m,resetFields:v,setFieldsValue:F,updateSchema:h,getFieldsValue:R,scrollToField:D}]=Qt({schemas:E,showActionButtonGroup:!1,baseColProps:k,labelWidth:d,labelCol:P,wrapperCol:_}),U=I(!1);function L(){let f=i.disabled;U.value=f,o({disabled:f})}function ee(f,g,M){return K(this,null,function*(){yield v(),j.value="";let $=Ae(f);b.value=$,$&&(yield W(g)),yield Ne(()=>{!$&&M&&F(M),Q(),Xe("js","loaded"),L()})})}function Q(){if(Ae(b)===!1){let f=be(H[B.value]);st(f,g=>{F(g)})}}function W(f){return K(this,null,function*(){let g=yield te(f.id);(!g||Object.keys(g).length==0)&&(g=he({},be(f))),j.value=Object.assign({},g);let M=G.value,$=Ye(g,...M);i.isVxeTableData===!0&&($=Object.assign({},$,f)),yield F($)})}let G=pe(()=>{let f=E.value,g=[];for(let M of f)g.push(M.field);return g});function te(f){let g=`${nt.optPre}${i.id}/${f}`;return new Promise((M,$)=>{me.get({url:g},{isTransformResponse:!1}).then(oe=>{oe.success?M(oe.result):($(),t.warning(oe.message))}).catch(()=>{$()})})}function ne(f){return K(this,null,function*(){A.value=f.head.tableType,B.value=f.head.tableName,y.value=f.head.tableType==1,we(f.head.extConfigJson),w(f.schema.properties,f.schema.required,V,O),l=r(f.enhanceJs),e("rendered",O);let g=yield Me(c);g.$formValueChange=(M,$,oe)=>{pi(M,$),oe&&F(oe)}})}function we(f){let g={reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:1,modalMinWidth:"",formLabelLength:null};f&&(g=JSON.parse(f)),Object.keys(g).map(M=>{O[M]=g[M]})}function Ke(){y.value===!0?Ie():Y()}function Y(){re().then(f=>{ue(f)})}function re(){let f={};return new Promise((g,M)=>{m().then($=>g($),({errorFields:$})=>{M({code:je,key:B.value,scrollToField:()=>$[0]&&D($[0].name,{behavior:"smooth",block:"center"})})})}).then(g=>(Object.assign(f,J(g)),De())).then(g=>(Object.assign(f,g),Promise.resolve(f))).catch(g=>((g===je||(g==null?void 0:g.code)===je)&&(t.warning("校验未通过"),g.key&&(ve(g.key),g.scrollToField&&setTimeout(()=>g.scrollToField(),150))),Promise.reject(null)))}function ve(f){let g=q.value;for(let M=0;M<g.length;M++)if(f==g[M].key){Be.value=M+"";break}}function De(){return new Promise((f,g)=>K(this,null,function*(){let M={};try{let $=q.value;for(let oe=0;oe<$.length;oe++){let Se=$[oe].key,xe=X[Se].value;if(xe instanceof Array&&(xe=xe[0]),$[oe].relationType==1)try{let Ge=yield xe.getAll();M[Se]=[],M[Se].push(Ge)}catch(Ge){return g(he({code:je,key:Se},Ge))}else{if(yield xe.fullValidateTable())return g({code:je,key:Se});M[Se]=xe.getTableData()}}}catch($){g($)}f(M)}))}function Ie(){return K(this,null,function*(){try{let f=yield m();f=Object.assign({},j.value,f),f=J(f),p.value=!0,ue(f)}catch(f){if(Ot(f)){const g=f.errorFields;g!=null&&g.length&&g[0].errors&&(t.warning(g[0].errors[0]),D(g[0].name,{behavior:"smooth",block:"center"}))}}finally{p.value=!1}})}function ue(f){mi(bt,f).then(()=>{wi(f)}).catch(g=>{t.warning(g)})}function $e(f,g,M){g&&M?M.setValues?M.setValues(f):M.setValues([{rowKey:g,values:f}]):F(f)}function ze(f,g){let M={};M[f]=g,F(M)}const Be=I("0"),ht=I(300),ft=I(340);function si(f){if(b.value===!0){let g=j.value;return ai(g,f)}return""}function ai(f,g){if(f){let M=f[g];return!M&&M!==0&&(M=f[g.toLowerCase()],!M&&M!==0&&(M=f[g.toUpperCase()])),M}return""}function ri(f,g){if(l&&l[g+"_onlChange"]){let M=l[g+"_onlChange"](),$=Object.keys(f)[0];if(M[$]){let oe=X[g].value;oe instanceof Array&&(oe=oe[0]);let Se=oe.getFormEvent(),xe=he({column:{key:$},value:f[$]},Se);M[$].call(C,C,xe)}}}function ui(f,g){if(l&&l[g+"_onlChange"]){let M=l[g+"_onlChange"](C);M[f.column.key]&&M[f.column.key].call(C,C,f)}}function di(f,g){}function ci(f){return"online_"+f+":"}function pi(f,g){return K(this,null,function*(){if(j.value[f]!=g&&e("dataChange",f),!l||!l.onlChange||!f)return!1;let M=l.onlChange();if(M[f]){let $={row:yield R(),column:{key:f},value:g};M[f].call(C,C,$)}})}function Xe(f,g){if(f=="js")l&&l[g]&&l[g].call(C,C);else if(f=="action"){let M=j.value,$={formId:i.id,buttonCode:g,dataId:M.id,uiFormData:Object.assign({},M)};me.post({url:`${nt.urlButtonAction}`,params:$},{isTransformResponse:!1}).then(oe=>{oe.success?t.success("处理完成!"):t.warning("处理失败!")})}}function mt(f){let g=X[f].value,M=[...g.getNewDataWithId(),...x.value[f]];if(!M||M.length==0)return!1;let $=[];for(let oe of M)$.push(oe.id);g.removeRowsById($)}function gt(f,g){if(!g)return!1;let M=X[f].value;typeof g=="object"?M.addRows(g,!0):t.error("添加子表数据,参数不识别!")}function hi(f,g){mt(f),gt(f,g)}function fi(f,g){!g&&g.length<=0&&(g=[]),g.map(M=>{M.hasOwnProperty("label")||(M.label=M.text)}),h({field:f,componentProps:{options:g}})}function mi(f,g){return l&&l.beforeSubmit?l.beforeSubmit(f,g):Promise.resolve()}function gi(f,g){let M=be(S);f&&f.length>0?Object.keys(M).map($=>{!$.endsWith("_load")&&f.indexOf($)<0&&(S[$]=!1)}):g&&g.length>0&&Object.keys(M).map($=>{g.indexOf($)>=0&&(S[$]=!1)})}function bi(f){return K(this,null,function*(){yield v(),j.value="",b.value=!0,yield W(f),yield Ne(()=>{Xe("js","loaded")})})}function vi(f){let g=X[f].value;return g instanceof Array&&(g=g[0]),g}function yi(){let f=O.reportPrintUrl,g=j.value.id,M=Pt();Pi(f,g,M)}function Ai(f){s.value=f.id,u(!0)}function Ci(f){}function wi(f){if(Object.keys(f).map(g=>{Array.isArray(f[g])&&f[g].length==0&&(f[g]="")}),i.request==!1)e("success",f);else{let g=`${nt.optPre}${i.id}?tabletype=${A.value}`;T.value===!0&&(f[an]=1);let M=b.value===!0?"put":"post";me.request({url:g,method:M,params:f},{isTransformResponse:!1}).then($=>{$.success?($.result&&(f.id||(f.id=$.result)),e("success",f),j.value=f,b.value=!0,t.success("操作成功!")):t.warning($.message)}).finally(()=>{p.value=!1})}}function Si(){return K(this,null,function*(){let f=j.value,g=G.value,M=Ye(f,...g);if(f)yield F(M);else{let $={};for(let oe of g)$[oe]="";yield F($)}})}let bt={tableName:B,loading:p,subActiveKey:Be,onlineFormRef:c,getFieldsValue:R,setFieldsValue:F,submitFlowFlag:T,subFormHeight:ht,subTableHeight:ft,refMap:X,triggleChangeValues:$e,triggleChangeValue:ze,sh:S,clearSubRows:mt,addSubRows:gt,clearThenAddRows:hi,changeOptions:fi,isUpdate:b,getSubTableInstance:vi};return N(bt),{tableName:B,onlineFormRef:c,registerForm:a,loading:p,subActiveKey:Be,hasSubTable:z,subTabInfo:q,refMap:X,subFormHeight:ht,getSubTableForeignKeyValue:si,isUpdate:b,handleSubFormChange:ri,subTableHeight:ft,onlineFormDisabled:U,subDataSource:x,getSubTableAuthPre:ci,handleAdded:di,handleValueChange:ui,openSubFormModalForAdd:Ai,openSubFormModalForEdit:Ci,registerVxeFormModal:n,vxeTableId:s,show:ee,createRootProperties:ne,handleSubmit:Ke,sh:S,handleCgButtonClick:Xe,handleCustomFormSh:gi,handleCustomFormEdit:bi,dbData:j,onOpenReportPrint:yi,onlineExtConfigJson:O,recoverFormData:Si}}},pl=["id"];function hl(i,e,t,n,u,s){const c=le("BasicForm");return ie(),de("div",{id:n.tableName+"_form",class:Ue(["onlinePopFormWrap",[`formTemplate_${t.formTemplate}`]])},[ce(c,{ref:"onlineFormRef",onRegister:n.registerForm},null,8,["onRegister"])],10,pl)}const ei=Ee(cl,[["render",hl],["__scopeId","data-v-2c2d5706"]]),ys=Object.freeze(Object.defineProperty({__proto__:null,default:ei},Symbol.toStringTag,{value:"Module"}));function fl(i,{emit:e}={},t){const n=I(null),u=I(!1),s=I(1),c=I([]),y=I(!1),p=I(0),A=I(!1),T=I(""),b=I(!1),O=I(!1),C=I(!0),N=Ce({}),E=I(!0),H=I(""),J=I(!0),B=I(!1),{popModalFixedWidth:j,resetBodyStyle:V,popBodyStyle:z}=ti(),q=I(!1),X=I(""),{getIsMobile:x}=kt(),k={handleOpenModal:Y=>{}},w=I(""),S=I(""),P=I(""),_=I(!1);let d={};const l=pe(()=>H.value||(Ae(u)===!0?"详情":Ae(O)===!0?"编辑":"新增")),[r,{setModalProps:a,closeModal:o}]=Dt(Y=>K(this,null,function*(){H.value="",B.value=!1,i===!0?yield k.handleOpenModal(Y):yield F(Y),V(),t&&t()})),m=I(!1);function v(){return K(this,null,function*(){return yield Me(m),q.value})}function F(Y){return K(this,null,function*(){a({confirmLoading:!1}),O.value=Y.isUpdate,u.value=Y.disableSubmit||!1,(Y==null?void 0:Y.hideSub)===!0&&(E.value=!1),Y!=null&&Y.title&&(H.value=Y.title),Y!=null&&Y.record?P.value=Y.record.id:P.value="",yield Ne(()=>K(this,null,function*(){yield Me(y),R(),yield n.value.show(Y==null?void 0:Y.isUpdate,Y==null?void 0:Y.record,Y==null?void 0:Y.param)}))})}function h(Y){y.value=!0,p.value=Y.modalMinWidth,Y.modelFullscreen==1?a({defaultFullscreen:!0}):a({defaultFullscreen:!1}),d=Y,x.value&&(d.commentStatus=0)}function R(){let Y=P.value;d.commentStatus==1&&Y?(_.value=!0,a({defaultFullscreen:!0})):_.value=!1}const D=800,U=1100,L=pe(()=>{let Y=200*(s.value-1),re=(Ae(C)?D:U)+Y;re=ee(re);let ve=p.value;return ve&&re<ve&&(re=ve),re});function ee(Y){let re=N.modalMinWidth;if(re!=null&&re!=="")try{if(re=Number.parseInt(re),Y<re)return re}catch(ve){}return Y}function Q(Y,re){n.value.handleCgButtonClick(Y,re)}function W(){b.value=!0,setTimeout(()=>{b.value=!1},1500),n.value.handleSubmit()}function G(){o()}function te(Y,re={}){let ve=`/online/cgform/api/getFormItem/${Y}`;return new Promise((De,Ie)=>{me.get({url:ve,params:re},{isTransformResponse:!1}).then(ue=>{ue.success?De(ue.result):Ie(ue.message)}).catch(()=>{Ie()})})}function ne(Y,re,ve,De,Ie){return K(this,null,function*(){let ue=null;if(De&&Ie){const ze=`/online/cgform/api/getFormItemBytbname/${Ie}`,Be={taskId:De};ue=yield me.get({url:ze,params:Be})}else ue=yield te(Y,re);let $e=ue.head.formTemplate;s.value=$e?Number($e):1,c.value=ue.cgButtonList,A.value=ue.head.isTree==="Y",T.value=ue.head.treeParentIdField||"",w.value=ue.head.id,S.value=ue.head.tableName,X.value=ue.head.themeTemplate,ue.form_disable_update===!0?q.value=!0:q.value=!1,m.value=!0,e&&e("formConfig",ue),ve&&ve(ue),yield Ne(()=>K(this,null,function*(){yield(yield Me(n)).createRootProperties(ue)}))})}function we(Y){Y[rn]=S.value,e("success",Y),J.value==!0&&o(),B.value=!1,J.value=!0}function Ke(){n.value&&n.value.onCloseModal(),O.value&&(d!=null?d:{}).commentStatus==1&&a({defaultFullscreen:!1})}return{title:l,modalWidth:L,registerModal:r,closeModal:o,modalObject:k,onCloseEvent:Ke,cgButtonList:c,handleCgButtonClick:Q,disableSubmit:u,handleSubmit:W,submitLoading:b,handleCancel:G,successThenClose:J,handleSuccess:we,topTipVisible:B,handleFormConfig:ne,onlineFormCompRef:n,formTemplate:s,isTreeForm:A,pidFieldName:T,renderSuccess:h,formRendered:y,isUpdate:O,showSub:E,themeTemplate:X,tableId:w,tableName:S,formDataId:P,enableComment:_,popBodyStyle:z,popModalFixedWidth:j,getFormStatus:v}}function ti(){const i=I(800);let e=window.innerWidth-300;e<800&&(e=800),i.value=e;const t=I({});function n(){let u=window.innerHeight-210;t.value={height:u+"px",overflowY:"auto"}}return{popModalFixedWidth:i,popBodyStyle:t,resetBodyStyle:n}}const ml=Mt({name:"OnlinePopModal",props:{id:{type:String,default:""},showFields:{type:Array,default:()=>[]},hideFields:{type:Array,default:()=>[]},topTip:{type:Boolean,default:!1},request:{type:Boolean,default:!0},saveClose:{type:Boolean,default:!1},isVxeTableData:{type:Boolean,default:!1},formTableType:{type:String,default:""},taskId:{type:String},tableName:{type:String}},components:{BasicModal:jt,OnlinePopForm:ei,JModalTip:Gi,Button:Ze},emits:["success","register","formConfig"],setup(i,{emit:e}){const{title:t,registerModal:n,cgButtonList:u,handleCgButtonClick:s,disableSubmit:c,handleSubmit:y,submitLoading:p,handleCancel:A,handleFormConfig:T,onlineFormCompRef:b,formTemplate:O,isTreeForm:C,pidFieldName:N,renderSuccess:E,formRendered:H,handleSuccess:J,topTipVisible:B,successThenClose:j,isUpdate:V,popBodyStyle:z,popModalFixedWidth:q,getFormStatus:X}=fl(!1,{emit:e});ge(()=>i.id,x,{immediate:!0});function x(){return K(this,null,function*(){if(H.value=!1,!i.id)return;let d={};i.formTableType&&(d.tabletype=i.formTableType),i.taskId?yield T(i.id,d,null,i.taskId,i.tableName):yield T(i.id,d)})}function k(){i.saveClose===!1&&(j.value=!1),y()}function w(){B.value=!1,b.value.recoverFormData()}function S(){B.value=!0}const P=pe(()=>c.value||!V.value?!1:i.topTip),_=pe(()=>{if(V.value==!0)return null;{let d=p.value;return[_e(Ze,{type:"primary",loading:d,onClick:y},()=>"确定"),_e(Ze,{onClick:A},()=>"关闭")]}});return{title:t,topTipVisible:B,handleSaveData:k,handleRecover:w,onlineFormCompRef:b,renderSuccess:E,registerModal:n,handleSubmit:y,handleSuccess:J,handleCancel:A,formTemplate:O,disableSubmit:c,cgButtonList:u,handleCgButtonClick:s,isTreeForm:C,pidFieldName:N,submitLoading:p,handleDataChange:S,isUpdate:V,showTopTip:P,modalFooter:_,popBodyStyle:z,popModalFixedWidth:q,getFormStatus:X}}});function gl(i,e,t,n,u,s){const c=le("j-modal-tip"),y=le("online-pop-form"),p=le("BasicModal");return ie(),ke(p,Et({width:i.popModalFixedWidth,dialogStyle:{top:"70px"},bodyStyle:i.popBodyStyle},i.$attrs,{footer:i.modalFooter,cancelText:"关闭",onRegister:i.registerModal,wrapClassName:"jeecg-online-pop-modal",onOk:i.handleSubmit}),{title:ae(()=>[Fe(Oe(i.title)+" ",1),i.showTopTip?(ie(),ke(c,{key:0,visible:i.topTipVisible,onSave:i.handleSaveData,onCancel:i.handleRecover},null,8,["visible","onSave","onCancel"])):Te("",!0)]),default:ae(()=>[ce(y,{ref:"onlineFormCompRef",id:i.id,disabled:i.disableSubmit,"form-template":i.formTemplate,isTree:i.isTreeForm,pidField:i.pidFieldName,request:i.request,isVxeTableData:i.isVxeTableData,onRendered:i.renderSuccess,onSuccess:i.handleSuccess,onDataChange:i.handleDataChange,"modal-class":"jeecg-online-pop-modal"},null,8,["id","disabled","form-template","isTree","pidField","request","isVxeTableData","onRendered","onSuccess","onDataChange"])]),_:1},16,["width","bodyStyle","footer","onRegister","onOk"])}const We=Ee(ml,[["render",gl]]),As=Object.freeze(Object.defineProperty({__proto__:null,default:We},Symbol.toStringTag,{value:"Module"}));function Ft(i,e){const t="/online/cgform/api/getData/"+i;return me.get({url:t,params:e})}function bl(i,e){const t="/online/cgform/api/getColumns/"+i;return me.get({url:t,params:e})}function ii(i){const e=I("1"),t=I({}),n=I({}),u=I(""),s=Ce({add:!0,update:!0}),c=pe(()=>i.textField?i.textField.split(","):[]),y=I([]),p=pe(()=>{let x=y.value;return i.multi==!0?x.slice(0,3):x.slice(0,6)});_i(()=>K(this,null,function*(){if(i.tableName){let x=i.valueField||"",k=i.textField||"",w=[];if(x&&w.push(x),k){let P=k.split(",");u.value=P[0];for(let _ of P)w.push(_)}let S=i.imageField||"";S&&w.push(S),t.value={linkTableSelectFields:w.join(",")},yield C(),yield N()}}));const A=pe(()=>{let x=i.textField||"",k=[],w="";if(x){let S=x.split(",");w=S[0];for(let P=0;P<S.length;P++)P>0&&k.push(S[P])}return{others:k,labelField:w}}),T=I([]),b=I([]),O=I({});function C(){return K(this,null,function*(){let x=t.value;const k=yield bl(i.tableName,x);if(b.value=k.columns,k.columns){let w=i.imageField,S=k.columns.filter(P=>P.dataIndex!=u.value&&P.dataIndex!=w);y.value=S}if(O.value=k.dictOptions,k.hideColumns){let w=k.hideColumns;w.indexOf("add")>=0?s.add=!1:s.add=!0,w.indexOf("update")>=0?s.update=!1:s.update=!0}})}function N(){return K(this,null,function*(){let x=H(),k=(yield Ft(i.tableName,x)).records,w=[],{others:S,labelField:P}=A.value,_=i.imageField;if(k&&k.length>0)for(let d of k){let l=he({},d);E(l);let r=Object.assign({},Ye(l,S),{id:l.id,label:l[P],value:l[i.valueField]});_&&(r[_]=l[_]),w.push(r)}i.editBtnShow&&w.push({}),T.value=w})}function E(x){let k=b.value,w=O.value;for(let S of k){const{dataIndex:P,customRender:_}=S;if((x[P]||x[P]===0)&&_&&_==P&&w[_]){x[P]=Rt(w[_],x[P]);continue}let d=x[P+"_dictText"];d&&(x[P]=d)}}function H(){return Object.assign({pageSize:100,pageNo:e.value},t.value,n.value)}function J(x){if(!x)n.value={};else{let k=c.value,w=[],S=[];for(let P=0;P<k.length;P++)P<=1&&(S.push(k[P]),w.push({field:k[P],rule:"like",val:x}));w.superQueryMatchType="or",w.superQueryParams=encodeURI(JSON.stringify(w)),n.value=w}}function B(x){return K(this,null,function*(){if(!x)return[];let k=i.valueField,w=Le(he({},t.value),{pageSize:100,pageNo:e.value});w.superQueryMatchType="and";let S=[{field:k,rule:"in",val:x}];w.superQueryParams=encodeURI(JSON.stringify(S));let P=(yield Ft(i.tableName,w)).records,_=[];if(P&&P.length>0)for(let d of P){let l=he({},d);E(l),_.push(l)}return _})}function j(x,k){if(!x||x.length==0)return!1;let w=k.split(",");if(w.length!=x.length)return!1;let S=!0;for(let P of x){let _=P[i.valueField];w.indexOf(_)<0&&(S=!1)}return S}function V(x){Object.keys(x).map(k=>{x[k]instanceof Array&&(x[k]=x[k].join(","))})}function z(x,k,w){if(w||(w={}),k&&k.length>0)for(let S of k){let P=S.split(","),_=P[0],d=P[1];if(x[_])x[_].push(w[d]);else{let l=w[d]||"";x[_]=[l]}}}function q(x){if(i.imageField){let k=x[i.imageField];return typeof k=="string"&&(k=k.split(",")[0]),Ve(k)}return""}const X=pe(()=>!!i.imageField);return{pageNo:e,otherColumns:y,realShowColumns:p,selectOptions:T,reloadTableLinkOptions:N,textFieldArray:c,addQueryParams:J,tableColumns:b,transData:E,mainContentField:u,loadOne:B,compareData:j,formatData:V,initFormData:z,getImageSrc:q,showImage:X,auths:s}}const vl={name:"LinkTableSelect",components:{PlusOutlined:rt,EditOutlined:Yi,OnlinePopModal:We},props:{valueField:se.string.def(""),textField:se.string.def(""),tableName:se.string.def(""),multi:se.bool.def(!1),value:se.oneOfType([se.string,se.number,se.array]),linkFields:se.array.def([]),imageField:se.string.def(""),editBtnShow:se.bool.def(!0)},emits:["change","update:value"],setup(i,{emit:e,attrs:t}){const n=Qi("tableId",I(null)),u=I(),s=I([]),{auths:c,mainContentField:y,textFieldArray:p,selectOptions:A,reloadTableLinkOptions:T,addQueryParams:b,formatData:O,initFormData:C,getImageSrc:N,showImage:E}=ii(i),[H,{openModal:J}]=Pe(),B=pe(()=>i.tableName),j=pe(()=>i.multi===!0?Le(he({},t),{mode:"multiple"}):he({},t));function V(d){d==null||d.stopPropagation(),d==null||d.preventDefault(),J(!0,{})}function z(d,l){d==null||d.stopPropagation(),d==null||d.preventDefault(),c.update!=!1&&J(!0,{isUpdate:!0,record:l})}const q="custom:online:reload";Bt(()=>{u.value&&u.value.addEventListener(q,X)}),Ni(()=>{u.value&&u.value.removeEventListener(q,X)});function X(){T()}function x(d){return K(this,null,function*(){try{const r=document.querySelectorAll(`.online-list-${n.value} .jeecg-basic-table-form-container.online-query-form .link-table-select-box`);r&&r.length>0&&r.forEach(a=>a.dispatchEvent(new Event(q)))}catch(r){}yield T();let l=d[i.valueField];i.multi===!0?s.value=[l]:s.value=l,w(s.value)})}function k(d){b(d),T()}function w(d){S(d),d||(b(),T())}function S(d){let l={},r=i.linkFields,a=[];if(!d)C(l,r);else{let o=be(A.value),m=be(d);m instanceof Array?a=[...m]:i.multi==!0?a=m.split(","):a=[m];let v=o.filter(F=>a.indexOf(F[i.valueField])>=0);if(v&&v.length>0){let F=he({},v[0]);if(v.length>1)for(let R=1;R<v.length;R++)F=P(F,v[R]);let h=y.value;F[h]=F.label,C(l,r,F)}}O(l),e("change",a.join(",")||"",l),e("update:value",a.join(",")||"")}function P(d,l){let r={};return Object.keys(d).map(a=>{r[a]=(d[a]||"")+","+(l[a]||"")}),r}ge(()=>i.value,d=>K(this,null,function*(){d?(i.multi==!0?s.value=d.split(","):s.value=d,i.linkFields&&i.linkFields.length>0&&S(d)):s.value=[]}),{immediate:!0}),ge(()=>A.value,d=>{d&&d.length>0&&i.linkFields&&i.linkFields.length>0&&s.value&&s.value.length>0&&S(s.value)});const _=d=>{d.target.src=qe};return{boxRef:u,selectValue:s,selectOptions:A,registerPopModal:H,popTableName:B,textFieldArray:p,handleClickAdd:V,handleClickEdit:z,getFormData:x,handleSearch:Di(k,800),handleChange:w,bindValue:j,showImage:E,getImageSrc:N,auths:c,placeholderImage:qe,handleImageError:_}}},yl={class:"link-table-select-box",ref:"boxRef"},Al={key:1,class:"online-select-item"},Cl={key:0,class:"left-avatar"},wl=["src"],Sl=["src"],xl={class:"right-content"},Fl={class:"others"},Tl={class:"other-item ellipsis"};function Ol(i,e,t,n,u,s){const c=le("PlusOutlined"),y=le("EditOutlined"),p=le("a-select"),A=le("online-pop-modal");return ie(),de("div",yl,[ce(p,Et({value:n.selectValue,"onUpdate:value":e[2]||(e[2]=T=>n.selectValue=T),style:{width:"100%"},placeholder:"请选择","option-label-prop":"label",popupClassName:"table-link-select",allowClear:"","show-search":""},n.bindValue,{options:n.selectOptions,"filter-option":!1,"not-found-content":null,onSearch:n.handleSearch,onChange:n.handleChange}),{option:ae(T=>[!T.value&&n.auths.add?(ie(),de("div",{key:0,class:"opt-add",onClick:e[0]||(e[0]=(...b)=>n.handleClickAdd&&n.handleClickAdd(...b))},[ce(c),e[3]||(e[3]=Fe(" 记录 "))])):(ie(),de("div",Al,[n.showImage?(ie(),de("div",Cl,[n.getImageSrc(T)?(ie(),de("img",{key:0,src:n.getImageSrc(T),alt:"",onError:e[1]||(e[1]=(...b)=>n.handleImageError&&n.handleImageError(...b))},null,40,wl)):(ie(),de("img",{key:1,src:n.placeholderImage,alt:""},null,8,Sl))])):Te("",!0),fe("div",xl,[fe("div",{class:Ue(["label",{noEditBtn:!(t.editBtnShow&&n.auths.update)}])},[t.editBtnShow&&n.auths.update?(ie(),ke(y,{key:0,onClick:b=>n.handleClickEdit(b,T)},null,8,["onClick"])):Te("",!0),Fe(" "+Oe(T.label),1)],2),fe("div",Fl,[(ie(!0),de(He,null,lt(n.textFieldArray,b=>(ie(),de("div",Tl,Oe(T[b]),1))),256))])])]))]),_:1},16,["value","options","onSearch","onChange"]),ce(A,{id:n.popTableName,onRegister:n.registerPopModal,onSuccess:n.getFormData,topTip:""},null,8,["id","onRegister","onSuccess"])],512)}const ni=Ee(vl,[["render",Ol],["__scopeId","data-v-76bee333"]]),Cs=Object.freeze(Object.defineProperty({__proto__:null,default:ni},Symbol.toStringTag,{value:"Module"}));function kl(i,e){let t=Ji();const n=I([]),u=I({}),s=I([]),c=I(null);let y=I(!0),p=pe(()=>{if(y.value!=!0)return{x:!1}});const[A,{openModal:T}]=Pe(),b=I(""),[O,{openModal:C}]=Pe(),N=I("");function E(l,r="checkbox"){u.value=l.dictOptions,l.checkboxFlag=="Y"?c.value={selectedRowKeys:s,onChange:H,type:r}:c.value=null,y.value=l.scrollFlag==1;let a=l.columns;a.forEach(F=>{var h;if(F.fieldExtendJson&&JSON.parse(F.fieldExtendJson).isFixed&&(F.fixed="left"),F.hrefSlotName&&F.scopedSlots){const R=(h=l.fieldHrefSlots)==null?void 0:h.find(D=>D.slotName===F.hrefSlotName);R&&(F.fieldHref=R)}Object.keys(F).map(R=>{F[R]==null&&delete F[R]})});let o=l.fieldHrefSlots;const m={};o.forEach(F=>m[F.slotName]=F);let v=[];if(v=J(a,m),X(v),i.isTree()===!0){let F=l.textField,h=-1;for(let R=0;R<v.length;R++)if(v[R].dataIndex==F){h=R;break}if(h>0){let R=v.splice(h,1);v.unshift(R[0])}v.length>0&&(v[0].align="left")}n.value=v,i.reloadTable()}function H(l,r){s.value=l,i.selectedRows=be(r),i.selectedRowKeys=be(l)}function J(l,r){var a;for(let o of l){let{customRender:m,hrefSlotName:v,fieldType:F}=o;if(F=="date"||F=="Date")o.customRender=({text:h})=>h?h.length>10?h.substring(0,10):h:"";else if(F=="link_table"){const h=(a=o.fieldExtendJson)!=null?a:"{}",R=JSON.parse(h);o.customRender=({text:D,record:U})=>{if(!D)return"";if(i.isPopList===!0)return U[o.dataIndex+"_dictText"];{let L=(D+"").split(","),ee=[];U[o.dataIndex+"_dictText"]&&(ee=U[o.dataIndex+"_dictText"].split(","));let Q=[];for(let W=0;W<L.length;W++){let G=_e(zi,{id:L[W],text:ee[W],onTab:te=>d(te,v,R.isListReadOnly)});Q.push(G)}return Q.length==0?"":_e("div",{style:{overflow:"hidden"}},Q)}}}else if(F==="popup_dict")o.customRender=({text:h,record:R})=>R[o.dataIndex+"_dictText"]!=null?R[o.dataIndex+"_dictText"]:h;else{if(!v&&o.scopedSlots&&o.scopedSlots.customRender&&r.hasOwnProperty(o.scopedSlots.customRender)&&(v=o.scopedSlots.customRender),m||v){let h=m,R="_replace_text_";o.ellipsis=!0,o.customRender=({text:D,record:U})=>{let L=D;if(h)if(h.startsWith(R)){let ee=h.replace(R,"");L=U[ee]}else L=Rt(Ae(u)[h],D+"");if(o.showLength&&L&&L.length>o.showLength&&(L=L.substr(0,o.showLength)+"..."),v){let ee=r[v];if(ee)return _e("a",{onClick:()=>B(ee,U)},L)}return L}}if(o.scopedSlots){o.ellipsis=!0;let h=o.scopedSlots;o.slots=h,delete o.scopedSlots}}}return l}function B(l,r){let a=l.href,o=/(ht|f)tp(s?)\:\/\/[0-9a-zA-Z]([-.\w]*[0-9a-zA-Z])*(:(0-9)*)*(\/?)([a-zA-Z0-9\-\.\?\,\'\/\\\+&amp;%\$#_]*)?/,m=/\.vue(\?.*)?$/,v=/{{([^}]+)}}/g;if(typeof a=="string")if(a.startsWith("ONLINE:")){let F=a.split(":");b.value=F[1];let h=F[2];T(!0,{isUpdate:!0,disableSubmit:!0,hideSub:!0,record:{id:r[h]}})}else a=a.trim().replace(/\${([^}]+)?}/g,(F,h)=>r[h]),v.test(a)&&(a=a.replace(v,function(F,h){try{return h.trim()==="ACCESS_TOKEN"?Pt():at(h)}catch(R){return F}})),o.test(a)?window.open(a,"_blank"):m.test(a)?V(a):t.push(a)}const j=Ce({model:{title:"",okText:"关闭",width:"100%",open:!1,destroyOnClose:!0,style:{top:0,left:0,height:"100%",margin:0,padding:0},bodyStyle:{padding:"8px",height:"calc(100vh - 108px)",overflow:"auto",overflowX:"hidden"},cancelButtonProps:{style:{display:"none"}}},on:{ok:()=>j.model.open=!1,cancel:()=>j.model.open=!1},is:null,params:{}});function V(l){let r=l.indexOf("?"),a=l;if(r!==-1){a=l.substring(0,r);let o=l.substring(r+1,l.length).split("&"),m={};o.forEach(v=>{let F=v.split("=");m[F[0]]=F[1]}),j.params=m}else j.params={};j.model.open=!0,j.model.title="操作",j.is=Li($i(()=>Ri(a)))}let z="left";i.isTree()&&(z="right");const q=Ce({title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:z,align:"center",width:150});ge(()=>e==null?void 0:e.value,()=>{var l,r;((l=e==null?void 0:e.value)==null?void 0:l.tableFixedAction)===1&&(q.fixed=((r=e==null?void 0:e.value)==null?void 0:r.tableFixedActionType)||"right",i.isTree()&&(q.fixed="right"))});function X(l){let r=!1;for(let a=0;a<l.length;a++)if(l[a].dataIndex.toLowerCase()=="bpm_status"){r=!0;break}return i.hasBpmStatus=r,r}function x(l,r,a,o){if(l)if(l.indexOf(",")>0)Mi(`/online/cgform/field/download/${o}/${r.id}/${a.dataIndex}`,`文件_${r.id}.zip`);else{const m=Ve(l);window.open(m)}}function k(l){return l&&l.indexOf(",")>0&&(l=yt(l)[0]),Ve(l)}function w(l){return l?Ei(l):""}function S(l,r){if(!l)return"";let a=l;a.length>10&&(a=a.substring(0,10));let o=r==null?void 0:r.fieldExtendJson;return o&&(o=JSON.parse(o),o.picker&&o.picker!="default")?Bi(a)[o.picker]:a}ge(s,()=>{i.selectedRowKeys=be(s.value)}),i.clearSelectedRow=()=>{s.value=[],i.selectedRows=[],i.selectedRowKeys=[]};function P(l){if(l){let r=[];const a=yt(l);for(let o of a)o&&r.push(Ve(o));ji({imageList:r})}}const _=I();function d(l,r,a){return K(this,null,function*(){N.value=r,(yield _.value.getFormStatus())==!0?(b.value=r,T(!0,{isUpdate:!0,disableSubmit:!0,hideSub:!0,record:{id:l}})):C(!0,{isUpdate:!0,disableSubmit:!!a,record:{id:l}})})}return{columns:n,actionColumn:q,selectedKeys:s,rowSelection:c,enableScrollBar:y,tableScroll:p,downloadRowFile:x,getImgView:k,getPcaText:w,getFormatDate:S,handleColumnResult:E,onSelectChange:H,hrefComponent:j,viewOnlineCellImage:P,hrefMainTableId:b,registerOnlineHrefModal:A,registerPopModal:O,openPopModal:C,openOnlineHrefModal:T,onlinePopModalRef:_,popTableId:N,handleClickFieldHref:B}}const Il=Mt({name:"OnlinePopListModal",props:{id:{type:String,default:""},multi:{type:Boolean,default:!1},addAuth:{type:Boolean,default:!0}},components:{BasicModal:jt,BasicTable:en,TableAction:xi,PlusOutlined:rt,OnlinePopModal:We},emits:["success","register"],setup(i,{emit:e}){const{createMessage:t}=Re(),{popModalFixedWidth:n,resetBodyStyle:u,popBodyStyle:s}=ti(),c=I(""),y=I(800),[p,{closeModal:A}]=Dt(Q=>{c.value="",m.value=Q.selectedRowKeys,v.value=Q.selectedRows,a({current:1}),r(),u()}),[T,{openModal:b}]=Pe();function O(){A()}const C=pe(()=>{const Q=m.value;return!(Q&&Q.length>0)}),N=I(!1);function E(){N.value=!0;let Q=be(v.value);Q&&Q.length>0&&(e("success",Q),A()),setTimeout(()=>{N.value=!1},200)}function H(Q){const W="/online/cgform/api/getData/"+i.id;return me.get({url:W,params:Q})}function J(Q){return Q.column="id",new Promise((W,G)=>K(this,null,function*(){const te=yield H(Q);W(te)}))}const B={isPopList:!0,reloadTable(){},isTree(){return!1}},j=I({}),{columns:V,downloadRowFile:z,getImgView:q,getPcaText:X,getFormatDate:x,handleColumnResult:k,hrefComponent:w,viewOnlineCellImage:S}=kl(B,j);function P(){const Q="/online/cgform/api/getColumns/"+i.id;return new Promise((W,G)=>{me.get({url:Q},{isTransformResponse:!1}).then(te=>{te.success?W(te.result):(t.warning(te.message),G())})})}const _=I("");ge(()=>i.id,()=>K(this,null,function*(){let Q=yield P();k(Q),_.value=Q.description}),{immediate:!0});const{tableContext:d}=Ki({designScope:"process-design",pagination:!0,tableProps:{title:"",api:J,clickToRowSelect:!0,columns:V,showTableSetting:!1,immediate:!1,canResize:!1,showActionColumn:!1,actionColumn:{dataIndex:"action",slots:{customRender:"action"}},useSearchForm:!1,beforeFetch:Q=>U(Q)}}),[l,{reload:r,setPagination:a},{rowSelection:o,selectedRowKeys:m,selectedRows:v}]=d;ge(()=>i.multi,Q=>{Q==!0?o.type="checkbox":o.type="radio"},{immediate:!0});function F(Q){return[{label:"编辑",onClick:h.bind(null,Q)}]}function h(Q){}function R(){r()}const D=["int","double","Date","Datetime","BigDecimal"];function U(Q){let W=c.value;if(!W)return Q.superQueryMatchType="or",Q.superQueryParams="",Q;let G=V.value,te=[];if(G&&G.length>0)for(let ne of G)ne.dbType&&(ne.dbType=="string"?te.push({field:ne.dataIndex,type:ne.dbType.toLowerCase(),rule:"like",val:W}):ne.dbType=="Date"?W.length==10&&te.push({field:ne.dataIndex,type:ne.dbType.toLowerCase(),rule:"eq",val:W}):ne.dbType=="Datetime"?W.length==19&&te.push({field:ne.dataIndex,type:ne.dbType.toLowerCase(),rule:"eq",val:W}):D.indexOf(ne.dbType)&&te.push({field:ne.dataIndex,type:ne.dbType.toLowerCase(),rule:"eq",val:W}));return Q.superQueryMatchType="or",Q.superQueryParams=encodeURI(JSON.stringify(te)),Q}function L(){b(!0,{})}function ee(Q){e("success",[Q]),A()}return{registerModal:p,modalWidth:y,handleCancel:O,submitDisabled:C,submitLoading:N,handleSubmit:E,registerTable:l,getTableAction:F,searchText:c,onSearch:R,downloadRowFile:z,getImgView:q,getPcaText:X,getFormatDate:x,hrefComponent:w,viewOnlineCellImage:S,rowSelection:o,modalTitle:_,registerPopModal:T,handleAdd:L,reload:r,popModalFixedWidth:n,popBodyStyle:s,handleDataSave:ee}}}),Pl={style:{display:"inline-block",width:"calc(100% - 140px)","text-align":"left"}},Dl={key:0,style:{"font-size":"12px","font-style":"italic"}},Rl={key:0,style:{"font-size":"12px","font-style":"italic"}},Ml=["src","onClick"],El=["innerHTML"],Bl=["title"];function jl(i,e,t,n,u,s){const c=le("PlusOutlined"),y=le("a-button"),p=le("a-input-search"),A=le("TableAction"),T=le("BasicTable"),b=le("BasicModal"),O=le("online-pop-modal");return ie(),de(He,null,[ce(b,{onRegister:i.registerModal,width:i.popModalFixedWidth,dialogStyle:{top:"70px"},bodyStyle:i.popBodyStyle,title:i.modalTitle,wrapClassName:"jeecg-online-pop-list-modal"},{footer:ae(()=>[fe("div",Pl,[i.addAuth?(ie(),ke(y,{key:0,style:{"border-radius":"50px"},type:"primary",onClick:i.handleAdd},{default:ae(()=>[ce(c),e[1]||(e[1]=Fe("新增记录"))]),_:1},8,["onClick"])):Te("",!0)]),ce(y,{key:"back",onClick:i.handleCancel},{default:ae(()=>e[2]||(e[2]=[Fe("关闭")])),_:1},8,["onClick"]),ce(y,{disabled:i.submitDisabled,key:"submit",type:"primary",onClick:i.handleSubmit,loading:i.submitLoading},{default:ae(()=>e[3]||(e[3]=[Fe("确定")])),_:1},8,["disabled","onClick","loading"])]),default:ae(()=>[ce(T,{onRegister:i.registerTable,rowSelection:i.rowSelection},{tableTitle:ae(()=>[ce(p,{value:i.searchText,"onUpdate:value":e[0]||(e[0]=C=>i.searchText=C),onSearch:i.onSearch,placeholder:"请输入关键词，按回车搜索",style:{width:"240px"}},null,8,["value","onSearch"])]),action:ae(({record:C})=>[ce(A,{actions:i.getTableAction(C)},null,8,["actions"])]),fileSlot:ae(({text:C})=>[C?(ie(),ke(y,{key:1,ghost:!0,type:"primary",preIcon:"ant-design:download",size:"small",onClick:N=>i.downloadRowFile(C)},{default:ae(()=>e[4]||(e[4]=[Fe(" 下载 ")])),_:2},1032,["onClick"])):(ie(),de("span",Dl,"无文件"))]),imgSlot:ae(({text:C})=>[C?(ie(),de("img",{key:1,src:i.getImgView(C),alt:"图片不存在",class:"online-cell-image",onClick:N=>i.viewOnlineCellImage(C)},null,8,Ml)):(ie(),de("span",Rl,"无图片"))]),htmlSlot:ae(({text:C})=>[fe("div",{innerHTML:C},null,8,El)]),pcaSlot:ae(({text:C})=>[fe("div",{title:i.getPcaText(C)},Oe(i.getPcaText(C)),9,Bl)]),dateSlot:ae(({text:C,column:N})=>[fe("span",null,Oe(i.getFormatDate(C,N)),1)]),_:1},8,["onRegister","rowSelection"])]),_:1},8,["onRegister","width","bodyStyle","title"]),ce(O,{id:i.id,onRegister:i.registerPopModal,onSuccess:i.handleDataSave,topTip:""},null,8,["id","onRegister","onSuccess"])],64)}const li=Ee(Il,[["render",jl]]),ws=Object.freeze(Object.defineProperty({__proto__:null,default:li},Symbol.toStringTag,{value:"Module"})),_l={name:"LinkTableCard",props:{valueField:se.string.def(""),textField:se.string.def(""),tableName:se.string.def(""),multi:se.bool.def(!1),value:se.oneOfType([se.string,se.number]),linkFields:se.array.def([]),disabled:se.bool.def(!1),detail:se.bool.def(!1),imageField:se.string.def("")},components:{PlusOutlined:rt,MinusCircleFilled:qi,OnlinePopListModal:li,OnlinePopModal:We},emits:["change","update:value"],setup(i,{emit:e}){const t=pe(()=>i.tableName),[n,{openModal:u}]=Pe(),[s,{openModal:c}]=Pe(),y=I([]),p=I([]),A=I(null),T=I(0),b=pe(()=>!(i.disabled==!0||i.multi===!1&&p.value.length>0)),{auths:O,otherColumns:C,realShowColumns:N,tableColumns:E,textFieldArray:H,transData:J,loadOne:B,compareData:j,formatData:V,initFormData:z,getImageSrc:q,showImage:X}=ii(i),x=pe(()=>i.multi===!0?12:24),k=pe(()=>i.multi===!0?24:12);function w(o){if(o&&H.value.length>0){let m=H.value[0];return o[m]}}function S(o){o==null||o.stopPropagation(),o==null||o.preventDefault()}function P(o,m){S(o),O.update!=!1&&i.disabled==!1&&c(!0,{isUpdate:!0,record:m})}function _(o){u(!0,{selectedRowKeys:p.value.map(m=>m.id),selectedRows:[...p.value]})}function d(o){let m=[];for(let v of o){let F=he({},v);J(F),m.push(F)}p.value=m,a()}function l(o){let m=p.value;for(let v=0;v<m.length;v++)if(m[v].id===o.id){let F=he({},o);J(F),m.splice(v,1,F)}p.value=m,a()}function r(o,m){S(o);let v=p.value;v&&v.length>m&&(v.splice(m,1),p.value=v),a()}function a(){let o=p.value,m=[],v={},F=i.linkFields;if(o.length>0)for(let R=0;R<o.length;R++)m.push(o[R][i.valueField]),z(v,F,o[R]);else z(v,F);let h=m.join(",");V(v),e("change",h,v),e("update:value",h)}return ge(()=>i.value,o=>K(this,null,function*(){if(o){if(j(p.value,o)===!1){let m=yield B(o);p.value=m}i.linkFields&&i.linkFields.length>0&&a()}else p.value=[]}),{immediate:!0}),Bt(()=>{A.value.offsetWidth<250&&(T.value=24)}),{popTableName:t,selectRecords:p,otherColumns:C,realShowColumns:N,showButton:b,selectValue:y,handleAddRecord:_,handleDeleteRecord:r,getMainContent:w,itemSpan:x,columnSpan:k,tableColumns:E,addCard:d,registerListModal:n,registerFormModal:s,handleClickEdit:P,updateCardData:l,getImageSrc:q,showImage:X,auths:O,tableLinkCardRef:A,fixedSpan:T,placeholderImage:qe,handleImageError:o=>{o.target.src=qe}}}},Ql={ref:"tableLinkCardRef"},Nl={class:"table-link-card"},Jl={style:{width:"100%",height:"100%"}},Ll={key:0,class:"card-button"},$l=["onClick"],Vl={key:0,class:"card-delete"},Ul={class:"card-inner"},Hl={class:"card-main-content"},Yl={class:"other-content"},ql={class:"label ellipsis"},Wl={class:"text ellipsis"},Kl={key:0,class:"card-item-image"},zl=["src"];function Xl(i,e,t,n,u,s){const c=le("PlusOutlined"),y=le("a-button"),p=le("minus-circle-filled"),A=le("a-col"),T=le("a-row"),b=le("online-pop-list-modal"),O=le("online-pop-modal");return ie(),de("div",Ql,[fe("div",Nl,[fe("div",Jl,[n.showButton?(ie(),de("div",Ll,[ce(y,{onClick:n.handleAddRecord},{default:ae(()=>[ce(c),e[1]||(e[1]=Fe("记 录"))]),_:1},8,["onClick"])])):Te("",!0),ce(T,null,{default:ae(()=>[(ie(!0),de(He,null,lt(n.selectRecords,(C,N)=>(ie(),ke(A,{span:n.fixedSpan?n.fixedSpan:n.itemSpan},{default:ae(()=>[fe("div",{class:Ue(["card-item",{"disabled-chunk":t.detail==!0}]),onClick:E=>n.handleClickEdit(E,C)},[fe("div",{class:Ue(["card-item-left",{"show-right-image":n.getImageSrc(C)}])},[t.disabled==!1?(ie(),de("span",Vl,[ce(p,{onClick:E=>n.handleDeleteRecord(E,N)},null,8,["onClick"])])):Te("",!0),fe("div",Ul,[fe("div",Hl,Oe(n.getMainContent(C)),1),fe("div",Yl,[ce(T,null,{default:ae(()=>[(ie(!0),de(He,null,lt(n.realShowColumns,E=>(ie(),ke(A,{span:n.columnSpan},{default:ae(()=>[fe("span",ql,Oe(E.title),1),fe("span",Wl,Oe(C[E.dataIndex]),1)]),_:2},1032,["span"]))),256))]),_:2},1024)])])],2),n.getImageSrc(C)?(ie(),de("div",Kl,[n.getImageSrc(C)?(ie(),de("img",{key:0,src:n.getImageSrc(C),alt:"",onError:e[0]||(e[0]=(...E)=>n.handleImageError&&n.handleImageError(...E))},null,40,zl)):Te("",!0)])):Te("",!0)],10,$l)]),_:2},1032,["span"]))),256))]),_:1})])]),ce(b,{onRegister:n.registerListModal,multi:t.multi,id:n.popTableName,addAuth:n.auths.add,onSuccess:n.addCard},null,8,["onRegister","multi","id","addAuth","onSuccess"]),ce(O,{id:n.popTableName,onRegister:n.registerFormModal,onSuccess:n.updateCardData,topTip:""},null,8,["id","onRegister","onSuccess"])],512)}const oi=Ee(_l,[["render",Xl],["__scopeId","data-v-6c31f866"]]),Ss=Object.freeze(Object.defineProperty({__proto__:null,default:oi},Symbol.toStringTag,{value:"Module"})),Tt={};function Gl(){i("OnlineSelectCascade",Xi),i("LinkTableSelect",ni),i("LinkTableCard",oi);function i(t,n){Tt[t]||(Fi(t,n),Tt[t]=1)}function e(t){t.component=="LinkTableCard"&&(t.component="LinkTableSelect",t.componentProps.popContainer="body")}return{addComponent:i,linkTableCard2Select:e}}export{hs as E,et as F,oi as L,We as O,an as S,je as V,kl as a,fl as b,Zt as c,il as d,Xt as e,cs as f,Me as g,rn as h,Gl as i,ye as j,gs as k,st as l,bs as m,fs as n,ms as o,pt as p,nl as q,xt as r,ps as s,vs as t,al as u,ys as v,As as w,Cs as x,ws as y,Ss as z};
