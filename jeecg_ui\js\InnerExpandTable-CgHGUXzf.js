import{d as M,f as s,ag as g,aB as S,ar as E,aD as r,k as i,u as o,G as D}from"./vue-vendor-dy9k-Yad.js";import{ad as L,j as p,a as U}from"./index-CCWaWN5g.js";import{u as V}from"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import j from"./JVxeTableModal-Dn6H7AVC.js";import{Q as z}from"./componentMap-Bkie1n3v.js";import x from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./jvxetable.api-C3sIybRa.js";import"./useJvxeMethods-CdpRH_1y.js";import"./vxeUtils-B1NxCh07.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const A=M({__name:"InnerExpandTable",setup(N){const a={list:"/test/order/orderList",delete:"/test/order/delete",deleteBatch:"/test/order/deleteBatch",customerListByMainId:"/test/order/listOrderCustomerByMainId"},d=s([]),m=s([]),l=s([]),h=[{title:"订单号",align:"center",dataIndex:"orderCode",width:100},{title:"订单类型",align:"center",dataIndex:"ctype",width:100,customRender:({text:e})=>{let t="";return e==="1"?t="国内订单":e==="2"&&(t="国际订单"),t}},{title:"订单日期",align:"center",width:100,dataIndex:"orderDate"},{title:"订单金额",align:"center",dataIndex:"orderMoney",width:100},{title:"订单备注",align:"center",dataIndex:"content",width:100}],b=[{title:"客户名",align:"center",width:100,dataIndex:"name",key:"name"},{title:"性别",align:"center",dataIndex:"sex",customRender:function(e){return e.value=="1"?"男":e.value=="2"?"女":e}},{title:"身份证号码",align:"center",dataIndex:"idcard"},{title:"电话",dataIndex:"telphone",align:"center"}],_=e=>p.get({url:a.list,params:e}),[w,{openModal:c}]=L(),[I,{reload:u}]=V({columns:h,api:_,rowKey:"id",striped:!0,useSearchForm:!1,showTableSetting:!0,clickToRowSelect:!1,bordered:!0,actionColumn:{width:110,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:void 0}}),y={type:"checkbox",columnWidth:30,selectedRowKeys:m,onChange:R};function R(e){m.value=e}function v(e,t){d.value=[],l.value=[],e===!0&&(d.value.push(t.id),p.get({url:a.customerListByMainId,params:{orderId:t.id}},{isTransformResponse:!1}).then(n=>{n.success&&(l.value=n.result.records)}))}function C(){c(!0,{isUpdate:!1})}function T(e){c(!0,{record:e,isUpdate:!0})}function k(e){p.delete({url:a.delete,data:{id:e.id}},{joinParamsToUrl:!0}).then(()=>{u()})}function B(e){return[{label:"编辑",onClick:T.bind(null,e)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:k.bind(null,e)}}]}return(e,t)=>{const n=g("a-button"),K=g("a-card");return E(),S(K,{bordered:!1},{default:r(()=>[i(o(x),{onRegister:o(I),expandedRowKeys:d.value,rowSelection:y,onExpand:v},{tableTitle:r(()=>[i(n,{type:"primary",onClick:C,preIcon:"ant-design:plus-outlined"},{default:r(()=>t[1]||(t[1]=[D(" 新增")])),_:1,__:[1]})]),expandedRowRender:r(()=>[i(o(x),{bordered:"",size:"middle",rowKey:"id",canResize:!1,columns:b,dataSource:l.value,pagination:!1},null,8,["dataSource"])]),action:r(({record:f})=>[i(o(z),{actions:B(f)},null,8,["actions"])]),_:1},8,["onRegister","expandedRowKeys"]),i(j,{onRegister:o(w),onSuccess:t[0]||(t[0]=f=>o(u)())},null,8,["onRegister"])]),_:1})}}}),We=U(A,[["__scopeId","data-v-eb82fc77"]]);export{We as default};
