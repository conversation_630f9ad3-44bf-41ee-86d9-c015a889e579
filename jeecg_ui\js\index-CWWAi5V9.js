var _=Object.defineProperty;var y=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable;var m=(t,a,e)=>a in t?_(t,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[a]=e,p=(t,a)=>{for(var e in a||(a={}))$.call(a,e)&&m(t,e,a[e]);if(y)for(var e of y(a))T.call(a,e)&&m(t,e,a[e]);return t};import{bd as g,be as B,P as C,a as E,w as F}from"./index-CCWaWN5g.js";import{d as k,f as S,e as P,u as q,h as z,w as A,o as D,aq as I,ar as M,aA as j,au as G}from"./vue-vendor-dy9k-Yad.js";const H={startVal:{type:Number,default:0},endVal:{type:Number,default:2021},duration:{type:Number,default:1500},autoplay:{type:Boolean,default:!0},decimals:{type:Number,default:0,validator(t){return t>=0}},prefix:{type:String,default:""},suffix:{type:String,default:""},separator:{type:String,default:","},decimal:{type:String,default:"."},color:{type:String},useEasing:{type:Boolean,default:!0},transition:{type:String,default:"linear"}},J=k({name:"CountTo",props:H,emits:["onStarted","onFinished"],setup(t,{emit:a}){const e=S(t.startVal),u=S(!1);let o=g(e);const d=P(()=>h(q(o)));z(()=>{e.value=t.startVal}),A([()=>t.startVal,()=>t.endVal],()=>{t.autoplay&&s()}),D(()=>{t.autoplay&&s()});function s(){f(),e.value=t.endVal}function b(){e.value=t.startVal,f()}function f(){o=g(e,p({disabled:u,duration:t.duration,onFinished:()=>a("onFinished"),onStarted:()=>a("onStarted")},t.useEasing?{transition:B[t.transition]}:{}))}function h(n){if(!n&&n!==0)return"";const{decimals:x,decimal:V,separator:i,suffix:N,prefix:v}=t;n=Number(n).toFixed(x),n+="";const l=n.split(".");let r=l[0];const w=l.length>1?V+l[1]:"",c=/(\d+)(\d{3})/;if(i&&!C(i))for(;c.test(r);)r=r.replace(c,"$1"+i+"$2");return v+r+w+N}return{value:d,start:s,reset:b}}});function K(t,a,e,u,o,d){return M(),I("span",{style:j({color:t.color})},G(t.value),5)}const L=E(J,[["render",K]]),U=F(L);export{U as C};
