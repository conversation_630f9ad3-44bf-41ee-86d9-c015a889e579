import{d as r,f as v,ag as l,aB as p,ar as u,aD as t,at as o,k as e,u as f}from"./vue-vendor-dy9k-Yad.js";import{P as y}from"./index-CtJ0w2CP.js";import{T as g}from"./index-ByPySmGo.js";import{a as h}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const T={class:"p-4"},w={class:"mb-4"},_={class:"mb-4"},x={class:"mt-4"},k=["innerHTML"],b=r({__name:"font-demo",setup(N){const a=v(`
<p style="font-family: 'PingFang Medium'; font-size: 16px;">
  欢迎测试字体功能！请选择不同的字体来体验效果。
</p>
`),m=d=>{};return(d,s)=>{const i=l("a-alert"),n=l("a-card");return u(),p(f(y),{title:"Tiptap字体演示"},{default:t(()=>[o("div",T,[e(n,{title:"字体展示"},{default:t(()=>[o("div",w,[e(i,{message:"字体演示说明",description:"以下展示了Tiptap富文本编辑器支持的所有字体，包括系统字体和项目本地字体。点击下方编辑器中的字体下拉菜单可以选择这些字体。",type:"info","show-icon":""})]),s[1]||(s[1]=o("div",{class:"font-showcase"},[o("h3",null,"系统字体"),o("div",{class:"font-group"},[o("div",{class:"font-item"},[o("div",{class:"font-name"},"默认字体"),o("div",{class:"font-sample",style:{"font-family":"inherit"}}," 这是默认字体的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"宋体 (SimSun)"),o("div",{class:"font-sample",style:{"font-family":"SimSun"}}," 这是宋体的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"黑体 (SimHei)"),o("div",{class:"font-sample",style:{"font-family":"SimHei"}}," 这是黑体的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"微软雅黑 (Microsoft YaHei)"),o("div",{class:"font-sample",style:{"font-family":"Microsoft YaHei"}}," 这是微软雅黑的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"楷体 (KaiTi)"),o("div",{class:"font-sample",style:{"font-family":"KaiTi"}}," 这是楷体的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"仿宋 (FangSong)"),o("div",{class:"font-sample",style:{"font-family":"FangSong"}}," 这是仿宋的演示文本 - The quick brown fox jumps over the lazy dog ")])]),o("h3",null,"苹方字体系列"),o("div",{class:"font-group"},[o("div",{class:"font-item"},[o("div",{class:"font-name"},"苹方常规 (PingFang Regular)"),o("div",{class:"font-sample",style:{"font-family":"'PingFang Regular'"}}," 这是苹方常规的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"苹方中等 (PingFang Medium)"),o("div",{class:"font-sample",style:{"font-family":"'PingFang Medium'"}}," 这是苹方中等的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"苹方加粗 (PingFang Bold)"),o("div",{class:"font-sample",style:{"font-family":"'PingFang Bold'"}}," 这是苹方加粗的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"苹方特粗 (PingFang Heavy)"),o("div",{class:"font-sample",style:{"font-family":"'PingFang Heavy'"}}," 这是苹方特粗的演示文本 - The quick brown fox jumps over the lazy dog ")])]),o("h3",null,"DIN字体系列"),o("div",{class:"font-group"},[o("div",{class:"font-item"},[o("div",{class:"font-name"},"DIN常规 (DIN Regular)"),o("div",{class:"font-sample",style:{"font-family":"'DIN Regular'"}}," 这是DIN常规的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"DIN加粗 (DIN Bold)"),o("div",{class:"font-sample",style:{"font-family":"'DIN Bold'"}}," 这是DIN加粗的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"DIN黑斜体 (DIN BlackItalic)"),o("div",{class:"font-sample",style:{"font-family":"'DIN BlackItalic'"}}," 这是DIN黑斜体的演示文本 - The quick brown fox jumps over the lazy dog ")])]),o("h3",null,"特殊字体"),o("div",{class:"font-group"},[o("div",{class:"font-item"},[o("div",{class:"font-name"},"方正综艺 (FZZongYi-M05S)"),o("div",{class:"font-sample",style:{"font-family":"'FZZongYi-M05S'"}}," 这是方正综艺的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"优设标题黑 (YouSheBiaoTiHei)"),o("div",{class:"font-sample",style:{"font-family":"'YouSheBiaoTiHei'"}}," 这是优设标题黑的演示文本 - The quick brown fox jumps over the lazy dog ")])]),o("h3",null,"英文字体"),o("div",{class:"font-group"},[o("div",{class:"font-item"},[o("div",{class:"font-name"},"Arial"),o("div",{class:"font-sample",style:{"font-family":"Arial"}}," 这是Arial的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"Times New Roman"),o("div",{class:"font-sample",style:{"font-family":"'Times New Roman'"}}," 这是Times New Roman的演示文本 - The quick brown fox jumps over the lazy dog ")]),o("div",{class:"font-item"},[o("div",{class:"font-name"},"Courier New"),o("div",{class:"font-sample",style:{"font-family":"'Courier New'"}}," 这是Courier New的演示文本 - The quick brown fox jumps over the lazy dog ")])])],-1))]),_:1,__:[1]}),e(n,{title:"实际编辑测试",class:"mt-4"},{default:t(()=>[o("div",_,[e(i,{message:"编辑器测试",description:"在下方编辑器中测试字体选择功能。点击工具栏中的字体下拉菜单，选择不同的字体进行测试。",type:"success","show-icon":""})]),e(f(g),{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=c=>a.value=c),height:"300px",placeholder:"请在这里测试字体选择功能...",onChange:m},null,8,["modelValue"]),o("div",x,[s[2]||(s[2]=o("h4",null,"当前内容：",-1)),o("div",{class:"content-preview",innerHTML:a.value},null,8,k)])]),_:1})])]),_:1})}}}),C=h(b,[["__scopeId","data-v-8f1bdae7"]]);export{C as default};
