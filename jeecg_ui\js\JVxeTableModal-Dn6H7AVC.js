var N=Object.defineProperty,Y=Object.defineProperties;var E=Object.getOwnPropertyDescriptors;var D=Object.getOwnPropertySymbols;var z=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var O=(e,t,l)=>t in e?N(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,L=(e,t)=>{for(var l in t||(t={}))z.call(t,l)&&O(e,l,t[l]);if(D)for(var l of D(t))I.call(t,l)&&O(e,l,t[l]);return e},U=(e,t)=>Y(e,E(t));var T=(e,t,l)=>new Promise((p,f)=>{var v=r=>{try{s(l.next(r))}catch(c){f(c)}},m=r=>{try{s(l.throw(r))}catch(c){f(c)}},s=r=>r.done?p(r.value):Promise.resolve(r.value).then(v,m);s((l=l.apply(e,t)).next())});import{d as P,f as g,r as h,e as A,u as $,ag as u,aB as G,ar as X,aE as Q,aD as o,k as a,at as W,G as j}from"./vue-vendor-dy9k-Yad.js";import{B as Z}from"./index-Diw57m_E.js";import{bx as M,by as x,ac as ee,a as te}from"./index-CCWaWN5g.js";import{o as ae,a as oe,s as le}from"./jvxetable.api-C3sIybRa.js";import{useJvxeMethod as re}from"./useJvxeMethods-CdpRH_1y.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./vxeUtils-B1NxCh07.js";const ne=[{title:"客户名",key:"name",width:180,type:M.input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"性别",key:"sex",width:180,type:M.select,options:[{title:"男",value:"1"},{title:"女",value:"2"}],defaultValue:"",placeholder:"请选择${title}"},{title:"身份证号",key:"idcard",width:180,type:M.input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{pattern:"^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|[xX])$",message:"${title}格式不正确"}]},{title:"手机号",key:"telphone",width:180,type:M.input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{pattern:"^1[3456789]\\d{9}$",message:"${title}格式不正确"}]}],de=[{title:"航班号",key:"ticketCode",width:180,type:M.input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"航班时间",key:"tickectDate",width:180,type:M.date,placeholder:"请选择${title}",defaultValue:""}],se=P({name:"JVexTableModal",components:{BasicModal:Z,JVxeTable:x},emits:["success","register"],setup(e,{emit:t}){const l=g(300),p=g(!0),f=g(),v=g(),m=g(["tableRef1","tableRef2"]),s=g("tableRef1"),r={tableRef1:f,tableRef2:v},c=h({xs:{span:24},sm:{span:5}}),C=h({xs:{span:24},sm:{span:16}}),y=h({loading:!1,dataSource:[],columns:ne}),b=h({loading:!1,dataSource:[],columns:de}),n=h({id:null,orderCode:"",orderMoney:"",ctype:"",content:"",jeecgOrderCustomerList:[],jeecgOrderTicketList:[]}),[_,S,k,R]=re(q,K,r,s,m),[d,{setModalProps:V,closeModal:B}]=ee(i=>T(null,null,function*(){if(V({confirmLoading:!1}),H(),p.value=!!(i!=null&&i.isUpdate),$(p)){Object.assign(n,i.record);let w={id:n.id};k(ae,w,y),k(oe,w,b)}})),F={orderCode:[{required:!0,message:"订单号不能为空",trigger:"blur"}]},J=A(()=>$(p)?"编辑":"新增");function K(i){let w=Object.assign(n,i.formValue);return U(L({},w),{jeecgOrderCustomerList:i.tablesValue[0].tableData,jeecgOrderTicketList:i.tablesValue[1].tableData})}function H(){n.id=null,n.orderCode="",n.orderMoney="",n.orderDate=null,n.ctype="",n.content="",n.jeecgOrderCustomerList=[],n.jeecgOrderTicketList=[],y.dataSource=[],b.dataSource=[]}function q(i){return T(this,null,function*(){V({confirmLoading:!0}),yield le(i,$(p)),B(),t("success")})}return{formRef:R,activeKey:s,table1:y,table2:b,tableRef1:f,tableRef2:v,getTitle:J,labelCol:c,wrapperCol:C,validatorRules:F,orderMainModel:n,registerModal:d,handleChangeTabs:_,handleSubmit:S,handleFullScreen:i=>{l.value=i?document.documentElement.clientHeight-387:300},tableH:l}}}),ie={style:{"overflow-x":"hidden"}};function ue(e,t,l,p,f,v){const m=u("a-input"),s=u("a-form-item"),r=u("a-col"),c=u("a-select-option"),C=u("a-select"),y=u("a-date-picker"),b=u("a-row"),n=u("JVxeTable"),_=u("a-tab-pane"),S=u("a-tabs"),k=u("a-form"),R=u("BasicModal");return X(),G(R,Q(e.$attrs,{onRegister:e.registerModal,title:e.getTitle,onOk:e.handleSubmit,width:"70%",onFullScreen:e.handleFullScreen}),{default:o(()=>[a(k,{ref:"formRef",model:e.orderMainModel,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,rules:e.validatorRules},{default:o(()=>[W("div",ie,[a(b,{class:"form-row",gutter:16},{default:o(()=>[a(r,{lg:8},{default:o(()=>[a(s,{label:"订单号",name:"orderCode"},{default:o(()=>[a(m,{value:e.orderMainModel.orderCode,"onUpdate:value":t[0]||(t[0]=d=>e.orderMainModel.orderCode=d),placeholder:"请输入订单号"},null,8,["value"])]),_:1})]),_:1}),a(r,{lg:8},{default:o(()=>[a(s,{label:"订单类型",name:"ctype"},{default:o(()=>[a(C,{placeholder:"请选择订单类型",value:e.orderMainModel.ctype,"onUpdate:value":t[1]||(t[1]=d=>e.orderMainModel.ctype=d)},{default:o(()=>[a(c,{value:"1"},{default:o(()=>t[6]||(t[6]=[j("国内订单")])),_:1,__:[6]}),a(c,{value:"2"},{default:o(()=>t[7]||(t[7]=[j("国际订单")])),_:1,__:[7]})]),_:1},8,["value"])]),_:1})]),_:1}),a(r,{lg:8},{default:o(()=>[a(s,{label:"订单日期",name:"orderDate"},{default:o(()=>[a(y,{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss",value:e.orderMainModel.orderDate,"onUpdate:value":t[2]||(t[2]=d=>e.orderMainModel.orderDate=d)},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(b,{class:"form-row",gutter:16},{default:o(()=>[a(r,{lg:8},{default:o(()=>[a(s,{label:"订单金额",name:"orderMoney"},{default:o(()=>[a(m,{value:e.orderMainModel.orderMoney,"onUpdate:value":t[3]||(t[3]=d=>e.orderMainModel.orderMoney=d),placeholder:"请输入订单金额"},null,8,["value"])]),_:1})]),_:1}),a(r,{lg:8},{default:o(()=>[a(s,{label:"订单备注",name:"content"},{default:o(()=>[a(m,{value:e.orderMainModel.content,"onUpdate:value":t[4]||(t[4]=d=>e.orderMainModel.content=d),placeholder:"请输入订单备注"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),a(S,{activeKey:e.activeKey,"onUpdate:activeKey":t[5]||(t[5]=d=>e.activeKey=d),onChange:e.handleChangeTabs},{default:o(()=>[a(_,{tab:"客户信息",key:"tableRef1"},{default:o(()=>[a(n,{ref:"tableRef1",stripe:"",toolbar:"",rowNumber:"",rowSelection:"",resizable:"",keepSource:"",height:e.tableH,"checkbox-config":{range:!0},loading:e.table1.loading,columns:e.table1.columns,dataSource:e.table1.dataSource},null,8,["height","loading","columns","dataSource"])]),_:1}),a(_,{tab:"机票信息",key:"tableRef2",forceRender:""},{default:o(()=>[a(n,{ref:"tableRef2",stripe:"",toolbar:"",rowNumber:"",rowSelection:"",resizable:"",keepSource:"",height:e.tableH,"checkbox-config":{range:!0},loading:e.table2.loading,columns:e.table2.columns,dataSource:e.table2.dataSource},null,8,["height","loading","columns","dataSource"])]),_:1})]),_:1},8,["activeKey","onChange"])]),_:1},8,["model","label-col","wrapper-col","rules"])]),_:1},16,["onRegister","title","onOk","onFullScreen"])}const Oe=te(se,[["render",ue]]);export{Oe as default};
