import{d as m,f as u,ap as v,aq as _,ar as T}from"./vue-vendor-dy9k-Yad.js";import{c1 as c,r as l,bN as d,c2 as p,ah as y,u as L,N as O,bf as E,j as q,c3 as x,aZ as w,bo as N,c4 as S}from"./index-CCWaWN5g.js";import{d as b}from"./index-5UjQUK5f.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const $=m({__name:"OAuth2Login",setup(B){const g=u(c()),t=u({thirdApp:!1,wxWork:!1,dingtalk:!1}),{currentRoute:f}=v(),a=f.value;c()||l.replace({path:d.BASE_LOGIN,query:a.query}),g.value&&h();function h(){/wxwork/i.test(navigator.userAgent)&&(t.value.thirdApp=!0,t.value.wxWork=!0),/dingtalk/i.test(navigator.userAgent)&&(t.value.thirdApp=!0,t.value.dingtalk=!0),k()}function k(){if(t.value.thirdApp)if(a.query.oauth2LoginToken){let s=a.query.oauth2LoginToken;A({token:s,thirdType:a.query.thirdType,tenantId:N})}else t.value.wxWork?p("wechat_enterprise"):t.value.dingtalk&&I()}function A(s){const o=y(),{notification:e}=L(),{t:i}=O();o.ThirdLogin(s).then(n=>{n&&n.userInfo?e.success({message:i("sys.login.loginSuccessTitle"),description:`${i("sys.login.loginSuccessDesc")}: ${n.userInfo.realname}`,duration:3}):e.error({message:"登录失败",description:((n.response||{}).data||{}).message||n.message||"请求出现错误，请稍后再试",duration:4})})}function I(){let o=`/sys/thirdLogin/get/corpId/clientId?tenantId=${E(S)||0}`;q.get({url:o},{isTransformResponse:!1}).then(e=>{e.success&&e.result&&e.result.corpId&&e.result.clientId?b.requestAuthCode({corpId:e.result.corpId,clientId:e.result.clientId}).then(i=>{let{code:n}=i;x(n)}):r()}).catch(e=>{r()})}function r(){w()?l.replace({path:d.BASE_HOME}):p("dingtalk")}return(s,o)=>(T(),_("div"))}});export{$ as default};
