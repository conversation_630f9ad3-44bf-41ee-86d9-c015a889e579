import{u as a,j as r}from"./index-CCWaWN5g.js";const s=[{title:"创建/修改时的用户id",align:"center",dataIndex:"userId"},{title:"证件号",align:"center",dataIndex:"cartId"},{title:"真实姓名",align:"center",dataIndex:"name"},{title:"审核状态（1待审核，2已通过，3,未通过）",align:"center",dataIndex:"review"},{title:"审核未通过说明",align:"center",dataIndex:"notes"},{title:"创建时间",align:"center",dataIndex:"createTime"},{title:"更新时间",align:"center",dataIndex:"updateTime"},{title:"创建人",align:"center",dataIndex:"createBy"},{title:"更新人",align:"center",dataIndex:"updateBy"},{title:"手机号",align:"center",dataIndex:"phone"},{title:"证件类型(1:身份证,other:其他)",align:"center",dataIndex:"cartType"},{title:"租户id",align:"center",dataIndex:"tenantId"},{title:"审核人",align:"center",dataIndex:"reviewUser"}],d=[{label:"证件号",field:"cartId",component:"Input"},{label:"审核状态（1待审核，2已通过，3,未通过）",field:"review",component:"InputNumber"},{label:"证件类型(1:身份证,other:其他)",field:"cartType",component:"InputNumber"}],l=[{label:"证件号",field:"cartId",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入证件号!"}]},{label:"真实姓名",field:"name",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入真实姓名!"}]},{label:"审核状态（1待审核，2已通过，3,未通过）",field:"review",component:"InputNumber"},{label:"审核未通过说明",field:"notes",component:"Input"},{label:"手机号",field:"phone",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入手机号!"}]},{label:"证件类型(1:身份证,other:其他)",field:"cartType",component:"InputNumber",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入证件类型(1:身份证,other:其他)!"}]},{label:"",field:"id",component:"Input",show:!1}],c={userId:{title:"创建/修改时的用户id",order:0,view:"text",type:"string"},cartId:{title:"证件号",order:1,view:"text",type:"string"},name:{title:"真实姓名",order:2,view:"text",type:"string"},review:{title:"审核状态（1待审核，2已通过，3,未通过）",order:3,view:"number",type:"number"},notes:{title:"审核未通过说明",order:4,view:"text",type:"string"},createTime:{title:"创建时间",order:5,view:"datetime",type:"string"},updateTime:{title:"更新时间",order:6,view:"datetime",type:"string"},createBy:{title:"创建人",order:8,view:"text",type:"string"},updateBy:{title:"更新人",order:9,view:"text",type:"string"},phone:{title:"手机号",order:10,view:"text",type:"string"},cartType:{title:"证件类型(1:身份证,other:其他)",order:11,view:"number",type:"number"},tenantId:{title:"租户id",order:12,view:"number",type:"number"},reviewUser:{title:"审核人",order:13,view:"text",type:"string"}};function u(e){return l}const{createConfirm:o}=a();const p="/hgy/personalCenter/hgyPersonalAuth/exportXls",h="/hgy/personalCenter/hgyPersonalAuth/importExcel",m=e=>r.get({url:"/hgy/personalCenter/hgyPersonalAuth/list",params:e}),g=(e,t)=>r.delete({url:"/hgy/personalCenter/hgyPersonalAuth/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),y=(e,t)=>{o({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>r.delete({url:"/hgy/personalCenter/hgyPersonalAuth/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},I=(e,t)=>{let n=t?"/hgy/personalCenter/hgyPersonalAuth/edit":"/hgy/personalCenter/hgyPersonalAuth/add";return r.post({url:n,params:e})};export{p as a,y as b,d as c,s as d,g as e,I as f,h as g,l as h,u as i,m as l,c as s};
