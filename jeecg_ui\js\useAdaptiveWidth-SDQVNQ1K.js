import{f as l}from"./vue-vendor-dy9k-Yad.js";import{z as h,x as m,q as w}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const s={"<=565":"100%","<=1366":"800px","<=1600":"600px","<=1920":"600px",">1920":"500px"};function v(t=s,a=!0,c=50){const n=a?Object.assign({},s,t):t,f=Object.keys(n),i=l();function o(u){let e;for(const r of f)try{if(new Function(`return ${u} ${r}`)()){e=n[r];break}}catch(p){}e&&(i.value=e)}o(window.innerWidth);const{removeEvent:d}=h({el:window,name:"resize",listener:m(()=>o(window.innerWidth),c)});return w(()=>d()),{adaptiveWidth:i}}function b(){return v({"<=620":"100%","<=1600":600,"<=1920":650,">1920":700},!1)}export{v as useAdaptiveWidth,b as useDrawerAdaptiveWidth};
