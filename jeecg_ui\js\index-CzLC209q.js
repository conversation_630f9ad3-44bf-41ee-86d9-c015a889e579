import{d as p,ap as s,e as m,u,ag as r,aB as i,ar as c,aD as d,G as a,at as f,k as l,au as _}from"./vue-vendor-dy9k-Yad.js";import{P as g}from"./index-CtJ0w2CP.js";import{j as x}from"./antd-vue-vendor-me9YkNVC.js";import{a as P}from"./index-CCWaWN5g.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const B=p({name:"TestTab",components:{PageWrapper:g,Input:x},setup(){const{currentRoute:e}=s();return{params:m(()=>u(e).params)}}});function C(e,t,$,b,k,I){const o=r("Input"),n=r("PageWrapper");return c(),i(n,{title:"带参数标签页",content:"支持带参数多tab缓存"},{default:d(()=>[a(" Current Param : "+_(e.params)+" ",1),t[0]||(t[0]=f("br",null,null,-1)),t[1]||(t[1]=a(" Keep Alive ")),l(o)]),_:1,__:[0,1]})}const A=P(B,[["render",C]]);export{A as default};
