var k=Object.defineProperty;var u=Object.getOwnPropertySymbols;var x=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var g=(e,t,o)=>t in e?k(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,_=(e,t)=>{for(var o in t||(t={}))x.call(t,o)&&g(e,o,t[o]);if(u)for(var o of u(t))O.call(t,o)&&g(e,o,t[o]);return e};var d=(e,t,o)=>new Promise((r,m)=>{var n=i=>{try{a(o.next(i))}catch(l){m(l)}},p=i=>{try{a(o.throw(i))}catch(l){m(l)}},a=i=>i.done?r(i.value):Promise.resolve(i.value).then(n,p);a((o=o.apply(e,t)).next())});import{f as w,u as h,ag as B,aq as I,ar as K,k as v,aD as R}from"./vue-vendor-dy9k-Yad.js";import{I as q}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{B as L}from"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import{f as S}from"./AiKnowledgeBase.data-BCjuIj-H.js";import{q as V,s as U,e as b}from"./AiKnowledgeBase.api-Dgaf5KfS.js";import{u as A}from"./useForm-CgkFTrrO.js";import{ac as D,a as E}from"./index-CCWaWN5g.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const G={name:"KnowledgeBaseModal",components:{BasicForm:L,BasicModal:q},emits:["success","register"],setup(e,{emit:t}){const o=w("创建知识库"),r=w(!1),[m,{resetFields:n,setFieldsValue:p,validate:a,clearValidate:i,updateSchema:l}]=A({schemas:S,showActionButtonGroup:!1,layout:"vertical",wrapperCol:{span:24}}),[y,{closeModal:f,setModalProps:c}]=D(s=>d(null,null,function*(){if(yield n(),c({confirmLoading:!1}),r.value=!!(s!=null&&s.isUpdate),o.value=r.value?"编辑知识库":"创建知识库",h(r)){let C=yield V({id:s.id});yield p(_({},C.result))}c({minHeight:500,bodyStyle:{padding:"10px"}})}));function M(){return d(this,null,function*(){try{c({confirmLoading:!0});let s=yield a();h(r)?yield b(s):yield U(s),f(),t("success")}finally{c({confirmLoading:!1})}})}function F(){f()}return{registerModal:y,registerForm:m,title:o,handleOk:M,handleCancel:F}}},H={class:"p-2"};function N(e,t,o,r,m,n){const p=B("BasicForm"),a=B("BasicModal");return K(),I("div",H,[v(a,{destroyOnClose:"",onRegister:r.registerModal,canFullscreen:!1,width:"600px",title:r.title,onOk:r.handleOk,onCancel:r.handleCancel},{default:R(()=>[v(p,{onRegister:r.registerForm},null,8,["onRegister"])]),_:1},8,["onRegister","title","onOk","onCancel"])])}const Qo=E(G,[["render",N],["__scopeId","data-v-8982a5c1"]]);export{Qo as default};
