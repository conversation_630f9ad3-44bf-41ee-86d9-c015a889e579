import{cs as l,H as i}from"./index-CCWaWN5g.js";import{aq as o,ar as r,at as p,au as c}from"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const d={name:"LinkTableListPiece",props:{text:i.string.def(""),id:i.string.def("")},emits:["tab"],setup(n,{emit:a}){function t(e){e==null||e.stopPropagation(),e==null||e.preventDefault(),a("tab",n.id)}return{handleClick:t}}};function m(n,a,t,e,u,f){return r(),o("div",{class:"link-table-piece",onClick:a[0]||(a[0]=(...s)=>e.handleClick&&e.handleClick(...s))},[p("span",null,c(t.text),1)])}const v=l(d,[["render",m],["__scopeId","data-v-ade036b6"]]);export{v as default};
