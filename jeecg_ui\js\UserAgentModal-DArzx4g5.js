var _=Object.defineProperty;var d=Object.getOwnPropertySymbols;var h=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var g=(i,o,t)=>o in i?_(i,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[o]=t,w=(i,o)=>{for(var t in o||(o={}))h.call(o,t)&&g(i,t,o[t]);if(d)for(var t of d(o))M.call(o,t)&&g(i,t,o[t]);return i};var f=(i,o,t)=>new Promise((n,p)=>{var c=r=>{try{e(t.next(r))}catch(s){p(s)}},l=r=>{try{e(t.throw(r))}catch(s){p(s)}},e=r=>r.done?n(r.value):Promise.resolve(r.value).then(c,l);e((t=t.apply(i,o)).next())});import{d as y,aB as A,ar as F,aD as k,k as R,u as a,aE as v}from"./vue-vendor-dy9k-Yad.js";import{B as C}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{d as L}from"./user.data-CLRTqTDz.js";import{f as N,m as O}from"./user.api-mLAlJze4.js";import{u as U}from"./useForm-CgkFTrrO.js";import{ac as x}from"./index-CCWaWN5g.js";import{B as P}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";const Gt=y({__name:"UserAgentModal",emits:["success","register"],setup(i,{emit:o}){const t=o,[n,{resetFields:p,setFieldsValue:c,validate:l}]=U({schemas:L,showActionButtonGroup:!1}),[e,{setModalProps:r,closeModal:s}]=x(m=>f(null,null,function*(){yield p(),r({confirmLoading:!1});const u=yield N({userName:m.userName});m=u.result?u.result:m,yield c(w({},m))}));function B(){return f(this,null,function*(){try{const m=yield l();r({confirmLoading:!0}),yield O(m),s(),t("success")}finally{r({confirmLoading:!1})}})}return(m,u)=>(F(),A(a(C),v(m.$attrs,{onRegister:a(e),width:800,title:"用户代理",onOk:B,destroyOnClose:""}),{default:k(()=>[R(a(P),{onRegister:a(n)},null,8,["onRegister"])]),_:1},16,["onRegister"]))}});export{Gt as default};
