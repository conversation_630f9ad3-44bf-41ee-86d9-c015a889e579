import{d as b,ag as p,aB as C,ar as v,aD as e,at as a,k as s,G as l}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{C as k}from"./index-LCGLvkB3.js";import{u as $,a as B}from"./index-CCWaWN5g.js";import{P as g}from"./index-CtJ0w2CP.js";import{B as w}from"./BasicForm-DBcXiHk0.js";import{u as S}from"./useForm-CgkFTrrO.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const u=[{field:"field1",component:"Input",label:"字段1",colProps:{span:8},componentProps:{placeholder:"自定义placeholder",onChange:t=>{}}},{field:"field2",component:"Input",label:"字段2",colProps:{span:8}},{field:"field3",component:"DatePicker",label:"字段3",colProps:{span:8}},{field:"fieldTime",component:"RangePicker",label:"时间字段",defaultValue:[new Date("2024-03-21"),new Date("2024-03-27")],componentProps:{valueType:"Date"},colProps:{span:8}},{field:"field4",component:"Select",label:"字段4",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1",key:"1"},{label:"选项2",value:"2",key:"2"}]}},{field:"field5",component:"CheckboxGroup",label:"字段5",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]}},{field:"field7",component:"RadioGroup",label:"字段7",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]}}],T=b({components:{BasicForm:w,CollapseContainer:k,PageWrapper:g},setup(){const{createMessage:t}=$(),[o,{setProps:n}]=S({labelWidth:120,schemas:u,actionColOptions:{span:24},fieldMapToTime:[["fieldTime",["startTime","endTime"],"YYYY-MM"]]});return{register:o,schemas:u,handleSubmit:m=>{t.success("click search,values:"+JSON.stringify(m))},setProps:n}}}),W={class:"mb-4"},F={class:"mb-4"};function R(t,o,n,m,D,G){const r=p("a-button"),d=p("BasicForm"),f=p("CollapseContainer"),P=p("PageWrapper");return v(),C(P,{title:"UseForm操作示例"},{default:e(()=>[a("div",W,[s(r,{onClick:o[0]||(o[0]=i=>t.setProps({labelWidth:150})),class:"mr-2"},{default:e(()=>o[17]||(o[17]=[l(" 更改labelWidth ")])),_:1,__:[17]}),s(r,{onClick:o[1]||(o[1]=i=>t.setProps({labelWidth:120})),class:"mr-2"},{default:e(()=>o[18]||(o[18]=[l(" 还原labelWidth ")])),_:1,__:[18]}),s(r,{onClick:o[2]||(o[2]=i=>t.setProps({size:"large"})),class:"mr-2"},{default:e(()=>o[19]||(o[19]=[l(" 更改Size ")])),_:1,__:[19]}),s(r,{onClick:o[3]||(o[3]=i=>t.setProps({size:"default"})),class:"mr-2"},{default:e(()=>o[20]||(o[20]=[l(" 还原Size ")])),_:1,__:[20]}),s(r,{onClick:o[4]||(o[4]=i=>t.setProps({disabled:!0})),class:"mr-2"},{default:e(()=>o[21]||(o[21]=[l(" 禁用表单 ")])),_:1,__:[21]}),s(r,{onClick:o[5]||(o[5]=i=>t.setProps({disabled:!1})),class:"mr-2"},{default:e(()=>o[22]||(o[22]=[l(" 解除禁用 ")])),_:1,__:[22]}),s(r,{onClick:o[6]||(o[6]=i=>t.setProps({compact:!0})),class:"mr-2"},{default:e(()=>o[23]||(o[23]=[l(" 紧凑表单 ")])),_:1,__:[23]}),s(r,{onClick:o[7]||(o[7]=i=>t.setProps({compact:!1})),class:"mr-2"},{default:e(()=>o[24]||(o[24]=[l(" 还原正常间距 ")])),_:1,__:[24]}),s(r,{onClick:o[8]||(o[8]=i=>t.setProps({actionColOptions:{span:8}})),class:"mr-2"},{default:e(()=>o[25]||(o[25]=[l(" 操作按钮位置 ")])),_:1,__:[25]})]),a("div",F,[s(r,{onClick:o[9]||(o[9]=i=>t.setProps({showActionButtonGroup:!1})),class:"mr-2"},{default:e(()=>o[26]||(o[26]=[l(" 隐藏操作按钮 ")])),_:1,__:[26]}),s(r,{onClick:o[10]||(o[10]=i=>t.setProps({showActionButtonGroup:!0})),class:"mr-2"},{default:e(()=>o[27]||(o[27]=[l(" 显示操作按钮 ")])),_:1,__:[27]}),s(r,{onClick:o[11]||(o[11]=i=>t.setProps({showResetButton:!1})),class:"mr-2"},{default:e(()=>o[28]||(o[28]=[l(" 隐藏重置按钮 ")])),_:1,__:[28]}),s(r,{onClick:o[12]||(o[12]=i=>t.setProps({showResetButton:!0})),class:"mr-2"},{default:e(()=>o[29]||(o[29]=[l(" 显示重置按钮 ")])),_:1,__:[29]}),s(r,{onClick:o[13]||(o[13]=i=>t.setProps({showSubmitButton:!1})),class:"mr-2"},{default:e(()=>o[30]||(o[30]=[l(" 隐藏查询按钮 ")])),_:1,__:[30]}),s(r,{onClick:o[14]||(o[14]=i=>t.setProps({showSubmitButton:!0})),class:"mr-2"},{default:e(()=>o[31]||(o[31]=[l(" 显示查询按钮 ")])),_:1,__:[31]}),s(r,{onClick:o[15]||(o[15]=i=>t.setProps({resetButtonOptions:{disabled:!0,text:"重置New"}})),class:"mr-2"},{default:e(()=>o[32]||(o[32]=[l(" 修改重置按钮 ")])),_:1,__:[32]}),s(r,{onClick:o[16]||(o[16]=i=>t.setProps({submitButtonOptions:{disabled:!0,loading:!0}})),class:"mr-2"},{default:e(()=>o[33]||(o[33]=[l(" 修改查询按钮 ")])),_:1,__:[33]})]),s(f,{title:"useForm示例"},{default:e(()=>[s(d,{onRegister:t.register,onSubmit:t.handleSubmit},null,8,["onRegister","onSubmit"])]),_:1})]),_:1})}const Yo=B(T,[["render",R]]);export{Yo as default};
