var V=Object.defineProperty,z=Object.defineProperties;var I=Object.getOwnPropertyDescriptors;var k=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var y=(a,e,t)=>e in a?V(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,_=(a,e)=>{for(var t in e||(e={}))N.call(e,t)&&y(a,t,e[t]);if(k)for(var t of k(e))O.call(e,t)&&y(a,t,e[t]);return a},b=(a,e)=>z(a,I(e));var g=(a,e,t)=>new Promise((u,s)=>{var l=o=>{try{c(t.next(o))}catch(m){s(m)}},d=o=>{try{c(t.throw(o))}catch(m){s(m)}},c=o=>o.done?u(o.value):Promise.resolve(o.value).then(l,d);c((t=t.apply(a,e)).next())});import{d as C,r as P,f as w,ag as f,aB as U,ar as j,aD as i,k as n,G as q,aE as D,u as G}from"./vue-vendor-dy9k-Yad.js";import{I as L}from"./BasicModal-BLFvpBuk.js";import{ah as T,ac as $,u as A}from"./index-CCWaWN5g.js";import"./index-Diw57m_E.js";import{rules as H}from"./validator-B_KkcUnu.js";import{a5 as J}from"./antd-vue-vendor-me9YkNVC.js";import{b as K}from"./UserSetting.api-BJ086Ekj.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./user.api-mLAlJze4.js";const Q=C({name:"user-replace-email-modal"}),_e=C(b(_({},Q),{emits:["register","success"],setup(a,{emit:e}){const t=T(),{createMessage:u}=A(),s=P({email:""}),l=w(),d={email:[_({},H.duplicateCheckRule("sys_user","email",s,{label:"邮箱"})[0]),{required:!0,type:"email",message:"邮箱格式不正确"}]},c=J.useForm,o=w(""),m=e,[M,{setModalProps:R,closeModal:h}]=$(r=>g(null,null,function*(){l.value.resetFields(),l.value.clearValidate(),R({confirmLoading:!1}),o.value="修改邮箱",r.record.smscode="",Object.assign(s,r.record)}));function B(){return g(this,null,function*(){yield l.value.validateFields(),K(s).then(r=>{r.success?(u.success("修改邮箱成功"),m("success"),h()):u.warning(r.message)})})}return(r,p)=>{const F=f("a-input"),v=f("a-form-item"),x=f("a-button"),E=f("a-form");return j(),U(L,D(r.$attrs,{onRegister:G(M),width:"500px",title:o.value,showCancelBtn:!1,showOkBtn:!1}),{default:i(()=>[n(E,{class:"antd-modal-form",ref_key:"formRef",ref:l,model:s,rules:d},{default:i(()=>[n(v,{name:"email"},{default:i(()=>[n(F,{size:"large",value:s.email,"onUpdate:value":p[0]||(p[0]=S=>s.email=S),placeholder:"请输入邮箱"},null,8,["value"])]),_:1}),n(v,null,{default:i(()=>[n(x,{size:"large",type:"primary",block:"",onClick:B},{default:i(()=>p[1]||(p[1]=[q(" 确认 ")])),_:1,__:[1]})]),_:1})]),_:1},8,["model"])]),_:1},16,["onRegister","title"])}}}));export{_e as default};
