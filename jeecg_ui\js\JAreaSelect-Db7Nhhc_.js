var I=Object.defineProperty,R=Object.defineProperties;var U=Object.getOwnPropertyDescriptors;var $=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable;var B=(e,t,r)=>t in e?I(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,O=(e,t)=>{for(var r in t||(t={}))E.call(t,r)&&B(e,r,t[r]);if($)for(var r of $(t))T.call(t,r)&&B(e,r,t[r]);return e},S=(e,t)=>R(e,U(t));import{d as q,f as z,r as G,e as A,h as H,w as J,I as L,u as p,ag as C,aB as v,ar as u,aD as o,at as P,k as j,ah as F,aq as m,F as b,aC as h,G as k,au as _}from"./vue-vendor-dy9k-Yad.js";import{H as s,a as K}from"./index-CCWaWN5g.js";import{useRuleFormItem as M}from"./useFormItem-CHvpjy4o.js";import{g as y,c as Q,d as W}from"./areaDataUtil-BXVjRArW.js";const X=q({name:"JAreaSelect",props:{value:[Array,String],province:[String],city:[String],area:[String],level:s.number.def(3),disabled:s.bool.def(!1),codeField:s.string,size:s.string,placeholder:s.string,formValues:s.any,allowClear:s.bool.def(!1),getPopupContainer:{type:Function,default:e=>e==null?void 0:e.parentNode}},emits:["change","update:value","update:area","update:city","update:province"],setup(e,{emit:t,refs:r}){const w=z([]),a=G({province:"",city:"",area:""}),[i]=M(e,"value","change",w),c=A(()=>a.province?y(a.province):[]),d=A(()=>a.city?y(a.city):[]);H(()=>{e&&g()}),J(a,n=>{e.value||(t("update:province",a.province),t("update:city",a.city),t("update:area",a.area))});function g(){if(e.value)if(Array.isArray(e.value))a.province=e.value[0],a.city=e.value[1]?e.value[1]:"",a.area=e.value[2]?e.value[2]:"";else{let n=Q(e.value,e.level);n&&(a.province=n[0],a.city=e.level>=2&&n[1]?n[1]:"",a.area=e.level>=3&&n[2]?n[2]:"")}else a.province=e.province?e.province:"",a.city=e.city?e.city:"",a.area=e.area?e.area:""}function l(n){var f,V;a.city=n&&((f=y(n)[0])==null?void 0:f.value),a.area=a.city&&((V=y(a.city)[0])==null?void 0:V.value),i.value=e.level<=1?n:e.level<=2?a.city:a.area,t("update:value",p(i))}function N(n){var f;a.area=n&&((f=y(n)[0])==null?void 0:f.value),i.value=e.level<=2?n:a.area,t("update:value",p(i))}function D(n){i.value=n,t("update:value",p(i))}return S(O({},L(a)),{provinceOptions:W,cityOptions:c,areaOptions:d,proChange:l,cityChange:N,areaChange:D})}}),Y={class:"area-select"};function Z(e,t,r,w,a,i){const c=C("a-select-option"),d=C("a-select"),g=C("a-form-item-rest");return u(),v(g,null,{default:o(()=>[P("div",Y,[j(d,{value:e.province,"onUpdate:value":t[0]||(t[0]=l=>e.province=l),onChange:e.proChange,allowClear:"",disabled:e.disabled},{default:o(()=>[(u(!0),m(b,null,h(e.provinceOptions,l=>(u(),v(c,{key:`${l.value}`,value:l.value},{default:o(()=>[k(_(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange","disabled"]),e.level>=2?(u(),v(d,{key:0,value:e.city,"onUpdate:value":t[1]||(t[1]=l=>e.city=l),onChange:e.cityChange,disabled:e.disabled},{default:o(()=>[(u(!0),m(b,null,h(e.cityOptions,l=>(u(),v(c,{key:`${l.value}`,value:l.value},{default:o(()=>[k(_(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange","disabled"])):F("",!0),e.level>=3?(u(),v(d,{key:1,value:e.area,"onUpdate:value":t[2]||(t[2]=l=>e.area=l),onChange:e.areaChange,disabled:e.disabled},{default:o(()=>[(u(!0),m(b,null,h(e.areaOptions,l=>(u(),v(c,{key:`${l.value}`,value:l.value},{default:o(()=>[k(_(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange","disabled"])):F("",!0)])]),_:1})}const ne=K(X,[["render",Z],["__scopeId","data-v-a1f3b448"]]);export{ne as i};
