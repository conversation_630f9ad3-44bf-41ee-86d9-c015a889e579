import{d as O,f as c,u as t,h as q,e as j,ag as m,aq as w,ar as B,k as u,aE as K,aD as p,ah as z,at as M,G as y,au as A}from"./vue-vendor-dy9k-Yad.js";import{B as F}from"./index-Diw57m_E.js";import{ac as G,b5 as J,a9 as H,u as P,k as W,j as X,a as x}from"./index-CCWaWN5g.js";const Q=O({name:"JImportModal",components:{BasicModal:F},props:{url:{type:String,default:"",required:!1},biz:{type:String,default:"",required:!1},online:{type:Boolean,default:!1,required:!1}},emits:["ok","register"],setup(e,{emit:s,refs:$}){const{createMessage:h,createWarningModal:k}=P(),[_,{closeModal:v}]=G(a=>{C(a)}),r=J(),b=H(),i=c(!1),o=c([]),L=c(""),d=c(""),g=c(0),D=Object.assign({},t(e),t(b));q(()=>{e.url&&(L.value=`${r.uploadUrl}${e.url}`)});const R=j(()=>!(t(o).length>0));function S(){v(),C()}function V(a){g.value=a?1:0}function E(a){const f=t(o).indexOf(a),n=t(o).slice();n.splice(f,1),o.value=n}function I(a){return o.value=[...t(o),a],!1}function T(){let{biz:a,online:f}=e;const n=new FormData;a&&n.append("isSingleTableImport",a),t(d)&&t(d).length>0&&n.append("foreignKeys",t(d)),W(d.value)&&n.append("foreignKeys",JSON.stringify(d.value)),f&&n.append("validateStatus",t(g)),t(o).forEach(l=>{n.append("files[]",l)}),i.value=!0;let N={"Content-Type":"multipart/form-data;boundary = "+new Date().getTime()};X.post({url:e.url,params:n,headers:N},{isTransformResponse:!1}).then(l=>{i.value=!1,l.success?(l.code==201?U(l.message,l.result):h.success(l.message),S(),C(),s("ok")):h.warning(l.message)}).catch(()=>{i.value=!1})}function U(a,f){let n=r.uploadUrl+f;k({title:"导入成功,但是有错误数据!",centered:!1,content:`<div>
                        <span>${a}</span><br/>
                        <span>具体详情请<a href = ${n} target="_blank"> 点击下载 </a> </span>
                      </div>`})}function C(a){o.value=[],i.value=!1,d.value=a,g.value=0}return{register:_,getBindValue:D,uploadDisabled:R,fileList:o,uploading:i,validateStatus:g,handleClose:S,handleChangeValidateStatus:V,handleRemove:E,beforeUpload:I,handleImport:T}}}),Y={key:0,style:{margin:"0 5px 5px"}},Z={style:{"margin-left":"6px"}};function ee(e,s,$,h,k,_){const v=m("a-switch"),r=m("a-button"),b=m("a-upload"),i=m("BasicModal");return B(),w("div",null,[u(i,K(e.$attrs,{onRegister:e.register,title:"导入EXCEL",width:600,onCancel:e.handleClose,confirmLoading:e.uploading,destroyOnClose:""}),{footer:p(()=>[u(r,{onClick:e.handleClose},{default:p(()=>s[2]||(s[2]=[y("关闭")])),_:1,__:[2]},8,["onClick"]),u(r,{type:"primary",onClick:e.handleImport,disabled:e.uploadDisabled,loading:e.uploading},{default:p(()=>[y(A(e.uploading?"上传中...":"开始上传"),1)]),_:1},8,["onClick","disabled","loading"])]),default:p(()=>[e.online?(B(),w("div",Y,[s[0]||(s[0]=M("span",{style:{display:"inline-block",height:"32px","line-height":"32px","vertical-align":"middle"}},"是否开启校验:",-1)),M("span",Z,[u(v,{checked:e.validateStatus==1,onChange:e.handleChangeValidateStatus,"checked-children":"是","un-checked-children":"否"},null,8,["checked","onChange"])])])):z("",!0),u(b,{name:"file",accept:".xls,.xlsx",multiple:!0,fileList:e.fileList,onRemove:e.handleRemove,beforeUpload:e.beforeUpload},{default:p(()=>[u(r,{preIcon:"ant-design:upload-outlined"},{default:p(()=>s[1]||(s[1]=[y("选择导入文件")])),_:1,__:[1]})]),_:1},8,["fileList","onRemove","beforeUpload"])]),_:1},16,["onRegister","onCancel","confirmLoading"])])}const oe=x(Q,[["render",ee]]);export{oe as a};
