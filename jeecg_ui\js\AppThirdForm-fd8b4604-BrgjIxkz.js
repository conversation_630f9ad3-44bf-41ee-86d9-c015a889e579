import{f as u,u as c,d as q,ag as D,q as M,ar as R,aq as G,at as p,k as I,aD as P,G as x,B as Q}from"./vue-vendor-dy9k-Yad.js";import{a5 as L,j as z,bH as J}from"./antd-vue-vendor-me9YkNVC.js";import{C as K}from"./index-DFrpKMGa.js";import{cs as W,u as X,N as Y,b5 as Z,ah as ee,j as B}from"./index-CCWaWN5g.js";import"./useCountdown-CCWNeb_r.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFormItemSingle-Cw668yj5.js";var te=Object.defineProperty,O=Object.getOwnPropertySymbols,ne=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable,j=(n,e,t)=>e in n?te(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,C=(n,e)=>{for(var t in e||(e={}))ne.call(e,t)&&j(n,t,e[t]);if(O)for(var t of O(e))re.call(e,t)&&j(n,t,e[t]);return n},ie=(n,e,t)=>new Promise((g,f)=>{var h=o=>{try{m(t.next(o))}catch(s){f(s)}},d=o=>{try{m(t.throw(o))}catch(s){f(s)}},m=o=>o.done?g(o.value):Promise.resolve(o.value).then(h,d);m((t=t.apply(n,e)).next())});function oe(n){const{createMessage:e,notification:t}=X(),{t:g}=Y(),f=Z(),h=ee(),d=u(""),m=u({}),o=u(!1),s=u(!1),y=u(""),T=u(!1),v=u(""),b=u("");function S(r){let i=`${f.uploadUrl}/sys/thirdLogin/render/${r}`;window.open(i,`login ${r}`,"height=500, width=500, top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no"),d.value=r,m.value={},o.value=!1;let l=function(H){let a=H.data;if(typeof a=="string")if(a==="登录失败")e.warning(a);else if(a.includes("绑定手机号")){s.value=!0;let V=a.split(",");y.value=V[1],n("type",{loginType:"thirdLogin"})}else w(a);else typeof a=="object"?a.isObj===!0&&(T.value=!0,m.value=C({},a)):e.warning("不识别的信息传递");window.removeEventListener("message",c(l),!1)};window.addEventListener("message",l,!1)}function w(r){c(o)===!1&&(o.value=!0,h.ThirdLogin({token:r,thirdType:c(d)}).then(i=>{i&&i.userInfo?t.success({message:g("sys.login.loginSuccessTitle"),description:`${g("sys.login.loginSuccessDesc")}: ${i.userInfo.realname}`,duration:3}):F(i)}))}function F(r){t.error({message:"登录失败",description:((r.response||{}).data||{}).message||r.message||"请求出现错误，请稍后再试",duration:4})}function k(){c(v)||A("请输入手机号"),c(b)||A("请输入验证码");let r={mobile:c(v),captcha:c(b),thirdUserUuid:c(y)};B.post({url:"/sys/thirdLogin/bindingThirdPhone",params:r},{isTransformResponse:!1}).then(i=>{i.success?(s.value=!1,w(i.result)):e.warning(i.message)}).catch(i=>{e.warning(i.message)})}function A(r){t.error({message:"登录失败",description:r,duration:4})}function U(){n("type",{loginType:"login"})}function E(){n("type",{loginType:"register"})}function N(){s.value=!1}function _(r){return ie(this,null,function*(){let i=C({},r);i.thirdUserUuid=c(y),yield B.put({url:"/sys/thirdLogin/registerBindThirdAccount",params:i},{isTransformResponse:!1}).then(l=>{l.success?(s.value=!1,w(l.result)):e.warning(l.message)}).catch(l=>{e.warning(l.message)})})}function $(r){v.value=r.mobile,b.value=r.sms,k()}return{thirdConfirmShow:T,bindingAccount:s,thirdHandleOk:k,thirdPhone:v,thirdCaptcha:b,onThirdLogin:S,loginAccountClick:U,registerAccountClick:E,hideBindThirdAccount:N,bindThirdAccount:$,createAccountBindThird:_}}const se=L.Item,ae=z.Password,ce=q({name:"AppThirdForm",components:{FormItem:se,Form:L,InputPassword:ae,CountdownInput:K,QuestionCircleFilled:J},setup(n,{emit:e}){return C({},oe(e))}}),le={class:"third-account"},ue={class:"content"},de={class:"enter-x bind-btn"},pe={class:"enter-x bind-btn",style:{"margin-top":"20px"}};function me(n,e,t,g,f,h){const d=D("a-button");return M((R(),G("div",le,[p("div",ue,[e[2]||(e[2]=p("div",{class:"bind-title"},[p("span",null,"还未绑定敲敲云账号")],-1)),e[3]||(e[3]=p("div",{class:"bind-title-desc"},[p("span",null,"请选择绑定已有帐户，或创建新帐号")],-1)),p("div",de,[I(d,{type:"primary",onClick:n.loginAccountClick},{default:P(()=>e[0]||(e[0]=[x("登录并绑定")])),_:1},8,["onClick"])]),p("div",pe,[I(d,{type:"primary",onClick:n.registerAccountClick},{default:P(()=>e[1]||(e[1]=[x("注册新账号")])),_:1},8,["onClick"])])])],512)),[[Q,n.bindingAccount]])}const Se=W(ce,[["render",me],["__scopeId","data-v-c98a927f"]]);export{Se as default};
