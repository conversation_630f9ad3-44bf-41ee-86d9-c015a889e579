var _=(N,B,i)=>new Promise((U,S)=>{var C=s=>{try{b(i.next(s))}catch(o){S(o)}},L=s=>{try{b(i.throw(s))}catch(o){S(o)}},b=s=>s.done?U(s.value):Promise.resolve(s.value).then(C,L);b((i=i.apply(N,B)).next())});import{d as ae,f as u,w as le,ag as d,aB as I,ar as m,aD as l,k as f,ah as R,as as $,aq as G,aC as j,G as y,au as A,F as J,at as K,aE as O,u as t}from"./vue-vendor-dy9k-Yad.js";import{B as se}from"./index-Diw57m_E.js";import{g as oe,h as ne}from"./depart.api-BoGnt_ZX.js";import{w as re}from"./tenant.api-CTNrRQ_d.js";import{ah as ue,u as ie,a as fe}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const ce=ae({__name:"DepartSelect",props:{title:{type:String,default:"部门选择"},closable:{type:Boolean,default:!1},username:{type:String,default:""}},setup(N,{expose:B}){const i=ue(),{createMessage:U,notification:S}=ie(),C=N,L={labelCol:{span:4},wrapperCol:{span:18}},b={maskClosable:!1,closable:!1,canFullscreen:!1,width:"500px",minHeight:20,maxHeight:20},s=u(""),o=u(!1),w=u(""),c=u(),M=u([]),x=u(""),p=u(!1),k=u(""),v=u(""),h=u([]),T=u(""),D=u(!1);function Q(){return _(this,null,function*(){yield F(),yield W(),t(o)&&t(p)?s.value="切换租户和部门":t(o)?s.value=t(w)&&t(w).length>0?`租户切换（当前租户 :${t(w)}）`:C.title:t(p)&&(s.value=t(k)&&t(k).length>0?`部门切换（当前部门 :${t(k)}）`:C.title),(t(o)||t(p))&&(D.value=!0)})}function F(){return _(this,null,function*(){const a=yield oe();if(!a.list||a.list.length==0)return;let e=a.list.filter(r=>r.orgCode==a.orgCode);h.value=a.list,v.value=e&&e.length>0?a.orgCode:"",k.value=e&&e.length>0?e[0].departName:"",p.value=!0})}function W(){return _(this,null,function*(){const a=yield re();if(!a.list||a.list.length==0)return;let e=i.getTenant,r=a.list.filter(g=>g.id==e);w.value=r&&r.length>0?r[0].name:"",M.value=a.list,c.value=e,o.value=!0})}function X(){return _(this,null,function*(){if(t(o)&&t(c)==null)return x.value="error",!1;if(t(p)&&!t(v))return T.value="error",!1;Y().then(()=>{t(o)&&i.setTenant(t(c)),U.success("切换成功"),window.location.reload()}).catch(a=>{}).finally(()=>{t(o)&&i.setTenant(t(c)),V()})})}function Y(){return new Promise((a,e)=>_(null,null,function*(){if(!t(p))a();else{const r=yield ne({username:i.getUserInfo.username,orgCode:t(v),loginTenantId:t(c)});if(r.userInfo){const g=r.userInfo;i.setUserInfo(g),a()}else Z(r),i.logout(),e()}}))}function Z(a){S.error({message:"登录失败",description:((a.response||{}).data||{}).message||a.message||"请求出现错误，请稍后再试",duration:4})}function V(){ee()}function ee(){s.value="",o.value=!1,w.value="",c.value="",M.value=[],x.value="",p.value=!1,k.value="",v.value="",h.value=[],T.value="",D.value=!1}return le(()=>C.username,a=>{a&&F()}),B({show:Q}),(a,e)=>{const r=d("a-avatar"),g=d("a-tooltip"),z=d("Icon"),H=d("a-select-option"),q=d("a-select"),E=d("a-form-item"),te=d("a-form"),P=d("a-button");return m(),I(t(se),O(b,{maxHeight:500,title:s.value,visible:D.value,"onUpdate:visible":e[2]||(e[2]=n=>D.value=n),wrapClassName:"loginSelectModal"}),{footer:l(()=>[f(P,{onClick:V},{default:l(()=>e[7]||(e[7]=[y("关闭")])),_:1,__:[7]}),f(P,{onClick:X,type:"primary"},{default:l(()=>e[8]||(e[8]=[y("确认")])),_:1,__:[8]})]),default:l(()=>[f(te,O({ref:"formRef"},L,{colon:!1,class:"loginSelectForm"}),{default:l(()=>[o.value?(m(),I(E,{key:0,"validate-status":x.value},{label:l(()=>[f(g,{placement:"topLeft"},{title:l(()=>e[3]||(e[3]=[K("span",null,"您隶属于多租户，请选择当前所属租户",-1)])),default:l(()=>[f(r,{style:{"background-color":"#87d068"},size:30},{default:l(()=>e[4]||(e[4]=[y(" 租户 ")])),_:1,__:[4]})]),_:1})]),default:l(()=>[f(q,{value:c.value,"onUpdate:value":e[0]||(e[0]=n=>c.value=n),placeholder:"请选择登录部门",class:$({"valid-error":x.value=="error"})},{suffixIcon:l(()=>[f(z,{icon:"ant-design:gold-outline"})]),default:l(()=>[(m(!0),G(J,null,j(M.value,n=>(m(),I(H,{key:n.id,value:n.id},{default:l(()=>[y(A(n.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","class"])]),_:1},8,["validate-status"])):R("",!0),p.value?(m(),I(E,{key:1,"validate-status":T.value},{label:l(()=>[f(g,{placement:"topLeft"},{title:l(()=>e[5]||(e[5]=[K("span",null,"您隶属于多部门，请选择当前所在部门",-1)])),default:l(()=>[f(r,{style:{"background-color":"rgb(104, 208, 203)"},size:30},{default:l(()=>e[6]||(e[6]=[y(" 部门 ")])),_:1,__:[6]})]),_:1})]),default:l(()=>[f(q,{value:v.value,"onUpdate:value":e[1]||(e[1]=n=>v.value=n),placeholder:"请选择登录部门",class:$({"valid-error":T.value=="error"})},{suffixIcon:l(()=>[f(z,{icon:"ant-design:gold-outline"})]),default:l(()=>[(m(!0),G(J,null,j(h.value,n=>(m(),I(H,{key:n.orgCode,value:n.orgCode},{default:l(()=>[y(A(n.departName),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","class"])]),_:1},8,["validate-status"])):R("",!0)]),_:1},16)]),_:1},16,["title","visible"])}}}),Ue=fe(ce,[["__scopeId","data-v-9b3269b0"]]);export{Ue as default};
