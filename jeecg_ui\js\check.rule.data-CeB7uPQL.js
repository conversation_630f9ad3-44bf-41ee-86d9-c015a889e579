import{render as a}from"./renderUtils-D7XVOFwj.js";import{a as u}from"./user.api-mLAlJze4.js";import{j as c}from"./index-CCWaWN5g.js";import{M as d}from"./antd-vue-vendor-me9YkNVC.js";const R="sys/checkRule/exportXls",f="sys/checkRule/importExcel",y=e=>c.get({url:"/sys/checkRule/list",params:e}),C=(e,t)=>c.delete({url:"/sys/checkRule/delete",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),I=(e,t)=>{d.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>c.delete({url:"/sys/checkRule/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},i=(e,t)=>{t=encodeURIComponent(t);let r={ruleCode:e,value:t};return c.get({url:"/sys/checkRule/checkByCode",params:r},{isTransformResponse:!1})},x=e=>c.post({url:"/sys/checkRule/add",params:e}),b=e=>c.put({url:"/sys/checkRule/edit",params:e}),g=[{title:"规则名称",dataIndex:"ruleName",width:200,align:"center"},{title:"规则编码",dataIndex:"ruleCode",width:200,align:"center"},{title:"规则描述",dataIndex:"ruleDescription",width:300,align:"center",customRender:function({text:e}){return a.renderTip(e,30)}}],P=[{field:"ruleName",label:"规则名称",component:"Input",colProps:{span:6}},{field:"ruleCode",label:"规则编码",component:"Input",colProps:{span:6}}],w=[{label:"",field:"id",component:"Input",show:!1},{field:"ruleName",label:"规则名称",component:"Input",required:!0,colProps:{span:24}},{field:"ruleCode",label:"规则编码",component:"Input",colProps:{span:24},dynamicDisabled:({values:e})=>!!e.id,dynamicRules:({model:e})=>[{required:!0,validator:(t,r)=>new Promise((n,l)=>{if(!r)return l("请输入规则编码！");let o={tableName:"sys_check_rule",fieldName:"rule_code",fieldVal:r,dataId:e.id};u(o).then(s=>{s.success?n():l("规则编码已存在!")}).catch(s=>{l(s.message||"校验失败")})})}]},{field:"ruleDescription",label:"规则描述",colProps:{span:24},component:"InputTextArea",componentProps:{placeholder:"请输入规则描述",rows:2}}],T=[{label:"123",field:"ruleCode",component:"Input",show:!1},{field:"testValue",label:"需要测试的值:",component:"Input",componentProps:({formModel:e})=>({onChange:t=>{e.testValue=t.target.value}}),dynamicRules:({model:e})=>{const{ruleCode:t}=e;return[{required:!1,validator:(r,n)=>new Promise((l,o)=>{t&&n?i(t,n).then(s=>{s.success?l():o(s.message)}).catch(s=>{o(s.message||s)}):l()})}]}}];export{P as a,I as b,T as c,g as d,R as e,w as f,y as g,C as h,f as i,x as s,b as u};
