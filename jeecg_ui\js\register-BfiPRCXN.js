const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/AiChat-DA_7PGsT.js","js/vue-vendor-dy9k-Yad.js","js/slide-Ds8o58gV.js","js/index-CCWaWN5g.js","js/antd-vue-vendor-me9YkNVC.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/ailogo-DG2_TD5d.js","assets/slide-CGzfJpe7.css","js/chat-I63fsyMB.js","js/chatMessage-DcTiQunt.js","js/chatText-DGPEwQvb.js","assets/chatText-E5uzeQ2u.css","assets/chatMessage-BZP8k8Dw.css","js/presetQuestion-CYEhQsHK.js","assets/presetQuestion-XxJJtIg3.css","assets/chat-FQpJSxst.css","assets/AiChat-D8SS-qTU.css","js/AiChatIcon-CKeX2hjl.js","assets/AiChatIcon-BUkyvZvH.css"])))=>i.map(i=>d[i]);
var n=(a,i,e)=>new Promise((_,m)=>{var c=t=>{try{p(e.next(t))}catch(r){m(r)}},h=t=>{try{p(e.throw(t))}catch(r){m(r)}},p=t=>t.done?_(t.value):Promise.resolve(t.value).then(c,h);p((e=e.apply(a,i)).next())});import{_ as o,L as u,r as I}from"./index-CCWaWN5g.js";import"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const d=[{path:"/ai/app/chat/:appId",name:"ai-chat-@appId-@modeType",component:()=>o(()=>import("./AiChat-DA_7PGsT.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17])),meta:{title:"AI聊天",ignoreAuth:!0}},{path:"/ai/app/chatIcon/:appId",name:"ai-chatIcon-@appId",component:()=>o(()=>import("./AiChatIcon-CKeX2hjl.js"),__vite__mapDeps([18,1,0,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,19])),meta:{title:"AI聊天",ignoreAuth:!0}},{path:"/ai/chat",name:"aiChat",component:u,meta:{title:"ai聊天"},children:[{path:"/ai/chat/:appId",name:"ai-chat-@appId",component:()=>o(()=>import("./AiChat-DA_7PGsT.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17])),meta:{title:"AI助手",ignoreAuth:!1}},{path:"/ai/chat",name:"ai-chat",component:()=>o(()=>import("./AiChat-DA_7PGsT.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17])),meta:{title:"AI助手",ignoreAuth:!1}}]}];function g(a){return n(this,null,function*(){yield A(a)})}function A(a){return n(this,null,function*(){for(let i of d)yield I.addRoute(i)})}export{g as register};
