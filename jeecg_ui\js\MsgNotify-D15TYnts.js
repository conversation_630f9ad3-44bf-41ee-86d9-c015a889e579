import{H as f,L as a}from"./antd-vue-vendor-me9YkNVC.js";import{d as u,ag as t,aB as r,ar as s,aD as e,k as n,aq as L,F as C,aC as h,at as k,au as i,G as I}from"./vue-vendor-dy9k-Yad.js";import{C as x}from"./index-LCGLvkB3.js";import{m as g}from"./data-DwTukD7a.js";import{a as y}from"./index-CCWaWN5g.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";const B=u({components:{CollapseContainer:x,List:a,ListItem:a.Item,ListItemMeta:a.Item.Meta,Switch:f},setup(){return{list:g}}});function M(c,N,w,S,$,v){const m=t("Switch"),p=t("ListItemMeta"),l=t("ListItem"),d=t("List"),_=t("CollapseContainer");return s(),r(_,{title:"新消息通知",canExpan:!1},{default:e(()=>[n(d,null,{default:e(()=>[(s(!0),L(C,null,h(c.list,o=>(s(),r(l,{key:o.key},{default:e(()=>[n(p,null,{title:e(()=>[I(i(o.title)+" ",1),n(m,{class:"extra","checked-children":"开","un-checked-children":"关","default-checked":""})]),description:e(()=>[k("div",null,i(o.description),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})}const K=y(B,[["render",M],["__scopeId","data-v-5f713e36"]]);export{K as default};
