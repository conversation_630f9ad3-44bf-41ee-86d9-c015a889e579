import{d as ge,r as fe,f as n,e as u,u as r,ag as w,aq as N,ar as k,F as he,q as j,k as l,at as a,au as h,aD as d,ah as z,as as y,aB as G,G as X,B as H}from"./vue-vendor-dy9k-Yad.js";import ye from"./AppLoginHeader-4432c584-CwU9kapN.js";import{cs as be,u as K,N as we,ad as ke,b6 as Pe,bS as xe,aV as _e,bR as Te}from"./index-CCWaWN5g.js";import Ce from"./AppNameEmail-3303a037-DZzNwvXO.js";import Be from"./CaptchaModal-CFhSDiLW.js";import"./index-Diw57m_E.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./checkcode-DLY3GIII.js";import"./CustomModal-BakuIxQv.js";var D=(C,B,v)=>new Promise((R,t)=>{var i=m=>{try{g(v.next(m))}catch(b){t(b)}},P=m=>{try{g(v.throw(m))}catch(b){t(b)}},g=m=>m.done?R(m.value):Promise.resolve(m.value).then(i,P);g((v=v.apply(C,B)).next())});const Re={class:"register-box"},Ae={class:"register-subject"},Ee={class:"flex-row align-items-center margin-top40"},Ue={class:"register-title"},Se={class:"register-content"},Me={class:"register-rule"},Ve={key:0,class:"line"},Ie=ge({__name:"AppRegister",props:{bindThirdAccount:{type:Boolean,default:!1}},emits:["return-login","login-account","bind-third-account"],setup(C,{expose:B,emit:v}){const{createMessage:R}=K(),{t}=we(),i=fe({mobile:"",sms:"",regPassword:"",policy:!0}),P=n(),g=n(),m=n(),b=n(),A=n(!1),c=n(""),Q=u(()=>i.mobile!=""||r(c)==="mobile"?"current-active":"");u(()=>i.username!=""||r(c)==="username"?"current-active":"");const W=u(()=>i.sms!=""||r(c)==="sms"?"current-active":""),Y=u(()=>i.regPassword!=""||r(c)==="regPassword"?"current-active":""),E=v,U=n(!0),f=n(60),x=n(null),S=n(),Z=u(()=>se(t("sys.login.mobilePlaceholder"))),J=u(()=>ie(t("sys.login.smsPlaceholder"))),ee=u(()=>[{required:!0,validator:ae},{pattern:/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/,message:"8-20位，需包含字母和数字"}]),oe=u(()=>({mobile:r(Z),sms:r(J),regPassword:r(ee)})),{notification:q,createErrorModal:Ne}=K(),F=u(()=>t("component.countdown.normalText")),L=u(()=>t("component.countdown.sendText",[r(f)])),_=n("register"),O=n(),M=C,[te,{openModal:re}]=ke();function ie(o){return[{required:!0,message:o,trigger:"change"}]}function se(o){return[{required:!0,message:o,trigger:"change"},{pattern:/^1[3456789]\d{9}$/,message:t("sys.login.mobileCorrectPlaceholder")}]}function V(o){c.value=o,o==="mobile"?P.value.focus():o==="sms"?m.value.focus():o==="username"?b.value.focus():g.value.focus()}function I(){c.value=""}function $(){return D(this,null,function*(){if(!i.mobile){R.warn(t("sys.login.mobilePlaceholder"));return}(yield Pe({mobile:i.mobile,smsmode:xe.REGISTER}).catch(o=>{o.code===_e.PHONE_SMS_FAIL_CODE&&re(!0,{})}))&&(r(x)||(f.value=60,U.value=!1,x.value=setInterval(()=>{r(f)>0&&r(f)<=60?f.value=f.value-1:(U.value=!0,clearInterval(r(x)),x.value=null)},1e3)))})}function ae(o,e){return e===""?Promise.reject(t("sys.login.passwordPlaceholder")):Promise.resolve()}function le(){E("return-login")}function ne(){return D(this,null,function*(){S.value.validateFields().then(o=>D(this,null,function*(){A.value=!0,yield Te({phone:o.mobile,smscode:o.sms}).then(e=>{if(e.success)e.result&&e.result.username&&q.warning({message:t("sys.api.errorTip"),description:"手机号已注册",duration:3});else if(e.message==="用户信息不存在"){let p={password:o.regPassword,phone:o.mobile,smscode:o.sms,bindThirdAccount:!1,thirdUserUuid:"",thirdType:""};_.value="email",setTimeout(()=>{p.bindThirdAccount=M.bindThirdAccount,O.value.setRegisterData(p)},300)}else q.warning({message:t("sys.api.errorTip"),description:e.message||t("sys.api.networkExceptionMsg"),duration:3})}).finally(()=>{A.value=!1})}))})}function me(){_.value="register",Object.assign(i,{mobile:"",sms:"",regPassword:"",policy:!0}),S.value.clearValidate()}function ue(o){E("login-account",o)}function ce(o){E("bind-third-account",o)}return B({clearValidate:me}),(o,e)=>{const p=w("a-input"),T=w("a-form-item"),pe=w("a-checkbox"),de=w("a-button"),ve=w("a-form");return k(),N(he,null,[j(a("div",Re,[a("div",Ae,[l(ye),a("div",Ee,[a("div",Ue,h(r(t)("sys.login.signUpFormTitle")),1)]),a("div",Se,[l(ve,{ref_key:"registerRef",ref:S,model:i,rules:oe.value},{default:d(()=>[a("div",{class:y(["content-item",Q.value]),onClick:e[1]||(e[1]=s=>V("mobile"))},[l(T,{name:"mobile"},{default:d(()=>[l(p,{ref_key:"phoneRef",ref:P,value:i.mobile,"onUpdate:value":e[0]||(e[0]=s=>i.mobile=s),style:{height:"40px"},onBlur:I},null,8,["value"]),a("div",{class:y(["form-title",c.value==="mobile"?"active-title":""])},h(r(t)("sys.login.mobile")),3)]),_:1})],2),a("div",{class:y(["content-item",W.value])},[l(T,{name:"sms",onClick:e[3]||(e[3]=s=>V("sms"))},{default:d(()=>[l(p,{ref_key:"smscodeRef",ref:m,maxLength:6,value:i.sms,"onUpdate:value":e[2]||(e[2]=s=>i.sms=s),style:{height:"40px"},onBlur:I},null,8,["value"]),a("div",{class:y(["form-title",c.value==="sms"?"active-title":""])},h(r(t)("sys.login.smsCode")),3)]),_:1}),U.value?(k(),G(p,{key:0,type:"button",class:"aui-code-line pointer",bordered:!1,onClick:$,value:F.value,"onUpdate:value":e[4]||(e[4]=s=>F.value=s)},null,8,["value"])):(k(),G(p,{key:1,type:"button",class:"aui-code-line disabled-btn",bordered:!1,value:L.value,"onUpdate:value":e[5]||(e[5]=s=>L.value=s)},null,8,["value"]))],2),a("div",{class:y(["content-item",Y.value]),onClick:e[7]||(e[7]=s=>V("regPassword"))},[l(T,{name:"regPassword"},{default:d(()=>[l(p,{ref_key:"pwdRef",ref:g,type:"password",value:i.regPassword,"onUpdate:value":e[6]||(e[6]=s=>i.regPassword=s),style:{height:"40px"},onBlur:I,autocomplete:"new-password"},null,8,["value"]),a("div",{class:y(["form-title",c.value==="regPassword"?"active-title":""])}," 8-20位，需包含字母和数字 ",2)]),_:1})],2),a("p",Me,[l(T,{name:"policy"},{default:d(()=>[l(pe,{checked:i.policy,"onUpdate:checked":e[8]||(e[8]=s=>i.policy=s)},{default:d(()=>[X(h(r(t)("sys.login.policy")),1)]),_:1},8,["checked"])]),_:1})]),a("div",null,[l(de,{type:"primary",loading:A.value,class:"registr-btn pointer",onClick:ne},{default:d(()=>[X(h(r(t)("sys.login.nextStep")),1)]),_:1},8,["loading"])]),M.bindThirdAccount?z("",!0):(k(),N("div",Ve)),M.bindThirdAccount?z("",!0):(k(),N("span",{key:1,class:"to-login pointer",onClick:le},h(r(t)("sys.exception.backLogin")),1))]),_:1},8,["model","rules"])])])],512),[[H,_.value==="register"]]),j(a("div",null,[l(Ce,{ref_key:"emailRef",ref:O,onLoginAccount:ue,onBindThirdAccount:ce},null,512)],512),[[H,_.value==="email"]]),l(Be,{onRegister:r(te),onOk:$},null,8,["onRegister"])],64)}}}),$o=be(Ie,[["__scopeId","data-v-288dafe5"]]);export{$o as default};
