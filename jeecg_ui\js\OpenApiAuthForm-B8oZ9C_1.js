var U=(k,g,m)=>new Promise((u,p)=>{var A=n=>{try{a(m.next(n))}catch(f){p(f)}},F=n=>{try{a(m.throw(n))}catch(f){p(f)}},a=n=>n.done?u(n.value):Promise.resolve(n.value).then(A,F);a((m=m.apply(k,g)).next())});import{d as J,f as c,r as O,e as P,ag as d,aB as R,ar as q,aD as l,k as t,aE as b,u as y,n as G}from"./vue-vendor-dy9k-Yad.js";import{u as M,bf as N,bG as V,e as L,a as Y}from"./index-CCWaWN5g.js";import{J as z,c as H,s as Q}from"./JFormContainer-BUU5aWZC.js";import{a5 as W}from"./antd-vue-vendor-me9YkNVC.js";import{J as X}from"./JSearchSelect-c_lfTydU.js";import"./vxe-table-vendor-B22HppNm.js";const Z=J({__name:"OpenApiAuthForm",props:{formDisabled:{type:Boolean,default:!1},formData:{type:Object,default:()=>({})},formBpm:{type:Boolean,default:!0},title:{type:String,default:""}},emits:["register","ok"],setup(k,{expose:g,emit:m}){const u=k,p=c(),A=W.useForm,F=m,a=O({id:"",name:"",ak:"",sk:"",systemUserId:""}),{createMessage:n}=M(),f=c({xs:{span:24},sm:{span:5}}),C=c({xs:{span:24},sm:{span:16}}),w=c(!1),I=O({name:[{required:!0,message:"请输入授权名称!"}],systemUserId:[{required:!0,message:"请输入关联系统用户名!"}]}),{resetFields:K,validate:x,validateInfos:_}=A(a,I,{immediate:!1}),D=P(()=>u.formBpm===!0?u.formData.disabled!==!1:u.formDisabled);function S(){return U(this,null,function*(){h({});const r=yield H({});a.ak=r[0],a.sk=r[1]})}function h(r){const s=N(V);u.title=="新增"&&(r.systemUserId=s.id),G(()=>{K();const e={};Object.keys(a).forEach(o=>{r.hasOwnProperty(o)&&(e[o]=r[o])}),Object.assign(a,e)})}function B(){return U(this,null,function*(){try{yield x()}catch({errorFields:e}){if(e){const o=e[0];o&&p.value.scrollToField(o.name,{behavior:"smooth",block:"center"})}return Promise.reject(e)}w.value=!0;const r=c(!1);let s=a;s.id&&(r.value=!0);for(let e in s)s[e]instanceof Array&&L(p.value.getProps,e)==="string"&&(s[e]=s[e].join(","));yield Q(s,r.value).then(e=>{e.success?(n.success(e.message),F("ok")):n.warning(e.message)}).finally(()=>{w.value=!1})})}return g({add:S,edit:h,submitForm:B}),(r,s)=>{const e=d("a-input"),o=d("a-form-item"),v=d("a-col"),j=d("a-row"),T=d("a-form"),E=d("a-spin");return q(),R(E,{spinning:w.value},{default:l(()=>[t(z,{disabled:D.value},{detail:l(()=>[t(T,{ref_key:"formRef",ref:p,class:"antd-modal-form",labelCol:f.value,wrapperCol:C.value,name:"OpenApiAuthForm"},{default:l(()=>[t(j,null,{default:l(()=>[t(v,{span:24},{default:l(()=>[t(o,b({label:"授权名称"},y(_).name,{id:"OpenApiAuthForm-name",name:"name"}),{default:l(()=>[t(e,{value:a.name,"onUpdate:value":s[0]||(s[0]=i=>a.name=i),placeholder:"请输入授权名称","allow-clear":""},null,8,["value"])]),_:1},16)]),_:1}),t(v,{span:24},{default:l(()=>[t(o,b({label:"AK"},y(_).ak,{id:"OpenApiAuthForm-ak",name:"ak"}),{default:l(()=>[t(e,{value:a.ak,"onUpdate:value":s[1]||(s[1]=i=>a.ak=i),placeholder:"请输入AK",disabled:"","allow-clear":""},null,8,["value"])]),_:1},16)]),_:1}),t(v,{span:24},{default:l(()=>[t(o,b({label:"SK"},y(_).sk,{id:"OpenApiAuthForm-sk",name:"sk"}),{default:l(()=>[t(e,{value:a.sk,"onUpdate:value":s[2]||(s[2]=i=>a.sk=i),placeholder:"请输入SK",disabled:"","allow-clear":""},null,8,["value"])]),_:1},16)]),_:1}),t(v,{span:24},{default:l(()=>[t(o,b({label:"关联系统用户名"},y(_).systemUserId,{id:"OpenApiAuthForm-systemUserId",name:"systemUserId"}),{default:l(()=>[t(X,{dict:"sys_user,username,id",value:a.systemUserId,"onUpdate:value":s[3]||(s[3]=i=>a.systemUserId=i),placeholder:"请输入关联系统用户名","allow-clear":""},null,8,["value"])]),_:1},16)]),_:1})]),_:1})]),_:1},8,["labelCol","wrapperCol"])]),_:1},8,["disabled"])]),_:1},8,["spinning"])}}}),ne=Y(Z,[["__scopeId","data-v-15114ec4"]]);export{ne as default};
