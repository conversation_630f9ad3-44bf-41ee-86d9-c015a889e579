var N=Object.defineProperty;var b=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable;var V=(o,t,e)=>t in o?N(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,v=(o,t)=>{for(var e in t||(t={}))k.call(t,e)&&V(o,e,t[e]);if(b)for(var e of b(t))y.call(t,e)&&V(o,e,t[e]);return o};var w=(o,t,e)=>new Promise((i,u)=>{var r=s=>{try{n(e.next(s))}catch(a){u(a)}},p=s=>{try{n(e.throw(s))}catch(a){u(a)}},n=s=>s.done?i(s.value):Promise.resolve(s.value).then(r,p);n((e=e.apply(o,t)).next())});import{d as P,r as B,w as x,e as C,f as F,ag as g,aq as S,ar as z,k as l,aD as h,at as d}from"./vue-vendor-dy9k-Yad.js";import{a as D}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const E={class:"step2"},I={class:"form-section"},R={class:"section-content"},U={class:"form-row contact-row"},j=P({__name:"Step2",props:{modelValue:{}},emits:["update:modelValue"],setup(o,{expose:t,emit:e}){const i=o,u=e,r=B(v({},i.modelValue));x(r,a=>{u("update:modelValue",v({},a))},{deep:!0}),x(()=>i.modelValue,a=>{Object.assign(r,a)},{deep:!0});const p=C(()=>({contactName:[{required:!0,message:"请输入联系人姓名",trigger:"blur"},{min:2,max:20,message:"联系人姓名长度应在2-20个字符之间",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]})),n=F();return t({validateForm:()=>w(null,null,function*(){var a;try{return yield(a=n.value)==null?void 0:a.validate(),!0}catch(c){const m=document.querySelector(".ant-form-item-has-error");return m&&m.scrollIntoView({behavior:"smooth",block:"center"}),!1}})}),(a,c)=>{const m=g("a-input"),f=g("a-form-item"),q=g("a-form");return z(),S("div",E,[l(q,{model:r.contact,rules:p.value,ref_key:"formRef",ref:n,"scroll-to-first-error":!0},{default:h(()=>[d("div",I,[c[2]||(c[2]=d("div",{class:"section-title"},"联系人信息",-1)),d("div",R,[d("div",U,[l(f,{label:"联系人姓名",name:"contactName",required:"",class:"contact-item"},{default:h(()=>[l(m,{value:r.contact.contactName,"onUpdate:value":c[0]||(c[0]=_=>r.contact.contactName=_),placeholder:"请输入联系人姓名",size:"large"},null,8,["value"])]),_:1}),l(f,{label:"联系电话",name:"contactPhone",required:"",class:"contact-item"},{default:h(()=>[l(m,{value:r.contact.contactPhone,"onUpdate:value":c[1]||(c[1]=_=>r.contact.contactPhone=_),placeholder:"请输入联系电话",maxlength:11,size:"large"},null,8,["value"])]),_:1}),l(f,{class:"contact-item"})])])])]),_:1},8,["model","rules"])])}}}),J=D(j,[["__scopeId","data-v-e4445298"]]);export{J as default};
