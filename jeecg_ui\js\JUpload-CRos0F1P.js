var le=Object.defineProperty;var q=Object.getOwnPropertySymbols;var re=Object.prototype.hasOwnProperty,ie=Object.prototype.propertyIsEnumerable;var j=(o,c,i)=>c in o?le(o,c,{enumerable:!0,configurable:!0,writable:!0,value:i}):o[c]=i,z=(o,c)=>{for(var i in c||(c={}))re.call(c,i)&&j(o,i,c[i]);if(q)for(var i of q(c))ie.call(c,i)&&j(o,i,c[i]);return o};import{d as W,e as x,u as f,aq as k,ar as L,F as X,q as D,B as R,at as b,k as C,f as F,w as S,n as ue,R as ce,ag as H,as as de,aE as fe,aD as J,aB as me,ah as G,au as K}from"./vue-vendor-dy9k-Yad.js";import{c as U,u as Y,H as u,F as pe,a9 as ge,aC as he,aM as ve,d as O,aE as be,aL as we}from"./index-CCWaWN5g.js";var E=(o=>(o.all="all",o.image="image",o.file="file",o))(E||{});const Ce={class:"upload-download-handler"},ye={class:"upload-mover-handler"},xe=W({__name:"UploadItemActions",props:{element:{type:HTMLElement,required:!0},fileList:{type:Object,required:!0},mover:{type:Boolean,required:!0},download:{type:Boolean,required:!0},emitValue:{type:Function,required:!0}},setup(o){const{createMessage:c}=Y(),i=o,p=x(()=>f(i.fileList));function N(){let s=y();if(s===-1){c.warn("移动失败："+s);return}if(s===0){w(s,f(p).length-1);return}w(s,s-1)}function B(){let s=y();if(s===-1){c.warn("移动失败："+s);return}if(s==f(p).length-1){w(s,0);return}w(s,s+1)}function w(s,r){if(s!==r){let d=[...f(p)],v=d[s];d[s]=d[r],d[r]=v,i.emitValue(d.map(g=>g.url).join(","))}}function y(){var r,d;const s=(d=(r=i.element)==null?void 0:r.getElementsByTagName("img")[0])==null?void 0:d.src;if(s){const v=f(p);for(let g=0;g<v.length;g++){let m=v[g].url;const _=s.replace(window.location.origin,"");if(m===_||encodeURI(m)===_)return g}}return-1}function l(){var r,d;const s=(d=(r=i.element)==null?void 0:r.getElementsByTagName("img")[0])==null?void 0:d.src;window.open(s)}return(s,r)=>(L(),k(X,null,[D(b("div",Ce,[b("a",{class:"download",title:"下载",onClick:l},[C(f(U),{icon:"ant-design:download"})])],512),[[R,o.download]]),D(b("div",ye,[b("a",{title:"向前移动",onClick:N},[C(f(U),{icon:"ant-design:arrow-left"})]),b("a",{title:"向后移动",onClick:B},[C(f(U),{icon:"ant-design:arrow-right"})])],512),[[R,o.mover&&p.value.length>1]])],64))}}),Le={key:0},Ue={class:"ant-upload-text"},Q="ant-upload-list-item",Ne=W({__name:"JUpload",props:{value:u.oneOfType([u.string,u.array]),text:u.string.def("上传"),fileType:u.string.def(E.all),bizPath:u.string.def("temp"),returnUrl:u.bool.def(!0),maxCount:u.number.def(0),buttonVisible:u.bool.def(!0),multiple:u.bool.def(!0),mover:u.bool.def(!0),download:u.bool.def(!0),removeConfirm:u.bool.def(!1),beforeUpload:u.func,disabled:u.bool.def(!1),replaceLastOne:u.bool.def(!1)},emits:["change","update:value"],setup(o,{expose:c,emit:i}){const{createMessage:p,createConfirm:N}=Y(),{prefixCls:B}=pe("j-upload"),w=ge(),y=i,l=o,s=he(),r=F([]),d=F(!0),v=F(),g=x(()=>l.maxCount>0&&r.value.length>=l.maxCount),m=x(()=>l.fileType===E.image),_=x(()=>l.disabled===!0?!0:g.value===!0?l.replaceLastOne!==!0:!1),Z=x(()=>{const e=Object.assign({},l,f(w));return e.name="file",e.listType=m.value?"picture-card":"text",e.class=[e.class,{"upload-disabled":l.disabled}],e.data=z({biz:l.bizPath},e.data),e.beforeUpload||(e.beforeUpload=ee),m.value&&!e.accept&&(e.accept="image/*"),e});S(()=>l.value,e=>{Array.isArray(e)?l.returnUrl?V(e.join(",")):T(e):l.returnUrl?V(e):e&&T(JSON.parse(e))},{immediate:!0}),S(r,()=>ue(()=>I()),{immediate:!0});function I(){if(!m.value)return;const e=v.value?v.value.getElementsByClassName(Q):null;if(!(!e||e.length===0))for(const n of e)n.getAttribute("data-has-actions")==="true"||n.addEventListener("mouseover",M)}function M(e){const a=(()=>{for(const P of e.path){if(P.classList.contains(Q))return P;if(P.classList.contains(`${B}-container`))return null}return null})();if(!a)return;const t=a.getElementsByClassName("ant-upload-list-item-actions");if(!t||t.length===0)return;const h=document.createElement("div");h.className="upload-actions-container",ce(xe,{element:a,fileList:r,mover:l.mover,download:l.download,emitValue:A}).mount(h),t[0].appendChild(h),a.setAttribute("data-has-actions","true"),a.removeEventListener("mouseover",M)}function V(e){if(!e||e.length==0){r.value=[];return}let n=[];const a=ve(e);for(const t of a){let h=O(t);n.push({uid:$(),name:oe(t),status:"done",url:h,response:{status:"history",message:t}})}r.value=n}function T(e){if(!e||e.length==0){r.value=[];return}let n=[];for(const a of e){let t=O(a.filePath);n.push({uid:$(),name:a.fileName,url:t,status:"done",response:{status:"history",message:a.filePath}})}r.value=n}function ee(e){return d.value=!0,m.value&&e.type.indexOf("image")<0?(p.warning("请上传图片"),d.value=!1,!1):typeof l.beforeUpload=="function"?l.beforeUpload(e):!0}function te(){return l.removeConfirm?new Promise(e=>{N({title:"删除",content:`确定要删除这${m.value?"张图片":"个文件"}吗？`,iconType:"warning",onOk:()=>e(!0),onCancel:()=>e(!1)})}):!0}function ne(e){!e.file.status&&d.value===!1&&e.fileList.pop();let n=e.fileList;if(l.maxCount>0&&n.length>=l.maxCount)if(l.maxCount-n.length>=0)n=n.slice(-l.maxCount);else return;if(e.file.status==="done"){let a=[];e.file.response.success?a=n.map(t=>{if(t.response){let h=t.response.message;t.url=O(h)}return t}):(a=n.filter(t=>t.uid!=e.file.uid),p.error(`${e.file.name} 上传失败.`)),n=a}else e.file.status==="error"&&p.error(`${e.file.name} 上传失败.`);if(e.file.status&&(r.value=n),e.file.status==="done"||e.file.status==="removed")if(l.returnUrl)ae();else{let a=[];for(const t of n)if(t.status==="done"){let h={fileName:t.name,filePath:t.response.message,fileSize:t.size};a.push(h)}else return;A(JSON.stringify(a))}}function ae(){let e=r.value,n="";(!e||e.length==0)&&(n="");let a=[];for(const t of e)if(t.status==="done")a.push(t.response.message);else return;a.length>0&&(n=a.join(",")),A(n)}function se(e){m.value?we({imageList:[e.url],maskClosable:!0}):window.open(e.url)}function A(e){y("change",e),y("update:value",e)}function $(){return"-"+parseInt(Math.random()*1e4+1,10)}function oe(e){if(e.lastIndexOf("\\")>=0){let n=new RegExp("\\\\","g");e=e.replace(n,"/")}return e.substring(e.lastIndexOf("/")+1)}return c({addActionsListener:I}),(e,n)=>{const a=H("a-button"),t=H("a-upload");return L(),k("div",{ref_key:"containerRef",ref:v,class:de(`${f(B)}-container`)},[C(t,fe({headers:f(s),multiple:o.multiple,action:f(be),fileList:r.value,disabled:o.disabled},Z.value,{onRemove:te,onChange:ne,onPreview:se}),{default:J(()=>[m.value?(L(),k(X,{key:0},[g.value?G("",!0):(L(),k("div",Le,[C(f(U),{icon:"ant-design:plus-outlined"}),b("div",Ue,K(o.text),1)]))],64)):o.buttonVisible?(L(),me(a,{key:1,disabled:_.value},{default:J(()=>[C(f(U),{icon:"ant-design:upload-outlined"}),b("span",null,K(o.text),1)]),_:1},8,["disabled"])):G("",!0)]),_:1},16,["headers","multiple","action","fileList","disabled"])],2)}}});export{E as U,Ne as _};
