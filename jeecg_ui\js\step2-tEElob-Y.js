var g=(e,o,t)=>new Promise((c,r)=>{var i=s=>{try{a(t.next(s))}catch(m){r(m)}},u=s=>{try{a(t.throw(s))}catch(m){r(m)}},a=s=>s.done?c(s.value):Promise.resolve(s.value).then(i,u);a((t=t.apply(e,o)).next())});import{d as P,f as F,r as k,J as y,ag as d,aB as D,ar as h,aD as p,k as n,G as v}from"./vue-vendor-dy9k-Yad.js";import{j as I,a5 as b,B as C}from"./antd-vue-vendor-me9YkNVC.js";import{S}from"./index-CBCjSSNZ.js";import{bP as _,N as j,u as B,bQ as M,bT as N,a as R}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const z=P({name:"step2",components:{Button:C,Form:b,FormItem:b.Item,InputPassword:I.Password,Input:I,StrengthMeter:S},props:{accountInfo:{type:Object,default:()=>({})}},emits:["prevStep","nextStep"],setup(e,{emit:o}){const{t}=j(),{createErrorModal:c}=B(),{accountInfo:r}=e,i=F(),u=k({username:r.obj.username||"",password:"",confirmPassword:""}),{getFormRules:a}=_(u),{validForm:s}=M(i);function m(){o("prevStep",r.obj)}function w(){return g(this,null,function*(){const f=yield s();if(!f)return;const l=yield N(y({username:f.username,password:f.password,smscode:r.obj.smscode,phone:r.obj.phone}));l.success?o("nextStep",r.obj):c({title:t("sys.api.errorTip"),content:l.message||t("sys.api.networkExceptionMsg")})})}return{t,formRef:i,formData:u,getFormRules:a,handleNext:w,handlePrev:m}}});function T(e,o,t,c,r,i){const u=d("Input"),a=d("FormItem"),s=d("StrengthMeter"),m=d("InputPassword"),w=d("Button"),f=d("Form");return h(),D(f,{class:"p-4 enter-x",model:e.formData,rules:e.getFormRules,ref:"formRef"},{default:p(()=>[n(a,{name:"username",class:"enter-x"},{default:p(()=>[n(u,{size:"large",value:e.formData.username,"onUpdate:value":o[0]||(o[0]=l=>e.formData.username=l),placeholder:e.t("sys.login.userName"),disabled:""},null,8,["value","placeholder"])]),_:1}),n(a,{name:"password",class:"enter-x"},{default:p(()=>[n(s,{size:"large",value:e.formData.password,"onUpdate:value":o[1]||(o[1]=l=>e.formData.password=l),placeholder:e.t("sys.login.password")},null,8,["value","placeholder"])]),_:1}),n(a,{name:"confirmPassword",class:"enter-x"},{default:p(()=>[n(m,{size:"large",visibilityToggle:"",value:e.formData.confirmPassword,"onUpdate:value":o[2]||(o[2]=l=>e.formData.confirmPassword=l),placeholder:e.t("sys.login.confirmPassword")},null,8,["value","placeholder"])]),_:1}),n(a,{class:"enter-x"},{default:p(()=>[n(w,{type:"primary",size:"large",block:"",onClick:e.handlePrev},{default:p(()=>o[3]||(o[3]=[v(" 上一步 ")])),_:1,__:[3]},8,["onClick"]),n(w,{size:"large",block:"",class:"mt-4",onClick:e.handleNext},{default:p(()=>o[4]||(o[4]=[v(" 下一步 ")])),_:1,__:[4]},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])}const J=R(z,[["render",T]]);export{J as default};
