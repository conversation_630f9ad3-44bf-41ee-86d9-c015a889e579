var p=Object.defineProperty;var i=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var s=(o,e,t)=>e in o?p(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t,l=(o,e)=>{for(var t in e||(e={}))m.call(e,t)&&s(o,t,e[t]);if(i)for(var t of i(e))c.call(e,t)&&s(o,t,e[t]);return o};import{d as h,f,h as y,aq as u,ar as d,aA as g}from"./vue-vendor-dy9k-Yad.js";import{useECharts as b}from"./useECharts-BU6FzBZi.js";import{b as w}from"./props-BGjQktHt.js";import{D as S}from"./index-CCWaWN5g.js";import"./useTimeout-CeTdFD_D.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const P=h({__name:"VisitAnalysis",props:l({},w),setup(o){const e=f(null),{setOptions:t}=b(e),{getThemeColor:a}=S(),n=()=>{t({tooltip:{trigger:"axis",axisPointer:{lineStyle:{width:1,color:a.value}}},xAxis:{type:"category",boundaryGap:!1,data:["6:00","7:00","8:00","9:00","10:00","11:00","12:00","13:00","14:00","15:00","16:00","17:00","18:00","19:00","20:00","21:00","22:00","23:00"],splitLine:{show:!0,lineStyle:{width:1,type:"solid",color:"rgba(226,226,226,0.5)"}},axisTick:{show:!1}},yAxis:[{type:"value",max:8e4,splitNumber:4,axisTick:{show:!1},splitArea:{show:!0,areaStyle:{color:["rgba(255,255,255,0.2)","rgba(226,226,226,0.2)"]}}}],grid:{left:"1%",right:"1%",top:"2  %",bottom:0,containLabel:!0},series:[{smooth:!0,data:[111,222,4e3,18e3,33333,55555,66666,33333,14e3,36e3,66666,44444,22222,11111,4e3,2e3,500,333,222,111],type:"line",areaStyle:{},itemStyle:{color:"#5ab1ef"}},{smooth:!0,data:[33,66,88,333,3333,5e3,18e3,3e3,1200,13e3,22e3,11e3,2221,1201,390,198,60,30,22,11],type:"line",areaStyle:{},itemStyle:{color:a.value}}]})};return y(()=>{n()}),(r,x)=>(d(),u("div",{ref_key:"chartRef",ref:e,style:g({height:r.height,width:r.width})},null,4))}});export{P as default};
