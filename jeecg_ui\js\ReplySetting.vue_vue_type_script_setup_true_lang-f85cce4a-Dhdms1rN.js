import{d,aq as v,ar as b,at as r,k as p,u as t,H as o}from"./vue-vendor-dy9k-Yad.js";import{b as f}from"./useSettings-4a774f12-DjycrGR6.js";import g from"./VarListPicker-4ef8c64a-BYzXoS8G.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./index-CCWaWN5g.js";import"./VarListEditor.vue_vue_type_style_index_0_scoped_407b7ab3_lang-4ed993c7-l0sNRNKZ.js";import{r as y}from"./VarTextarea.vue_vue_type_script_setup_true_lang-8c82777b-BlFMq0Y1.js";import"./VarTextarea.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import"./VarEditable.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const O={class:"reply-setting"},V={class:"setting-item"},q={class:"setting-item"},E=d({__name:"ReplySetting",props:{type:{type:String,required:!0},node:{type:Object,required:!0},properties:{type:Object,required:!0},setProperties:{type:Function,required:!0}},setup(l){const n=l,{inputParams:a,prevVariables:m,inputVarsOptions:u,createOptionRef:c}=f(n),s=c("content");return(h,e)=>(b(),v("div",O,[r("div",V,[e[2]||(e[2]=r("div",{class:"label"},"输入变量",-1)),p(t(g),{vars:t(a),"onUpdate:vars":e[0]||(e[0]=i=>o(a)?a.value=i:null),prevVariables:t(m)},null,8,["vars","prevVariables"])]),r("div",q,[e[3]||(e[3]=r("div",{class:"label"},"回复内容",-1)),p(t(y),{value:t(s),"onUpdate:value":e[1]||(e[1]=i=>o(s)?s.value=i:null),height:140,varsOptions:t(u),placeholder:"请输入回复内容。按下 “/” 可以选择变量"},null,8,["value","varsOptions"])])]))}});export{E as $};
