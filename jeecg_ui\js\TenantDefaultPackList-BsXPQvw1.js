var V=Object.defineProperty,A=Object.defineProperties;var D=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable;var b=(e,t,o)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,C=(e,t)=>{for(var o in t||(t={}))E.call(t,o)&&b(e,o,t[o]);if(y)for(var o of y(t))K.call(t,o)&&b(e,o,t[o]);return e},w=(e,t)=>A(e,D(t));var s=(e,t,o)=>new Promise((g,l)=>{var a=r=>{try{c(o.next(r))}catch(p){l(p)}},_=r=>{try{c(o.throw(r))}catch(p){l(p)}},c=r=>r.done?g(r.value):Promise.resolve(r.value).then(a,_);c((o=o.apply(e,t)).next())});import{d as P,ag as L,aq as O,ar as M,k as d,aD as f,u as n,aB as q,ah as G,G as x}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{k as S,p as Q}from"./tenant.api-CTNrRQ_d.js";import{p as $,d as z}from"./tenant.data-BEsk-IZ-.js";import H from"./TenantPackMenuModal-CVBiAp3Q.js";import{ad as T,ah as J,u as W}from"./index-CCWaWN5g.js";import{useListPage as X}from"./useListPage-Soxgnx9a.js";import{M as Y}from"./antd-vue-vendor-me9YkNVC.js";import{Q as Z}from"./componentMap-Bkie1n3v.js";import tt from"./BasicTable-xCEZpGLb.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./renderUtils-D7XVOFwj.js";import"./index-QxsVJqiT.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const ot=P({name:"tenant-default-pack"}),go=P(w(C({},ot),{setup(e){const{createMessage:t}=W(),[o,{openModal:g}]=T(),[l,{openModal:a}]=T(),_=J(),{prefixCls:c,tableContext:r}=X({designScope:"tenant-template",tableProps:{api:Q,columns:z,formConfig:{schemas:$},beforeFetch:i=>Object.assign(i,{packType:"default"})}}),[p,{reload:R},{rowSelection:v,selectedRowKeys:m,selectedRows:et}]=r;function B(i){return[{label:"编辑",onClick:I.bind(null,i)},{label:"删除",popConfirm:{title:"是否确认删除租户套餐包",confirm:j.bind(null,i.id)}}]}function F(){a(!0,{isUpdate:!1,packType:"default",showFooter:!0})}function j(i){return s(this,null,function*(){yield S({ids:i},k)})}function I(i){a(!0,{isUpdate:!0,record:i,packType:"default",showFooter:!0})}function rt(){return s(this,null,function*(){if(n(m).length>1){t.warn("请选择一个");return}a(!0,{tenantId:n(m.value.join(","))})})}function k(){(m.value=[])&&R()}function N(){return s(this,null,function*(){Y.confirm({title:"删除租户套餐包",content:"是否删除租户套餐包",okText:"确认",cancelText:"取消",onOk:()=>s(null,null,function*(){yield S({ids:m.value.join(",")},k)})})})}return(i,u)=>{const h=L("a-button");return M(),O("div",null,[d(n(tt),{onRegister:n(p),rowSelection:n(v)},{tableTitle:f(()=>[d(h,{preIcon:"ant-design:user-add-outlined",type:"primary",onClick:F},{default:f(()=>u[0]||(u[0]=[x("新增")])),_:1,__:[0]}),n(m).length>0?(M(),q(h,{key:0,preIcon:"ant-design:delete-outlined",type:"primary",onClick:N,style:{"margin-right":"5px"}},{default:f(()=>u[1]||(u[1]=[x("批量删除 ")])),_:1,__:[1]})):G("",!0)]),action:f(({record:U})=>[d(n(Z),{actions:B(U)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"]),d(H,{onRegister:n(l),onSuccess:k},null,8,["onRegister"])])}}}));export{go as default};
