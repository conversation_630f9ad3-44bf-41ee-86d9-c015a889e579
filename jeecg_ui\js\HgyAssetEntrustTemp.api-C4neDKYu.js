import{u as s,j as r}from"./index-CCWaWN5g.js";const d=[{title:"资产名称",align:"center",dataIndex:"assetName"},{title:"资产类型",align:"center",dataIndex:"assetType_dictText"},{title:"资产数量",align:"center",dataIndex:"quantity"},{title:"计量单位",align:"center",dataIndex:"unit"},{title:"省份",align:"center",dataIndex:"province"},{title:"城市",align:"center",dataIndex:"city"},{title:"区县",align:"center",dataIndex:"district"},{title:"详细地址",align:"center",dataIndex:"address"},{title:"特殊说明",align:"center",dataIndex:"specialNotes"},{title:"创建时间",align:"center",dataIndex:"createTime"},{title:"更新时间",align:"center",dataIndex:"updateTime"},{title:"创建人",align:"center",dataIndex:"createBy"},{title:"更新人",align:"center",dataIndex:"updateBy"}],c=[{label:"资产名称",field:"assetName",component:"JInput"}],i=[{label:"资产名称",field:"assetName",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入资产名称!"}]},{label:"资产类型",field:"assetType",component:"JDictSelectTag",componentProps:{dictCode:""}},{label:"资产数量",field:"quantity",component:"InputNumber"},{label:"计量单位",field:"unit",component:"Input"},{label:"省份",field:"province",component:"Input"},{label:"城市",field:"city",component:"Input"},{label:"区县",field:"district",component:"Input"},{label:"详细地址",field:"address",component:"Input"},{label:"特殊说明",field:"specialNotes",component:"Input"},{label:"",field:"id",component:"Input",show:!1}],o={assetName:{title:"资产名称",order:0,view:"text",type:"string"},assetType:{title:"资产类型",order:1,view:"number",type:"number",dictCode:""},quantity:{title:"资产数量",order:2,view:"number",type:"number"},unit:{title:"计量单位",order:3,view:"text",type:"string"},province:{title:"省份",order:4,view:"text",type:"string"},city:{title:"城市",order:5,view:"text",type:"string"},district:{title:"区县",order:6,view:"text",type:"string"},address:{title:"详细地址",order:7,view:"text",type:"string"},specialNotes:{title:"特殊说明",order:8,view:"text",type:"string"},createTime:{title:"创建时间",order:9,view:"datetime",type:"string"},updateTime:{title:"更新时间",order:10,view:"datetime",type:"string"},createBy:{title:"创建人",order:12,view:"text",type:"string"},updateBy:{title:"更新人",order:13,view:"text",type:"string"}};function u(e){return i}const{createConfirm:a}=s();const p="/hgy/entrustService/hgyAssetEntrustTemp/exportXls",m="/hgy/entrustService/hgyAssetEntrustTemp/importExcel",g=e=>r.get({url:"/hgy/entrustService/hgyAssetEntrustTemp/list",params:e}),y=(e,t)=>r.delete({url:"/hgy/entrustService/hgyAssetEntrustTemp/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),h=(e,t)=>{a({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>r.delete({url:"/hgy/entrustService/hgyAssetEntrustTemp/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},v=(e,t)=>{let n=t?"/hgy/entrustService/hgyAssetEntrustTemp/edit":"/hgy/entrustService/hgyAssetEntrustTemp/add";return r.post({url:n,params:e})};export{p as a,h as b,c,d,y as e,v as f,m as g,i as h,u as i,g as l,o as s};
