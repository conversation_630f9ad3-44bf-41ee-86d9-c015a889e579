var M=Object.defineProperty,q=Object.defineProperties;var L=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var Y=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable;var v=(e,t,o)=>t in e?M(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,T=(e,t)=>{for(var o in t||(t={}))Y.call(t,o)&&v(e,o,t[o]);if(y)for(var o of y(t))K.call(t,o)&&v(e,o,t[o]);return e},b=(e,t)=>q(e,L(t));import{d as P,f as u,ag as _,aB as N,ar as g,aD as h,aq as w,ah as I,at as r,k as i,au as f,u as C}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{j as V,u as A,a as F}from"./index-CCWaWN5g.js";import{useListPage as W}from"./useListPage-Soxgnx9a.js";import j from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const z=e=>V.get({url:"/sys/log/list",params:e}),x=[{title:"日志内容",dataIndex:"logContent",width:100,align:"left"},{title:"操作人ID",dataIndex:"userid",width:80},{title:"操作人",dataIndex:"username",width:80},{title:"IP",dataIndex:"ip",width:80},{title:"耗时(毫秒)",dataIndex:"costTime",width:80},{title:"创建时间",dataIndex:"createTime",sorter:!0,width:80},{title:"客户端类型",dataIndex:"clientType_dictText",width:60}],E=[...x,{title:"操作类型",dataIndex:"operateType_dictText",width:40}],H=[{title:"异常标题",dataIndex:"logContent",width:100,align:"left"},{title:"请求地址",dataIndex:"requestUrl",width:100},{title:"请求参数",dataIndex:"method",width:60},{title:"操作人",dataIndex:"username",width:60,customRender:({record:e})=>{let t=e.username,o=e.userid;return!t&&!o?"":t+" (账号: "+o+" )"}},{title:"IP",dataIndex:"ip",width:60},{title:"创建时间",dataIndex:"createTime",sorter:!0,width:60},{title:"客户端类型",dataIndex:"clientType_dictText",width:60}],p=[{field:"keyWord",label:"搜索日志",component:"Input",colProps:{span:8}},{field:"fieldTime",component:"RangePicker",label:"创建时间",componentProps:{valueType:"Date"},colProps:{span:6}}],J=[...p,{field:"operateType",label:"操作类型",component:"JDictSelectTag",colProps:{span:4},componentProps:{dictCode:"operate_type"}}],U={key:0},G={style:{"margin-bottom":"5px"}},O={style:{"vertical-align":"middle"}},Q={style:{"vertical-align":"middle"}},X={key:1},Z={style:{"margin-bottom":"5px"}},$={class:"error-box",style:{"vertical-align":"middle"}},tt=P({name:"monitor-log"}),et=P(b(T({},tt),{setup(e){const{createMessage:t}=A(),o=u([]),s=u(x),n=u(p),l={logType:"4"},{prefixCls:ot,tableContext:k}=W({designScope:"user-list",tableProps:{title:"日志列表",api:z,expandRowByClick:!0,showActionColumn:!1,rowSelection:{columnWidth:20},formConfig:{schemas:n,fieldMapToTime:[["fieldTime",["createTime_begin","createTime_end"],"YYYY-MM-DD"]]}}}),[S,{reload:D}]=k;function R(a){l.logType=a,a=="2"?(s.value=E,n.value=J):a=="4"?(n.value=p,s.value=H):(n.value=p,s.value=x),D()}function it(a){o.value=a}return(a,at)=>{const m=_("a-tab-pane"),B=_("a-tabs"),d=_("a-badge");return g(),N(C(j),{ellipsis:!0,onRegister:C(S),searchInfo:l,columns:s.value,"expand-column-width":16},{tableTitle:h(()=>[i(B,{defaultActiveKey:"4",onChange:R,size:"small"},{default:h(()=>[i(m,{tab:"异常日志",key:"4"}),i(m,{tab:"登录日志",key:"1"}),i(m,{tab:"操作日志",key:"2"})]),_:1})]),expandedRowRender:h(({record:c})=>[l.logType==2?(g(),w("div",U,[r("div",G,[i(d,{status:"success",style:{"vertical-align":"middle"}}),r("span",O,"请求方法:"+f(c.method),1)]),r("div",null,[i(d,{status:"processing",style:{"vertical-align":"middle"}}),r("span",Q,"请求参数:"+f(c.requestParam),1)])])):I("",!0),l.logType==4?(g(),w("div",X,[r("div",Z,[i(d,{status:"success",style:{"vertical-align":"middle"}}),r("span",$,"异常堆栈:"+f(c.requestParam),1)])])):I("",!0)]),_:1},8,["onRegister","columns"])}}})),me=F(et,[["__scopeId","data-v-554fdab3"]]);export{me as default};
