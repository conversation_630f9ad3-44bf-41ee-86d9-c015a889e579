import{d as p,aq as r,ar as s,F as u,aC as f,aB as m,u as e,as as x,aD as l,at as o,k as t,au as n,G as _}from"./vue-vendor-dy9k-Yad.js";import{C as i}from"./index-CWWAi5V9.js";import{c as g}from"./index-CCWaWN5g.js";import{J as C,X as V}from"./antd-vue-vendor-me9YkNVC.js";import{g as w}from"./data-BpJ37qIE.js";import"./vxe-table-vendor-B22HppNm.js";const y={class:"md:flex"},B={class:"py-4 px-4 flex justify-between"},h={class:"p-2 px-4 flex justify-between"},E=p({__name:"GrowCard",props:{loading:{type:Boolean}},setup(c){return(k,v)=>(s(),r("div",y,[(s(!0),r(u,null,f(e(w),(a,d)=>(s(),m(e(C),{key:a.title,size:"small",loading:c.loading,title:a.title,class:x(["md:w-1/4 w-full !md:mt-0 !mt-4",[d+1<4&&"!md:mr-4"]]),canExpan:!1},{extra:l(()=>[t(e(V),{color:a.color},{default:l(()=>[_(n(a.action),1)]),_:2},1032,["color"])]),default:l(()=>[o("div",B,[t(e(i),{prefix:"$",startVal:1,endVal:a.value,class:"text-2xl"},null,8,["endVal"]),t(e(g),{icon:a.icon,size:40},null,8,["icon"])]),o("div",h,[o("span",null,"总"+n(a.title),1),t(e(i),{prefix:"$",startVal:1,endVal:a.total},null,8,["endVal"])])]),_:2},1032,["loading","title","class"]))),128))]))}});export{E as default};
