import{d as k,ag as t,aB as C,ar as R,aD as n,k as o,G as m}from"./vue-vendor-dy9k-Yad.js";import{A as $}from"./antd-vue-vendor-me9YkNVC.js";import{a as s}from"./index-JbqXEynz.js";import P from"./Drawer1-DPV88m_2.js";import v from"./Drawer2-BI70KwuW.js";import A from"./Drawer3-Bts-ImPu.js";import B from"./Drawer4-DTQ9keKj.js";import W from"./Drawer5-BBDToChK.js";import{P as b}from"./index-CtJ0w2CP.js";import{a as L}from"./index-CCWaWN5g.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const N=k({components:{Alert:$,PageWrapper:b,Drawer1:P,Drawer2:v,Drawer3:A,Drawer4:B,Drawer5:W},setup(){const[e,{openDrawer:r,setDrawerProps:a}]=s(),[f,{openDrawer:d}]=s(),[y,{openDrawer:i}]=s(),[p,{openDrawer:w}]=s(),[l,{openDrawer:D}]=s();function g(){w(!0,{data:"content",info:"Info"})}function u(){r(),a({loading:!0}),setTimeout(()=>{a({loading:!1})},2e3)}return{register1:e,openDrawer1:r,register2:f,openDrawer2:d,register3:y,openDrawer3:i,register4:p,register5:l,openDrawer5:D,send:g,openDrawerLoading:u}}});function T(e,r,a,f,d,y){const i=t("Alert"),p=t("a-button"),w=t("Drawer1"),l=t("Drawer2"),D=t("Drawer3"),g=t("Drawer4"),u=t("Drawer5"),c=t("PageWrapper");return R(),C(c,{title:"抽屉组件使用示例"},{default:n(()=>[o(i,{message:"使用 useDrawer 进行抽屉操作","show-icon":""}),o(p,{type:"primary",class:"my-4",onClick:e.openDrawerLoading},{default:n(()=>r[3]||(r[3]=[m(" 打开Drawer ")])),_:1,__:[3]},8,["onClick"]),o(i,{message:"内外同时控制显示隐藏","show-icon":""}),o(p,{type:"primary",class:"my-4",onClick:r[0]||(r[0]=_=>e.openDrawer2(!0))},{default:n(()=>r[4]||(r[4]=[m(" 打开Drawer ")])),_:1,__:[4]}),o(i,{message:"自适应高度/显示footer","show-icon":""}),o(p,{type:"primary",class:"my-4",onClick:r[1]||(r[1]=_=>e.openDrawer3(!0))},{default:n(()=>r[5]||(r[5]=[m(" 打开Drawer ")])),_:1,__:[5]}),o(i,{message:"内外数据交互","show-icon":""}),o(p,{type:"primary",class:"my-4",onClick:e.send},{default:n(()=>r[6]||(r[6]=[m(" 打开Drawer并传递数据 ")])),_:1,__:[6]},8,["onClick"]),o(i,{message:"详情页模式","show-icon":""}),o(p,{type:"primary",class:"my-4",onClick:r[2]||(r[2]=_=>e.openDrawer5(!0))},{default:n(()=>r[7]||(r[7]=[m(" 打开详情Drawer ")])),_:1,__:[7]}),o(w,{onRegister:e.register1},null,8,["onRegister"]),o(l,{onRegister:e.register2},null,8,["onRegister"]),o(D,{onRegister:e.register3},null,8,["onRegister"]),o(g,{onRegister:e.register4},null,8,["onRegister"]),o(u,{onRegister:e.register5},null,8,["onRegister"])]),_:1})}const Mr=L(N,[["render",T]]);export{Mr as default};
