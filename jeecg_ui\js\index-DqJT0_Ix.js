var u=(t,o,r)=>new Promise((m,i)=>{var a=e=>{try{p(r.next(e))}catch(n){i(n)}},s=e=>{try{p(r.throw(e))}catch(n){i(n)}},p=e=>e.done?m(e.value):Promise.resolve(e.value).then(a,s);p((r=r.apply(t,o)).next())});import{d as w,ag as c,aB as g,ar as P,aD as l,at as f,k as d,G as _}from"./vue-vendor-dy9k-Yad.js";import{P as h}from"./index-CtJ0w2CP.js";import"./index-L3cSIXth.js";import{B as b}from"./BasicForm-DBcXiHk0.js";import{u as B}from"./useForm-CgkFTrrO.js";import{a as y}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const C=[{field:"passwordOld",label:"当前密码",component:"InputPassword",required:!0},{field:"passwordNew",label:"新密码",component:"StrengthMeter",componentProps:{placeholder:"新密码"},rules:[{required:!0,message:"请输入新密码"}]},{field:"confirmPassword",label:"确认密码",component:"InputPassword",dynamicRules:({values:t})=>[{required:!0,validator:(o,r)=>r?r!==t.passwordNew?Promise.reject("两次输入的密码不一致!"):Promise.resolve():Promise.reject("不能为空")}]}],k=w({name:"ChangePassword",components:{BasicForm:b,PageWrapper:h},setup(){const[t,{validate:o,resetFields:r}]=B({size:"large",labelWidth:100,showActionButtonGroup:!1,schemas:C});function m(){return u(this,null,function*(){try{const i=yield o(),{passwordOld:a,passwordNew:s}=i}catch(i){}})}return{register:t,resetFields:r,handleSubmit:m}}}),v={class:"py-8 bg-white flex flex-col justify-center items-center"},x={class:"flex justify-center"};function F(t,o,r,m,i,a){const s=c("BasicForm"),p=c("a-button"),e=c("PageWrapper");return P(),g(e,{title:"修改当前用户密码",content:"修改成功后会自动退出当前登录！"},{default:l(()=>[f("div",v,[d(s,{onRegister:t.register},null,8,["onRegister"]),f("div",x,[d(p,{onClick:t.resetFields},{default:l(()=>o[0]||(o[0]=[_(" 重置 ")])),_:1,__:[0]},8,["onClick"]),d(p,{class:"!ml-4",type:"primary",onClick:t.handleSubmit},{default:l(()=>o[1]||(o[1]=[_(" 确认 ")])),_:1,__:[1]},8,["onClick"])])])]),_:1})}const Rr=y(k,[["render",F]]);export{Rr as default};
