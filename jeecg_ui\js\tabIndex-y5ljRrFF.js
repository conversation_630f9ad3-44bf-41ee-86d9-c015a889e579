import{d as u,f as l,e as _,ag as r,aq as n,ar as m,k as s,aD as b,aB as c,F as y,aC as T,aQ as k}from"./vue-vendor-dy9k-Yad.js";import C from"./AuthColumnDemo-CmJ32EVo.js";import B from"./BasicTableBorder-mKXTZP8w.js";import g from"./BasicTableDemo-CxyT33Pq.js";import v from"./BasicTableDemoAjax-Crxydtvg.js";import $ from"./CustomerCellDemo-CWl0ALs6.js";import h from"./EditCellTableDemo-CpWtKzVY.js";import E from"./EditRowTableDemo-CHegHTES.js";import x from"./ExpandTableDemo-CYeS3f8U.js";import A from"./ExportTableDemo-PnLDzPiU.js";import K from"./FixedHeaderColumn-BevF5bsD.js";import F from"./InnerTableDemo-DY8tLxTR.js";import H from"./MergeHeaderDemo-B1blCC2p.js";import M from"./MergeTableDemo-BfgxNYql.js";import w from"./SelectTableDemo-DQuNHfnR.js";import I from"./TreeTableDemo-jV-NPoxt.js";import{a as L}from"./index-CCWaWN5g.js";import"./index-BkGZ5fiW.js";import"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";import"./table-BDFKJhHv.js";import"./useListPage-Soxgnx9a.js";import"./select-nVA4yav1.js";import"./tree-C9AW4Mg9.js";import"./index-CtJ0w2CP.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";const j=u({name:"document-table-demo",components:{AuthColumnDemo:C,BasicTableBorder:B,BasicTableDemo:g,BasicTableDemoAjax:v,CustomerCellDemo:$,EditCellTableDemo:h,EditRowTableDemo:E,ExpandTableDemo:x,ExportTableDemo:A,FixedHeaderColumn:K,InnerTableDemo:F,MergeHeaderDemo:H,MergeTableDemo:M,SelectTableDemo:w,TreeTableDemo:I},setup(){const e=l("BasicTableDemo"),o=l([{key:"BasicTableDemo",label:"基础静态表格"},{key:"BasicTableDemoAjax",label:"常规AJAX表格"},{key:"BasicTableBorder",label:"边框表格"},{key:"CustomerCellDemo",label:"自定义列内容"},{key:"EditCellTableDemo",label:"可编辑单元格"},{key:"EditRowTableDemo",label:"可编辑行"},{key:"ExpandTableDemo",label:"可展开表格"},{key:"ExportTableDemo",label:"导入导出"},{key:"FixedHeaderColumn",label:"固定头和列示例"},{key:"InnerTableDemo",label:"内嵌表格"},{key:"MergeHeaderDemo",label:"分组表头示例"},{key:"MergeTableDemo",label:"合并行列"},{key:"SelectTableDemo",label:"可选择表格"},{key:"TreeTableDemo",label:"树形表格"},{key:"AuthColumnDemo",label:"权限列设置"}]),a=_(()=>e.value);function i(p){e.value=p}return{activeKey:e,currentComponent:a,tabChange:i,compList:o}}}),R={class:"p-4"};function S(e,o,a,i,p,q){const d=r("a-tab-pane"),f=r("a-tabs"),D=r("a-card");return m(),n("div",R,[s(D,{bordered:!1,style:{height:"100%"}},{default:b(()=>[s(f,{activeKey:e.activeKey,"onUpdate:activeKey":o[0]||(o[0]=t=>e.activeKey=t),onChange:e.tabChange},{default:b(()=>[(m(!0),n(y,null,T(e.compList,t=>(m(),c(d,{key:t.key,tab:t.label},null,8,["tab"]))),128))]),_:1},8,["activeKey","onChange"]),(m(),c(k(e.currentComponent)))]),_:1})])}const fo=L(j,[["render",S]]);export{fo as default};
