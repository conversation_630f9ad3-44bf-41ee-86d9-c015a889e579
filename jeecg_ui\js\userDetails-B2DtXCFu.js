import{d as a,ag as n,aB as i,ar as m}from"./vue-vendor-dy9k-Yad.js";import{D as p,u as c}from"./index-Dce_QJ6p.js";import{a as l}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";const r={username:"test",nickName:"VB",age:"123",phone:"15695909xxx",email:"<EMAIL>",addr:"厦门市思明区",sex:"男",certy:"3504256199xxxxxxxxx",tag:"orange"},o=[{field:"username",label:"用户名"},{field:"nickName",label:"昵称",render:(e,t)=>`${t.username}-${e}`},{field:"phone",label:"联系电话"},{field:"email",label:"邮箱"},{field:"addr",label:"地址"}],d=a({components:{Description:p},setup(){const[e]=c({title:"useDescription",data:r,schema:o});return{mockData:r,schema:o,register:e}}});function u(e,t,x,f,_,g){const s=n("Description");return m(),i(s,{onRegister:e.register,class:"mt-4"},null,8,["onRegister"])}const y=l(d,[["render",u]]);export{y as default};
