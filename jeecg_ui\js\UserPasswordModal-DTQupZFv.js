var O=Object.defineProperty,j=Object.defineProperties;var A=Object.getOwnPropertyDescriptors;var P=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var k=(a,o,s)=>o in a?O(a,o,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[o]=s,y=(a,o)=>{for(var s in o||(o={}))E.call(o,s)&&k(a,s,o[s]);if(P)for(var s of P(o))N.call(o,s)&&k(a,s,o[s]);return a},C=(a,o)=>j(a,A(o));var M=(a,o,s)=>new Promise((f,m)=>{var _=r=>{try{g(s.next(r))}catch(i){m(i)}},t=r=>{try{g(s.throw(r))}catch(i){m(i)}},g=r=>r.done?f(r.value):Promise.resolve(r.value).then(_,t);g((s=s.apply(a,o)).next())});import{d as B,f as b,r as T,ag as w,aB as V,ar as $,aD as u,k as l,at as c,u as v,aE as D}from"./vue-vendor-dy9k-Yad.js";import{B as F}from"./index-Diw57m_E.js";import{d as H}from"./UserSetting.api-BJ086Ekj.js";import{ac as Z,ah as G,u as J,j as K,a as Q}from"./index-CCWaWN5g.js";import{C as W}from"./index-DFrpKMGa.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";const X={class:"pass-padding"},Y={class:"pass-padding"},ee=B({name:"user-pass-word-modal"}),se=B(C(y({},ee),{emits:["success","register"],setup(a,{emit:o}){const{createMessage:s,createErrorModal:f}=J(),m=b(""),_=b(),t=T({oldpassword:"",password:"",smscode:"",phone:""}),g=o,[r,{setModalProps:i,closeModal:R}]=Z(n=>M(null,null,function*(){i({confirmLoading:!1}),m.value=n.record.username,Object.assign(t,{password:"",smscode:"",phone:""})})),U=G(),S={password:[{required:!0,validator:q},{pattern:/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/,message:"8-20位，需包含字母和数字"}],phone:[{required:!0,message:"请输入手机号"}],smscode:[{required:!0,message:"请输入6位验证码"}]};function z(){return M(this,null,function*(){try{let n=yield _.value.validateFields();i({confirmLoading:!0}),n.username=v(m),yield H(n).then(e=>{e.success?(s.info({content:"密码修改成功，请重新登录！3s后自动退出登录",duration:3}),setTimeout(()=>{U.logout(!0)},3e3),R()):s.warn(e.message)})}finally{i({confirmLoading:!1})}})}function q(n,e){return e===""?Promise.reject("请输入新密码"):Promise.resolve()}function x(){return new Promise((n,e)=>{let h={mobile:t.phone};K.post({url:"/sys/sendChangePwdSms",params:h},{isTransformResponse:!1}).then(d=>{d.success?n(!0):(f({title:"错误提示",content:d.message||"未知问题"}),e())}).catch(d=>{f({title:"错误提示",content:d.message||"未知问题"}),e()})})}return(n,e)=>{const h=w("a-input"),d=w("a-form-item"),I=w("a-input-password"),L=w("a-form");return $(),V(v(F),D(n.$attrs,{onRegister:v(r),title:"修改密码",onOk:z,destroyOnClose:"",width:400}),{default:u(()=>[l(L,{class:"antd-modal-form",ref_key:"formRef",ref:_,model:t,rules:S},{default:u(()=>[l(d,{name:"phone"},{default:u(()=>[e[3]||(e[3]=c("div",{class:"black font-size-13"},"验证手机号",-1)),c("div",X,[l(h,{placeholder:"请输入手机号",value:t.phone,"onUpdate:value":e[0]||(e[0]=p=>t.phone=p)},null,8,["value"])])]),_:1,__:[3]}),l(d,{name:"smscode"},{default:u(()=>[l(v(W),{value:t.smscode,"onUpdate:value":e[1]||(e[1]=p=>t.smscode=p),placeholder:"请输入6位验证码",sendCodeApi:x},null,8,["value"])]),_:1}),l(d,{name:"password"},{default:u(()=>[e[4]||(e[4]=c("span",{class:"black font-size-13"},"新密码",-1)),c("div",Y,[l(I,{value:t.password,"onUpdate:value":e[2]||(e[2]=p=>t.password=p),placeholder:"新密码",autocomplete:"new-password"},null,8,["value"])]),e[5]||(e[5]=c("span",{class:"gray-9e font-size-13"},"8-20位，需包含字母和数字",-1))]),_:1,__:[4,5]})]),_:1},8,["model"])]),_:1},16,["onRegister"])}}})),ke=Q(se,[["__scopeId","data-v-a9acbd71"]]);export{ke as default};
