var G=Object.defineProperty;var M=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable;var N=(f,p,d)=>p in f?G(f,p,{enumerable:!0,configurable:!0,writable:!0,value:d}):f[p]=d,F=(f,p)=>{for(var d in p||(p={}))L.call(p,d)&&N(f,d,p[d]);if(M)for(var d of M(p))H.call(p,d)&&N(f,d,p[d]);return f};var S=(f,p,d)=>new Promise((T,_)=>{var b=h=>{try{g(d.next(h))}catch(B){_(B)}},u=h=>{try{g(d.throw(h))}catch(B){_(B)}},g=h=>h.done?T(h.value):Promise.resolve(h.value).then(b,u);g((d=d.apply(f,p)).next())});import{d as E,f as k,w as V,ag as y,aq as P,ar as $,as as K,at as i,k as e,u as z,r as Q,e as W,aD as l,F as X,aC as Y,aB as D,ah as Z,au as x,G as v}from"./vue-vendor-dy9k-Yad.js";import{_ as O}from"./JUpload-CRos0F1P.js";import{i as ee}from"./JAreaSelect-Db7Nhhc_.js";import"./index-L3cSIXth.js";import{a as R,aU as te}from"./index-CCWaWN5g.js";import{J as ae}from"./JEditorTiptap-BwAoWsi9.js";import"./useFormItem-CHvpjy4o.js";import"./antd-vue-vendor-me9YkNVC.js";import"./areaDataUtil-BXVjRArW.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-ByPySmGo.js";const oe={class:"duration-inputs"},se={class:"duration-input-group"},re={class:"duration-input-group"},le={class:"duration-input-group"},ie={class:"duration-input-group"},ne=E({__name:"DurationPicker",props:{value:{default:""},placeholder:{default:"请选择时长"},size:{default:"default"}},emits:["update:value","change"],setup(f,{emit:p}){const d=f,T=p,_=k(0),b=k(0),u=k(0),g=k(0),h=Array.from({length:11},(o,r)=>({label:r.toString(),value:r})),B=Array.from({length:24},(o,r)=>({label:r.toString(),value:r})),C=Array.from({length:60},(o,r)=>({label:r.toString(),value:r})),c=Array.from({length:60},(o,r)=>({label:r.toString(),value:r})),a=o=>{if(!o){_.value=0,b.value=0,u.value=0,g.value=0;return}const r=o.match(/(\d+)[天d]/),I=o.match(/(\d+)[小时h]/),m=o.match(/(\d+)[分钟m]/),q=o.match(/(\d+)[秒s]/);_.value=r?parseInt(r[1]):0,b.value=I?parseInt(I[1]):0,u.value=m?parseInt(m[1]):0,g.value=q?parseInt(q[1]):0},U=()=>{const o=[];return _.value>0&&o.push(`${_.value}天`),b.value>0&&o.push(`${b.value}小时`),u.value>0&&o.push(`${u.value}分钟`),g.value>0&&o.push(`${g.value}秒`),o.join("")},w=()=>{const o=U();T("update:value",o),T("change",o)};return V(()=>d.value,o=>{a(o)},{immediate:!0}),V([_,b,u,g],()=>{w()}),(o,r)=>{const I=y("a-select");return $(),P("div",{class:K(["duration-picker",`duration-picker-${o.size}`])},[i("div",oe,[i("div",se,[e(I,{value:_.value,"onUpdate:value":r[0]||(r[0]=m=>_.value=m),size:o.size,options:z(h)},null,8,["value","size","options"]),r[4]||(r[4]=i("span",{class:"duration-unit"},"天",-1))]),i("div",re,[e(I,{value:b.value,"onUpdate:value":r[1]||(r[1]=m=>b.value=m),size:o.size,options:z(B)},null,8,["value","size","options"]),r[5]||(r[5]=i("span",{class:"duration-unit"},"小时",-1))]),i("div",le,[e(I,{value:u.value,"onUpdate:value":r[2]||(r[2]=m=>u.value=m),size:o.size,options:z(C)},null,8,["value","size","options"]),r[6]||(r[6]=i("span",{class:"duration-unit"},"分钟",-1))]),i("div",ie,[e(I,{value:g.value,"onUpdate:value":r[3]||(r[3]=m=>g.value=m),size:o.size,options:z(c)},null,8,["value","size","options"]),r[7]||(r[7]=i("span",{class:"duration-unit"},"秒",-1))])])],2)}}}),A=R(ne,[["__scopeId","data-v-30fea7ce"]]),ue={class:"step2"},de={class:"section-content"},me={class:"item-header"},pe={class:"item-title"},ce={class:"item-section"},ve={class:"form-row"},fe={class:"form-row"},_e={class:"form-row"},ge={class:"item-section"},be={class:"form-row location-row"},he={class:"item-section"},Ie={class:"form-row basic-three-row"},ye={class:"form-row basic-three-row"},Ue={class:"form-row basic-three-row"},we={class:"form-row basic-three-row"},qe={class:"form-row basic-three-row"},ze={class:"form-row basic-three-row"},Te={class:"item-section"},Be={class:"item-section"},$e={class:"form-row"},ke={class:"upload-container"},Pe={class:"form-row"},Ve={class:"upload-container"},Ce=E({__name:"Step2",props:{modelValue:{},serviceType:{}},emits:["update:modelValue"],setup(f,{expose:p,emit:d}){const T=k(),_=f,b=d,u=Q(F({},_.modelValue));V(u,c=>{b("update:modelValue",F({},c))},{deep:!0}),V(()=>_.modelValue,c=>{Object.assign(u,c)},{deep:!0});const g=W(()=>{const c={auctionName:[{required:!0,message:"请输入关联拍卖会",trigger:"blur"}],itemTitle:[{required:!0,message:"请输入标的标题",trigger:"blur"}],itemType:[{required:!0,message:"请选择标的分类",trigger:"change"}],province:[{required:!0,message:"请选择存放位置",trigger:"change"}],address:[{required:!0,message:"请输入详细地址",trigger:"blur"}],startPrice:[{required:!0,message:"请输入起拍价",trigger:"blur"}],appraisalPrice:[{required:!0,message:"请输入评估价格",trigger:"blur"}],deposit:[{required:!0,message:"请输入保证金",trigger:"blur"}],quantity:[{required:!0,message:"请输入标的数量",trigger:"blur"}],unit:[{required:!0,message:"请选择标的单位",trigger:"change"}],quantityFlag:[{required:!0,message:"请选择是否展示实际数量",trigger:"change"}],auctionMode:[{required:!0,message:"请选择拍卖方式",trigger:"change"}],description:[{required:!0,message:"请输入拍卖公告",trigger:"blur"}]},a={};return u.auctionItems.forEach((U,w)=>{a[w]=c}),{auctionItems:a}}),h=()=>S(null,null,function*(){var c;try{return yield(c=T.value)==null?void 0:c.validate(),!0}catch(a){const U=document.querySelector(".ant-form-item-has-error");return U&&U.scrollIntoView({behavior:"smooth",block:"center"}),!1}}),B=()=>{const c={auctionName:"",itemTitle:"",itemType:1,province:"",city:"",district:"",address:"",startPrice:0,appraisalPrice:0,reservePrice:0,deposit:0,quantity:1,unit:"件",quantityFlag:0,showCommission:"",bidIncrement:0,auctionMode:1,freeBidTime:"",timedBidTime:"",description:"",coverImage:"",itemImages:[]};u.auctionItems.push(c)},C=c=>{u.auctionItems.length>1&&u.auctionItems.splice(c,1)};return p({validateForm:h}),(c,a)=>{const U=y("a-button"),w=y("a-input"),o=y("a-form-item"),r=y("a-radio"),I=y("a-radio-group"),m=y("a-input-number"),q=y("a-select-option"),j=y("a-select"),J=y("a-form");return $(),P("div",ue,[e(J,{model:u,rules:g.value,ref_key:"formRef",ref:T,"scroll-to-first-error":!0},{default:l(()=>[i("div",de,[($(!0),P(X,null,Y(u.auctionItems,(s,n)=>($(),P("div",{key:n,class:"auction-item"},[i("div",me,[i("span",pe,"标的"+x(n+1),1),n===u.auctionItems.length-1?($(),D(U,{key:0,class:"action-btn add-btn",onClick:B},{default:l(()=>[e(z(te),{name:"add",size:12}),a[0]||(a[0]=i("span",null,"新增",-1))]),_:1,__:[0]})):u.auctionItems.length>1?($(),D(U,{key:1,class:"action-btn delete-btn",onClick:t=>C(n)},{default:l(()=>a[1]||(a[1]=[v(" 删除 ")])),_:2,__:[1]},1032,["onClick"])):Z("",!0)]),i("div",ce,[a[7]||(a[7]=i("div",{class:"section-subtitle"},"基本信息",-1)),i("div",ve,[e(o,{label:"关联拍卖会",name:["auctionItems",n,"auctionName"],required:"",class:"form-item-third"},{default:l(()=>[e(w,{value:s.auctionName,"onUpdate:value":t=>s.auctionName=t,placeholder:"请输入关联拍卖会",size:"large"},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])]),i("div",fe,[e(o,{label:"标的标题",name:["auctionItems",n,"itemTitle"],required:"",class:"form-item-third"},{default:l(()=>[e(w,{value:s.itemTitle,"onUpdate:value":t=>s.itemTitle=t,placeholder:"请输入标的标题",size:"large"},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])]),i("div",_e,[e(o,{label:"标的分类",name:["auctionItems",n,"itemType"],required:"",class:"form-item-third"},{default:l(()=>[e(I,{value:s.itemType,"onUpdate:value":t=>s.itemType=t},{default:l(()=>[e(r,{value:1},{default:l(()=>a[2]||(a[2]=[v("物资/设备")])),_:1,__:[2]}),e(r,{value:2},{default:l(()=>a[3]||(a[3]=[v("机动车")])),_:1,__:[3]}),e(r,{value:3},{default:l(()=>a[4]||(a[4]=[v("房产")])),_:1,__:[4]}),e(r,{value:4},{default:l(()=>a[5]||(a[5]=[v("土地")])),_:1,__:[5]}),e(r,{value:5},{default:l(()=>a[6]||(a[6]=[v("其他")])),_:1,__:[6]})]),_:2},1032,["value","onUpdate:value"])]),_:2},1032,["name"])])]),i("div",ge,[a[8]||(a[8]=i("div",{class:"section-subtitle"},"存放位置",-1)),i("div",be,[e(o,{name:["auctionItems",n,"province"],required:"",class:"location-item"},{default:l(()=>[e(ee,{province:s.province,"onUpdate:province":t=>s.province=t,city:s.city,"onUpdate:city":t=>s.city=t,area:s.district,"onUpdate:area":t=>s.district=t,placeholder:"请选择存放位置",level:3},null,8,["province","onUpdate:province","city","onUpdate:city","area","onUpdate:area"])]),_:2},1032,["name"]),e(o,{label:"详细地址",name:["auctionItems",n,"address"],required:"",class:"location-item"},{default:l(()=>[e(w,{value:s.address,"onUpdate:value":t=>s.address=t,placeholder:"请输入详细地址",size:"large"},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])])]),i("div",he,[a[18]||(a[18]=i("div",{class:"section-subtitle"},"标的设置",-1)),i("div",Ie,[e(o,{label:"起拍价",name:["auctionItems",n,"startPrice"],required:"",class:"basic-three-item"},{default:l(()=>[e(m,{value:s.startPrice,"onUpdate:value":t=>s.startPrice=t,placeholder:"请输入起拍价",size:"large",style:{width:"100%"},min:0,precision:2,"addon-after":"元",formatter:t=>`￥ ${t}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:t=>t.replace(/￥\s?|(,*)/g,"")},null,8,["value","onUpdate:value","formatter","parser"])]),_:2},1032,["name"]),e(o,{label:"评估价格",name:["auctionItems",n,"appraisalPrice"],required:"",class:"basic-three-item"},{default:l(()=>[e(m,{value:s.appraisalPrice,"onUpdate:value":t=>s.appraisalPrice=t,placeholder:"请输入评估价格",size:"large",style:{width:"100%"},min:0,precision:2,"addon-after":"元",formatter:t=>`￥ ${t}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:t=>t.replace(/￥\s?|(,*)/g,"")},null,8,["value","onUpdate:value","formatter","parser"])]),_:2},1032,["name"]),e(o,{class:"placeholder-item"})]),i("div",ye,[e(o,{label:"保留价",name:["auctionItems",n,"reservePrice"],class:"basic-three-item"},{default:l(()=>[e(m,{value:s.reservePrice,"onUpdate:value":t=>s.reservePrice=t,placeholder:"请输入保留价",size:"large",style:{width:"100%"},min:0,precision:2,"addon-after":"元",formatter:t=>`￥ ${t}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:t=>t.replace(/￥\s?|(,*)/g,"")},null,8,["value","onUpdate:value","formatter","parser"])]),_:2},1032,["name"]),e(o,{label:"保证金",name:["auctionItems",n,"deposit"],required:"",class:"basic-three-item"},{default:l(()=>[e(m,{value:s.deposit,"onUpdate:value":t=>s.deposit=t,placeholder:"请输入保证金",size:"large",style:{width:"100%"},min:0,precision:2,"addon-after":"元",formatter:t=>`￥ ${t}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:t=>t.replace(/￥\s?|(,*)/g,"")},null,8,["value","onUpdate:value","formatter","parser"])]),_:2},1032,["name"]),e(o,{class:"placeholder-item"})]),i("div",Ue,[e(o,{label:"标的数量",name:["auctionItems",n,"quantity"],required:"",class:"basic-three-item"},{default:l(()=>[e(m,{value:s.quantity,"onUpdate:value":t=>s.quantity=t,placeholder:"请输入标的数量",size:"large",style:{width:"100%"},min:1},null,8,["value","onUpdate:value"])]),_:2},1032,["name"]),e(o,{label:"标的单位",name:["auctionItems",n,"unit"],required:"",class:"basic-three-item"},{default:l(()=>[e(j,{value:s.unit,"onUpdate:value":t=>s.unit=t,placeholder:"请选择标的单位",size:"large"},{default:l(()=>[e(q,{value:"件"},{default:l(()=>a[9]||(a[9]=[v("件")])),_:1,__:[9]}),e(q,{value:"台"},{default:l(()=>a[10]||(a[10]=[v("台")])),_:1,__:[10]}),e(q,{value:"套"},{default:l(()=>a[11]||(a[11]=[v("套")])),_:1,__:[11]}),e(q,{value:"个"},{default:l(()=>a[12]||(a[12]=[v("个")])),_:1,__:[12]}),e(q,{value:"批"},{default:l(()=>a[13]||(a[13]=[v("批")])),_:1,__:[13]})]),_:2},1032,["value","onUpdate:value"])]),_:2},1032,["name"]),e(o,{class:"placeholder-item"})]),i("div",we,[e(o,{label:"展示佣金",name:["auctionItems",n,"showCommission"],class:"basic-three-item"},{default:l(()=>[e(w,{value:s.showCommission,"onUpdate:value":t=>s.showCommission=t,placeholder:"请输入展示佣金",size:"large"},null,8,["value","onUpdate:value"])]),_:2},1032,["name"]),e(o,{label:"加价幅度",name:["auctionItems",n,"bidIncrement"],class:"basic-three-item"},{default:l(()=>[e(m,{value:s.bidIncrement,"onUpdate:value":t=>s.bidIncrement=t,placeholder:"请输入加价幅度",size:"large",style:{width:"100%"},min:0,precision:2,"addon-after":"元",formatter:t=>`￥ ${t}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:t=>t.replace(/￥\s?|(,*)/g,"")},null,8,["value","onUpdate:value","formatter","parser"])]),_:2},1032,["name"]),e(o,{class:"placeholder-item"})]),i("div",qe,[e(o,{label:"自由竞价",name:["auctionItems",n,"freeBidTime"],class:"basic-three-item"},{default:l(()=>[e(z(A),{value:s.freeBidTime,"onUpdate:value":t=>s.freeBidTime=t,placeholder:"请选择自由竞价时长",size:"large",style:{width:"100%"}},null,8,["value","onUpdate:value"])]),_:2},1032,["name"]),e(o,{label:"限时竞价",name:["auctionItems",n,"timedBidTime"],class:"basic-three-item"},{default:l(()=>[e(z(A),{value:s.timedBidTime,"onUpdate:value":t=>s.timedBidTime=t,placeholder:"请选择限时竞价时长",size:"large",style:{width:"100%"}},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])]),i("div",ze,[e(o,{label:"拍卖方式",name:["auctionItems",n,"auctionMode"],required:"",class:"basic-three-item"},{default:l(()=>[e(I,{value:s.auctionMode,"onUpdate:value":t=>s.auctionMode=t},{default:l(()=>[e(r,{value:1},{default:l(()=>a[14]||(a[14]=[v("总价")])),_:1,__:[14]}),e(r,{value:2},{default:l(()=>a[15]||(a[15]=[v("单价")])),_:1,__:[15]})]),_:2},1032,["value","onUpdate:value"])]),_:2},1032,["name"]),e(o,{label:"是否展示实际数量",name:["auctionItems",n,"quantityFlag"],required:"",class:"basic-three-item"},{default:l(()=>[e(I,{value:s.quantityFlag,"onUpdate:value":t=>s.quantityFlag=t,size:"large"},{default:l(()=>[e(r,{value:1},{default:l(()=>a[16]||(a[16]=[v("是")])),_:1,__:[16]}),e(r,{value:0},{default:l(()=>a[17]||(a[17]=[v("否")])),_:1,__:[17]})]),_:2},1032,["value","onUpdate:value"])]),_:2},1032,["name"]),e(o,{class:"placeholder-item"})])]),i("div",Te,[a[19]||(a[19]=i("div",{class:"section-subtitle"},"标的介绍",-1)),e(o,{label:"拍卖公告",name:["auctionItems",n,"description"],class:"form-item-full"},{default:l(()=>[e(z(ae),{value:s.description,"onUpdate:value":t=>s.description=t,placeholder:"请输入拍卖公告",height:"200px","auto-focus":!1},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])]),i("div",Be,[a[22]||(a[22]=i("div",{class:"section-subtitle"},"标的照片",-1)),i("div",$e,[e(o,{label:"封面图片",name:["auctionItems",n,"coverImage"],class:"upload-item"},{default:l(()=>[i("div",ke,[e(O,{value:s.coverImage,"onUpdate:value":t=>s.coverImage=t,multiple:!1,"max-count":1,accept:"image/*","list-type":"picture-card","file-type":"image",class:"upload-component upload-normal"},null,8,["value","onUpdate:value"]),a[20]||(a[20]=i("div",{class:"upload-tip"},"只能上传一张",-1))])]),_:2},1032,["name"])]),i("div",Pe,[e(o,{label:"标的照片",name:["auctionItems",n,"itemImages"],class:"upload-item"},{default:l(()=>[i("div",Ve,[e(O,{value:s.itemImages,"onUpdate:value":t=>s.itemImages=t,multiple:!0,"max-count":10,accept:"image/*","list-type":"picture-card","file-type":"image","return-url":!1,class:"upload-component upload-normal"},null,8,["value","onUpdate:value"]),a[21]||(a[21]=i("div",{class:"upload-tip"},"可上传多张图片，最多10张",-1))])]),_:2},1032,["name"])])])]))),128))])]),_:1},8,["model","rules"])])}}}),Mt=R(Ce,[["__scopeId","data-v-5e2bd576"]]);export{Mt as default};
