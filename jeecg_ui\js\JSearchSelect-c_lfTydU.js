var ae=Object.defineProperty;var H=Object.getOwnPropertySymbols;var te=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable;var B=(e,l,i)=>l in e?ae(e,l,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[l]=i,U=(e,l)=>{for(var i in l||(l={}))te.call(l,i)&&B(e,i,l[i]);if(H)for(var i of H(l))ne.call(l,i)&&B(e,i,l[i]);return e};var P=(e,l,i)=>new Promise((u,h)=>{var y=s=>{try{o(i.next(s))}catch(t){h(t)}},f=s=>{try{o(i.throw(s))}catch(t){h(t)}},o=s=>s.done?u(s.value):Promise.resolve(s.value).then(y,f);o((i=i.apply(e,l)).next())});import{H as p,a9 as le,j as A,aF as K,x as oe,aJ as ie,k as ue,a as re}from"./index-CCWaWN5g.js";import{d as se,f as k,e as ce,w as N,u as b,ag as T,aB as F,ar as S,aE as Y,aD as D,aq as E,F as R,aC as q,G,au as j,k as fe,ah as de}from"./vue-vendor-dy9k-Yad.js";import{al as ge}from"./antd-vue-vendor-me9YkNVC.js";const ve=se({name:"JSearchSelect",inheritAttrs:!1,props:{value:p.oneOfType([p.string,p.number]),dict:p.string,dictOptions:{type:Array,default:()=>[]},async:p.bool.def(!1),placeholder:p.string,popContainer:p.string,pageSize:p.number.def(10),getPopupContainer:{type:Function,default:e=>e==null?void 0:e.parentNode},adjustY:p.bool.def(!0),immediateChange:p.bool.def(!1),params:{type:Object,default:()=>{}}},emits:["change","update:value"],setup(e,{emit:l,refs:i}){const u=k([]),h=k(!1),y=le({excludeDefaultKeys:!1}),f=k([]),o=k([]),s=k(0),t=k(!0);let m=!0,O=!1,d=1,V="";const w=ce(()=>e.dict?e.dict.split(",").length>=2:!1);N(()=>e.dict,()=>{e.dict&&(w.value?z():Q())},{immediate:!0}),N(()=>e.value,a=>{a||a===0?M():(f.value=[],o.value=[])},{immediate:!0}),N(()=>e.dictOptions,a=>{a&&a.length>=0&&(u.value=[...a])},{immediate:!0});const $=ge(function(n){return P(this,null,function*(){if(!w.value)return;d=1,m=!0,V=n,s.value+=1;const g=b(s);u.value=[],h.value=!0;let c=I(n);c=c.replaceAll("'",""),A.get({url:`/sys/dict/loadDict/${e.dict}`,params:{keyword:c,pageSize:e.pageSize,pageNo:d}}).then(r=>{if(h.value=!1,r&&r.length>0){if(g!=b(s))return;u.value=r,d++}else d==1&&(m=!1)})})},300);function M(){if(t.value===!1){t.value=!0;return}let{async:a,value:n,dict:g}=e;a?(!o||!o.key||o.key!==n)&&A.get({url:`/sys/dict/loadDictItem/${g}`,params:{key:n}}).then(c=>{if(c&&c.length>0){let r={key:n,label:c};e.value==n&&(o.value=U({},r)),e.immediateChange==!0&&l("change",e.value)}}):(f.value=n.toString(),e.immediateChange==!0&&l("change",n.toString()))}function z(){return P(this,null,function*(){let{dict:a,async:n,dictOptions:g,pageSize:c}=e;if(n){if(a){d=1,m=!0,V="",h.value=!0;let r=I("");A.get({url:`/sys/dict/loadDict/${a}`,params:{pageSize:c,keyword:r,pageNo:d}}).then(v=>{h.value=!1,v&&v.length>0?(u.value=v,d++):d==1&&(m=!1)})}}else if(g&&g.length>0)u.value=g;else{let r="";if(a){let v=a.split(",");if(v[0].indexOf("where")>0){let J=v[0].split("where");r=J[0].trim()+","+v[1]+","+v[2]+","+encodeURIComponent(J[1])}else r=a;const C=yield K(r);u.value=C}}})}function Q(){return P(this,null,function*(){u.value=yield K(e.dict)})}function W(a){f.value=a,L()}function X(a){a?(o.value=a,f.value=a.key):(o.value=null,f.value=null,u.value=null,$("")),L(),a!=null||(t.value=!0)}function L(){t.value=!1,l("change",b(f)),l("update:value",b(f))}function Z(a,n){let g="",c="";try{g=n.value,c=n.children()[0].children}catch(v){}let r=a.toLowerCase();return g.toLowerCase().indexOf(r)>=0||c.toLowerCase().indexOf(r)>=0}function _(a){return e.popContainer?ie(a,e.popContainer):typeof e.getPopupContainer=="function"?e.getPopupContainer(a):a==null?void 0:a.parentNode}function I(a){if(e.params&&e.params.column&&e.params.order){let n=a||"";return n=n+"[orderby:"+e.params.column+","+e.params.order+"]",encodeURI(n)}else return a}const x=()=>{var a,n;(ue(o.value)||(a=o.value)!=null&&a.length)&&w.value&&e.async&&(u.value=[],z()),Array.isArray(o.value)&&o.value.length===0&&w.value&&e.async&&d>2&&(u.value=[],z()),(n=y.onFocus)==null||n.call(y)},ee=a=>P(null,null,function*(){if(w.value){const{target:n}=a,{scrollTop:g,scrollHeight:c,clientHeight:r}=n;if(!O&&m&&g+r>=c-10){O=!0;let v=I(V);A.get({url:`/sys/dict/loadDict/${e.dict}`,params:{pageSize:e.pageSize,keyword:v,pageNo:d}}).then(C=>{if(h.value=!1,(C==null?void 0:C.length)>0){if(JSON.stringify(C[0])===JSON.stringify(u.value[0])){m=!1;return}u.value.push(...C),d++}else m=!1}).finally(()=>{O=!1}).catch(()=>{d!=1&&d--})}}});return{attrs:y,options:u,loading:h,isDictTable:w,selectedValue:f,selectedAsyncValue:o,loadData:oe($,800),getParentContainer:_,filterOption:Z,handleChange:W,handleAsyncChange:X,handleAsyncFocus:x,handlePopupScroll:ee}}});function he(e,l,i,u,h,y){const f=T("a-spin"),o=T("a-select-option"),s=T("a-select");return e.async?(S(),F(s,Y({key:0},e.attrs,{value:e.selectedAsyncValue,"onUpdate:value":l[0]||(l[0]=t=>e.selectedAsyncValue=t),showSearch:"",labelInValue:"",allowClear:"",getPopupContainer:e.getParentContainer,placeholder:e.placeholder,filterOption:e.isDictTable?!1:e.filterOption,notFoundContent:e.loading?void 0:null,onFocus:e.handleAsyncFocus,onSearch:e.loadData,onChange:e.handleAsyncChange,onPopupScroll:e.handlePopupScroll}),{notFoundContent:D(()=>[fe(f,{size:"small"})]),default:D(()=>[(S(!0),E(R,null,q(e.options,t=>(S(),F(o,{key:t==null?void 0:t.value,value:t==null?void 0:t.value},{default:D(()=>[G(j(t==null?void 0:t.text),1)]),_:2},1032,["value"]))),128))]),_:1},16,["value","getPopupContainer","placeholder","filterOption","notFoundContent","onFocus","onSearch","onChange","onPopupScroll"])):(S(),F(s,Y({key:1,value:e.selectedValue,"onUpdate:value":l[1]||(l[1]=t=>e.selectedValue=t)},e.attrs,{showSearch:"",getPopupContainer:e.getParentContainer,placeholder:e.placeholder,filterOption:e.filterOption,notFoundContent:e.loading?void 0:null,dropdownAlign:{overflow:{adjustY:e.adjustY}},onChange:e.handleChange}),{notFoundContent:D(()=>[e.loading?(S(),F(f,{key:0,size:"small"})):de("",!0)]),default:D(()=>[(S(!0),E(R,null,q(e.options,t=>(S(),F(o,{key:t==null?void 0:t.value,value:t==null?void 0:t.value},{default:D(()=>[G(j(t==null?void 0:t.text),1)]),_:2},1032,["value"]))),128))]),_:1},16,["value","getPopupContainer","placeholder","filterOption","notFoundContent","dropdownAlign","onChange"]))}const Se=re(ve,[["render",he]]);export{Se as J};
