import{d as i,ag as t,aB as p,ar as m,aE as c,aD as o,G as a,k as d}from"./vue-vendor-dy9k-Yad.js";import{B as f,u as l}from"./index-JbqXEynz.js";import{a as u}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";const w=i({components:{BasicDrawer:f},setup(){const[e,{closeDrawer:r}]=l();return{register:e,closeDrawer:r}}});function _(e,r,D,B,g,k){const s=t("a-button"),n=t("BasicDrawer");return m(),p(n,c(e.$attrs,{onRegister:e.register,title:"Drawer Title",width:"50%"}),{default:o(()=>[r[1]||(r[1]=a(" Drawer Info. ")),d(s,{type:"primary",onClick:e.closeDrawer},{default:o(()=>r[0]||(r[0]=[a(" 内部关闭drawer ")])),_:1,__:[0]},8,["onClick"])]),_:1,__:[1]},16,["onRegister"])}const E=u(w,[["render",_]]);export{E as default};
