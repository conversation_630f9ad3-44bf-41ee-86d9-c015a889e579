const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/BasicTable-xCEZpGLb.js","js/vue-vendor-dy9k-Yad.js","js/antd-vue-vendor-me9YkNVC.js","js/index-L3cSIXth.js","js/BasicForm-DBcXiHk0.js","js/componentMap-Bkie1n3v.js","js/index-CCWaWN5g.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/useFormItem-CHvpjy4o.js","js/index-Diw57m_E.js","js/BasicModal-BLFvpBuk.js","js/ModalHeader-BJG9dHtK.js","js/useTimeout-CeTdFD_D.js","js/index-CImCetrx.js","assets/index-BObJM2Lc.css","assets/ModalHeader-HwQKX-UU.css","js/useWindowSizeFn-DDbrQbks.js","js/index-LCGLvkB3.js","js/index-De_W6s5g.js","js/index-D6l0IxOU.js","js/useIntersectionObserver-C4LVxQJW.js","assets/index-zj-Vfn3Q.css","assets/BasicModal-ByeTDAzn.css","js/CustomModal-BakuIxQv.js","assets/CustomModal-DWxHZmza.css","assets/index-yRxe3SQ1.css","js/download-CZ-9H9a3.js","js/base64Conver-24EVOS6V.js","js/index-CBCjSSNZ.js","assets/index-NmxXH94f.css","js/index-DFrpKMGa.js","js/useCountdown-CCWNeb_r.js","js/useFormItemSingle-Cw668yj5.js","assets/index-BB9COjV3.css","js/JAddInput-CxJ-JBK-.js","js/props-CCT78mKr.js","js/JSelectBiz-jOYRdMJf.js","assets/JSelectBiz-CYw1rOZ6.css","js/index-QxsVJqiT.js","js/index-BtIdS_Qz.js","js/bem-sRx7x0Ii.js","js/props-qAqCef5R.js","js/useContextMenu-BU2ycxls.js","assets/useContextMenu-DRJLeHo9.css","assets/index-D8VMPii6.css","js/depart.api-BoGnt_ZX.js","assets/JAddInput-i6a6KIoQ.css","js/JSelectDept-I-NqkbOH.js","assets/JSelectDept-WHP406xL.css","js/JAreaSelect-Db7Nhhc_.js","js/areaDataUtil-BXVjRArW.js","assets/JAreaSelect-Pwl_5U28.css","js/JEditorTiptap-BwAoWsi9.js","js/index-ByPySmGo.js","assets/index-BrdQT4ew.css","js/JPopup-CeU6ry6r.js","assets/JPopup-Dn0_YeSX.css","js/JEllipsis-BsXuWNHJ.js","js/JUpload-CRos0F1P.js","assets/JUpload-CsrjJkIs.css","js/JSearchSelect-c_lfTydU.js","js/index-CXHeQyuE.js","js/index-Dyko68ZT.js","assets/index-CTbO_Zqi.css","assets/componentMap-Degzw4_e.css","assets/BasicForm-DTEnYz8c.css","js/useForm-CgkFTrrO.js","js/JAreaLinkage-DFCdF3cr.js","js/JCodeEditor-B-WXz11X.js","js/htmlmixed-CmvhkW5V.js","js/vue-CAKGUkuE.js","assets/vue-DyVx2_Fd.css","assets/JCodeEditor-DaPRKM4Q.css","assets/idea-C3eFBO7g.css","js/EasyCronInput-BuvtO5dv.js","assets/EasyCronInput-BLbXuoBB.css","js/JUploadModal-C-iKhVFc.js","js/injectionKey-DPVn4AgL.js","assets/BasicTable-DcVosJye.css"])))=>i.map(i=>d[i]);
var ee=Object.defineProperty,te=Object.defineProperties;var ae=Object.getOwnPropertyDescriptors;var D=Object.getOwnPropertySymbols;var le=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable;var J=(e,t,o)=>t in e?ee(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,T=(e,t)=>{for(var o in t||(t={}))le.call(t,o)&&J(e,o,t[o]);if(D)for(var o of D(t))ne.call(t,o)&&J(e,o,t[o]);return e},A=(e,t)=>te(e,ae(t));var V=(e,t,o)=>new Promise((d,n)=>{var f=l=>{try{a(o.next(l))}catch(h){n(h)}},c=l=>{try{a(o.throw(l))}catch(h){n(h)}},a=l=>l.done?d(l.value):Promise.resolve(l.value).then(f,c);a((o=o.apply(e,t)).next())});import{c as $,f as u,r as N,w as F,u as C,d as W,ag as y,aq as q,ar as Q,k as w,aE as E,aD as I,at as se,p as j,h as oe}from"./vue-vendor-dy9k-Yad.js";import{u as ie,i as ce,aj as re,ac as ue,a9 as X,av as G,_ as de,a as Y,H as k,ad as fe}from"./index-CCWaWN5g.js";import{B as he}from"./index-Diw57m_E.js";import{s as me}from"./props-CCT78mKr.js";import{J as ge}from"./JSelectBiz-jOYRdMJf.js";import{h as z}from"./antd-vue-vendor-me9YkNVC.js";function ve(e,t,o){const d=$("selectOptions",u([])),n=$("selectValues",N({value:[],change:!1})),f=$("loadingEcho",u(!1)),c=u([]),a=u([]),l=u([]),h=ie();let m=!0;F(n,()=>{var s;if(n.change==!1&&!ce(n.value)){let r={isMultiTranslate:"true",pageSize:(s=n.value)==null?void 0:s.length};r[t.rowKey]=n.value.join(","),f.value=m,m=!1,x(r,!0).then().finally(()=>{f.value=m})}a.value=[...n.value]},{immediate:!0});function v(s,r){return V(this,null,function*(){if(a.value=s,t.showSelected&&C(a).length!==C(r).length){let{records:p}=yield e({code:C(a).join(","),pageSize:C(a).length});l.value=p}else l.value=r})}const R={type:t.isRadioSelection?"radio":"checkbox",columnWidth:20,selectedRowKeys:a,onChange:v,preserveSelectedRowKeys:!0},_={dataIndex:"index",width:50};function x(s,r){return V(this,null,function*(){let{records:p}=yield e(s);if(c.value=p,r){let S=[];p.forEach(M=>{S.push({label:M[t.labelKey],value:M[t.rowKey]})}),d.value=S}})}function O(){return V(this,null,function*(){let{records:s}=yield e({code:n.value.join(","),pageSize:n.value.length});a.value=[...n.value],l.value=s})}function K(s){return V(this,null,function*(){s?(a.value=[...n.value],t.showSelected&&O()):o==null||o("close")})}function B(s){let r=[],p=[];if(l.value.forEach(S=>{r.push({label:S[t.labelKey],value:S[t.rowKey]})}),a.value.forEach(S=>{p.push(S)}),d.value=r,t.maxSelectCount&&p.length>t.maxSelectCount)return h.createMessage.warning(`最多只能选择${t.maxSelectCount}条数据`),!1;s&&s(r,p)}function b(s){a.value=a.value.filter(r=>r!=s[t.rowKey]),l.value=l.value.filter(r=>r[t.rowKey]!==s[t.rowKey])}function i(){a.value=[],l.value=[]}return[{onSelectChange:v,getDataSource:x,visibleChange:K,selectOptions:d,selectValues:n,rowSelection:R,indexColumnProps:_,checkedKeys:a,selectRows:l,dataSource:c,getSelectResult:B,handleDeleteSelected:b,reset:i}]}const pe=W({name:"UserSelectModal",components:{BasicModal:he,BasicTable:re(()=>de(()=>import("./BasicTable-xCEZpGLb.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79])),{loading:!0})},props:A(T({},me),{modalTitle:{type:String,default:"选择用户"},excludeUserIdList:{type:Array,default:[]}}),emits:["register","getSelectResult","close"],setup(e,{emit:t,refs:o}){const d=u({x:!1}),n=u(),f=u(600),[c,{closeModal:a}]=ue(()=>{window.innerWidth<900?d.value={x:900}:d.value={x:!1},setTimeout(()=>{n.value&&n.value.setSelectedRowKeys(_.value||[])},800)}),l=X(),h={canResize:!1,bordered:!0,size:"small"},m=Object.assign({},C(e),C(l),h),[{rowSelection:v,visibleChange:R,selectValues:_,indexColumnProps:x,getSelectResult:O,handleDeleteSelected:K,selectRows:B}]=ve(G,m,t),b=u(e.params);F(v.selectedRowKeys,g=>{n.value&&n.value.setSelectedRowKeys(g)});const i={baseColProps:{xs:24,sm:8,md:6,lg:8,xl:6,xxl:6},actionColOptions:{xs:24,sm:8,md:8,lg:8,xl:8,xxl:8},schemas:[{label:"账号",field:"username",component:"JInput"},{label:"姓名",field:"realname",component:"JInput"}]},s=[{title:"用户账号",dataIndex:"username",width:120,align:"left"},{title:"用户姓名",dataIndex:"realname",width:120},{title:"性别",dataIndex:"sex_dictText",width:50},{title:"手机号码",dataIndex:"phone",width:120},{title:"邮箱",dataIndex:"email"},{title:"状态",dataIndex:"status_dictText",width:80}],r={pagination:!1,showIndexColumn:!1,scroll:{y:390},size:"small",canResize:!1,bordered:!0,rowKey:"id",columns:[{title:"用户姓名",dataIndex:"realname",width:40},{title:"操作",dataIndex:"action",align:"center",width:40,slots:{customRender:"action"}}]};function p(){O((g,U)=>{t("getSelectResult",g,U),a()})}function S(g){let U=e.excludeUserIdList;if(!U)return g;let H=[];if(U.length>0&&g&&g.length>0){for(let P of g)U.indexOf(P.id)<0&&H.push(T({},P));return H}return g}const M=()=>{t("close")},L=document.documentElement.clientHeight*200;f.value=L>600?600:L;function Z(g){return Object.assign({column:"createTime",order:"desc"},g)}return{handleOk:p,searchInfo:b,register:c,indexColumnProps:x,visibleChange:R,getBindValue:m,getUserList:G,formConfig:i,columns:s,rowSelection:v,selectRows:B,selectedTable:r,handleDeleteSelected:K,tableScroll:d,tableRef:n,afterFetch:S,handleCancel:M,maxHeight:f,beforeFetch:Z}}}),we=["onClick"];function Se(e,t,o,d,n,f){const c=y("BasicTable"),a=y("a-col"),l=y("Icon"),h=y("a-row"),m=y("BasicModal");return Q(),q("div",null,[w(m,E(e.$attrs,{onRegister:e.register,title:e.modalTitle,width:e.showSelected?"1200px":"900px",wrapClassName:"j-user-select-modal",onOk:e.handleOk,onCancel:e.handleCancel,maxHeight:e.maxHeight,centered:!0,destroyOnClose:"",onVisibleChange:e.visibleChange}),{default:I(()=>[w(h,null,{default:I(()=>[w(a,{span:e.showSelected?18:24},{default:I(()=>[w(c,E({ref:"tableRef",columns:e.columns,scroll:e.tableScroll},e.getBindValue,{useSearchForm:!0,formConfig:e.formConfig,api:e.getUserList,searchInfo:e.searchInfo,rowSelection:e.rowSelection,indexColumnProps:e.indexColumnProps,afterFetch:e.afterFetch,beforeFetch:e.beforeFetch}),{tableTitle:I(()=>t[0]||(t[0]=[])),_:1},16,["columns","scroll","formConfig","api","searchInfo","rowSelection","indexColumnProps","afterFetch","beforeFetch"])]),_:1},8,["span"]),w(a,{span:e.showSelected?6:0},{default:I(()=>[w(c,E(e.selectedTable,{dataSource:e.selectRows,useSearchForm:!0,formConfig:{showActionButtonGroup:!1,baseRowStyle:{minHeight:"40px"}}}),{action:I(({record:v})=>[se("a",{href:"javascript:void(0)",onClick:R=>e.handleDeleteSelected(v)},[w(l,{icon:"ant-design:delete-outlined"})],8,we)]),_:1},16,["dataSource"])]),_:1},8,["span"])]),_:1})]),_:1},16,["onRegister","title","width","onOk","onCancel","maxHeight","onVisibleChange"])])}const ye=Y(pe,[["render",Se]]),Ce=W({name:"JSelectUser",components:{UserSelectModal:ye,JSelectBiz:ge},inheritAttrs:!1,props:{value:k.oneOfType([k.string,k.array]),labelKey:{type:String,default:"realname"},rowKey:{type:String,default:"username"},params:{type:Object,default:()=>{}},excludeUserIdList:{type:Array,default:()=>[]}},emits:["options-change","change","update:value"],setup(e,{emit:t}){const o=u(),[d,{openModal:n}]=fe(),f=u([]);let c=N({value:[],change:!1}),a=[];const l=u(!1);j("selectOptions",f),j("selectValues",c),j("loadingEcho",l);const h=u(!1),m=X();oe(()=>{a=[],e.value&&_(),e.value||(c.value=[])});const v=u([]);F(()=>e.excludeUserIdList,i=>{v.value=i},{immediate:!0});function R(){h.value=!0,n(!0,{isUpdate:!1})}function _(){let i=e.value?e.value:[];i&&typeof i=="string"&&i!="null"&&i!="undefined"?(c.value=i.split(","),a=i.split(",")):(c.value=i,a=z(i))}function x(i,s){f.value=i,c.value=s,b(s)}const O=Object.assign({},C(e),C(m)),K=()=>{a.length?c.value=z(a):b(a)},B=i=>{a=z(i),b(a)},b=i=>{let s=typeof e.value=="string"?i.join(","):i;t("update:value",s),t("change",s)};return{attrs:m,selectOptions:f,getBindValue:O,selectValues:c,loadingEcho:l,tag:h,regModal:d,setValue:x,handleOpen:R,excludeUserIdList:v,handleClose:K,handleSelectChange:B}}}),be={class:"JselectUser"};function Ie(e,t,o,d,n,f){const c=y("JSelectBiz"),a=y("UserSelectModal"),l=y("a-form-item");return Q(),q("div",be,[w(c,E({onChange:e.handleSelectChange,onHandleOpen:e.handleOpen,loading:e.loadingEcho},e.attrs),null,16,["onChange","onHandleOpen","loading"]),w(l,null,{default:I(()=>[w(a,E({rowKey:e.rowKey,onRegister:e.regModal,onGetSelectResult:e.setValue},e.getBindValue,{excludeUserIdList:e.excludeUserIdList,onClose:e.handleClose}),null,16,["rowKey","onRegister","onGetSelectResult","excludeUserIdList","onClose"])]),_:1})])}const Ve=Y(Ce,[["render",Ie],["__scopeId","data-v-13190c7d"]]);export{ye as U,Ve as l,ve as u};
