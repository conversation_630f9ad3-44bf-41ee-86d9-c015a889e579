import{d as h,f as p,n as v,u as T,ag as i,aB as K,ar as R,aD as n,k as e,G as g}from"./vue-vendor-dy9k-Yad.js";import{_ as $}from"./index-BtIdS_Qz.js";import{t as _}from"./data-B6s_JPbS.js";import{P as B}from"./index-CtJ0w2CP.js";import{n as L,a7 as E,a6 as b,J as w,h as C,bA as A}from"./antd-vue-vendor-me9YkNVC.js";import{R as P,a as x}from"./index-CCWaWN5g.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./vxe-table-vendor-B22HppNm.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const S=h({name:"system-testtree",components:{BasicTree:$,PageWrapper:B,Card:w,Row:b,Col:E,Spin:L},setup(){const t=p(null),l=p(null),D=p(null),c=p([]),s=p(!1);function y(a,u){}function o(){s.value=!0,setTimeout(()=>{c.value=C(_),s.value=!1,v(()=>{var a;(a=T(l))==null||a.expandAll(!0)})},2e3)}function r(){s.value=!0,setTimeout(()=>{c.value=C(_),s.value=!1},2e3)}const f=p([{title:"parent ",key:"0-0"}]);function m(a){return new Promise(u=>{if(P(a.children)&&a.children.length>0){u();return}setTimeout(()=>{const d=T(t);if(d){const k=[{title:`Child Node ${a.eventKey}-0`,key:`${a.eventKey}-0`},{title:`Child Node ${a.eventKey}-1`,key:`${a.eventKey}-1`}];d.updateNodeByKey(a.eventKey,{children:k}),d.setExpandedKeys(A([a.eventKey,...d.getExpandedKeys()]))}u()},300)})}return{treeData:_,handleCheck:y,tree:f,onLoadData:m,asyncTreeRef:t,asyncExpandTreeRef:l,loadTreeRef:D,tree2:c,loadTreeData:o,treeLoading:s,loadTreeData2:r}}});function W(t,l,D,c,s,y){const o=i("BasicTree"),r=i("Col"),f=i("a-button"),m=i("Spin"),a=i("Card"),u=i("Row"),d=i("PageWrapper");return R(),K(d,{title:"Tree基础示例"},{default:n(()=>[e(u,{gutter:[16,16]},{default:n(()=>[e(r,{span:8},{default:n(()=>[e(o,{title:"基础示例，默认展开第一层",treeData:t.treeData,defaultExpandLevel:"1"},null,8,["treeData"])]),_:1}),e(r,{span:8},{default:n(()=>[e(o,{title:"可勾选，默认全部展开",treeData:t.treeData,checkable:!0,defaultExpandAll:"",onCheck:t.handleCheck},null,8,["treeData","onCheck"])]),_:1}),e(r,{span:8},{default:n(()=>[e(o,{title:"指定默认展开/勾选示例",treeData:t.treeData,checkable:!0,expandedKeys:["0-0"],checkedKeys:["0-0"]},null,8,["treeData"])]),_:1}),e(r,{span:8},{default:n(()=>[e(o,{title:"懒加载异步树",ref:"asyncTreeRef",treeData:t.tree,"load-data":t.onLoadData},null,8,["treeData","load-data"])]),_:1}),e(r,{span:8},{default:n(()=>[e(a,{title:"异步数据，默认展开"},{extra:n(()=>[e(f,{onClick:t.loadTreeData,loading:t.treeLoading},{default:n(()=>l[0]||(l[0]=[g("加载数据")])),_:1,__:[0]},8,["onClick","loading"])]),default:n(()=>[e(m,{spinning:t.treeLoading},{default:n(()=>[e(o,{ref:"asyncExpandTreeRef",treeData:t.tree2},null,8,["treeData"])]),_:1},8,["spinning"])]),_:1})]),_:1}),e(r,{span:8},{default:n(()=>[e(a,{title:"BasicTree内置加载"},{extra:n(()=>[e(f,{onClick:t.loadTreeData2,loading:t.treeLoading},{default:n(()=>l[1]||(l[1]=[g("请求数据")])),_:1,__:[1]},8,["onClick","loading"])]),default:n(()=>[e(o,{ref:"loadTreeRef",treeData:t.tree2,loading:t.treeLoading},null,8,["treeData","loading"])]),_:1})]),_:1})]),_:1})]),_:1})}const oe=x(S,[["render",W]]);export{oe as default};
