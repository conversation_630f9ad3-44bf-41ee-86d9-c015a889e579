import{d as c,ag as s,aq as u,ar as k,k as a,aD as n,G as d}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as w}from"./useListPage-Soxgnx9a.js";import f from"./BasicTable-xCEZpGLb.js";import{a as g}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const b=[{title:"ID",dataIndex:"id",fixed:"left",width:200},{title:"姓名",dataIndex:"name",width:150,filters:[{text:"Male",value:"male"},{text:"Female",value:"female"}]},{title:"地址",dataIndex:"address",width:300},{title:"编号",dataIndex:"no",width:150,sorter:!0,defaultHidden:!0},{title:"开始时间",width:150,sorter:!0,dataIndex:"beginTime"},{title:"结束时间",width:150,sorter:!0,dataIndex:"endTime"}],T=c({components:{BasicTable:f},setup(){const{tableContext:o}=w({tableProps:{title:"树形表格",isTreeTable:!0,rowSelection:{type:"checkbox",getCheckboxProps(i){return i.id==="0"?{disabled:!0}:{disabled:!1}}},columns:b,dataSource:l(),rowKey:"id",useSearchForm:!1}}),[t,{expandAll:m,collapseAll:p}]=o;function l(){return(()=>{const r=[];for(let e=0;e<40;e++)r.push({id:`${e}`,name:"John Brown",age:`1${e}`,no:`${e+10}`,address:"New York No. 1 Lake ParkNew York No. 1 Lake Park",beginTime:new Date().toLocaleString(),endTime:new Date().toLocaleString(),children:[{id:`l2-${e}`,name:"John Brown",age:`1${e}`,no:`${e+10}`,address:"New York No. 1 Lake ParkNew York No. 1 Lake Park",beginTime:new Date().toLocaleString(),endTime:new Date().toLocaleString()},{id:`l3-${e}`,name:"John Mary",age:`1${e}`,no:`${e+10}`,address:"New York No. 1 Lake ParkNew York No. 1 Lake Park",beginTime:new Date().toLocaleString(),endTime:new Date().toLocaleString()}]});return r})()}return{register:t,expandAll:m,collapseAll:p}}}),x={class:"p-4"};function _(o,t,m,p,l,i){const r=s("a-button"),e=s("BasicTable");return k(),u("div",x,[a(e,{onRegister:o.register},{toolbar:n(()=>[a(r,{type:"primary",onClick:o.expandAll},{default:n(()=>t[0]||(t[0]=[d("展开全部")])),_:1,__:[0]},8,["onClick"]),a(r,{type:"primary",onClick:o.collapseAll},{default:n(()=>t[1]||(t[1]=[d("折叠全部")])),_:1,__:[1]},8,["onClick"])]),_:1},8,["onRegister"])])}const De=g(T,[["render",_]]);export{De as default};
