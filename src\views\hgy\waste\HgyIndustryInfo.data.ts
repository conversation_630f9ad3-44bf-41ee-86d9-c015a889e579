import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '物资种类',
    align: 'center',
    dataIndex: 'materialType_dictText',
  },
  {
    title: '品类/细类',
    align: 'center',
    dataIndex: 'category',
  },
  {
    title: '省份',
    align: 'center',
    dataIndex: 'province',
  },
  {
    title: '城市',
    align: 'center',
    dataIndex: 'city',
  },
  {
    title: '区县',
    align: 'center',
    dataIndex: 'district',
  },
  {
    title: '高价',
    align: 'center',
    dataIndex: 'highPrice',
  },
  {
    title: '低价',
    align: 'center',
    dataIndex: 'lowPrice',
  },
  {
    title: '均价',
    align: 'center',
    dataIndex: 'avgPrice',
  },
  {
    title: '涨跌额',
    align: 'center',
    dataIndex: 'changeAmount',
  },
  {
    title: '涨跌幅',
    align: 'center',
    dataIndex: 'changeRate',
  },
  {
    title: '资讯日期',
    align: 'center',
    dataIndex: 'infoDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '创建时间',
    align: 'center',
    dataIndex: 'createTime',
  },
  {
    title: '更新时间',
    align: 'center',
    dataIndex: 'updateTime',
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy',
  },
  {
    title: '更新人',
    align: 'center',
    dataIndex: 'updateBy',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '物资种类',
    labelWidth: 64,
    field: 'materialType',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'wasteMaterialsDict',
    },
    colProps: { span: 6 },
  },
  {
    label: '城市',
    labelWidth: 64,
    field: 'city',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '时间',
    labelWidth: 64,
    field: 'infoDate',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
    },
    colProps: { span: 8 },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '物资种类',
    field: 'materialType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'wasteMaterialsDict',
    },
    dynamicRules: () => {
      return [{ required: true, message: '请输入物资种类!' }];
    },
  },
  {
    label: '品类/细类',
    field: 'category',
    component: 'Input',
  },
  {
    label: '省份',
    field: 'province',
    component: 'Input',
  },
  {
    label: '城市',
    field: 'city',
    component: 'Input',
  },
  {
    label: '区县',
    field: 'district',
    component: 'Input',
  },
  {
    label: '高价',
    field: 'highPrice',
    component: 'InputNumber',
    dynamicRules: () => {
      return [{ required: true, message: '请输入高价!' }];
    },
  },
  {
    label: '低价',
    field: 'lowPrice',
    component: 'InputNumber',
    dynamicRules: () => {
      return [{ required: true, message: '请输入低价!' }];
    },
  },
  {
    label: '均价',
    field: 'avgPrice',
    component: 'InputNumber',
    dynamicRules: () => {
      return [{ required: true, message: '请输入均价!' }];
    },
  },
  {
    label: '涨跌额',
    field: 'changeAmount',
    component: 'InputNumber',
  },
  {
    label: '涨跌幅',
    field: 'changeRate',
    component: 'InputNumber',
  },
  {
    label: '资讯日期',
    field: 'infoDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    dynamicRules: () => {
      return [{ required: true, message: '请输入资讯日期!' }];
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  materialType: {
    title: '物资种类',
    order: 0,
    view: 'number',
    type: 'number',
    dictCode: 'wasteMaterialsDict',
  },
  category: { title: '品类/细类', order: 1, view: 'text', type: 'string' },
  province: { title: '省份', order: 2, view: 'link_down', type: 'string' },
  city: { title: '城市', order: 3, view: 'text', type: 'string' },
  district: { title: '区县', order: 4, view: 'text', type: 'string' },
  highPrice: { title: '高价', order: 5, view: 'number', type: 'number' },
  lowPrice: { title: '低价', order: 6, view: 'number', type: 'number' },
  avgPrice: { title: '均价', order: 7, view: 'number', type: 'number' },
  changeAmount: { title: '涨跌额', order: 8, view: 'number', type: 'number' },
  changeRate: { title: '涨跌幅', order: 9, view: 'number', type: 'number' },
  infoDate: { title: '资讯日期', order: 10, view: 'date', type: 'string' },
  createTime: { title: '创建时间', order: 11, view: 'datetime', type: 'string' },
  updateTime: { title: '更新时间', order: 12, view: 'datetime', type: 'string' },
  createBy: { title: '创建人', order: 13, view: 'text', type: 'string' },
  updateBy: { title: '更新人', order: 14, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
