var u=(w,p,s)=>new Promise((n,o)=>{var f=e=>{try{i(s.next(e))}catch(t){o(t)}},a=e=>{try{i(s.throw(e))}catch(t){o(t)}},i=e=>e.done?n(e.value):Promise.resolve(e.value).then(f,a);i((s=s.apply(w,p)).next())});import{d as N,f as d,aB as v,ar as k,aD as x,k as C,u as m,aE as F}from"./vue-vendor-dy9k-Yad.js";import{rules as I}from"./validator-B_KkcUnu.js";import{bV as L,N as S,ac as E,j as V,u as j}from"./index-CCWaWN5g.js";import{B as q}from"./index-Diw57m_E.js";import{B as O}from"./BasicForm-DBcXiHk0.js";import{u as $}from"./useForm-CgkFTrrO.js";import"./user.api-mLAlJze4.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const Ve=N({__name:"UpdatePassword",emits:["register"],setup(w,{expose:p,emit:s}){const n=L(),{t:o}=S(),f=s,a=j(),i=d(),e=d(""),t=d(o("layout.changePassword.changePassword")),[P,{resetFields:h,validate:y,clearValidate:M}]=$({schemas:[{label:o("layout.changePassword.oldPassword"),field:"oldpassword",component:"InputPassword",required:!0},{label:o("layout.changePassword.newPassword"),field:"password",component:"StrengthMeter",componentProps:{placeholder:o("layout.changePassword.pleaseEnterNewPassword")},rules:[{required:!0,message:o("layout.changePassword.pleaseEnterNewPassword")}]},{label:o("layout.changePassword.confirmNewPassword"),field:"confirmpassword",component:"InputPassword",dynamicRules:({values:r})=>I.confirmPassword(r,!0)}],showActionButtonGroup:!1,wrapperCol:null,labelWidth:n.getLocale=="zh_CN"?100:160}),[_,{setModalProps:l,closeModal:b}]=E();function B(){return u(this,null,function*(){try{const r=yield y();l({confirmLoading:!0});let g=Object.assign({username:m(e)},r);V.put({url:"/sys/user/updatePassword",params:g},{isTransformResponse:!1}).then(c=>{c.success?(a.createMessage.success(c.message),b()):a.createMessage.warning(c.message)})}finally{l({confirmLoading:!1})}})}function R(r){return u(this,null,function*(){if(r)e.value=r,yield l({visible:!0}),yield h(),yield M();else{a.createMessage.warning("当前系统无登录用户!");return}})}return p({title:t,show:R}),(r,g)=>(k(),v(m(q),F(r.$attrs,{onRegister:m(_),title:t.value,onOk:B,width:"600px"}),{default:x(()=>[C(O,{onRegister:m(P)},null,8,["onRegister"])]),_:1},16,["onRegister","title"]))}});export{Ve as default};
