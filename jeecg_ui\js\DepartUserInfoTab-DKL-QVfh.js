var Z=Object.defineProperty,ee=Object.defineProperties;var te=Object.getOwnPropertyDescriptors;var x=Object.getOwnPropertySymbols;var oe=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable;var v=(i,r,t)=>r in i?Z(i,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[r]=t,S=(i,r)=>{for(var t in r||(r={}))oe.call(r,t)&&v(i,t,r[t]);if(x)for(var t of x(r))re.call(r,t)&&v(i,t,r[t]);return i},R=(i,r)=>ee(i,te(r));var b=(i,r,t)=>new Promise((f,n)=>{var w=s=>{try{u(t.next(s))}catch(m){n(m)}},g=s=>{try{u(t.throw(s))}catch(m){n(m)}},u=s=>s.done?f(s.value):Promise.resolve(s.value).then(w,g);u((t=t.apply(i,r)).next())});import{d as ne,c as ie,f as ae,e as se,w as le,ag as d,aq as pe,ar as h,k as a,u as l,aD as p,aB as me,ah as ue,G as I,at as B,F as ce}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{a as A}from"./index-JbqXEynz.js";import{useListPage as de}from"./useListPage-Soxgnx9a.js";import fe from"./UserDrawer-xnPiWN1B.js";import{U as we}from"./JSelectUser-COkExGbu.js";import _e from"./DepartRoleUserAuthDrawer-BtPM-ExV.js";import{m as be,u as ge,n as ye}from"./depart.user.api-D_abnxSU.js";import{c as De,e as ke}from"./depart.user.data-BFBNnnrj.js";import{ad as Ce}from"./index-CCWaWN5g.js";import Ue from"./BasicTable-xCEZpGLb.js";import{Q as xe}from"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectDept-I-NqkbOH.js";import"./props-CCT78mKr.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JSelectBiz-jOYRdMJf.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./user.data-CLRTqTDz.js";import"./user.api-mLAlJze4.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";import"./useAdaptiveWidth-SDQVNQ1K.js";const Gt=ne({__name:"DepartUserInfoTab",props:{data:{require:!0,type:Object}},setup(i){const r=ie("prefixCls"),t=i,f=ae(null),n=se(()=>{var e;return(e=t.data)==null?void 0:e.id}),w={xs:24,sm:24,md:24,lg:12,xl:12,xxl:8},{tableContext:g,createMessage:u}=de({tableProps:{api:be,columns:ke,canResize:!1,formConfig:{schemas:De,baseColProps:w,labelAlign:"left",labelCol:{xs:24,sm:24,md:24,lg:9,xl:7,xxl:5},wrapperCol:{},actionColOptions:R(S({},w),{style:{textAlign:"left"}}),showResetButton:!!n.value,showSubmitButton:!!n.value},tableSetting:{cacheKey:"depart_user_userInfo"},beforeFetch(e){e.depId=n.value},immediate:!!n.value}}),[s,{reload:m,setProps:ve,setLoading:_,updateTableDataRecord:M},{rowSelection:P,selectedRowKeys:y}]=g;le(()=>t.data,()=>m());const[T,{openDrawer:D,setDrawerProps:Se}]=A(),[F,K]=A(),[j,N]=Ce();function O(){y.value=[]}function V(e){K.openDrawer(!0,{userId:e.id,departId:n})}function q(){var e,o;n.value?D(!0,{isUpdate:!1,departDisabled:!0,nextDepartOptions:{value:(e=t.data)==null?void 0:e.key,label:(o=t.data)==null?void 0:o.title},record:{activitiSync:1,userIdentity:1,selecteddeparts:n.value}}):u.warning("请先选择一个部门")}function G(e){D(!0,{record:e,isUpdate:!0,departDisabled:!0,showFooter:!1})}function L(e){D(!0,{isUpdate:!0,record:e,departDisabled:!0})}function $(){f.value.rowSelection.selectedRowKeys=[],N.openModal()}function k(e,o){return b(this,null,function*(){if(!n.value)u.warning("请先选择一个部门");else{_(!0);let c=l(e).join(",");try{return yield ge({depId:n.value,userIds:c},o),m()}finally{_(!1)}}return Promise.reject()})}function z(){return b(this,null,function*(){try{yield k(y,!0),O()}catch(e){}})}function E(e,o){return b(this,null,function*(){if(o.length>0)try{_(!0),yield ye(n.value,o),m()}finally{_(!1)}})}function Q({isUpdate:e,values:o}){e?M(o.id,o):m()}function H(e){return[{label:"编辑",onClick:L.bind(null,e)}]}function J(e){return[{label:"部门角色",onClick:V.bind(null,e)},{label:"用户详情",onClick:G.bind(null,e)},{label:"取消关联",color:"error",popConfirm:{title:"确认取消关联吗？",confirm:k.bind(null,[e.id],!1)}}]}return(e,o)=>{const c=d("a-button"),C=d("icon"),W=d("a-menu-item"),X=d("a-menu"),Y=d("a-dropdown");return h(),pe(ce,null,[a(l(Ue),{onRegister:l(s),rowSelection:l(P)},{tableTitle:p(()=>[a(c,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:$,disabled:!n.value},{default:p(()=>o[0]||(o[0]=[I("添加已有用户")])),_:1,__:[0]},8,["disabled"]),a(c,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:q,disabled:!n.value},{default:p(()=>o[1]||(o[1]=[I("新建用户")])),_:1,__:[1]},8,["disabled"]),l(y).length>0?(h(),me(Y,{key:0},{overlay:p(()=>[a(X,null,{default:p(()=>[a(W,{key:"1",onClick:z},{default:p(()=>[a(C,{icon:"bx:bx-unlink"}),o[2]||(o[2]=B("span",null,"取消关联",-1))]),_:1,__:[2]})]),_:1})]),default:p(()=>[a(c,null,{default:p(()=>[o[3]||(o[3]=B("span",null,"批量操作 ",-1)),a(C,{icon:"akar-icons:chevron-down"})]),_:1,__:[3]})]),_:1})):ue("",!0)]),action:p(({record:U})=>[a(l(xe),{actions:H(U),dropDownActions:J(U)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),a(fe,{onRegister:l(T),onSuccess:Q},null,8,["onRegister"]),a(_e,{onRegister:l(F)},null,8,["onRegister"]),a(we,{ref_key:"userSelectModalRef",ref:f,rowKey:"id",onRegister:l(j),onGetSelectResult:E},null,8,["onRegister"])],64)}}});export{Gt as default};
