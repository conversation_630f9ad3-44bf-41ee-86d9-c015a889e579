var _=Object.defineProperty;var f=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable;var g=(e,o,t)=>o in e?_(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,b=(e,o)=>{for(var t in o||(o={}))w.call(o,t)&&g(e,t,o[t]);if(f)for(var t of f(o))y.call(o,t)&&g(e,t,o[t]);return e};var d=(e,o,t)=>new Promise((s,l)=>{var n=r=>{try{a(t.next(r))}catch(i){l(i)}},c=r=>{try{a(t.throw(r))}catch(i){l(i)}},a=r=>r.done?s(r.value):Promise.resolve(r.value).then(n,c);a((t=t.apply(e,o)).next())});import{d as F,f as k,e as D,u as p,aB as I,ar as P,aD as R,k as Y,aE as x}from"./vue-vendor-dy9k-Yad.js";import{B as C}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{ac as L,j as N}from"./index-CCWaWN5g.js";import{u as O}from"./useForm-CgkFTrrO.js";import{B as S}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const Vt=F({__name:"JeecgOrderModal",emits:["register","success"],setup(e,{emit:o}){const t=o,s=k(!0),[l,{resetFields:n,setFieldsValue:c,validate:a}]=O({labelWidth:150,schemas:[{field:"orderCode",label:"订单号",component:"Input",required:!0},{field:"ctype",label:"订单类型",component:"Select",componentProps:{options:[{label:"国内订单",value:"1"},{label:"国际订单",value:"2"}]}},{field:"orderDate",label:"订单日期",component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD hh:mm:ss"}},{field:"orderMoney",label:"订单金额",component:"InputNumber"},{field:"content",label:"订单备注",component:"Input"},{field:"id",label:"id",component:"Input",show:!1}],showActionButtonGroup:!1}),[r,{setModalProps:i,closeModal:h}]=L(m=>d(null,null,function*(){yield n(),i({confirmLoading:!1}),s.value=!!(m!=null&&m.isUpdate),p(s)&&(yield c(b({},m.record)))})),M=D(()=>p(s)?"编辑订单":"新增订单");function v(m){return d(this,null,function*(){try{let u=yield a();i({confirmLoading:!0});let B=p(s)?"/test/order/edit":"/test/order/add";N.post({url:B,params:u}),h(),t("success")}finally{i({confirmLoading:!1})}})}return(m,u)=>(P(),I(p(C),x(m.$attrs,{onRegister:p(r),title:M.value,onOk:v,width:"700px"}),{default:R(()=>[Y(p(S),{onRegister:p(l)},null,8,["onRegister"])]),_:1},16,["onRegister","title"]))}});export{Vt as default};
