var d=(v,c,e)=>new Promise((i,u)=>{var I=o=>{try{a(e.next(o))}catch(s){u(s)}},f=o=>{try{a(e.throw(o))}catch(s){u(s)}},a=o=>o.done?i(o.value):Promise.resolve(o.value).then(I,f);a((e=e.apply(v,c)).next())});import{d as g,ap as k,f as O,ag as A,aq as _,ar as P,at as r,ah as q,k as l,aD as p,G as y,au as N}from"./vue-vendor-dy9k-Yad.js";import{f as n}from"./antd-vue-vendor-me9YkNVC.js";import{q as B}from"./entrustBidding-7diNZrW8.js";import{q as E}from"./autonomouslyBidding-DdSjnKHD.js";import{a as C}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const $={class:"edit-function-test"},w={class:"test-section"},x={class:"test-section"},R={class:"test-section"},V={key:0,class:"test-result"},S=g({__name:"EditFunctionTest",setup(v){const c=k(),e=O(""),i={id:"test-entrust-001",entrustOrderId:"ENT202401001",projectName:"测试委托竞价项目",serviceType:1},u={id:"test-autonomous-001",entrustOrderId:"AUT202401001",projectName:"测试自主竞价项目",serviceType:1};function I(){return d(this,null,function*(){try{e.value="正在测试委托竞价编辑功能...",yield B(i.id),c.push({path:"/entrust/appreciationEntrust",query:{id:i.entrustOrderId,serviceType:1}}),n.success("委托竞价编辑功能测试成功"),e.value="委托竞价编辑功能测试成功，已跳转到增值委托页面"}catch(s){n.error("委托竞价编辑测试失败"),e.value=`委托竞价编辑测试失败: ${s}`}})}function f(){return d(this,null,function*(){try{e.value="正在测试自主竞价编辑功能...",yield E(u.id),c.push({path:"/entrust/selfEntrust",query:{id:u.entrustOrderId,serviceType:1}}),n.success("自主竞价编辑功能测试成功"),e.value="自主竞价编辑功能测试成功，已跳转到自主委托页面"}catch(s){n.error("自主竞价编辑测试失败"),e.value=`自主竞价编辑测试失败: ${s}`}})}function a(){return d(this,null,function*(){try{e.value="正在测试委托竞价API...";const s=yield B(i.id);e.value=`委托竞价API测试成功:
${JSON.stringify(s,null,2)}`,n.success("委托竞价API测试成功")}catch(s){e.value=`委托竞价API测试失败: ${s}`,n.error("委托竞价API测试失败")}})}function o(){return d(this,null,function*(){try{e.value="正在测试自主竞价API...";const s=yield E(u.id);e.value=`自主竞价API测试成功:
${JSON.stringify(s,null,2)}`,n.success("自主竞价API测试成功")}catch(s){e.value=`自主竞价API测试失败: ${s}`,n.error("自主竞价API测试失败")}})}return(s,t)=>{const m=A("a-button"),T=A("a-space");return P(),_("div",$,[t[10]||(t[10]=r("h2",null,"编辑功能测试页面",-1)),r("div",w,[t[1]||(t[1]=r("h3",null,"委托竞价编辑测试",-1)),l(m,{type:"primary",onClick:I},{default:p(()=>t[0]||(t[0]=[y(" 测试委托竞价编辑 ")])),_:1,__:[0]}),t[2]||(t[2]=r("p",null,"点击后会调用 queryOrderItemTempById 接口并跳转到增值委托页面",-1))]),r("div",x,[t[4]||(t[4]=r("h3",null,"自主竞价编辑测试",-1)),l(m,{type:"primary",onClick:f},{default:p(()=>t[3]||(t[3]=[y(" 测试自主竞价编辑 ")])),_:1,__:[3]}),t[5]||(t[5]=r("p",null,"点击后会调用 queryOrderAuctionItemById 接口并跳转到自主委托页面",-1))]),r("div",R,[t[8]||(t[8]=r("h3",null,"API 接口测试",-1)),l(T,null,{default:p(()=>[l(m,{onClick:a},{default:p(()=>t[6]||(t[6]=[y("测试委托竞价API")])),_:1,__:[6]}),l(m,{onClick:o},{default:p(()=>t[7]||(t[7]=[y("测试自主竞价API")])),_:1,__:[7]})]),_:1})]),e.value?(P(),_("div",V,[t[9]||(t[9]=r("h4",null,"测试结果：",-1)),r("pre",null,N(e.value),1)])):q("",!0)])}}}),z=C(S,[["__scopeId","data-v-2a90d078"]]);export{z as default};
