var ue=Object.defineProperty;var te=Object.getOwnPropertySymbols;var fe=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable;var ne=(t,l,s)=>l in t?ue(t,l,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[l]=s,q=(t,l)=>{for(var s in l||(l={}))fe.call(l,s)&&ne(t,s,l[s]);if(te)for(var s of te(l))ye.call(l,s)&&ne(t,s,l[s]);return t};var z=(t,l,s)=>new Promise((e,u)=>{var x=i=>{try{p(s.next(i))}catch(h){u(h)}},d=i=>{try{p(s.throw(i))}catch(h){u(h)}},p=i=>i.done?e(i.value):Promise.resolve(i.value).then(x,d);p((s=s.apply(t,l)).next())});import{r as _e,J as X,e as B,h as me,ag as a,aB as k,ar as f,aD as v,k as m,at as U,aA as N,au as E,aq as O,ah as F,f as I,w as Y,u as se,F as V,aC as G,G as le,as as Q,d as pe}from"./vue-vendor-dy9k-Yad.js";import{aR as Z,aS as he,aT as ve,ao as ae,aU as ge,aV as Se,x as Ue,S as xe,aO as Le,a5 as Ie}from"./antd-vue-vendor-me9YkNVC.js";import{B as ke}from"./index-Diw57m_E.js";import{d as oe,a as j,j as P,ah as Ce,ac as we,ad as be}from"./index-CCWaWN5g.js";const Te={name:"UserList",props:{multi:{type:Boolean,default:!1},dataList:{type:Array,default:()=>[]},depart:{type:Boolean,default:!1},selectedIdList:{type:Array,default:()=>[]},excludeUserIdList:{type:Array,default:()=>[]}},components:{UserOutlined:Z},emits:["selected","unSelect"],setup(t,{emit:l}){function s(r,g){r&&i(r),p[g.id]===!0?l("unSelect",g.id):l("selected",X(g))}function e(r){return r?r.substr(0,2):""}const u=B(()=>t.depart===!0?{flex:1}:{display:"none"}),x=B(()=>t.depart===!0?{width:"200px"}:{flex:1});function d(r){}const p=_e({});me(()=>{let r=t.dataList;if(!r||r.length===0)return;let g=t.selectedIdList;for(let L of r)g.indexOf(L.id)>=0?p[L.id]=!0:p[L.id]=!1});function i(r){r.preventDefault(),r.stopPropagation()}const h=B(()=>t.dataList);return{onClickUser:s,getTwoText:e,departStyle:u,nameStyle:x,onChangeChecked:d,checkStatus:p,showDataList:h,getFileAccessHttpUrl:oe}}},De=["onClick"],Oe={style:{"margin-left":"10px"}},ze=["title"];function Be(t,l,s,e,u,x){const d=a("a-checkbox"),p=a("a-radio"),i=a("a-avatar"),h=a("Icon"),r=a("UserOutlined"),g=a("a-list-item"),L=a("a-list");return f(),k(L,{"item-layout":"horizontal","data-source":e.showDataList},{renderItem:v(({item:y})=>[m(g,{style:{padding:"3px 0"}},{default:v(()=>[U("div",{class:"user-select-user-info",onClick:S=>e.onClickUser(S,y)},[U("div",Oe,[s.multi?(f(),k(d,{key:0,checked:e.checkStatus[y.id],"onUpdate:checked":S=>e.checkStatus[y.id]=S},null,8,["checked","onUpdate:checked"])):(f(),k(p,{key:1,checked:e.checkStatus[y.id],"onUpdate:checked":S=>e.checkStatus[y.id]=S},null,8,["checked","onUpdate:checked"]))]),U("div",null,[y.avatar?(f(),k(i,{key:0,src:e.getFileAccessHttpUrl(y.avatar)},null,8,["src"])):y.avatarIcon?(f(),k(i,{key:1,class:"ant-btn-primary"},{icon:v(()=>[m(h,{icon:"ant-design:"+y.avatarIcon,style:{"margin-top":"4px","font-size":"24px"}},null,8,["icon"])]),_:2},1024)):(f(),k(i,{key:2},{icon:v(()=>[m(r)]),_:1}))]),U("div",{style:N(e.nameStyle)},E(y.realname),5),U("div",{style:N(e.departStyle),class:"ellipsis",title:y.orgCodeTxt},E(y.orgCodeTxt),13,ze),l[0]||(l[0]=U("div",{style:{width:"1px"}},null,-1))],8,De)]),_:2},1024)]),_:1},8,["data-source"])}const $=j(Te,[["render",Be]]),Re={name:"SelectedUserItem",components:{UserOutlined:Z,MinusCircleFilled:ge,CloseOutlined:ae,TagsOutlined:ve,TeamOutlined:he},props:{info:{type:Object,default:()=>{}},query:{type:Boolean,default:!1}},emits:["unSelect"],setup(t,{emit:l}){function s(u){u.preventDefault(),u.stopPropagation(),l("unSelect",t.info.id)}return{showClose:B(()=>t.query===!0),removeSelect:s,getFileAccessHttpUrl:oe}}},Ae={class:"user-selected-item"},Fe={style:{display:"flex","flex-direction":"row",height:"24px","border-radius":"12px","padding-right":"10px","vertical-align":"middle","background-color":"#f5f5f5"}},Ke={style:{width:"24px",height:"24px","line-height":"20px","margin-right":"3px",display:"inline-block"}},Me={style:{height:"24px","line-height":"24px"},class:"ellipsis"},Ee={key:0,class:"icon-close"},Ne={key:0,class:"icon-remove"};function Pe(t,l,s,e,u,x){const d=a("a-avatar"),p=a("Icon"),i=a("team-outlined"),h=a("TagsOutlined"),r=a("UserOutlined"),g=a("CloseOutlined"),L=a("MinusCircleFilled");return f(),O("div",Ae,[U("div",Fe,[U("span",Ke,[s.info.avatar?(f(),k(d,{key:0,src:e.getFileAccessHttpUrl(s.info.avatar),size:24},null,8,["src"])):s.info.avatarIcon?(f(),k(d,{key:1,class:"ant-btn-primary",size:24},{icon:v(()=>[m(p,{icon:"ant-design:"+s.info.avatarIcon,style:{"font-size":"16px","margin-top":"4px"}},null,8,["icon"])]),_:1})):s.info.selectType=="sys_role"?(f(),k(d,{key:2,size:24,style:{"background-color":"rgb(255, 173, 0)"}},{icon:v(()=>[m(i,{style:{"font-size":"16px"}})]),_:1})):s.info.selectType=="sys_position"?(f(),k(d,{key:3,size:24,style:{"background-color":"rgb(245, 34, 45)"}},{icon:v(()=>[m(h,{style:{"font-size":"16px"}})]),_:1})):(f(),k(d,{key:4,size:24},{icon:v(()=>[m(r)]),_:1}))]),U("div",Me,E(s.info.realname||s.info.name),1),e.showClose?(f(),O("div",Ee,[m(g,{onClick:e.removeSelect},null,8,["onClick"])])):F("",!0)]),e.showClose?F("",!0):(f(),O("div",Ne,[m(L,{onClick:e.removeSelect},null,8,["onClick"])]))])}const re=j(Re,[["render",Pe]]),je={name:"DepartUserList",components:{UserList:$,FolderFilled:Se},props:{searchText:{type:String,default:""},selectedIdList:{type:Array,default:()=>[]},excludeUserIdList:{type:Array,default:()=>[]},multi:{type:Boolean,default:!1}},emits:["loaded","selected","unSelect"],setup(t,{emit:l}){function s(n){return z(this,null,function*(){const o="/sys/sysDepart/queryDepartTreeSync";let c={};return n&&(c.pid=n),yield P.get({url:o,params:c},{isTransformResponse:!1})})}function e(){return z(this,null,function*(){const n=yield s();if(n.success){let o=n.result;x.value=o,u(o)}r()})}function u(n){let o=[];n&&n.length>0&&(o=n.map(c=>({value:c.id,label:c.departName}))),l("loaded",o)}e();const x=I([]),d=I([]),p=I([]),i=I("");function h(n,o){let c=o.node.dataRef;d.value=[c.key];let C=n[0];i.value=C,b()}function r(){i.value=""}function g(n){return z(this,null,function*(){const o=yield s(n.eventKey);if(o.success){let c=o.result;n.dataRef.children=[...c],x.value=[...x.value]}})}const L=I(300);L.value=window.innerHeight-300;const y=B(()=>({"overflow-y":"auto","max-height":L.value+"px"})),S=I([]);function b(){return z(this,null,function*(){const n="/sys/user/selectUserList";let o={pageNo:1,pageSize:99};t.searchText&&(o.keyword=t.searchText),i.value&&(o.departId=i.value),t.excludeUserIdList&&t.excludeUserIdList.length>0&&(o.excludeUserIdList=t.excludeUserIdList.join(","));const c=yield P.get({url:n,params:o},{isTransformResponse:!1});if(c.success){const{records:C}=c.result;S.value=C}})}Y(()=>t.searchText,()=>{b()});function T(n){l("selected",n)}function R(n){l("unSelect",n)}return{containerStyle:y,treeData:x,selectedKeys:d,expandedKeys:p,onSelect:h,loadChildren:g,onSelectUser:T,unSelectUser:R,userDataList:S}}},qe={style:{"margin-left":"5px"}};function He(t,l,s,e,u,x){const d=a("FolderFilled"),p=a("a-tree"),i=a("a-col"),h=a("user-list"),r=a("a-row");return f(),k(r,null,{default:v(()=>[m(i,{span:12},{default:v(()=>[U("div",{style:N(e.containerStyle)},[e.treeData.length>0?(f(),k(p,{key:0,"load-data":e.loadChildren,showIcon:"",autoExpandParent:"",treeData:e.treeData,selectedKeys:e.selectedKeys,expandedKeys:e.expandedKeys,"onUpdate:expandedKeys":l[0]||(l[0]=g=>e.expandedKeys=g),onSelect:e.onSelect},{title:v(({title:g,key:L})=>[m(d,{style:{color:"#9e9e9e"}}),U("span",qe,E(g),1)]),_:1},8,["load-data","treeData","selectedKeys","expandedKeys","onSelect"])):F("",!0)],4)]),_:1}),m(i,{span:12,style:{"padding-left":"10px"}},{default:v(()=>[U("div",{style:N(e.containerStyle)},[m(h,{multi:s.multi,excludeUserIdList:s.excludeUserIdList,dataList:e.userDataList,selectedIdList:s.selectedIdList,onSelected:e.onSelectUser,onUnSelect:e.unSelectUser},null,8,["multi","excludeUserIdList","dataList","selectedIdList","onSelected","onUnSelect"])],4)]),_:1})]),_:1})}const Qe=j(je,[["render",He]]),Ve={name:"RoleUserList",components:{UserList:$,UserOutlined:Z},props:{searchText:{type:String,default:""},selectedIdList:{type:Array,default:()=>[]},excludeUserIdList:{type:Array,default:()=>[]},multi:{type:Boolean,default:!1}},emits:["selected","unSelect"],setup(t,{emit:l}){const s=I([]);function e(){return z(this,null,function*(){const y="/sys/role/listByTenant";let S={order:"desc",column:"createTime",pageSize:200},b=[];const T=yield P.get({url:y,params:S},{isTransformResponse:!1});if(T.success){const{records:R}=T.result;b=R.map(n=>({title:n.roleName,id:n.id,key:n.id}))}s.value=b})}e();const u=I([]),x=I("");function d(y,S){let b=S.node.dataRef;u.value=[b.key];let T=y[0];x.value=T,i()}const p=I([]);function i(){return z(this,null,function*(){const y="/sys/user/selectUserList";let S={pageNo:1,pageSize:99};t.searchText&&(S.keyword=t.searchText),x.value&&(S.roleId=x.value),t.excludeUserIdList&&t.excludeUserIdList.length>0&&(S.excludeUserIdList=t.excludeUserIdList.join(","));const b=yield P.get({url:y,params:S},{isTransformResponse:!1});if(b.success){const{records:T}=b.result;p.value=T}})}Y(()=>t.searchText,()=>{i()});function h(y){l("selected",y)}function r(y){l("unSelect",y)}const g=I(300);return g.value=window.innerHeight-300,{containerStyle:B(()=>({"overflow-y":"auto","max-height":g.value+"px"})),treeData:s,selectedKeys:u,onSelect:d,onSelectUser:h,unSelectUser:r,userDataList:p}}},Ge={style:{"margin-left":"5px"}};function Je(t,l,s,e,u,x){const d=a("UserOutlined"),p=a("a-tree"),i=a("a-col"),h=a("user-list"),r=a("a-row");return f(),k(r,null,{default:v(()=>[m(i,{span:12},{default:v(()=>[U("div",{style:N(e.containerStyle)},[e.treeData.length>0?(f(),k(p,{key:0,showIcon:"",treeData:e.treeData,selectedKeys:e.selectedKeys,onSelect:e.onSelect},{title:v(({title:g,key:L})=>[m(d,{style:{color:"#9e9e9e"}}),U("span",Ge,E(g),1)]),_:1},8,["treeData","selectedKeys","onSelect"])):F("",!0)],4)]),_:1}),m(i,{span:12,style:{"padding-left":"10px"}},{default:v(()=>[U("div",{style:N(e.containerStyle)},[m(h,{multi:s.multi,excludeUserIdList:s.excludeUserIdList,dataList:e.userDataList,selectedIdList:s.selectedIdList,onSelected:e.onSelectUser,onUnSelect:e.unSelectUser},null,8,["multi","excludeUserIdList","dataList","selectedIdList","onSelected","onUnSelect"])],4)]),_:1})]),_:1})}const We=j(Ve,[["render",Je]]),J="#{sys_user_code}",ce={id:J,username:J,realname:"当前用户",avatarIcon:"idcard-outlined",avatarColor:"rgb(75 176 79)"},Xe=Ue,Ye={name:"UserSelectModal",components:{BasicModal:ke,SearchOutlined:xe,CloseOutlined:ae,SelectedUserItem:re,UserList:$,DepartUserList:Qe,RoleUserList:We,APagination:Xe},props:{multi:{type:Boolean,default:!1},getContainer:{type:Function,default:null},izExcludeMy:{type:Boolean,default:!1},inSuperQuery:{type:Boolean,default:!1}},emits:["selected","register"],setup(t,{emit:l}){const s=I("1"),e=I([]),u=Ce(),x=B(()=>{let _=e.value;return!_||_.length==0?[]:_.map(w=>w.id)}),d=I([]),[p]=we(_=>{let w=_.list;w&&w.length>0?e.value=[...w]:e.value=[],_.excludeUserIdList?d.value=_.excludeUserIdList:d.value=[],t.izExcludeMy&&d.value.push(u.getUserInfo.id),D()});function i(){let _=X(e.value);l("selected",_)}const h=I(""),r=I([]);function g(_){r.value=[{value:"",label:"全部用户"},..._],h.value=""}function L(){D()}const y=I(!1),S=I("");function b(_){_&&ee(_),y.value=!0}function T(){n.value=1,D()}function R(_){_&&ee(_),n.value=1,S.value="",y.value=!1,D()}const n=I(1),o=I(0),c=I([]);function C(){return z(this,null,function*(){yield D()})}function D(){return z(this,null,function*(){const _="/sys/user/selectUserList";let w={pageNo:n.value,pageSize:10};S.value&&(w.keyword=S.value),h.value&&(w.departId=h.value),se(d)&&se(d).length>0&&(w.excludeUserIdList=d.value.join(","));const K=yield P.get({url:_,params:w},{isTransformResponse:!1});if(K.success){let{records:M,total:de}=K.result;o.value=de,A(M),c.value=M}})}function A(_){n.value==1&&t.inSuperQuery===!0&&_.unshift(q({},ce))}function H(_){if(t.multi===!0){let w=e.value;x.value.indexOf(_.id)<0&&(w.push(q({},_)),e.value=w)}else e.value=[q({},_)]}function W(_){let w=e.value,K=-1;for(let M=0;M<w.length;M++)if(w[M].id===_){K=M;break}K>=0&&(w.splice(K,1),e.value=w)}function ie(_){s.value=_}function ee(_){_.preventDefault(),_.stopPropagation()}return D(),{selectedDepart:h,departOptions:r,initDepartOptions:g,onDepartChange:L,register:p,handleOk:i,searchText:S,searchInputStatus:y,showSearchInput:b,onSearchUser:T,clearSearch:R,myActiveKey:s,onChangeTab:ie,pageNo:n,totalRecord:o,onPageChange:C,userDataList:c,selectedUserList:e,selectedIdList:x,onSelectUser:H,unSelectUser:W,excludeUserIdList:d}}},Ze={style:{position:"relative","min-height":"350px"}},$e={class:"my-tabs"},et={class:"selected-users",style:{width:"100%","overflow-x":"hidden"}},tt={style:{display:"flex","justify-content":"space-between",width:"100%"}},nt={class:"select-user-page-info"};function st(t,l,s,e,u,x){const d=a("a-select-option"),p=a("a-select"),i=a("SearchOutlined"),h=a("CloseOutlined"),r=a("a-input"),g=a("user-list"),L=a("a-tab-pane"),y=a("depart-user-list"),S=a("role-user-list"),b=a("a-tabs"),T=a("SelectedUserItem"),R=a("a-pagination"),n=a("a-button"),o=a("BasicModal");return f(),k(o,{onRegister:e.register,getContainer:s.getContainer,canFullscreen:!1,title:"选择用户",width:600,wrapClassName:"j-user-select-modal2"},{footer:v(()=>[U("div",tt,[U("div",nt,[e.myActiveKey=="1"?(f(),k(R,{key:0,current:e.pageNo,"onUpdate:current":l[3]||(l[3]=c=>e.pageNo=c),size:"small",total:e.totalRecord,"show-quick-jumper":"",onChange:e.onPageChange},null,8,["current","total","onChange"])):F("",!0)]),m(n,{type:"primary",onClick:e.handleOk},{default:v(()=>l[4]||(l[4]=[le("确 定")])),_:1,__:[4]},8,["onClick"])])]),default:v(()=>[m(p,{value:e.selectedDepart,"onUpdate:value":l[0]||(l[0]=c=>e.selectedDepart=c),style:{width:"100%"},class:"depart-select",onChange:e.onDepartChange},{default:v(()=>[(f(!0),O(V,null,G(e.departOptions,c=>(f(),k(d,{value:c.value},{default:v(()=>[le(E(c.label),1)]),_:2},1032,["value"]))),256))]),_:1},8,["value","onChange"]),U("div",Ze,[U("div",{class:Q(e.searchInputStatus?"my-search all-width":"my-search")},[U("span",{class:Q(e.searchInputStatus?"hidden":""),style:{"margin-left":"10px"}},[m(i,{style:{color:"#c0c0c0"},onClick:e.showSearchInput},null,8,["onClick"])],2),U("div",{style:{width:"100%"},class:Q(e.searchInputStatus?"":"hidden")},[m(r,{value:e.searchText,"onUpdate:value":l[1]||(l[1]=c=>e.searchText=c),onPressEnter:e.onSearchUser,style:{width:"100%"},placeholder:"请输入用户名按回车搜索"},{prefix:v(()=>[m(i,{style:{color:"#c0c0c0"}})]),suffix:v(()=>[m(h,{title:"退出搜索",onClick:e.clearSearch},null,8,["onClick"])]),_:1},8,["value","onPressEnter"])],2)],2),U("div",$e,[m(b,{activeKey:e.myActiveKey,"onUpdate:activeKey":l[2]||(l[2]=c=>e.myActiveKey=c),centered:!0,onChange:e.onChangeTab},{default:v(()=>[m(L,{key:"1",tab:"全部",forceRender:""},{default:v(()=>[m(g,{multi:s.multi,excludeUserIdList:e.excludeUserIdList,dataList:e.userDataList,selectedIdList:e.selectedIdList,depart:"",onSelected:e.onSelectUser,onUnSelect:e.unSelectUser},null,8,["multi","excludeUserIdList","dataList","selectedIdList","onSelected","onUnSelect"])]),_:1}),m(L,{key:"2",tab:"按部门",forceRender:""},{default:v(()=>[m(y,{searchText:e.searchText,selectedIdList:e.selectedIdList,excludeUserIdList:e.excludeUserIdList,onLoaded:e.initDepartOptions,onSelected:e.onSelectUser,onUnSelect:e.unSelectUser},null,8,["searchText","selectedIdList","excludeUserIdList","onLoaded","onSelected","onUnSelect"])]),_:1}),m(L,{key:"3",tab:"按角色",forceRender:""},{default:v(()=>[m(S,{excludeUserIdList:e.excludeUserIdList,searchText:e.searchText,selectedIdList:e.selectedIdList,onSelected:e.onSelectUser,onUnSelect:e.unSelectUser},null,8,["excludeUserIdList","searchText","selectedIdList","onSelected","onUnSelect"])]),_:1})]),_:1},8,["activeKey","onChange"])]),U("div",et,[(f(!0),O(V,null,G(e.selectedUserList,c=>(f(),k(T,{info:c,onUnSelect:e.unSelectUser},null,8,["info","onUnSelect"]))),256))])])]),_:1},8,["onRegister","getContainer"])}const lt=j(Ye,[["render",st]]),at=pe({name:"UserSelect",components:{PlusOutlined:Le,UserSelectModal:lt,SelectedUserItem:re},props:{store:{type:String,default:"id"},value:{type:String,default:""},multi:{type:Boolean,default:!1},getContainer:{type:Function,default:null},query:{type:Boolean,default:!1},maxCount:{type:Number,default:2},disabled:{type:Boolean,default:!1},izExcludeMy:{type:Boolean,default:!1},inSuperQuery:{type:Boolean,default:!1}},emits:["update:value","change"],setup(t,{emit:l}){const s=Ie.useInjectFormItemContext(),e=I(!0),u=I([]),x=B(()=>{let n=u.value,o=t.maxCount;return n.length<=o?n:n.filter((c,C)=>C<o)}),d=B(()=>{let n=t.maxCount,o=u.value.length;return o>n?{status:!0,count:o-n}:{status:!1}}),[p,{openModal:i,closeModal:h}]=be();function r(){if(t.disabled===!0)return;let n=X(u.value);i(!0,{list:n})}function g(n){u.value=n,L(),h()}function L(){e.value=!1;let n=[],o=u.value;o&&o.length>0&&(n=o.map(C=>C[t.store]));let c=n.join(",");l("update:value",c),l("change",c),s.onFieldChange()}Y(()=>t.value,n=>z(null,null,function*(){n?e.value===!0&&(yield y(n)):u.value=[],e.value=!0}),{immediate:!0});function y(n){return z(this,null,function*(){let o=!1,c=n,C=[];if(u.value=[],n){let D=n.split(",").map(A=>A.trim()).filter(A=>A!="");D.includes(J)?(o=!0,C=D.filter(A=>A!=J)):C=D}if(C.length>0){c=C.join(",");const D="/sys/user/list";let A={[t.store]:c};const H=yield P.get({url:D,params:A},{isTransformResponse:!1});if(H.success){const{records:W}=H.result;u.value=W}}o&&u.value.push(q({},ce))})}const S=B(()=>t.disabled===!0?!1:t.multi===!0?!0:!(u.value.length>0));function b(n){let o=u.value,c=-1;for(let C=0;C<o.length;C++)if(o[C].id==n){c=C;break}c>=0&&(o.splice(c,1),u.value=o,L())}function T(n){n.preventDefault(),n.stopPropagation(),r()}const R=B(()=>t.query===!0);return{registerModal:p,onShowModal:r,isSearchFormComp:R,onSelected:g,showAddButton:S,unSelectUser:b,selectedUserList:u,showUserList:x,ellipsisInfo:d,click2Add:T}}}),ot={key:1,style:{height:"30px","line-height":"30px",display:"inline-block","margin-left":"7px",color:"#bfbfbf"}},rt={key:2,class:"user-selected-item"},ct={class:"user-select-ellipsis"},it={style:{color:"red"}},dt={key:1,style:{display:"flex","flex-wrap":"wrap","flex-direction":"row"}};function ut(t,l,s,e,u,x){const d=a("SelectedUserItem"),p=a("PlusOutlined"),i=a("a-button"),h=a("user-select-modal");return f(),O("div",null,[t.isSearchFormComp?(f(),O("div",{key:0,onClick:l[0]||(l[0]=(...r)=>t.click2Add&&t.click2Add(...r)),class:Q(t.disabled?"disabled-user-select":""),style:{padding:"0 5px","background-color":"#fff",border:"1px solid #ccc","border-radius":"3px","box-sizing":"border-box",display:"flex",color:"#9e9e9e","font-size":"14px","flex-wrap":"wrap","min-height":"32px"}},[t.selectedUserList.length>0?(f(!0),O(V,{key:0},G(t.showUserList,r=>(f(),k(d,{info:r,onUnSelect:t.unSelectUser,query:""},null,8,["info","onUnSelect"]))),256)):(f(),O("span",ot,"请选择用户")),t.ellipsisInfo.status?(f(),O("div",rt,[U("div",ct,[U("span",it,"+"+E(t.ellipsisInfo.count)+"...",1)])])):F("",!0)],2)):(f(),O("div",dt,[t.selectedUserList.length>0?(f(!0),O(V,{key:0},G(t.selectedUserList,r=>(f(),k(d,{info:r,onUnSelect:t.unSelectUser},null,8,["info","onUnSelect"]))),256)):F("",!0),t.showAddButton?(f(),k(i,{key:1,shape:"circle",onClick:t.onShowModal},{default:v(()=>[m(p)]),_:1},8,["onClick"])):F("",!0)])),m(h,{inSuperQuery:t.inSuperQuery,multi:t.multi,getContainer:t.getContainer,onRegister:t.registerModal,onSelected:t.onSelected,izExcludeMy:t.izExcludeMy},null,8,["inSuperQuery","multi","getContainer","onRegister","onSelected","izExcludeMy"])])}const ht=j(at,[["render",ut],["__scopeId","data-v-f5165e21"]]);export{re as S,ht as U,lt as a};
