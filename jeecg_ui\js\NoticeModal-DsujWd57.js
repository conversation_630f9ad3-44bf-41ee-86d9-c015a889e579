var _=Object.defineProperty;var f=Object.getOwnPropertySymbols;var D=Object.prototype.hasOwnProperty,v=Object.prototype.propertyIsEnumerable;var h=(e,t,n)=>t in e?_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,C=(e,t)=>{for(var n in t||(t={}))D.call(t,n)&&h(e,n,t[n]);if(f)for(var n of f(t))v.call(t,n)&&h(e,n,t[n]);return e};var g=(e,t,n)=>new Promise((r,m)=>{var p=s=>{try{u(n.next(s))}catch(a){m(a)}},y=s=>{try{u(n.throw(s))}catch(a){m(a)}},u=s=>s.done?r(s.value):Promise.resolve(s.value).then(p,y);u((n=n.apply(e,t)).next())});import{d as R,f as T,e as x,u as i,aB as S,ar as B,aD as P,k as L,aE as M}from"./vue-vendor-dy9k-Yad.js";import{B as U}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{render as l}from"./renderUtils-D7XVOFwj.js";import{j as c,ac as k}from"./index-CCWaWN5g.js";import{u as E}from"./useForm-CgkFTrrO.js";import{B as F}from"./BasicForm-DBcXiHk0.js";const $=[{title:"标题",width:150,dataIndex:"titile"},{title:"消息类型",dataIndex:"msgCategory",width:100,customRender:({text:e})=>l.renderDict(e,"msg_category")},{title:"发布人",width:100,dataIndex:"sender"},{title:"优先级",dataIndex:"priority",width:70,customRender:({text:e})=>{const t=e=="L"?"blue":e=="M"?"yellow":"red";return l.renderTag(l.renderDict(e,"priority"),t)}},{title:"通告对象",dataIndex:"msgType",width:100,customRender:({text:e})=>l.renderDict(e,"msg_type")},{title:"发布状态",dataIndex:"sendStatus",width:70,customRender:({text:e})=>{const t=e=="0"?"red":e=="1"?"green":"gray";return l.renderTag(l.renderDict(e,"send_status"),t)}},{title:"发布时间",width:100,dataIndex:"sendTime"},{title:"撤销时间",width:100,dataIndex:"cancelTime"}],Q=[{field:"titile",label:"标题",component:"JInput",colProps:{span:8}}],N=[{field:"id",label:"id",component:"Input",show:!1},{field:"msgCategory",label:"消息类型",required:!0,component:"JDictSelectTag",defaultValue:"1",componentProps:{type:"radio",dictCode:"msg_category",placeholder:"请选择类型"}},{field:"titile",label:"标题",component:"Input",required:!0,componentProps:{placeholder:"请输入标题"},dynamicRules(){return[{validator:(e,t)=>new Promise((n,r)=>{t.length>100&&r("最长100个字符"),n()})}]}},{field:"msgAbstract",label:"摘要",component:"InputTextArea",required:!0},{field:"msgType",label:"接收用户",defaultValue:"ALL",component:"JDictSelectTag",required:!0,componentProps:{type:"radio",dictCode:"msg_type",placeholder:"请选择发布范围"}},{field:"userIds",label:"指定用户",component:"JSelectUserByDepartment",required:!0,componentProps:{rowKey:"id",labelKey:"realname"},ifShow:({values:e})=>e.msgType=="USER"},{field:"priority",label:"优先级",defaultValue:"H",component:"JDictSelectTag",componentProps:{dictCode:"priority",type:"radio",placeholder:"请选择优先级"}},{field:"msgContent",label:"内容",component:"Input",render:l.renderTinymce}];const W="/sys/annountCement/exportXls",Y="/sys/annountCement/importExcel",Z=e=>c.get({url:"/sys/annountCement/list",params:e}),j=(e,t)=>{let n=t?"/sys/annountCement/edit":"/sys/annountCement/add";return c.post({url:n,params:e})},A=(e,t)=>c.delete({url:"/sys/annountCement/delete",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),ee=e=>c.delete({url:"/sys/annountCement/deleteBatch",data:e},{joinParamsToUrl:!0}),te=e=>c.get({url:"/sys/annountCement/doReleaseData",params:e}),ne=e=>c.get({url:"/sys/annountCement/doReovkeData",params:e}),q=R({__name:"NoticeModal",emits:["register","success"],setup(e,{emit:t}){const n=t,r=T(!0),[m,{resetFields:p,setFieldsValue:y,validate:u}]=E({schemas:N,showActionButtonGroup:!1}),[s,{setModalProps:a,closeModal:I}]=k(o=>g(null,null,function*(){yield p(),a({confirmLoading:!1}),r.value=!!(o!=null&&o.isUpdate),i(r)&&(o.record.userIds&&(o.record.userIds=o.record.userIds.substring(0,o.record.userIds.length-1)),yield y(C({},o.record)))})),w=x(()=>i(r)?"编辑":"新增");function b(o){return g(this,null,function*(){try{let d=yield u();a({confirmLoading:!0}),d.msgType==="ALL"?d.userIds="":d.userIds+=",",r.value&&(d.sendStatus="0"),yield j(d,r.value),I(),n("success")}finally{a({confirmLoading:!1})}})}return(o,d)=>(B(),S(i(U),M(o.$attrs,{onRegister:i(s),title:w.value,onOk:b,width:"900px",destroyOnClose:""}),{default:P(()=>[L(i(F),{onRegister:i(m)},null,8,["onRegister"])]),_:1},16,["onRegister","title"]))}}),oe=Object.freeze(Object.defineProperty({__proto__:null,default:q},Symbol.toStringTag,{value:"Module"}));export{oe as N,q as _,W as a,ee as b,$ as c,Z as d,A as e,te as f,Y as g,ne as h,Q as s};
