import{d as o,f as n,w as r,aB as l,ar as s,u as d,aD as m,at as f,aA as u}from"./vue-vendor-dy9k-Yad.js";import{J as c}from"./antd-vue-vendor-me9YkNVC.js";import{useECharts as h}from"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const k=o({__name:"VisitSource",props:{loading:Boolean,width:{type:String,default:"100%"},height:{type:String,default:"300px"}},setup(e){const t=e,a=n(null),{setOptions:i}=h(a);return r(()=>t.loading,()=>{t.loading||i({tooltip:{trigger:"item"},legend:{bottom:"1%",left:"center"},series:[{color:["#5ab1ef","#b6a2de","#67e0e3","#2ec7c9"],name:"访问来源",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"12",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:1048,name:"搜索引擎"},{value:735,name:"直接访问"},{value:580,name:"邮件营销"},{value:484,name:"联盟广告"}],animationType:"scale",animationEasing:"exponentialInOut",animationDelay:function(){return Math.random()*100}}]})},{immediate:!0}),(p,g)=>(s(),l(d(c),{title:"访问来源",loading:e.loading},{default:m(()=>[f("div",{ref_key:"chartRef",ref:a,style:u({width:e.width,height:e.height})},null,4)]),_:1},8,["loading"]))}});export{k as default};
