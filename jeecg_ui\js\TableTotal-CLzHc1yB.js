import{d as r,f as a,aB as n,ar as l,aD as d,k as u,u as i}from"./vue-vendor-dy9k-Yad.js";import{P as s}from"./index-CtJ0w2CP.js";import{u as T}from"./index-BkGZ5fiW.js";import{bB as v}from"./index-CCWaWN5g.js";import c from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const Bt=r({__name:"TableTotal",setup(f){const o=a([]);setTimeout(()=>{o.value=[{id:0,name:"张三",point:23,level:3,updateTime:"2019-8-14"},{id:1,name:"小鹿",point:33,level:9,updateTime:"2019-8-10"},{id:2,name:"小王",point:6,level:1,updateTime:"2019-8-13"},{id:3,name:"李四",point:53,level:8,updateTime:"2019-8-12"},{id:4,name:"小红",point:44,level:5,updateTime:"2019-8-11"},{id:5,name:"王五",point:97,level:10,updateTime:"2019-8-10"},{id:6,name:"小明",point:33,level:2,updateTime:"2019-8-10"},{id:7,name:"小张",point:33,level:4,updateTime:"2019-8-10"},{id:8,name:"小六",point:33,level:2,updateTime:"2019-8-10"},{id:9,name:"小五",point:33,level:7,updateTime:"2019-8-10"},{id:10,name:"小赵",point:33,level:2,updateTime:"2019-8-10"},{id:11,name:"李华",point:33,level:8,updateTime:"2019-8-10"},{id:12,name:"小康",point:33,level:5,updateTime:"2019-8-10"}]},1e3);const[m]=T({rowKey:"id",bordered:!0,canResize:!0,columns:[{title:"姓名",width:500,dataIndex:"name"},{title:"贡献点",width:500,dataIndex:"point"},{title:"等级",width:500,dataIndex:"level"},{title:"更新时间",width:500,dataIndex:"updateTime"}],dataSource:o,showSummary:!0,striped:!0,summaryFunc:p});function p(t){const e=v(t,["point","level"]);return[e,{_row:"平均",_index:"平均",point:(e.point/t.length).toFixed(2),level:(e.level/t.length).toFixed(0)}]}return(t,e)=>(l(),n(i(s),null,{default:d(()=>[u(i(c),{onRegister:i(m),striped:!0},null,8,["onRegister"])]),_:1}))}});export{Bt as default};
