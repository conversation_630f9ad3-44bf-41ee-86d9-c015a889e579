import{a as b,j as g,bx as a}from"./index-CCWaWN5g.js";import{ag as r,aB as w,ar as m,aD as h,k as n}from"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const S={name:"Template3",components:{},data(){return{table1:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0},lastRow:null,selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:a.input},{key:"call",title:"呼叫",width:"80px",type:a.input},{key:"len",title:"长",width:"80px",type:a.input},{key:"ton",title:"吨",width:"120px",type:a.input},{key:"payer",title:"付款方",width:"120px",type:a.input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",width:"180px",type:a.input},{key:"trend",title:"动向",width:"120px",type:a.input}]},table2:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:a.input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:a.input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:a.input},{key:"type",title:"船舶分类",width:"120px",type:a.input},{key:"port_area",title:"所属港区",width:"120px",type:a.input}]},url:{getData:"/mock/vxe/getData"}}},watch:{"table1.lastRow"(e){this.loadTable2Data()}},created(){this.loadTable1Data()},methods:{loadTable1Data(){let e={pageNo:this.table1.pagination.current,pageSize:this.table1.pagination.pageSize};this.table1.loading=!0,g.get({url:this.url.getData,params:e}).then(l=>{this.table1.pagination.total=l.total,this.table1.dataSource=l.records}).finally(()=>{this.table1.loading=!1})},loadTable2Data(){let e=this.table1.selectedRows;if(!e||e.length===0){this.table2.pagination.total=0,this.table2.dataSource=[];return}else this.table1.lastRow==null&&(this.table1.lastRow=e[e.length-1]);let l={parentId:this.table1.lastRow.id,pageNo:this.table2.pagination.current,pageSize:this.table2.pagination.pageSize};this.table2.loading=!0,g.get({url:this.url.getData,params:l}).then(o=>{this.table2.pagination.total=o.total,this.table2.dataSource=o.records}).finally(()=>{this.table2.loading=!1})},handleTable1PageChange(e){this.table1.pagination.current=e.current,this.table1.pagination.pageSize=e.pageSize,this.loadTable1Data(),this.table1.selectedRows=[],this.loadTable2Data()},handleTable2PageChange(e){this.table1.pagination.current=e.current,this.table1.pagination.pageSize=e.pageSize,this.loadTable2Data()},handleTable1SelectRowChange(e){this.handleTableSelectRowChange(this.table1,e)},handleTableSelectRowChange(e,l){let{row:o,action:p,selectedRows:t,$table:s}=l,i=t[t.length-1];p==="selected"?e.lastRow=o:p==="selected-all"?t.length===0?e.lastRow=null:e.lastRow||(e.lastRow=i):p==="unselected"&&o===e.lastRow&&(e.lastRow=i),s.setCurrentRow(e.lastRow),e.selectedRows=t}}};function y(e,l,o,p,t,s){const i=r("JVxeTable"),c=r("a-col"),d=r("a-row"),u=r("a-card");return m(),w(u,{bordered:!1},{default:h(()=>[n(d,{gutter:8},{default:h(()=>[n(c,{span:12},{default:h(()=>[n(i,{toolbar:"",rowNumber:"",rowSelection:"",clickSelectRow:"",highlightCurrentRow:"",radioConfig:{highlight:!1},checkboxConfig:{highlight:!1},height:357,loading:t.table1.loading,columns:t.table1.columns,dataSource:t.table1.dataSource,pagination:t.table1.pagination,style:{"margin-bottom":"8px"},onPageChange:s.handleTable1PageChange,onSelectRowChange:s.handleTable1SelectRowChange},null,8,["loading","columns","dataSource","pagination","onPageChange","onSelectRowChange"]),n(i,{toolbar:"",rowNumber:"",rowSelection:"",clickSelectRow:"",height:356,loading:t.table2.loading,columns:t.table2.columns,dataSource:t.table2.dataSource,pagination:t.table2.pagination,onPageChange:s.handleTable2PageChange},null,8,["loading","columns","dataSource","pagination","onPageChange"])]),_:1}),n(c,{span:12},{default:h(()=>[n(i,{rowNumber:"",height:812,columns:t.table1.columns,dataSource:t.table1.selectedRows,style:{"margin-top":"52px"}},null,8,["columns","dataSource"])]),_:1})]),_:1})]),_:1})}const k=b(S,[["render",y]]);export{k as default};
