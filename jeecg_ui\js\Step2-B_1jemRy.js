var f=(c,t,e)=>new Promise((l,m)=>{var u=i=>{try{o(e.next(i))}catch(s){m(s)}},p=i=>{try{o(e.throw(i))}catch(s){m(s)}},o=i=>i.done?l(i.value):Promise.resolve(i.value).then(u,p);o((e=e.apply(c,t)).next())});import{d as B,ag as n,aq as x,ar as F,k as r,aD as a,G as _}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{step2Schemas as O}from"./data-tH9hseJm.js";import{K as d,V as b,A as g}from"./antd-vue-vendor-me9YkNVC.js";import{B as y}from"./BasicForm-DBcXiHk0.js";import{u as C}from"./useForm-CgkFTrrO.js";import{a as $}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const k=B({components:{BasicForm:y,[g.name]:g,[b.name]:b,[d.name]:d,[d.Item.name]:d.Item},emits:["next","prev"],setup(c,{emit:t}){const[e,{validate:l,setProps:m}]=C({labelWidth:120,schemas:O,actionColOptions:{span:14},resetButtonOptions:{text:"上一步"},submitButtonOptions:{text:"提交"},resetFunc:u,submitFunc:p});function u(){return f(this,null,function*(){t("prev")})}function p(){return f(this,null,function*(){try{const o=yield l();m({submitButtonOptions:{loading:!0}}),setTimeout(()=>{m({submitButtonOptions:{loading:!1}}),t("next",o)},1500)}catch(o){}})}return{register:e}}}),w={class:"step2"};function D(c,t,e,l,m,u){const p=n("a-alert"),o=n("a-descriptions-item"),i=n("a-descriptions"),s=n("a-divider"),v=n("BasicForm");return F(),x("div",w,[r(p,{message:"确认转账后，资金将直接打入对方账户，无法退回。","show-icon":""}),r(i,{column:1,class:"mt-5"},{default:a(()=>[r(o,{label:"付款账户"},{default:a(()=>t[0]||(t[0]=[_(" <EMAIL> ")])),_:1,__:[0]}),r(o,{label:"收款账户"},{default:a(()=>t[1]||(t[1]=[_(" <EMAIL> ")])),_:1,__:[1]}),r(o,{label:"收款人姓名"},{default:a(()=>t[2]||(t[2]=[_(" Jeecg ")])),_:1,__:[2]}),r(o,{label:"转账金额"},{default:a(()=>t[3]||(t[3]=[_(" 500元 ")])),_:1,__:[3]})]),_:1}),r(s),r(v,{onRegister:c.register},null,8,["onRegister"])])}const St=$(k,[["render",D],["__scopeId","data-v-3f7c23c8"]]);export{St as default};
