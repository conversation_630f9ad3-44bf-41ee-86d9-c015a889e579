import{d,ag as o,aq as _,ar as m,k as e,at as s,aD as l,G as r}from"./vue-vendor-dy9k-Yad.js";import{by as c,a2 as p}from"./antd-vue-vendor-me9YkNVC.js";import{a as u}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const f=d({components:{Result:p,CloseCircleOutlined:c}}),C={class:"m-5 result-error"},v={class:"result-error__content"},x={class:"mb-4"},b={class:"mb-4"};function k(y,t,$,B,N,O){const a=o("a-button"),i=o("Result"),n=o("CloseCircleOutlined");return m(),_("div",C,[e(i,{status:"error",title:"提交失败","sub-title":"请核对并修改以下信息后，再重新提交。"},{extra:l(()=>[e(a,{key:"console",type:"primary"},{default:l(()=>t[0]||(t[0]=[r(" 返回修改 ")])),_:1,__:[0]})]),_:1}),s("div",v,[t[5]||(t[5]=s("div",{class:"result-error__content-title"}," 您提交的内容有如下错误： ",-1)),s("div",x,[e(n,{class:"mr-2 result-error__content-icon"}),t[1]||(t[1]=r(" 您的账户已被冻结 ")),t[2]||(t[2]=s("a",{class:"ml-4"},"立即解冻 >",-1))]),s("div",b,[e(n,{class:"mr-2 result-error__content-icon"}),t[3]||(t[3]=r(" 您的账户还不具备申请资格 ")),t[4]||(t[4]=s("a",{class:"ml-4"},"立即解冻 >",-1))])])])}const w=u(f,[["render",k],["__scopeId","data-v-845e1853"]]);export{w as default};
