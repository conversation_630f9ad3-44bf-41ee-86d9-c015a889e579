var I=(u,a,m)=>new Promise((e,p)=>{var f=i=>{try{d(m.next(i))}catch(_){p(_)}},y=i=>{try{d(m.throw(i))}catch(_){p(_)}},d=i=>i.done?e(i.value):Promise.resolve(i.value).then(f,y);d((m=m.apply(u,a)).next())});import{f as v,r as T,ag as s,aq as A,ar as k,F as B,at as n,k as t,aB as J,ah as q,aO as D,aD as l,G as L,aC as R,au as x,A as N}from"./vue-vendor-dy9k-Yad.js";import{A as E,l as O,i as Q,d as V}from"./AiModelModal-Cyy66bIK.js";import"./index-Diw57m_E.js";import{x as F}from"./antd-vue-vendor-me9YkNVC.js";import{y as K}from"./componentMap-Bkie1n3v.js";import{l as U}from"./JSelectUser-COkExGbu.js";import{_ as j}from"./JAddInput-CxJ-JBK-.js";import{ad as G,a as H}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectDept-I-NqkbOH.js";import"./props-CCT78mKr.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JSelectBiz-jOYRdMJf.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./AiModelSeniorForm-DwUUzhZR.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";const W={name:"ModelList",components:{JDictSelectTag:j,JSelectUser:U,JInput:K,AiModelModal:E,Pagination:F},setup(){const u=v([]),[a,{openModal:m}]=G(),e=v(1),p=v(10),f=v(0),y=v(["10","20","30"]),d=T({}),i=T({xs:24,sm:4,xl:6,xxl:6}),_=T({xs:24,sm:20}),g=v();r();function C(){return I(this,null,function*(){m(!0,{})})}function b(o){m(!0,{id:o.id})}function r(){let o={pageNo:e.value,pageSize:p.value,column:"createTime",order:"desc"};Object.assign(o,d),O(o).then(c=>{c.success?(u.value=c.result.records,f.value=c.result.total):(u.value=[],f.value=0)})}function h(o,c){e.value=o,p.value=c,r()}const w=o=>Q.value[o];function z(o){return I(this,null,function*(){yield V({id:o.id,name:o.name},r)})}function M(){r()}function P(){g.value.resetFields(),d.createBy="",r()}function S(o){}return{handleAdd:C,handleEditClick:b,registerModal:a,modalList:u,reload:r,pageNo:e,pageSize:p,pageSizeOptions:y,total:f,handlePageChange:h,getImage:w,handleDeleteClick:z,searchQuery:M,searchReset:P,queryParam:d,labelCol:i,wrapperCol:_,formRef:g,handleParamClick:S}}},X={class:"model"},Y={class:"jeecg-basic-table-form-container"},Z={style:{float:"left",overflow:"hidden"},class:"table-page-search-submitButtons"},$={class:"flex"},ee={class:"model-header"},oe={class:"flex"},te=["src"],ae={class:"header-text"},ne={class:"mt-6"},le={class:"flex mr-14"},ie={class:"described"},se={class:"flex mr-14 mt-6"},re={class:"described"},de={class:"flex mr-14 mt-6"},ce={class:"described"},me={class:"model-btn"};function pe(u,a,m,e,p,f){const y=s("JInput"),d=s("a-form-item"),i=s("a-col"),_=s("JDictSelectTag"),g=s("a-button"),C=s("a-row"),b=s("a-form"),r=s("Icon"),h=s("a-card"),w=s("a-menu-item"),z=s("a-menu"),M=s("a-dropdown"),P=s("Pagination"),S=s("AiModelModal");return k(),A(B,null,[n("div",X,[n("div",Y,[t(b,{ref:"formRef",onKeyup:D(e.searchQuery,["enter","native"]),model:e.queryParam,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,style:{"background-color":"#f7f8fc !important"}},{default:l(()=>[t(C,{gutter:24},{default:l(()=>[t(i,{lg:6},{default:l(()=>[t(d,{name:"name",label:"模板名称"},{default:l(()=>[t(y,{value:e.queryParam.name,"onUpdate:value":a[0]||(a[0]=o=>e.queryParam.name=o)},null,8,["value"])]),_:1})]),_:1}),t(i,{lg:6},{default:l(()=>[t(d,{name:"modelType",label:"模板类型"},{default:l(()=>[t(_,{value:e.queryParam.modelType,"onUpdate:value":a[1]||(a[1]=o=>e.queryParam.modelType=o),"dict-code":"model_type"},null,8,["value"])]),_:1})]),_:1}),t(i,{xl:6,lg:7,md:8,sm:24},{default:l(()=>[n("span",Z,[t(i,{lg:6},{default:l(()=>[t(g,{type:"primary",preIcon:"ant-design:search-outlined",onClick:e.searchQuery},{default:l(()=>a[3]||(a[3]=[L("查询")])),_:1,__:[3]},8,["onClick"]),t(g,{type:"primary",preIcon:"ant-design:reload-outlined",onClick:e.searchReset,style:{"margin-left":"8px"}},{default:l(()=>a[4]||(a[4]=[L("重置")])),_:1,__:[4]},8,["onClick"])]),_:1})])]),_:1})]),_:1})]),_:1},8,["onKeyup","model","label-col","wrapper-col"])]),t(C,{span:24,class:"model-row"},{default:l(()=>[t(i,{xxl:4,xl:6,lg:6,md:6,sm:12,xs:24},{default:l(()=>[t(h,{class:"add-knowledge-card",onClick:e.handleAdd},{default:l(()=>[n("div",$,[t(r,{icon:"ant-design:plus-outlined",class:"add-knowledge-card-icon",size:"20"}),a[5]||(a[5]=n("span",{class:"add-knowledge-card-title"},"添加模型",-1))])]),_:1},8,["onClick"])]),_:1}),e.modalList&&e.modalList.length>0?(k(!0),A(B,{key:0},R(e.modalList,o=>(k(),J(i,{xxl:4,xl:6,lg:6,md:6,sm:12,xs:24},{default:l(()=>[t(h,{class:"model-card",onClick:c=>e.handleEditClick(o)},{default:l(()=>[n("div",ee,[n("div",oe,[n("img",{src:e.getImage(o.provider),class:"header-img"},null,8,te),n("div",ae,x(o.name),1)])]),n("div",ne,[n("ul",null,[n("li",le,[a[6]||(a[6]=n("span",{class:"label"},"模型类型",-1)),n("span",ie,x(o.modelType_dictText),1)]),n("li",se,[a[7]||(a[7]=n("span",{class:"label"},"基础模型",-1)),n("span",re,x(o.modelName),1)]),n("li",de,[a[8]||(a[8]=n("span",{class:"label"},"创建者",-1)),n("span",ce,x(o.createBy),1)])])]),n("div",me,[t(g,{class:"model-icon",size:"small",onClick:N(c=>e.handleEditClick(o),["prevent","stop"])},{default:l(()=>[t(r,{icon:"ant-design:edit-outlined"})]),_:2},1032,["onClick"]),t(M,{placement:"bottomRight",trigger:["click"],getPopupContainer:c=>c.parentNode},{overlay:l(()=>[t(z,null,{default:l(()=>[t(w,{key:"delete",onClick:N(c=>e.handleDeleteClick(o),["prevent","stop"])},{default:l(()=>[t(r,{icon:"ant-design:delete-outlined",size:"16"}),a[9]||(a[9]=L(" 删除 "))]),_:2,__:[9]},1032,["onClick"])]),_:2},1024)]),default:l(()=>[n("div",{class:"ant-dropdown-link pointer model-icon",onClick:a[2]||(a[2]=N(()=>{},["prevent","stop"]))},[t(r,{icon:"ant-design:ellipsis-outlined"})])]),_:2},1032,["getPopupContainer"])])]),_:2},1032,["onClick"])]),_:2},1024))),256)):q("",!0)]),_:1}),e.modalList.length>0?(k(),J(P,{key:0,current:e.pageNo,"page-size":e.pageSize,"page-size-options":e.pageSizeOptions,total:e.total,showQuickJumper:!0,showSizeChanger:!0,onChange:e.handlePageChange,class:"list-footer",size:"small"},null,8,["current","page-size","page-size-options","total","onChange"])):q("",!0)]),t(S,{onRegister:e.registerModal,onSuccess:e.reload},null,8,["onRegister","onSuccess"])],64)}const vo=H(W,[["render",pe],["__scopeId","data-v-f007b483"]]);export{vo as default};
