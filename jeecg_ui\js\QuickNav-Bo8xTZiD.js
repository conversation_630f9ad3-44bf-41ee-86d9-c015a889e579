import{d as i,aB as r,ar as t,u as e,aE as m,aD as s,aq as d,aC as p,at as o,k as u,au as _,F as f}from"./vue-vendor-dy9k-Yad.js";import{J as n}from"./antd-vue-vendor-me9YkNVC.js";import{n as k}from"./data-BJoU8O08.js";import{c as x}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const B={class:"flex flex-col items-center"},C={class:"text-md mt-2"},F=i({__name:"QuickNav",setup(h){const c=n.Grid;return(l,g)=>(t(),r(e(n),m({title:"快捷导航"},l.$attrs),{default:s(()=>[(t(!0),d(f,null,p(e(k),a=>(t(),r(e(c),{key:a},{default:s(()=>[o("span",B,[u(e(x),{icon:a.icon,color:a.color,size:"20"},null,8,["icon","color"]),o("span",C,_(a.title),1)])]),_:2},1024))),128))]),_:1},16))}});export{F as default};
