var M=Object.defineProperty,F=Object.defineProperties;var N=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var R=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable;var w=(i,e,o)=>e in i?M(i,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):i[e]=o,U=(i,e)=>{for(var o in e||(e={}))R.call(e,o)&&w(i,o,e[o]);if(y)for(var o of y(e))B.call(e,o)&&w(i,o,e[o]);return i},C=(i,e)=>F(i,N(e));var T=(i,e,o)=>new Promise((m,u)=>{var d=s=>{try{a(o.next(s))}catch(p){u(p)}},f=s=>{try{a(o.throw(s))}catch(p){u(p)}},a=s=>s.done?m(s.value):Promise.resolve(s.value).then(d,f);a((o=o.apply(i,e)).next())});import{d as S,e as x,ag as I,aq as j,ar as P,k as n,aD as l,u as g,G as v}from"./vue-vendor-dy9k-Yad.js";import{u as V}from"./index-BkGZ5fiW.js";import{j as k,b5 as D,aZ as E,u as G,bv as H}from"./index-CCWaWN5g.js";import{Q as K}from"./componentMap-Bkie1n3v.js";import L from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const q=[{title:"文件名称",dataIndex:"fileName",width:120},{title:"文件地址",dataIndex:"url",width:100}],A=[{label:"文件名称",field:"fileName",component:"Input",colProps:{span:6}},{label:"文件地址",field:"url",component:"Input",colProps:{span:6}}];const Q="/sys/oss/file/upload",W="/sys/upload/uploadMinio",X=i=>k.get({url:"/sys/oss/file/list",params:i}),Z=(i,e)=>k.delete({url:"/sys/oss/file/delete",params:i},{joinParamsToUrl:!0}).then(()=>{e()}),z=S({name:"system-ossfile"}),ot=S(C(U({},z),{setup(i){const{createMessage:e}=G(),o=D(),m={"X-Access-Token":E()},[u,{reload:d}]=V({api:X,rowKey:"id",columns:q,formConfig:{labelWidth:120,schemas:A,autoSubmitOnEnter:!0},striped:!0,useSearchForm:!0,showTableSetting:!0,clickToRowSelect:!1,bordered:!0,showIndexColumn:!1,tableSetting:{fullScreen:!0},beforeFetch:t=>Object.assign({column:"createTime",order:"desc"},t),actionColumn:{width:80,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:void 0}}),f=x(()=>`${o.uploadUrl}${Q}`),a=x(()=>`${o.uploadUrl}${W}`);function s(t){if(t&&t.url){let r=encodeURIComponent(H(t.url)),c=`${o.viewUrl}?url=`+r;window.open(c,"_blank")}}function p(t){return T(this,null,function*(){yield Z({id:t.id},d)})}function b(t){var r=t.type;if(r==="image"){if(r.indexOf("image")<0)return e.warning("请上传图片"),!1}else if(r==="file"&&r.indexOf("image")>=0)return e.warning("请上传文件"),!1;return!0}function _(t){t.file.status==="done"?t.file.response.success?(d(),e.success(`${t.file.name} 上传成功!`)):e.error(`${t.file.response.message}`):t.file.status==="error"&&e.error(`${t.file.response.message}`)}function $(t){return[{label:"预览",onClick:s.bind(null,t)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:p.bind(null,t)}}]}return(t,r)=>{const c=I("a-button"),h=I("a-upload");return P(),j("div",null,[n(g(L),{onRegister:g(u)},{tableTitle:l(()=>[n(h,{name:"file",showUploadList:!1,action:f.value,headers:m,beforeUpload:b,onChange:_},{default:l(()=>[n(c,{type:"primary",preIcon:"ant-design:upload-outlined"},{default:l(()=>r[0]||(r[0]=[v("OSS文件上传")])),_:1,__:[0]})]),_:1},8,["action"]),n(h,{name:"file",showUploadList:!1,action:a.value,headers:m,beforeUpload:b,onChange:_},{default:l(()=>[n(c,{type:"primary",preIcon:"ant-design:upload-outlined"},{default:l(()=>r[1]||(r[1]=[v("MINIO文件上传")])),_:1,__:[1]})]),_:1},8,["action"])]),action:l(({record:O})=>[n(g(K),{actions:$(O)},null,8,["actions"])]),_:1},8,["onRegister"])])}}}));export{ot as default};
