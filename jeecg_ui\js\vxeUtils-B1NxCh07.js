var y=(e,n,t)=>new Promise((u,f)=>{var a=i=>{try{r(t.next(i))}catch(o){f(o)}},s=i=>{try{r(t.throw(i))}catch(o){f(o)}},r=i=>i.done?u(i.value):Promise.resolve(i.value).then(a,s);r((t=t.apply(e,n)).next())});import{e as v}from"./index-CCWaWN5g.js";import"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const c=Symbol();function w(e,n,t,u,f){return y(this,null,function*(){if(!(e&&typeof e=="function"))throw`validate 参数需要的是一个方法，而传入的却是${typeof e}`;let a={},s=yield new Promise((i,o)=>{e().then(()=>{for(let l in n)n[l]instanceof Array&&v(u,l)==="string"&&(n[l]=n[l].join(","));i(n)}).catch(({errorFields:l})=>{o({error:c,index:0,errorFields:l})})});Object.assign(a,{formValue:s});let r=yield T(t,f);return a=Object.assign(a,{tablesValue:r}),a})}function T(e,n=!0){if(!(e instanceof Array))throw`'validateTables'函数的'cases'参数需要的是一个数组，而传入的却是${typeof e}`;return new Promise((t,u)=>{let f=[],a=0;(!e||e.length===0)&&t(f),function s(){let r=e[a];r.value.validateTable().then(i=>{if(!i)f[a]={tableData:r.value.getTableData()},++a===e.length?t(f):s();else{let o,l=p(r.value,"ATabPane");if(l&&(o=l.$.vnode.key,n)){let b=p(l,"Tabs");b&&b.setActiveKey&&b.setActiveKey(o)}u({error:c,index:a,paneKey:o,errMap:i,subIndex:a})}})}()})}function p(e,n){let t=e.$parent;if(t&&t.$options){if(t.$options.name===n)return t;{let u=p(t,n);if(u)return u}}return null}export{c as VALIDATE_FAILED,p as getVmParentByName,w as validateFormModelAndTables,T as validateTables};
