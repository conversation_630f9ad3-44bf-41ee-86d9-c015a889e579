var b=Object.defineProperty;var f=Object.getOwnPropertySymbols;var h=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable;var g=(o,r,t)=>r in o?b(o,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[r]=t,l=(o,r)=>{for(var t in r||(r={}))h.call(r,t)&&g(o,t,r[t]);if(f)for(var t of f(r))y.call(r,t)&&g(o,t,r[t]);return o};var d=(o,r,t)=>new Promise((a,s)=>{var c=e=>{try{m(t.next(e))}catch(p){s(p)}},i=e=>{try{m(t.throw(e))}catch(p){s(p)}},m=e=>e.done?a(e.value):Promise.resolve(e.value).then(c,i);m((t=t.apply(o,r)).next())});import"./index-L3cSIXth.js";import{d as C,e as D,ag as O,aB as j,ar as w}from"./vue-vendor-dy9k-Yad.js";import{a as $,j as k,H as q}from"./index-CCWaWN5g.js";import{g as v}from"./data-BTFJZHwa.js";import{u as x}from"./useForm-CgkFTrrO.js";import{B as H}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const I=C({name:"JeecgOrderCustomerForm",components:{BasicForm:H},props:{formData:q.object.def({})},setup(o){const[r,{setFieldsValue:t,setProps:a,getFieldsValue:s,updateSchema:c}]=x({labelWidth:150,schemas:v(o.formData),showActionButtonGroup:!1,baseColProps:{span:8}}),i=D(()=>o.formData.disabled!==!1);let m={};const e="/test/jeecgOrderMain/queryOrderCustomerListByMainId";function p(n){return d(this,null,function*(){let B={id:n};const u=yield k.get({url:e,params:B});if(u&&u.length>0){let _=u[0];m=l({},_),yield t(m),yield a({disabled:i.value})}})}function F(){return d(this,null,function*(){let n=l({},m);return Object.keys(n).length>0?n:!1})}return{registerForm:r,formDisabled:i,initFormData:p,getFormData:F}}});function J(o,r,t,a,s,c){const i=O("BasicForm");return w(),j(i,{onRegister:o.registerForm},null,8,["onRegister"])}const Rt=$(I,[["render",J]]);export{Rt as default};
