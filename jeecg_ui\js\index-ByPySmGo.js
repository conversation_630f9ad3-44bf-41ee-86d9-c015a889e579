var uu=Object.defineProperty,du=Object.defineProperties;var fu=Object.getOwnPropertyDescriptors;var In=Object.getOwnPropertySymbols;var eo=Object.prototype.hasOwnProperty,to=Object.prototype.propertyIsEnumerable;var Zs=(n,e,t)=>e in n?uu(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,x=(n,e)=>{for(var t in e||(e={}))eo.call(e,t)&&Zs(n,t,e[t]);if(In)for(var t of In(e))to.call(e,t)&&Zs(n,t,e[t]);return n},U=(n,e)=>du(n,fu(e));var ni=(n,e)=>{var t={};for(var r in n)eo.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(n!=null&&In)for(var r of In(n))e.indexOf(r)<0&&to.call(n,r)&&(t[r]=n[r]);return t};import{d as En,aw as hu,ak as pu,l as $r,f as Al,g as mu,h as gu,n as yu,u as Y,j as Ol,s as Zy,o as bu,r as e0,y as t0,J as n0,p as r0,w as no,ag as Lt,aq as Rn,ar as Pn,as as Ee,ah as ku,at as N,k as M,aD as O,au as ri,G as z,aA as Qt,F as ro,aC as io}from"./vue-vendor-dy9k-Yad.js";import{aZ as xu,aE as Su,u as vu,a as Cu}from"./index-CCWaWN5g.js";import{aW as ii,aX as Mu,aY as wu,aZ as Tu,a_ as Eu,a$ as Au,b0 as Ou,b1 as Nu,b2 as Du,b3 as Iu,b4 as Ru,b5 as Pu,b6 as Lu,b7 as Bu,b8 as zu,b9 as Fu,aG as $u}from"./antd-vue-vendor-me9YkNVC.js";function se(n){this.content=n}se.prototype={constructor:se,find:function(n){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===n)return e;return-1},get:function(n){var e=this.find(n);return e==-1?void 0:this.content[e+1]},update:function(n,e,t){var r=t&&t!=n?this.remove(t):this,i=r.find(n),s=r.content.slice();return i==-1?s.push(t||n,e):(s[i+1]=e,t&&(s[i]=t)),new se(s)},remove:function(n){var e=this.find(n);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new se(t)},addToStart:function(n,e){return new se([n,e].concat(this.remove(n).content))},addToEnd:function(n,e){var t=this.remove(n).content.slice();return t.push(n,e),new se(t)},addBefore:function(n,e,t){var r=this.remove(e),i=r.content.slice(),s=r.find(n);return i.splice(s==-1?i.length:s,0,e,t),new se(i)},forEach:function(n){for(var e=0;e<this.content.length;e+=2)n(this.content[e],this.content[e+1])},prepend:function(n){return n=se.from(n),n.size?new se(n.content.concat(this.subtract(n).content)):this},append:function(n){return n=se.from(n),n.size?new se(this.subtract(n).content.concat(n.content)):this},subtract:function(n){var e=this;n=se.from(n);for(var t=0;t<n.content.length;t+=2)e=e.remove(n.content[t]);return e},toObject:function(){var n={};return this.forEach(function(e,t){n[e]=t}),n},get size(){return this.content.length>>1}};se.from=function(n){if(n instanceof se)return n;var e=[];if(n)for(var t in n)e.push(t,n[t]);return new se(e)};function Nl(n,e,t){for(let r=0;;r++){if(r==n.childCount||r==e.childCount)return n.childCount==e.childCount?null:t;let i=n.child(r),s=e.child(r);if(i==s){t+=i.nodeSize;continue}if(!i.sameMarkup(s))return t;if(i.isText&&i.text!=s.text){for(let o=0;i.text[o]==s.text[o];o++)t++;return t}if(i.content.size||s.content.size){let o=Nl(i.content,s.content,t+1);if(o!=null)return o}t+=i.nodeSize}}function Dl(n,e,t,r){for(let i=n.childCount,s=e.childCount;;){if(i==0||s==0)return i==s?null:{a:t,b:r};let o=n.child(--i),l=e.child(--s),a=o.nodeSize;if(o==l){t-=a,r-=a;continue}if(!o.sameMarkup(l))return{a:t,b:r};if(o.isText&&o.text!=l.text){let c=0,u=Math.min(o.text.length,l.text.length);for(;c<u&&o.text[o.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,r--;return{a:t,b:r}}if(o.content.size||l.content.size){let c=Dl(o.content,l.content,t-1,r-1);if(c)return c}t-=a,r-=a}}let S=class de{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let r=0;r<e.length;r++)this.size+=e[r].nodeSize}nodesBetween(e,t,r,i=0,s){for(let o=0,l=0;l<t;o++){let a=this.content[o],c=l+a.nodeSize;if(c>e&&r(a,i+l,s||null,o)!==!1&&a.content.size){let u=l+1;a.nodesBetween(Math.max(0,e-u),Math.min(a.content.size,t-u),r,i+u)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,r,i){let s="",o=!0;return this.nodesBetween(e,t,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,t-a):l.isLeaf?i?typeof i=="function"?i(l):i:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&r&&(o?o=!1:s+=r),s+=c},0),s}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,r=e.firstChild,i=this.content.slice(),s=0;for(t.isText&&t.sameMarkup(r)&&(i[i.length-1]=t.withText(t.text+r.text),s=1);s<e.content.length;s++)i.push(e.content[s]);return new de(i,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let r=[],i=0;if(t>e)for(let s=0,o=0;o<t;s++){let l=this.content[s],a=o+l.nodeSize;a>e&&((o<e||a>t)&&(l.isText?l=l.cut(Math.max(0,e-o),Math.min(l.text.length,t-o)):l=l.cut(Math.max(0,e-o-1),Math.min(l.content.size,t-o-1))),r.push(l),i+=l.nodeSize),o=a}return new de(r,i)}cutByIndex(e,t){return e==t?de.empty:e==0&&t==this.content.length?this:new de(this.content.slice(e,t))}replaceChild(e,t){let r=this.content[e];if(r==t)return this;let i=this.content.slice(),s=this.size+t.nodeSize-r.nodeSize;return i[e]=t,new de(i,s)}addToStart(e){return new de([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new de(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,r=0;t<this.content.length;t++){let i=this.content[t];e(i,r,t),r+=i.nodeSize}}findDiffStart(e,t=0){return Nl(this,e,t)}findDiffEnd(e,t=this.size,r=e.size){return Dl(this,e,t,r)}findIndex(e){if(e==0)return Ln(0,e);if(e==this.size)return Ln(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let t=0,r=0;;t++){let i=this.child(t),s=r+i.nodeSize;if(s>=e)return s==e?Ln(t+1,s):Ln(t,r);r=s}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return de.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new de(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return de.empty;let t,r=0;for(let i=0;i<e.length;i++){let s=e[i];r+=s.nodeSize,i&&s.isText&&e[i-1].sameMarkup(s)?(t||(t=e.slice(0,i)),t[t.length-1]=s.withText(t[t.length-1].text+s.text)):t&&t.push(s)}return new de(t||e,r)}static from(e){if(!e)return de.empty;if(e instanceof de)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new de([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}};S.empty=new S([],0);const si={index:0,offset:0};function Ln(n,e){return si.index=n,si.offset=e,si}function Xn(n,e){if(n===e)return!0;if(!(n&&typeof n=="object")||!(e&&typeof e=="object"))return!1;let t=Array.isArray(n);if(Array.isArray(e)!=t)return!1;if(t){if(n.length!=e.length)return!1;for(let r=0;r<n.length;r++)if(!Xn(n[r],e[r]))return!1}else{for(let r in n)if(!(r in e)||!Xn(n[r],e[r]))return!1;for(let r in e)if(!(r in n))return!1}return!0}let j=class Ii{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,r=!1;for(let i=0;i<e.length;i++){let s=e[i];if(this.eq(s))return e;if(this.type.excludes(s.type))t||(t=e.slice(0,i));else{if(s.type.excludes(this.type))return e;!r&&s.type.rank>this.type.rank&&(t||(t=e.slice(0,i)),t.push(this),r=!0),t&&t.push(s)}}return t||(t=e.slice()),r||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Xn(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let r=e.marks[t.type];if(!r)throw new RangeError(`There is no mark type ${t.type} in this schema`);let i=r.create(t.attrs);return r.checkAttrs(i.attrs),i}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let r=0;r<e.length;r++)if(!e[r].eq(t[r]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return Ii.none;if(e instanceof Ii)return[e];let t=e.slice();return t.sort((r,i)=>r.type.rank-i.type.rank),t}};j.none=[];class Qn extends Error{}class w{constructor(e,t,r){this.content=e,this.openStart=t,this.openEnd=r}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let r=Rl(this.content,e+this.openStart,t);return r&&new w(r,this.openStart,this.openEnd)}removeBetween(e,t){return new w(Il(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return w.empty;let r=t.openStart||0,i=t.openEnd||0;if(typeof r!="number"||typeof i!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new w(S.fromJSON(e,t.content),r,i)}static maxOpen(e,t=!0){let r=0,i=0;for(let s=e.firstChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.firstChild)r++;for(let s=e.lastChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.lastChild)i++;return new w(e,r,i)}}w.empty=new w(S.empty,0,0);function Il(n,e,t){let{index:r,offset:i}=n.findIndex(e),s=n.maybeChild(r),{index:o,offset:l}=n.findIndex(t);if(i==e||s.isText){if(l!=t&&!n.child(o).isText)throw new RangeError("Removing non-flat range");return n.cut(0,e).append(n.cut(t))}if(r!=o)throw new RangeError("Removing non-flat range");return n.replaceChild(r,s.copy(Il(s.content,e-i-1,t-i-1)))}function Rl(n,e,t,r){let{index:i,offset:s}=n.findIndex(e),o=n.maybeChild(i);if(s==e||o.isText)return r&&!r.canReplace(i,i,t)?null:n.cut(0,e).append(t).append(n.cut(e));let l=Rl(o.content,e-s-1,t);return l&&n.replaceChild(i,o.copy(l))}function Hu(n,e,t){if(t.openStart>n.depth)throw new Qn("Inserted content deeper than insertion position");if(n.depth-t.openStart!=e.depth-t.openEnd)throw new Qn("Inconsistent open depths");return Pl(n,e,t,0)}function Pl(n,e,t,r){let i=n.index(r),s=n.node(r);if(i==e.index(r)&&r<n.depth-t.openStart){let o=Pl(n,e,t,r+1);return s.copy(s.content.replaceChild(i,o))}else if(t.content.size)if(!t.openStart&&!t.openEnd&&n.depth==r&&e.depth==r){let o=n.parent,l=o.content;return wt(o,l.cut(0,n.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}else{let{start:o,end:l}=_u(t,n);return wt(s,Bl(n,o,l,e,r))}else return wt(s,Zn(n,e,r))}function Ll(n,e){if(!e.type.compatibleContent(n.type))throw new Qn("Cannot join "+e.type.name+" onto "+n.type.name)}function Ri(n,e,t){let r=n.node(t);return Ll(r,e.node(t)),r}function Mt(n,e){let t=e.length-1;t>=0&&n.isText&&n.sameMarkup(e[t])?e[t]=n.withText(e[t].text+n.text):e.push(n)}function on(n,e,t,r){let i=(e||n).node(t),s=0,o=e?e.index(t):i.childCount;n&&(s=n.index(t),n.depth>t?s++:n.textOffset&&(Mt(n.nodeAfter,r),s++));for(let l=s;l<o;l++)Mt(i.child(l),r);e&&e.depth==t&&e.textOffset&&Mt(e.nodeBefore,r)}function wt(n,e){return n.type.checkContent(e),n.copy(e)}function Bl(n,e,t,r,i){let s=n.depth>i&&Ri(n,e,i+1),o=r.depth>i&&Ri(t,r,i+1),l=[];return on(null,n,i,l),s&&o&&e.index(i)==t.index(i)?(Ll(s,o),Mt(wt(s,Bl(n,e,t,r,i+1)),l)):(s&&Mt(wt(s,Zn(n,e,i+1)),l),on(e,t,i,l),o&&Mt(wt(o,Zn(t,r,i+1)),l)),on(r,null,i,l),new S(l)}function Zn(n,e,t){let r=[];if(on(null,n,t,r),n.depth>t){let i=Ri(n,e,t+1);Mt(wt(i,Zn(n,e,t+1)),r)}return on(e,null,t,r),new S(r)}function _u(n,e){let t=e.depth-n.openStart,i=e.node(t).copy(n.content);for(let s=t-1;s>=0;s--)i=e.node(s).copy(S.from(i));return{start:i.resolveNoCache(n.openStart+t),end:i.resolveNoCache(i.content.size-n.openEnd-t)}}class gn{constructor(e,t,r){this.pos=e,this.path=t,this.parentOffset=r,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[this.resolveDepth(e)*3]}index(e){return this.path[this.resolveDepth(e)*3+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e==this.depth&&!this.textOffset?0:1)}start(e){return e=this.resolveDepth(e),e==0?0:this.path[e*3-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]}after(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]+this.path[e*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let r=this.pos-this.path[this.path.length-1],i=e.child(t);return r?e.child(t).cut(r):i}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let r=this.path[t*3],i=t==0?0:this.path[t*3-1]+1;for(let s=0;s<e;s++)i+=r.child(s).nodeSize;return i}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return j.none;if(this.textOffset)return e.child(t).marks;let r=e.maybeChild(t-1),i=e.maybeChild(t);if(!r){let l=r;r=i,i=l}let s=r.marks;for(var o=0;o<s.length;o++)s[o].type.spec.inclusive===!1&&(!i||!s[o].isInSet(i.marks))&&(s=s[o--].removeFromSet(s));return s}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let r=t.marks,i=e.parent.maybeChild(e.index());for(var s=0;s<r.length;s++)r[s].type.spec.inclusive===!1&&(!i||!r[s].isInSet(i.marks))&&(r=r[s--].removeFromSet(r));return r}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let r=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);r>=0;r--)if(e.pos<=this.end(r)&&(!t||t(this.node(r))))return new er(this,e,r);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let r=[],i=0,s=t;for(let o=e;;){let{index:l,offset:a}=o.content.findIndex(s),c=s-a;if(r.push(o,l,i+a),!c||(o=o.child(l),o.isText))break;s=c-1,i+=a+1}return new gn(t,r,s)}static resolveCached(e,t){let r=so.get(e);if(r)for(let s=0;s<r.elts.length;s++){let o=r.elts[s];if(o.pos==t)return o}else so.set(e,r=new Vu);let i=r.elts[r.i]=gn.resolve(e,t);return r.i=(r.i+1)%ju,i}}class Vu{constructor(){this.elts=[],this.i=0}}const ju=12,so=new WeakMap;class er{constructor(e,t,r){this.$from=e,this.$to=t,this.depth=r}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const Wu=Object.create(null);class Ne{constructor(e,t,r,i=j.none){this.type=e,this.attrs=t,this.marks=i,this.content=r||S.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,r,i=0){this.content.nodesBetween(e,t,r,i,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,r,i){return this.content.textBetween(e,t,r,i)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,r){return this.type==e&&Xn(this.attrs,t||e.defaultAttrs||Wu)&&j.sameSet(this.marks,r||j.none)}copy(e=null){return e==this.content?this:new Ne(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new Ne(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,r=!1){if(e==t)return w.empty;let i=this.resolve(e),s=this.resolve(t),o=r?0:i.sharedDepth(t),l=i.start(o),c=i.node(o).content.cut(i.pos-l,s.pos-l);return new w(c,i.depth-o,s.depth-o)}replace(e,t,r){return Hu(this.resolve(e),this.resolve(t),r)}nodeAt(e){for(let t=this;;){let{index:r,offset:i}=t.content.findIndex(e);if(t=t.maybeChild(r),!t)return null;if(i==e||t.isText)return t;e-=i+1}}childAfter(e){let{index:t,offset:r}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:r}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:r}=this.content.findIndex(e);if(r<e)return{node:this.content.child(t),index:t,offset:r};let i=this.content.child(t-1);return{node:i,index:t-1,offset:r-i.nodeSize}}resolve(e){return gn.resolveCached(this,e)}resolveNoCache(e){return gn.resolve(this,e)}rangeHasMark(e,t,r){let i=!1;return t>e&&this.nodesBetween(e,t,s=>(r.isInSet(s.marks)&&(i=!0),!i)),i}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),zl(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,r=S.empty,i=0,s=r.childCount){let o=this.contentMatchAt(e).matchFragment(r,i,s),l=o&&o.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=i;a<s;a++)if(!this.type.allowsMarks(r.child(a).marks))return!1;return!0}canReplaceWith(e,t,r,i){if(i&&!this.type.allowsMarks(i))return!1;let s=this.contentMatchAt(e).matchType(r),o=s&&s.matchFragment(this.content,t);return o?o.validEnd:!1}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=j.none;for(let t=0;t<this.marks.length;t++){let r=this.marks[t];r.type.checkAttrs(r.attrs),e=r.addToSet(e)}if(!j.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let r;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");r=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,r)}let i=S.fromJSON(e,t.content),s=e.nodeType(t.type).create(t.attrs,i,r);return s.type.checkAttrs(s.attrs),s}}Ne.prototype.text=void 0;class tr extends Ne{constructor(e,t,r,i){if(super(e,t,null,i),!r)throw new RangeError("Empty text nodes are not allowed");this.text=r}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):zl(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new tr(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new tr(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function zl(n,e){for(let t=n.length-1;t>=0;t--)e=n[t].type.name+"("+e+")";return e}class Ot{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let r=new Ku(e,t);if(r.next==null)return Ot.empty;let i=Fl(r);r.next&&r.err("Unexpected trailing text");let s=Qu(Xu(i));return Zu(s,r),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,r=e.childCount){let i=this;for(let s=t;i&&s<r;s++)i=i.matchType(e.child(s).type);return i}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let r=0;r<e.next.length;r++)if(this.next[t].type==e.next[r].type)return!0;return!1}fillBefore(e,t=!1,r=0){let i=[this];function s(o,l){let a=o.matchFragment(e,r);if(a&&(!t||a.validEnd))return S.from(l.map(c=>c.createAndFill()));for(let c=0;c<o.next.length;c++){let{type:u,next:d}=o.next[c];if(!(u.isText||u.hasRequiredAttrs())&&i.indexOf(d)==-1){i.push(d);let f=s(d,l.concat(u));if(f)return f}}return null}return s(this,[])}findWrapping(e){for(let r=0;r<this.wrapCache.length;r+=2)if(this.wrapCache[r]==e)return this.wrapCache[r+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),r=[{match:this,type:null,via:null}];for(;r.length;){let i=r.shift(),s=i.match;if(s.matchType(e)){let o=[];for(let l=i;l.type;l=l.via)o.push(l.type);return o.reverse()}for(let o=0;o<s.next.length;o++){let{type:l,next:a}=s.next[o];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in t)&&(!i.type||a.validEnd)&&(r.push({match:l.contentMatch,type:l,via:i}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];function t(r){e.push(r);for(let i=0;i<r.next.length;i++)e.indexOf(r.next[i].next)==-1&&t(r.next[i].next)}return t(this),e.map((r,i)=>{let s=i+(r.validEnd?"*":" ")+" ";for(let o=0;o<r.next.length;o++)s+=(o?", ":"")+r.next[o].type.name+"->"+e.indexOf(r.next[o].next);return s}).join(`
`)}}Ot.empty=new Ot(!0);class Ku{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function Fl(n){let e=[];do e.push(Uu(n));while(n.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function Uu(n){let e=[];do e.push(qu(n));while(n.next&&n.next!=")"&&n.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function qu(n){let e=Yu(n);for(;;)if(n.eat("+"))e={type:"plus",expr:e};else if(n.eat("*"))e={type:"star",expr:e};else if(n.eat("?"))e={type:"opt",expr:e};else if(n.eat("{"))e=Ju(n,e);else break;return e}function oo(n){/\D/.test(n.next)&&n.err("Expected number, got '"+n.next+"'");let e=Number(n.next);return n.pos++,e}function Ju(n,e){let t=oo(n),r=t;return n.eat(",")&&(n.next!="}"?r=oo(n):r=-1),n.eat("}")||n.err("Unclosed braced range"),{type:"range",min:t,max:r,expr:e}}function Gu(n,e){let t=n.nodeTypes,r=t[e];if(r)return[r];let i=[];for(let s in t){let o=t[s];o.isInGroup(e)&&i.push(o)}return i.length==0&&n.err("No node type or group '"+e+"' found"),i}function Yu(n){if(n.eat("(")){let e=Fl(n);return n.eat(")")||n.err("Missing closing paren"),e}else if(/\W/.test(n.next))n.err("Unexpected token '"+n.next+"'");else{let e=Gu(n,n.next).map(t=>(n.inline==null?n.inline=t.isInline:n.inline!=t.isInline&&n.err("Mixing inline and block content"),{type:"name",value:t}));return n.pos++,e.length==1?e[0]:{type:"choice",exprs:e}}}function Xu(n){let e=[[]];return i(s(n,0),t()),e;function t(){return e.push([])-1}function r(o,l,a){let c={term:a,to:l};return e[o].push(c),c}function i(o,l){o.forEach(a=>a.to=l)}function s(o,l){if(o.type=="choice")return o.exprs.reduce((a,c)=>a.concat(s(c,l)),[]);if(o.type=="seq")for(let a=0;;a++){let c=s(o.exprs[a],l);if(a==o.exprs.length-1)return c;i(c,l=t())}else if(o.type=="star"){let a=t();return r(l,a),i(s(o.expr,a),a),[r(a)]}else if(o.type=="plus"){let a=t();return i(s(o.expr,l),a),i(s(o.expr,a),a),[r(a)]}else{if(o.type=="opt")return[r(l)].concat(s(o.expr,l));if(o.type=="range"){let a=l;for(let c=0;c<o.min;c++){let u=t();i(s(o.expr,a),u),a=u}if(o.max==-1)i(s(o.expr,a),a);else for(let c=o.min;c<o.max;c++){let u=t();r(a,u),i(s(o.expr,a),u),a=u}return[r(a)]}else{if(o.type=="name")return[r(l,void 0,o.value)];throw new Error("Unknown expr type")}}}}function $l(n,e){return e-n}function lo(n,e){let t=[];return r(e),t.sort($l);function r(i){let s=n[i];if(s.length==1&&!s[0].term)return r(s[0].to);t.push(i);for(let o=0;o<s.length;o++){let{term:l,to:a}=s[o];!l&&t.indexOf(a)==-1&&r(a)}}}function Qu(n){let e=Object.create(null);return t(lo(n,0));function t(r){let i=[];r.forEach(o=>{n[o].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let u=0;u<i.length;u++)i[u][0]==l&&(c=i[u][1]);lo(n,a).forEach(u=>{c||i.push([l,c=[]]),c.indexOf(u)==-1&&c.push(u)})})});let s=e[r.join(",")]=new Ot(r.indexOf(n.length-1)>-1);for(let o=0;o<i.length;o++){let l=i[o][1].sort($l);s.next.push({type:i[o][0],next:e[l.join(",")]||t(l)})}return s}}function Zu(n,e){for(let t=0,r=[n];t<r.length;t++){let i=r[t],s=!i.validEnd,o=[];for(let l=0;l<i.next.length;l++){let{type:a,next:c}=i.next[l];o.push(a.name),s&&!(a.isText||a.hasRequiredAttrs())&&(s=!1),r.indexOf(c)==-1&&r.push(c)}s&&e.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function Hl(n){let e=Object.create(null);for(let t in n){let r=n[t];if(!r.hasDefault)return null;e[t]=r.default}return e}function _l(n,e){let t=Object.create(null);for(let r in n){let i=e&&e[r];if(i===void 0){let s=n[r];if(s.hasDefault)i=s.default;else throw new RangeError("No value supplied for attribute "+r)}t[r]=i}return t}function Vl(n,e,t,r){for(let i in e)if(!(i in n))throw new RangeError(`Unsupported attribute ${i} for ${t} of type ${i}`);for(let i in n){let s=n[i];s.validate&&s.validate(e[i])}}function jl(n,e){let t=Object.create(null);if(e)for(let r in e)t[r]=new td(n,r,e[r]);return t}let ao=class Wl{constructor(e,t,r){this.name=e,this.schema=t,this.spec=r,this.markSet=null,this.groups=r.group?r.group.split(" "):[],this.attrs=jl(e,r.attrs),this.defaultAttrs=Hl(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(r.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==Ot.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:_l(this.attrs,e)}create(e=null,t,r){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Ne(this,this.computeAttrs(e),S.from(t),j.setFrom(r))}createChecked(e=null,t,r){return t=S.from(t),this.checkContent(t),new Ne(this,this.computeAttrs(e),t,j.setFrom(r))}createAndFill(e=null,t,r){if(e=this.computeAttrs(e),t=S.from(t),t.size){let o=this.contentMatch.fillBefore(t);if(!o)return null;t=o.append(t)}let i=this.contentMatch.matchFragment(t),s=i&&i.fillBefore(S.empty,!0);return s?new Ne(this,e,t.append(s),j.setFrom(r)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let r=0;r<e.childCount;r++)if(!this.allowsMarks(e.child(r).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){Vl(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let r=0;r<e.length;r++)this.allowsMarkType(e[r].type)?t&&t.push(e[r]):t||(t=e.slice(0,r));return t?t.length?t:j.none:e}static compile(e,t){let r=Object.create(null);e.forEach((s,o)=>r[s]=new Wl(s,t,o));let i=t.spec.topNode||"doc";if(!r[i])throw new RangeError("Schema is missing its top node type ('"+i+"')");if(!r.text)throw new RangeError("Every schema needs a 'text' type");for(let s in r.text.attrs)throw new RangeError("The text node type should not have attributes");return r}};function ed(n,e,t){let r=t.split("|");return i=>{let s=i===null?"null":typeof i;if(r.indexOf(s)<0)throw new RangeError(`Expected value of type ${r} for attribute ${e} on type ${n}, got ${s}`)}}class td{constructor(e,t,r){this.hasDefault=Object.prototype.hasOwnProperty.call(r,"default"),this.default=r.default,this.validate=typeof r.validate=="string"?ed(e,t,r.validate):r.validate}get isRequired(){return!this.hasDefault}}class Hr{constructor(e,t,r,i){this.name=e,this.rank=t,this.schema=r,this.spec=i,this.attrs=jl(e,i.attrs),this.excluded=null;let s=Hl(this.attrs);this.instance=s?new j(this,s):null}create(e=null){return!e&&this.instance?this.instance:new j(this,_l(this.attrs,e))}static compile(e,t){let r=Object.create(null),i=0;return e.forEach((s,o)=>r[s]=new Hr(s,i++,t,o)),r}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){Vl(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class Kl{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let i in e)t[i]=e[i];t.nodes=se.from(e.nodes),t.marks=se.from(e.marks||{}),this.nodes=ao.compile(this.spec.nodes,this),this.marks=Hr.compile(this.spec.marks,this);let r=Object.create(null);for(let i in this.nodes){if(i in this.marks)throw new RangeError(i+" can not be both a node and a mark");let s=this.nodes[i],o=s.spec.content||"",l=s.spec.marks;if(s.contentMatch=r[o]||(r[o]=Ot.parse(o,this.nodes)),s.inlineContent=s.contentMatch.inlineContent,s.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!s.isInline||!s.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=s}s.markSet=l=="_"?null:l?co(this,l.split(" ")):l==""||!s.inlineContent?[]:null}for(let i in this.marks){let s=this.marks[i],o=s.spec.excludes;s.excluded=o==null?[s]:o==""?[]:co(this,o.split(" "))}this.nodeFromJSON=i=>Ne.fromJSON(this,i),this.markFromJSON=i=>j.fromJSON(this,i),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,r,i){if(typeof e=="string")e=this.nodeType(e);else if(e instanceof ao){if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}else throw new RangeError("Invalid node type: "+e);return e.createChecked(t,r,i)}text(e,t){let r=this.nodes.text;return new tr(r,r.defaultAttrs,e,j.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function co(n,e){let t=[];for(let r=0;r<e.length;r++){let i=e[r],s=n.marks[i],o=s;if(s)t.push(s);else for(let l in n.marks){let a=n.marks[l];(i=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(i)>-1)&&t.push(o=a)}if(!o)throw new SyntaxError("Unknown mark type: '"+e[r]+"'")}return t}function nd(n){return n.tag!=null}function rd(n){return n.style!=null}class lt{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let r=this.matchedStyles=[];t.forEach(i=>{if(nd(i))this.tags.push(i);else if(rd(i)){let s=/[^=]*/.exec(i.style)[0];r.indexOf(s)<0&&r.push(s),this.styles.push(i)}}),this.normalizeLists=!this.tags.some(i=>{if(!/^(ul|ol)\b/.test(i.tag)||!i.node)return!1;let s=e.nodes[i.node];return s.contentMatch.matchType(s)})}parse(e,t={}){let r=new fo(this,t,!1);return r.addAll(e,j.none,t.from,t.to),r.finish()}parseSlice(e,t={}){let r=new fo(this,t,!0);return r.addAll(e,j.none,t.from,t.to),w.maxOpen(r.finish())}matchTag(e,t,r){for(let i=r?this.tags.indexOf(r)+1:0;i<this.tags.length;i++){let s=this.tags[i];if(od(e,s.tag)&&(s.namespace===void 0||e.namespaceURI==s.namespace)&&(!s.context||t.matchesContext(s.context))){if(s.getAttrs){let o=s.getAttrs(e);if(o===!1)continue;s.attrs=o||void 0}return s}}}matchStyle(e,t,r,i){for(let s=i?this.styles.indexOf(i)+1:0;s<this.styles.length;s++){let o=this.styles[s],l=o.style;if(!(l.indexOf(e)!=0||o.context&&!r.matchesContext(o.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(o.getAttrs){let a=o.getAttrs(t);if(a===!1)continue;o.attrs=a||void 0}return o}}}static schemaRules(e){let t=[];function r(i){let s=i.priority==null?50:i.priority,o=0;for(;o<t.length;o++){let l=t[o];if((l.priority==null?50:l.priority)<s)break}t.splice(o,0,i)}for(let i in e.marks){let s=e.marks[i].spec.parseDOM;s&&s.forEach(o=>{r(o=ho(o)),o.mark||o.ignore||o.clearMark||(o.mark=i)})}for(let i in e.nodes){let s=e.nodes[i].spec.parseDOM;s&&s.forEach(o=>{r(o=ho(o)),o.node||o.ignore||o.mark||(o.node=i)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new lt(e,lt.schemaRules(e)))}}const Ul={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},id={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},ql={ol:!0,ul:!0},yn=1,Pi=2,ln=4;function uo(n,e,t){return e!=null?(e?yn:0)|(e==="full"?Pi:0):n&&n.whitespace=="pre"?yn|Pi:t&~ln}class Bn{constructor(e,t,r,i,s,o){this.type=e,this.attrs=t,this.marks=r,this.solid=i,this.options=o,this.content=[],this.activeMarks=j.none,this.match=s||(o&ln?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(S.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let r=this.type.contentMatch,i;return(i=r.findWrapping(e.type))?(this.match=r,i):null}}return this.match.findWrapping(e.type)}finish(e){if(!(this.options&yn)){let r=this.content[this.content.length-1],i;if(r&&r.isText&&(i=/[ \t\r\n\u000c]+$/.exec(r.text))){let s=r;r.text.length==i[0].length?this.content.pop():this.content[this.content.length-1]=s.withText(s.text.slice(0,s.text.length-i[0].length))}}let t=S.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(S.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Ul.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class fo{constructor(e,t,r){this.parser=e,this.options=t,this.isOpen=r,this.open=0,this.localPreserveWS=!1;let i=t.topNode,s,o=uo(null,t.preserveWhitespace,0)|(r?ln:0);i?s=new Bn(i.type,i.attrs,j.none,!0,t.topMatch||i.type.contentMatch,o):r?s=new Bn(null,null,j.none,!0,null,o):s=new Bn(e.schema.topNodeType,null,j.none,!0,null,o),this.nodes=[s],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){e.nodeType==3?this.addTextNode(e,t):e.nodeType==1&&this.addElement(e,t)}addTextNode(e,t){let r=e.nodeValue,i=this.top,s=i.options&Pi?"full":this.localPreserveWS||(i.options&yn)>0;if(s==="full"||i.inlineContext(e)||/[^ \t\r\n\u000c]/.test(r)){if(s)s!=="full"?r=r.replace(/\r?\n|\r/g," "):r=r.replace(/\r\n?/g,`
`);else if(r=r.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(r)&&this.open==this.nodes.length-1){let o=i.content[i.content.length-1],l=e.previousSibling;(!o||l&&l.nodeName=="BR"||o.isText&&/[ \t\r\n\u000c]$/.test(o.text))&&(r=r.slice(1))}r&&this.insertNode(this.parser.schema.text(r),t,!/\S/.test(r)),this.findInText(e)}else this.findInside(e)}addElement(e,t,r){let i=this.localPreserveWS,s=this.top;(e.tagName=="PRE"||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let o=e.nodeName.toLowerCase(),l;ql.hasOwnProperty(o)&&this.parser.normalizeLists&&sd(e);let a=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(l=this.parser.matchTag(e,this,r));e:if(a?a.ignore:id.hasOwnProperty(o))this.findInside(e),this.ignoreFallback(e,t);else if(!a||a.skip||a.closeParent){a&&a.closeParent?this.open=Math.max(0,this.open-1):a&&a.skip.nodeType&&(e=a.skip);let c,u=this.needsBlock;if(Ul.hasOwnProperty(o))s.content.length&&s.content[0].isInline&&this.open&&(this.open--,s=this.top),c=!0,s.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let d=a&&a.skip?t:this.readStyles(e,t);d&&this.addAll(e,d),c&&this.sync(s),this.needsBlock=u}else{let c=this.readStyles(e,t);c&&this.addElementByRule(e,a,c,a.consuming===!1?l:void 0)}this.localPreserveWS=i}leafFallback(e,t){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),t)}ignoreFallback(e,t){e.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let r=e.style;if(r&&r.length)for(let i=0;i<this.parser.matchedStyles.length;i++){let s=this.parser.matchedStyles[i],o=r.getPropertyValue(s);if(o)for(let l=void 0;;){let a=this.parser.matchStyle(s,o,this,l);if(!a)break;if(a.ignore)return null;if(a.clearMark?t=t.filter(c=>!a.clearMark(c)):t=t.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming===!1)l=a;else break}}return t}addElementByRule(e,t,r,i){let s,o;if(t.node)if(o=this.parser.schema.nodes[t.node],o.isLeaf)this.insertNode(o.create(t.attrs),r,e.nodeName=="BR")||this.leafFallback(e,r);else{let a=this.enter(o,t.attrs||null,r,t.preserveWhitespace);a&&(s=!0,r=a)}else{let a=this.parser.schema.marks[t.mark];r=r.concat(a.create(t.attrs))}let l=this.top;if(o&&o.isLeaf)this.findInside(e);else if(i)this.addElement(e,r,i);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a,r,!1));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a,r),this.findAround(e,a,!1)}s&&this.sync(l)&&this.open--}addAll(e,t,r,i){let s=r||0;for(let o=r?e.childNodes[r]:e.firstChild,l=i==null?null:e.childNodes[i];o!=l;o=o.nextSibling,++s)this.findAtPoint(e,s),this.addDOM(o,t);this.findAtPoint(e,s)}findPlace(e,t,r){let i,s;for(let o=this.open,l=0;o>=0;o--){let a=this.nodes[o],c=a.findWrapping(e);if(c&&(!i||i.length>c.length+l)&&(i=c,s=a,!c.length))break;if(a.solid){if(r)break;l+=2}}if(!i)return null;this.sync(s);for(let o=0;o<i.length;o++)t=this.enterInner(i[o],null,t,!1);return t}insertNode(e,t,r){if(e.isInline&&this.needsBlock&&!this.top.type){let s=this.textblockFromContext();s&&(t=this.enterInner(s,null,t))}let i=this.findPlace(e,t,r);if(i){this.closeExtra();let s=this.top;s.match&&(s.match=s.match.matchType(e.type));let o=j.none;for(let l of i.concat(e.marks))(s.type?s.type.allowsMarkType(l.type):po(l.type,e.type))&&(o=l.addToSet(o));return s.content.push(e.mark(o)),!0}return!1}enter(e,t,r,i){let s=this.findPlace(e.create(t),r,!1);return s&&(s=this.enterInner(e,t,r,!0,i)),s}enterInner(e,t,r,i=!1,s){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(e);let l=uo(e,s,o.options);o.options&ln&&o.content.length==0&&(l|=ln);let a=j.none;return r=r.filter(c=>(o.type?o.type.allowsMarkType(c.type):po(c.type,e))?(a=c.addToSet(a),!1):!0),this.nodes.push(new Bn(e,t,a,i,null,l)),this.open++,r}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=yn)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let r=this.nodes[t].content;for(let i=r.length-1;i>=0;i--)e+=r[i].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let r=0;r<this.find.length;r++)this.find[r].node==e&&this.find[r].offset==t&&(this.find[r].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,r){if(e!=t&&this.find)for(let i=0;i<this.find.length;i++)this.find[i].pos==null&&e.nodeType==1&&e.contains(this.find[i].node)&&t.compareDocumentPosition(this.find[i].node)&(r?2:4)&&(this.find[i].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),r=this.options.context,i=!this.isOpen&&(!r||r.parent.type==this.nodes[0].type),s=-(r?r.depth+1:0)+(i?0:1),o=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=s;a--)if(o(l-1,a))return!0;return!1}else{let u=a>0||a==0&&i?this.nodes[a].type:r&&a>=s?r.node(a-s).type:null;if(!u||u.name!=c&&!u.isInGroup(c))return!1;a--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let r=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(r&&r.isTextblock&&r.defaultAttrs)return r}for(let t in this.parser.schema.nodes){let r=this.parser.schema.nodes[t];if(r.isTextblock&&r.defaultAttrs)return r}}}function sd(n){for(let e=n.firstChild,t=null;e;e=e.nextSibling){let r=e.nodeType==1?e.nodeName.toLowerCase():null;r&&ql.hasOwnProperty(r)&&t?(t.appendChild(e),e=t):r=="li"?t=e:r&&(t=null)}}function od(n,e){return(n.matches||n.msMatchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector).call(n,e)}function ho(n){let e={};for(let t in n)e[t]=n[t];return e}function po(n,e){let t=e.schema.nodes;for(let r in t){let i=t[r];if(!i.allowsMarkType(n))continue;let s=[],o=l=>{s.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:u}=l.edge(a);if(c==e||s.indexOf(u)<0&&o(u))return!0}};if(o(i.contentMatch))return!0}}class Pt{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},r){r||(r=oi(t).createDocumentFragment());let i=r,s=[];return e.forEach(o=>{if(s.length||o.marks.length){let l=0,a=0;for(;l<s.length&&a<o.marks.length;){let c=o.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(s[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<s.length;)i=s.pop()[1];for(;a<o.marks.length;){let c=o.marks[a++],u=this.serializeMark(c,o.isInline,t);u&&(s.push([c,i]),i.appendChild(u.dom),i=u.contentDOM||u.dom)}}i.appendChild(this.serializeNodeInner(o,t))}),r}serializeNodeInner(e,t){let{dom:r,contentDOM:i}=Un(oi(t),this.nodes[e.type.name](e),null,e.attrs);if(i){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,i)}return r}serializeNode(e,t={}){let r=this.serializeNodeInner(e,t);for(let i=e.marks.length-1;i>=0;i--){let s=this.serializeMark(e.marks[i],e.isInline,t);s&&((s.contentDOM||s.dom).appendChild(r),r=s.dom)}return r}serializeMark(e,t,r={}){let i=this.marks[e.type.name];return i&&Un(oi(r),i(e,t),null,e.attrs)}static renderSpec(e,t,r=null,i){return Un(e,t,r,i)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new Pt(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=mo(e.nodes);return t.text||(t.text=r=>r.text),t}static marksFromSchema(e){return mo(e.marks)}}function mo(n){let e={};for(let t in n){let r=n[t].spec.toDOM;r&&(e[t]=r)}return e}function oi(n){return n.document||window.document}const go=new WeakMap;function ld(n){let e=go.get(n);return e===void 0&&go.set(n,e=ad(n)),e}function ad(n){let e=null;function t(r){if(r&&typeof r=="object")if(Array.isArray(r))if(typeof r[0]=="string")e||(e=[]),e.push(r);else for(let i=0;i<r.length;i++)t(r[i]);else for(let i in r)t(r[i])}return t(n),e}function Un(n,e,t,r){if(typeof e=="string")return{dom:n.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let i=e[0],s;if(typeof i!="string")throw new RangeError("Invalid array passed to renderSpec");if(r&&(s=ld(r))&&s.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let o=i.indexOf(" ");o>0&&(t=i.slice(0,o),i=i.slice(o+1));let l,a=t?n.createElementNS(t,i):n.createElement(i),c=e[1],u=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){u=2;for(let d in c)if(c[d]!=null){let f=d.indexOf(" ");f>0?a.setAttributeNS(d.slice(0,f),d.slice(f+1),c[d]):d=="style"&&a.style?a.style.cssText=c[d]:a.setAttribute(d,c[d])}}for(let d=u;d<e.length;d++){let f=e[d];if(f===0){if(d<e.length-1||d>u)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}else{let{dom:h,contentDOM:p}=Un(n,f,t,r);if(a.appendChild(h),p){if(l)throw new RangeError("Multiple content holes");l=p}}}return{dom:a,contentDOM:l}}const Jl=65535,Gl=Math.pow(2,16);function cd(n,e){return n+e*Gl}function yo(n){return n&Jl}function ud(n){return(n-(n&Jl))/Gl}const Yl=1,Xl=2,qn=4,Ql=8;class Li{constructor(e,t,r){this.pos=e,this.delInfo=t,this.recover=r}get deleted(){return(this.delInfo&Ql)>0}get deletedBefore(){return(this.delInfo&(Yl|qn))>0}get deletedAfter(){return(this.delInfo&(Xl|qn))>0}get deletedAcross(){return(this.delInfo&qn)>0}}class Se{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&Se.empty)return Se.empty}recover(e){let t=0,r=yo(e);if(!this.inverted)for(let i=0;i<r;i++)t+=this.ranges[i*3+2]-this.ranges[i*3+1];return this.ranges[r*3]+t+ud(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,r){let i=0,s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?i:0);if(a>e)break;let c=this.ranges[l+s],u=this.ranges[l+o],d=a+c;if(e<=d){let f=c?e==a?-1:e==d?1:t:t,h=a+i+(f<0?0:u);if(r)return h;let p=e==(t<0?a:d)?null:cd(l/3,e-a),m=e==a?Xl:e==d?Yl:qn;return(t<0?e!=a:e!=d)&&(m|=Ql),new Li(h,m,p)}i+=u-c}return r?e+i:new Li(e+i,0,null)}touches(e,t){let r=0,i=yo(t),s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let c=this.ranges[l+s],u=a+c;if(e<=u&&l==i*3)return!0;r+=this.ranges[l+o]-c}return!1}forEach(e){let t=this.inverted?2:1,r=this.inverted?1:2;for(let i=0,s=0;i<this.ranges.length;i+=3){let o=this.ranges[i],l=o-(this.inverted?s:0),a=o+(this.inverted?0:s),c=this.ranges[i+t],u=this.ranges[i+r];e(l,l+c,a,a+u),s+=u-c}}invert(){return new Se(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?Se.empty:new Se(e<0?[0,-e,0]:[0,0,e])}}Se.empty=new Se([]);class bn{constructor(e,t,r=0,i=e?e.length:0){this.mirror=t,this.from=r,this.to=i,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new bn(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),t!=null&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,r=this._maps.length;t<e._maps.length;t++){let i=e.getMirror(t);this.appendMap(e._maps[t],i!=null&&i<t?r+i:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,r=this._maps.length+e._maps.length;t>=0;t--){let i=e.getMirror(t);this.appendMap(e._maps[t].invert(),i!=null&&i>t?r-i-1:void 0)}}invert(){let e=new bn;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let r=this.from;r<this.to;r++)e=this._maps[r].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,r){let i=0;for(let s=this.from;s<this.to;s++){let o=this._maps[s],l=o.mapResult(e,t);if(l.recover!=null){let a=this.getMirror(s);if(a!=null&&a>s&&a<this.to){s=a,e=this._maps[a].recover(l.recover);continue}}i|=l.delInfo,e=l.pos}return r?e:new Li(e,i,null)}}const li=Object.create(null);class ue{getMap(){return Se.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let r=li[t.stepType];if(!r)throw new RangeError(`No step type ${t.stepType} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in li)throw new RangeError("Duplicate use of step JSON ID "+e);return li[e]=t,t.prototype.jsonID=e,t}}class Z{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new Z(e,null)}static fail(e){return new Z(null,e)}static fromReplace(e,t,r,i){try{return Z.ok(e.replace(t,r,i))}catch(s){if(s instanceof Qn)return Z.fail(s.message);throw s}}}function ls(n,e,t){let r=[];for(let i=0;i<n.childCount;i++){let s=n.child(i);s.content.size&&(s=s.copy(ls(s.content,e,s))),s.isInline&&(s=e(s,t,i)),r.push(s)}return S.fromArray(r)}class it extends ue{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=e.resolve(this.from),i=r.node(r.sharedDepth(this.to)),s=new w(ls(t.content,(o,l)=>!o.isAtom||!l.type.allowsMarkType(this.mark.type)?o:o.mark(this.mark.addToSet(o.marks)),i),t.openStart,t.openEnd);return Z.fromReplace(e,this.from,this.to,s)}invert(){return new Oe(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new it(t.pos,r.pos,this.mark)}merge(e){return e instanceof it&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new it(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new it(t.from,t.to,e.markFromJSON(t.mark))}}ue.jsonID("addMark",it);class Oe extends ue{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=new w(ls(t.content,i=>i.mark(this.mark.removeFromSet(i.marks)),e),t.openStart,t.openEnd);return Z.fromReplace(e,this.from,this.to,r)}invert(){return new it(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new Oe(t.pos,r.pos,this.mark)}merge(e){return e instanceof Oe&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Oe(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new Oe(t.from,t.to,e.markFromJSON(t.mark))}}ue.jsonID("removeMark",Oe);class st extends ue{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return Z.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return Z.fromReplace(e,this.pos,this.pos+1,new w(S.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let r=this.mark.addToSet(t.marks);if(r.length==t.marks.length){for(let i=0;i<t.marks.length;i++)if(!t.marks[i].isInSet(r))return new st(this.pos,t.marks[i]);return new st(this.pos,this.mark)}}return new Nt(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new st(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new st(t.pos,e.markFromJSON(t.mark))}}ue.jsonID("addNodeMark",st);class Nt extends ue{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return Z.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return Z.fromReplace(e,this.pos,this.pos+1,new w(S.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return!t||!this.mark.isInSet(t.marks)?this:new st(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Nt(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new Nt(t.pos,e.markFromJSON(t.mark))}}ue.jsonID("removeNodeMark",Nt);class te extends ue{constructor(e,t,r,i=!1){super(),this.from=e,this.to=t,this.slice=r,this.structure=i}apply(e){return this.structure&&Bi(e,this.from,this.to)?Z.fail("Structure replace would overwrite content"):Z.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new Se([this.from,this.to-this.from,this.slice.size])}invert(e){return new te(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deletedAcross&&r.deletedAcross?null:new te(t.pos,Math.max(t.pos,r.pos),this.slice,this.structure)}merge(e){if(!(e instanceof te)||e.structure||this.structure)return null;if(this.from+this.slice.size==e.from&&!this.slice.openEnd&&!e.slice.openStart){let t=this.slice.size+e.slice.size==0?w.empty:new w(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new te(this.from,this.to+(e.to-e.from),t,this.structure)}else if(e.to==this.from&&!this.slice.openStart&&!e.slice.openEnd){let t=this.slice.size+e.slice.size==0?w.empty:new w(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new te(e.from,this.to,t,this.structure)}else return null}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new te(t.from,t.to,w.fromJSON(e,t.slice),!!t.structure)}}ue.jsonID("replace",te);class ne extends ue{constructor(e,t,r,i,s,o,l=!1){super(),this.from=e,this.to=t,this.gapFrom=r,this.gapTo=i,this.slice=s,this.insert=o,this.structure=l}apply(e){if(this.structure&&(Bi(e,this.from,this.gapFrom)||Bi(e,this.gapTo,this.to)))return Z.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return Z.fail("Gap is not a flat range");let r=this.slice.insertAt(this.insert,t.content);return r?Z.fromReplace(e,this.from,this.to,r):Z.fail("Content does not fit in gap")}getMap(){return new Se([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new ne(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1),i=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),s=this.to==this.gapTo?r.pos:e.map(this.gapTo,1);return t.deletedAcross&&r.deletedAcross||i<t.pos||s>r.pos?null:new ne(t.pos,r.pos,i,s,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new ne(t.from,t.to,t.gapFrom,t.gapTo,w.fromJSON(e,t.slice),t.insert,!!t.structure)}}ue.jsonID("replaceAround",ne);function Bi(n,e,t){let r=n.resolve(e),i=t-e,s=r.depth;for(;i>0&&s>0&&r.indexAfter(s)==r.node(s).childCount;)s--,i--;if(i>0){let o=r.node(s).maybeChild(r.indexAfter(s));for(;i>0;){if(!o||o.isLeaf)return!0;o=o.firstChild,i--}}return!1}function dd(n,e,t,r){let i=[],s=[],o,l;n.doc.nodesBetween(e,t,(a,c,u)=>{if(!a.isInline)return;let d=a.marks;if(!r.isInSet(d)&&u.type.allowsMarkType(r.type)){let f=Math.max(c,e),h=Math.min(c+a.nodeSize,t),p=r.addToSet(d);for(let m=0;m<d.length;m++)d[m].isInSet(p)||(o&&o.to==f&&o.mark.eq(d[m])?o.to=h:i.push(o=new Oe(f,h,d[m])));l&&l.to==f?l.to=h:s.push(l=new it(f,h,r))}}),i.forEach(a=>n.step(a)),s.forEach(a=>n.step(a))}function fd(n,e,t,r){let i=[],s=0;n.doc.nodesBetween(e,t,(o,l)=>{if(!o.isInline)return;s++;let a=null;if(r instanceof Hr){let c=o.marks,u;for(;u=r.isInSet(c);)(a||(a=[])).push(u),c=u.removeFromSet(c)}else r?r.isInSet(o.marks)&&(a=[r]):a=o.marks;if(a&&a.length){let c=Math.min(l+o.nodeSize,t);for(let u=0;u<a.length;u++){let d=a[u],f;for(let h=0;h<i.length;h++){let p=i[h];p.step==s-1&&d.eq(i[h].style)&&(f=p)}f?(f.to=c,f.step=s):i.push({style:d,from:Math.max(l,e),to:c,step:s})}}}),i.forEach(o=>n.step(new Oe(o.from,o.to,o.style)))}function as(n,e,t,r=t.contentMatch,i=!0){let s=n.doc.nodeAt(e),o=[],l=e+1;for(let a=0;a<s.childCount;a++){let c=s.child(a),u=l+c.nodeSize,d=r.matchType(c.type);if(!d)o.push(new te(l,u,w.empty));else{r=d;for(let f=0;f<c.marks.length;f++)t.allowsMarkType(c.marks[f].type)||n.step(new Oe(l,u,c.marks[f]));if(i&&c.isText&&t.whitespace!="pre"){let f,h=/\r?\n|\r/g,p;for(;f=h.exec(c.text);)p||(p=new w(S.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),o.push(new te(l+f.index,l+f.index+f[0].length,p))}}l=u}if(!r.validEnd){let a=r.fillBefore(S.empty,!0);n.replace(l,l,new w(a,0,0))}for(let a=o.length-1;a>=0;a--)n.step(o[a])}function hd(n,e,t){return(e==0||n.canReplace(e,n.childCount))&&(t==n.childCount||n.canReplace(0,t))}function Yt(n){let t=n.parent.content.cutByIndex(n.startIndex,n.endIndex);for(let r=n.depth;;--r){let i=n.$from.node(r),s=n.$from.index(r),o=n.$to.indexAfter(r);if(r<n.depth&&i.canReplace(s,o,t))return r;if(r==0||i.type.spec.isolating||!hd(i,s,o))break}return null}function pd(n,e,t){let{$from:r,$to:i,depth:s}=e,o=r.before(s+1),l=i.after(s+1),a=o,c=l,u=S.empty,d=0;for(let p=s,m=!1;p>t;p--)m||r.index(p)>0?(m=!0,u=S.from(r.node(p).copy(u)),d++):a--;let f=S.empty,h=0;for(let p=s,m=!1;p>t;p--)m||i.after(p+1)<i.end(p)?(m=!0,f=S.from(i.node(p).copy(f)),h++):c++;n.step(new ne(a,c,o,l,new w(u.append(f),d,h),u.size-d,!0))}function cs(n,e,t=null,r=n){let i=md(n,e),s=i&&gd(r,e);return s?i.map(bo).concat({type:e,attrs:t}).concat(s.map(bo)):null}function bo(n){return{type:n,attrs:null}}function md(n,e){let{parent:t,startIndex:r,endIndex:i}=n,s=t.contentMatchAt(r).findWrapping(e);if(!s)return null;let o=s.length?s[0]:e;return t.canReplaceWith(r,i,o)?s:null}function gd(n,e){let{parent:t,startIndex:r,endIndex:i}=n,s=t.child(r),o=e.contentMatch.findWrapping(s.type);if(!o)return null;let a=(o.length?o[o.length-1]:e).contentMatch;for(let c=r;a&&c<i;c++)a=a.matchType(t.child(c).type);return!a||!a.validEnd?null:o}function yd(n,e,t){let r=S.empty;for(let o=t.length-1;o>=0;o--){if(r.size){let l=t[o].type.contentMatch.matchFragment(r);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=S.from(t[o].type.create(t[o].attrs,r))}let i=e.start,s=e.end;n.step(new ne(i,s,i,s,new w(r,0,0),t.length,!0))}function bd(n,e,t,r,i){if(!r.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let s=n.steps.length;n.doc.nodesBetween(e,t,(o,l)=>{let a=typeof i=="function"?i(o):i;if(o.isTextblock&&!o.hasMarkup(r,a)&&kd(n.doc,n.mapping.slice(s).map(l),r)){let c=null;if(r.schema.linebreakReplacement){let h=r.whitespace=="pre",p=!!r.contentMatch.matchType(r.schema.linebreakReplacement);h&&!p?c=!1:!h&&p&&(c=!0)}c===!1&&ea(n,o,l,s),as(n,n.mapping.slice(s).map(l,1),r,void 0,c===null);let u=n.mapping.slice(s),d=u.map(l,1),f=u.map(l+o.nodeSize,1);return n.step(new ne(d,f,d+1,f-1,new w(S.from(r.create(a,null,o.marks)),0,0),1,!0)),c===!0&&Zl(n,o,l,s),!1}})}function Zl(n,e,t,r){e.forEach((i,s)=>{if(i.isText){let o,l=/\r?\n|\r/g;for(;o=l.exec(i.text);){let a=n.mapping.slice(r).map(t+1+s+o.index);n.replaceWith(a,a+1,e.type.schema.linebreakReplacement.create())}}})}function ea(n,e,t,r){e.forEach((i,s)=>{if(i.type==i.type.schema.linebreakReplacement){let o=n.mapping.slice(r).map(t+1+s);n.replaceWith(o,o+1,e.type.schema.text(`
`))}})}function kd(n,e,t){let r=n.resolve(e),i=r.index();return r.parent.canReplaceWith(i,i+1,t)}function xd(n,e,t,r,i){let s=n.doc.nodeAt(e);if(!s)throw new RangeError("No node at given position");t||(t=s.type);let o=t.create(r,null,i||s.marks);if(s.isLeaf)return n.replaceWith(e,e+s.nodeSize,o);if(!t.validContent(s.content))throw new RangeError("Invalid content for node type "+t.name);n.step(new ne(e,e+s.nodeSize,e+1,e+s.nodeSize-1,new w(S.from(o),0,0),1,!0))}function We(n,e,t=1,r){let i=n.resolve(e),s=i.depth-t,o=r&&r[r.length-1]||i.parent;if(s<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!o.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let c=i.depth-1,u=t-2;c>s;c--,u--){let d=i.node(c),f=i.index(c);if(d.type.spec.isolating)return!1;let h=d.content.cutByIndex(f,d.childCount),p=r&&r[u+1];p&&(h=h.replaceChild(0,p.type.create(p.attrs)));let m=r&&r[u]||d;if(!d.canReplace(f+1,d.childCount)||!m.type.validContent(h))return!1}let l=i.indexAfter(s),a=r&&r[0];return i.node(s).canReplaceWith(l,l,a?a.type:i.node(s+1).type)}function Sd(n,e,t=1,r){let i=n.doc.resolve(e),s=S.empty,o=S.empty;for(let l=i.depth,a=i.depth-t,c=t-1;l>a;l--,c--){s=S.from(i.node(l).copy(s));let u=r&&r[c];o=S.from(u?u.type.create(u.attrs,o):i.node(l).copy(o))}n.step(new te(e,e,new w(s.append(o),t,t),!0))}function pt(n,e){let t=n.resolve(e),r=t.index();return ta(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(r,r+1)}function vd(n,e){e.content.size||n.type.compatibleContent(e.type);let t=n.contentMatchAt(n.childCount),{linebreakReplacement:r}=n.type.schema;for(let i=0;i<e.childCount;i++){let s=e.child(i),o=s.type==r?n.type.schema.nodes.text:s.type;if(t=t.matchType(o),!t||!n.type.allowsMarks(s.marks))return!1}return t.validEnd}function ta(n,e){return!!(n&&e&&!n.isLeaf&&vd(n,e))}function _r(n,e,t=-1){let r=n.resolve(e);for(let i=r.depth;;i--){let s,o,l=r.index(i);if(i==r.depth?(s=r.nodeBefore,o=r.nodeAfter):t>0?(s=r.node(i+1),l++,o=r.node(i).maybeChild(l)):(s=r.node(i).maybeChild(l-1),o=r.node(i+1)),s&&!s.isTextblock&&ta(s,o)&&r.node(i).canReplace(l,l+1))return e;if(i==0)break;e=t<0?r.before(i):r.after(i)}}function Cd(n,e,t){let r=null,{linebreakReplacement:i}=n.doc.type.schema,s=n.doc.resolve(e-t),o=s.node().type;if(i&&o.inlineContent){let u=o.whitespace=="pre",d=!!o.contentMatch.matchType(i);u&&!d?r=!1:!u&&d&&(r=!0)}let l=n.steps.length;if(r===!1){let u=n.doc.resolve(e+t);ea(n,u.node(),u.before(),l)}o.inlineContent&&as(n,e+t-1,o,s.node().contentMatchAt(s.index()),r==null);let a=n.mapping.slice(l),c=a.map(e-t);if(n.step(new te(c,a.map(e+t,-1),w.empty,!0)),r===!0){let u=n.doc.resolve(c);Zl(n,u.node(),u.before(),n.steps.length)}return n}function Md(n,e,t){let r=n.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),t))return e;if(r.parentOffset==0)for(let i=r.depth-1;i>=0;i--){let s=r.index(i);if(r.node(i).canReplaceWith(s,s,t))return r.before(i+1);if(s>0)return null}if(r.parentOffset==r.parent.content.size)for(let i=r.depth-1;i>=0;i--){let s=r.indexAfter(i);if(r.node(i).canReplaceWith(s,s,t))return r.after(i+1);if(s<r.node(i).childCount)return null}return null}function na(n,e,t){let r=n.resolve(e);if(!t.content.size)return e;let i=t.content;for(let s=0;s<t.openStart;s++)i=i.firstChild.content;for(let s=1;s<=(t.openStart==0&&t.size?2:1);s++)for(let o=r.depth;o>=0;o--){let l=o==r.depth?0:r.pos<=(r.start(o+1)+r.end(o+1))/2?-1:1,a=r.index(o)+(l>0?1:0),c=r.node(o),u=!1;if(s==1)u=c.canReplace(a,a,i);else{let d=c.contentMatchAt(a).findWrapping(i.firstChild.type);u=d&&c.canReplaceWith(a,a,d[0])}if(u)return l==0?r.pos:l<0?r.before(o+1):r.after(o+1)}return null}function Vr(n,e,t=e,r=w.empty){if(e==t&&!r.size)return null;let i=n.resolve(e),s=n.resolve(t);return ra(i,s,r)?new te(e,t,r):new wd(i,s,r).fit()}function ra(n,e,t){return!t.openStart&&!t.openEnd&&n.start()==e.start()&&n.parent.canReplace(n.index(),e.index(),t.content)}class wd{constructor(e,t,r){this.$from=e,this.$to=t,this.unplaced=r,this.frontier=[],this.placed=S.empty;for(let i=0;i<=e.depth;i++){let s=e.node(i);this.frontier.push({type:s.type,match:s.contentMatchAt(e.indexAfter(i))})}for(let i=e.depth;i>0;i--)this.placed=S.from(e.node(i).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,r=this.$from,i=this.close(e<0?this.$to:r.doc.resolve(e));if(!i)return null;let s=this.placed,o=r.depth,l=i.depth;for(;o&&l&&s.childCount==1;)s=s.firstChild.content,o--,l--;let a=new w(s,o,l);return e>-1?new ne(r.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||r.pos!=this.$to.pos?new te(r.pos,i.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,r=0,i=this.unplaced.openEnd;r<e;r++){let s=t.firstChild;if(t.childCount>1&&(i=0),s.type.spec.isolating&&i<=r){e=r;break}t=s.content}for(let t=1;t<=2;t++)for(let r=t==1?e:this.unplaced.openStart;r>=0;r--){let i,s=null;r?(s=ai(this.unplaced.content,r-1).firstChild,i=s.content):i=this.unplaced.content;let o=i.firstChild;for(let l=this.depth;l>=0;l--){let{type:a,match:c}=this.frontier[l],u,d=null;if(t==1&&(o?c.matchType(o.type)||(d=c.fillBefore(S.from(o),!1)):s&&a.compatibleContent(s.type)))return{sliceDepth:r,frontierDepth:l,parent:s,inject:d};if(t==2&&o&&(u=c.findWrapping(o.type)))return{sliceDepth:r,frontierDepth:l,parent:s,wrap:u};if(s&&c.matchType(s.type))break}}}openMore(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=ai(e,t);return!i.childCount||i.firstChild.isLeaf?!1:(this.unplaced=new w(e,t+1,Math.max(r,i.size+t>=e.size-r?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=ai(e,t);if(i.childCount<=1&&t>0){let s=e.size-t<=t+i.size;this.unplaced=new w(tn(e,t-1,1),t-1,s?t-1:r)}else this.unplaced=new w(tn(e,t,1),t,r)}placeNodes({sliceDepth:e,frontierDepth:t,parent:r,inject:i,wrap:s}){for(;this.depth>t;)this.closeFrontierNode();if(s)for(let m=0;m<s.length;m++)this.openFrontierNode(s[m]);let o=this.unplaced,l=r?r.content:o.content,a=o.openStart-e,c=0,u=[],{match:d,type:f}=this.frontier[t];if(i){for(let m=0;m<i.childCount;m++)u.push(i.child(m));d=d.matchFragment(i)}let h=l.size+e-(o.content.size-o.openEnd);for(;c<l.childCount;){let m=l.child(c),g=d.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(d=g,u.push(ia(m.mark(f.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?h:-1)))}let p=c==l.childCount;p||(h=-1),this.placed=nn(this.placed,t,S.from(u)),this.frontier[t].match=d,p&&h<0&&r&&r.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<h;m++){let b=g.lastChild;this.frontier.push({type:b.type,match:b.contentMatchAt(b.childCount)}),g=b.content}this.unplaced=p?e==0?w.empty:new w(tn(o.content,e-1,1),e-1,h<0?o.openEnd:e-1):new w(tn(o.content,e,c),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!ci(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:r}=this.$to,i=this.$to.after(r);for(;r>1&&i==this.$to.end(--r);)++i;return i}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:r,type:i}=this.frontier[t],s=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=ci(e,t,i,r,s);if(o){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],u=ci(e,l,c,a,!0);if(!u||u.childCount)continue e}return{depth:t,fit:o,move:s?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=nn(this.placed,t.depth,t.fit)),e=t.move;for(let r=t.depth+1;r<=e.depth;r++){let i=e.node(r),s=i.type.contentMatch.fillBefore(i.content,!0,e.index(r));this.openFrontierNode(i.type,i.attrs,s)}return e}openFrontierNode(e,t=null,r){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=nn(this.placed,this.depth,S.from(e.create(t,r))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(S.empty,!0);t.childCount&&(this.placed=nn(this.placed,this.frontier.length,t))}}function tn(n,e,t){return e==0?n.cutByIndex(t,n.childCount):n.replaceChild(0,n.firstChild.copy(tn(n.firstChild.content,e-1,t)))}function nn(n,e,t){return e==0?n.append(t):n.replaceChild(n.childCount-1,n.lastChild.copy(nn(n.lastChild.content,e-1,t)))}function ai(n,e){for(let t=0;t<e;t++)n=n.firstChild.content;return n}function ia(n,e,t){if(e<=0)return n;let r=n.content;return e>1&&(r=r.replaceChild(0,ia(r.firstChild,e-1,r.childCount==1?t-1:0))),e>0&&(r=n.type.contentMatch.fillBefore(r).append(r),t<=0&&(r=r.append(n.type.contentMatch.matchFragment(r).fillBefore(S.empty,!0)))),n.copy(r)}function ci(n,e,t,r,i){let s=n.node(e),o=i?n.indexAfter(e):n.index(e);if(o==s.childCount&&!t.compatibleContent(s.type))return null;let l=r.fillBefore(s.content,!0,o);return l&&!Td(t,s.content,o)?l:null}function Td(n,e,t){for(let r=t;r<e.childCount;r++)if(!n.allowsMarks(e.child(r).marks))return!0;return!1}function Ed(n){return n.spec.defining||n.spec.definingForContent}function Ad(n,e,t,r){if(!r.size)return n.deleteRange(e,t);let i=n.doc.resolve(e),s=n.doc.resolve(t);if(ra(i,s,r))return n.step(new te(e,t,r));let o=oa(i,n.doc.resolve(t));o[o.length-1]==0&&o.pop();let l=-(i.depth+1);o.unshift(l);for(let f=i.depth,h=i.pos-1;f>0;f--,h--){let p=i.node(f).type.spec;if(p.defining||p.definingAsContext||p.isolating)break;o.indexOf(f)>-1?l=f:i.before(f)==h&&o.splice(1,0,-f)}let a=o.indexOf(l),c=[],u=r.openStart;for(let f=r.content,h=0;;h++){let p=f.firstChild;if(c.push(p),h==r.openStart)break;f=p.content}for(let f=u-1;f>=0;f--){let h=c[f],p=Ed(h.type);if(p&&!h.sameMarkup(i.node(Math.abs(l)-1)))u=f;else if(p||!h.type.isTextblock)break}for(let f=r.openStart;f>=0;f--){let h=(f+u+1)%(r.openStart+1),p=c[h];if(p)for(let m=0;m<o.length;m++){let g=o[(m+a)%o.length],b=!0;g<0&&(b=!1,g=-g);let v=i.node(g-1),D=i.index(g-1);if(v.canReplaceWith(D,D,p.type,p.marks))return n.replace(i.before(g),b?s.after(g):t,new w(sa(r.content,0,r.openStart,h),h,r.openEnd))}}let d=n.steps.length;for(let f=o.length-1;f>=0&&(n.replace(e,t,r),!(n.steps.length>d));f--){let h=o[f];h<0||(e=i.before(h),t=s.after(h))}}function sa(n,e,t,r,i){if(e<t){let s=n.firstChild;n=n.replaceChild(0,s.copy(sa(s.content,e+1,t,r,s)))}if(e>r){let s=i.contentMatchAt(0),o=s.fillBefore(n).append(n);n=o.append(s.matchFragment(o).fillBefore(S.empty,!0))}return n}function Od(n,e,t,r){if(!r.isInline&&e==t&&n.doc.resolve(e).parent.content.size){let i=Md(n.doc,e,r.type);i!=null&&(e=t=i)}n.replaceRange(e,t,new w(S.from(r),0,0))}function Nd(n,e,t){let r=n.doc.resolve(e),i=n.doc.resolve(t),s=oa(r,i);for(let o=0;o<s.length;o++){let l=s[o],a=o==s.length-1;if(a&&l==0||r.node(l).type.contentMatch.validEnd)return n.delete(r.start(l),i.end(l));if(l>0&&(a||r.node(l-1).canReplace(r.index(l-1),i.indexAfter(l-1))))return n.delete(r.before(l),i.after(l))}for(let o=1;o<=r.depth&&o<=i.depth;o++)if(e-r.start(o)==r.depth-o&&t>r.end(o)&&i.end(o)-t!=i.depth-o&&r.start(o-1)==i.start(o-1)&&r.node(o-1).canReplace(r.index(o-1),i.index(o-1)))return n.delete(r.before(o),t);n.delete(e,t)}function oa(n,e){let t=[],r=Math.min(n.depth,e.depth);for(let i=r;i>=0;i--){let s=n.start(i);if(s<n.pos-(n.depth-i)||e.end(i)>e.pos+(e.depth-i)||n.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(s==e.start(i)||i==n.depth&&i==e.depth&&n.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==s-1)&&t.push(i)}return t}class jt extends ue{constructor(e,t,r){super(),this.pos=e,this.attr=t,this.value=r}apply(e){let t=e.nodeAt(this.pos);if(!t)return Z.fail("No node at attribute step's position");let r=Object.create(null);for(let s in t.attrs)r[s]=t.attrs[s];r[this.attr]=this.value;let i=t.type.create(r,null,t.marks);return Z.fromReplace(e,this.pos,this.pos+1,new w(S.from(i),0,t.isLeaf?0:1))}getMap(){return Se.empty}invert(e){return new jt(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new jt(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new jt(t.pos,t.attr,t.value)}}ue.jsonID("attr",jt);class kn extends ue{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let i in e.attrs)t[i]=e.attrs[i];t[this.attr]=this.value;let r=e.type.create(t,e.content,e.marks);return Z.ok(r)}getMap(){return Se.empty}invert(e){return new kn(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new kn(t.attr,t.value)}}ue.jsonID("docAttr",kn);let Kt=class extends Error{};Kt=function n(e){let t=Error.call(this,e);return t.__proto__=n.prototype,t};Kt.prototype=Object.create(Error.prototype);Kt.prototype.constructor=Kt;Kt.prototype.name="TransformError";class la{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new bn}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new Kt(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,r=w.empty){let i=Vr(this.doc,e,t,r);return i&&this.step(i),this}replaceWith(e,t,r){return this.replace(e,t,new w(S.from(r),0,0))}delete(e,t){return this.replace(e,t,w.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,r){return Ad(this,e,t,r),this}replaceRangeWith(e,t,r){return Od(this,e,t,r),this}deleteRange(e,t){return Nd(this,e,t),this}lift(e,t){return pd(this,e,t),this}join(e,t=1){return Cd(this,e,t),this}wrap(e,t){return yd(this,e,t),this}setBlockType(e,t=e,r,i=null){return bd(this,e,t,r,i),this}setNodeMarkup(e,t,r=null,i){return xd(this,e,t,r,i),this}setNodeAttribute(e,t,r){return this.step(new jt(e,t,r)),this}setDocAttribute(e,t){return this.step(new kn(e,t)),this}addNodeMark(e,t){return this.step(new st(e,t)),this}removeNodeMark(e,t){let r=this.doc.nodeAt(e);if(!r)throw new RangeError("No node at position "+e);if(t instanceof j)t.isInSet(r.marks)&&this.step(new Nt(e,t));else{let i=r.marks,s,o=[];for(;s=t.isInSet(i);)o.push(new Nt(e,s)),i=s.removeFromSet(i);for(let l=o.length-1;l>=0;l--)this.step(o[l])}return this}split(e,t=1,r){return Sd(this,e,t,r),this}addMark(e,t,r){return dd(this,e,t,r),this}removeMark(e,t,r){return fd(this,e,t,r),this}clearIncompatible(e,t,r){return as(this,e,t,r),this}}const ui=Object.create(null);let P=class{constructor(e,t,r){this.$anchor=e,this.$head=t,this.ranges=r||[new Dd(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=w.empty){let r=t.content.lastChild,i=null;for(let l=0;l<t.openEnd;l++)i=r,r=r.lastChild;let s=e.steps.length,o=this.ranges;for(let l=0;l<o.length;l++){let{$from:a,$to:c}=o[l],u=e.mapping.slice(s);e.replaceRange(u.map(a.pos),u.map(c.pos),l?w.empty:t),l==0&&So(e,s,(r?r.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let r=e.steps.length,i=this.ranges;for(let s=0;s<i.length;s++){let{$from:o,$to:l}=i[s],a=e.mapping.slice(r),c=a.map(o.pos),u=a.map(l.pos);s?e.deleteRange(c,u):(e.replaceRangeWith(c,u,t),So(e,r,t.isInline?-1:1))}}static findFrom(e,t,r=!1){let i=e.parent.inlineContent?new R(e):Ft(e.node(0),e.parent,e.pos,e.index(),t,r);if(i)return i;for(let s=e.depth-1;s>=0;s--){let o=t<0?Ft(e.node(0),e.node(s),e.before(s+1),e.index(s),t,r):Ft(e.node(0),e.node(s),e.after(s+1),e.index(s)+1,t,r);if(o)return o}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new ye(e.node(0))}static atStart(e){return Ft(e,e,0,0,1)||new ye(e)}static atEnd(e){return Ft(e,e,e.content.size,e.childCount,-1)||new ye(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let r=ui[t.type];if(!r)throw new RangeError(`No selection type ${t.type} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in ui)throw new RangeError("Duplicate use of selection JSON ID "+e);return ui[e]=t,t.prototype.jsonID=e,t}getBookmark(){return R.between(this.$anchor,this.$head).getBookmark()}};P.prototype.visible=!0;class Dd{constructor(e,t){this.$from=e,this.$to=t}}let ko=!1;function xo(n){!ko&&!n.parent.inlineContent&&(ko=!0)}class R extends P{constructor(e,t=e){xo(e),xo(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let r=e.resolve(t.map(this.head));if(!r.parent.inlineContent)return P.near(r);let i=e.resolve(t.map(this.anchor));return new R(i.parent.inlineContent?i:r,r)}replace(e,t=w.empty){if(super.replace(e,t),t==w.empty){let r=this.$from.marksAcross(this.$to);r&&e.ensureMarks(r)}}eq(e){return e instanceof R&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new jr(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new R(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,r=t){let i=e.resolve(t);return new this(i,r==t?i:e.resolve(r))}static between(e,t,r){let i=e.pos-t.pos;if((!r||i)&&(r=i>=0?1:-1),!t.parent.inlineContent){let s=P.findFrom(t,r,!0)||P.findFrom(t,-r,!0);if(s)t=s.$head;else return P.near(t,r)}return e.parent.inlineContent||(i==0?e=t:(e=(P.findFrom(e,-r,!0)||P.findFrom(e,r,!0)).$anchor,e.pos<t.pos!=i<0&&(e=t))),new R(e,t)}}P.jsonID("text",R);class jr{constructor(e,t){this.anchor=e,this.head=t}map(e){return new jr(e.map(this.anchor),e.map(this.head))}resolve(e){return R.between(e.resolve(this.anchor),e.resolve(this.head))}}class A extends P{constructor(e){let t=e.nodeAfter,r=e.node(0).resolve(e.pos+t.nodeSize);super(e,r),this.node=t}map(e,t){let{deleted:r,pos:i}=t.mapResult(this.anchor),s=e.resolve(i);return r?P.near(s):new A(s)}content(){return new w(S.from(this.node),0,0)}eq(e){return e instanceof A&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new us(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new A(e.resolve(t.anchor))}static create(e,t){return new A(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}}A.prototype.visible=!1;P.jsonID("node",A);class us{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:r}=e.mapResult(this.anchor);return t?new jr(r,r):new us(r)}resolve(e){let t=e.resolve(this.anchor),r=t.nodeAfter;return r&&A.isSelectable(r)?new A(t):P.near(t)}}class ye extends P{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=w.empty){if(t==w.empty){e.delete(0,e.doc.content.size);let r=P.atStart(e.doc);r.eq(e.selection)||e.setSelection(r)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new ye(e)}map(e){return new ye(e)}eq(e){return e instanceof ye}getBookmark(){return Id}}P.jsonID("all",ye);const Id={map(){return this},resolve(n){return new ye(n)}};function Ft(n,e,t,r,i,s=!1){if(e.inlineContent)return R.create(n,t);for(let o=r-(i>0?0:1);i>0?o<e.childCount:o>=0;o+=i){let l=e.child(o);if(l.isAtom){if(!s&&A.isSelectable(l))return A.create(n,t-(i<0?l.nodeSize:0))}else{let a=Ft(n,l,t+i,i<0?l.childCount:0,i,s);if(a)return a}t+=l.nodeSize*i}return null}function So(n,e,t){let r=n.steps.length-1;if(r<e)return;let i=n.steps[r];if(!(i instanceof te||i instanceof ne))return;let s=n.mapping.maps[r],o;s.forEach((l,a,c,u)=>{o==null&&(o=u)}),n.setSelection(P.near(n.doc.resolve(o),t))}const vo=1,zn=2,Co=4;class Rd extends la{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|vo)&~zn,this.storedMarks=null,this}get selectionSet(){return(this.updated&vo)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=zn,this}ensureMarks(e){return j.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&zn)>0}addStep(e,t){super.addStep(e,t),this.updated=this.updated&~zn,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let r=this.selection;return t&&(e=e.mark(this.storedMarks||(r.empty?r.$from.marks():r.$from.marksAcross(r.$to)||j.none))),r.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,r){let i=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(i.text(e),!0):this.deleteSelection();{if(r==null&&(r=t),r=r==null?t:r,!e)return this.deleteRange(t,r);let s=this.storedMarks;if(!s){let o=this.doc.resolve(t);s=r==t?o.marks():o.marksAcross(this.doc.resolve(r))}return this.replaceRangeWith(t,r,i.text(e,s)),this.selection.empty||this.setSelection(P.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=Co,this}get scrolledIntoView(){return(this.updated&Co)>0}}function Mo(n,e){return!e||!n?n:n.bind(e)}class rn{constructor(e,t,r){this.name=e,this.init=Mo(t.init,r),this.apply=Mo(t.apply,r)}}const Pd=[new rn("doc",{init(n){return n.doc||n.schema.topNodeType.createAndFill()},apply(n){return n.doc}}),new rn("selection",{init(n,e){return n.selection||P.atStart(e.doc)},apply(n){return n.selection}}),new rn("storedMarks",{init(n){return n.storedMarks||null},apply(n,e,t,r){return r.selection.$cursor?n.storedMarks:null}}),new rn("scrollToSelection",{init(){return 0},apply(n,e){return n.scrolledIntoView?e+1:e}})];class di{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=Pd.slice(),t&&t.forEach(r=>{if(this.pluginsByKey[r.key])throw new RangeError("Adding different instances of a keyed plugin ("+r.key+")");this.plugins.push(r),this.pluginsByKey[r.key]=r,r.spec.state&&this.fields.push(new rn(r.key,r.spec.state,r))})}}class Vt{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let r=0;r<this.config.plugins.length;r++)if(r!=t){let i=this.config.plugins[r];if(i.spec.filterTransaction&&!i.spec.filterTransaction.call(i,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],r=this.applyInner(e),i=null;for(;;){let s=!1;for(let o=0;o<this.config.plugins.length;o++){let l=this.config.plugins[o];if(l.spec.appendTransaction){let a=i?i[o].n:0,c=i?i[o].state:this,u=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,r);if(u&&r.filterTransaction(u,o)){if(u.setMeta("appendedTransaction",e),!i){i=[];for(let d=0;d<this.config.plugins.length;d++)i.push(d<o?{state:r,n:t.length}:{state:this,n:0})}t.push(u),r=r.applyInner(u),s=!0}i&&(i[o]={state:r,n:t.length})}}if(!s)return{state:r,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new Vt(this.config),r=this.config.fields;for(let i=0;i<r.length;i++){let s=r[i];t[s.name]=s.apply(e,this[s.name],this,t)}return t}get tr(){return new Rd(this)}static create(e){let t=new di(e.doc?e.doc.type.schema:e.schema,e.plugins),r=new Vt(t);for(let i=0;i<t.fields.length;i++)r[t.fields[i].name]=t.fields[i].init(e,r);return r}reconfigure(e){let t=new di(this.schema,e.plugins),r=t.fields,i=new Vt(t);for(let s=0;s<r.length;s++){let o=r[s].name;i[o]=this.hasOwnProperty(o)?this[o]:r[s].init(e,i)}return i}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(r=>r.toJSON())),e&&typeof e=="object")for(let r in e){if(r=="doc"||r=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let i=e[r],s=i.spec.state;s&&s.toJSON&&(t[r]=s.toJSON.call(i,this[i.key]))}return t}static fromJSON(e,t,r){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let i=new di(e.schema,e.plugins),s=new Vt(i);return i.fields.forEach(o=>{if(o.name=="doc")s.doc=Ne.fromJSON(e.schema,t.doc);else if(o.name=="selection")s.selection=P.fromJSON(s.doc,t.selection);else if(o.name=="storedMarks")t.storedMarks&&(s.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(r)for(let l in r){let a=r[l],c=a.spec.state;if(a.key==o.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l)){s[o.name]=c.fromJSON.call(a,e,t[l],s);return}}s[o.name]=o.init(e,s)}}),s}}function aa(n,e,t){for(let r in n){let i=n[r];i instanceof Function?i=i.bind(e):r=="handleDOMEvents"&&(i=aa(i,e,{})),t[r]=i}return t}class Q{constructor(e){this.spec=e,this.props={},e.props&&aa(e.props,this,this.props),this.key=e.key?e.key.key:ca("plugin")}getState(e){return e[this.key]}}const fi=Object.create(null);function ca(n){return n in fi?n+"$"+ ++fi[n]:(fi[n]=0,n+"$")}class ie{constructor(e="key"){this.key=ca(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const oe=function(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e},Ut=function(n){let e=n.assignedSlot||n.parentNode;return e&&e.nodeType==11?e.host:e};let zi=null;const Ve=function(n,e,t){let r=zi||(zi=document.createRange());return r.setEnd(n,t==null?n.nodeValue.length:t),r.setStart(n,e||0),r},Ld=function(){zi=null},Dt=function(n,e,t,r){return t&&(wo(n,e,t,r,-1)||wo(n,e,t,r,1))},Bd=/^(img|br|input|textarea|hr)$/i;function wo(n,e,t,r,i){for(var s;;){if(n==t&&e==r)return!0;if(e==(i<0?0:we(n))){let o=n.parentNode;if(!o||o.nodeType!=1||An(n)||Bd.test(n.nodeName)||n.contentEditable=="false")return!1;e=oe(n)+(i<0?0:1),n=o}else if(n.nodeType==1){let o=n.childNodes[e+(i<0?-1:0)];if(o.nodeType==1&&o.contentEditable=="false")if(!((s=o.pmViewDesc)===null||s===void 0)&&s.ignoreForSelection)e+=i;else return!1;else n=o,e=i<0?we(n):0}else return!1}}function we(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function zd(n,e){for(;;){if(n.nodeType==3&&e)return n;if(n.nodeType==1&&e>0){if(n.contentEditable=="false")return null;n=n.childNodes[e-1],e=we(n)}else if(n.parentNode&&!An(n))e=oe(n),n=n.parentNode;else return null}}function Fd(n,e){for(;;){if(n.nodeType==3&&e<n.nodeValue.length)return n;if(n.nodeType==1&&e<n.childNodes.length){if(n.contentEditable=="false")return null;n=n.childNodes[e],e=0}else if(n.parentNode&&!An(n))e=oe(n)+1,n=n.parentNode;else return null}}function $d(n,e,t){for(let r=e==0,i=e==we(n);r||i;){if(n==t)return!0;let s=oe(n);if(n=n.parentNode,!n)return!1;r=r&&s==0,i=i&&s==we(n)}}function An(n){let e;for(let t=n;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==n||e.contentDOM==n)}const Wr=function(n){return n.focusNode&&Dt(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)};function kt(n,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=n,t.key=t.code=e,t}function Hd(n){let e=n.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function _d(n,e,t){if(n.caretPositionFromPoint)try{let r=n.caretPositionFromPoint(e,t);if(r)return{node:r.offsetNode,offset:Math.min(we(r.offsetNode),r.offset)}}catch(r){}if(n.caretRangeFromPoint){let r=n.caretRangeFromPoint(e,t);if(r)return{node:r.startContainer,offset:Math.min(we(r.startContainer),r.startOffset)}}}const Le=typeof navigator!="undefined"?navigator:null,To=typeof document!="undefined"?document:null,mt=Le&&Le.userAgent||"",Fi=/Edge\/(\d+)/.exec(mt),ua=/MSIE \d/.exec(mt),$i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(mt),be=!!(ua||$i||Fi),at=ua?document.documentMode:$i?+$i[1]:Fi?+Fi[1]:0,De=!be&&/gecko\/(\d+)/i.test(mt);De&&+(/Firefox\/(\d+)/.exec(mt)||[0,0])[1];const Hi=!be&&/Chrome\/(\d+)/.exec(mt),ce=!!Hi,da=Hi?+Hi[1]:0,he=!be&&!!Le&&/Apple Computer/.test(Le.vendor),qt=he&&(/Mobile\/\w+/.test(mt)||!!Le&&Le.maxTouchPoints>2),Me=qt||(Le?/Mac/.test(Le.platform):!1),Vd=Le?/Win/.test(Le.platform):!1,je=/Android \d/.test(mt),On=!!To&&"webkitFontSmoothing"in To.documentElement.style,jd=On?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function Wd(n){let e=n.defaultView&&n.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:n.documentElement.clientWidth,top:0,bottom:n.documentElement.clientHeight}}function Fe(n,e){return typeof n=="number"?n:n[e]}function Kd(n){let e=n.getBoundingClientRect(),t=e.width/n.offsetWidth||1,r=e.height/n.offsetHeight||1;return{left:e.left,right:e.left+n.clientWidth*t,top:e.top,bottom:e.top+n.clientHeight*r}}function Eo(n,e,t){let r=n.someProp("scrollThreshold")||0,i=n.someProp("scrollMargin")||5,s=n.dom.ownerDocument;for(let o=t||n.dom;o;){if(o.nodeType!=1){o=Ut(o);continue}let l=o,a=l==s.body,c=a?Wd(s):Kd(l),u=0,d=0;if(e.top<c.top+Fe(r,"top")?d=-(c.top-e.top+Fe(i,"top")):e.bottom>c.bottom-Fe(r,"bottom")&&(d=e.bottom-e.top>c.bottom-c.top?e.top+Fe(i,"top")-c.top:e.bottom-c.bottom+Fe(i,"bottom")),e.left<c.left+Fe(r,"left")?u=-(c.left-e.left+Fe(i,"left")):e.right>c.right-Fe(r,"right")&&(u=e.right-c.right+Fe(i,"right")),u||d)if(a)s.defaultView.scrollBy(u,d);else{let h=l.scrollLeft,p=l.scrollTop;d&&(l.scrollTop+=d),u&&(l.scrollLeft+=u);let m=l.scrollLeft-h,g=l.scrollTop-p;e={left:e.left-m,top:e.top-g,right:e.right-m,bottom:e.bottom-g}}let f=a?"fixed":getComputedStyle(o).position;if(/^(fixed|sticky)$/.test(f))break;o=f=="absolute"?o.offsetParent:Ut(o)}}function Ud(n){let e=n.dom.getBoundingClientRect(),t=Math.max(0,e.top),r,i;for(let s=(e.left+e.right)/2,o=t+1;o<Math.min(innerHeight,e.bottom);o+=5){let l=n.root.elementFromPoint(s,o);if(!l||l==n.dom||!n.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=t-20){r=l,i=a.top;break}}return{refDOM:r,refTop:i,stack:fa(n.dom)}}function fa(n){let e=[],t=n.ownerDocument;for(let r=n;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),n!=t);r=Ut(r));return e}function qd({refDOM:n,refTop:e,stack:t}){let r=n?n.getBoundingClientRect().top:0;ha(t,r==0?0:r-e)}function ha(n,e){for(let t=0;t<n.length;t++){let{dom:r,top:i,left:s}=n[t];r.scrollTop!=i+e&&(r.scrollTop=i+e),r.scrollLeft!=s&&(r.scrollLeft=s)}}let Bt=null;function Jd(n){if(n.setActive)return n.setActive();if(Bt)return n.focus(Bt);let e=fa(n);n.focus(Bt==null?{get preventScroll(){return Bt={preventScroll:!0},!0}}:void 0),Bt||(Bt=!1,ha(e,0))}function pa(n,e){let t,r=2e8,i,s=0,o=e.top,l=e.top,a,c;for(let u=n.firstChild,d=0;u;u=u.nextSibling,d++){let f;if(u.nodeType==1)f=u.getClientRects();else if(u.nodeType==3)f=Ve(u).getClientRects();else continue;for(let h=0;h<f.length;h++){let p=f[h];if(p.top<=o&&p.bottom>=l){o=Math.max(p.bottom,o),l=Math.min(p.top,l);let m=p.left>e.left?p.left-e.left:p.right<e.left?e.left-p.right:0;if(m<r){t=u,r=m,i=m&&t.nodeType==3?{left:p.right<e.left?p.right:p.left,top:e.top}:e,u.nodeType==1&&m&&(s=d+(e.left>=(p.left+p.right)/2?1:0));continue}}else p.top>e.top&&!a&&p.left<=e.left&&p.right>=e.left&&(a=u,c={left:Math.max(p.left,Math.min(p.right,e.left)),top:p.top});!t&&(e.left>=p.right&&e.top>=p.top||e.left>=p.left&&e.top>=p.bottom)&&(s=d+1)}}return!t&&a&&(t=a,i=c,r=0),t&&t.nodeType==3?Gd(t,i):!t||r&&t.nodeType==1?{node:n,offset:s}:pa(t,i)}function Gd(n,e){let t=n.nodeValue.length,r=document.createRange();for(let i=0;i<t;i++){r.setEnd(n,i+1),r.setStart(n,i);let s=Xe(r,1);if(s.top!=s.bottom&&ds(e,s))return{node:n,offset:i+(e.left>=(s.left+s.right)/2?1:0)}}return{node:n,offset:0}}function ds(n,e){return n.left>=e.left-1&&n.left<=e.right+1&&n.top>=e.top-1&&n.top<=e.bottom+1}function Yd(n,e){let t=n.parentNode;return t&&/^li$/i.test(t.nodeName)&&e.left<n.getBoundingClientRect().left?t:n}function Xd(n,e,t){let{node:r,offset:i}=pa(e,t),s=-1;if(r.nodeType==1&&!r.firstChild){let o=r.getBoundingClientRect();s=o.left!=o.right&&t.left>(o.left+o.right)/2?1:-1}return n.docView.posFromDOM(r,i,s)}function Qd(n,e,t,r){let i=-1;for(let s=e,o=!1;s!=n.dom;){let l=n.docView.nearestDesc(s,!0),a;if(!l)return null;if(l.dom.nodeType==1&&(l.node.isBlock&&l.parent||!l.contentDOM)&&((a=l.dom.getBoundingClientRect()).width||a.height)&&(l.node.isBlock&&l.parent&&!/^T(R|BODY|HEAD|FOOT)$/.test(l.dom.nodeName)&&(!o&&a.left>r.left||a.top>r.top?i=l.posBefore:(!o&&a.right<r.left||a.bottom<r.top)&&(i=l.posAfter),o=!0),!l.contentDOM&&i<0&&!l.node.isText))return(l.node.isBlock?r.top<(a.top+a.bottom)/2:r.left<(a.left+a.right)/2)?l.posBefore:l.posAfter;s=l.dom.parentNode}return i>-1?i:n.docView.posFromDOM(e,t,-1)}function ma(n,e,t){let r=n.childNodes.length;if(r&&t.top<t.bottom)for(let i=Math.max(0,Math.min(r-1,Math.floor(r*(e.top-t.top)/(t.bottom-t.top))-2)),s=i;;){let o=n.childNodes[s];if(o.nodeType==1){let l=o.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(ds(e,c))return ma(o,e,c)}}if((s=(s+1)%r)==i)break}return n}function Zd(n,e){let t=n.dom.ownerDocument,r,i=0,s=_d(t,e.left,e.top);s&&({node:r,offset:i}=s);let o=(n.root.elementFromPoint?n.root:t).elementFromPoint(e.left,e.top),l;if(!o||!n.dom.contains(o.nodeType!=1?o.parentNode:o)){let c=n.dom.getBoundingClientRect();if(!ds(e,c)||(o=ma(n.dom,e,c),!o))return null}if(he)for(let c=o;r&&c;c=Ut(c))c.draggable&&(r=void 0);if(o=Yd(o,e),r){if(De&&r.nodeType==1&&(i=Math.min(i,r.childNodes.length),i<r.childNodes.length)){let u=r.childNodes[i],d;u.nodeName=="IMG"&&(d=u.getBoundingClientRect()).right<=e.left&&d.bottom>e.top&&i++}let c;On&&i&&r.nodeType==1&&(c=r.childNodes[i-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&i--,r==n.dom&&i==r.childNodes.length-1&&r.lastChild.nodeType==1&&e.top>r.lastChild.getBoundingClientRect().bottom?l=n.state.doc.content.size:(i==0||r.nodeType!=1||r.childNodes[i-1].nodeName!="BR")&&(l=Qd(n,r,i,e))}l==null&&(l=Xd(n,o,e));let a=n.docView.nearestDesc(o,!0);return{pos:l,inside:a?a.posAtStart-a.border:-1}}function Ao(n){return n.top<n.bottom||n.left<n.right}function Xe(n,e){let t=n.getClientRects();if(t.length){let r=t[e<0?0:t.length-1];if(Ao(r))return r}return Array.prototype.find.call(t,Ao)||n.getBoundingClientRect()}const ef=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function ga(n,e,t){let{node:r,offset:i,atom:s}=n.docView.domFromPos(e,t<0?-1:1),o=On||De;if(r.nodeType==3)if(o&&(ef.test(r.nodeValue)||(t<0?!i:i==r.nodeValue.length))){let a=Xe(Ve(r,i,i),t);if(De&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let c=Xe(Ve(r,i-1,i-1),-1);if(c.top==a.top){let u=Xe(Ve(r,i,i+1),-1);if(u.top!=a.top)return Zt(u,u.left<c.left)}}return a}else{let a=i,c=i,u=t<0?1:-1;return t<0&&!i?(c++,u=-1):t>=0&&i==r.nodeValue.length?(a--,u=1):t<0?a--:c++,Zt(Xe(Ve(r,a,c),u),u<0)}if(!n.state.doc.resolve(e-(s||0)).parent.inlineContent){if(s==null&&i&&(t<0||i==we(r))){let a=r.childNodes[i-1];if(a.nodeType==1)return hi(a.getBoundingClientRect(),!1)}if(s==null&&i<we(r)){let a=r.childNodes[i];if(a.nodeType==1)return hi(a.getBoundingClientRect(),!0)}return hi(r.getBoundingClientRect(),t>=0)}if(s==null&&i&&(t<0||i==we(r))){let a=r.childNodes[i-1],c=a.nodeType==3?Ve(a,we(a)-(o?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return Zt(Xe(c,1),!1)}if(s==null&&i<we(r)){let a=r.childNodes[i];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?Ve(a,0,o?0:1):a.nodeType==1?a:null:null;if(c)return Zt(Xe(c,-1),!0)}return Zt(Xe(r.nodeType==3?Ve(r):r,-t),t>=0)}function Zt(n,e){if(n.width==0)return n;let t=e?n.left:n.right;return{top:n.top,bottom:n.bottom,left:t,right:t}}function hi(n,e){if(n.height==0)return n;let t=e?n.top:n.bottom;return{top:t,bottom:t,left:n.left,right:n.right}}function ya(n,e,t){let r=n.state,i=n.root.activeElement;r!=e&&n.updateState(e),i!=n.dom&&n.focus();try{return t()}finally{r!=e&&n.updateState(r),i!=n.dom&&i&&i.focus()}}function tf(n,e,t){let r=e.selection,i=t=="up"?r.$from:r.$to;return ya(n,e,()=>{let{node:s}=n.docView.domFromPos(i.pos,t=="up"?-1:1);for(;;){let l=n.docView.nearestDesc(s,!0);if(!l)break;if(l.node.isBlock){s=l.contentDOM||l.dom;break}s=l.dom.parentNode}let o=ga(n,i.pos,1);for(let l=s.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=Ve(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let u=a[c];if(u.bottom>u.top+1&&(t=="up"?o.top-u.top>(u.bottom-o.top)*2:u.bottom-o.bottom>(o.bottom-u.top)*2))return!1}}return!0})}const nf=/[\u0590-\u08ac]/;function rf(n,e,t){let{$head:r}=e.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,s=!i,o=i==r.parent.content.size,l=n.domSelection();return l?!nf.test(r.parent.textContent)||!l.modify?t=="left"||t=="backward"?s:o:ya(n,e,()=>{let{focusNode:a,focusOffset:c,anchorNode:u,anchorOffset:d}=n.domSelectionRange(),f=l.caretBidiLevel;l.modify("move",t,"character");let h=r.depth?n.docView.domAfterPos(r.before()):n.dom,{focusNode:p,focusOffset:m}=n.domSelectionRange(),g=p&&!h.contains(p.nodeType==1?p:p.parentNode)||a==p&&c==m;try{l.collapse(u,d),a&&(a!=u||c!=d)&&l.extend&&l.extend(a,c)}catch(b){}return f!=null&&(l.caretBidiLevel=f),g}):r.pos==r.start()||r.pos==r.end()}let Oo=null,No=null,Do=!1;function sf(n,e,t){return Oo==e&&No==t?Do:(Oo=e,No=t,Do=t=="up"||t=="down"?tf(n,e,t):rf(n,e,t))}const Te=0,Io=1,xt=2,Be=3;class Nn{constructor(e,t,r,i){this.parent=e,this.children=t,this.dom=r,this.contentDOM=i,this.dirty=Te,r.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,r){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,r=this.posAtStart;;t++){let i=this.children[t];if(i==e)return r;r+=i.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,r){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode))if(r<0){let s,o;if(e==this.contentDOM)s=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.previousSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.previousSibling;return s?this.posBeforeChild(o)+o.size:this.posAtStart}else{let s,o;if(e==this.contentDOM)s=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.nextSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.nextSibling;return s?this.posBeforeChild(o):this.posAtEnd}let i;if(e==this.dom&&this.contentDOM)i=t>oe(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))i=e.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(t==0)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!1;break}if(s.previousSibling)break}if(i==null&&t==e.childNodes.length)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!0;break}if(s.nextSibling)break}}return(i==null?r>0:i)?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let r=!0,i=e;i;i=i.parentNode){let s=this.getDesc(i),o;if(s&&(!t||s.node))if(r&&(o=s.nodeDOM)&&!(o.nodeType==1?o.contains(e.nodeType==1?e:e.parentNode):o==e))r=!1;else return s}}getDesc(e){let t=e.pmViewDesc;for(let r=t;r;r=r.parent)if(r==this)return t}posFromDOM(e,t,r){for(let i=e;i;i=i.parentNode){let s=this.getDesc(i);if(s)return s.localPosFromDOM(e,t,r)}return-1}descAt(e){for(let t=0,r=0;t<this.children.length;t++){let i=this.children[t],s=r+i.size;if(r==e&&s!=r){for(;!i.border&&i.children.length;)for(let o=0;o<i.children.length;o++){let l=i.children[o];if(l.size){i=l;break}}return i}if(e<s)return i.descAt(e-r-i.border);r=s}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let r=0,i=0;for(let s=0;r<this.children.length;r++){let o=this.children[r],l=s+o.size;if(l>e||o instanceof ka){i=e-s;break}s=l}if(i)return this.children[r].domFromPos(i-this.children[r].border,t);for(let s;r&&!(s=this.children[r-1]).size&&s instanceof ba&&s.side>=0;r--);if(t<=0){let s,o=!0;for(;s=r?this.children[r-1]:null,!(!s||s.dom.parentNode==this.contentDOM);r--,o=!1);return s&&t&&o&&!s.border&&!s.domAtom?s.domFromPos(s.size,t):{node:this.contentDOM,offset:s?oe(s.dom)+1:0}}else{let s,o=!0;for(;s=r<this.children.length?this.children[r]:null,!(!s||s.dom.parentNode==this.contentDOM);r++,o=!1);return s&&o&&!s.border&&!s.domAtom?s.domFromPos(0,t):{node:this.contentDOM,offset:s?oe(s.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,r=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let i=-1,s=-1;for(let o=r,l=0;;l++){let a=this.children[l],c=o+a.size;if(i==-1&&e<=c){let u=o+a.border;if(e>=u&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,u);e=o;for(let d=l;d>0;d--){let f=this.children[d-1];if(f.size&&f.dom.parentNode==this.contentDOM&&!f.emptyChildAt(1)){i=oe(f.dom)+1;break}e-=f.size}i==-1&&(i=0)}if(i>-1&&(c>t||l==this.children.length-1)){t=c;for(let u=l+1;u<this.children.length;u++){let d=this.children[u];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(-1)){s=oe(d.dom);break}t+=d.size}s==-1&&(s=this.contentDOM.childNodes.length);break}o=c}return{node:this.contentDOM,from:e,to:t,fromOffset:i,toOffset:s}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:r}=this.domFromPos(e,0);if(t.nodeType!=1||r==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[r]}setSelection(e,t,r,i=!1){let s=Math.min(e,t),o=Math.max(e,t);for(let h=0,p=0;h<this.children.length;h++){let m=this.children[h],g=p+m.size;if(s>p&&o<g)return m.setSelection(e-p-m.border,t-p-m.border,r,i);p=g}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=r.root.getSelection(),u=r.domSelectionRange(),d=!1;if((De||he)&&e==t){let{node:h,offset:p}=l;if(h.nodeType==3){if(d=!!(p&&h.nodeValue[p-1]==`
`),d&&p==h.nodeValue.length)for(let m=h,g;m;m=m.parentNode){if(g=m.nextSibling){g.nodeName=="BR"&&(l=a={node:g.parentNode,offset:oe(g)+1});break}let b=m.pmViewDesc;if(b&&b.node&&b.node.isBlock)break}}else{let m=h.childNodes[p-1];d=m&&(m.nodeName=="BR"||m.contentEditable=="false")}}if(De&&u.focusNode&&u.focusNode!=a.node&&u.focusNode.nodeType==1){let h=u.focusNode.childNodes[u.focusOffset];h&&h.contentEditable=="false"&&(i=!0)}if(!(i||d&&he)&&Dt(l.node,l.offset,u.anchorNode,u.anchorOffset)&&Dt(a.node,a.offset,u.focusNode,u.focusOffset))return;let f=!1;if((c.extend||e==t)&&!d){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),f=!0}catch(h){}}if(!f){if(e>t){let p=l;l=a,a=p}let h=document.createRange();h.setEnd(a.node,a.offset),h.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(h)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let r=0,i=0;i<this.children.length;i++){let s=this.children[i],o=r+s.size;if(r==o?e<=o&&t>=r:e<o&&t>r){let l=r+s.border,a=o-s.border;if(e>=l&&t<=a){this.dirty=e==r||t==o?xt:Io,e==l&&t==a&&(s.contentLost||s.dom.parentNode!=this.contentDOM)?s.dirty=Be:s.markDirty(e-l,t-l);return}else s.dirty=s.dom==s.contentDOM&&s.dom.parentNode==this.contentDOM&&!s.children.length?xt:Be}r=o}this.dirty=xt}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let r=e==1?xt:Io;t.dirty<r&&(t.dirty=r)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(e){return!1}}class ba extends Nn{constructor(e,t,r,i){let s,o=t.type.toDOM;if(typeof o=="function"&&(o=o(r,()=>{if(!s)return i;if(s.parent)return s.parent.posBeforeChild(s)})),!t.type.spec.raw){if(o.nodeType!=1){let l=document.createElement("span");l.appendChild(o),o=l}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(e,[],o,null),this.widget=t,this.widget=t,s=this}matchesWidget(e){return this.dirty==Te&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return t?t(e):!1}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}}class of extends Nn{constructor(e,t,r,i){super(e,[],t,null),this.textDOM=r,this.text=i}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}}class It extends Nn{constructor(e,t,r,i,s){super(e,[],r,i),this.mark=t,this.spec=s}static create(e,t,r,i){let s=i.nodeViews[t.type.name],o=s&&s(t,i,r);return(!o||!o.dom)&&(o=Pt.renderSpec(document,t.type.spec.toDOM(t,r),null,t.attrs)),new It(e,t,o.dom,o.contentDOM||o.dom,o)}parseRule(){return this.dirty&Be||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=Be&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=Te){let r=this.parent;for(;!r.node;)r=r.parent;r.dirty<this.dirty&&(r.dirty=this.dirty),this.dirty=Te}}slice(e,t,r){let i=It.create(this.parent,this.mark,!0,r),s=this.children,o=this.size;t<o&&(s=Vi(s,t,o,r)),e>0&&(s=Vi(s,0,e,r));for(let l=0;l<s.length;l++)s[l].parent=i;return i.children=s,i}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class ct extends Nn{constructor(e,t,r,i,s,o,l,a,c){super(e,[],s,o),this.node=t,this.outerDeco=r,this.innerDeco=i,this.nodeDOM=l}static create(e,t,r,i,s,o){let l=s.nodeViews[t.type.name],a,c=l&&l(t,s,()=>{if(!a)return o;if(a.parent)return a.parent.posBeforeChild(a)},r,i),u=c&&c.dom,d=c&&c.contentDOM;if(t.isText){if(!u)u=document.createTextNode(t.text);else if(u.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else u||({dom:u,contentDOM:d}=Pt.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs));!d&&!t.isText&&u.nodeName!="BR"&&(u.hasAttribute("contenteditable")||(u.contentEditable="false"),t.type.spec.draggable&&(u.draggable=!0));let f=u;return u=va(u,r,t),c?a=new lf(e,t,r,i,u,d||null,f,c,s,o+1):t.isText?new Kr(e,t,r,i,u,f,s):new ct(e,t,r,i,u,d||null,f,s,o+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),!this.contentDOM)e.getContent=()=>this.node.content;else if(!this.contentLost)e.contentElement=this.contentDOM;else{for(let t=this.children.length-1;t>=0;t--){let r=this.children[t];if(this.dom.contains(r.dom.parentNode)){e.contentElement=r.dom.parentNode;break}}e.contentElement||(e.getContent=()=>S.empty)}return e}matchesNode(e,t,r){return this.dirty==Te&&e.eq(this.node)&&nr(t,this.outerDeco)&&r.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let r=this.node.inlineContent,i=t,s=e.composing?this.localCompositionInfo(e,t):null,o=s&&s.pos>-1?s:null,l=s&&s.pos<0,a=new cf(this,o&&o.node,e);ff(this.node,this.innerDeco,(c,u,d)=>{c.spec.marks?a.syncToMarks(c.spec.marks,r,e):c.type.side>=0&&!d&&a.syncToMarks(u==this.node.childCount?j.none:this.node.child(u).marks,r,e),a.placeWidget(c,e,i)},(c,u,d,f)=>{a.syncToMarks(c.marks,r,e);let h;a.findNodeMatch(c,u,d,f)||l&&e.state.selection.from>i&&e.state.selection.to<i+c.nodeSize&&(h=a.findIndexWithChild(s.node))>-1&&a.updateNodeAt(c,u,d,h,e)||a.updateNextNode(c,u,d,e,f,i)||a.addNode(c,u,d,e,i),i+=c.nodeSize}),a.syncToMarks([],r,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==xt)&&(o&&this.protectLocalComposition(e,o),xa(this.contentDOM,this.children,e),qt&&hf(this.dom))}localCompositionInfo(e,t){let{from:r,to:i}=e.state.selection;if(!(e.state.selection instanceof R)||r<t||i>t+this.node.content.size)return null;let s=e.input.compositionNode;if(!s||!this.dom.contains(s.parentNode))return null;if(this.node.inlineContent){let o=s.nodeValue,l=pf(this.node.content,o,r-t,i-t);return l<0?null:{node:s,pos:l,text:o}}else return{node:s,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:r,text:i}){if(this.getDesc(t))return;let s=t;for(;s.parentNode!=this.contentDOM;s=s.parentNode){for(;s.previousSibling;)s.parentNode.removeChild(s.previousSibling);for(;s.nextSibling;)s.parentNode.removeChild(s.nextSibling);s.pmViewDesc&&(s.pmViewDesc=void 0)}let o=new of(this,s,t,i);e.input.compositionNodes.push(o),this.children=Vi(this.children,r,r+i.length,e,o)}update(e,t,r,i){return this.dirty==Be||!e.sameMarkup(this.node)?!1:(this.updateInner(e,t,r,i),!0)}updateInner(e,t,r,i){this.updateOuterDeco(t),this.node=e,this.innerDeco=r,this.contentDOM&&this.updateChildren(i,this.posAtStart),this.dirty=Te}updateOuterDeco(e){if(nr(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,r=this.dom;this.dom=Sa(this.dom,this.nodeDOM,_i(this.outerDeco,this.node,t),_i(e,this.node,t)),this.dom!=r&&(r.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function Ro(n,e,t,r,i){va(r,e,n);let s=new ct(void 0,n,e,t,r,r,r,i,0);return s.contentDOM&&s.updateChildren(i,0),s}class Kr extends ct{constructor(e,t,r,i,s,o,l){super(e,t,r,i,s,null,o,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,r,i){return this.dirty==Be||this.dirty!=Te&&!this.inParent()||!e.sameMarkup(this.node)?!1:(this.updateOuterDeco(t),(this.dirty!=Te||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,i.trackWrites==this.nodeDOM&&(i.trackWrites=null)),this.node=e,this.dirty=Te,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,r){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,r)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,r){let i=this.node.cut(e,t),s=document.createTextNode(i.text);return new Kr(this.parent,i,this.outerDeco,this.innerDeco,s,s,r)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(e==0||t==this.nodeDOM.nodeValue.length)&&(this.dirty=Be)}get domAtom(){return!1}isText(e){return this.node.text==e}}class ka extends Nn{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==Te&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class lf extends ct{constructor(e,t,r,i,s,o,l,a,c,u){super(e,t,r,i,s,o,l,c,u),this.spec=a}update(e,t,r,i){if(this.dirty==Be)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let s=this.spec.update(e,t,r);return s&&this.updateInner(e,t,r,i),s}else return!this.contentDOM&&!e.isLeaf?!1:super.update(e,t,r,i)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,r,i){this.spec.setSelection?this.spec.setSelection(e,t,r.root):super.setSelection(e,t,r,i)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return this.spec.stopEvent?this.spec.stopEvent(e):!1}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function xa(n,e,t){let r=n.firstChild,i=!1;for(let s=0;s<e.length;s++){let o=e[s],l=o.dom;if(l.parentNode==n){for(;l!=r;)r=Po(r),i=!0;r=r.nextSibling}else i=!0,n.insertBefore(l,r);if(o instanceof It){let a=r?r.previousSibling:n.lastChild;xa(o.contentDOM,o.children,t),r=a?a.nextSibling:n.firstChild}}for(;r;)r=Po(r),i=!0;i&&t.trackWrites==n&&(t.trackWrites=null)}const an=function(n){n&&(this.nodeName=n)};an.prototype=Object.create(null);const St=[new an];function _i(n,e,t){if(n.length==0)return St;let r=t?St[0]:new an,i=[r];for(let s=0;s<n.length;s++){let o=n[s].type.attrs;if(o){o.nodeName&&i.push(r=new an(o.nodeName));for(let l in o){let a=o[l];a!=null&&(t&&i.length==1&&i.push(r=new an(e.isInline?"span":"div")),l=="class"?r.class=(r.class?r.class+" ":"")+a:l=="style"?r.style=(r.style?r.style+";":"")+a:l!="nodeName"&&(r[l]=a))}}}return i}function Sa(n,e,t,r){if(t==St&&r==St)return e;let i=e;for(let s=0;s<r.length;s++){let o=r[s],l=t[s];if(s){let a;l&&l.nodeName==o.nodeName&&i!=n&&(a=i.parentNode)&&a.nodeName.toLowerCase()==o.nodeName||(a=document.createElement(o.nodeName),a.pmIsDeco=!0,a.appendChild(i),l=St[0]),i=a}af(i,l||St[0],o)}return i}function af(n,e,t){for(let r in e)r!="class"&&r!="style"&&r!="nodeName"&&!(r in t)&&n.removeAttribute(r);for(let r in t)r!="class"&&r!="style"&&r!="nodeName"&&t[r]!=e[r]&&n.setAttribute(r,t[r]);if(e.class!=t.class){let r=e.class?e.class.split(" ").filter(Boolean):[],i=t.class?t.class.split(" ").filter(Boolean):[];for(let s=0;s<r.length;s++)i.indexOf(r[s])==-1&&n.classList.remove(r[s]);for(let s=0;s<i.length;s++)r.indexOf(i[s])==-1&&n.classList.add(i[s]);n.classList.length==0&&n.removeAttribute("class")}if(e.style!=t.style){if(e.style){let r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,i;for(;i=r.exec(e.style);)n.style.removeProperty(i[1])}t.style&&(n.style.cssText+=t.style)}}function va(n,e,t){return Sa(n,n,St,_i(e,t,n.nodeType!=1))}function nr(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].type.eq(e[t].type))return!1;return!0}function Po(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}class cf{constructor(e,t,r){this.lock=t,this.view=r,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=uf(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let r=e;r<t;r++)this.top.children[r].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,r){let i=0,s=this.stack.length>>1,o=Math.min(s,e.length);for(;i<o&&(i==s-1?this.top:this.stack[i+1<<1]).matchesMark(e[i])&&e[i].type.spec.spanning!==!1;)i++;for(;i<s;)this.destroyRest(),this.top.dirty=Te,this.index=this.stack.pop(),this.top=this.stack.pop(),s--;for(;s<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[s])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=It.create(this.top,e[s],t,r);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,s++}}findNodeMatch(e,t,r,i){let s=-1,o;if(i>=this.preMatch.index&&(o=this.preMatch.matches[i-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,r))s=this.top.children.indexOf(o,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,r)&&!this.preMatch.matched.has(c)){s=l;break}}return s<0?!1:(this.destroyBetween(this.index,s),this.index++,!0)}updateNodeAt(e,t,r,i,s){let o=this.top.children[i];return o.dirty==Be&&o.dom==o.contentDOM&&(o.dirty=xt),o.update(e,t,r,s)?(this.destroyBetween(this.index,i),this.index++,!0):!1}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let r=e.pmViewDesc;if(r){for(let i=this.index;i<this.top.children.length;i++)if(this.top.children[i]==r)return i}return-1}e=t}}updateNextNode(e,t,r,i,s,o){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof ct){let c=this.preMatch.matched.get(a);if(c!=null&&c!=s)return!1;let u=a.dom,d,f=this.isLocked(u)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=Be&&nr(t,a.outerDeco));if(!f&&a.update(e,t,r,i))return this.destroyBetween(this.index,l),a.dom!=u&&(this.changed=!0),this.index++,!0;if(!f&&(d=this.recreateWrapper(a,e,t,r,i,o)))return this.destroyBetween(this.index,l),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=xt,d.updateChildren(i,o+1),d.dirty=Te),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,r,i,s,o){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!nr(r,e.outerDeco)||!i.eq(e.innerDeco))return null;let l=ct.create(this.top,t,r,i,s,o);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,t,r,i,s){let o=ct.create(this.top,e,t,r,i,s);o.contentDOM&&o.updateChildren(i,s+1),this.top.children.splice(this.index++,0,o),this.changed=!0}placeWidget(e,t,r){let i=this.index<this.top.children.length?this.top.children[this.index]:null;if(i&&i.matchesWidget(e)&&(e==i.widget||!i.widget.type.toDOM.parentNode))this.index++;else{let s=new ba(this.top,e,t,r);this.top.children.splice(this.index++,0,s),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof It;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof Kr)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((he||ce)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let r=document.createElement(e);e=="IMG"&&(r.className="ProseMirror-separator",r.alt=""),e=="BR"&&(r.className="ProseMirror-trailingBreak");let i=new ka(this.top,[],r,null);t!=this.top?t.children.push(i):t.children.splice(this.index++,0,i),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}}function uf(n,e){let t=e,r=t.children.length,i=n.childCount,s=new Map,o=[];e:for(;i>0;){let l;for(;;)if(r){let c=t.children[r-1];if(c instanceof It)t=c,r=c.children.length;else{l=c,r--;break}}else{if(t==e)break e;r=t.parent.children.indexOf(t),t=t.parent}let a=l.node;if(a){if(a!=n.child(i-1))break;--i,s.set(l,i),o.push(l)}}return{index:i,matched:s,matches:o.reverse()}}function df(n,e){return n.type.side-e.type.side}function ff(n,e,t,r){let i=e.locals(n),s=0;if(i.length==0){for(let c=0;c<n.childCount;c++){let u=n.child(c);r(u,i,e.forChild(s,u),c),s+=u.nodeSize}return}let o=0,l=[],a=null;for(let c=0;;){let u,d;for(;o<i.length&&i[o].to==s;){let g=i[o++];g.widget&&(u?(d||(d=[u])).push(g):u=g)}if(u)if(d){d.sort(df);for(let g=0;g<d.length;g++)t(d[g],c,!!a)}else t(u,c,!!a);let f,h;if(a)h=-1,f=a,a=null;else if(c<n.childCount)h=c,f=n.child(c++);else break;for(let g=0;g<l.length;g++)l[g].to<=s&&l.splice(g--,1);for(;o<i.length&&i[o].from<=s&&i[o].to>s;)l.push(i[o++]);let p=s+f.nodeSize;if(f.isText){let g=p;o<i.length&&i[o].from<g&&(g=i[o].from);for(let b=0;b<l.length;b++)l[b].to<g&&(g=l[b].to);g<p&&(a=f.cut(g-s),f=f.cut(0,g-s),p=g,h=-1)}else for(;o<i.length&&i[o].to<p;)o++;let m=f.isInline&&!f.isLeaf?l.filter(g=>!g.inline):l.slice();r(f,m,e.forChild(s,f),h),s=p}}function hf(n){if(n.nodeName=="UL"||n.nodeName=="OL"){let e=n.style.cssText;n.style.cssText=e+"; list-style: square !important",window.getComputedStyle(n).listStyle,n.style.cssText=e}}function pf(n,e,t,r){for(let i=0,s=0;i<n.childCount&&s<=r;){let o=n.child(i++),l=s;if(s+=o.nodeSize,!o.isText)continue;let a=o.text;for(;i<n.childCount;){let c=n.child(i++);if(s+=c.nodeSize,!c.isText)break;a+=c.text}if(s>=t){if(s>=r&&a.slice(r-e.length-l,r-l)==e)return r-e.length;let c=l<r?a.lastIndexOf(e,r-l-1):-1;if(c>=0&&c+e.length+l>=t)return l+c;if(t==r&&a.length>=r+e.length-l&&a.slice(r-l,r-l+e.length)==e)return r}}return-1}function Vi(n,e,t,r,i){let s=[];for(let o=0,l=0;o<n.length;o++){let a=n[o],c=l,u=l+=a.size;c>=t||u<=e?s.push(a):(c<e&&s.push(a.slice(0,e-c,r)),i&&(s.push(i),i=void 0),u>t&&s.push(a.slice(t-c,a.size,r)))}return s}function fs(n,e=null){let t=n.domSelectionRange(),r=n.state.doc;if(!t.focusNode)return null;let i=n.docView.nearestDesc(t.focusNode),s=i&&i.size==0,o=n.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(o<0)return null;let l=r.resolve(o),a,c;if(Wr(t)){for(a=o;i&&!i.node;)i=i.parent;let d=i.node;if(i&&d.isAtom&&A.isSelectable(d)&&i.parent&&!(d.isInline&&$d(t.focusNode,t.focusOffset,i.dom))){let f=i.posBefore;c=new A(o==f?l:r.resolve(f))}}else{if(t instanceof n.dom.ownerDocument.defaultView.Selection&&t.rangeCount>1){let d=o,f=o;for(let h=0;h<t.rangeCount;h++){let p=t.getRangeAt(h);d=Math.min(d,n.docView.posFromDOM(p.startContainer,p.startOffset,1)),f=Math.max(f,n.docView.posFromDOM(p.endContainer,p.endOffset,-1))}if(d<0)return null;[a,o]=f==n.state.selection.anchor?[f,d]:[d,f],l=r.resolve(o)}else a=n.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(a<0)return null}let u=r.resolve(a);if(!c){let d=e=="pointer"||n.state.selection.head<l.pos&&!s?1:-1;c=hs(n,u,l,d)}return c}function Ca(n){return n.editable?n.hasFocus():wa(n)&&document.activeElement&&document.activeElement.contains(n.dom)}function Ke(n,e=!1){let t=n.state.selection;if(Ma(n,t),!!Ca(n)){if(!e&&n.input.mouseDown&&n.input.mouseDown.allowDefault&&ce){let r=n.domSelectionRange(),i=n.domObserver.currentSelection;if(r.anchorNode&&i.anchorNode&&Dt(r.anchorNode,r.anchorOffset,i.anchorNode,i.anchorOffset)){n.input.mouseDown.delayedSelectionSync=!0,n.domObserver.setCurSelection();return}}if(n.domObserver.disconnectSelection(),n.cursorWrapper)gf(n);else{let{anchor:r,head:i}=t,s,o;Lo&&!(t instanceof R)&&(t.$from.parent.inlineContent||(s=Bo(n,t.from)),!t.empty&&!t.$from.parent.inlineContent&&(o=Bo(n,t.to))),n.docView.setSelection(r,i,n,e),Lo&&(s&&zo(s),o&&zo(o)),t.visible?n.dom.classList.remove("ProseMirror-hideselection"):(n.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&mf(n))}n.domObserver.setCurSelection(),n.domObserver.connectSelection()}}const Lo=he||ce&&da<63;function Bo(n,e){let{node:t,offset:r}=n.docView.domFromPos(e,0),i=r<t.childNodes.length?t.childNodes[r]:null,s=r?t.childNodes[r-1]:null;if(he&&i&&i.contentEditable=="false")return pi(i);if((!i||i.contentEditable=="false")&&(!s||s.contentEditable=="false")){if(i)return pi(i);if(s)return pi(s)}}function pi(n){return n.contentEditable="true",he&&n.draggable&&(n.draggable=!1,n.wasDraggable=!0),n}function zo(n){n.contentEditable="false",n.wasDraggable&&(n.draggable=!0,n.wasDraggable=null)}function mf(n){let e=n.dom.ownerDocument;e.removeEventListener("selectionchange",n.input.hideSelectionGuard);let t=n.domSelectionRange(),r=t.anchorNode,i=t.anchorOffset;e.addEventListener("selectionchange",n.input.hideSelectionGuard=()=>{(t.anchorNode!=r||t.anchorOffset!=i)&&(e.removeEventListener("selectionchange",n.input.hideSelectionGuard),setTimeout(()=>{(!Ca(n)||n.state.selection.visible)&&n.dom.classList.remove("ProseMirror-hideselection")},20))})}function gf(n){let e=n.domSelection(),t=document.createRange();if(!e)return;let r=n.cursorWrapper.dom,i=r.nodeName=="IMG";i?t.setStart(r.parentNode,oe(r)+1):t.setStart(r,0),t.collapse(!0),e.removeAllRanges(),e.addRange(t),!i&&!n.state.selection.visible&&be&&at<=11&&(r.disabled=!0,r.disabled=!1)}function Ma(n,e){if(e instanceof A){let t=n.docView.descAt(e.from);t!=n.lastSelectedViewDesc&&(Fo(n),t&&t.selectNode(),n.lastSelectedViewDesc=t)}else Fo(n)}function Fo(n){n.lastSelectedViewDesc&&(n.lastSelectedViewDesc.parent&&n.lastSelectedViewDesc.deselectNode(),n.lastSelectedViewDesc=void 0)}function hs(n,e,t,r){return n.someProp("createSelectionBetween",i=>i(n,e,t))||R.between(e,t,r)}function $o(n){return n.editable&&!n.hasFocus()?!1:wa(n)}function wa(n){let e=n.domSelectionRange();if(!e.anchorNode)return!1;try{return n.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(n.editable||n.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch(t){return!1}}function yf(n){let e=n.docView.domFromPos(n.state.selection.anchor,0),t=n.domSelectionRange();return Dt(e.node,e.offset,t.anchorNode,t.anchorOffset)}function ji(n,e){let{$anchor:t,$head:r}=n.selection,i=e>0?t.max(r):t.min(r),s=i.parent.inlineContent?i.depth?n.doc.resolve(e>0?i.after():i.before()):null:i;return s&&P.findFrom(s,e)}function Qe(n,e){return n.dispatch(n.state.tr.setSelection(e).scrollIntoView()),!0}function Ho(n,e,t){let r=n.state.selection;if(r instanceof R)if(t.indexOf("s")>-1){let{$head:i}=r,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!s||s.isText||!s.isLeaf)return!1;let o=n.state.doc.resolve(i.pos+s.nodeSize*(e<0?-1:1));return Qe(n,new R(r.$anchor,o))}else if(r.empty){if(n.endOfTextblock(e>0?"forward":"backward")){let i=ji(n.state,e);return i&&i instanceof A?Qe(n,i):!1}else if(!(Me&&t.indexOf("m")>-1)){let i=r.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter,o;if(!s||s.isText)return!1;let l=e<0?i.pos-s.nodeSize:i.pos;return s.isAtom||(o=n.docView.descAt(l))&&!o.contentDOM?A.isSelectable(s)?Qe(n,new A(e<0?n.state.doc.resolve(i.pos-s.nodeSize):i)):On?Qe(n,new R(n.state.doc.resolve(e<0?l:l+s.nodeSize))):!1:!1}}else return!1;else{if(r instanceof A&&r.node.isInline)return Qe(n,new R(e>0?r.$to:r.$from));{let i=ji(n.state,e);return i?Qe(n,i):!1}}}function rr(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function cn(n,e){let t=n.pmViewDesc;return t&&t.size==0&&(e<0||n.nextSibling||n.nodeName!="BR")}function zt(n,e){return e<0?bf(n):kf(n)}function bf(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i,s,o=!1;for(De&&t.nodeType==1&&r<rr(t)&&cn(t.childNodes[r],-1)&&(o=!0);;)if(r>0){if(t.nodeType!=1)break;{let l=t.childNodes[r-1];if(cn(l,-1))i=t,s=--r;else if(l.nodeType==3)t=l,r=t.nodeValue.length;else break}}else{if(Ta(t))break;{let l=t.previousSibling;for(;l&&cn(l,-1);)i=t.parentNode,s=oe(l),l=l.previousSibling;if(l)t=l,r=rr(t);else{if(t=t.parentNode,t==n.dom)break;r=0}}}o?Wi(n,t,r):i&&Wi(n,i,s)}function kf(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i=rr(t),s,o;for(;;)if(r<i){if(t.nodeType!=1)break;let l=t.childNodes[r];if(cn(l,1))s=t,o=++r;else break}else{if(Ta(t))break;{let l=t.nextSibling;for(;l&&cn(l,1);)s=l.parentNode,o=oe(l)+1,l=l.nextSibling;if(l)t=l,r=0,i=rr(t);else{if(t=t.parentNode,t==n.dom)break;r=i=0}}}s&&Wi(n,s,o)}function Ta(n){let e=n.pmViewDesc;return e&&e.node&&e.node.isBlock}function xf(n,e){for(;n&&e==n.childNodes.length&&!An(n);)e=oe(n)+1,n=n.parentNode;for(;n&&e<n.childNodes.length;){let t=n.childNodes[e];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=0}}function Sf(n,e){for(;n&&!e&&!An(n);)e=oe(n),n=n.parentNode;for(;n&&e;){let t=n.childNodes[e-1];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=n.childNodes.length}}function Wi(n,e,t){if(e.nodeType!=3){let s,o;(o=xf(e,t))?(e=o,t=0):(s=Sf(e,t))&&(e=s,t=s.nodeValue.length)}let r=n.domSelection();if(!r)return;if(Wr(r)){let s=document.createRange();s.setEnd(e,t),s.setStart(e,t),r.removeAllRanges(),r.addRange(s)}else r.extend&&r.extend(e,t);n.domObserver.setCurSelection();let{state:i}=n;setTimeout(()=>{n.state==i&&Ke(n)},50)}function _o(n,e){let t=n.state.doc.resolve(e);if(!(ce||Vd)&&t.parent.inlineContent){let i=n.coordsAtPos(e);if(e>t.start()){let s=n.coordsAtPos(e-1),o=(s.top+s.bottom)/2;if(o>i.top&&o<i.bottom&&Math.abs(s.left-i.left)>1)return s.left<i.left?"ltr":"rtl"}if(e<t.end()){let s=n.coordsAtPos(e+1),o=(s.top+s.bottom)/2;if(o>i.top&&o<i.bottom&&Math.abs(s.left-i.left)>1)return s.left>i.left?"ltr":"rtl"}}return getComputedStyle(n.dom).direction=="rtl"?"rtl":"ltr"}function Vo(n,e,t){let r=n.state.selection;if(r instanceof R&&!r.empty||t.indexOf("s")>-1||Me&&t.indexOf("m")>-1)return!1;let{$from:i,$to:s}=r;if(!i.parent.inlineContent||n.endOfTextblock(e<0?"up":"down")){let o=ji(n.state,e);if(o&&o instanceof A)return Qe(n,o)}if(!i.parent.inlineContent){let o=e<0?i:s,l=r instanceof ye?P.near(o,e):P.findFrom(o,e);return l?Qe(n,l):!1}return!1}function jo(n,e){if(!(n.state.selection instanceof R))return!0;let{$head:t,$anchor:r,empty:i}=n.state.selection;if(!t.sameParent(r))return!0;if(!i)return!1;if(n.endOfTextblock(e>0?"forward":"backward"))return!0;let s=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(s&&!s.isText){let o=n.state.tr;return e<0?o.delete(t.pos-s.nodeSize,t.pos):o.delete(t.pos,t.pos+s.nodeSize),n.dispatch(o),!0}return!1}function Wo(n,e,t){n.domObserver.stop(),e.contentEditable=t,n.domObserver.start()}function vf(n){if(!he||n.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:t}=n.domSelectionRange();if(e&&e.nodeType==1&&t==0&&e.firstChild&&e.firstChild.contentEditable=="false"){let r=e.firstChild;Wo(n,r,"true"),setTimeout(()=>Wo(n,r,"false"),20)}return!1}function Cf(n){let e="";return n.ctrlKey&&(e+="c"),n.metaKey&&(e+="m"),n.altKey&&(e+="a"),n.shiftKey&&(e+="s"),e}function Mf(n,e){let t=e.keyCode,r=Cf(e);if(t==8||Me&&t==72&&r=="c")return jo(n,-1)||zt(n,-1);if(t==46&&!e.shiftKey||Me&&t==68&&r=="c")return jo(n,1)||zt(n,1);if(t==13||t==27)return!0;if(t==37||Me&&t==66&&r=="c"){let i=t==37?_o(n,n.state.selection.from)=="ltr"?-1:1:-1;return Ho(n,i,r)||zt(n,i)}else if(t==39||Me&&t==70&&r=="c"){let i=t==39?_o(n,n.state.selection.from)=="ltr"?1:-1:1;return Ho(n,i,r)||zt(n,i)}else{if(t==38||Me&&t==80&&r=="c")return Vo(n,-1,r)||zt(n,-1);if(t==40||Me&&t==78&&r=="c")return vf(n)||Vo(n,1,r)||zt(n,1);if(r==(Me?"m":"c")&&(t==66||t==73||t==89||t==90))return!0}return!1}function ps(n,e){n.someProp("transformCopied",h=>{e=h(e,n)});let t=[],{content:r,openStart:i,openEnd:s}=e;for(;i>1&&s>1&&r.childCount==1&&r.firstChild.childCount==1;){i--,s--;let h=r.firstChild;t.push(h.type.name,h.attrs!=h.type.defaultAttrs?h.attrs:null),r=h.content}let o=n.someProp("clipboardSerializer")||Pt.fromSchema(n.state.schema),l=Ia(),a=l.createElement("div");a.appendChild(o.serializeFragment(r,{document:l}));let c=a.firstChild,u,d=0;for(;c&&c.nodeType==1&&(u=Da[c.nodeName.toLowerCase()]);){for(let h=u.length-1;h>=0;h--){let p=l.createElement(u[h]);for(;a.firstChild;)p.appendChild(a.firstChild);a.appendChild(p),d++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${i} ${s}${d?` -${d}`:""} ${JSON.stringify(t)}`);let f=n.someProp("clipboardTextSerializer",h=>h(e,n))||e.content.textBetween(0,e.content.size,`

`);return{dom:a,text:f,slice:e}}function Ea(n,e,t,r,i){let s=i.parent.type.spec.code,o,l;if(!t&&!e)return null;let a=e&&(r||s||!t);if(a){if(n.someProp("transformPastedText",f=>{e=f(e,s||r,n)}),s)return e?new w(S.from(n.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):w.empty;let d=n.someProp("clipboardTextParser",f=>f(e,i,r,n));if(d)l=d;else{let f=i.marks(),{schema:h}=n.state,p=Pt.fromSchema(h);o=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=o.appendChild(document.createElement("p"));m&&g.appendChild(p.serializeNode(h.text(m,f)))})}}else n.someProp("transformPastedHTML",d=>{t=d(t,n)}),o=Af(t),On&&Of(o);let c=o&&o.querySelector("[data-pm-slice]"),u=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(u&&u[3])for(let d=+u[3];d>0;d--){let f=o.firstChild;for(;f&&f.nodeType!=1;)f=f.nextSibling;if(!f)break;o=f}if(l||(l=(n.someProp("clipboardParser")||n.someProp("domParser")||lt.fromSchema(n.state.schema)).parseSlice(o,{preserveWhitespace:!!(a||u),context:i,ruleFromNode(f){return f.nodeName=="BR"&&!f.nextSibling&&f.parentNode&&!wf.test(f.parentNode.nodeName)?{ignore:!0}:null}})),u)l=Nf(Ko(l,+u[1],+u[2]),u[4]);else if(l=w.maxOpen(Tf(l.content,i),!0),l.openStart||l.openEnd){let d=0,f=0;for(let h=l.content.firstChild;d<l.openStart&&!h.type.spec.isolating;d++,h=h.firstChild);for(let h=l.content.lastChild;f<l.openEnd&&!h.type.spec.isolating;f++,h=h.lastChild);l=Ko(l,d,f)}return n.someProp("transformPasted",d=>{l=d(l,n)}),l}const wf=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Tf(n,e){if(n.childCount<2)return n;for(let t=e.depth;t>=0;t--){let i=e.node(t).contentMatchAt(e.index(t)),s,o=[];if(n.forEach(l=>{if(!o)return;let a=i.findWrapping(l.type),c;if(!a)return o=null;if(c=o.length&&s.length&&Oa(a,s,l,o[o.length-1],0))o[o.length-1]=c;else{o.length&&(o[o.length-1]=Na(o[o.length-1],s.length));let u=Aa(l,a);o.push(u),i=i.matchType(u.type),s=a}}),o)return S.from(o)}return n}function Aa(n,e,t=0){for(let r=e.length-1;r>=t;r--)n=e[r].create(null,S.from(n));return n}function Oa(n,e,t,r,i){if(i<n.length&&i<e.length&&n[i]==e[i]){let s=Oa(n,e,t,r.lastChild,i+1);if(s)return r.copy(r.content.replaceChild(r.childCount-1,s));if(r.contentMatchAt(r.childCount).matchType(i==n.length-1?t.type:n[i+1]))return r.copy(r.content.append(S.from(Aa(t,n,i+1))))}}function Na(n,e){if(e==0)return n;let t=n.content.replaceChild(n.childCount-1,Na(n.lastChild,e-1)),r=n.contentMatchAt(n.childCount).fillBefore(S.empty,!0);return n.copy(t.append(r))}function Ki(n,e,t,r,i,s){let o=e<0?n.firstChild:n.lastChild,l=o.content;return n.childCount>1&&(s=0),i<r-1&&(l=Ki(l,e,t,r,i+1,s)),i>=t&&(l=e<0?o.contentMatchAt(0).fillBefore(l,s<=i).append(l):l.append(o.contentMatchAt(o.childCount).fillBefore(S.empty,!0))),n.replaceChild(e<0?0:n.childCount-1,o.copy(l))}function Ko(n,e,t){return e<n.openStart&&(n=new w(Ki(n.content,-1,e,n.openStart,0,n.openEnd),e,n.openEnd)),t<n.openEnd&&(n=new w(Ki(n.content,1,t,n.openEnd,0,0),n.openStart,t)),n}const Da={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let Uo=null;function Ia(){return Uo||(Uo=document.implementation.createHTMLDocument("title"))}let mi=null;function Ef(n){let e=window.trustedTypes;return e?(mi||(mi=e.defaultPolicy||e.createPolicy("ProseMirrorClipboard",{createHTML:t=>t})),mi.createHTML(n)):n}function Af(n){let e=/^(\s*<meta [^>]*>)*/.exec(n);e&&(n=n.slice(e[0].length));let t=Ia().createElement("div"),r=/<([a-z][^>\s]+)/i.exec(n),i;if((i=r&&Da[r[1].toLowerCase()])&&(n=i.map(s=>"<"+s+">").join("")+n+i.map(s=>"</"+s+">").reverse().join("")),t.innerHTML=Ef(n),i)for(let s=0;s<i.length;s++)t=t.querySelector(i[s])||t;return t}function Of(n){let e=n.querySelectorAll(ce?"span:not([class]):not([style])":"span.Apple-converted-space");for(let t=0;t<e.length;t++){let r=e[t];r.childNodes.length==1&&r.textContent==" "&&r.parentNode&&r.parentNode.replaceChild(n.ownerDocument.createTextNode(" "),r)}}function Nf(n,e){if(!n.size)return n;let t=n.content.firstChild.type.schema,r;try{r=JSON.parse(e)}catch(l){return n}let{content:i,openStart:s,openEnd:o}=n;for(let l=r.length-2;l>=0;l-=2){let a=t.nodes[r[l]];if(!a||a.hasRequiredAttrs())break;i=S.from(a.create(r[l+1],i)),s++,o++}return new w(i,s,o)}const pe={},me={},Df={touchstart:!0,touchmove:!0};class If{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function Rf(n){for(let e in pe){let t=pe[e];n.dom.addEventListener(e,n.input.eventHandlers[e]=r=>{Lf(n,r)&&!ms(n,r)&&(n.editable||!(r.type in me))&&t(n,r)},Df[e]?{passive:!0}:void 0)}he&&n.dom.addEventListener("input",()=>null),Ui(n)}function ot(n,e){n.input.lastSelectionOrigin=e,n.input.lastSelectionTime=Date.now()}function Pf(n){n.domObserver.stop();for(let e in n.input.eventHandlers)n.dom.removeEventListener(e,n.input.eventHandlers[e]);clearTimeout(n.input.composingTimeout),clearTimeout(n.input.lastIOSEnterFallbackTimeout)}function Ui(n){n.someProp("handleDOMEvents",e=>{for(let t in e)n.input.eventHandlers[t]||n.dom.addEventListener(t,n.input.eventHandlers[t]=r=>ms(n,r))})}function ms(n,e){return n.someProp("handleDOMEvents",t=>{let r=t[e.type];return r?r(n,e)||e.defaultPrevented:!1})}function Lf(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=n.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function Bf(n,e){!ms(n,e)&&pe[e.type]&&(n.editable||!(e.type in me))&&pe[e.type](n,e)}me.keydown=(n,e)=>{let t=e;if(n.input.shiftKey=t.keyCode==16||t.shiftKey,!Pa(n,t)&&(n.input.lastKeyCode=t.keyCode,n.input.lastKeyCodeTime=Date.now(),!(je&&ce&&t.keyCode==13)))if(t.keyCode!=229&&n.domObserver.forceFlush(),qt&&t.keyCode==13&&!t.ctrlKey&&!t.altKey&&!t.metaKey){let r=Date.now();n.input.lastIOSEnter=r,n.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{n.input.lastIOSEnter==r&&(n.someProp("handleKeyDown",i=>i(n,kt(13,"Enter"))),n.input.lastIOSEnter=0)},200)}else n.someProp("handleKeyDown",r=>r(n,t))||Mf(n,t)?t.preventDefault():ot(n,"key")};me.keyup=(n,e)=>{e.keyCode==16&&(n.input.shiftKey=!1)};me.keypress=(n,e)=>{let t=e;if(Pa(n,t)||!t.charCode||t.ctrlKey&&!t.altKey||Me&&t.metaKey)return;if(n.someProp("handleKeyPress",i=>i(n,t))){t.preventDefault();return}let r=n.state.selection;if(!(r instanceof R)||!r.$from.sameParent(r.$to)){let i=String.fromCharCode(t.charCode),s=()=>n.state.tr.insertText(i).scrollIntoView();!/[\r\n]/.test(i)&&!n.someProp("handleTextInput",o=>o(n,r.$from.pos,r.$to.pos,i,s))&&n.dispatch(s()),t.preventDefault()}};function Ur(n){return{left:n.clientX,top:n.clientY}}function zf(n,e){let t=e.x-n.clientX,r=e.y-n.clientY;return t*t+r*r<100}function gs(n,e,t,r,i){if(r==-1)return!1;let s=n.state.doc.resolve(r);for(let o=s.depth+1;o>0;o--)if(n.someProp(e,l=>o>s.depth?l(n,t,s.nodeAfter,s.before(o),i,!0):l(n,t,s.node(o),s.before(o),i,!1)))return!0;return!1}function Wt(n,e,t){if(n.focused||n.focus(),n.state.selection.eq(e))return;let r=n.state.tr.setSelection(e);t=="pointer"&&r.setMeta("pointer",!0),n.dispatch(r)}function Ff(n,e){if(e==-1)return!1;let t=n.state.doc.resolve(e),r=t.nodeAfter;return r&&r.isAtom&&A.isSelectable(r)?(Wt(n,new A(t),"pointer"),!0):!1}function $f(n,e){if(e==-1)return!1;let t=n.state.selection,r,i;t instanceof A&&(r=t.node);let s=n.state.doc.resolve(e);for(let o=s.depth+1;o>0;o--){let l=o>s.depth?s.nodeAfter:s.node(o);if(A.isSelectable(l)){r&&t.$from.depth>0&&o>=t.$from.depth&&s.before(t.$from.depth+1)==t.$from.pos?i=s.before(t.$from.depth):i=s.before(o);break}}return i!=null?(Wt(n,A.create(n.state.doc,i),"pointer"),!0):!1}function Hf(n,e,t,r,i){return gs(n,"handleClickOn",e,t,r)||n.someProp("handleClick",s=>s(n,e,r))||(i?$f(n,t):Ff(n,t))}function _f(n,e,t,r){return gs(n,"handleDoubleClickOn",e,t,r)||n.someProp("handleDoubleClick",i=>i(n,e,r))}function Vf(n,e,t,r){return gs(n,"handleTripleClickOn",e,t,r)||n.someProp("handleTripleClick",i=>i(n,e,r))||jf(n,t,r)}function jf(n,e,t){if(t.button!=0)return!1;let r=n.state.doc;if(e==-1)return r.inlineContent?(Wt(n,R.create(r,0,r.content.size),"pointer"),!0):!1;let i=r.resolve(e);for(let s=i.depth+1;s>0;s--){let o=s>i.depth?i.nodeAfter:i.node(s),l=i.before(s);if(o.inlineContent)Wt(n,R.create(r,l+1,l+1+o.content.size),"pointer");else if(A.isSelectable(o))Wt(n,A.create(r,l),"pointer");else continue;return!0}}function ys(n){return ir(n)}const Ra=Me?"metaKey":"ctrlKey";pe.mousedown=(n,e)=>{let t=e;n.input.shiftKey=t.shiftKey;let r=ys(n),i=Date.now(),s="singleClick";i-n.input.lastClick.time<500&&zf(t,n.input.lastClick)&&!t[Ra]&&n.input.lastClick.button==t.button&&(n.input.lastClick.type=="singleClick"?s="doubleClick":n.input.lastClick.type=="doubleClick"&&(s="tripleClick")),n.input.lastClick={time:i,x:t.clientX,y:t.clientY,type:s,button:t.button};let o=n.posAtCoords(Ur(t));o&&(s=="singleClick"?(n.input.mouseDown&&n.input.mouseDown.done(),n.input.mouseDown=new Wf(n,o,t,!!r)):(s=="doubleClick"?_f:Vf)(n,o.pos,o.inside,t)?t.preventDefault():ot(n,"pointer"))};class Wf{constructor(e,t,r,i){this.view=e,this.pos=t,this.event=r,this.flushed=i,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!r[Ra],this.allowDefault=r.shiftKey;let s,o;if(t.inside>-1)s=e.state.doc.nodeAt(t.inside),o=t.inside;else{let u=e.state.doc.resolve(t.pos);s=u.parent,o=u.depth?u.before():0}const l=i?null:r.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&a.dom.nodeType==1?a.dom:null;let{selection:c}=e.state;(r.button==0&&s.type.spec.draggable&&s.type.spec.selectable!==!1||c instanceof A&&c.from<=o&&c.to>o)&&(this.mightDrag={node:s,pos:o,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&De&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),ot(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>Ke(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(Ur(e))),this.updateAllowDefault(e),this.allowDefault||!t?ot(this.view,"pointer"):Hf(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||he&&this.mightDrag&&!this.mightDrag.node.isAtom||ce&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(Wt(this.view,P.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):ot(this.view,"pointer")}move(e){this.updateAllowDefault(e),ot(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}pe.touchstart=n=>{n.input.lastTouch=Date.now(),ys(n),ot(n,"pointer")};pe.touchmove=n=>{n.input.lastTouch=Date.now(),ot(n,"pointer")};pe.contextmenu=n=>ys(n);function Pa(n,e){return n.composing?!0:he&&Math.abs(e.timeStamp-n.input.compositionEndedAt)<500?(n.input.compositionEndedAt=-2e8,!0):!1}const Kf=je?5e3:-1;me.compositionstart=me.compositionupdate=n=>{if(!n.composing){n.domObserver.flush();let{state:e}=n,t=e.selection.$to;if(e.selection instanceof R&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(r=>r.type.spec.inclusive===!1)))n.markCursor=n.state.storedMarks||t.marks(),ir(n,!0),n.markCursor=null;else if(ir(n,!e.selection.empty),De&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let r=n.domSelectionRange();for(let i=r.focusNode,s=r.focusOffset;i&&i.nodeType==1&&s!=0;){let o=s<0?i.lastChild:i.childNodes[s-1];if(!o)break;if(o.nodeType==3){let l=n.domSelection();l&&l.collapse(o,o.nodeValue.length);break}else i=o,s=-1}}n.input.composing=!0}La(n,Kf)};me.compositionend=(n,e)=>{n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=e.timeStamp,n.input.compositionPendingChanges=n.domObserver.pendingRecords().length?n.input.compositionID:0,n.input.compositionNode=null,n.input.compositionPendingChanges&&Promise.resolve().then(()=>n.domObserver.flush()),n.input.compositionID++,La(n,20))};function La(n,e){clearTimeout(n.input.composingTimeout),e>-1&&(n.input.composingTimeout=setTimeout(()=>ir(n),e))}function Ba(n){for(n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=qf());n.input.compositionNodes.length>0;)n.input.compositionNodes.pop().markParentsDirty()}function Uf(n){let e=n.domSelectionRange();if(!e.focusNode)return null;let t=zd(e.focusNode,e.focusOffset),r=Fd(e.focusNode,e.focusOffset);if(t&&r&&t!=r){let i=r.pmViewDesc,s=n.domObserver.lastChangedTextNode;if(t==s||r==s)return s;if(!i||!i.isText(r.nodeValue))return r;if(n.input.compositionNode==r){let o=t.pmViewDesc;if(!(!o||!o.isText(t.nodeValue)))return r}}return t||r}function qf(){let n=document.createEvent("Event");return n.initEvent("event",!0,!0),n.timeStamp}function ir(n,e=!1){if(!(je&&n.domObserver.flushingSoon>=0)){if(n.domObserver.forceFlush(),Ba(n),e||n.docView&&n.docView.dirty){let t=fs(n),r=n.state.selection;return t&&!t.eq(r)?n.dispatch(n.state.tr.setSelection(t)):(n.markCursor||e)&&!r.$from.node(r.$from.sharedDepth(r.to)).inlineContent?n.dispatch(n.state.tr.deleteSelection()):n.updateState(n.state),!0}return!1}}function Jf(n,e){if(!n.dom.parentNode)return;let t=n.dom.parentNode.appendChild(document.createElement("div"));t.appendChild(e),t.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(e),n.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),n.focus()},50)}const xn=be&&at<15||qt&&jd<604;pe.copy=me.cut=(n,e)=>{let t=e,r=n.state.selection,i=t.type=="cut";if(r.empty)return;let s=xn?null:t.clipboardData,o=r.content(),{dom:l,text:a}=ps(n,o);s?(t.preventDefault(),s.clearData(),s.setData("text/html",l.innerHTML),s.setData("text/plain",a)):Jf(n,l),i&&n.dispatch(n.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function Gf(n){return n.openStart==0&&n.openEnd==0&&n.content.childCount==1?n.content.firstChild:null}function Yf(n,e){if(!n.dom.parentNode)return;let t=n.input.shiftKey||n.state.selection.$from.parent.type.spec.code,r=n.dom.parentNode.appendChild(document.createElement(t?"textarea":"div"));t||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=n.input.shiftKey&&n.input.lastKeyCode!=45;setTimeout(()=>{n.focus(),r.parentNode&&r.parentNode.removeChild(r),t?Sn(n,r.value,null,i,e):Sn(n,r.textContent,r.innerHTML,i,e)},50)}function Sn(n,e,t,r,i){let s=Ea(n,e,t,r,n.state.selection.$from);if(n.someProp("handlePaste",a=>a(n,i,s||w.empty)))return!0;if(!s)return!1;let o=Gf(s),l=o?n.state.tr.replaceSelectionWith(o,r):n.state.tr.replaceSelection(s);return n.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function za(n){let e=n.getData("text/plain")||n.getData("Text");if(e)return e;let t=n.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}me.paste=(n,e)=>{let t=e;if(n.composing&&!je)return;let r=xn?null:t.clipboardData,i=n.input.shiftKey&&n.input.lastKeyCode!=45;r&&Sn(n,za(r),r.getData("text/html"),i,t)?t.preventDefault():Yf(n,t)};class Fa{constructor(e,t,r){this.slice=e,this.move=t,this.node=r}}const Xf=Me?"altKey":"ctrlKey";function $a(n,e){let t=n.someProp("dragCopies",r=>!r(e));return t!=null?t:!e[Xf]}pe.dragstart=(n,e)=>{let t=e,r=n.input.mouseDown;if(r&&r.done(),!t.dataTransfer)return;let i=n.state.selection,s=i.empty?null:n.posAtCoords(Ur(t)),o;if(!(s&&s.pos>=i.from&&s.pos<=(i instanceof A?i.to-1:i.to))){if(r&&r.mightDrag)o=A.create(n.state.doc,r.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let d=n.docView.nearestDesc(t.target,!0);d&&d.node.type.spec.draggable&&d!=n.docView&&(o=A.create(n.state.doc,d.posBefore))}}let l=(o||n.state.selection).content(),{dom:a,text:c,slice:u}=ps(n,l);(!t.dataTransfer.files.length||!ce||da>120)&&t.dataTransfer.clearData(),t.dataTransfer.setData(xn?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",xn||t.dataTransfer.setData("text/plain",c),n.dragging=new Fa(u,$a(n,t),o)};pe.dragend=n=>{let e=n.dragging;window.setTimeout(()=>{n.dragging==e&&(n.dragging=null)},50)};me.dragover=me.dragenter=(n,e)=>e.preventDefault();me.drop=(n,e)=>{let t=e,r=n.dragging;if(n.dragging=null,!t.dataTransfer)return;let i=n.posAtCoords(Ur(t));if(!i)return;let s=n.state.doc.resolve(i.pos),o=r&&r.slice;o?n.someProp("transformPasted",p=>{o=p(o,n)}):o=Ea(n,za(t.dataTransfer),xn?null:t.dataTransfer.getData("text/html"),!1,s);let l=!!(r&&$a(n,t));if(n.someProp("handleDrop",p=>p(n,t,o||w.empty,l))){t.preventDefault();return}if(!o)return;t.preventDefault();let a=o?na(n.state.doc,s.pos,o):s.pos;a==null&&(a=s.pos);let c=n.state.tr;if(l){let{node:p}=r;p?p.replace(c):c.deleteSelection()}let u=c.mapping.map(a),d=o.openStart==0&&o.openEnd==0&&o.content.childCount==1,f=c.doc;if(d?c.replaceRangeWith(u,u,o.content.firstChild):c.replaceRange(u,u,o),c.doc.eq(f))return;let h=c.doc.resolve(u);if(d&&A.isSelectable(o.content.firstChild)&&h.nodeAfter&&h.nodeAfter.sameMarkup(o.content.firstChild))c.setSelection(new A(h));else{let p=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,b,v)=>p=v),c.setSelection(hs(n,h,c.doc.resolve(p)))}n.focus(),n.dispatch(c.setMeta("uiEvent","drop"))};pe.focus=n=>{n.input.lastFocus=Date.now(),n.focused||(n.domObserver.stop(),n.dom.classList.add("ProseMirror-focused"),n.domObserver.start(),n.focused=!0,setTimeout(()=>{n.docView&&n.hasFocus()&&!n.domObserver.currentSelection.eq(n.domSelectionRange())&&Ke(n)},20))};pe.blur=(n,e)=>{let t=e;n.focused&&(n.domObserver.stop(),n.dom.classList.remove("ProseMirror-focused"),n.domObserver.start(),t.relatedTarget&&n.dom.contains(t.relatedTarget)&&n.domObserver.currentSelection.clear(),n.focused=!1)};pe.beforeinput=(n,e)=>{if(ce&&je&&e.inputType=="deleteContentBackward"){n.domObserver.flushSoon();let{domChangeCount:r}=n.input;setTimeout(()=>{if(n.input.domChangeCount!=r||(n.dom.blur(),n.focus(),n.someProp("handleKeyDown",s=>s(n,kt(8,"Backspace")))))return;let{$cursor:i}=n.state.selection;i&&i.pos>0&&n.dispatch(n.state.tr.delete(i.pos-1,i.pos).scrollIntoView())},50)}};for(let n in me)pe[n]=me[n];function vn(n,e){if(n==e)return!0;for(let t in n)if(n[t]!==e[t])return!1;for(let t in e)if(!(t in n))return!1;return!0}class sr{constructor(e,t){this.toDOM=e,this.spec=t||Tt,this.side=this.spec.side||0}map(e,t,r,i){let{pos:s,deleted:o}=e.mapResult(t.from+i,this.side<0?-1:1);return o?null:new fe(s-r,s-r,this)}valid(){return!0}eq(e){return this==e||e instanceof sr&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&vn(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class ut{constructor(e,t){this.attrs=e,this.spec=t||Tt}map(e,t,r,i){let s=e.map(t.from+i,this.spec.inclusiveStart?-1:1)-r,o=e.map(t.to+i,this.spec.inclusiveEnd?1:-1)-r;return s>=o?null:new fe(s,o,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof ut&&vn(this.attrs,e.attrs)&&vn(this.spec,e.spec)}static is(e){return e.type instanceof ut}destroy(){}}class bs{constructor(e,t){this.attrs=e,this.spec=t||Tt}map(e,t,r,i){let s=e.mapResult(t.from+i,1);if(s.deleted)return null;let o=e.mapResult(t.to+i,-1);return o.deleted||o.pos<=s.pos?null:new fe(s.pos-r,o.pos-r,this)}valid(e,t){let{index:r,offset:i}=e.content.findIndex(t.from),s;return i==t.from&&!(s=e.child(r)).isText&&i+s.nodeSize==t.to}eq(e){return this==e||e instanceof bs&&vn(this.attrs,e.attrs)&&vn(this.spec,e.spec)}destroy(){}}class fe{constructor(e,t,r){this.from=e,this.to=t,this.type=r}copy(e,t){return new fe(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,r){return this.type.map(e,this,t,r)}static widget(e,t,r){return new fe(e,e,new sr(t,r))}static inline(e,t,r,i){return new fe(e,t,new ut(r,i))}static node(e,t,r,i){return new fe(e,t,new bs(r,i))}get spec(){return this.type.spec}get inline(){return this.type instanceof ut}get widget(){return this.type instanceof sr}}const $t=[],Tt={};class q{constructor(e,t){this.local=e.length?e:$t,this.children=t.length?t:$t}static create(e,t){return t.length?or(t,e,0,Tt):ae}find(e,t,r){let i=[];return this.findInner(e==null?0:e,t==null?1e9:t,i,0,r),i}findInner(e,t,r,i,s){for(let o=0;o<this.local.length;o++){let l=this.local[o];l.from<=t&&l.to>=e&&(!s||s(l.spec))&&r.push(l.copy(l.from+i,l.to+i))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<t&&this.children[o+1]>e){let l=this.children[o]+1;this.children[o+2].findInner(e-l,t-l,r,i+l,s)}}map(e,t,r){return this==ae||e.maps.length==0?this:this.mapInner(e,t,0,0,r||Tt)}mapInner(e,t,r,i,s){let o;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,r,i);a&&a.type.valid(t,a)?(o||(o=[])).push(a):s.onRemove&&s.onRemove(this.local[l].spec)}return this.children.length?Qf(this.children,o||[],e,t,r,i,s):o?new q(o.sort(Et),$t):ae}add(e,t){return t.length?this==ae?q.create(e,t):this.addInner(e,t,0):this}addInner(e,t,r){let i,s=0;e.forEach((l,a)=>{let c=a+r,u;if(u=_a(t,l,c)){for(i||(i=this.children.slice());s<i.length&&i[s]<a;)s+=3;i[s]==a?i[s+2]=i[s+2].addInner(l,u,c+1):i.splice(s,0,a,a+l.nodeSize,or(u,l,c+1,Tt)),s+=3}});let o=Ha(s?Va(t):t,-r);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||o.splice(l--,1);return new q(o.length?this.local.concat(o).sort(Et):this.local,i||this.children)}remove(e){return e.length==0||this==ae?this:this.removeInner(e,0)}removeInner(e,t){let r=this.children,i=this.local;for(let s=0;s<r.length;s+=3){let o,l=r[s]+t,a=r[s+1]+t;for(let u=0,d;u<e.length;u++)(d=e[u])&&d.from>l&&d.to<a&&(e[u]=null,(o||(o=[])).push(d));if(!o)continue;r==this.children&&(r=this.children.slice());let c=r[s+2].removeInner(o,l+1);c!=ae?r[s+2]=c:(r.splice(s,3),s-=3)}if(i.length){for(let s=0,o;s<e.length;s++)if(o=e[s])for(let l=0;l<i.length;l++)i[l].eq(o,t)&&(i==this.local&&(i=this.local.slice()),i.splice(l--,1))}return r==this.children&&i==this.local?this:i.length||r.length?new q(i,r):ae}forChild(e,t){if(this==ae)return this;if(t.isLeaf)return q.empty;let r,i;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(r=this.children[l+2]);break}let s=e+1,o=s+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<o&&a.to>s&&a.type instanceof ut){let c=Math.max(s,a.from)-s,u=Math.min(o,a.to)-s;c<u&&(i||(i=[])).push(a.copy(c,u))}}if(i){let l=new q(i.sort(Et),$t);return r?new nt([l,r]):l}return r||ae}eq(e){if(this==e)return!0;if(!(e instanceof q)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return ks(this.localsInner(e))}localsInner(e){if(this==ae)return $t;if(e.inlineContent||!this.local.some(ut.is))return this.local;let t=[];for(let r=0;r<this.local.length;r++)this.local[r].type instanceof ut||t.push(this.local[r]);return t}forEachSet(e){e(this)}}q.empty=new q([],[]);q.removeOverlap=ks;const ae=q.empty;class nt{constructor(e){this.members=e}map(e,t){const r=this.members.map(i=>i.map(e,t,Tt));return nt.from(r)}forChild(e,t){if(t.isLeaf)return q.empty;let r=[];for(let i=0;i<this.members.length;i++){let s=this.members[i].forChild(e,t);s!=ae&&(s instanceof nt?r=r.concat(s.members):r.push(s))}return nt.from(r)}eq(e){if(!(e instanceof nt)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,r=!0;for(let i=0;i<this.members.length;i++){let s=this.members[i].localsInner(e);if(s.length)if(!t)t=s;else{r&&(t=t.slice(),r=!1);for(let o=0;o<s.length;o++)t.push(s[o])}}return t?ks(r?t:t.sort(Et)):$t}static from(e){switch(e.length){case 0:return ae;case 1:return e[0];default:return new nt(e.every(t=>t instanceof q)?e:e.reduce((t,r)=>t.concat(r instanceof q?r:r.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function Qf(n,e,t,r,i,s,o){let l=n.slice();for(let c=0,u=s;c<t.maps.length;c++){let d=0;t.maps[c].forEach((f,h,p,m)=>{let g=m-p-(h-f);for(let b=0;b<l.length;b+=3){let v=l[b+1];if(v<0||f>v+u-d)continue;let D=l[b]+u-d;h>=D?l[b+1]=f<=D?-2:-1:f>=u&&g&&(l[b]+=g,l[b+1]+=g)}d+=g}),u=t.maps[c].map(u,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let u=t.map(n[c]+s),d=u-i;if(d<0||d>=r.content.size){a=!0;continue}let f=t.map(n[c+1]+s,-1),h=f-i,{index:p,offset:m}=r.content.findIndex(d),g=r.maybeChild(p);if(g&&m==d&&m+g.nodeSize==h){let b=l[c+2].mapInner(t,g,u+1,n[c]+s+1,o);b!=ae?(l[c]=d,l[c+1]=h,l[c+2]=b):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=Zf(l,n,e,t,i,s,o),u=or(c,r,0,o);e=u.local;for(let d=0;d<l.length;d+=3)l[d+1]<0&&(l.splice(d,3),d-=3);for(let d=0,f=0;d<u.children.length;d+=3){let h=u.children[d];for(;f<l.length&&l[f]<h;)f+=3;l.splice(f,0,u.children[d],u.children[d+1],u.children[d+2])}}return new q(e.sort(Et),l)}function Ha(n,e){if(!e||!n.length)return n;let t=[];for(let r=0;r<n.length;r++){let i=n[r];t.push(new fe(i.from+e,i.to+e,i.type))}return t}function Zf(n,e,t,r,i,s,o){function l(a,c){for(let u=0;u<a.local.length;u++){let d=a.local[u].map(r,i,c);d?t.push(d):o.onRemove&&o.onRemove(a.local[u].spec)}for(let u=0;u<a.children.length;u+=3)l(a.children[u+2],a.children[u]+c+1)}for(let a=0;a<n.length;a+=3)n[a+1]==-1&&l(n[a+2],e[a]+s+1);return t}function _a(n,e,t){if(e.isLeaf)return null;let r=t+e.nodeSize,i=null;for(let s=0,o;s<n.length;s++)(o=n[s])&&o.from>t&&o.to<r&&((i||(i=[])).push(o),n[s]=null);return i}function Va(n){let e=[];for(let t=0;t<n.length;t++)n[t]!=null&&e.push(n[t]);return e}function or(n,e,t,r){let i=[],s=!1;e.forEach((l,a)=>{let c=_a(n,l,a+t);if(c){s=!0;let u=or(c,l,t+a+1,r);u!=ae&&i.push(a,a+l.nodeSize,u)}});let o=Ha(s?Va(n):n,-t).sort(Et);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||(r.onRemove&&r.onRemove(o[l].spec),o.splice(l--,1));return o.length||i.length?new q(o,i):ae}function Et(n,e){return n.from-e.from||n.to-e.to}function ks(n){let e=n;for(let t=0;t<e.length-1;t++){let r=e[t];if(r.from!=r.to)for(let i=t+1;i<e.length;i++){let s=e[i];if(s.from==r.from){s.to!=r.to&&(e==n&&(e=n.slice()),e[i]=s.copy(s.from,r.to),qo(e,i+1,s.copy(r.to,s.to)));continue}else{s.from<r.to&&(e==n&&(e=n.slice()),e[t]=r.copy(r.from,s.from),qo(e,i,r.copy(s.from,r.to)));break}}}return e}function qo(n,e,t){for(;e<n.length&&Et(t,n[e])>0;)e++;n.splice(e,0,t)}function gi(n){let e=[];return n.someProp("decorations",t=>{let r=t(n.state);r&&r!=ae&&e.push(r)}),n.cursorWrapper&&e.push(q.create(n.state.doc,[n.cursorWrapper.deco])),nt.from(e)}const eh={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},th=be&&at<=11;class nh{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class rh{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new nh,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(r=>{for(let i=0;i<r.length;i++)this.queue.push(r[i]);be&&at<=11&&r.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),th&&(this.onCharData=r=>{this.queue.push({target:r.target,type:"characterData",oldValue:r.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,eh)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if($o(this.view)){if(this.suppressingSelectionUpdates)return Ke(this.view);if(be&&at<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&Dt(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,r;for(let s=e.focusNode;s;s=Ut(s))t.add(s);for(let s=e.anchorNode;s;s=Ut(s))if(t.has(s)){r=s;break}let i=r&&this.view.docView.nearestDesc(r);if(i&&i.ignoreMutation({type:"selection",target:r.nodeType==3?r.parentNode:r}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let r=e.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(r)&&$o(e)&&!this.ignoreSelectionChange(r),s=-1,o=-1,l=!1,a=[];if(e.editable)for(let u=0;u<t.length;u++){let d=this.registerMutation(t[u],a);d&&(s=s<0?d.from:Math.min(d.from,s),o=o<0?d.to:Math.max(d.to,o),d.typeOver&&(l=!0))}if(De&&a.length){let u=a.filter(d=>d.nodeName=="BR");if(u.length==2){let[d,f]=u;d.parentNode&&d.parentNode.parentNode==f.parentNode?f.remove():d.remove()}else{let{focusNode:d}=this.currentSelection;for(let f of u){let h=f.parentNode;h&&h.nodeName=="LI"&&(!d||oh(e,d)!=h)&&f.remove()}}}let c=null;s<0&&i&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&Wr(r)&&(c=fs(e))&&c.eq(P.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,Ke(e),this.currentSelection.set(r),e.scrollToSelection()):(s>-1||i)&&(s>-1&&(e.docView.markDirty(s,o),ih(e)),this.handleDOMChange(s,o,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(r)||Ke(e),this.currentSelection.set(r))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let r=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(r==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!r||r.ignoreMutation(e))return null;if(e.type=="childList"){for(let u=0;u<e.addedNodes.length;u++){let d=e.addedNodes[u];t.push(d),d.nodeType==3&&(this.lastChangedTextNode=d)}if(r.contentDOM&&r.contentDOM!=r.dom&&!r.contentDOM.contains(e.target))return{from:r.posBefore,to:r.posAfter};let i=e.previousSibling,s=e.nextSibling;if(be&&at<=11&&e.addedNodes.length)for(let u=0;u<e.addedNodes.length;u++){let{previousSibling:d,nextSibling:f}=e.addedNodes[u];(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(i=d),(!f||Array.prototype.indexOf.call(e.addedNodes,f)<0)&&(s=f)}let o=i&&i.parentNode==e.target?oe(i)+1:0,l=r.localPosFromDOM(e.target,o,-1),a=s&&s.parentNode==e.target?oe(s):e.target.childNodes.length,c=r.localPosFromDOM(e.target,a,1);return{from:l,to:c}}else return e.type=="attributes"?{from:r.posAtStart-r.border,to:r.posAtEnd+r.border}:(this.lastChangedTextNode=e.target,{from:r.posAtStart,to:r.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let Jo=new WeakMap,Go=!1;function ih(n){if(!Jo.has(n)&&(Jo.set(n,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(n.dom).whiteSpace)!==-1)){if(n.requiresGeckoHackNode=De,Go)return;Go=!0}}function Yo(n,e){let t=e.startContainer,r=e.startOffset,i=e.endContainer,s=e.endOffset,o=n.domAtPos(n.state.selection.anchor);return Dt(o.node,o.offset,i,s)&&([t,r,i,s]=[i,s,t,r]),{anchorNode:t,anchorOffset:r,focusNode:i,focusOffset:s}}function sh(n,e){if(e.getComposedRanges){let i=e.getComposedRanges(n.root)[0];if(i)return Yo(n,i)}let t;function r(i){i.preventDefault(),i.stopImmediatePropagation(),t=i.getTargetRanges()[0]}return n.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),n.dom.removeEventListener("beforeinput",r,!0),t?Yo(n,t):null}function oh(n,e){for(let t=e.parentNode;t&&t!=n.dom;t=t.parentNode){let r=n.docView.nearestDesc(t,!0);if(r&&r.node.isBlock)return t}return null}function lh(n,e,t){let{node:r,fromOffset:i,toOffset:s,from:o,to:l}=n.docView.parseRange(e,t),a=n.domSelectionRange(),c,u=a.anchorNode;if(u&&n.dom.contains(u.nodeType==1?u:u.parentNode)&&(c=[{node:u,offset:a.anchorOffset}],Wr(a)||c.push({node:a.focusNode,offset:a.focusOffset})),ce&&n.input.lastKeyCode===8)for(let g=s;g>i;g--){let b=r.childNodes[g-1],v=b.pmViewDesc;if(b.nodeName=="BR"&&!v){s=g;break}if(!v||v.size)break}let d=n.state.doc,f=n.someProp("domParser")||lt.fromSchema(n.state.schema),h=d.resolve(o),p=null,m=f.parse(r,{topNode:h.parent,topMatch:h.parent.contentMatchAt(h.index()),topOpen:!0,from:i,to:s,preserveWhitespace:h.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:ah,context:h});if(c&&c[0].pos!=null){let g=c[0].pos,b=c[1]&&c[1].pos;b==null&&(b=g),p={anchor:g+o,head:b+o}}return{doc:m,sel:p,from:o,to:l}}function ah(n){let e=n.pmViewDesc;if(e)return e.parseRule();if(n.nodeName=="BR"&&n.parentNode){if(he&&/^(ul|ol)$/i.test(n.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}else if(n.parentNode.lastChild==n||he&&/^(tr|table)$/i.test(n.parentNode.nodeName))return{ignore:!0}}else if(n.nodeName=="IMG"&&n.getAttribute("mark-placeholder"))return{ignore:!0};return null}const ch=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|img|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function uh(n,e,t,r,i){let s=n.input.compositionPendingChanges||(n.composing?n.input.compositionID:0);if(n.input.compositionPendingChanges=0,e<0){let y=n.input.lastSelectionTime>Date.now()-50?n.input.lastSelectionOrigin:null,T=fs(n,y);if(T&&!n.state.selection.eq(T)){if(ce&&je&&n.input.lastKeyCode===13&&Date.now()-100<n.input.lastKeyCodeTime&&n.someProp("handleKeyDown",xe=>xe(n,kt(13,"Enter"))))return;let $=n.state.tr.setSelection(T);y=="pointer"?$.setMeta("pointer",!0):y=="key"&&$.scrollIntoView(),s&&$.setMeta("composition",s),n.dispatch($)}return}let o=n.state.doc.resolve(e),l=o.sharedDepth(t);e=o.before(l+1),t=n.state.doc.resolve(t).after(l+1);let a=n.state.selection,c=lh(n,e,t),u=n.state.doc,d=u.slice(c.from,c.to),f,h;n.input.lastKeyCode===8&&Date.now()-100<n.input.lastKeyCodeTime?(f=n.state.selection.to,h="end"):(f=n.state.selection.from,h="start"),n.input.lastKeyCode=null;let p=hh(d.content,c.doc.content,c.from,f,h);if(p&&n.input.domChangeCount++,(qt&&n.input.lastIOSEnter>Date.now()-225||je)&&i.some(y=>y.nodeType==1&&!ch.test(y.nodeName))&&(!p||p.endA>=p.endB)&&n.someProp("handleKeyDown",y=>y(n,kt(13,"Enter")))){n.input.lastIOSEnter=0;return}if(!p)if(r&&a instanceof R&&!a.empty&&a.$head.sameParent(a.$anchor)&&!n.composing&&!(c.sel&&c.sel.anchor!=c.sel.head))p={start:a.from,endA:a.to,endB:a.to};else{if(c.sel){let y=Xo(n,n.state.doc,c.sel);if(y&&!y.eq(n.state.selection)){let T=n.state.tr.setSelection(y);s&&T.setMeta("composition",s),n.dispatch(T)}}return}n.state.selection.from<n.state.selection.to&&p.start==p.endB&&n.state.selection instanceof R&&(p.start>n.state.selection.from&&p.start<=n.state.selection.from+2&&n.state.selection.from>=c.from?p.start=n.state.selection.from:p.endA<n.state.selection.to&&p.endA>=n.state.selection.to-2&&n.state.selection.to<=c.to&&(p.endB+=n.state.selection.to-p.endA,p.endA=n.state.selection.to)),be&&at<=11&&p.endB==p.start+1&&p.endA==p.start&&p.start>c.from&&c.doc.textBetween(p.start-c.from-1,p.start-c.from+1)=="  "&&(p.start--,p.endA--,p.endB--);let m=c.doc.resolveNoCache(p.start-c.from),g=c.doc.resolveNoCache(p.endB-c.from),b=u.resolve(p.start),v=m.sameParent(g)&&m.parent.inlineContent&&b.end()>=p.endA,D;if((qt&&n.input.lastIOSEnter>Date.now()-225&&(!v||i.some(y=>y.nodeName=="DIV"||y.nodeName=="P"))||!v&&m.pos<c.doc.content.size&&(!m.sameParent(g)||!m.parent.inlineContent)&&!/\S/.test(c.doc.textBetween(m.pos,g.pos,"",""))&&(D=P.findFrom(c.doc.resolve(m.pos+1),1,!0))&&D.head>m.pos)&&n.someProp("handleKeyDown",y=>y(n,kt(13,"Enter")))){n.input.lastIOSEnter=0;return}if(n.state.selection.anchor>p.start&&fh(u,p.start,p.endA,m,g)&&n.someProp("handleKeyDown",y=>y(n,kt(8,"Backspace")))){je&&ce&&n.domObserver.suppressSelectionUpdates();return}ce&&p.endB==p.start&&(n.input.lastChromeDelete=Date.now()),je&&!v&&m.start()!=g.start()&&g.parentOffset==0&&m.depth==g.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==p.endA&&(p.endB-=2,g=c.doc.resolveNoCache(p.endB-c.from),setTimeout(()=>{n.someProp("handleKeyDown",function(y){return y(n,kt(13,"Enter"))})},20));let F=p.start,I=p.endA,H=y=>{let T=y||n.state.tr.replace(F,I,c.doc.slice(p.start-c.from,p.endB-c.from));if(c.sel){let $=Xo(n,T.doc,c.sel);$&&!(ce&&n.composing&&$.empty&&(p.start!=p.endB||n.input.lastChromeDelete<Date.now()-100)&&($.head==F||$.head==T.mapping.map(I)-1)||be&&$.empty&&$.head==F)&&T.setSelection($)}return s&&T.setMeta("composition",s),T.scrollIntoView()},C;if(v){if(m.pos==g.pos){be&&at<=11&&m.parentOffset==0&&(n.domObserver.suppressSelectionUpdates(),setTimeout(()=>Ke(n),20));let y=H(n.state.tr.delete(F,I)),T=u.resolve(p.start).marksAcross(u.resolve(p.endA));T&&y.ensureMarks(T),n.dispatch(y)}else if(p.endA==p.endB&&(C=dh(m.parent.content.cut(m.parentOffset,g.parentOffset),b.parent.content.cut(b.parentOffset,p.endA-b.start())))){let y=H(n.state.tr);C.type=="add"?y.addMark(F,I,C.mark):y.removeMark(F,I,C.mark),n.dispatch(y)}else if(m.parent.child(m.index()).isText&&m.index()==g.index()-(g.textOffset?0:1)){let y=m.parent.textBetween(m.parentOffset,g.parentOffset),T=()=>H(n.state.tr.insertText(y,F,I));n.someProp("handleTextInput",$=>$(n,F,I,y,T))||n.dispatch(T())}}else n.dispatch(H())}function Xo(n,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:hs(n,e.resolve(t.anchor),e.resolve(t.head))}function dh(n,e){let t=n.firstChild.marks,r=e.firstChild.marks,i=t,s=r,o,l,a;for(let u=0;u<r.length;u++)i=r[u].removeFromSet(i);for(let u=0;u<t.length;u++)s=t[u].removeFromSet(s);if(i.length==1&&s.length==0)l=i[0],o="add",a=u=>u.mark(l.addToSet(u.marks));else if(i.length==0&&s.length==1)l=s[0],o="remove",a=u=>u.mark(l.removeFromSet(u.marks));else return null;let c=[];for(let u=0;u<e.childCount;u++)c.push(a(e.child(u)));if(S.from(c).eq(n))return{mark:l,type:o}}function fh(n,e,t,r,i){if(t-e<=i.pos-r.pos||yi(r,!0,!1)<i.pos)return!1;let s=n.resolve(e);if(!r.parent.isTextblock){let l=s.nodeAfter;return l!=null&&t==e+l.nodeSize}if(s.parentOffset<s.parent.content.size||!s.parent.isTextblock)return!1;let o=n.resolve(yi(s,!0,!0));return!o.parent.isTextblock||o.pos>t||yi(o,!0,!1)<t?!1:r.parent.content.cut(r.parentOffset).eq(o.parent.content)}function yi(n,e,t){let r=n.depth,i=e?n.end():n.pos;for(;r>0&&(e||n.indexAfter(r)==n.node(r).childCount);)r--,i++,e=!1;if(t){let s=n.node(r).maybeChild(n.indexAfter(r));for(;s&&!s.isLeaf;)s=s.firstChild,i++}return i}function hh(n,e,t,r,i){let s=n.findDiffStart(e,t);if(s==null)return null;let{a:o,b:l}=n.findDiffEnd(e,t+n.size,t+e.size);if(i=="end"){let a=Math.max(0,s-Math.min(o,l));r-=o+a-s}if(o<s&&n.size<e.size){let a=r<=s&&r>=o?s-r:0;s-=a,s&&s<e.size&&Qo(e.textBetween(s-1,s+1))&&(s+=a?1:-1),l=s+(l-o),o=s}else if(l<s){let a=r<=s&&r>=l?s-r:0;s-=a,s&&s<n.size&&Qo(n.textBetween(s-1,s+1))&&(s+=a?1:-1),o=s+(o-l),l=s}return{start:s,endA:o,endB:l}}function Qo(n){if(n.length!=2)return!1;let e=n.charCodeAt(0),t=n.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}class ja{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new If,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(rl),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=tl(this),el(this),this.nodeViews=nl(this),this.docView=Ro(this.state.doc,Zo(this),gi(this),this.dom,this),this.domObserver=new rh(this,(r,i,s,o)=>uh(this,r,i,s,o)),this.domObserver.start(),Rf(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&Ui(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(rl),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let r in this._props)t[r]=this._props[r];t.state=this.state;for(let r in e)t[r]=e[r];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var r;let i=this.state,s=!1,o=!1;e.storedMarks&&this.composing&&(Ba(this),o=!0),this.state=e;let l=i.plugins!=e.plugins||this._props.plugins!=t.plugins;if(l||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let h=nl(this);mh(h,this.nodeViews)&&(this.nodeViews=h,s=!0)}(l||t.handleDOMEvents!=this._props.handleDOMEvents)&&Ui(this),this.editable=tl(this),el(this);let a=gi(this),c=Zo(this),u=i.plugins!=e.plugins&&!i.doc.eq(e.doc)?"reset":e.scrollToSelection>i.scrollToSelection?"to selection":"preserve",d=s||!this.docView.matchesNode(e.doc,c,a);(d||!e.selection.eq(i.selection))&&(o=!0);let f=u=="preserve"&&o&&this.dom.style.overflowAnchor==null&&Ud(this);if(o){this.domObserver.stop();let h=d&&(be||ce)&&!this.composing&&!i.selection.empty&&!e.selection.empty&&ph(i.selection,e.selection);if(d){let p=ce?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=Uf(this)),(s||!this.docView.update(e.doc,c,a,this))&&(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=Ro(e.doc,c,a,this.dom,this)),p&&!this.trackWrites&&(h=!0)}h||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&yf(this))?Ke(this,h):(Ma(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(i),!((r=this.dragging)===null||r===void 0)&&r.node&&!i.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,i),u=="reset"?this.dom.scrollTop=0:u=="to selection"?this.scrollToSelection():f&&qd(f)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!(!e||!this.dom.contains(e.nodeType==1?e:e.parentNode))){if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof A){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&Eo(this,t.getBoundingClientRect(),e)}else Eo(this,this.coordsAtPos(this.state.selection.head,1),e)}}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(!e||e.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let r=this.directPlugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let r=this.state.plugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}}else for(let t=0;t<this.pluginViews.length;t++){let r=this.pluginViews[t];r.update&&r.update(this,e)}}updateDraggedNode(e,t){let r=e.node,i=-1;if(this.state.doc.nodeAt(r.from)==r.node)i=r.from;else{let s=r.from+(this.state.doc.content.size-t.doc.content.size);(s>0&&this.state.doc.nodeAt(s))==r.node&&(i=s)}this.dragging=new Fa(e.slice,e.move,i<0?void 0:A.create(this.state.doc,i))}someProp(e,t){let r=this._props&&this._props[e],i;if(r!=null&&(i=t?t(r):r))return i;for(let o=0;o<this.directPlugins.length;o++){let l=this.directPlugins[o].props[e];if(l!=null&&(i=t?t(l):l))return i}let s=this.state.plugins;if(s)for(let o=0;o<s.length;o++){let l=s[o].props[e];if(l!=null&&(i=t?t(l):l))return i}}hasFocus(){if(be){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&Jd(this.dom),Ke(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return Zd(this,e)}coordsAtPos(e,t=1){return ga(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,r=-1){let i=this.docView.posFromDOM(e,t,r);if(i==null)throw new RangeError("DOM position not inside the editor");return i}endOfTextblock(e,t){return sf(this,t||this.state,e)}pasteHTML(e,t){return Sn(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return Sn(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return ps(this,e)}destroy(){this.docView&&(Pf(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],gi(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,Ld())}get isDestroyed(){return this.docView==null}dispatchEvent(e){return Bf(this,e)}domSelectionRange(){let e=this.domSelection();return e?he&&this.root.nodeType===11&&Hd(this.dom.ownerDocument)==this.dom&&sh(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}ja.prototype.dispatch=function(n){let e=this._props.dispatchTransaction;e?e.call(this,n):this.updateState(this.state.apply(n))};function Zo(n){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(n.editable),n.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(n.state)),t)for(let r in t)r=="class"?e.class+=" "+t[r]:r=="style"?e.style=(e.style?e.style+";":"")+t[r]:!e[r]&&r!="contenteditable"&&r!="nodeName"&&(e[r]=String(t[r]))}),e.translate||(e.translate="no"),[fe.node(0,n.state.doc.content.size,e)]}function el(n){if(n.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),n.cursorWrapper={dom:e,deco:fe.widget(n.state.selection.from,e,{raw:!0,marks:n.markCursor})}}else n.cursorWrapper=null}function tl(n){return!n.someProp("editable",e=>e(n.state)===!1)}function ph(n,e){let t=Math.min(n.$anchor.sharedDepth(n.head),e.$anchor.sharedDepth(e.head));return n.$anchor.start(t)!=e.$anchor.start(t)}function nl(n){let e=Object.create(null);function t(r){for(let i in r)Object.prototype.hasOwnProperty.call(e,i)||(e[i]=r[i])}return n.someProp("nodeViews",t),n.someProp("markViews",t),e}function mh(n,e){let t=0,r=0;for(let i in n){if(n[i]!=e[i])return!0;t++}for(let i in e)r++;return t!=r}function rl(n){if(n.spec.state||n.spec.filterTransaction||n.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}var dt={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},lr={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},gh=typeof navigator!="undefined"&&/Mac/.test(navigator.platform),yh=typeof navigator!="undefined"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var le=0;le<10;le++)dt[48+le]=dt[96+le]=String(le);for(var le=1;le<=24;le++)dt[le+111]="F"+le;for(var le=65;le<=90;le++)dt[le]=String.fromCharCode(le+32),lr[le]=String.fromCharCode(le);for(var bi in dt)lr.hasOwnProperty(bi)||(lr[bi]=dt[bi]);function bh(n){var e=gh&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||yh&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?lr:dt)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}const kh=typeof navigator!="undefined"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),xh=typeof navigator!="undefined"&&/Win/.test(navigator.platform);function Sh(n){let e=n.split(/-(?!$)/),t=e[e.length-1];t=="Space"&&(t=" ");let r,i,s,o;for(let l=0;l<e.length-1;l++){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))kh?o=!0:i=!0;else throw new Error("Unrecognized modifier name: "+a)}return r&&(t="Alt-"+t),i&&(t="Ctrl-"+t),o&&(t="Meta-"+t),s&&(t="Shift-"+t),t}function vh(n){let e=Object.create(null);for(let t in n)e[Sh(t)]=n[t];return e}function ki(n,e,t=!0){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t&&e.shiftKey&&(n="Shift-"+n),n}function Ch(n){return new Q({props:{handleKeyDown:Wa(n)}})}function Wa(n){let e=vh(n);return function(t,r){let i=bh(r),s,o=e[ki(i,r)];if(o&&o(t.state,t.dispatch,t))return!0;if(i.length==1&&i!=" "){if(r.shiftKey){let l=e[ki(i,r,!1)];if(l&&l(t.state,t.dispatch,t))return!0}if((r.altKey||r.metaKey||r.ctrlKey)&&!(xh&&r.ctrlKey&&r.altKey)&&(s=dt[r.keyCode])&&s!=i){let l=e[ki(s,r)];if(l&&l(t.state,t.dispatch,t))return!0}}return!1}}const xs=(n,e)=>n.selection.empty?!1:(e&&e(n.tr.deleteSelection().scrollIntoView()),!0);function Ka(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("backward",n):t.parentOffset>0)?null:t}const Ua=(n,e,t)=>{let r=Ka(n,t);if(!r)return!1;let i=Ss(r);if(!i){let o=r.blockRange(),l=o&&Yt(o);return l==null?!1:(e&&e(n.tr.lift(o,l).scrollIntoView()),!0)}let s=i.nodeBefore;if(nc(n,i,e,-1))return!0;if(r.parent.content.size==0&&(Jt(s,"end")||A.isSelectable(s)))for(let o=r.depth;;o--){let l=Vr(n.doc,r.before(o),r.after(o),w.empty);if(l&&l.slice.size<l.to-l.from){if(e){let a=n.tr.step(l);a.setSelection(Jt(s,"end")?P.findFrom(a.doc.resolve(a.mapping.map(i.pos,-1)),-1):A.create(a.doc,i.pos-s.nodeSize)),e(a.scrollIntoView())}return!0}if(o==1||r.node(o-1).childCount>1)break}return s.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos-s.nodeSize,i.pos).scrollIntoView()),!0):!1},Mh=(n,e,t)=>{let r=Ka(n,t);if(!r)return!1;let i=Ss(r);return i?qa(n,i,e):!1},wh=(n,e,t)=>{let r=Ga(n,t);if(!r)return!1;let i=vs(r);return i?qa(n,i,e):!1};function qa(n,e,t){let r=e.nodeBefore,i=r,s=e.pos-1;for(;!i.isTextblock;s--){if(i.type.spec.isolating)return!1;let u=i.lastChild;if(!u)return!1;i=u}let o=e.nodeAfter,l=o,a=e.pos+1;for(;!l.isTextblock;a++){if(l.type.spec.isolating)return!1;let u=l.firstChild;if(!u)return!1;l=u}let c=Vr(n.doc,s,a,w.empty);if(!c||c.from!=s||c instanceof te&&c.slice.size>=a-s)return!1;if(t){let u=n.tr.step(c);u.setSelection(R.create(u.doc,s)),t(u.scrollIntoView())}return!0}function Jt(n,e,t=!1){for(let r=n;r;r=e=="start"?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(t&&r.childCount!=1)return!1}return!1}const Ja=(n,e,t)=>{let{$head:r,empty:i}=n.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("backward",n):r.parentOffset>0)return!1;s=Ss(r)}let o=s&&s.nodeBefore;return!o||!A.isSelectable(o)?!1:(e&&e(n.tr.setSelection(A.create(n.doc,s.pos-o.nodeSize)).scrollIntoView()),!0)};function Ss(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){if(n.index(e)>0)return n.doc.resolve(n.before(e+1));if(n.node(e).type.spec.isolating)break}return null}function Ga(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("forward",n):t.parentOffset<t.parent.content.size)?null:t}const Ya=(n,e,t)=>{let r=Ga(n,t);if(!r)return!1;let i=vs(r);if(!i)return!1;let s=i.nodeAfter;if(nc(n,i,e,1))return!0;if(r.parent.content.size==0&&(Jt(s,"start")||A.isSelectable(s))){let o=Vr(n.doc,r.before(),r.after(),w.empty);if(o&&o.slice.size<o.to-o.from){if(e){let l=n.tr.step(o);l.setSelection(Jt(s,"start")?P.findFrom(l.doc.resolve(l.mapping.map(i.pos)),1):A.create(l.doc,l.mapping.map(i.pos))),e(l.scrollIntoView())}return!0}}return s.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos,i.pos+s.nodeSize).scrollIntoView()),!0):!1},Xa=(n,e,t)=>{let{$head:r,empty:i}=n.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("forward",n):r.parentOffset<r.parent.content.size)return!1;s=vs(r)}let o=s&&s.nodeAfter;return!o||!A.isSelectable(o)?!1:(e&&e(n.tr.setSelection(A.create(n.doc,s.pos)).scrollIntoView()),!0)};function vs(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){let t=n.node(e);if(n.index(e)+1<t.childCount)return n.doc.resolve(n.after(e+1));if(t.type.spec.isolating)break}return null}const Th=(n,e)=>{let t=n.selection,r=t instanceof A,i;if(r){if(t.node.isTextblock||!pt(n.doc,t.from))return!1;i=t.from}else if(i=_r(n.doc,t.from,-1),i==null)return!1;if(e){let s=n.tr.join(i);r&&s.setSelection(A.create(s.doc,i-n.doc.resolve(i).nodeBefore.nodeSize)),e(s.scrollIntoView())}return!0},Eh=(n,e)=>{let t=n.selection,r;if(t instanceof A){if(t.node.isTextblock||!pt(n.doc,t.to))return!1;r=t.to}else if(r=_r(n.doc,t.to,1),r==null)return!1;return e&&e(n.tr.join(r).scrollIntoView()),!0},Ah=(n,e)=>{let{$from:t,$to:r}=n.selection,i=t.blockRange(r),s=i&&Yt(i);return s==null?!1:(e&&e(n.tr.lift(i,s).scrollIntoView()),!0)},Qa=(n,e)=>{let{$head:t,$anchor:r}=n.selection;return!t.parent.type.spec.code||!t.sameParent(r)?!1:(e&&e(n.tr.insertText(`
`).scrollIntoView()),!0)};function Cs(n){for(let e=0;e<n.edgeCount;e++){let{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}const Za=(n,e)=>{let{$head:t,$anchor:r}=n.selection;if(!t.parent.type.spec.code||!t.sameParent(r))return!1;let i=t.node(-1),s=t.indexAfter(-1),o=Cs(i.contentMatchAt(s));if(!o||!i.canReplaceWith(s,s,o))return!1;if(e){let l=t.after(),a=n.tr.replaceWith(l,l,o.createAndFill());a.setSelection(P.near(a.doc.resolve(l),1)),e(a.scrollIntoView())}return!0},ec=(n,e)=>{let t=n.selection,{$from:r,$to:i}=t;if(t instanceof ye||r.parent.inlineContent||i.parent.inlineContent)return!1;let s=Cs(i.parent.contentMatchAt(i.indexAfter()));if(!s||!s.isTextblock)return!1;if(e){let o=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,l=n.tr.insert(o,s.createAndFill());l.setSelection(R.create(l.doc,o+1)),e(l.scrollIntoView())}return!0},tc=(n,e)=>{let{$cursor:t}=n.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let s=t.before();if(We(n.doc,s))return e&&e(n.tr.split(s).scrollIntoView()),!0}let r=t.blockRange(),i=r&&Yt(r);return i==null?!1:(e&&e(n.tr.lift(r,i).scrollIntoView()),!0)};function Oh(n){return(e,t)=>{let{$from:r,$to:i}=e.selection;if(e.selection instanceof A&&e.selection.node.isBlock)return!r.parentOffset||!We(e.doc,r.pos)?!1:(t&&t(e.tr.split(r.pos).scrollIntoView()),!0);if(!r.depth)return!1;let s=[],o,l,a=!1,c=!1;for(let h=r.depth;;h--)if(r.node(h).isBlock){a=r.end(h)==r.pos+(r.depth-h),c=r.start(h)==r.pos-(r.depth-h),l=Cs(r.node(h-1).contentMatchAt(r.indexAfter(h-1)));let m=n&&n(i.parent,a,r);s.unshift(m||(a&&l?{type:l}:null)),o=h;break}else{if(h==1)return!1;s.unshift(null)}let u=e.tr;(e.selection instanceof R||e.selection instanceof ye)&&u.deleteSelection();let d=u.mapping.map(r.pos),f=We(u.doc,d,s.length,s);if(f||(s[0]=l?{type:l}:null,f=We(u.doc,d,s.length,s)),!f)return!1;if(u.split(d,s.length,s),!a&&c&&r.node(o).type!=l){let h=u.mapping.map(r.before(o)),p=u.doc.resolve(h);l&&r.node(o-1).canReplaceWith(p.index(),p.index()+1,l)&&u.setNodeMarkup(u.mapping.map(r.before(o)),l)}return t&&t(u.scrollIntoView()),!0}}const Nh=Oh();const Dh=(n,e)=>{let{$from:t,to:r}=n.selection,i,s=t.sharedDepth(r);return s==0?!1:(i=t.before(s),e&&e(n.tr.setSelection(A.create(n.doc,i))),!0)},Ih=(n,e)=>(e&&e(n.tr.setSelection(new ye(n.doc))),!0);function Rh(n,e,t){let r=e.nodeBefore,i=e.nodeAfter,s=e.index();return!r||!i||!r.type.compatibleContent(i.type)?!1:!r.content.size&&e.parent.canReplace(s-1,s)?(t&&t(n.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),!0):!e.parent.canReplace(s,s+1)||!(i.isTextblock||pt(n.doc,e.pos))?!1:(t&&t(n.tr.join(e.pos).scrollIntoView()),!0)}function nc(n,e,t,r){let i=e.nodeBefore,s=e.nodeAfter,o,l,a=i.type.spec.isolating||s.type.spec.isolating;if(!a&&Rh(n,e,t))return!0;let c=!a&&e.parent.canReplace(e.index(),e.index()+1);if(c&&(o=(l=i.contentMatchAt(i.childCount)).findWrapping(s.type))&&l.matchType(o[0]||s.type).validEnd){if(t){let h=e.pos+s.nodeSize,p=S.empty;for(let b=o.length-1;b>=0;b--)p=S.from(o[b].create(null,p));p=S.from(i.copy(p));let m=n.tr.step(new ne(e.pos-1,h,e.pos,h,new w(p,1,0),o.length,!0)),g=m.doc.resolve(h+2*o.length);g.nodeAfter&&g.nodeAfter.type==i.type&&pt(m.doc,g.pos)&&m.join(g.pos),t(m.scrollIntoView())}return!0}let u=s.type.spec.isolating||r>0&&a?null:P.findFrom(e,1),d=u&&u.$from.blockRange(u.$to),f=d&&Yt(d);if(f!=null&&f>=e.depth)return t&&t(n.tr.lift(d,f).scrollIntoView()),!0;if(c&&Jt(s,"start",!0)&&Jt(i,"end")){let h=i,p=[];for(;p.push(h),!h.isTextblock;)h=h.lastChild;let m=s,g=1;for(;!m.isTextblock;m=m.firstChild)g++;if(h.canReplace(h.childCount,h.childCount,m.content)){if(t){let b=S.empty;for(let D=p.length-1;D>=0;D--)b=S.from(p[D].copy(b));let v=n.tr.step(new ne(e.pos-p.length,e.pos+s.nodeSize,e.pos+g,e.pos+s.nodeSize-g,new w(b,p.length,0),0,!0));t(v.scrollIntoView())}return!0}}return!1}function rc(n){return function(e,t){let r=e.selection,i=n<0?r.$from:r.$to,s=i.depth;for(;i.node(s).isInline;){if(!s)return!1;s--}return i.node(s).isTextblock?(t&&t(e.tr.setSelection(R.create(e.doc,n<0?i.start(s):i.end(s)))),!0):!1}}const ic=rc(-1),sc=rc(1);function Ph(n,e=null){return function(t,r){let{$from:i,$to:s}=t.selection,o=i.blockRange(s),l=o&&cs(o,n,e);return l?(r&&r(t.tr.wrap(o,l).scrollIntoView()),!0):!1}}function il(n,e=null){return function(t,r){let i=!1;for(let s=0;s<t.selection.ranges.length&&!i;s++){let{$from:{pos:o},$to:{pos:l}}=t.selection.ranges[s];t.doc.nodesBetween(o,l,(a,c)=>{if(i)return!1;if(!(!a.isTextblock||a.hasMarkup(n,e)))if(a.type==n)i=!0;else{let u=t.doc.resolve(c),d=u.index();i=u.parent.canReplaceWith(d,d+1,n)}})}if(!i)return!1;if(r){let s=t.tr;for(let o=0;o<t.selection.ranges.length;o++){let{$from:{pos:l},$to:{pos:a}}=t.selection.ranges[o];s.setBlockType(l,a,n,e)}r(s.scrollIntoView())}return!0}}function Ms(...n){return function(e,t,r){for(let i=0;i<n.length;i++)if(n[i](e,t,r))return!0;return!1}}let xi=Ms(xs,Ua,Ja),sl=Ms(xs,Ya,Xa);const Ze={Enter:Ms(Qa,ec,tc,Nh),"Mod-Enter":Za,Backspace:xi,"Mod-Backspace":xi,"Shift-Backspace":xi,Delete:sl,"Mod-Delete":sl,"Mod-a":Ih},Lh={"Ctrl-h":Ze.Backspace,"Alt-Backspace":Ze["Mod-Backspace"],"Ctrl-d":Ze.Delete,"Ctrl-Alt-Backspace":Ze["Mod-Delete"],"Alt-Delete":Ze["Mod-Delete"],"Alt-d":Ze["Mod-Delete"],"Ctrl-a":ic,"Ctrl-e":sc};for(let n in Ze)Lh[n]=Ze[n];const l0=typeof navigator!="undefined"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os!="undefined"&&os.platform?os.platform()=="darwin":!1;function Bh(n,e=null){return function(t,r){let{$from:i,$to:s}=t.selection,o=i.blockRange(s);if(!o)return!1;let l=r?t.tr:null;return zh(l,o,n,e)?(r&&r(l.scrollIntoView()),!0):!1}}function zh(n,e,t,r=null){let i=!1,s=e,o=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(t)&&e.startIndex==0){if(e.$from.index(e.depth-1)==0)return!1;let a=o.resolve(e.start-2);s=new er(a,a,e.depth),e.endIndex<e.parent.childCount&&(e=new er(e.$from,o.resolve(e.$to.end(e.depth)),e.depth)),i=!0}let l=cs(s,t,r,e);return l?(n&&Fh(n,e,l,i,t),!0):!1}function Fh(n,e,t,r,i){let s=S.empty;for(let u=t.length-1;u>=0;u--)s=S.from(t[u].type.create(t[u].attrs,s));n.step(new ne(e.start-(r?2:0),e.end,e.start,e.end,new w(s,0,0),t.length,!0));let o=0;for(let u=0;u<t.length;u++)t[u].type==i&&(o=u+1);let l=t.length-o,a=e.start+t.length-(r?2:0),c=e.parent;for(let u=e.startIndex,d=e.endIndex,f=!0;u<d;u++,f=!1)!f&&We(n.doc,a,l)&&(n.split(a,l),a+=2*l),a+=c.child(u).nodeSize;return n}function $h(n){return function(e,t){let{$from:r,$to:i}=e.selection,s=r.blockRange(i,o=>o.childCount>0&&o.firstChild.type==n);return s?t?r.node(s.depth-1).type==n?Hh(e,t,n,s):_h(e,t,s):!0:!1}}function Hh(n,e,t,r){let i=n.tr,s=r.end,o=r.$to.end(r.depth);s<o&&(i.step(new ne(s-1,o,s,o,new w(S.from(t.create(null,r.parent.copy())),1,0),1,!0)),r=new er(i.doc.resolve(r.$from.pos),i.doc.resolve(o),r.depth));const l=Yt(r);if(l==null)return!1;i.lift(r,l);let a=i.doc.resolve(i.mapping.map(s,-1)-1);return pt(i.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&i.join(a.pos),e(i.scrollIntoView()),!0}function _h(n,e,t){let r=n.tr,i=t.parent;for(let h=t.end,p=t.endIndex-1,m=t.startIndex;p>m;p--)h-=i.child(p).nodeSize,r.delete(h-1,h+1);let s=r.doc.resolve(t.start),o=s.nodeAfter;if(r.mapping.map(t.end)!=t.start+s.nodeAfter.nodeSize)return!1;let l=t.startIndex==0,a=t.endIndex==i.childCount,c=s.node(-1),u=s.index(-1);if(!c.canReplace(u+(l?0:1),u+1,o.content.append(a?S.empty:S.from(i))))return!1;let d=s.pos,f=d+o.nodeSize;return r.step(new ne(d-(l?1:0),f+(a?1:0),d+1,f-1,new w((l?S.empty:S.from(i.copy(S.empty))).append(a?S.empty:S.from(i.copy(S.empty))),l?0:1,a?0:1),l?0:1)),e(r.scrollIntoView()),!0}function Vh(n){return function(e,t){let{$from:r,$to:i}=e.selection,s=r.blockRange(i,c=>c.childCount>0&&c.firstChild.type==n);if(!s)return!1;let o=s.startIndex;if(o==0)return!1;let l=s.parent,a=l.child(o-1);if(a.type!=n)return!1;if(t){let c=a.lastChild&&a.lastChild.type==l.type,u=S.from(c?n.create():null),d=new w(S.from(n.create(null,S.from(l.type.create(null,u)))),c?3:1,0),f=s.start,h=s.end;t(e.tr.step(new ne(f-(c?3:1),h,f,h,d,1,!0)).scrollIntoView())}return!0}}var jh=Object.defineProperty,oc=(n,e)=>{for(var t in e)jh(n,t,{get:e[t],enumerable:!0})};function qr(n){const{state:e,transaction:t}=n;let{selection:r}=t,{doc:i}=t,{storedMarks:s}=t;return U(x({},e),{apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return s},get selection(){return r},get doc(){return i},get tr(){return r=t.selection,i=t.doc,s=t.storedMarks,t}})}var Jr=class{constructor(n){this.editor=n.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=n.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:n,editor:e,state:t}=this,{view:r}=e,{tr:i}=t,s=this.buildProps(i);return Object.fromEntries(Object.entries(n).map(([o,l])=>[o,(...c)=>{const u=l(...c)(s);return!i.getMeta("preventDispatch")&&!this.hasCustomState&&r.dispatch(i),u}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(n,e=!0){const{rawCommands:t,editor:r,state:i}=this,{view:s}=r,o=[],l=!!n,a=n||i.tr,c=()=>(!l&&e&&!a.getMeta("preventDispatch")&&!this.hasCustomState&&s.dispatch(a),o.every(d=>d===!0)),u=U(x({},Object.fromEntries(Object.entries(t).map(([d,f])=>[d,(...p)=>{const m=this.buildProps(a,e),g=f(...p)(m);return o.push(g),u}]))),{run:c});return u}createCan(n){const{rawCommands:e,state:t}=this,r=!1,i=n||t.tr,s=this.buildProps(i,r),o=Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...c)=>a(...c)(U(x({},s),{dispatch:void 0}))]));return U(x({},o),{chain:()=>this.createChain(i,r)})}buildProps(n,e=!0){const{rawCommands:t,editor:r,state:i}=this,{view:s}=r,o={tr:n,editor:r,view:s,state:qr({state:i,transaction:n}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(n,e),can:()=>this.createCan(n),get commands(){return Object.fromEntries(Object.entries(t).map(([l,a])=>[l,(...c)=>a(...c)(o)]))}};return o}},Wh=class{constructor(){this.callbacks={}}on(n,e){return this.callbacks[n]||(this.callbacks[n]=[]),this.callbacks[n].push(e),this}emit(n,...e){const t=this.callbacks[n];return t&&t.forEach(r=>r.apply(this,e)),this}off(n,e){const t=this.callbacks[n];return t&&(e?this.callbacks[n]=t.filter(r=>r!==e):delete this.callbacks[n]),this}once(n,e){const t=(...r)=>{this.off(n,t),e.apply(this,r)};return this.on(n,t)}removeAllListeners(){this.callbacks={}}};function lc(n,e){const t=new la(n);return e.forEach(r=>{r.steps.forEach(i=>{t.step(i)})}),t}var ac=n=>{const e=n.childNodes;for(let t=e.length-1;t>=0;t-=1){const r=e[t];r.nodeType===3&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?n.removeChild(r):r.nodeType===1&&ac(r)}return n};function Fn(n){if(typeof window=="undefined")throw new Error("[tiptap error]: there is no window object available, so this function cannot be used");const e=`<body>${n}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return ac(t)}function Cn(n,e,t){if(n instanceof Ne||n instanceof S)return n;t=x({slice:!0,parseOptions:{}},t);const r=typeof n=="object"&&n!==null,i=typeof n=="string";if(r)try{if(Array.isArray(n)&&n.length>0)return S.fromArray(n.map(l=>e.nodeFromJSON(l)));const o=e.nodeFromJSON(n);return t.errorOnInvalidContent&&o.check(),o}catch(s){if(t.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:s});return Cn("",e,t)}if(i){if(t.errorOnInvalidContent){let o=!1,l="";const a=new Kl({topNode:e.spec.topNode,marks:e.spec.marks,nodes:e.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:c=>(o=!0,l=typeof c=="string"?c:c.outerHTML,null)}]}})});if(t.slice?lt.fromSchema(a).parseSlice(Fn(n),t.parseOptions):lt.fromSchema(a).parse(Fn(n),t.parseOptions),t.errorOnInvalidContent&&o)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${l}`)})}const s=lt.fromSchema(e);return t.slice?s.parseSlice(Fn(n),t.parseOptions).content:s.parse(Fn(n),t.parseOptions)}return Cn("",e,t)}function qi(n,e,t={},r={}){return Cn(n,e,{slice:!1,parseOptions:t,errorOnInvalidContent:r.errorOnInvalidContent})}function Kh(n){for(let e=0;e<n.edgeCount;e+=1){const{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}function Uh(n,e,t){const r=[];return n.nodesBetween(e.from,e.to,(i,s)=>{t(i)&&r.push({node:i,pos:s})}),r}function qh(n,e){for(let t=n.depth;t>0;t-=1){const r=n.node(t);if(e(r))return{pos:t>0?n.before(t):0,start:n.start(t),depth:t,node:r}}}function Gr(n){return e=>qh(e.$from,n)}function E(n,e,t){return n.config[e]===void 0&&n.parent?E(n.parent,e,t):typeof n.config[e]=="function"?n.config[e].bind(U(x({},t),{parent:n.parent?E(n.parent,e,t):null})):n.config[e]}function ws(n){return n.map(e=>{const t={name:e.name,options:e.options,storage:e.storage},r=E(e,"addExtensions",t);return r?[e,...ws(r())]:e}).flat(10)}function Ts(n,e){const t=Pt.fromSchema(e).serializeFragment(n),i=document.implementation.createHTMLDocument().createElement("div");return i.appendChild(t),i.innerHTML}function cc(n){return typeof n=="function"}function _(n,e=void 0,...t){return cc(n)?e?n.bind(e)(...t):n(...t):n}function Jh(n={}){return Object.keys(n).length===0&&n.constructor===Object}function Mn(n){const e=n.filter(i=>i.type==="extension"),t=n.filter(i=>i.type==="node"),r=n.filter(i=>i.type==="mark");return{baseExtensions:e,nodeExtensions:t,markExtensions:r}}function uc(n){const e=[],{nodeExtensions:t,markExtensions:r}=Mn(n),i=[...t,...r],s={default:null,validate:void 0,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return n.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage,extensions:i},a=E(o,"addGlobalAttributes",l);if(!a)return;a().forEach(u=>{u.types.forEach(d=>{Object.entries(u.attributes).forEach(([f,h])=>{e.push({type:d,name:f,attribute:x(x({},s),h)})})})})}),i.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage},a=E(o,"addAttributes",l);if(!a)return;const c=a();Object.entries(c).forEach(([u,d])=>{const f=x(x({},s),d);typeof(f==null?void 0:f.default)=="function"&&(f.default=f.default()),f!=null&&f.isRequired&&(f==null?void 0:f.default)===void 0&&delete f.default,e.push({type:o.name,name:u,attribute:f})})}),e}function J(...n){return n.filter(e=>!!e).reduce((e,t)=>{const r=x({},e);return Object.entries(t).forEach(([i,s])=>{if(!r[i]){r[i]=s;return}if(i==="class"){const l=s?String(s).split(" "):[],a=r[i]?r[i].split(" "):[],c=l.filter(u=>!a.includes(u));r[i]=[...a,...c].join(" ")}else if(i==="style"){const l=s?s.split(";").map(u=>u.trim()).filter(Boolean):[],a=r[i]?r[i].split(";").map(u=>u.trim()).filter(Boolean):[],c=new Map;a.forEach(u=>{const[d,f]=u.split(":").map(h=>h.trim());c.set(d,f)}),l.forEach(u=>{const[d,f]=u.split(":").map(h=>h.trim());c.set(d,f)}),r[i]=Array.from(c.entries()).map(([u,d])=>`${u}: ${d}`).join("; ")}else r[i]=s}),r},{})}function ar(n,e){return e.filter(t=>t.type===n.type.name).filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(n.attrs)||{}:{[t.name]:n.attrs[t.name]}).reduce((t,r)=>J(t,r),{})}function Gh(n){return typeof n!="string"?n:n.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(n):n==="true"?!0:n==="false"?!1:n}function ol(n,e){return"style"in n?n:U(x({},n),{getAttrs:t=>{const r=n.getAttrs?n.getAttrs(t):n.attrs;if(r===!1)return!1;const i=e.reduce((s,o)=>{const l=o.attribute.parseHTML?o.attribute.parseHTML(t):Gh(t.getAttribute(o.name));return l==null?s:U(x({},s),{[o.name]:l})},{});return x(x({},r),i)}})}function ll(n){return Object.fromEntries(Object.entries(n).filter(([e,t])=>e==="attrs"&&Jh(t)?!1:t!=null))}function Yh(n,e){var t;const r=uc(n),{nodeExtensions:i,markExtensions:s}=Mn(n),o=(t=i.find(c=>E(c,"topNode")))==null?void 0:t.name,l=Object.fromEntries(i.map(c=>{const u=r.filter(b=>b.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=n.reduce((b,v)=>{const D=E(v,"extendNodeSchema",d);return x(x({},b),D?D(c):{})},{}),h=ll(U(x({},f),{content:_(E(c,"content",d)),marks:_(E(c,"marks",d)),group:_(E(c,"group",d)),inline:_(E(c,"inline",d)),atom:_(E(c,"atom",d)),selectable:_(E(c,"selectable",d)),draggable:_(E(c,"draggable",d)),code:_(E(c,"code",d)),whitespace:_(E(c,"whitespace",d)),linebreakReplacement:_(E(c,"linebreakReplacement",d)),defining:_(E(c,"defining",d)),isolating:_(E(c,"isolating",d)),attrs:Object.fromEntries(u.map(b=>{var v,D;return[b.name,{default:(v=b==null?void 0:b.attribute)==null?void 0:v.default,validate:(D=b==null?void 0:b.attribute)==null?void 0:D.validate}]}))})),p=_(E(c,"parseHTML",d));p&&(h.parseDOM=p.map(b=>ol(b,u)));const m=E(c,"renderHTML",d);m&&(h.toDOM=b=>m({node:b,HTMLAttributes:ar(b,u)}));const g=E(c,"renderText",d);return g&&(h.toText=g),[c.name,h]})),a=Object.fromEntries(s.map(c=>{const u=r.filter(g=>g.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=n.reduce((g,b)=>{const v=E(b,"extendMarkSchema",d);return x(x({},g),v?v(c):{})},{}),h=ll(U(x({},f),{inclusive:_(E(c,"inclusive",d)),excludes:_(E(c,"excludes",d)),group:_(E(c,"group",d)),spanning:_(E(c,"spanning",d)),code:_(E(c,"code",d)),attrs:Object.fromEntries(u.map(g=>{var b,v;return[g.name,{default:(b=g==null?void 0:g.attribute)==null?void 0:b.default,validate:(v=g==null?void 0:g.attribute)==null?void 0:v.validate}]}))})),p=_(E(c,"parseHTML",d));p&&(h.parseDOM=p.map(g=>ol(g,u)));const m=E(c,"renderHTML",d);return m&&(h.toDOM=g=>m({mark:g,HTMLAttributes:ar(g,u)})),[c.name,h]}));return new Kl({topNode:o,nodes:l,marks:a})}function Xh(n){const e=n.filter((t,r)=>n.indexOf(t)!==r);return Array.from(new Set(e))}function Es(n){return n.sort((t,r)=>{const i=E(t,"priority")||100,s=E(r,"priority")||100;return i>s?-1:i<s?1:0})}function dc(n){const e=Es(ws(n));return Xh(e.map(r=>r.name)).length,e}function fc(n,e,t){const{from:r,to:i}=e,{blockSeparator:s=`

`,textSerializers:o={}}=t||{};let l="";return n.nodesBetween(r,i,(a,c,u,d)=>{var f;a.isBlock&&c>r&&(l+=s);const h=o==null?void 0:o[a.type.name];if(h)return u&&(l+=h({node:a,pos:c,parent:u,index:d,range:e})),!1;a.isText&&(l+=(f=a==null?void 0:a.text)==null?void 0:f.slice(Math.max(r,c)-c,i-c))}),l}function Qh(n,e){const t={from:0,to:n.content.size};return fc(n,t,e)}function hc(n){return Object.fromEntries(Object.entries(n.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}function Ue(n,e){if(typeof n=="string"){if(!e.marks[n])throw Error(`There is no mark type named '${n}'. Maybe you forgot to add the extension?`);return e.marks[n]}return n}function pc(n,e){const t=Ue(e,n.schema),{from:r,to:i,empty:s}=n.selection,o=[];s?(n.storedMarks&&o.push(...n.storedMarks),o.push(...n.selection.$head.marks())):n.doc.nodesBetween(r,i,a=>{o.push(...a.marks)});const l=o.find(a=>a.type.name===t.name);return l?x({},l.attrs):{}}function ee(n,e){if(typeof n=="string"){if(!e.nodes[n])throw Error(`There is no node type named '${n}'. Maybe you forgot to add the extension?`);return e.nodes[n]}return n}function Zh(n,e){const t=ee(e,n.schema),{from:r,to:i}=n.selection,s=[];n.doc.nodesBetween(r,i,l=>{s.push(l)});const o=s.reverse().find(l=>l.type.name===t.name);return o?x({},o.attrs):{}}function Yr(n,e){return e.nodes[n]?"node":e.marks[n]?"mark":null}function mc(n,e){const t=Yr(typeof e=="string"?e:e.name,n.schema);return t==="node"?Zh(n,e):t==="mark"?pc(n,e):{}}function ep(n,e=JSON.stringify){const t={};return n.filter(r=>{const i=e(r);return Object.prototype.hasOwnProperty.call(t,i)?!1:t[i]=!0})}function tp(n){const e=ep(n);return e.length===1?e:e.filter((t,r)=>!e.filter((s,o)=>o!==r).some(s=>t.oldRange.from>=s.oldRange.from&&t.oldRange.to<=s.oldRange.to&&t.newRange.from>=s.newRange.from&&t.newRange.to<=s.newRange.to))}function gc(n){const{mapping:e,steps:t}=n,r=[];return e.maps.forEach((i,s)=>{const o=[];if(i.ranges.length)i.forEach((l,a)=>{o.push({from:l,to:a})});else{const{from:l,to:a}=t[s];if(l===void 0||a===void 0)return;o.push({from:l,to:a})}o.forEach(({from:l,to:a})=>{const c=e.slice(s).map(l,-1),u=e.slice(s).map(a),d=e.invert().map(c,-1),f=e.invert().map(u);r.push({oldRange:{from:d,to:f},newRange:{from:c,to:u}})})}),tp(r)}function As(n){return Object.prototype.toString.call(n)==="[object RegExp]"}function cr(n,e,t={strict:!0}){const r=Object.keys(e);return r.length?r.every(i=>t.strict?e[i]===n[i]:As(e[i])?e[i].test(n[i]):e[i]===n[i]):!0}function yc(n,e,t={}){return n.find(r=>r.type===e&&cr(Object.fromEntries(Object.keys(t).map(i=>[i,r.attrs[i]])),t))}function al(n,e,t={}){return!!yc(n,e,t)}function Os(n,e,t){var r;if(!n||!e)return;let i=n.parent.childAfter(n.parentOffset);if((!i.node||!i.node.marks.some(u=>u.type===e))&&(i=n.parent.childBefore(n.parentOffset)),!i.node||!i.node.marks.some(u=>u.type===e)||(t=t||((r=i.node.marks[0])==null?void 0:r.attrs),!yc([...i.node.marks],e,t)))return;let o=i.index,l=n.start()+i.offset,a=o+1,c=l+i.node.nodeSize;for(;o>0&&al([...n.parent.child(o-1).marks],e,t);)o-=1,l-=n.parent.child(o).nodeSize;for(;a<n.parent.childCount&&al([...n.parent.child(a).marks],e,t);)c+=n.parent.child(a).nodeSize,a+=1;return{from:l,to:c}}function Ns(n,e,t){const r=[];return n===e?t.resolve(n).marks().forEach(i=>{const s=t.resolve(n),o=Os(s,i.type);o&&r.push(x({mark:i},o))}):t.nodesBetween(n,e,(i,s)=>{!i||(i==null?void 0:i.nodeSize)===void 0||r.push(...i.marks.map(o=>({from:s,to:s+i.nodeSize,mark:o})))}),r}var np=(n,e,t,r=20)=>{const i=n.doc.resolve(t);let s=r,o=null;for(;s>0&&o===null;){const l=i.node(s);(l==null?void 0:l.type.name)===e?o=l:s-=1}return[o,s]};function Si(n,e){return e.nodes[n]||e.marks[n]||null}function Jn(n,e,t){return Object.fromEntries(Object.entries(t).filter(([r])=>{const i=n.find(s=>s.type===e&&s.name===r);return i?i.attribute.keepOnSplit:!1}))}var rp=(n,e=500)=>{let t="";const r=n.parentOffset;return n.parent.nodesBetween(Math.max(0,r-e),r,(i,s,o,l)=>{var a,c;const u=((c=(a=i.type.spec).toText)==null?void 0:c.call(a,{node:i,pos:s,parent:o,index:l}))||i.textContent||"%leaf%";t+=i.isAtom&&!i.isText?u:u.slice(0,Math.max(0,r-s))}),t};function Ji(n,e,t={}){const{empty:r,ranges:i}=n.selection,s=e?Ue(e,n.schema):null;if(r)return!!(n.storedMarks||n.selection.$from.marks()).filter(d=>s?s.name===d.type.name:!0).find(d=>cr(d.attrs,t,{strict:!1}));let o=0;const l=[];if(i.forEach(({$from:d,$to:f})=>{const h=d.pos,p=f.pos;n.doc.nodesBetween(h,p,(m,g)=>{if(!m.isText&&!m.marks.length)return;const b=Math.max(h,g),v=Math.min(p,g+m.nodeSize),D=v-b;o+=D,l.push(...m.marks.map(F=>({mark:F,from:b,to:v})))})}),o===0)return!1;const a=l.filter(d=>s?s.name===d.mark.type.name:!0).filter(d=>cr(d.mark.attrs,t,{strict:!1})).reduce((d,f)=>d+f.to-f.from,0),c=l.filter(d=>s?d.mark.type!==s&&d.mark.type.excludes(s):!0).reduce((d,f)=>d+f.to-f.from,0);return(a>0?a+c:a)>=o}function ft(n,e,t={}){const{from:r,to:i,empty:s}=n.selection,o=e?ee(e,n.schema):null,l=[];n.doc.nodesBetween(r,i,(d,f)=>{if(d.isText)return;const h=Math.max(r,f),p=Math.min(i,f+d.nodeSize);l.push({node:d,from:h,to:p})});const a=i-r,c=l.filter(d=>o?o.name===d.node.type.name:!0).filter(d=>cr(d.node.attrs,t,{strict:!1}));return s?!!c.length:c.reduce((d,f)=>d+f.to-f.from,0)>=a}function ip(n,e,t={}){if(!e)return ft(n,null,t)||Ji(n,null,t);const r=Yr(e,n.schema);return r==="node"?ft(n,e,t):r==="mark"?Ji(n,e,t):!1}var sp=(n,e)=>{const{$from:t,$to:r,$anchor:i}=n.selection;if(e){const s=Gr(l=>l.type.name===e)(n.selection);if(!s)return!1;const o=n.doc.resolve(s.pos+1);return i.pos+1===o.end()}return!(r.parentOffset<r.parent.nodeSize-2||t.pos!==r.pos)},op=n=>{const{$from:e,$to:t}=n.selection;return!(e.parentOffset>0||e.pos!==t.pos)};function cl(n,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===n.name):e}function ul(n,e){const{nodeExtensions:t}=Mn(e),r=t.find(o=>o.name===n);if(!r)return!1;const i={name:r.name,options:r.options,storage:r.storage},s=_(E(r,"group",i));return typeof s!="string"?!1:s.split(" ").includes("list")}function Xr(n,{checkChildren:e=!0,ignoreWhitespace:t=!1}={}){var r;if(t){if(n.type.name==="hardBreak")return!0;if(n.isText)return/^\s*$/m.test((r=n.text)!=null?r:"")}if(n.isText)return!n.text;if(n.isAtom||n.isLeaf)return!1;if(n.content.childCount===0)return!0;if(e){let i=!0;return n.content.forEach(s=>{i!==!1&&(Xr(s,{ignoreWhitespace:t,checkChildren:e})||(i=!1))}),i}return!1}function bc(n){return n instanceof A}function kc(n){return n instanceof R}function vt(n=0,e=0,t=0){return Math.min(Math.max(n,e),t)}function xc(n,e=null){if(!e)return null;const t=P.atStart(n),r=P.atEnd(n);if(e==="start"||e===!0)return t;if(e==="end")return r;const i=t.from,s=r.to;return e==="all"?R.create(n,vt(0,i,s),vt(n.content.size,i,s)):R.create(n,vt(e,i,s),vt(e,i,s))}function lp(n,e,t){const r=n.steps.length-1;if(r<e)return;const i=n.steps[r];if(!(i instanceof te||i instanceof ne))return;const s=n.mapping.maps[r];let o=0;s.forEach((l,a,c,u)=>{o===0&&(o=u)}),n.setSelection(P.near(n.doc.resolve(o),t))}var Qr=class{constructor(n){this.find=n.find,this.handler=n.handler}},ap=(n,e)=>{if(As(e))return e.exec(n);const t=e(n);if(!t)return null;const r=[t.text];return r.index=t.index,r.input=n,r.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith),r.push(t.replaceWith)),r};function $n(n){var e;const{editor:t,from:r,to:i,text:s,rules:o,plugin:l}=n,{view:a}=t;if(a.composing)return!1;const c=a.state.doc.resolve(r);if(c.parent.type.spec.code||(e=c.nodeBefore||c.nodeAfter)!=null&&e.marks.find(f=>f.type.spec.code))return!1;let u=!1;const d=rp(c)+s;return o.forEach(f=>{if(u)return;const h=ap(d,f.find);if(!h)return;const p=a.state.tr,m=qr({state:a.state,transaction:p}),g={from:r-(h[0].length-s.length),to:i},{commands:b,chain:v,can:D}=new Jr({editor:t,state:m});f.handler({state:m,range:g,match:h,commands:b,chain:v,can:D})===null||!p.steps.length||(p.setMeta(l,{transform:p,from:r,to:i,text:s}),a.dispatch(p),u=!0)}),u}function cp(n){const{editor:e,rules:t}=n,r=new Q({state:{init(){return null},apply(i,s,o){const l=i.getMeta(r);if(l)return l;const a=i.getMeta("applyInputRules");return!!a&&setTimeout(()=>{let{text:u}=a;typeof u=="string"?u=u:u=Ts(S.from(u),o.schema);const{from:d}=a,f=d+u.length;$n({editor:e,from:d,to:f,text:u,rules:t,plugin:r})}),i.selectionSet||i.docChanged?null:s}},props:{handleTextInput(i,s,o,l){return $n({editor:e,from:s,to:o,text:l,rules:t,plugin:r})},handleDOMEvents:{compositionend:i=>(setTimeout(()=>{const{$cursor:s}=i.state.selection;s&&$n({editor:e,from:s.pos,to:s.pos,text:"",rules:t,plugin:r})}),!1)},handleKeyDown(i,s){if(s.key!=="Enter")return!1;const{$cursor:o}=i.state.selection;return o?$n({editor:e,from:o.pos,to:o.pos,text:`
`,rules:t,plugin:r}):!1}},isInputRules:!0});return r}function up(n){return Object.prototype.toString.call(n).slice(8,-1)}function Hn(n){return up(n)!=="Object"?!1:n.constructor===Object&&Object.getPrototypeOf(n)===Object.prototype}function Sc(n,e){const t=x({},n);return Hn(n)&&Hn(e)&&Object.keys(e).forEach(r=>{Hn(e[r])&&Hn(n[r])?t[r]=Sc(n[r],e[r]):t[r]=e[r]}),t}var Ds=class{constructor(n={}){this.type="extendable",this.parent=null,this.child=null,this.name="",this.config={name:this.name},this.config=x(x({},this.config),n),this.name=this.config.name}get options(){return x({},_(E(this,"addOptions",{name:this.name}))||{})}get storage(){return x({},_(E(this,"addStorage",{name:this.name,options:this.options}))||{})}configure(n={}){const e=this.extend(U(x({},this.config),{addOptions:()=>Sc(this.options,n)}));return e.name=this.name,e.parent=this.parent,e}extend(n={}){const e=new this.constructor(x(x({},this.config),n));return e.parent=this,this.child=e,e.name="name"in n?n.name:e.parent.name,e}},qe=class vc extends Ds{constructor(){super(...arguments),this.type="mark"}static create(e={}){const t=typeof e=="function"?e():e;return new vc(t)}static handleExit({editor:e,mark:t}){const{tr:r}=e.state,i=e.state.selection.$from;if(i.pos===i.end()){const o=i.marks();if(!!!o.find(c=>(c==null?void 0:c.type.name)===t.name))return!1;const a=o.find(c=>(c==null?void 0:c.type.name)===t.name);return a&&r.removeStoredMark(a),r.insertText(" ",i.pos),e.view.dispatch(r),!0}return!1}configure(e){return super.configure(e)}extend(e){const t=typeof e=="function"?e():e;return super.extend(t)}};function dp(n){return typeof n=="number"}var fp=class{constructor(n){this.find=n.find,this.handler=n.handler}},hp=(n,e,t)=>{if(As(e))return[...n.matchAll(e)];const r=e(n,t);return r?r.map(i=>{const s=[i.text];return s.index=i.index,s.input=n,s.data=i.data,i.replaceWith&&(i.text.includes(i.replaceWith),s.push(i.replaceWith)),s}):[]};function pp(n){const{editor:e,state:t,from:r,to:i,rule:s,pasteEvent:o,dropEvent:l}=n,{commands:a,chain:c,can:u}=new Jr({editor:e,state:t}),d=[];return t.doc.nodesBetween(r,i,(h,p)=>{if(!h.isTextblock||h.type.spec.code)return;const m=Math.max(r,p),g=Math.min(i,p+h.content.size),b=h.textBetween(m-p,g-p,void 0,"￼");hp(b,s.find,o).forEach(D=>{if(D.index===void 0)return;const F=m+D.index+1,I=F+D[0].length,H={from:t.tr.mapping.map(F),to:t.tr.mapping.map(I)},C=s.handler({state:t,range:H,match:D,commands:a,chain:c,can:u,pasteEvent:o,dropEvent:l});d.push(C)})}),d.every(h=>h!==null)}var _n=null,mp=n=>{var e;const t=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(e=t.clipboardData)==null||e.setData("text/html",n),t};function gp(n){const{editor:e,rules:t}=n;let r=null,i=!1,s=!1,o=typeof ClipboardEvent!="undefined"?new ClipboardEvent("paste"):null,l;try{l=typeof DragEvent!="undefined"?new DragEvent("drop"):null}catch(u){l=null}const a=({state:u,from:d,to:f,rule:h,pasteEvt:p})=>{const m=u.tr,g=qr({state:u,transaction:m});if(!(!pp({editor:e,state:g,from:Math.max(d-1,0),to:f.b-1,rule:h,pasteEvent:p,dropEvent:l})||!m.steps.length)){try{l=typeof DragEvent!="undefined"?new DragEvent("drop"):null}catch(v){l=null}return o=typeof ClipboardEvent!="undefined"?new ClipboardEvent("paste"):null,m}};return t.map(u=>new Q({view(d){const f=p=>{var m;r=(m=d.dom.parentElement)!=null&&m.contains(p.target)?d.dom.parentElement:null,r&&(_n=e)},h=()=>{_n&&(_n=null)};return window.addEventListener("dragstart",f),window.addEventListener("dragend",h),{destroy(){window.removeEventListener("dragstart",f),window.removeEventListener("dragend",h)}}},props:{handleDOMEvents:{drop:(d,f)=>{if(s=r===d.dom.parentElement,l=f,!s){const h=_n;h!=null&&h.isEditable&&setTimeout(()=>{const p=h.state.selection;p&&h.commands.deleteRange({from:p.from,to:p.to})},10)}return!1},paste:(d,f)=>{var h;const p=(h=f.clipboardData)==null?void 0:h.getData("text/html");return o=f,i=!!(p!=null&&p.includes("data-pm-slice")),!1}}},appendTransaction:(d,f,h)=>{const p=d[0],m=p.getMeta("uiEvent")==="paste"&&!i,g=p.getMeta("uiEvent")==="drop"&&!s,b=p.getMeta("applyPasteRules"),v=!!b;if(!m&&!g&&!v)return;if(v){let{text:I}=b;typeof I=="string"?I=I:I=Ts(S.from(I),h.schema);const{from:H}=b,C=H+I.length,y=mp(I);return a({rule:u,state:h,from:H,to:{b:C},pasteEvt:y})}const D=f.doc.content.findDiffStart(h.doc.content),F=f.doc.content.findDiffEnd(h.doc.content);if(!(!dp(D)||!F||D===F.b))return a({rule:u,state:h,from:D,to:F,pasteEvt:o})}}))}var Zr=class{constructor(n,e){this.splittableMarks=[],this.editor=e,this.extensions=dc(n),this.schema=Yh(this.extensions,e),this.setupExtensions()}get commands(){return this.extensions.reduce((n,e)=>{const t={name:e.name,options:e.options,storage:this.editor.extensionStorage[e.name],editor:this.editor,type:Si(e.name,this.schema)},r=E(e,"addCommands",t);return r?x(x({},n),r()):n},{})}get plugins(){const{editor:n}=this,e=Es([...this.extensions].reverse()),t=[],r=[],i=e.map(s=>{const o={name:s.name,options:s.options,storage:this.editor.extensionStorage[s.name],editor:n,type:Si(s.name,this.schema)},l=[],a=E(s,"addKeyboardShortcuts",o);let c={};if(s.type==="mark"&&E(s,"exitable",o)&&(c.ArrowRight=()=>qe.handleExit({editor:n,mark:s})),a){const p=Object.fromEntries(Object.entries(a()).map(([m,g])=>[m,()=>g({editor:n})]));c=x(x({},c),p)}const u=Ch(c);l.push(u);const d=E(s,"addInputRules",o);cl(s,n.options.enableInputRules)&&d&&t.push(...d());const f=E(s,"addPasteRules",o);cl(s,n.options.enablePasteRules)&&f&&r.push(...f());const h=E(s,"addProseMirrorPlugins",o);if(h){const p=h();l.push(...p)}return l}).flat();return[cp({editor:n,rules:t}),...gp({editor:n,rules:r}),...i]}get attributes(){return uc(this.extensions)}get nodeViews(){const{editor:n}=this,{nodeExtensions:e}=Mn(this.extensions);return Object.fromEntries(e.filter(t=>!!E(t,"addNodeView")).map(t=>{const r=this.attributes.filter(l=>l.type===t.name),i={name:t.name,options:t.options,storage:this.editor.extensionStorage[t.name],editor:n,type:ee(t.name,this.schema)},s=E(t,"addNodeView",i);if(!s)return[];const o=(l,a,c,u,d)=>{const f=ar(l,r);return s()({node:l,view:a,getPos:c,decorations:u,innerDecorations:d,editor:n,extension:t,HTMLAttributes:f})};return[t.name,o]}))}get markViews(){const{editor:n}=this,{markExtensions:e}=Mn(this.extensions);return Object.fromEntries(e.filter(t=>!!E(t,"addMarkView")).map(t=>{const r=this.attributes.filter(l=>l.type===t.name),i={name:t.name,options:t.options,storage:this.editor.extensionStorage[t.name],editor:n,type:Ue(t.name,this.schema)},s=E(t,"addMarkView",i);if(!s)return[];const o=(l,a,c)=>{const u=ar(l,r);return s()({mark:l,view:a,inline:c,editor:n,extension:t,HTMLAttributes:u,updateAttributes:d=>{Dm(l,n,d)}})};return[t.name,o]}))}setupExtensions(){const n=this.extensions;this.editor.extensionStorage=Object.fromEntries(n.map(e=>[e.name,e.storage])),n.forEach(e=>{var t;const r={name:e.name,options:e.options,storage:this.editor.extensionStorage[e.name],editor:this.editor,type:Si(e.name,this.schema)};e.type==="mark"&&((t=_(E(e,"keepOnSplit",r)))==null||t)&&this.splittableMarks.push(e.name);const i=E(e,"onBeforeCreate",r),s=E(e,"onCreate",r),o=E(e,"onUpdate",r),l=E(e,"onSelectionUpdate",r),a=E(e,"onTransaction",r),c=E(e,"onFocus",r),u=E(e,"onBlur",r),d=E(e,"onDestroy",r);i&&this.editor.on("beforeCreate",i),s&&this.editor.on("create",s),o&&this.editor.on("update",o),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),u&&this.editor.on("blur",u),d&&this.editor.on("destroy",d)})}};Zr.resolve=dc;Zr.sort=Es;Zr.flatten=ws;var yp={};oc(yp,{ClipboardTextSerializer:()=>Mc,Commands:()=>Ec,Delete:()=>Ac,Drop:()=>Oc,Editable:()=>Nc,FocusEvents:()=>Ic,Keymap:()=>Rc,Paste:()=>Pc,Tabindex:()=>Lc,focusEventsPluginKey:()=>Dc});var V=class Cc extends Ds{constructor(){super(...arguments),this.type="extension"}static create(e={}){const t=typeof e=="function"?e():e;return new Cc(t)}configure(e){return super.configure(e)}extend(e){const t=typeof e=="function"?e():e;return super.extend(t)}},Mc=V.create({name:"clipboardTextSerializer",addOptions(){return{blockSeparator:void 0}},addProseMirrorPlugins(){return[new Q({key:new ie("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:n}=this,{state:e,schema:t}=n,{doc:r,selection:i}=e,{ranges:s}=i,o=Math.min(...s.map(u=>u.$from.pos)),l=Math.max(...s.map(u=>u.$to.pos)),a=hc(t);return fc(r,{from:o,to:l},U(x({},this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{}),{textSerializers:a}))}}})]}}),wc={};oc(wc,{blur:()=>bp,clearContent:()=>kp,clearNodes:()=>xp,command:()=>Sp,createParagraphNear:()=>vp,cut:()=>Cp,deleteCurrentNode:()=>Mp,deleteNode:()=>wp,deleteRange:()=>Tp,deleteSelection:()=>Ep,enter:()=>Ap,exitCode:()=>Op,extendMarkRange:()=>Np,first:()=>Dp,focus:()=>Rp,forEach:()=>Pp,insertContent:()=>Lp,insertContentAt:()=>zp,joinBackward:()=>Hp,joinDown:()=>$p,joinForward:()=>_p,joinItemBackward:()=>Vp,joinItemForward:()=>jp,joinTextblockBackward:()=>Wp,joinTextblockForward:()=>Kp,joinUp:()=>Fp,keyboardShortcut:()=>qp,lift:()=>Jp,liftEmptyBlock:()=>Gp,liftListItem:()=>Yp,newlineInCode:()=>Xp,resetAttributes:()=>Qp,scrollIntoView:()=>Zp,selectAll:()=>em,selectNodeBackward:()=>tm,selectNodeForward:()=>nm,selectParentNode:()=>rm,selectTextblockEnd:()=>im,selectTextblockStart:()=>sm,setContent:()=>om,setMark:()=>am,setMeta:()=>cm,setNode:()=>um,setNodeSelection:()=>dm,setTextSelection:()=>fm,sinkListItem:()=>hm,splitBlock:()=>pm,splitListItem:()=>mm,toggleList:()=>gm,toggleMark:()=>ym,toggleNode:()=>bm,toggleWrap:()=>km,undoInputRule:()=>xm,unsetAllMarks:()=>Sm,unsetMark:()=>vm,updateAttributes:()=>Cm,wrapIn:()=>Mm,wrapInList:()=>wm});var bp=()=>({editor:n,view:e})=>(requestAnimationFrame(()=>{var t;n.isDestroyed||(e.dom.blur(),(t=window==null?void 0:window.getSelection())==null||t.removeAllRanges())}),!0),kp=(n=!0)=>({commands:e})=>e.setContent("",{emitUpdate:n}),xp=()=>({state:n,tr:e,dispatch:t})=>{const{selection:r}=e,{ranges:i}=r;return t&&i.forEach(({$from:s,$to:o})=>{n.doc.nodesBetween(s.pos,o.pos,(l,a)=>{if(l.type.isText)return;const{doc:c,mapping:u}=e,d=c.resolve(u.map(a)),f=c.resolve(u.map(a+l.nodeSize)),h=d.blockRange(f);if(!h)return;const p=Yt(h);if(l.type.isTextblock){const{defaultType:m}=d.parent.contentMatchAt(d.index());e.setNodeMarkup(h.start,m)}(p||p===0)&&e.lift(h,p)})}),!0},Sp=n=>e=>n(e),vp=()=>({state:n,dispatch:e})=>ec(n,e),Cp=(n,e)=>({editor:t,tr:r})=>{const{state:i}=t,s=i.doc.slice(n.from,n.to);r.deleteRange(n.from,n.to);const o=r.mapping.map(e);return r.insert(o,s.content),r.setSelection(new R(r.doc.resolve(Math.max(o-1,0)))),!0},Mp=()=>({tr:n,dispatch:e})=>{const{selection:t}=n,r=t.$anchor.node();if(r.content.size>0)return!1;const i=n.selection.$anchor;for(let s=i.depth;s>0;s-=1)if(i.node(s).type===r.type){if(e){const l=i.before(s),a=i.after(s);n.delete(l,a).scrollIntoView()}return!0}return!1},wp=n=>({tr:e,state:t,dispatch:r})=>{const i=ee(n,t.schema),s=e.selection.$anchor;for(let o=s.depth;o>0;o-=1)if(s.node(o).type===i){if(r){const a=s.before(o),c=s.after(o);e.delete(a,c).scrollIntoView()}return!0}return!1},Tp=n=>({tr:e,dispatch:t})=>{const{from:r,to:i}=n;return t&&e.delete(r,i),!0},Ep=()=>({state:n,dispatch:e})=>xs(n,e),Ap=()=>({commands:n})=>n.keyboardShortcut("Enter"),Op=()=>({state:n,dispatch:e})=>Za(n,e),Np=(n,e={})=>({tr:t,state:r,dispatch:i})=>{const s=Ue(n,r.schema),{doc:o,selection:l}=t,{$from:a,from:c,to:u}=l;if(i){const d=Os(a,s,e);if(d&&d.from<=c&&d.to>=u){const f=R.create(o,d.from,d.to);t.setSelection(f)}}return!0},Dp=n=>e=>{const t=typeof n=="function"?n(e):n;for(let r=0;r<t.length;r+=1)if(t[r](e))return!0;return!1};function Ip(){return navigator.platform==="Android"||/android/i.test(navigator.userAgent)}function Is(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}var Rp=(n=null,e={})=>({editor:t,view:r,tr:i,dispatch:s})=>{e=x({scrollIntoView:!0},e);const o=()=>{(Is()||Ip())&&r.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(r.focus(),e!=null&&e.scrollIntoView&&t.commands.scrollIntoView())})};if(r.hasFocus()&&n===null||n===!1)return!0;if(s&&n===null&&!kc(t.state.selection))return o(),!0;const l=xc(i.doc,n)||t.state.selection,a=t.state.selection.eq(l);return s&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),o()),!0},Pp=(n,e)=>t=>n.every((r,i)=>e(r,U(x({},t),{index:i}))),Lp=(n,e)=>({tr:t,commands:r})=>r.insertContentAt({from:t.selection.from,to:t.selection.to},n,e),Bp=n=>!("type"in n),zp=(n,e,t)=>({tr:r,dispatch:i,editor:s})=>{var o;if(i){t=x({parseOptions:s.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1},t);let l;const{selection:a}=s.state,c=b=>{s.emit("contentError",{editor:s,error:b,disableCollaboration:()=>{"collaboration"in s.storage&&typeof s.storage.collaboration=="object"&&s.storage.collaboration&&(s.storage.collaboration.isDisabled=!0)}})},u=x({preserveWhitespace:"full"},t.parseOptions);if(!t.errorOnInvalidContent&&!s.options.enableContentCheck&&s.options.emitContentError)try{Cn(e,s.schema,{parseOptions:u,errorOnInvalidContent:!0})}catch(b){c(b)}try{l=Cn(e,s.schema,{parseOptions:u,errorOnInvalidContent:(o=t.errorOnInvalidContent)!=null?o:s.options.enableContentCheck})}catch(b){return c(b),!1}let{from:d,to:f}=typeof n=="number"?{from:n,to:n}:{from:n.from,to:n.to},h=!0,p=!0;if((Bp(l)?l:[l]).forEach(b=>{b.check(),h=h?b.isText&&b.marks.length===0:!1,p=p?b.isBlock:!1}),d===f&&p){const{parent:b}=r.doc.resolve(d);b.isTextblock&&!b.type.spec.code&&!b.childCount&&(d-=1,f+=1)}let g;if(h){if(Array.isArray(e))g=e.map(b=>b.text||"").join("");else if(e instanceof S){let b="";e.forEach(v=>{v.text&&(b+=v.text)}),g=b}else typeof e=="object"&&e&&e.text?g=e.text:g=e;r.insertText(g,d,f)}else{g=l;const b=a.$from.parentOffset===0,v=a.$from.node().isText||a.$from.node().isTextblock,D=a.$from.node().content.size>0;b&&v&&D&&(d=Math.max(0,d-1)),r.replaceWith(d,f,g)}t.updateSelection&&lp(r,r.steps.length-1,-1),t.applyInputRules&&r.setMeta("applyInputRules",{from:d,text:g}),t.applyPasteRules&&r.setMeta("applyPasteRules",{from:d,text:g})}return!0},Fp=()=>({state:n,dispatch:e})=>Th(n,e),$p=()=>({state:n,dispatch:e})=>Eh(n,e),Hp=()=>({state:n,dispatch:e})=>Ua(n,e),_p=()=>({state:n,dispatch:e})=>Ya(n,e),Vp=()=>({state:n,dispatch:e,tr:t})=>{try{const r=_r(n.doc,n.selection.$from.pos,-1);return r==null?!1:(t.join(r,2),e&&e(t),!0)}catch(r){return!1}},jp=()=>({state:n,dispatch:e,tr:t})=>{try{const r=_r(n.doc,n.selection.$from.pos,1);return r==null?!1:(t.join(r,2),e&&e(t),!0)}catch(r){return!1}},Wp=()=>({state:n,dispatch:e})=>Mh(n,e),Kp=()=>({state:n,dispatch:e})=>wh(n,e);function Tc(){return typeof navigator!="undefined"?/Mac/.test(navigator.platform):!1}function Up(n){const e=n.split(/-(?!$)/);let t=e[e.length-1];t==="Space"&&(t=" ");let r,i,s,o;for(let l=0;l<e.length-1;l+=1){const a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))Is()||Tc()?o=!0:i=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return r&&(t=`Alt-${t}`),i&&(t=`Ctrl-${t}`),o&&(t=`Meta-${t}`),s&&(t=`Shift-${t}`),t}var qp=n=>({editor:e,view:t,tr:r,dispatch:i})=>{const s=Up(n).split(/-(?!$)/),o=s.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:o==="Space"?" ":o,altKey:s.includes("Alt"),ctrlKey:s.includes("Ctrl"),metaKey:s.includes("Meta"),shiftKey:s.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a==null||a.steps.forEach(c=>{const u=c.map(r.mapping);u&&i&&r.maybeStep(u)}),!0},Jp=(n,e={})=>({state:t,dispatch:r})=>{const i=ee(n,t.schema);return ft(t,i,e)?Ah(t,r):!1},Gp=()=>({state:n,dispatch:e})=>tc(n,e),Yp=n=>({state:e,dispatch:t})=>{const r=ee(n,e.schema);return $h(r)(e,t)},Xp=()=>({state:n,dispatch:e})=>Qa(n,e);function dl(n,e){const t=typeof e=="string"?[e]:e;return Object.keys(n).reduce((r,i)=>(t.includes(i)||(r[i]=n[i]),r),{})}var Qp=(n,e)=>({tr:t,state:r,dispatch:i})=>{let s=null,o=null;const l=Yr(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(s=ee(n,r.schema)),l==="mark"&&(o=Ue(n,r.schema)),i&&t.selection.ranges.forEach(a=>{r.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,u)=>{s&&s===c.type&&t.setNodeMarkup(u,void 0,dl(c.attrs,e)),o&&c.marks.length&&c.marks.forEach(d=>{o===d.type&&t.addMark(u,u+c.nodeSize,o.create(dl(d.attrs,e)))})})}),!0):!1},Zp=()=>({tr:n,dispatch:e})=>(e&&n.scrollIntoView(),!0),em=()=>({tr:n,dispatch:e})=>{if(e){const t=new ye(n.doc);n.setSelection(t)}return!0},tm=()=>({state:n,dispatch:e})=>Ja(n,e),nm=()=>({state:n,dispatch:e})=>Xa(n,e),rm=()=>({state:n,dispatch:e})=>Dh(n,e),im=()=>({state:n,dispatch:e})=>sc(n,e),sm=()=>({state:n,dispatch:e})=>ic(n,e),om=(n,{errorOnInvalidContent:e,emitUpdate:t=!0,parseOptions:r={}}={})=>({editor:i,tr:s,dispatch:o,commands:l})=>{const{doc:a}=s;if(r.preserveWhitespace!=="full"){const c=qi(n,i.schema,r,{errorOnInvalidContent:e!=null?e:i.options.enableContentCheck});return o&&s.replaceWith(0,a.content.size,c).setMeta("preventUpdate",!t),!0}return o&&s.setMeta("preventUpdate",!t),l.insertContentAt({from:0,to:a.content.size},n,{parseOptions:r,errorOnInvalidContent:e!=null?e:i.options.enableContentCheck})};function lm(n,e,t){var r;const{selection:i}=e;let s=null;if(kc(i)&&(s=i.$cursor),s){const l=(r=n.storedMarks)!=null?r:s.marks();return!!t.isInSet(l)||!l.some(a=>a.type.excludes(t))}const{ranges:o}=i;return o.some(({$from:l,$to:a})=>{let c=l.depth===0?n.doc.inlineContent&&n.doc.type.allowsMarkType(t):!1;return n.doc.nodesBetween(l.pos,a.pos,(u,d,f)=>{if(c)return!1;if(u.isInline){const h=!f||f.type.allowsMarkType(t),p=!!t.isInSet(u.marks)||!u.marks.some(m=>m.type.excludes(t));c=h&&p}return!c}),c})}var am=(n,e={})=>({tr:t,state:r,dispatch:i})=>{const{selection:s}=t,{empty:o,ranges:l}=s,a=Ue(n,r.schema);if(i)if(o){const c=pc(r,a);t.addStoredMark(a.create(x(x({},c),e)))}else l.forEach(c=>{const u=c.$from.pos,d=c.$to.pos;r.doc.nodesBetween(u,d,(f,h)=>{const p=Math.max(h,u),m=Math.min(h+f.nodeSize,d);f.marks.find(b=>b.type===a)?f.marks.forEach(b=>{a===b.type&&t.addMark(p,m,a.create(x(x({},b.attrs),e)))}):t.addMark(p,m,a.create(e))})});return lm(r,t,a)},cm=(n,e)=>({tr:t})=>(t.setMeta(n,e),!0),um=(n,e={})=>({state:t,dispatch:r,chain:i})=>{const s=ee(n,t.schema);let o;return t.selection.$anchor.sameParent(t.selection.$head)&&(o=t.selection.$anchor.parent.attrs),s.isTextblock?i().command(({commands:l})=>il(s,x(x({},o),e))(t)?!0:l.clearNodes()).command(({state:l})=>il(s,x(x({},o),e))(l,r)).run():!1},dm=n=>({tr:e,dispatch:t})=>{if(t){const{doc:r}=e,i=vt(n,0,r.content.size),s=A.create(r,i);e.setSelection(s)}return!0},fm=n=>({tr:e,dispatch:t})=>{if(t){const{doc:r}=e,{from:i,to:s}=typeof n=="number"?{from:n,to:n}:n,o=R.atStart(r).from,l=R.atEnd(r).to,a=vt(i,o,l),c=vt(s,o,l),u=R.create(r,a,c);e.setSelection(u)}return!0},hm=n=>({state:e,dispatch:t})=>{const r=ee(n,e.schema);return Vh(r)(e,t)};function fl(n,e){const t=n.storedMarks||n.selection.$to.parentOffset&&n.selection.$from.marks();if(t){const r=t.filter(i=>e==null?void 0:e.includes(i.type.name));n.tr.ensureMarks(r)}}var pm=({keepMarks:n=!0}={})=>({tr:e,state:t,dispatch:r,editor:i})=>{const{selection:s,doc:o}=e,{$from:l,$to:a}=s,c=i.extensionManager.attributes,u=Jn(c,l.node().type.name,l.node().attrs);if(s instanceof A&&s.node.isBlock)return!l.parentOffset||!We(o,l.pos)?!1:(r&&(n&&fl(t,i.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;const d=a.parentOffset===a.parent.content.size,f=l.depth===0?void 0:Kh(l.node(-1).contentMatchAt(l.indexAfter(-1)));let h=d&&f?[{type:f,attrs:u}]:void 0,p=We(e.doc,e.mapping.map(l.pos),1,h);if(!h&&!p&&We(e.doc,e.mapping.map(l.pos),1,f?[{type:f}]:void 0)&&(p=!0,h=f?[{type:f,attrs:u}]:void 0),r){if(p&&(s instanceof R&&e.deleteSelection(),e.split(e.mapping.map(l.pos),1,h),f&&!d&&!l.parentOffset&&l.parent.type!==f)){const m=e.mapping.map(l.before()),g=e.doc.resolve(m);l.node(-1).canReplaceWith(g.index(),g.index()+1,f)&&e.setNodeMarkup(e.mapping.map(l.before()),f)}n&&fl(t,i.extensionManager.splittableMarks),e.scrollIntoView()}return p},mm=(n,e={})=>({tr:t,state:r,dispatch:i,editor:s})=>{var o;const l=ee(n,r.schema),{$from:a,$to:c}=r.selection,u=r.selection.node;if(u&&u.isBlock||a.depth<2||!a.sameParent(c))return!1;const d=a.node(-1);if(d.type!==l)return!1;const f=s.extensionManager.attributes;if(a.parent.content.size===0&&a.node(-1).childCount===a.indexAfter(-1)){if(a.depth===2||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(i){let b=S.empty;const v=a.index(-1)?1:a.index(-2)?2:3;for(let y=a.depth-v;y>=a.depth-3;y-=1)b=S.from(a.node(y).copy(b));const D=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,F=x(x({},Jn(f,a.node().type.name,a.node().attrs)),e),I=((o=l.contentMatch.defaultType)==null?void 0:o.createAndFill(F))||void 0;b=b.append(S.from(l.createAndFill(null,I)||void 0));const H=a.before(a.depth-(v-1));t.replace(H,a.after(-D),new w(b,4-v,0));let C=-1;t.doc.nodesBetween(H,t.doc.content.size,(y,T)=>{if(C>-1)return!1;y.isTextblock&&y.content.size===0&&(C=T+1)}),C>-1&&t.setSelection(R.near(t.doc.resolve(C))),t.scrollIntoView()}return!0}const h=c.pos===a.end()?d.contentMatchAt(0).defaultType:null,p=x(x({},Jn(f,d.type.name,d.attrs)),e),m=x(x({},Jn(f,a.node().type.name,a.node().attrs)),e);t.delete(a.pos,c.pos);const g=h?[{type:l,attrs:p},{type:h,attrs:m}]:[{type:l,attrs:p}];if(!We(t.doc,a.pos,2))return!1;if(i){const{selection:b,storedMarks:v}=r,{splittableMarks:D}=s.extensionManager,F=v||b.$to.parentOffset&&b.$from.marks();if(t.split(a.pos,2,g).scrollIntoView(),!F||!i)return!0;const I=F.filter(H=>D.includes(H.type.name));t.ensureMarks(I)}return!0},vi=(n,e)=>{const t=Gr(o=>o.type===e)(n.selection);if(!t)return!0;const r=n.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(r===void 0)return!0;const i=n.doc.nodeAt(r);return t.node.type===(i==null?void 0:i.type)&&pt(n.doc,t.pos)&&n.join(t.pos),!0},Ci=(n,e)=>{const t=Gr(o=>o.type===e)(n.selection);if(!t)return!0;const r=n.doc.resolve(t.start).after(t.depth);if(r===void 0)return!0;const i=n.doc.nodeAt(r);return t.node.type===(i==null?void 0:i.type)&&pt(n.doc,r)&&n.join(r),!0},gm=(n,e,t,r={})=>({editor:i,tr:s,state:o,dispatch:l,chain:a,commands:c,can:u})=>{const{extensions:d,splittableMarks:f}=i.extensionManager,h=ee(n,o.schema),p=ee(e,o.schema),{selection:m,storedMarks:g}=o,{$from:b,$to:v}=m,D=b.blockRange(v),F=g||m.$to.parentOffset&&m.$from.marks();if(!D)return!1;const I=Gr(H=>ul(H.type.name,d))(m);if(D.depth>=1&&I&&D.depth-I.depth<=1){if(I.node.type===h)return c.liftListItem(p);if(ul(I.node.type.name,d)&&h.validContent(I.node.content)&&l)return a().command(()=>(s.setNodeMarkup(I.pos,h),!0)).command(()=>vi(s,h)).command(()=>Ci(s,h)).run()}return!t||!F||!l?a().command(()=>u().wrapInList(h,r)?!0:c.clearNodes()).wrapInList(h,r).command(()=>vi(s,h)).command(()=>Ci(s,h)).run():a().command(()=>{const H=u().wrapInList(h,r),C=F.filter(y=>f.includes(y.type.name));return s.ensureMarks(C),H?!0:c.clearNodes()}).wrapInList(h,r).command(()=>vi(s,h)).command(()=>Ci(s,h)).run()},ym=(n,e={},t={})=>({state:r,commands:i})=>{const{extendEmptyMarkRange:s=!1}=t,o=Ue(n,r.schema);return Ji(r,o,e)?i.unsetMark(o,{extendEmptyMarkRange:s}):i.setMark(o,e)},bm=(n,e,t={})=>({state:r,commands:i})=>{const s=ee(n,r.schema),o=ee(e,r.schema),l=ft(r,s,t);let a;return r.selection.$anchor.sameParent(r.selection.$head)&&(a=r.selection.$anchor.parent.attrs),l?i.setNode(o,a):i.setNode(s,x(x({},a),t))},km=(n,e={})=>({state:t,commands:r})=>{const i=ee(n,t.schema);return ft(t,i,e)?r.lift(i):r.wrapIn(i,e)},xm=()=>({state:n,dispatch:e})=>{const t=n.plugins;for(let r=0;r<t.length;r+=1){const i=t[r];let s;if(i.spec.isInputRules&&(s=i.getState(n))){if(e){const o=n.tr,l=s.transform;for(let a=l.steps.length-1;a>=0;a-=1)o.step(l.steps[a].invert(l.docs[a]));if(s.text){const a=o.doc.resolve(s.from).marks();o.replaceWith(s.from,s.to,n.schema.text(s.text,a))}else o.delete(s.from,s.to)}return!0}}return!1},Sm=()=>({tr:n,dispatch:e})=>{const{selection:t}=n,{empty:r,ranges:i}=t;return r||e&&i.forEach(s=>{n.removeMark(s.$from.pos,s.$to.pos)}),!0},vm=(n,e={})=>({tr:t,state:r,dispatch:i})=>{var s;const{extendEmptyMarkRange:o=!1}=e,{selection:l}=t,a=Ue(n,r.schema),{$from:c,empty:u,ranges:d}=l;if(!i)return!0;if(u&&o){let{from:f,to:h}=l;const p=(s=c.marks().find(g=>g.type===a))==null?void 0:s.attrs,m=Os(c,a,p);m&&(f=m.from,h=m.to),t.removeMark(f,h,a)}else d.forEach(f=>{t.removeMark(f.$from.pos,f.$to.pos,a)});return t.removeStoredMark(a),!0},Cm=(n,e={})=>({tr:t,state:r,dispatch:i})=>{let s=null,o=null;const l=Yr(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(s=ee(n,r.schema)),l==="mark"&&(o=Ue(n,r.schema)),i&&t.selection.ranges.forEach(a=>{const c=a.$from.pos,u=a.$to.pos;let d,f,h,p;t.selection.empty?r.doc.nodesBetween(c,u,(m,g)=>{s&&s===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,u),d=g,f=m)}):r.doc.nodesBetween(c,u,(m,g)=>{g<c&&s&&s===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,u),d=g,f=m),g>=c&&g<=u&&(s&&s===m.type&&t.setNodeMarkup(g,void 0,x(x({},m.attrs),e)),o&&m.marks.length&&m.marks.forEach(b=>{if(o===b.type){const v=Math.max(g,c),D=Math.min(g+m.nodeSize,u);t.addMark(v,D,o.create(x(x({},b.attrs),e)))}}))}),f&&(d!==void 0&&t.setNodeMarkup(d,void 0,x(x({},f.attrs),e)),o&&f.marks.length&&f.marks.forEach(m=>{o===m.type&&t.addMark(h,p,o.create(x(x({},m.attrs),e)))}))}),!0):!1},Mm=(n,e={})=>({state:t,dispatch:r})=>{const i=ee(n,t.schema);return Ph(i,e)(t,r)},wm=(n,e={})=>({state:t,dispatch:r})=>{const i=ee(n,t.schema);return Bh(i,e)(t,r)},Ec=V.create({name:"commands",addCommands(){return x({},wc)}}),Ac=V.create({name:"delete",onUpdate({transaction:n,appendedTransactions:e}){var t,r,i;const s=()=>{var o,l,a,c;if((c=(a=(l=(o=this.editor.options.coreExtensionOptions)==null?void 0:o.delete)==null?void 0:l.filterTransaction)==null?void 0:a.call(l,n))!=null?c:n.getMeta("y-sync$"))return;const u=lc(n.before,[n,...e]);gc(u).forEach(h=>{u.mapping.mapResult(h.oldRange.from).deletedAfter&&u.mapping.mapResult(h.oldRange.to).deletedBefore&&u.before.nodesBetween(h.oldRange.from,h.oldRange.to,(p,m)=>{const g=m+p.nodeSize-2,b=h.oldRange.from<=m&&g<=h.oldRange.to;this.editor.emit("delete",{type:"node",node:p,from:m,to:g,newFrom:u.mapping.map(m),newTo:u.mapping.map(g),deletedRange:h.oldRange,newRange:h.newRange,partial:!b,editor:this.editor,transaction:n,combinedTransform:u})})});const f=u.mapping;u.steps.forEach((h,p)=>{var m,g;if(h instanceof Oe){const b=f.slice(p).map(h.from,-1),v=f.slice(p).map(h.to),D=f.invert().map(b,-1),F=f.invert().map(v),I=(m=u.doc.nodeAt(b-1))==null?void 0:m.marks.some(C=>C.eq(h.mark)),H=(g=u.doc.nodeAt(v))==null?void 0:g.marks.some(C=>C.eq(h.mark));this.editor.emit("delete",{type:"mark",mark:h.mark,from:h.from,to:h.to,deletedRange:{from:D,to:F},newRange:{from:b,to:v},partial:!!(H||I),editor:this.editor,transaction:n,combinedTransform:u})}})};(i=(r=(t=this.editor.options.coreExtensionOptions)==null?void 0:t.delete)==null?void 0:r.async)==null||i?setTimeout(s,0):s()}}),Oc=V.create({name:"drop",addProseMirrorPlugins(){return[new Q({key:new ie("tiptapDrop"),props:{handleDrop:(n,e,t,r)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:t,moved:r})}}})]}}),Nc=V.create({name:"editable",addProseMirrorPlugins(){return[new Q({key:new ie("editable"),props:{editable:()=>this.editor.options.editable}})]}}),Dc=new ie("focusEvents"),Ic=V.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:n}=this;return[new Q({key:Dc,props:{handleDOMEvents:{focus:(e,t)=>{n.isFocused=!0;const r=n.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1},blur:(e,t)=>{n.isFocused=!1;const r=n.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1}}}})]}}),Rc=V.create({name:"keymap",addKeyboardShortcuts(){const n=()=>this.editor.commands.first(({commands:o})=>[()=>o.undoInputRule(),()=>o.command(({tr:l})=>{const{selection:a,doc:c}=l,{empty:u,$anchor:d}=a,{pos:f,parent:h}=d,p=d.parent.isTextblock&&f>0?l.doc.resolve(f-1):d,m=p.parent.type.spec.isolating,g=d.pos-d.parentOffset,b=m&&p.parent.childCount===1?g===d.pos:P.atStart(c).from===f;return!u||!h.type.isTextblock||h.textContent.length||!b||b&&d.parent.type.name==="paragraph"?!1:o.clearNodes()}),()=>o.deleteSelection(),()=>o.joinBackward(),()=>o.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:o})=>[()=>o.deleteSelection(),()=>o.deleteCurrentNode(),()=>o.joinForward(),()=>o.selectNodeForward()]),r={Enter:()=>this.editor.commands.first(({commands:o})=>[()=>o.newlineInCode(),()=>o.createParagraphNear(),()=>o.liftEmptyBlock(),()=>o.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:n,"Mod-Backspace":n,"Shift-Backspace":n,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},i=x({},r),s=U(x({},r),{"Ctrl-h":n,"Alt-Backspace":n,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()});return Is()||Tc()?s:i},addProseMirrorPlugins(){return[new Q({key:new ie("clearDocument"),appendTransaction:(n,e,t)=>{if(n.some(m=>m.getMeta("composition")))return;const r=n.some(m=>m.docChanged)&&!e.doc.eq(t.doc),i=n.some(m=>m.getMeta("preventClearDocument"));if(!r||i)return;const{empty:s,from:o,to:l}=e.selection,a=P.atStart(e.doc).from,c=P.atEnd(e.doc).to;if(s||!(o===a&&l===c)||!Xr(t.doc))return;const f=t.tr,h=qr({state:t,transaction:f}),{commands:p}=new Jr({editor:this.editor,state:h});if(p.clearNodes(),!!f.steps.length)return f}})]}}),Pc=V.create({name:"paste",addProseMirrorPlugins(){return[new Q({key:new ie("tiptapPaste"),props:{handlePaste:(n,e,t)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:t})}}})]}}),Lc=V.create({name:"tabindex",addProseMirrorPlugins(){return[new Q({key:new ie("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}}),Tm=class Ht{constructor(e,t,r=!1,i=null){this.currentNode=null,this.actualDepth=null,this.isBlock=r,this.resolvedPos=e,this.editor=t,this.currentNode=i}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!=null?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,r=this.to;if(this.isBlock){if(this.content.size===0)return;t=this.from+1,r=this.to-1}this.editor.commands.insertContentAt({from:t,to:r},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new Ht(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new Ht(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new Ht(e,this.editor)}get children(){const e=[];return this.node.content.forEach((t,r)=>{const i=t.isBlock&&!t.isTextblock,s=t.isAtom&&!t.isText,o=this.pos+r+(s?0:1);if(o<0||o>this.resolvedPos.doc.nodeSize-2)return;const l=this.resolvedPos.doc.resolve(o);if(!i&&l.depth<=this.depth)return;const a=new Ht(l,this.editor,i,i?t:null);i&&(a.actualDepth=this.depth+1),e.push(new Ht(l,this.editor,i,i?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let r=null,i=this.parent;for(;i&&!r;){if(i.node.type.name===e)if(Object.keys(t).length>0){const s=i.node.attrs,o=Object.keys(t);for(let l=0;l<o.length;l+=1){const a=o[l];if(s[a]!==t[a])break}}else r=i;i=i.parent}return r}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},r=!1){let i=[];if(!this.children||this.children.length===0)return i;const s=Object.keys(t);return this.children.forEach(o=>{r&&i.length>0||(o.node.type.name===e&&s.every(a=>t[a]===o.node.attrs[a])&&i.push(o),!(r&&i.length>0)&&(i=i.concat(o.querySelectorAll(e,t,r))))}),i}setAttribute(e){const{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,x(x({},this.node.attrs),e)),this.editor.view.dispatch(t)}},Em=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}`;function Am(n,e,t){const r=document.querySelector(`style[data-tiptap-style${t?`-${t}`:""}]`);if(r!==null)return r;const i=document.createElement("style");return e&&i.setAttribute("nonce",e),i.setAttribute(`data-tiptap-style${t?`-${t}`:""}`,""),i.innerHTML=n,document.getElementsByTagName("head")[0].appendChild(i),i}var Om=class extends Wh{constructor(e={}){super(),this.css=null,this.editorView=null,this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.instanceId=Math.random().toString(36).slice(2,9),this.options={element:typeof document!="undefined"?document.createElement("div"):null,content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:i})=>{throw i},onPaste:()=>null,onDrop:()=>null,onDelete:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:i,slice:s,moved:o})=>this.options.onDrop(i,s,o)),this.on("paste",({event:i,slice:s})=>this.options.onPaste(i,s)),this.on("delete",this.options.onDelete);const t=this.createDoc(),r=xc(t,this.options.autofocus);this.editorState=Vt.create({doc:t,schema:this.schema,selection:r||void 0}),this.options.element&&this.mount(this.options.element)}mount(e){if(typeof document=="undefined")throw new Error("[tiptap error]: The editor cannot be mounted because there is no 'document' defined in this environment.");this.createView(e),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}unmount(){var e;if(this.editorView){const t=this.editorView.dom;t!=null&&t.editor&&delete t.editor,this.editorView.destroy()}this.editorView=null,this.isInitialized=!1,(e=this.css)==null||e.remove(),this.css=null}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&typeof document!="undefined"&&(this.css=Am(Em,this.options.injectNonce))}setOptions(e={}){this.options=x(x({},this.options),e),!(!this.editorView||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr,appendedTransactions:[]})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get view(){return this.editorView?this.editorView:new Proxy({state:this.editorState,updateState:e=>{this.editorState=e},dispatch:e=>{this.editorState=this.state.apply(e)},composing:!1,dragging:null,editable:!0,isDestroyed:!1},{get:(e,t)=>{if(t==="state")return this.editorState;if(t in e)return Reflect.get(e,t);throw new Error(`[tiptap error]: The editor view is not available. Cannot access view['${t}']. The editor may not be mounted yet.`)}})}get state(){return this.editorView&&(this.editorState=this.view.state),this.editorState}registerPlugin(e,t){const r=cc(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],i=this.state.reconfigure({plugins:r});return this.view.updateState(i),i}unregisterPlugin(e){if(this.isDestroyed)return;const t=this.state.plugins;let r=t;if([].concat(e).forEach(s=>{const o=typeof s=="string"?`${s}$`:s.key;r=r.filter(l=>!l.key.startsWith(o))}),t.length===r.length)return;const i=this.state.reconfigure({plugins:r});return this.view.updateState(i),i}createExtensionManager(){var e,t;const i=[...this.options.enableCoreExtensions?[Nc,Mc.configure({blockSeparator:(t=(e=this.options.coreExtensionOptions)==null?void 0:e.clipboardTextSerializer)==null?void 0:t.blockSeparator}),Ec,Ic,Rc,Lc,Oc,Pc,Ac].filter(s=>typeof this.options.enableCoreExtensions=="object"?this.options.enableCoreExtensions[s.name]!==!1:!0):[],...this.options.extensions].filter(s=>["extension","node","mark"].includes(s==null?void 0:s.type));this.extensionManager=new Zr(i,this)}createCommandManager(){this.commandManager=new Jr({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createDoc(){let e;try{e=qi(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(t){if(!(t instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(t.message))throw t;this.emit("contentError",{editor:this,error:t,disableCollaboration:()=>{"collaboration"in this.storage&&typeof this.storage.collaboration=="object"&&this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(r=>r.name!=="collaboration"),this.createExtensionManager()}}),e=qi(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}return e}createView(e){var t;this.editorView=new ja(e,U(x({},this.options.editorProps),{attributes:x({role:"textbox"},(t=this.options.editorProps)==null?void 0:t.attributes),dispatchTransaction:this.dispatchTransaction.bind(this),state:this.editorState}));const r=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(r),this.createNodeViews(),this.prependClass(),this.injectCSS();const i=this.view.dom;i.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({markViews:this.extensionManager.markViews,nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(u=>{var d;return(d=this.capturedTransaction)==null?void 0:d.step(u)});return}const{state:t,transactions:r}=this.state.applyTransaction(e),i=!this.state.selection.eq(t.selection),s=r.includes(e),o=this.state;if(this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),!s)return;this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e,appendedTransactions:r.slice(1)}),i&&this.emit("selectionUpdate",{editor:this,transaction:e});const l=r.findLast(u=>u.getMeta("focus")||u.getMeta("blur")),a=l==null?void 0:l.getMeta("focus"),c=l==null?void 0:l.getMeta("blur");a&&this.emit("focus",{editor:this,event:a.event,transaction:l}),c&&this.emit("blur",{editor:this,event:c.event,transaction:l}),!(e.getMeta("preventUpdate")||!r.some(u=>u.docChanged)||o.doc.eq(t.doc))&&this.emit("update",{editor:this,transaction:e,appendedTransactions:r.slice(1)})}getAttributes(e){return mc(this.state,e)}isActive(e,t){const r=typeof e=="string"?e:null,i=typeof e=="string"?t:e;return ip(this.state,r,i)}getJSON(){return this.state.doc.toJSON()}getHTML(){return Ts(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t=`

`,textSerializers:r={}}=e||{};return Qh(this.state.doc,{blockSeparator:t,textSerializers:x(x({},hc(this.schema)),r)})}get isEmpty(){return Xr(this.state.doc)}destroy(){this.emit("destroy"),this.unmount(),this.removeAllListeners()}get isDestroyed(){var e,t;return(t=(e=this.editorView)==null?void 0:e.isDestroyed)!=null?t:!0}$node(e,t){var r;return((r=this.$doc)==null?void 0:r.querySelector(e,t))||null}$nodes(e,t){var r;return((r=this.$doc)==null?void 0:r.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new Tm(t,this)}get $doc(){return this.$pos(0)}};function Rt(n){return new Qr({find:n.find,handler:({state:e,range:t,match:r})=>{const i=_(n.getAttributes,void 0,r);if(i===!1||i===null)return null;const{tr:s}=e,o=r[r.length-1],l=r[0];if(o){const a=l.search(/\S/),c=t.from+l.indexOf(o),u=c+o.length;if(Ns(t.from,t.to,e.doc).filter(h=>h.mark.type.excluded.find(m=>m===n.type&&m!==h.mark.type)).filter(h=>h.to>c).length)return null;u<t.to&&s.delete(u,t.to),c>t.from&&s.delete(t.from+a,c);const f=t.from+a+o.length;s.addMark(t.from+a,f,n.type.create(i||{})),s.removeStoredMark(n.type)}}})}function Bc(n){return new Qr({find:n.find,handler:({state:e,range:t,match:r})=>{const i=_(n.getAttributes,void 0,r)||{},{tr:s}=e,o=t.from;let l=t.to;const a=n.type.create(i);if(r[1]){const c=r[0].lastIndexOf(r[1]);let u=o+c;u>l?u=l:l=u+r[1].length;const d=r[0][r[0].length-1];s.insertText(d,o+r[0].length-1),s.replaceWith(u,l,a)}else if(r[0]){const c=n.type.isInline?o:o-1;s.insert(c,n.type.create(i)).delete(s.mapping.map(o),s.mapping.map(l))}s.scrollIntoView()}})}function Gi(n){return new Qr({find:n.find,handler:({state:e,range:t,match:r})=>{const i=e.doc.resolve(t.from),s=_(n.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),n.type))return null;e.tr.delete(t.from,t.to).setBlockType(t.from,t.from,n.type,s)}})}function Gt(n){return new Qr({find:n.find,handler:({state:e,range:t,match:r,chain:i})=>{const s=_(n.getAttributes,void 0,r)||{},o=e.tr.delete(t.from,t.to),a=o.doc.resolve(t.from).blockRange(),c=a&&cs(a,n.type,s);if(!c)return null;if(o.wrap(a,c),n.keepMarks&&n.editor){const{selection:d,storedMarks:f}=e,{splittableMarks:h}=n.editor.extensionManager,p=f||d.$to.parentOffset&&d.$from.marks();if(p){const m=p.filter(g=>h.includes(g.type.name));o.ensureMarks(m)}}if(n.keepAttributes){const d=n.type.name==="bulletList"||n.type.name==="orderedList"?"listItem":"taskList";i().updateAttributes(d,s).run()}const u=o.doc.resolve(t.from-1).nodeBefore;u&&u.type===n.type&&pt(o.doc,t.from-1)&&(!n.joinPredicate||n.joinPredicate(r,u))&&o.join(t.from-1)}})}function Nm(n,e){const{selection:t}=n,{$from:r}=t;if(t instanceof A){const s=r.index();return r.parent.canReplaceWith(s,s+1,e)}let i=r.depth;for(;i>=0;){const s=r.index(i);if(r.node(i).contentMatchAt(s).matchType(e))return!0;i-=1}return!1}function Dm(n,e,t={}){const{state:r}=e,{doc:i,tr:s}=r,o=n;i.descendants((l,a)=>{const c=s.mapping.map(a),u=s.mapping.map(a)+l.nodeSize;let d=null;if(l.marks.forEach(h=>{if(h!==o)return!1;d=h}),!d)return;let f=!1;if(Object.keys(t).forEach(h=>{t[h]!==d.attrs[h]&&(f=!0)}),f){const h=n.type.create(x(x({},n.attrs),t));s.removeMark(c,u,n.type),s.addMark(c,u,h)}}),s.docChanged&&e.view.dispatch(s)}var ke=class zc extends Ds{constructor(){super(...arguments),this.type="node"}static create(e={}){const t=typeof e=="function"?e():e;return new zc(t)}configure(e){return super.configure(e)}extend(e){const t=typeof e=="function"?e():e;return super.extend(t)}};function ht(n){return new fp({find:n.find,handler:({state:e,range:t,match:r,pasteEvent:i})=>{const s=_(n.getAttributes,void 0,r,i);if(s===!1||s===null)return null;const{tr:o}=e,l=r[r.length-1],a=r[0];let c=t.to;if(l){const u=a.search(/\S/),d=t.from+a.indexOf(l),f=d+l.length;if(Ns(t.from,t.to,e.doc).filter(p=>p.mark.type.excluded.find(g=>g===n.type&&g!==p.mark.type)).filter(p=>p.to>d).length)return null;f<t.to&&o.delete(f,t.to),d>t.from&&o.delete(t.from+u,d),c=t.from+u+l.length,o.addMark(t.from+u,c,n.type.create(s||{})),o.removeStoredMark(n.type)}}})}function hl(n){return hu((e,t)=>({get(){return e(),n},set(r){n=r,requestAnimationFrame(()=>{requestAnimationFrame(()=>{t()})})}}))}var Im=class extends Om{constructor(n={}){return super(n),this.contentComponent=null,this.appContext=null,this.reactiveState=hl(this.view.state),this.reactiveExtensionStorage=hl(this.extensionStorage),this.on("beforeTransaction",({nextState:e})=>{this.reactiveState.value=e,this.reactiveExtensionStorage.value=this.extensionStorage}),pu(this)}get state(){return this.reactiveState?this.reactiveState.value:this.view.state}get storage(){return this.reactiveExtensionStorage?this.reactiveExtensionStorage.value:super.storage}registerPlugin(n,e){const t=super.registerPlugin(n,e);return this.reactiveState&&(this.reactiveState.value=t),t}unregisterPlugin(n){const e=super.unregisterPlugin(n);return this.reactiveState&&e&&(this.reactiveState.value=e),e}},Rm=En({name:"EditorContent",props:{editor:{default:null,type:Object}},setup(n){const e=Al(),t=mu();return gu(()=>{const r=n.editor;r&&r.options.element&&e.value&&yu(()=>{var i;if(!e.value||!((i=r.options.element)!=null&&i.firstChild))return;const s=Y(e.value);e.value.append(...r.options.element.childNodes),r.contentComponent=t.ctx._,t&&(r.appContext=U(x({},t.appContext),{provides:t.provides})),r.setOptions({element:s}),r.createNodeViews()})}),Ol(()=>{const r=n.editor;r&&(r.contentComponent=null,r.appContext=null)}),{rootEl:e}},render(){return $r("div",{ref:n=>{this.rootEl=n}})}}),c0=En({name:"NodeViewContent",props:{as:{type:String,default:"div"}},render(){return $r(this.as,{style:{whiteSpace:"pre-wrap"},"data-node-view-content":""})}}),u0=En({name:"NodeViewWrapper",props:{as:{type:String,default:"div"}},inject:["onDragStart","decorationClasses"],render(){var n,e;return $r(this.as,{class:this.decorationClasses,style:{whiteSpace:"normal"},"data-node-view-wrapper":"",onDragstart:this.onDragStart},(e=(n=this.$slots).default)==null?void 0:e.call(n))}});var d0=En({name:"MarkViewContent",props:{as:{type:String,default:"span"}},render(){return $r(this.as,{style:{whiteSpace:"inherit"},"data-mark-view-content":""})}});var ur=(n,e)=>{if(n==="slot")return 0;if(n instanceof Function)return n(e);const i=e!=null?e:{},{children:t}=i,r=ni(i,["children"]);if(n==="svg")throw new Error("SVG elements are not supported in the JSX syntax, use the array syntax instead");return[n,r,t]},Pm=/^\s*>\s$/,Lm=ke.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:n}){return ur("blockquote",U(x({},J(this.options.HTMLAttributes,n)),{children:ur("slot",{})}))},addCommands(){return{setBlockquote:()=>({commands:n})=>n.wrapIn(this.name),toggleBlockquote:()=>({commands:n})=>n.toggleWrap(this.name),unsetBlockquote:()=>({commands:n})=>n.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[Gt({find:Pm,type:this.type})]}});var Bm=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,zm=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,Fm=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,$m=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,Hm=qe.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:n=>n.style.fontWeight!=="normal"&&null},{style:"font-weight=400",clearMark:n=>n.type.name===this.name},{style:"font-weight",getAttrs:n=>/^(bold(er)?|[5-9]\d{2,})$/.test(n)&&null}]},renderHTML({HTMLAttributes:n}){return ur("strong",U(x({},J(this.options.HTMLAttributes,n)),{children:ur("slot",{})}))},addCommands(){return{setBold:()=>({commands:n})=>n.setMark(this.name),toggleBold:()=>({commands:n})=>n.toggleMark(this.name),unsetBold:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[Rt({find:Bm,type:this.type}),Rt({find:Fm,type:this.type})]},addPasteRules(){return[ht({find:zm,type:this.type}),ht({find:$m,type:this.type})]}});var _m=/(^|[^`])`([^`]+)`(?!`)/,Vm=/(^|[^`])`([^`]+)`(?!`)/g,jm=qe.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:n}){return["code",J(this.options.HTMLAttributes,n),0]},addCommands(){return{setCode:()=>({commands:n})=>n.setMark(this.name),toggleCode:()=>({commands:n})=>n.toggleMark(this.name),unsetCode:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[Rt({find:_m,type:this.type})]},addPasteRules(){return[ht({find:Vm,type:this.type})]}});var Wm=/^```([a-z]+)?[\s\n]$/,Km=/^~~~([a-z]+)?[\s\n]$/,Um=ke.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:n=>{var e;const{languageClassPrefix:t}=this.options,s=[...((e=n.firstElementChild)==null?void 0:e.classList)||[]].filter(o=>o.startsWith(t)).map(o=>o.replace(t,""))[0];return s||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:n,HTMLAttributes:e}){return["pre",J(this.options.HTMLAttributes,e),["code",{class:n.attrs.language?this.options.languageClassPrefix+n.attrs.language:null},0]]},addCommands(){return{setCodeBlock:n=>({commands:e})=>e.setNode(this.name,n),toggleCodeBlock:n=>({commands:e})=>e.toggleNode(this.name,"paragraph",n)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:n,$anchor:e}=this.editor.state.selection,t=e.pos===1;return!n||e.parent.type.name!==this.name?!1:t||!e.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:n})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:e}=n,{selection:t}=e,{$from:r,empty:i}=t;if(!i||r.parent.type!==this.type)return!1;const s=r.parentOffset===r.parent.nodeSize-2,o=r.parent.textContent.endsWith(`

`);return!s||!o?!1:n.chain().command(({tr:l})=>(l.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:n})=>{if(!this.options.exitOnArrowDown)return!1;const{state:e}=n,{selection:t,doc:r}=e,{$from:i,empty:s}=t;if(!s||i.parent.type!==this.type||!(i.parentOffset===i.parent.nodeSize-2))return!1;const l=i.after();return l===void 0?!1:r.nodeAt(l)?n.commands.command(({tr:c})=>(c.setSelection(P.near(r.resolve(l))),!0)):n.commands.exitCode()}}},addInputRules(){return[Gi({find:Wm,type:this.type,getAttributes:n=>({language:n[1]})}),Gi({find:Km,type:this.type,getAttributes:n=>({language:n[1]})})]},addProseMirrorPlugins(){return[new Q({key:new ie("codeBlockVSCodeHandler"),props:{handlePaste:(n,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;const t=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,s=i==null?void 0:i.mode;if(!t||!s)return!1;const{tr:o,schema:l}=n.state,a=l.text(t.replace(/\r\n?/g,`
`));return o.replaceSelectionWith(this.type.create({language:s},a)),o.selection.$from.parent.type!==this.type&&o.setSelection(R.near(o.doc.resolve(Math.max(0,o.selection.from-2)))),o.setMeta("paste",!0),n.dispatch(o),!0}}})]}});var qm=ke.create({name:"doc",topNode:!0,content:"block+"});var Jm=ke.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:n}){return["br",J(this.options.HTMLAttributes,n)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:n,chain:e,state:t,editor:r})=>n.first([()=>n.exitCode(),()=>n.command(()=>{const{selection:i,storedMarks:s}=t;if(i.$from.parent.type.spec.isolating)return!1;const{keepMarks:o}=this.options,{splittableMarks:l}=r.extensionManager,a=s||i.$to.parentOffset&&i.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:u})=>{if(u&&a&&o){const d=a.filter(f=>l.includes(f.type.name));c.ensureMarks(d)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}});var Gm=ke.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(n=>({tag:`h${n}`,attrs:{level:n}}))},renderHTML({node:n,HTMLAttributes:e}){return[`h${this.options.levels.includes(n.attrs.level)?n.attrs.level:this.options.levels[0]}`,J(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.setNode(this.name,n):!1,toggleHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.toggleNode(this.name,"paragraph",n):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((n,e)=>U(x({},n),{[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(n=>Gi({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${n}})\\s$`),type:this.type,getAttributes:{level:n}}))}});var Ym=ke.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:n}){return["hr",J(this.options.HTMLAttributes,n)]},addCommands(){return{setHorizontalRule:()=>({chain:n,state:e})=>{if(!Nm(e,e.schema.nodes[this.name]))return!1;const{selection:t}=e,{$to:r}=t,i=n();return bc(t)?i.insertContentAt(r.pos,{type:this.name}):i.insertContent({type:this.name}),i.command(({tr:s,dispatch:o})=>{var l;if(o){const{$to:a}=s.selection,c=a.end();if(a.nodeAfter)a.nodeAfter.isTextblock?s.setSelection(R.create(s.doc,a.pos+1)):a.nodeAfter.isBlock?s.setSelection(A.create(s.doc,a.pos)):s.setSelection(R.create(s.doc,a.pos));else{const u=(l=a.parent.type.contentMatch.defaultType)==null?void 0:l.create();u&&(s.insert(c,u),s.setSelection(R.create(s.doc,c+1)))}s.scrollIntoView()}return!0}).run()}}},addInputRules(){return[Bc({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}});var Xm=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,Qm=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,Zm=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,eg=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,tg=qe.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:n=>n.style.fontStyle!=="normal"&&null},{style:"font-style=normal",clearMark:n=>n.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:n}){return["em",J(this.options.HTMLAttributes,n),0]},addCommands(){return{setItalic:()=>({commands:n})=>n.setMark(this.name),toggleItalic:()=>({commands:n})=>n.toggleMark(this.name),unsetItalic:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[Rt({find:Xm,type:this.type}),Rt({find:Zm,type:this.type})]},addPasteRules(){return[ht({find:Qm,type:this.type}),ht({find:eg,type:this.type})]}});const ng="aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0axi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2",rg="ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2",Yi="numeric",Xi="ascii",Qi="alpha",un="asciinumeric",sn="alphanumeric",Zi="domain",Fc="emoji",ig="scheme",sg="slashscheme",Mi="whitespace";function og(n,e){return n in e||(e[n]=[]),e[n]}function Ct(n,e,t){e[Yi]&&(e[un]=!0,e[sn]=!0),e[Xi]&&(e[un]=!0,e[Qi]=!0),e[un]&&(e[sn]=!0),e[Qi]&&(e[sn]=!0),e[sn]&&(e[Zi]=!0),e[Fc]&&(e[Zi]=!0);for(const r in e){const i=og(r,t);i.indexOf(n)<0&&i.push(n)}}function lg(n,e){const t={};for(const r in e)e[r].indexOf(n)>=0&&(t[r]=!0);return t}function ge(n=null){this.j={},this.jr=[],this.jd=null,this.t=n}ge.groups={};ge.prototype={accepts(){return!!this.t},go(n){const e=this,t=e.j[n];if(t)return t;for(let r=0;r<e.jr.length;r++){const i=e.jr[r][0],s=e.jr[r][1];if(s&&i.test(n))return s}return e.jd},has(n,e=!1){return e?n in this.j:!!this.go(n)},ta(n,e,t,r){for(let i=0;i<n.length;i++)this.tt(n[i],e,t,r)},tr(n,e,t,r){r=r||ge.groups;let i;return e&&e.j?i=e:(i=new ge(e),t&&r&&Ct(e,t,r)),this.jr.push([n,i]),i},ts(n,e,t,r){let i=this;const s=n.length;if(!s)return i;for(let o=0;o<s-1;o++)i=i.tt(n[o]);return i.tt(n[s-1],e,t,r)},tt(n,e,t,r){r=r||ge.groups;const i=this;if(e&&e.j)return i.j[n]=e,e;const s=e;let o,l=i.go(n);if(l?(o=new ge,Object.assign(o.j,l.j),o.jr.push.apply(o.jr,l.jr),o.jd=l.jd,o.t=l.t):o=new ge,s){if(r)if(o.t&&typeof o.t=="string"){const a=Object.assign(lg(o.t,r),t);Ct(s,a,r)}else t&&Ct(s,t,r);o.t=s}return i.j[n]=o,o}};const L=(n,e,t,r,i)=>n.ta(e,t,r,i),G=(n,e,t,r,i)=>n.tr(e,t,r,i),pl=(n,e,t,r,i)=>n.ts(e,t,r,i),k=(n,e,t,r,i)=>n.tt(e,t,r,i),_e="WORD",es="UWORD",$c="ASCIINUMERICAL",Hc="ALPHANUMERICAL",wn="LOCALHOST",ts="TLD",ns="UTLD",Gn="SCHEME",_t="SLASH_SCHEME",Rs="NUM",rs="WS",Ps="NL",dn="OPENBRACE",fn="CLOSEBRACE",dr="OPENBRACKET",fr="CLOSEBRACKET",hr="OPENPAREN",pr="CLOSEPAREN",mr="OPENANGLEBRACKET",gr="CLOSEANGLEBRACKET",yr="FULLWIDTHLEFTPAREN",br="FULLWIDTHRIGHTPAREN",kr="LEFTCORNERBRACKET",xr="RIGHTCORNERBRACKET",Sr="LEFTWHITECORNERBRACKET",vr="RIGHTWHITECORNERBRACKET",Cr="FULLWIDTHLESSTHAN",Mr="FULLWIDTHGREATERTHAN",wr="AMPERSAND",Tr="APOSTROPHE",Er="ASTERISK",et="AT",Ar="BACKSLASH",Or="BACKTICK",Nr="CARET",rt="COLON",Ls="COMMA",Dr="DOLLAR",Ie="DOT",Ir="EQUALS",Bs="EXCLAMATION",Ce="HYPHEN",hn="PERCENT",Rr="PIPE",Pr="PLUS",Lr="POUND",pn="QUERY",zs="QUOTE",_c="FULLWIDTHMIDDLEDOT",Fs="SEMI",Re="SLASH",mn="TILDE",Br="UNDERSCORE",Vc="EMOJI",zr="SYM";var jc=Object.freeze({__proto__:null,ALPHANUMERICAL:Hc,AMPERSAND:wr,APOSTROPHE:Tr,ASCIINUMERICAL:$c,ASTERISK:Er,AT:et,BACKSLASH:Ar,BACKTICK:Or,CARET:Nr,CLOSEANGLEBRACKET:gr,CLOSEBRACE:fn,CLOSEBRACKET:fr,CLOSEPAREN:pr,COLON:rt,COMMA:Ls,DOLLAR:Dr,DOT:Ie,EMOJI:Vc,EQUALS:Ir,EXCLAMATION:Bs,FULLWIDTHGREATERTHAN:Mr,FULLWIDTHLEFTPAREN:yr,FULLWIDTHLESSTHAN:Cr,FULLWIDTHMIDDLEDOT:_c,FULLWIDTHRIGHTPAREN:br,HYPHEN:Ce,LEFTCORNERBRACKET:kr,LEFTWHITECORNERBRACKET:Sr,LOCALHOST:wn,NL:Ps,NUM:Rs,OPENANGLEBRACKET:mr,OPENBRACE:dn,OPENBRACKET:dr,OPENPAREN:hr,PERCENT:hn,PIPE:Rr,PLUS:Pr,POUND:Lr,QUERY:pn,QUOTE:zs,RIGHTCORNERBRACKET:xr,RIGHTWHITECORNERBRACKET:vr,SCHEME:Gn,SEMI:Fs,SLASH:Re,SLASH_SCHEME:_t,SYM:zr,TILDE:mn,TLD:ts,UNDERSCORE:Br,UTLD:ns,UWORD:es,WORD:_e,WS:rs});const $e=/[a-z]/,en=new RegExp("\\p{L}","u"),wi=new RegExp("\\p{Emoji}","u");const He=/\d/,Ti=/\s/;const ml="\r",Ei=`
`,ag="️",cg="‍",Ai="￼";let Vn=null,jn=null;function ug(n=[]){const e={};ge.groups=e;const t=new ge;Vn==null&&(Vn=gl(ng)),jn==null&&(jn=gl(rg)),k(t,"'",Tr),k(t,"{",dn),k(t,"}",fn),k(t,"[",dr),k(t,"]",fr),k(t,"(",hr),k(t,")",pr),k(t,"<",mr),k(t,">",gr),k(t,"（",yr),k(t,"）",br),k(t,"「",kr),k(t,"」",xr),k(t,"『",Sr),k(t,"』",vr),k(t,"＜",Cr),k(t,"＞",Mr),k(t,"&",wr),k(t,"*",Er),k(t,"@",et),k(t,"`",Or),k(t,"^",Nr),k(t,":",rt),k(t,",",Ls),k(t,"$",Dr),k(t,".",Ie),k(t,"=",Ir),k(t,"!",Bs),k(t,"-",Ce),k(t,"%",hn),k(t,"|",Rr),k(t,"+",Pr),k(t,"#",Lr),k(t,"?",pn),k(t,'"',zs),k(t,"/",Re),k(t,";",Fs),k(t,"~",mn),k(t,"_",Br),k(t,"\\",Ar),k(t,"・",_c);const r=G(t,He,Rs,{[Yi]:!0});G(r,He,r);const i=G(r,$e,$c,{[un]:!0}),s=G(r,en,Hc,{[sn]:!0}),o=G(t,$e,_e,{[Xi]:!0});G(o,He,i),G(o,$e,o),G(i,He,i),G(i,$e,i);const l=G(t,en,es,{[Qi]:!0});G(l,$e),G(l,He,s),G(l,en,l),G(s,He,s),G(s,$e),G(s,en,s);const a=k(t,Ei,Ps,{[Mi]:!0}),c=k(t,ml,rs,{[Mi]:!0}),u=G(t,Ti,rs,{[Mi]:!0});k(t,Ai,u),k(c,Ei,a),k(c,Ai,u),G(c,Ti,u),k(u,ml),k(u,Ei),G(u,Ti,u),k(u,Ai,u);const d=G(t,wi,Vc,{[Fc]:!0});k(d,"#"),G(d,wi,d),k(d,ag,d);const f=k(d,cg);k(f,"#"),G(f,wi,d);const h=[[$e,o],[He,i]],p=[[$e,null],[en,l],[He,s]];for(let m=0;m<Vn.length;m++)Ye(t,Vn[m],ts,_e,h);for(let m=0;m<jn.length;m++)Ye(t,jn[m],ns,es,p);Ct(ts,{tld:!0,ascii:!0},e),Ct(ns,{utld:!0,alpha:!0},e),Ye(t,"file",Gn,_e,h),Ye(t,"mailto",Gn,_e,h),Ye(t,"http",_t,_e,h),Ye(t,"https",_t,_e,h),Ye(t,"ftp",_t,_e,h),Ye(t,"ftps",_t,_e,h),Ct(Gn,{scheme:!0,ascii:!0},e),Ct(_t,{slashscheme:!0,ascii:!0},e),n=n.sort((m,g)=>m[0]>g[0]?1:-1);for(let m=0;m<n.length;m++){const g=n[m][0],v=n[m][1]?{[ig]:!0}:{[sg]:!0};g.indexOf("-")>=0?v[Zi]=!0:$e.test(g)?He.test(g)?v[un]=!0:v[Xi]=!0:v[Yi]=!0,pl(t,g,g,v)}return pl(t,"localhost",wn,{ascii:!0}),t.jd=new ge(zr),{start:t,tokens:Object.assign({groups:e},jc)}}function Wc(n,e){const t=dg(e.replace(/[A-Z]/g,l=>l.toLowerCase())),r=t.length,i=[];let s=0,o=0;for(;o<r;){let l=n,a=null,c=0,u=null,d=-1,f=-1;for(;o<r&&(a=l.go(t[o]));)l=a,l.accepts()?(d=0,f=0,u=l):d>=0&&(d+=t[o].length,f++),c+=t[o].length,s+=t[o].length,o++;s-=d,o-=f,c-=d,i.push({t:u.t,v:e.slice(s-c,s),s:s-c,e:s})}return i}function dg(n){const e=[],t=n.length;let r=0;for(;r<t;){let i=n.charCodeAt(r),s,o=i<55296||i>56319||r+1===t||(s=n.charCodeAt(r+1))<56320||s>57343?n[r]:n.slice(r,r+2);e.push(o),r+=o.length}return e}function Ye(n,e,t,r,i){let s;const o=e.length;for(let l=0;l<o-1;l++){const a=e[l];n.j[a]?s=n.j[a]:(s=new ge(r),s.jr=i.slice(),n.j[a]=s),n=s}return s=new ge(t),s.jr=i.slice(),n.j[e[o-1]]=s,s}function gl(n){const e=[],t=[];let r=0,i="0123456789";for(;r<n.length;){let s=0;for(;i.indexOf(n[r+s])>=0;)s++;if(s>0){e.push(t.join(""));for(let o=parseInt(n.substring(r,r+s),10);o>0;o--)t.pop();r+=s}else t.push(n[r]),r++}return e}const Tn={defaultProtocol:"http",events:null,format:yl,formatHref:yl,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function $s(n,e=null){let t=Object.assign({},Tn);n&&(t=Object.assign(t,n instanceof $s?n.o:n));const r=t.ignoreTags,i=[];for(let s=0;s<r.length;s++)i.push(r[s].toUpperCase());this.o=t,e&&(this.defaultRender=e),this.ignoreTags=i}$s.prototype={o:Tn,ignoreTags:[],defaultRender(n){return n},check(n){return this.get("validate",n.toString(),n)},get(n,e,t){const r=e!=null;let i=this.o[n];return i&&(typeof i=="object"?(i=t.t in i?i[t.t]:Tn[n],typeof i=="function"&&r&&(i=i(e,t))):typeof i=="function"&&r&&(i=i(e,t.t,t)),i)},getObj(n,e,t){let r=this.o[n];return typeof r=="function"&&e!=null&&(r=r(e,t.t,t)),r},render(n){const e=n.render(this);return(this.get("render",null,n)||this.defaultRender)(e,n.t,n)}};function yl(n){return n}function Kc(n,e){this.t="token",this.v=n,this.tk=e}Kc.prototype={isLink:!1,toString(){return this.v},toHref(n){return this.toString()},toFormattedString(n){const e=this.toString(),t=n.get("truncate",e,this),r=n.get("format",e,this);return t&&r.length>t?r.substring(0,t)+"…":r},toFormattedHref(n){return n.get("formatHref",this.toHref(n.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(n=Tn.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(n),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(n){return{type:this.t,value:this.toFormattedString(n),isLink:this.isLink,href:this.toFormattedHref(n),start:this.startIndex(),end:this.endIndex()}},validate(n){return n.get("validate",this.toString(),this)},render(n){const e=this,t=this.toHref(n.get("defaultProtocol")),r=n.get("formatHref",t,this),i=n.get("tagName",t,e),s=this.toFormattedString(n),o={},l=n.get("className",t,e),a=n.get("target",t,e),c=n.get("rel",t,e),u=n.getObj("attributes",t,e),d=n.getObj("events",t,e);return o.href=r,l&&(o.class=l),a&&(o.target=a),c&&(o.rel=c),u&&Object.assign(o,u),{tagName:i,attributes:o,content:s,eventListeners:d}}};function ei(n,e){class t extends Kc{constructor(i,s){super(i,s),this.t=n}}for(const r in e)t.prototype[r]=e[r];return t.t=n,t}const bl=ei("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),kl=ei("text"),fg=ei("nl"),Wn=ei("url",{isLink:!0,toHref(n=Tn.defaultProtocol){return this.hasProtocol()?this.v:`${n}://${this.v}`},hasProtocol(){const n=this.tk;return n.length>=2&&n[0].t!==wn&&n[1].t===rt}});const ve=n=>new ge(n);function hg({groups:n}){const e=n.domain.concat([wr,Er,et,Ar,Or,Nr,Dr,Ir,Ce,Rs,hn,Rr,Pr,Lr,Re,zr,mn,Br]),t=[Tr,rt,Ls,Ie,Bs,hn,pn,zs,Fs,mr,gr,dn,fn,fr,dr,hr,pr,yr,br,kr,xr,Sr,vr,Cr,Mr],r=[wr,Tr,Er,Ar,Or,Nr,Dr,Ir,Ce,dn,fn,hn,Rr,Pr,Lr,pn,Re,zr,mn,Br],i=ve(),s=k(i,mn);L(s,r,s),L(s,n.domain,s);const o=ve(),l=ve(),a=ve();L(i,n.domain,o),L(i,n.scheme,l),L(i,n.slashscheme,a),L(o,r,s),L(o,n.domain,o);const c=k(o,et);k(s,et,c),k(l,et,c),k(a,et,c);const u=k(s,Ie);L(u,r,s),L(u,n.domain,s);const d=ve();L(c,n.domain,d),L(d,n.domain,d);const f=k(d,Ie);L(f,n.domain,d);const h=ve(bl);L(f,n.tld,h),L(f,n.utld,h),k(c,wn,h);const p=k(d,Ce);k(p,Ce,p),L(p,n.domain,d),L(h,n.domain,d),k(h,Ie,f),k(h,Ce,p);const m=k(h,rt);L(m,n.numeric,bl);const g=k(o,Ce),b=k(o,Ie);k(g,Ce,g),L(g,n.domain,o),L(b,r,s),L(b,n.domain,o);const v=ve(Wn);L(b,n.tld,v),L(b,n.utld,v),L(v,n.domain,o),L(v,r,s),k(v,Ie,b),k(v,Ce,g),k(v,et,c);const D=k(v,rt),F=ve(Wn);L(D,n.numeric,F);const I=ve(Wn),H=ve();L(I,e,I),L(I,t,H),L(H,e,I),L(H,t,H),k(v,Re,I),k(F,Re,I);const C=k(l,rt),y=k(a,rt),T=k(y,Re),$=k(T,Re);L(l,n.domain,o),k(l,Ie,b),k(l,Ce,g),L(a,n.domain,o),k(a,Ie,b),k(a,Ce,g),L(C,n.domain,I),k(C,Re,I),k(C,pn,I),L($,n.domain,I),L($,e,I),k($,Re,I);const xe=[[dn,fn],[dr,fr],[hr,pr],[mr,gr],[yr,br],[kr,xr],[Sr,vr],[Cr,Mr]];for(let gt=0;gt<xe.length;gt++){const[Je,Xt]=xe[gt],yt=k(I,Je);k(H,Je,yt),k(yt,Xt,I);const ze=ve(Wn);L(yt,e,ze);const Ge=ve();L(yt,t),L(ze,e,ze),L(ze,t,Ge),L(Ge,e,ze),L(Ge,t,Ge),k(ze,Xt,I),k(Ge,Xt,I)}return k(i,wn,v),k(i,Ps,fg),{start:i,tokens:jc}}function pg(n,e,t){let r=t.length,i=0,s=[],o=[];for(;i<r;){let l=n,a=null,c=null,u=0,d=null,f=-1;for(;i<r&&!(a=l.go(t[i].t));)o.push(t[i++]);for(;i<r&&(c=a||l.go(t[i].t));)a=null,l=c,l.accepts()?(f=0,d=l):f>=0&&f++,i++,u++;if(f<0)i-=u,i<r&&(o.push(t[i]),i++);else{o.length>0&&(s.push(Oi(kl,e,o)),o=[]),i-=f,u-=f;const h=d.t,p=t.slice(i-u,i);s.push(Oi(h,e,p))}}return o.length>0&&s.push(Oi(kl,e,o)),s}function Oi(n,e,t){const r=t[0].s,i=t[t.length-1].e,s=e.slice(r,i);return new n(s,t)}const mg=typeof console!="undefined"&&console&&console.warn||(()=>{}),gg="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",W={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function yg(){return ge.groups={},W.scanner=null,W.parser=null,W.tokenQueue=[],W.pluginQueue=[],W.customSchemes=[],W.initialized=!1,W}function xl(n,e=!1){if(W.initialized&&mg(`linkifyjs: already initialized - will not register custom scheme "${n}" ${gg}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(n))throw new Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);W.customSchemes.push([n,e])}function bg(){W.scanner=ug(W.customSchemes);for(let n=0;n<W.tokenQueue.length;n++)W.tokenQueue[n][1]({scanner:W.scanner});W.parser=hg(W.scanner.tokens);for(let n=0;n<W.pluginQueue.length;n++)W.pluginQueue[n][1]({scanner:W.scanner,parser:W.parser});return W.initialized=!0,W}function Hs(n){return W.initialized||bg(),pg(W.parser.start,n,Wc(W.scanner.start,n))}Hs.scan=Wc;function Uc(n,e=null,t=null){if(e&&typeof e=="object"){if(t)throw Error(`linkifyjs: Invalid link type ${e}; must be a string`);t=e,e=null}const r=new $s(t),i=Hs(n),s=[];for(let o=0;o<i.length;o++){const l=i[o];l.isLink&&(!e||l.t===e)&&r.check(l)&&s.push(l.toFormattedObject(r))}return s}var _s="[\0-   ᠎ -\u2029 　]",kg=new RegExp(_s),xg=new RegExp(`${_s}$`),Sg=new RegExp(_s,"g");function vg(n){return n.length===1?n[0].isLink:n.length===3&&n[1].isLink?["()","[]"].includes(n[0].value+n[2].value):!1}function Cg(n){return new Q({key:new ie("autolink"),appendTransaction:(e,t,r)=>{const i=e.some(c=>c.docChanged)&&!t.doc.eq(r.doc),s=e.some(c=>c.getMeta("preventAutolink"));if(!i||s)return;const{tr:o}=r,l=lc(t.doc,[...e]);if(gc(l).forEach(({newRange:c})=>{const u=Uh(r.doc,c,h=>h.isTextblock);let d,f;if(u.length>1)d=u[0],f=r.doc.textBetween(d.pos,d.pos+d.node.nodeSize,void 0," ");else if(u.length){const h=r.doc.textBetween(c.from,c.to," "," ");if(!xg.test(h))return;d=u[0],f=r.doc.textBetween(d.pos,c.to,void 0," ")}if(d&&f){const h=f.split(kg).filter(Boolean);if(h.length<=0)return!1;const p=h[h.length-1],m=d.pos+f.lastIndexOf(p);if(!p)return!1;const g=Hs(p).map(b=>b.toObject(n.defaultProtocol));if(!vg(g))return!1;g.filter(b=>b.isLink).map(b=>U(x({},b),{from:m+b.start+1,to:m+b.end+1})).filter(b=>r.schema.marks.code?!r.doc.rangeHasMark(b.from,b.to,r.schema.marks.code):!0).filter(b=>n.validate(b.value)).filter(b=>n.shouldAutoLink(b.value)).forEach(b=>{Ns(b.from,b.to,r.doc).some(v=>v.mark.type===n.type)||o.addMark(b.from,b.to,n.type.create({href:b.href}))})}}),!!o.steps.length)return o}})}function Mg(n){return new Q({key:new ie("handleClickLink"),props:{handleClick:(e,t,r)=>{var i,s;if(r.button!==0||!e.editable)return!1;let o=null;if(r.target instanceof HTMLAnchorElement)o=r.target;else{let u=r.target;const d=[];for(;u.nodeName!=="DIV";)d.push(u),u=u.parentNode;o=d.find(f=>f.nodeName==="A")}if(!o)return!1;const l=mc(e.state,n.type.name),a=(i=o==null?void 0:o.href)!=null?i:l.href,c=(s=o==null?void 0:o.target)!=null?s:l.target;return n.enableClickSelection&&n.editor.commands.extendMarkRange(n.type.name),o&&a?(window.open(a,c),!0):!1}}})}function wg(n){return new Q({key:new ie("handlePasteLink"),props:{handlePaste:(e,t,r)=>{const{state:i}=e,{selection:s}=i,{empty:o}=s;if(o)return!1;let l="";r.content.forEach(c=>{l+=c.textContent});const a=Uc(l,{defaultProtocol:n.defaultProtocol}).find(c=>c.isLink&&c.value===l);return!l||!a?!1:n.editor.commands.setMark(n.type,{href:a.href})}}})}function bt(n,e){const t=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return e&&e.forEach(r=>{const i=typeof r=="string"?r:r.scheme;i&&t.push(i)}),!n||n.replace(Sg,"").match(new RegExp(`^(?:(?:${t.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}var qc=qe.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate),this.options.protocols.forEach(n=>{if(typeof n=="string"){xl(n);return}xl(n.scheme,n.optionalSlashes)})},onDestroy(){yg()},inclusive(){return this.options.autolink},addOptions(){return{openOnClick:!0,enableClickSelection:!1,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(n,e)=>!!bt(n,e.protocols),validate:n=>!!n,shouldAutoLink:n=>!!n}},addAttributes(){return{href:{default:null,parseHTML(n){return n.getAttribute("href")}},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:n=>{const e=n.getAttribute("href");return!e||!this.options.isAllowedUri(e,{defaultValidate:t=>!!bt(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?!1:null}}]},renderHTML({HTMLAttributes:n}){return this.options.isAllowedUri(n.href,{defaultValidate:e=>!!bt(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",J(this.options.HTMLAttributes,n),0]:["a",J(this.options.HTMLAttributes,U(x({},n),{href:""})),0]},addCommands(){return{setLink:n=>({chain:e})=>{const{href:t}=n;return this.options.isAllowedUri(t,{defaultValidate:r=>!!bt(r,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?e().setMark(this.name,n).setMeta("preventAutolink",!0).run():!1},toggleLink:n=>({chain:e})=>{const{href:t}=n||{};return t&&!this.options.isAllowedUri(t,{defaultValidate:r=>!!bt(r,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?!1:e().toggleMark(this.name,n,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:n})=>n().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[ht({find:n=>{const e=[];if(n){const{protocols:t,defaultProtocol:r}=this.options,i=Uc(n).filter(s=>s.isLink&&this.options.isAllowedUri(s.value,{defaultValidate:o=>!!bt(o,t),protocols:t,defaultProtocol:r}));i.length&&i.forEach(s=>e.push({text:s.value,data:{href:s.href},index:s.start}))}return e},type:this.type,getAttributes:n=>{var e;return{href:(e=n.data)==null?void 0:e.href}}})]},addProseMirrorPlugins(){const n=[],{protocols:e,defaultProtocol:t}=this.options;return this.options.autolink&&n.push(Cg({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:r=>this.options.isAllowedUri(r,{defaultValidate:i=>!!bt(i,e),protocols:e,defaultProtocol:t}),shouldAutoLink:this.options.shouldAutoLink})),this.options.openOnClick===!0&&n.push(Mg({type:this.type,editor:this.editor,enableClickSelection:this.options.enableClickSelection})),this.options.linkOnPaste&&n.push(wg({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),n}}),Tg=qc,Eg=Object.defineProperty,Ag=(n,e)=>{for(var t in e)Eg(n,t,{get:e[t],enumerable:!0})},Og="listItem",Sl="textStyle",vl=/^\s*([-+*])\s$/,Jc=ke.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:n}){return["ul",J(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleBulletList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(Og,this.editor.getAttributes(Sl)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let n=Gt({find:vl,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(n=Gt({find:vl,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(Sl),editor:this.editor})),[n]}}),Gc=ke.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:n}){return["li",J(this.options.HTMLAttributes,n),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),Ng={};Ag(Ng,{findListItemPos:()=>Dn,getNextListDepth:()=>Vs,handleBackspace:()=>is,handleDelete:()=>ss,hasListBefore:()=>Yc,hasListItemAfter:()=>Dg,hasListItemBefore:()=>Xc,listItemHasSubList:()=>Qc,nextListIsDeeper:()=>Zc,nextListIsHigher:()=>eu});var Dn=(n,e)=>{const{$from:t}=e.selection,r=ee(n,e.schema);let i=null,s=t.depth,o=t.pos,l=null;for(;s>0&&l===null;)i=t.node(s),i.type===r?l=s:(s-=1,o-=1);return l===null?null:{$pos:e.doc.resolve(o),depth:l}},Vs=(n,e)=>{const t=Dn(n,e);if(!t)return!1;const[,r]=np(e,n,t.$pos.pos+4);return r},Yc=(n,e,t)=>{const{$anchor:r}=n.selection,i=Math.max(0,r.pos-2),s=n.doc.resolve(i).node();return!(!s||!t.includes(s.type.name))},Xc=(n,e)=>{var t;const{$anchor:r}=e.selection,i=e.doc.resolve(r.pos-2);return!(i.index()===0||((t=i.nodeBefore)==null?void 0:t.type.name)!==n)},Qc=(n,e,t)=>{if(!t)return!1;const r=ee(n,e.schema);let i=!1;return t.descendants(s=>{s.type===r&&(i=!0)}),i},is=(n,e,t)=>{if(n.commands.undoInputRule())return!0;if(n.state.selection.from!==n.state.selection.to)return!1;if(!ft(n.state,e)&&Yc(n.state,e,t)){const{$anchor:l}=n.state.selection,a=n.state.doc.resolve(l.before()-1),c=[];a.node().descendants((f,h)=>{f.type.name===e&&c.push({node:f,pos:h})});const u=c.at(-1);if(!u)return!1;const d=n.state.doc.resolve(a.start()+u.pos+1);return n.chain().cut({from:l.start()-1,to:l.end()+1},d.end()).joinForward().run()}if(!ft(n.state,e)||!op(n.state))return!1;const r=Dn(e,n.state);if(!r)return!1;const s=n.state.doc.resolve(r.$pos.pos-2).node(r.depth),o=Qc(e,n.state,s);return Xc(e,n.state)&&!o?n.commands.joinItemBackward():n.chain().liftListItem(e).run()},Zc=(n,e)=>{const t=Vs(n,e),r=Dn(n,e);return!r||!t?!1:t>r.depth},eu=(n,e)=>{const t=Vs(n,e),r=Dn(n,e);return!r||!t?!1:t<r.depth},ss=(n,e)=>{if(!ft(n.state,e)||!sp(n.state,e))return!1;const{selection:t}=n.state,{$from:r,$to:i}=t;return!t.empty&&r.sameParent(i)?!1:Zc(e,n.state)?n.chain().focus(n.state.selection.from+4).lift(e).joinBackward().run():eu(e,n.state)?n.chain().joinForward().joinBackward().run():n.commands.joinItemForward()},Dg=(n,e)=>{var t;const{$anchor:r}=e.selection,i=e.doc.resolve(r.pos-r.parentOffset-2);return!(i.index()===i.parent.childCount-1||((t=i.nodeAfter)==null?void 0:t.type.name)!==n)},tu=V.create({name:"listKeymap",addOptions(){return{listTypes:[{itemName:"listItem",wrapperNames:["bulletList","orderedList"]},{itemName:"taskItem",wrapperNames:["taskList"]}]}},addKeyboardShortcuts(){return{Delete:({editor:n})=>{let e=!1;return this.options.listTypes.forEach(({itemName:t})=>{n.state.schema.nodes[t]!==void 0&&ss(n,t)&&(e=!0)}),e},"Mod-Delete":({editor:n})=>{let e=!1;return this.options.listTypes.forEach(({itemName:t})=>{n.state.schema.nodes[t]!==void 0&&ss(n,t)&&(e=!0)}),e},Backspace:({editor:n})=>{let e=!1;return this.options.listTypes.forEach(({itemName:t,wrapperNames:r})=>{n.state.schema.nodes[t]!==void 0&&is(n,t,r)&&(e=!0)}),e},"Mod-Backspace":({editor:n})=>{let e=!1;return this.options.listTypes.forEach(({itemName:t,wrapperNames:r})=>{n.state.schema.nodes[t]!==void 0&&is(n,t,r)&&(e=!0)}),e}}}}),Ig="listItem",Cl="textStyle",Ml=/^(\d+)\.\s$/,nu=ke.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:n=>n.hasAttribute("start")?parseInt(n.getAttribute("start")||"",10):1},type:{default:null,parseHTML:n=>n.getAttribute("type")}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:n}){const r=n,{start:e}=r,t=ni(r,["start"]);return e===1?["ol",J(this.options.HTMLAttributes,t),0]:["ol",J(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleOrderedList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(Ig,this.editor.getAttributes(Cl)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let n=Gt({find:Ml,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(n=Gt({find:Ml,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>x({start:+e[1]},this.editor.getAttributes(Cl)),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[n]}}),Rg=/^\s*(\[([( |x])?\])\s$/,Pg=ke.create({name:"taskItem",addOptions(){return{nested:!1,HTMLAttributes:{},taskListTypeName:"taskList",a11y:void 0}},content(){return this.options.nested?"paragraph block*":"paragraph+"},defining:!0,addAttributes(){return{checked:{default:!1,keepOnSplit:!1,parseHTML:n=>{const e=n.getAttribute("data-checked");return e===""||e==="true"},renderHTML:n=>({"data-checked":n.checked})}}},parseHTML(){return[{tag:`li[data-type="${this.name}"]`,priority:51}]},renderHTML({node:n,HTMLAttributes:e}){return["li",J(this.options.HTMLAttributes,e,{"data-type":this.name}),["label",["input",{type:"checkbox",checked:n.attrs.checked?"checked":null}],["span"]],["div",0]]},addKeyboardShortcuts(){const n={Enter:()=>this.editor.commands.splitListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)};return this.options.nested?U(x({},n),{Tab:()=>this.editor.commands.sinkListItem(this.name)}):n},addNodeView(){return({node:n,HTMLAttributes:e,getPos:t,editor:r})=>{const i=document.createElement("li"),s=document.createElement("label"),o=document.createElement("span"),l=document.createElement("input"),a=document.createElement("div"),c=()=>{var u,d;l.ariaLabel=((d=(u=this.options.a11y)==null?void 0:u.checkboxLabel)==null?void 0:d.call(u,n,l.checked))||`Task item checkbox for ${n.textContent||"empty task item"}`};return c(),s.contentEditable="false",l.type="checkbox",l.addEventListener("mousedown",u=>u.preventDefault()),l.addEventListener("change",u=>{if(!r.isEditable&&!this.options.onReadOnlyChecked){l.checked=!l.checked;return}const{checked:d}=u.target;r.isEditable&&typeof t=="function"&&r.chain().focus(void 0,{scrollIntoView:!1}).command(({tr:f})=>{const h=t();if(typeof h!="number")return!1;const p=f.doc.nodeAt(h);return f.setNodeMarkup(h,void 0,U(x({},p==null?void 0:p.attrs),{checked:d})),!0}).run(),!r.isEditable&&this.options.onReadOnlyChecked&&(this.options.onReadOnlyChecked(n,d)||(l.checked=!l.checked))}),Object.entries(this.options.HTMLAttributes).forEach(([u,d])=>{i.setAttribute(u,d)}),i.dataset.checked=n.attrs.checked,l.checked=n.attrs.checked,s.append(l,o),i.append(s,a),Object.entries(e).forEach(([u,d])=>{i.setAttribute(u,d)}),{dom:i,contentDOM:a,update:u=>u.type!==this.type?!1:(i.dataset.checked=u.attrs.checked,l.checked=u.attrs.checked,c(),!0)}}},addInputRules(){return[Gt({find:Rg,type:this.type,getAttributes:n=>({checked:n[n.length-1]==="x"})})]}}),Lg=ke.create({name:"taskList",addOptions(){return{itemTypeName:"taskItem",HTMLAttributes:{}}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:`ul[data-type="${this.name}"]`,priority:51}]},renderHTML({HTMLAttributes:n}){return["ul",J(this.options.HTMLAttributes,n,{"data-type":this.name}),0]},addCommands(){return{toggleTaskList:()=>({commands:n})=>n.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-9":()=>this.editor.commands.toggleTaskList()}}}),f0=V.create({name:"listKit",addExtensions(){const n=[];return this.options.bulletList!==!1&&n.push(Jc.configure(this.options.bulletList)),this.options.listItem!==!1&&n.push(Gc.configure(this.options.listItem)),this.options.listKeymap!==!1&&n.push(tu.configure(this.options.listKeymap)),this.options.orderedList!==!1&&n.push(nu.configure(this.options.orderedList)),this.options.taskItem!==!1&&n.push(Pg.configure(this.options.taskItem)),this.options.taskList!==!1&&n.push(Lg.configure(this.options.taskList)),n}}),Bg=ke.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:n}){return["p",J(this.options.HTMLAttributes,n),0]},addCommands(){return{setParagraph:()=>({commands:n})=>n.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}});var zg=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,Fg=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,$g=qe.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:n=>n.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["s",J(this.options.HTMLAttributes,n),0]},addCommands(){return{setStrike:()=>({commands:n})=>n.setMark(this.name),toggleStrike:()=>({commands:n})=>n.toggleMark(this.name),unsetStrike:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[Rt({find:zg,type:this.type})]},addPasteRules(){return[ht({find:Fg,type:this.type})]}});var Hg=ke.create({name:"text",group:"inline"});var ru=qe.create({name:"underline",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:n=>n.includes("underline")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["u",J(this.options.HTMLAttributes,n),0]},addCommands(){return{setUnderline:()=>({commands:n})=>n.setMark(this.name),toggleUnderline:()=>({commands:n})=>n.toggleMark(this.name),unsetUnderline:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}}),_g=ru;function Vg(n={}){return new Q({view(e){return new jg(e,n)}})}class jg{constructor(e,t){var r;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=(r=t.width)!==null&&r!==void 0?r:1,this.color=t.color===!1?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(i=>{let s=o=>{this[i](o)};return e.dom.addEventListener(i,s),{name:i,handler:s}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){this.cursorPos!=null&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,e==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e=this.editorView.state.doc.resolve(this.cursorPos),t=!e.parent.inlineContent,r,i=this.editorView.dom,s=i.getBoundingClientRect(),o=s.width/i.offsetWidth,l=s.height/i.offsetHeight;if(t){let d=e.nodeBefore,f=e.nodeAfter;if(d||f){let h=this.editorView.nodeDOM(this.cursorPos-(d?d.nodeSize:0));if(h){let p=h.getBoundingClientRect(),m=d?p.bottom:p.top;d&&f&&(m=(m+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let g=this.width/2*l;r={left:p.left,right:p.right,top:m-g,bottom:m+g}}}}if(!r){let d=this.editorView.coordsAtPos(this.cursorPos),f=this.width/2*o;r={left:d.left-f,right:d.left+f,top:d.top,bottom:d.bottom}}let a=this.editorView.dom.offsetParent;this.element||(this.element=a.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",t),this.element.classList.toggle("prosemirror-dropcursor-inline",!t);let c,u;if(!a||a==document.body&&getComputedStyle(a).position=="static")c=-pageXOffset,u=-pageYOffset;else{let d=a.getBoundingClientRect(),f=d.width/a.offsetWidth,h=d.height/a.offsetHeight;c=d.left-a.scrollLeft*f,u=d.top-a.scrollTop*h}this.element.style.left=(r.left-c)/o+"px",this.element.style.top=(r.top-u)/l+"px",this.element.style.width=(r.right-r.left)/o+"px",this.element.style.height=(r.bottom-r.top)/l+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),r=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),i=r&&r.type.spec.disableDropCursor,s=typeof i=="function"?i(this.editorView,t,e):i;if(t&&!s){let o=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let l=na(this.editorView.state.doc,o,this.editorView.dragging.slice);l!=null&&(o=l)}this.setCursor(o),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}class X extends P{constructor(e){super(e,e)}map(e,t){let r=e.resolve(t.map(this.head));return X.valid(r)?new X(r):P.near(r)}content(){return w.empty}eq(e){return e instanceof X&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new X(e.resolve(t.pos))}getBookmark(){return new js(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!Wg(e)||!Kg(e))return!1;let r=t.type.spec.allowGapCursor;if(r!=null)return r;let i=t.contentMatchAt(e.index()).defaultType;return i&&i.isTextblock}static findGapCursorFrom(e,t,r=!1){e:for(;;){if(!r&&X.valid(e))return e;let i=e.pos,s=null;for(let o=e.depth;;o--){let l=e.node(o);if(t>0?e.indexAfter(o)<l.childCount:e.index(o)>0){s=l.child(t>0?e.indexAfter(o):e.index(o)-1);break}else if(o==0)return null;i+=t;let a=e.doc.resolve(i);if(X.valid(a))return a}for(;;){let o=t>0?s.firstChild:s.lastChild;if(!o){if(s.isAtom&&!s.isText&&!A.isSelectable(s)){e=e.doc.resolve(i+s.nodeSize*t),r=!1;continue e}break}s=o,i+=t;let l=e.doc.resolve(i);if(X.valid(l))return l}return null}}}X.prototype.visible=!1;X.findFrom=X.findGapCursorFrom;P.jsonID("gapcursor",X);class js{constructor(e){this.pos=e}map(e){return new js(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return X.valid(t)?new X(t):P.near(t)}}function Wg(n){for(let e=n.depth;e>=0;e--){let t=n.index(e),r=n.node(e);if(t==0){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t-1);;i=i.lastChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function Kg(n){for(let e=n.depth;e>=0;e--){let t=n.indexAfter(e),r=n.node(e);if(t==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t);;i=i.firstChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function Ug(){return new Q({props:{decorations:Yg,createSelectionBetween(n,e,t){return e.pos==t.pos&&X.valid(t)?new X(t):null},handleClick:Jg,handleKeyDown:qg,handleDOMEvents:{beforeinput:Gg}}})}const qg=Wa({ArrowLeft:Kn("horiz",-1),ArrowRight:Kn("horiz",1),ArrowUp:Kn("vert",-1),ArrowDown:Kn("vert",1)});function Kn(n,e){const t=n=="vert"?e>0?"down":"up":e>0?"right":"left";return function(r,i,s){let o=r.selection,l=e>0?o.$to:o.$from,a=o.empty;if(o instanceof R){if(!s.endOfTextblock(t)||l.depth==0)return!1;a=!1,l=r.doc.resolve(e>0?l.after():l.before())}let c=X.findGapCursorFrom(l,e,a);return c?(i&&i(r.tr.setSelection(new X(c))),!0):!1}}function Jg(n,e,t){if(!n||!n.editable)return!1;let r=n.state.doc.resolve(e);if(!X.valid(r))return!1;let i=n.posAtCoords({left:t.clientX,top:t.clientY});return i&&i.inside>-1&&A.isSelectable(n.state.doc.nodeAt(i.inside))?!1:(n.dispatch(n.state.tr.setSelection(new X(r))),!0)}function Gg(n,e){if(e.inputType!="insertCompositionText"||!(n.state.selection instanceof X))return!1;let{$from:t}=n.state.selection,r=t.parent.contentMatchAt(t.index()).findWrapping(n.state.schema.nodes.text);if(!r)return!1;let i=S.empty;for(let o=r.length-1;o>=0;o--)i=S.from(r[o].createAndFill(null,i));let s=n.state.tr.replace(t.pos,t.pos,new w(i,0,0));return s.setSelection(R.near(s.doc.resolve(t.pos+1))),n.dispatch(s),!1}function Yg(n){if(!(n.selection instanceof X))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",q.create(n.doc,[fe.widget(n.selection.head,e,{key:"gapcursor"})])}var Fr=200,re=function(){};re.prototype.append=function(e){return e.length?(e=re.from(e),!this.length&&e||e.length<Fr&&this.leafAppend(e)||this.length<Fr&&e.leafPrepend(this)||this.appendInner(e)):this};re.prototype.prepend=function(e){return e.length?re.from(e).append(this):this};re.prototype.appendInner=function(e){return new Xg(this,e)};re.prototype.slice=function(e,t){return e===void 0&&(e=0),t===void 0&&(t=this.length),e>=t?re.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))};re.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)};re.prototype.forEach=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length),t<=r?this.forEachInner(e,t,r,0):this.forEachInvertedInner(e,t,r,0)};re.prototype.map=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length);var i=[];return this.forEach(function(s,o){return i.push(e(s,o))},t,r),i};re.from=function(e){return e instanceof re?e:e&&e.length?new iu(e):re.empty};var iu=function(n){function e(r){n.call(this),this.values=r}n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(i,s){return i==0&&s==this.length?this:new e(this.values.slice(i,s))},e.prototype.getInner=function(i){return this.values[i]},e.prototype.forEachInner=function(i,s,o,l){for(var a=s;a<o;a++)if(i(this.values[a],l+a)===!1)return!1},e.prototype.forEachInvertedInner=function(i,s,o,l){for(var a=s-1;a>=o;a--)if(i(this.values[a],l+a)===!1)return!1},e.prototype.leafAppend=function(i){if(this.length+i.length<=Fr)return new e(this.values.concat(i.flatten()))},e.prototype.leafPrepend=function(i){if(this.length+i.length<=Fr)return new e(i.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(re);re.empty=new iu([]);var Xg=function(n){function e(t,r){n.call(this),this.left=t,this.right=r,this.length=t.length+r.length,this.depth=Math.max(t.depth,r.depth)+1}return n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(r){return r<this.left.length?this.left.get(r):this.right.get(r-this.left.length)},e.prototype.forEachInner=function(r,i,s,o){var l=this.left.length;if(i<l&&this.left.forEachInner(r,i,Math.min(s,l),o)===!1||s>l&&this.right.forEachInner(r,Math.max(i-l,0),Math.min(this.length,s)-l,o+l)===!1)return!1},e.prototype.forEachInvertedInner=function(r,i,s,o){var l=this.left.length;if(i>l&&this.right.forEachInvertedInner(r,i-l,Math.max(s,l)-l,o+l)===!1||s<l&&this.left.forEachInvertedInner(r,Math.min(i,l),s,o)===!1)return!1},e.prototype.sliceInner=function(r,i){if(r==0&&i==this.length)return this;var s=this.left.length;return i<=s?this.left.slice(r,i):r>=s?this.right.slice(r-s,i-s):this.left.slice(r,s).append(this.right.slice(0,i-s))},e.prototype.leafAppend=function(r){var i=this.right.leafAppend(r);if(i)return new e(this.left,i)},e.prototype.leafPrepend=function(r){var i=this.left.leafPrepend(r);if(i)return new e(i,this.right)},e.prototype.appendInner=function(r){return this.left.depth>=Math.max(this.right.depth,r.depth)+1?new e(this.left,new e(this.right,r)):new e(this,r)},e}(re);const Qg=500;class Ae{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let r=this.items.length;for(;;r--)if(this.items.get(r-1).selection){--r;break}let i,s;t&&(i=this.remapping(r,this.items.length),s=i.maps.length);let o=e.tr,l,a,c=[],u=[];return this.items.forEach((d,f)=>{if(!d.step){i||(i=this.remapping(r,f+1),s=i.maps.length),s--,u.push(d);return}if(i){u.push(new Pe(d.map));let h=d.step.map(i.slice(s)),p;h&&o.maybeStep(h).doc&&(p=o.mapping.maps[o.mapping.maps.length-1],c.push(new Pe(p,void 0,void 0,c.length+u.length))),s--,p&&i.appendMap(p,s)}else o.maybeStep(d.step);if(d.selection)return l=i?d.selection.map(i.slice(s)):d.selection,a=new Ae(this.items.slice(0,r).append(u.reverse().concat(c)),this.eventCount-1),!1},this.items.length,0),{remaining:a,transform:o,selection:l}}addTransform(e,t,r,i){let s=[],o=this.eventCount,l=this.items,a=!i&&l.length?l.get(l.length-1):null;for(let u=0;u<e.steps.length;u++){let d=e.steps[u].invert(e.docs[u]),f=new Pe(e.mapping.maps[u],d,t),h;(h=a&&a.merge(f))&&(f=h,u?s.pop():l=l.slice(0,l.length-1)),s.push(f),t&&(o++,t=void 0),i||(a=f)}let c=o-r.depth;return c>ey&&(l=Zg(l,c),o-=c),new Ae(l.append(s),o)}remapping(e,t){let r=new bn;return this.items.forEach((i,s)=>{let o=i.mirrorOffset!=null&&s-i.mirrorOffset>=e?r.maps.length-i.mirrorOffset:void 0;r.appendMap(i.map,o)},e,t),r}addMaps(e){return this.eventCount==0?this:new Ae(this.items.append(e.map(t=>new Pe(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let r=[],i=Math.max(0,this.items.length-t),s=e.mapping,o=e.steps.length,l=this.eventCount;this.items.forEach(f=>{f.selection&&l--},i);let a=t;this.items.forEach(f=>{let h=s.getMirror(--a);if(h==null)return;o=Math.min(o,h);let p=s.maps[h];if(f.step){let m=e.steps[h].invert(e.docs[h]),g=f.selection&&f.selection.map(s.slice(a+1,h));g&&l++,r.push(new Pe(p,m,g))}else r.push(new Pe(p))},i);let c=[];for(let f=t;f<o;f++)c.push(new Pe(s.maps[f]));let u=this.items.slice(0,i).append(c).append(r),d=new Ae(u,l);return d.emptyItemCount()>Qg&&(d=d.compress(this.items.length-r.length)),d}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),r=t.maps.length,i=[],s=0;return this.items.forEach((o,l)=>{if(l>=e)i.push(o),o.selection&&s++;else if(o.step){let a=o.step.map(t.slice(r)),c=a&&a.getMap();if(r--,c&&t.appendMap(c,r),a){let u=o.selection&&o.selection.map(t.slice(r));u&&s++;let d=new Pe(c.invert(),a,u),f,h=i.length-1;(f=i.length&&i[h].merge(d))?i[h]=f:i.push(d)}}else o.map&&r--},this.items.length,0),new Ae(re.from(i.reverse()),s)}}Ae.empty=new Ae(re.empty,0);function Zg(n,e){let t;return n.forEach((r,i)=>{if(r.selection&&e--==0)return t=i,!1}),n.slice(t)}class Pe{constructor(e,t,r,i){this.map=e,this.step=t,this.selection=r,this.mirrorOffset=i}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new Pe(t.getMap().invert(),t,this.selection)}}}class tt{constructor(e,t,r,i,s){this.done=e,this.undone=t,this.prevRanges=r,this.prevTime=i,this.prevComposition=s}}const ey=20;function ty(n,e,t,r){let i=t.getMeta(At),s;if(i)return i.historyState;t.getMeta(iy)&&(n=new tt(n.done,n.undone,null,0,-1));let o=t.getMeta("appendedTransaction");if(t.steps.length==0)return n;if(o&&o.getMeta(At))return o.getMeta(At).redo?new tt(n.done.addTransform(t,void 0,r,Yn(e)),n.undone,wl(t.mapping.maps),n.prevTime,n.prevComposition):new tt(n.done,n.undone.addTransform(t,void 0,r,Yn(e)),null,n.prevTime,n.prevComposition);if(t.getMeta("addToHistory")!==!1&&!(o&&o.getMeta("addToHistory")===!1)){let l=t.getMeta("composition"),a=n.prevTime==0||!o&&n.prevComposition!=l&&(n.prevTime<(t.time||0)-r.newGroupDelay||!ny(t,n.prevRanges)),c=o?Ni(n.prevRanges,t.mapping):wl(t.mapping.maps);return new tt(n.done.addTransform(t,a?e.selection.getBookmark():void 0,r,Yn(e)),Ae.empty,c,t.time,l==null?n.prevComposition:l)}else return(s=t.getMeta("rebased"))?new tt(n.done.rebased(t,s),n.undone.rebased(t,s),Ni(n.prevRanges,t.mapping),n.prevTime,n.prevComposition):new tt(n.done.addMaps(t.mapping.maps),n.undone.addMaps(t.mapping.maps),Ni(n.prevRanges,t.mapping),n.prevTime,n.prevComposition)}function ny(n,e){if(!e)return!1;if(!n.docChanged)return!0;let t=!1;return n.mapping.maps[0].forEach((r,i)=>{for(let s=0;s<e.length;s+=2)r<=e[s+1]&&i>=e[s]&&(t=!0)}),t}function wl(n){let e=[];for(let t=n.length-1;t>=0&&e.length==0;t--)n[t].forEach((r,i,s,o)=>e.push(s,o));return e}function Ni(n,e){if(!n)return null;let t=[];for(let r=0;r<n.length;r+=2){let i=e.map(n[r],1),s=e.map(n[r+1],-1);i<=s&&t.push(i,s)}return t}function ry(n,e,t){let r=Yn(e),i=At.get(e).spec.config,s=(t?n.undone:n.done).popEvent(e,r);if(!s)return null;let o=s.selection.resolve(s.transform.doc),l=(t?n.done:n.undone).addTransform(s.transform,e.selection.getBookmark(),i,r),a=new tt(t?l:s.remaining,t?s.remaining:l,null,0,-1);return s.transform.setSelection(o).setMeta(At,{redo:t,historyState:a})}let Di=!1,Tl=null;function Yn(n){let e=n.plugins;if(Tl!=e){Di=!1,Tl=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){Di=!0;break}}return Di}const At=new ie("history"),iy=new ie("closeHistory");function sy(n={}){return n={depth:n.depth||100,newGroupDelay:n.newGroupDelay||500},new Q({key:At,state:{init(){return new tt(Ae.empty,Ae.empty,null,0,-1)},apply(e,t,r){return ty(t,r,e,n)}},config:n,props:{handleDOMEvents:{beforeinput(e,t){let r=t.inputType,i=r=="historyUndo"?su:r=="historyRedo"?ou:null;return i?(t.preventDefault(),i(e.state,e.dispatch)):!1}}}})}function ti(n,e){return(t,r)=>{let i=At.getState(t);if(!i||(n?i.undone:i.done).eventCount==0)return!1;if(r){let s=ry(i,t,n);s&&r(e?s.scrollIntoView():s)}return!0}}const su=ti(!1,!0),ou=ti(!0,!0),h0=ti(!1,!1),p0=ti(!0,!1);var m0=V.create({name:"characterCount",addOptions(){return{limit:null,mode:"textSize",textCounter:n=>n.length,wordCounter:n=>n.split(" ").filter(e=>e!=="").length}},addStorage(){return{characters:()=>0,words:()=>0}},onBeforeCreate(){this.storage.characters=n=>{const e=(n==null?void 0:n.node)||this.editor.state.doc;if(((n==null?void 0:n.mode)||this.options.mode)==="textSize"){const r=e.textBetween(0,e.content.size,void 0," ");return this.options.textCounter(r)}return e.nodeSize},this.storage.words=n=>{const e=(n==null?void 0:n.node)||this.editor.state.doc,t=e.textBetween(0,e.content.size," "," ");return this.options.wordCounter(t)}},addProseMirrorPlugins(){let n=!1;return[new Q({key:new ie("characterCount"),appendTransaction:(e,t,r)=>{if(n)return;const i=this.options.limit;if(i==null||i===0){n=!0;return}const s=this.storage.characters({node:r.doc});if(s>i){const o=s-i,l=0,a=o,c=r.tr.deleteRange(l,a);return n=!0,c}n=!0},filterTransaction:(e,t)=>{const r=this.options.limit;if(!e.docChanged||r===0||r===null||r===void 0)return!0;const i=this.storage.characters({node:t.doc}),s=this.storage.characters({node:e.doc});if(s<=r||i>r&&s>r&&s<=i)return!0;if(i>r&&s>r&&s>i||!e.getMeta("paste"))return!1;const l=e.selection.$head.pos,a=s-r,c=l-a,u=l;return e.deleteRange(c,u),!(this.storage.characters({node:e.doc})>r)}})]}}),oy=V.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[Vg(this.options)]}}),g0=V.create({name:"focus",addOptions(){return{className:"has-focus",mode:"all"}},addProseMirrorPlugins(){return[new Q({key:new ie("focus"),props:{decorations:({doc:n,selection:e})=>{const{isEditable:t,isFocused:r}=this.editor,{anchor:i}=e,s=[];if(!t||!r)return q.create(n,[]);let o=0;this.options.mode==="deepest"&&n.descendants((a,c)=>{if(a.isText)return;if(!(i>=c&&i<=c+a.nodeSize-1))return!1;o+=1});let l=0;return n.descendants((a,c)=>{if(a.isText||!(i>=c&&i<=c+a.nodeSize-1))return!1;if(l+=1,this.options.mode==="deepest"&&o-l>0||this.options.mode==="shallowest"&&l>1)return this.options.mode==="deepest";s.push(fe.node(c,c+a.nodeSize,{class:this.options.className}))}),q.create(n,s)}}})]}}),ly=V.create({name:"gapCursor",addProseMirrorPlugins(){return[Ug()]},extendNodeSchema(n){var e;const t={name:n.name,options:n.options,storage:n.storage};return{allowGapCursor:(e=_(E(n,"allowGapCursor",t)))!=null?e:null}}}),y0=V.create({name:"placeholder",addOptions(){return{emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}},addProseMirrorPlugins(){return[new Q({key:new ie("placeholder"),props:{decorations:({doc:n,selection:e})=>{const t=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:r}=e,i=[];if(!t)return null;const s=this.editor.isEmpty;return n.descendants((o,l)=>{const a=r>=l&&r<=l+o.nodeSize,c=!o.isLeaf&&Xr(o);if((a||!this.options.showOnlyCurrent)&&c){const u=[this.options.emptyNodeClass];s&&u.push(this.options.emptyEditorClass);const d=fe.node(l,l+o.nodeSize,{class:u.join(" "),"data-placeholder":typeof this.options.placeholder=="function"?this.options.placeholder({editor:this.editor,node:o,pos:l,hasAnchor:a}):this.options.placeholder});i.push(d)}return this.options.includeChildren}),q.create(n,i)}}})]}}),b0=V.create({name:"selection",addOptions(){return{className:"selection"}},addProseMirrorPlugins(){const{editor:n,options:e}=this;return[new Q({key:new ie("selection"),props:{decorations(t){return t.selection.empty||n.isFocused||!n.isEditable||bc(t.selection)||n.view.dragging?null:q.create(t.doc,[fe.inline(t.selection.from,t.selection.to,{class:e.className})])}}})]}});function El({types:n,node:e}){return e&&Array.isArray(n)&&n.includes(e.type)||(e==null?void 0:e.type)===n}var ay=V.create({name:"trailingNode",addOptions(){return{node:"paragraph",notAfter:[]}},addProseMirrorPlugins(){const n=new ie(this.name),e=Object.entries(this.editor.schema.nodes).map(([,t])=>t).filter(t=>(this.options.notAfter||[]).concat(this.options.node).includes(t.name));return[new Q({key:n,appendTransaction:(t,r,i)=>{const{doc:s,tr:o,schema:l}=i,a=n.getState(i),c=s.content.size,u=l.nodes[this.options.node];if(a)return o.insert(c,u.create())},state:{init:(t,r)=>{const i=r.tr.doc.lastChild;return!El({node:i,types:e})},apply:(t,r)=>{if(!t.docChanged)return r;const i=t.doc.lastChild;return!El({node:i,types:e})}}})]}}),cy=V.create({name:"undoRedo",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:n,dispatch:e})=>su(n,e),redo:()=>({state:n,dispatch:e})=>ou(n,e)}},addProseMirrorPlugins(){return[sy(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),uy=V.create({name:"starterKit",addExtensions(){var n,e,t,r;const i=[];return this.options.bold!==!1&&i.push(Hm.configure(this.options.bold)),this.options.blockquote!==!1&&i.push(Lm.configure(this.options.blockquote)),this.options.bulletList!==!1&&i.push(Jc.configure(this.options.bulletList)),this.options.code!==!1&&i.push(jm.configure(this.options.code)),this.options.codeBlock!==!1&&i.push(Um.configure(this.options.codeBlock)),this.options.document!==!1&&i.push(qm.configure(this.options.document)),this.options.dropcursor!==!1&&i.push(oy.configure(this.options.dropcursor)),this.options.gapcursor!==!1&&i.push(ly.configure(this.options.gapcursor)),this.options.hardBreak!==!1&&i.push(Jm.configure(this.options.hardBreak)),this.options.heading!==!1&&i.push(Gm.configure(this.options.heading)),this.options.undoRedo!==!1&&i.push(cy.configure(this.options.undoRedo)),this.options.horizontalRule!==!1&&i.push(Ym.configure(this.options.horizontalRule)),this.options.italic!==!1&&i.push(tg.configure(this.options.italic)),this.options.listItem!==!1&&i.push(Gc.configure(this.options.listItem)),this.options.listKeymap!==!1&&i.push(tu.configure((n=this.options)==null?void 0:n.listKeymap)),this.options.link!==!1&&i.push(qc.configure((e=this.options)==null?void 0:e.link)),this.options.orderedList!==!1&&i.push(nu.configure(this.options.orderedList)),this.options.paragraph!==!1&&i.push(Bg.configure(this.options.paragraph)),this.options.strike!==!1&&i.push($g.configure(this.options.strike)),this.options.text!==!1&&i.push(Hg.configure(this.options.text)),this.options.underline!==!1&&i.push(ru.configure((t=this.options)==null?void 0:t.underline)),this.options.trailingNode!==!1&&i.push(ay.configure((r=this.options)==null?void 0:r.trailingNode)),i}}),dy=uy,fy=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,hy=ke.create({name:"image",addOptions(){return{inline:!1,allowBase64:!1,HTMLAttributes:{}}},inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes(){return{src:{default:null},alt:{default:null},title:{default:null},width:{default:null},height:{default:null}}},parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:n}){return["img",J(this.options.HTMLAttributes,n)]},addCommands(){return{setImage:n=>({commands:e})=>e.insertContent({type:this.name,attrs:n})}},addInputRules(){return[Bc({find:fy,type:this.type,getAttributes:n=>{const[,,e,t,r]=n;return{src:t,alt:e,title:r}}})]}}),py=hy,my=V.create({name:"textAlign",addOptions(){return{types:[],alignments:["left","center","right","justify"],defaultAlignment:null}},addGlobalAttributes(){return[{types:this.options.types,attributes:{textAlign:{default:this.options.defaultAlignment,parseHTML:n=>{const e=n.style.textAlign;return this.options.alignments.includes(e)?e:this.options.defaultAlignment},renderHTML:n=>n.textAlign?{style:`text-align: ${n.textAlign}`}:{}}}}]},addCommands(){return{setTextAlign:n=>({commands:e})=>this.options.alignments.includes(n)?this.options.types.map(t=>e.updateAttributes(t,{textAlign:n})).every(t=>t):!1,unsetTextAlign:()=>({commands:n})=>this.options.types.map(e=>n.resetAttributes(e,"textAlign")).every(e=>e),toggleTextAlign:n=>({editor:e,commands:t})=>this.options.alignments.includes(n)?e.isActive({textAlign:n})?t.unsetTextAlign():t.setTextAlign(n):!1}},addKeyboardShortcuts(){return{"Mod-Shift-l":()=>this.editor.commands.setTextAlign("left"),"Mod-Shift-e":()=>this.editor.commands.setTextAlign("center"),"Mod-Shift-r":()=>this.editor.commands.setTextAlign("right"),"Mod-Shift-j":()=>this.editor.commands.setTextAlign("justify")}}}),gy=my,yy=n=>{if(!n.children.length)return;const e=n.querySelectorAll("span");e&&e.forEach(t=>{var r,i;const s=t.getAttribute("style"),o=(i=(r=t.parentElement)==null?void 0:r.closest("span"))==null?void 0:i.getAttribute("style");t.setAttribute("style",`${o};${s}`)})},lu=qe.create({name:"textStyle",priority:101,addOptions(){return{HTMLAttributes:{},mergeNestedSpanStyles:!0}},parseHTML(){return[{tag:"span",consuming:!1,getAttrs:n=>n.hasAttribute("style")?(this.options.mergeNestedSpanStyles&&yy(n),{}):!1}]},renderHTML({HTMLAttributes:n}){return["span",J(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleTextStyle:n=>({commands:e})=>e.toggleMark(this.name,n),removeEmptyTextStyle:()=>({tr:n})=>{const{selection:e}=n;return n.doc.nodesBetween(e.from,e.to,(t,r)=>{if(t.isTextblock)return!0;t.marks.filter(i=>i.type===this.type).some(i=>Object.values(i.attrs).some(s=>!!s))||n.removeMark(r,r+t.nodeSize,this.type)}),!0}}}}),by=V.create({name:"backgroundColor",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{backgroundColor:{default:null,parseHTML:n=>{var e;return(e=n.style.backgroundColor)==null?void 0:e.replace(/['"]+/g,"")},renderHTML:n=>n.backgroundColor?{style:`background-color: ${n.backgroundColor}`}:{}}}}]},addCommands(){return{setBackgroundColor:n=>({chain:e})=>e().setMark("textStyle",{backgroundColor:n}).run(),unsetBackgroundColor:()=>({chain:n})=>n().setMark("textStyle",{backgroundColor:null}).removeEmptyTextStyle().run()}}}),au=V.create({name:"color",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{color:{default:null,parseHTML:n=>{var e;return(e=n.style.color)==null?void 0:e.replace(/['"]+/g,"")},renderHTML:n=>n.color?{style:`color: ${n.color}`}:{}}}}]},addCommands(){return{setColor:n=>({chain:e})=>e().setMark("textStyle",{color:n}).run(),unsetColor:()=>({chain:n})=>n().setMark("textStyle",{color:null}).removeEmptyTextStyle().run()}}}),cu=V.create({name:"fontFamily",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{fontFamily:{default:null,parseHTML:n=>n.style.fontFamily,renderHTML:n=>n.fontFamily?{style:`font-family: ${n.fontFamily}`}:{}}}}]},addCommands(){return{setFontFamily:n=>({chain:e})=>e().setMark("textStyle",{fontFamily:n}).run(),unsetFontFamily:()=>({chain:n})=>n().setMark("textStyle",{fontFamily:null}).removeEmptyTextStyle().run()}}}),ky=V.create({name:"fontSize",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{fontSize:{default:null,parseHTML:n=>n.style.fontSize,renderHTML:n=>n.fontSize?{style:`font-size: ${n.fontSize}`}:{}}}}]},addCommands(){return{setFontSize:n=>({chain:e})=>e().setMark("textStyle",{fontSize:n}).run(),unsetFontSize:()=>({chain:n})=>n().setMark("textStyle",{fontSize:null}).removeEmptyTextStyle().run()}}}),xy=V.create({name:"lineHeight",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{lineHeight:{default:null,parseHTML:n=>n.style.lineHeight,renderHTML:n=>n.lineHeight?{style:`line-height: ${n.lineHeight}`}:{}}}}]},addCommands(){return{setLineHeight:n=>({chain:e})=>e().setMark("textStyle",{lineHeight:n}).run(),unsetLineHeight:()=>({chain:n})=>n().setMark("textStyle",{lineHeight:null}).removeEmptyTextStyle().run()}}}),k0=V.create({name:"textStyleKit",addExtensions(){const n=[];return this.options.backgroundColor!==!1&&n.push(by.configure(this.options.backgroundColor)),this.options.color!==!1&&n.push(au.configure(this.options.color)),this.options.fontFamily!==!1&&n.push(cu.configure(this.options.fontFamily)),this.options.fontSize!==!1&&n.push(ky.configure(this.options.fontSize)),this.options.lineHeight!==!1&&n.push(xy.configure(this.options.lineHeight)),this.options.textStyle!==!1&&n.push(lu.configure(this.options.textStyle)),n}});var Sy=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,vy=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,Cy=qe.create({name:"highlight",addOptions(){return{multicolor:!1,HTMLAttributes:{}}},addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:n=>n.getAttribute("data-color")||n.style.backgroundColor,renderHTML:n=>n.color?{"data-color":n.color,style:`background-color: ${n.color}; color: inherit`}:{}}}:{}},parseHTML(){return[{tag:"mark"}]},renderHTML({HTMLAttributes:n}){return["mark",J(this.options.HTMLAttributes,n),0]},addCommands(){return{setHighlight:n=>({commands:e})=>e.setMark(this.name,n),toggleHighlight:n=>({commands:e})=>e.toggleMark(this.name,n),unsetHighlight:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[Rt({find:Sy,type:this.type})]},addPasteRules(){return[ht({find:vy,type:this.type})]}});const My=V.create({name:"fontSize",addOptions(){return{types:["textStyle"]}},addGlobalAttributes(){return[{types:this.options.types,attributes:{fontSize:{default:null,parseHTML:n=>{var e;return(e=n.style.fontSize)==null?void 0:e.replace(/['"]+/g,"")},renderHTML:n=>n.fontSize?{style:`font-size: ${n.fontSize}`}:{}}}}]},addCommands(){return{setFontSize:n=>({chain:e})=>e().setMark("textStyle",{fontSize:n}).run(),unsetFontSize:()=>({chain:n})=>n().setMark("textStyle",{fontSize:null}).removeEmptyTextStyle().run()}}}),wy={key:0,class:"tiptap-toolbar"},Ty={class:"toolbar-group"},Ey={type:"button",class:"toolbar-btn toolbar-btn--dropdown",title:"字体"},Ay={type:"button",class:"toolbar-btn toolbar-btn--dropdown",title:"字号"},Oy={class:"toolbar-group"},Ny={class:"toolbar-group"},Dy={type:"button",class:"toolbar-btn",title:"文字颜色"},Iy={class:"color-picker"},Ry={class:"color-row"},Py=["onClick","title"],Ly={class:"color-row"},By={type:"button",class:"toolbar-btn",title:"背景颜色"},zy={class:"color-picker"},Fy={class:"color-row"},$y=["onClick","title"],Hy={class:"color-row"},_y={class:"toolbar-group"},Vy={type:"button",class:"toolbar-btn toolbar-btn--dropdown",title:"标题"},jy={class:"toolbar-group"},Wy={class:"toolbar-group"},Ky={class:"toolbar-group"},Uy={type:"button",class:"toolbar-btn",title:"插入图片"},qy={class:"toolbar-group"},Jy=["disabled"],Gy=["disabled"],Yy=En({__name:"TiptapEditor",props:{modelValue:{default:""},placeholder:{default:"请输入内容..."},height:{default:"300px"},disabled:{type:Boolean,default:!1},hideToolbar:{type:Boolean,default:!1},autoFocus:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(n,{emit:e}){const t=n,r=e,i=Al(),{createMessage:s}=vu(),o={"X-Access-Token":xu()},l=["#000000","#333333","#666666","#999999","#cccccc","#ffffff","#ff0000","#ff6600","#ffcc00","#00ff00","#0066ff","#6600ff","#ff3366","#ff9900","#ffff00","#66ff66","#3399ff","#9966ff","#cc0000","#cc6600","#cccc00","#00cc00","#0066cc","#6600cc"],a=["#ffff00","#ffcc99","#ffcccc","#ccffcc","#ccccff","#ccffff","#ffffcc","#ffccff","#cccccc","#ff9999","#99ccff","#99ffcc","#ffff99","#ff99cc","#ccff99","#99cccc","#cc99ff","#ffcc66"];bu(()=>{i.value=new Im({content:t.modelValue,extensions:[dy,py.configure({inline:!0,allowBase64:!0}),Tg.configure({openOnClick:!1,HTMLAttributes:{target:"_blank",rel:"noopener noreferrer"}}),gy.configure({types:["heading","paragraph"]}),_g,lu,au.configure({types:["textStyle"]}),cu.configure({types:["textStyle"]}),My.configure({types:["textStyle"]}),Cy.configure({multicolor:!0})],editable:!t.disabled,autofocus:t.autoFocus,onUpdate:({editor:C})=>{const y=C.getHTML();r("update:modelValue",y),r("change",y)}})}),Ol(()=>{var C;(C=i.value)==null||C.destroy()}),no(()=>t.modelValue,C=>{i.value&&i.value.getHTML()!==C&&i.value.commands.setContent(C)}),no(()=>t.disabled,C=>{var y;(y=i.value)==null||y.setEditable(!C)});const c=()=>{if(!i.value)return"正文";for(let C=1;C<=6;C++)if(i.value.isActive("heading",{level:C}))return`标题 ${C}`;return"正文"},u=({key:C})=>{var y,T;if(C==="paragraph")(y=i.value)==null||y.chain().focus().setParagraph().run();else{const $=parseInt(C);(T=i.value)==null||T.chain().focus().toggleHeading({level:$}).run()}},d=()=>{var T,$,xe;const C=(T=i.value)==null?void 0:T.getAttributes("link").href,y=window.prompt("请输入链接地址:",C);if(y!==null){if(y===""){($=i.value)==null||$.chain().focus().extendMarkRange("link").unsetLink().run();return}(xe=i.value)==null||xe.chain().focus().extendMarkRange("link").setLink({href:y}).run()}},f=({file:C})=>{var y,T;if(C.status==="done")if(C.response&&C.response.success){const $=C.response.message;(y=i.value)==null||y.chain().focus().setImage({src:$}).run(),s.success("图片上传成功")}else s.error(((T=C.response)==null?void 0:T.message)||"图片上传失败");else C.status==="error"&&s.error("图片上传失败")},h=()=>{if(!i.value)return"默认字体";const C=i.value.getAttributes("textStyle").fontFamily;return C?{SimSun:"宋体",SimHei:"黑体","Microsoft YaHei":"微软雅黑",KaiTi:"楷体",FangSong:"仿宋","PingFang Regular":"苹方常规","PingFang Medium":"苹方中等","PingFang Bold":"苹方加粗","PingFang Heavy":"苹方特粗","DIN Regular":"DIN常规","DIN Bold":"DIN加粗","DIN BlackItalic":"DIN黑斜体","FZZongYi-M05S":"方正综艺",YouSheBiaoTiHei:"优设标题黑",Arial:"Arial","Times New Roman":"Times New Roman","Courier New":"Courier New"}[C]||C:"默认字体"},p=({key:C})=>{var y,T;C==="default"?(y=i.value)==null||y.chain().focus().unsetFontFamily().run():(T=i.value)==null||T.chain().focus().setFontFamily(C).run()},m=()=>i.value&&i.value.getAttributes("textStyle").fontSize||"16px",g=({key:C})=>{var y;(y=i.value)==null||y.chain().focus().setFontSize(C).run()},b=()=>i.value&&i.value.getAttributes("textStyle").color||"#000000",v=C=>{var y;(y=i.value)==null||y.chain().focus().setColor(C).run()},D=()=>{var C;(C=i.value)==null||C.chain().focus().unsetColor().run()},F=()=>i.value&&i.value.getAttributes("highlight").color||"transparent",I=C=>{var y;(y=i.value)==null||y.chain().focus().setHighlight({color:C}).run()},H=()=>{var C;(C=i.value)==null||C.chain().focus().unsetHighlight().run()};return(C,y)=>{var yt,ze,Ge,Ws,Ks,Us,qs,Js,Gs,Ys,Xs,Qs;const T=Lt("a-menu-item"),$=Lt("a-menu-divider"),xe=Lt("a-menu-item-group"),gt=Lt("a-menu"),Je=Lt("a-dropdown"),Xt=Lt("a-upload");return Pn(),Rn("div",{class:Ee(["tiptap-editor",{"tiptap-editor--disabled":C.disabled}])},[C.hideToolbar?ku("",!0):(Pn(),Rn("div",wy,[N("div",Ty,[M(Je,{trigger:["click"]},{overlay:O(()=>[M(gt,{onClick:p},{default:O(()=>[M(T,{key:"default"},{default:O(()=>y[14]||(y[14]=[z("默认字体")])),_:1,__:[14]}),M($),M(xe,{title:"系统字体"},{default:O(()=>[M(T,{key:"SimSun"},{default:O(()=>y[15]||(y[15]=[z("宋体")])),_:1,__:[15]}),M(T,{key:"SimHei"},{default:O(()=>y[16]||(y[16]=[z("黑体")])),_:1,__:[16]}),M(T,{key:"Microsoft YaHei"},{default:O(()=>y[17]||(y[17]=[z("微软雅黑")])),_:1,__:[17]}),M(T,{key:"KaiTi"},{default:O(()=>y[18]||(y[18]=[z("楷体")])),_:1,__:[18]}),M(T,{key:"FangSong"},{default:O(()=>y[19]||(y[19]=[z("仿宋")])),_:1,__:[19]})]),_:1}),M($),M(xe,{title:"苹方字体"},{default:O(()=>[M(T,{key:"PingFang Regular"},{default:O(()=>y[20]||(y[20]=[z("苹方常规")])),_:1,__:[20]}),M(T,{key:"PingFang Medium"},{default:O(()=>y[21]||(y[21]=[z("苹方中等")])),_:1,__:[21]}),M(T,{key:"PingFang Bold"},{default:O(()=>y[22]||(y[22]=[z("苹方加粗")])),_:1,__:[22]}),M(T,{key:"PingFang Heavy"},{default:O(()=>y[23]||(y[23]=[z("苹方特粗")])),_:1,__:[23]})]),_:1}),M($),M(xe,{title:"DIN字体"},{default:O(()=>[M(T,{key:"DIN Regular"},{default:O(()=>y[24]||(y[24]=[z("DIN常规")])),_:1,__:[24]}),M(T,{key:"DIN Bold"},{default:O(()=>y[25]||(y[25]=[z("DIN加粗")])),_:1,__:[25]}),M(T,{key:"DIN BlackItalic"},{default:O(()=>y[26]||(y[26]=[z("DIN黑斜体")])),_:1,__:[26]})]),_:1}),M($),M(xe,{title:"特殊字体"},{default:O(()=>[M(T,{key:"FZZongYi-M05S"},{default:O(()=>y[27]||(y[27]=[z("方正综艺")])),_:1,__:[27]}),M(T,{key:"YouSheBiaoTiHei"},{default:O(()=>y[28]||(y[28]=[z("优设标题黑")])),_:1,__:[28]})]),_:1}),M($),M(xe,{title:"英文字体"},{default:O(()=>[M(T,{key:"Arial"},{default:O(()=>y[29]||(y[29]=[z("Arial")])),_:1,__:[29]}),M(T,{key:"Times New Roman"},{default:O(()=>y[30]||(y[30]=[z("Times New Roman")])),_:1,__:[30]}),M(T,{key:"Courier New"},{default:O(()=>y[31]||(y[31]=[z("Courier New")])),_:1,__:[31]})]),_:1})]),_:1})]),default:O(()=>[N("button",Ey,[N("span",null,ri(h()),1),M(Y(ii))])]),_:1}),M(Je,{trigger:["click"]},{overlay:O(()=>[M(gt,{onClick:g},{default:O(()=>[M(T,{key:"12px"},{default:O(()=>y[32]||(y[32]=[z("12px")])),_:1,__:[32]}),M(T,{key:"14px"},{default:O(()=>y[33]||(y[33]=[z("14px")])),_:1,__:[33]}),M(T,{key:"16px"},{default:O(()=>y[34]||(y[34]=[z("16px")])),_:1,__:[34]}),M(T,{key:"18px"},{default:O(()=>y[35]||(y[35]=[z("18px")])),_:1,__:[35]}),M(T,{key:"20px"},{default:O(()=>y[36]||(y[36]=[z("20px")])),_:1,__:[36]}),M(T,{key:"24px"},{default:O(()=>y[37]||(y[37]=[z("24px")])),_:1,__:[37]}),M(T,{key:"28px"},{default:O(()=>y[38]||(y[38]=[z("28px")])),_:1,__:[38]}),M(T,{key:"32px"},{default:O(()=>y[39]||(y[39]=[z("32px")])),_:1,__:[39]}),M(T,{key:"36px"},{default:O(()=>y[40]||(y[40]=[z("36px")])),_:1,__:[40]})]),_:1})]),default:O(()=>[N("button",Ay,[N("span",null,ri(m()),1),M(Y(ii))])]),_:1})]),y[48]||(y[48]=N("div",{class:"toolbar-divider"},null,-1)),N("div",Oy,[N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(yt=i.value)==null?void 0:yt.isActive("bold")}]),onClick:y[0]||(y[0]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().toggleBold().run()}),title:"粗体"},[M(Y(Mu))],2),N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(ze=i.value)==null?void 0:ze.isActive("italic")}]),onClick:y[1]||(y[1]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().toggleItalic().run()}),title:"斜体"},[M(Y(wu))],2),N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(Ge=i.value)==null?void 0:Ge.isActive("underline")}]),onClick:y[2]||(y[2]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().toggleUnderline().run()}),title:"下划线"},[M(Y(Tu))],2),N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(Ws=i.value)==null?void 0:Ws.isActive("strike")}]),onClick:y[3]||(y[3]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().toggleStrike().run()}),title:"删除线"},[M(Y(Eu))],2)]),y[49]||(y[49]=N("div",{class:"toolbar-divider"},null,-1)),N("div",Ny,[M(Je,{trigger:["click"]},{overlay:O(()=>[N("div",Iy,[N("div",Ry,[(Pn(),Rn(ro,null,io(l,K=>N("div",{key:K,class:"color-item",style:Qt({backgroundColor:K}),onClick:B=>v(K),title:K},null,12,Py)),64))]),N("div",Ly,[N("button",{class:"color-clear-btn",onClick:y[4]||(y[4]=K=>D())},"清除颜色")])])]),default:O(()=>[N("button",Dy,[M(Y(Au)),N("div",{class:"color-indicator",style:Qt({backgroundColor:b()})},null,4)])]),_:1}),M(Je,{trigger:["click"]},{overlay:O(()=>[N("div",zy,[N("div",Fy,[(Pn(),Rn(ro,null,io(a,K=>N("div",{key:K,class:"color-item",style:Qt({backgroundColor:K}),onClick:B=>I(K),title:K},null,12,$y)),64))]),N("div",Hy,[N("button",{class:"color-clear-btn",onClick:y[5]||(y[5]=K=>H())},"清除背景")])])]),default:O(()=>[N("button",By,[M(Y(Ou)),N("div",{class:"color-indicator",style:Qt({backgroundColor:F()})},null,4)])]),_:1})]),y[50]||(y[50]=N("div",{class:"toolbar-divider"},null,-1)),N("div",_y,[M(Je,{trigger:["click"]},{overlay:O(()=>[M(gt,{onClick:u},{default:O(()=>[M(T,{key:"paragraph"},{default:O(()=>y[41]||(y[41]=[z("正文")])),_:1,__:[41]}),M(T,{key:"1"},{default:O(()=>y[42]||(y[42]=[z("标题 1")])),_:1,__:[42]}),M(T,{key:"2"},{default:O(()=>y[43]||(y[43]=[z("标题 2")])),_:1,__:[43]}),M(T,{key:"3"},{default:O(()=>y[44]||(y[44]=[z("标题 3")])),_:1,__:[44]}),M(T,{key:"4"},{default:O(()=>y[45]||(y[45]=[z("标题 4")])),_:1,__:[45]}),M(T,{key:"5"},{default:O(()=>y[46]||(y[46]=[z("标题 5")])),_:1,__:[46]}),M(T,{key:"6"},{default:O(()=>y[47]||(y[47]=[z("标题 6")])),_:1,__:[47]})]),_:1})]),default:O(()=>[N("button",Vy,[N("span",null,ri(c()),1),M(Y(ii))])]),_:1})]),y[51]||(y[51]=N("div",{class:"toolbar-divider"},null,-1)),N("div",jy,[N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(Ks=i.value)==null?void 0:Ks.isActive({textAlign:"left"})}]),onClick:y[6]||(y[6]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().setTextAlign("left").run()}),title:"左对齐"},[M(Y(Nu))],2),N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(Us=i.value)==null?void 0:Us.isActive({textAlign:"center"})}]),onClick:y[7]||(y[7]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().setTextAlign("center").run()}),title:"居中对齐"},[M(Y(Du))],2),N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(qs=i.value)==null?void 0:qs.isActive({textAlign:"right"})}]),onClick:y[8]||(y[8]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().setTextAlign("right").run()}),title:"右对齐"},[M(Y(Iu))],2),N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(Js=i.value)==null?void 0:Js.isActive({textAlign:"justify"})}]),onClick:y[9]||(y[9]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().setTextAlign("justify").run()}),title:"两端对齐"},[M(Y(Ru))],2)]),y[52]||(y[52]=N("div",{class:"toolbar-divider"},null,-1)),N("div",Wy,[N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(Gs=i.value)==null?void 0:Gs.isActive("bulletList")}]),onClick:y[10]||(y[10]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().toggleBulletList().run()}),title:"无序列表"},[M(Y(Pu))],2),N("button",{type:"button",class:Ee(["toolbar-btn",{"is-active":(Ys=i.value)==null?void 0:Ys.isActive("orderedList")}]),onClick:y[11]||(y[11]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().toggleOrderedList().run()}),title:"有序列表"},[M(Y(Lu))],2)]),y[53]||(y[53]=N("div",{class:"toolbar-divider"},null,-1)),N("div",Ky,[N("button",{type:"button",class:"toolbar-btn",onClick:d,title:"插入链接"},[M(Y(Bu))]),M(Xt,{"show-upload-list":!1,accept:"image/*",action:Y(Su),headers:o,data:{biz:"temp"},onChange:f,class:"toolbar-upload"},{default:O(()=>[N("button",Uy,[M(Y(zu))])]),_:1},8,["action"])]),y[54]||(y[54]=N("div",{class:"toolbar-divider"},null,-1)),N("div",qy,[N("button",{type:"button",class:"toolbar-btn",onClick:y[12]||(y[12]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().undo().run()}),disabled:!((Xs=i.value)!=null&&Xs.can().undo()),title:"撤销"},[M(Y(Fu))],8,Jy),N("button",{type:"button",class:"toolbar-btn",onClick:y[13]||(y[13]=K=>{var B;return(B=i.value)==null?void 0:B.chain().focus().redo().run()}),disabled:!((Qs=i.value)!=null&&Qs.can().redo()),title:"重做"},[M(Y($u))],8,Gy)])])),N("div",{class:"tiptap-content",style:Qt({height:C.height})},[M(Y(Rm),{editor:i.value},null,8,["editor"])],4)],2)}}}),x0=Cu(Yy,[["__scopeId","data-v-e530fbab"]]);export{x0 as T};
