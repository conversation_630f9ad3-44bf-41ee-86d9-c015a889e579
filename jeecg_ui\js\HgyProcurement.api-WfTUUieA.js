import{u as i,j as r}from"./index-CCWaWN5g.js";const s=[{title:"服务单ID",align:"center",dataIndex:"entrustOrderId"},{title:"公告名称",align:"center",dataIndex:"noticeName"},{title:"省份",align:"center",dataIndex:"province"},{title:"城市",align:"center",dataIndex:"city"},{title:"区县",align:"center",dataIndex:"district"},{title:"详细地址",align:"center",dataIndex:"address"},{title:"特殊说明",align:"center",dataIndex:"specialNotes"},{title:"创建时间",align:"center",dataIndex:"createTime"},{title:"更新时间",align:"center",dataIndex:"updateTime"},{title:"创建人",align:"center",dataIndex:"createBy"},{title:"更新人",align:"center",dataIndex:"updateBy"}],o=[],a=[{label:"公告名称",field:"noticeName",component:"Input"},{label:"省份",field:"province",component:"Input"},{label:"城市",field:"city",component:"Input"},{label:"区县",field:"district",component:"Input"},{label:"",field:"id",component:"Input",show:!1}],d={entrustOrderId:{title:"服务单ID",order:0,view:"text",type:"string"},noticeName:{title:"公告名称",order:1,view:"text",type:"string"},province:{title:"省份",order:2,view:"text",type:"string"},city:{title:"城市",order:3,view:"text",type:"string"},district:{title:"区县",order:4,view:"text",type:"string"},address:{title:"详细地址",order:5,view:"text",type:"string"},specialNotes:{title:"特殊说明",order:6,view:"text",type:"string"},createTime:{title:"创建时间",order:7,view:"datetime",type:"string"},updateTime:{title:"更新时间",order:8,view:"datetime",type:"string"},createBy:{title:"创建人",order:10,view:"text",type:"string"},updateBy:{title:"更新人",order:11,view:"text",type:"string"}};function g(e){return a}const{createConfirm:c}=i();const u="/hgy/entrustService/hgyProcurement/exportXls",y="/hgy/entrustService/hgyProcurement/importExcel",m=e=>r.get({url:"/hgy/entrustService/hgyProcurement/list",params:e}),h=(e,t)=>r.delete({url:"/hgy/entrustService/hgyProcurement/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),p=(e,t)=>{c({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>r.delete({url:"/hgy/entrustService/hgyProcurement/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},v=(e,t)=>{let n=t?"/hgy/entrustService/hgyProcurement/edit":"/hgy/entrustService/hgyProcurement/add";return r.post({url:n,params:e})};export{u as a,p as b,o as c,s as d,h as e,v as f,y as g,a as h,g as i,m as l,d as s};
