var W=Object.defineProperty;var f=Object.getOwnPropertySymbols;var D=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable;var L=(e,o,a)=>o in e?W(e,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[o]=a,k=(e,o)=>{for(var a in o||(o={}))D.call(o,a)&&L(e,a,o[a]);if(f)for(var a of f(o))T.call(o,a)&&L(e,a,o[a]);return e};import{d as w,f as b,r as A,I as B,ag as l,v as P,q as $,ar as N,aB as R,aD as i,at as V,k as n,G as r}from"./vue-vendor-dy9k-Yad.js";import{bj as E,bk as C,a as j}from"./index-CCWaWN5g.js";import{P as q}from"./index-CtJ0w2CP.js";import{A as _}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const G=w({components:{Loading:E,PageWrapper:q,[_.name]:_},setup(){const e=b(null),o=b(!1),a=A({absolute:!1,loading:!1,theme:"dark",background:"rgba(111,111,111,.7)",tip:"加载中..."}),[u,g]=C({tip:"加载中..."}),[c,s]=C({target:e,props:{tip:"加载中...",absolute:!0}});function t(F){a.absolute=F,a.loading=!0,setTimeout(()=>{a.loading=!1},2e3)}function p(){t(!1)}function d(){t(!0)}function m(){u(),setTimeout(()=>{g()},2e3)}function y(){c(),setTimeout(()=>{s()},2e3)}function v(){o.value=!0,setTimeout(()=>{o.value=!1},2e3)}return k({openCompFullLoading:p,openFnFullLoading:m,openFnWrapLoading:y,openCompAbsolute:d,wrapEl:e,loadingRef:o,openDirectiveLoading:v},B(a))}}),I={ref:"wrapEl"};function S(e,o,a,u,g,c){const s=l("a-alert"),t=l("a-button"),p=l("Loading"),d=l("PageWrapper"),m=P("loading");return $((N(),R(d,{"loading-tip":"加载中...",title:"Loading组件示例"},{default:i(()=>[V("div",I,[n(s,{message:"组件方式"}),n(t,{class:"my-4 mr-4",type:"primary",onClick:e.openCompFullLoading},{default:i(()=>o[0]||(o[0]=[r(" 全屏 Loading ")])),_:1,__:[0]},8,["onClick"]),n(t,{class:"my-4",type:"primary",onClick:e.openCompAbsolute},{default:i(()=>o[1]||(o[1]=[r(" 容器内 Loading ")])),_:1,__:[1]},8,["onClick"]),n(p,{loading:e.loading,absolute:e.absolute,theme:e.theme,background:e.background,tip:e.tip},null,8,["loading","absolute","theme","background","tip"]),n(s,{message:"函数方式"}),n(t,{class:"my-4 mr-4",type:"primary",onClick:e.openFnFullLoading},{default:i(()=>o[2]||(o[2]=[r(" 全屏 Loading ")])),_:1,__:[2]},8,["onClick"]),n(t,{class:"my-4",type:"primary",onClick:e.openFnWrapLoading},{default:i(()=>o[3]||(o[3]=[r(" 容器内 Loading ")])),_:1,__:[3]},8,["onClick"]),n(s,{message:"指令方式"}),n(t,{class:"my-4 mr-4",type:"primary",onClick:e.openDirectiveLoading},{default:i(()=>o[4]||(o[4]=[r(" 打开指令Loading ")])),_:1,__:[4]},8,["onClick"])],512)]),_:1})),[[m,e.loadingRef]])}const h=j(G,[["render",S]]);export{h as default};
