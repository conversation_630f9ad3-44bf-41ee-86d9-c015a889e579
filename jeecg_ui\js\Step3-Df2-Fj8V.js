var N=Object.defineProperty;var b=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable;var V=(o,t,e)=>t in o?N(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,_=(o,t)=>{for(var e in t||(t={}))k.call(t,e)&&V(o,e,t[e]);if(b)for(var e of b(t))y.call(t,e)&&V(o,e,t[e]);return o};var w=(o,t,e)=>new Promise((i,u)=>{var r=s=>{try{c(e.next(s))}catch(a){u(a)}},p=s=>{try{c(e.throw(s))}catch(a){u(a)}},c=s=>s.done?i(s.value):Promise.resolve(s.value).then(r,p);c((e=e.apply(o,t)).next())});import{d as P,r as B,w as x,e as C,f as F,ag as v,aq as S,ar as z,k as m,aD as g,at as d}from"./vue-vendor-dy9k-Yad.js";import{a as D}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const E={class:"step3"},I={class:"form-section"},R={class:"section-content"},U={class:"form-row contact-row"},j=P({__name:"Step3",props:{modelValue:{}},emits:["update:modelValue"],setup(o,{expose:t,emit:e}){const i=o,u=e,r=B(_({},i.modelValue));x(r,a=>{u("update:modelValue",_({},a))},{deep:!0}),x(()=>i.modelValue,a=>{Object.assign(r,a)},{deep:!0});const p=C(()=>({contactName:[{required:!0,message:"请输入联系人姓名",trigger:"blur"},{min:2,max:20,message:"联系人姓名长度应在2-20个字符之间",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]})),c=F();return t({validateForm:()=>w(null,null,function*(){var a;try{return yield(a=c.value)==null?void 0:a.validate(),!0}catch(n){const l=document.querySelector(".ant-form-item-has-error");return l&&l.scrollIntoView({behavior:"smooth",block:"center"}),!1}})}),(a,n)=>{const l=v("a-input"),h=v("a-form-item"),q=v("a-form");return z(),S("div",E,[m(q,{model:r.contact,rules:p.value,ref_key:"formRef",ref:c,"scroll-to-first-error":!0},{default:g(()=>[d("div",I,[n[2]||(n[2]=d("div",{class:"section-title"},"联系人信息",-1)),d("div",R,[d("div",U,[m(h,{label:"联系人姓名",name:"contactName",required:"",class:"contact-item"},{default:g(()=>[m(l,{value:r.contact.contactName,"onUpdate:value":n[0]||(n[0]=f=>r.contact.contactName=f),placeholder:"请输入联系人姓名",size:"large"},null,8,["value"])]),_:1}),m(h,{label:"联系电话",name:"contactPhone",required:"",class:"contact-item"},{default:g(()=>[m(l,{value:r.contact.contactPhone,"onUpdate:value":n[1]||(n[1]=f=>r.contact.contactPhone=f),placeholder:"请输入联系电话",maxlength:11,size:"large"},null,8,["value"])]),_:1})])])])]),_:1},8,["model","rules"])])}}}),J=D(j,[["__scopeId","data-v-4d6a195d"]]);export{J as default};
