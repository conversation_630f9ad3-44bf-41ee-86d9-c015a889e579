import{d as f,n as h,ag as r,aq as b,ar as g,k as i,aD as p,G as T}from"./vue-vendor-dy9k-Yad.js";import{u as C}from"./index-BkGZ5fiW.js";import{c as _}from"./system-bqUZCbh5.js";import{a as w}from"./index-JbqXEynz.js";import{M as S,s as D,c as k}from"./MenuDrawer-NHN2sA7p.js";import{Q as M}from"./componentMap-Bkie1n3v.js";import F from"./BasicTable-xCEZpGLb.js";import{a as R}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const B=f({name:"MenuManagement",components:{BasicTable:F,MenuDrawer:S,TableAction:M},setup(){const[e,{openDrawer:t}]=w(),[c,{reload:l,expandAll:u}]=C({title:"菜单列表",api:_,columns:k,formConfig:{labelWidth:120,schemas:D},isTreeTable:!0,pagination:!1,striped:!1,useSearchForm:!0,showTableSetting:!0,bordered:!0,showIndexColumn:!1,canResize:!1,actionColumn:{width:80,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:void 0}});function d(){t(!0,{isUpdate:!1})}function n(o){t(!0,{record:o,isUpdate:!0})}function a(o){}function m(){l()}function s(){h(u)}return{registerTable:c,registerDrawer:e,handleCreate:d,handleEdit:n,handleDelete:a,handleSuccess:m,onFetchSuccess:s}}});function x(e,t,c,l,u,d){const n=r("a-button"),a=r("TableAction"),m=r("BasicTable"),s=r("MenuDrawer");return g(),b("div",null,[i(m,{onRegister:e.registerTable,onFetchSuccess:e.onFetchSuccess},{tableTitle:p(()=>[i(n,{type:"primary",onClick:e.handleCreate},{default:p(()=>t[0]||(t[0]=[T(" 新增菜单 ")])),_:1,__:[0]},8,["onClick"])]),action:p(({record:o})=>[i(a,{actions:[{icon:"clarity:note-edit-line",onClick:e.handleEdit.bind(null,o)},{icon:"ant-design:delete-outlined",color:"error",popConfirm:{title:"是否确认删除",confirm:e.handleDelete.bind(null,o)}}]},null,8,["actions"])]),_:1},8,["onRegister","onFetchSuccess"]),i(s,{onRegister:e.registerDrawer,onSuccess:e.handleSuccess},null,8,["onRegister","onSuccess"])])}const Ne=R(B,[["render",x]]);export{Ne as default};
