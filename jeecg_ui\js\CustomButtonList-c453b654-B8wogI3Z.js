import{d as X,f as R,r as Y,e as Z,n as $,ag as l,aq as P,ar as T,F as ee,k as n,aD as i,aB as oe,ah as A,G as f,aJ as te,aK as re}from"./vue-vendor-dy9k-Yad.js";import{J as ne}from"./useOnlineTest-e4bd8be3-hnhsV9Hd.js";import{useListPage as ie}from"./useListPage-Soxgnx9a.js";import"./index-BkGZ5fiW.js";import{B as ae}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{b as le,C as se,g as ce,R as D,m as me,h as pe}from"./BuiltInButtonList.vue_vue_type_script_setup_true_lang-07d0b7d0-BfKwe45a.js";import{cs as ue,u as de,ac as fe,ad as O}from"./index-CCWaWN5g.js";import{u as ge}from"./useForm-CgkFTrrO.js";import{B as Be}from"./BasicForm-DBcXiHk0.js";import{Q as ve}from"./componentMap-Bkie1n3v.js";import ye from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";var be=Object.defineProperty,x=Object.getOwnPropertySymbols,we=Object.prototype.hasOwnProperty,Ce=Object.prototype.propertyIsEnumerable,L=(o,e,r)=>e in o?be(o,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[e]=r,Me=(o,e)=>{for(var r in e||(e={}))we.call(e,r)&&L(o,r,e[r]);if(x)for(var r of x(e))Ce.call(e,r)&&L(o,r,e[r]);return o},y=(o,e,r)=>new Promise((B,g)=>{var v=a=>{try{c(r.next(a))}catch(m){g(m)}},s=a=>{try{c(r.throw(a))}catch(m){g(m)}},c=a=>a.done?B(a.value):Promise.resolve(a.value).then(v,s);c((r=r.apply(o,e)).next())});const he=X({name:"CustomButtonList",components:{BasicModal:ae,BasicTable:ye,TableAction:ve,BasicForm:Be,BuiltInButtonList:pe},emits:["register"],setup(){const{createMessage:o}=de(),e=R(""),r=R(),{doRequest:B,doDeleteRecord:g,tableContext:v}=ie({tableProps:{api:t=>se(e.value,t),columns:le,canResize:!1,useSearchForm:!1,beforeFetch(t){return Object.assign(t,{column:"orderNum",order:"asc"})}}}),[s,{reload:c},{rowSelection:a,selectedRowKeys:m}]=v,[b,{closeModal:w}]=fe(t=>y(this,null,function*(){e.value=t.row.id,r.value=t.row,c()})),{aiTestMode:C,genButtons:M}=ne(),[h,p]=O(),u=R(!1),d=Y({onRegister:h,title:Z(()=>u!=null&&u.value?"修改":"新增"),width:800,confirmLoading:!1,onOk:U,onCancel:p.closeModal});let k={};const[j,{resetFields:S,setFieldsValue:K,validate:V}]=ge({schemas:ce({redoModalHeight:p.redoModalHeight}),showActionButtonGroup:!1});function F(t){return y(this,null,function*(){var I;u.value=t.isUpdate,k=Me({},(I=t.record)!=null?I:{}),p.openModal(),yield $(),yield S(),K(k)})}function G(){F({isUpdate:!1})}function _(t){F({isUpdate:!0,record:t})}function N(){w()}function E(){M(e.value)}function H(){return y(this,null,function*(){B(()=>D(m.value))})}function U(){return y(this,null,function*(){try{d.confirmLoading=!0;let t=yield V();t=Object.assign({cgformHeadId:e.value},k,t),yield me(t,u.value),c(),p.closeModal()}finally{d.confirmLoading=!1}})}const[q,z]=O();function J(){var t;if(((t=r.value)==null?void 0:t.tableType)==3){o.warn("附表不支持管理内置按钮，请选择对应主表");return}z.openModal(!0,{})}function Q(t){return[{label:"编辑",onClick:()=>_(t)}]}function W(t){return[{label:"删除",popConfirm:{title:"确定删除吗？",placement:"left",confirm:()=>g(()=>D([t.id]))}}]}return{code:e,record:r,onAdd:G,onEdit:_,onBatchDelete:H,aiTestMode:C,onGenButtons:E,registerModal:b,registerTable:s,selectedRowKeys:m,rowSelection:a,onCancel:N,getTableAction:Q,getDropDownAction:W,registerForm:j,formModalProps:d,registerBIButtonModal:q,onOpenBIButton:J}}}),ke={key:0,style:{float:"left"}};function Re(o,e,r,B,g,v){const s=l("a-button"),c=l("a-icon"),a=l("a-menu-item"),m=l("a-menu"),b=l("a-dropdown"),w=l("TableAction"),C=l("BasicTable"),M=l("BasicForm"),h=l("a-spin"),p=l("BasicModal"),u=l("BuiltInButtonList");return T(),P(ee,null,[n(p,{onRegister:o.registerModal,title:"自定义按钮",width:1200,defaultFullscreen:"",onCancel:o.onCancel},{footer:i(()=>[n(s,{onClick:o.onCancel},{default:i(()=>e[0]||(e[0]=[f("关闭")])),_:1},8,["onClick"]),o.aiTestMode?(T(),P("div",ke,[n(s,{onClick:o.onGenButtons},{default:i(()=>e[1]||(e[1]=[f("生成测试数据")])),_:1},8,["onClick"])])):A("",!0)]),default:i(()=>[n(C,{onRegister:o.registerTable,rowSelection:o.rowSelection},{tableTitle:i(()=>[n(s,{onClick:o.onAdd,type:"primary",preIcon:"ant-design:plus"},{default:i(()=>e[2]||(e[2]=[f("新增")])),_:1},8,["onClick"]),n(s,{onClick:o.onOpenBIButton,preIcon:"ant-design:setting"},{default:i(()=>e[3]||(e[3]=[f("管理内置按钮")])),_:1},8,["onClick"]),o.selectedRowKeys.length>0?(T(),oe(b,{key:0},{overlay:i(()=>[n(m,null,{default:i(()=>[n(a,{key:"1",onClick:o.onBatchDelete},{default:i(()=>[n(c,{type:"delete"}),e[4]||(e[4]=f(" 删除 "))]),_:1},8,["onClick"])]),_:1})]),default:i(()=>[n(s,{style:{"margin-left":"8px"}},{default:i(()=>[e[5]||(e[5]=f(" 批量操作 ")),n(c,{type:"down"})]),_:1})]),_:1})):A("",!0)]),action:i(({record:d})=>[n(w,{actions:o.getTableAction(d),dropDownActions:o.getDropDownAction(d)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),n(p,te(re(o.formModalProps)),{default:i(()=>[n(h,{spinning:o.formModalProps.confirmLoading},{default:i(()=>[n(M,{onRegister:o.registerForm},null,8,["onRegister"])]),_:1},8,["spinning"])]),_:1},16)]),_:1},8,["onRegister","onCancel"]),n(u,{onRegister:o.registerBIButtonModal,record:o.record},null,8,["onRegister","record"])],64)}const Oo=ue(he,[["render",Re]]);export{Oo as default};
