import{d as M,f as d,ag as m,aB as v,ar as g,aD as f,k as y,at as B,ah as R,G as w}from"./vue-vendor-dy9k-Yad.js";import{cs as _,ac as x,j as C}from"./index-CCWaWN5g.js";import{B as T}from"./index-Diw57m_E.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";var p=(e,i,a)=>new Promise((o,n)=>{var c=t=>{try{r(a.next(t))}catch(l){n(l)}},s=t=>{try{r(a.throw(t))}catch(l){n(l)}},r=t=>t.done?o(t.value):Promise.resolve(t.value).then(c,s);r((a=a.apply(e,i)).next())});const V=M({name:"FileSelectModal",components:{BasicModal:T},emits:["select","register"],setup(e,{emit:i}){const a=d(!0),o=d([]),n=d(""),c=d(!1),[s,{closeModal:r}]=x(()=>p(this,null,function*(){n.value="",o.value.length===0&&h()}));function t(){i("select",n.value),r()}function l(){r()}function h(){return p(this,null,function*(){a.value=!0,o.value=yield C.get({url:"/online/cgform/head/rootFile"}).finally(()=>{a.value=!1,c.value=!0})})}function S(u){return p(this,null,function*(){if(u.dataRef.children)return;let D={parentPath:u.dataRef.key};u.dataRef.children=yield C.get({url:"/online/cgform/head/fileTree",params:D}),o.value=[...o.value]})}function k(u){n.value=u[0]}return{loading:a,treeData:o,onLoadData:S,onSelect:k,onSubmit:t,onCancel:l,registerModal:s,hanldeRefresh:()=>{n.value="",c.value=!1,h()},directoryTreeShow:c}}}),b={class:"btnArea"};function N(e,i,a,o,n,c){const s=m("a-button"),r=m("a-directory-tree"),t=m("a-spin"),l=m("BasicModal");return g(),v(l,{onRegister:e.registerModal,title:"选择目录",width:500,onOk:e.onSubmit,onCancel:e.onCancel},{default:f(()=>[y(t,{spinning:e.loading},{default:f(()=>[B("div",b,[y(s,{onClick:e.hanldeRefresh},{default:f(()=>i[0]||(i[0]=[w("刷新")])),_:1},8,["onClick"])]),e.directoryTreeShow?(g(),v(r,{key:0,treeData:e.treeData,loadData:e.onLoadData,onSelect:e.onSelect},null,8,["treeData","loadData","onSelect"])):R("",!0)]),_:1},8,["spinning"])]),_:1},8,["onRegister","onOk","onCancel"])}const U=_(V,[["render",N],["__scopeId","data-v-102e9e9f"]]);export{U as default};
