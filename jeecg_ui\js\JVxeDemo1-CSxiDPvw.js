var T=(V,u,n)=>new Promise((f,b)=>{var C=s=>{try{_(n.next(s))}catch(g){b(g)}},x=s=>{try{_(n.throw(s))}catch(g){b(g)}},_=s=>s.done?f(s.value):Promise.resolve(s.value).then(C,x);_((n=n.apply(V,u)).next())});import{d as O,f as w,ag as k,aq as U,ar as X,k as o,aD as a,G as r,at as h,u as Z,au as z,F as W}from"./vue-vendor-dy9k-Yad.js";import{P as K,q as c,i as Q}from"./antd-vue-vendor-me9YkNVC.js";import{bx as i,u as ee,bz as te,$ as le}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const ae={style:{padding:"20px"}},oe=["onClick"],de=O({__name:"JVxeDemo1",setup(V){const{createMessage:u}=ee(),n=w(),f=w(!1),b=w(!1),C=w([{title:"ID",key:"id",type:i.hidden},{title:"不可编辑",key:"noEdit",type:i.normal,width:180,defaultValue:"noEdit-new"},{title:"单行文本",key:"input",type:i.input,width:180,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"请输入${title}"},{pattern:/^[a-z|A-Z][a-z|A-Z\d_-]*$/,message:"必须以字母开头，可包含数字、下划线、横杠"},{unique:!0,message:"${title}不能重复"},{handler({cellValue:t,row:e,column:l},d,m){t==="abc"?d(!1,"${title}不能是abc"):d(!0)},message:"${title}默认提示"}]},{title:"多行文本",key:"textarea",type:i.textarea,width:200},{title:"数字",key:"number",type:i.inputNumber,width:80,defaultValue:32,statistics:["sum","average"]},{title:"下拉框",key:"select",type:i.select,width:180,options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}],allowSearch:!0,placeholder:"请选择"},{title:"下拉框_字典",key:"select_dict",type:i.select,width:180,options:[],dictCode:"sex",placeholder:"请选择"},{title:"下拉框_多选",key:"select_multiple",type:i.selectMultiple,width:205,options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}],defaultValue:["int","boolean"],placeholder:"多选"},{title:"下拉框_搜索",key:"select_search",type:i.selectSearch,width:180,options:[{title:"String",value:"string"},{title:"Integer",value:"int"},{title:"Double",value:"double"},{title:"Boolean",value:"boolean"}]},{title:"日期时间",key:"datetime",type:i.datetime,width:200,defaultValue:"2019-04-30 14:52:22",placeholder:"请选择"},{title:"时间",key:"time",type:i.time,width:200,defaultValue:"14:52:22",placeholder:"请选择"},{title:"复选框",key:"checkbox",type:i.checkbox,width:100,customValue:["Y","N"],defaultChecked:!1},{title:"操作",key:"action",type:i.slot,fixed:"right",minWidth:120,align:"center",slotName:"myAction"}]),x=w([]);function _(t,e,l=!1){l&&(f.value=!0);let d=()=>{let v=c(1e3,9999999999999);return Q(new Date(v)).format("YYYY-MM-DD HH:mm:ss")},m=(t-1)*e,y=["string","int","double","boolean"],D=Date.now(),p=[];for(let v=0;v<e;v++)p.push({id:le(),noEdit:`noEdit-${m+v+1}`,input:`text-${m+v+1}`,textarea:`textarea-${m+v+1}`,number:c(0,233),select:y[c(0,3)],select_dict:c(1,2).toString(),select_multiple:(()=>{let L=c(1,4),$=[];for(let N=0;N<L;N++)te($,y[c(0,3)]);return $.join(",")})(),select_search:y[c(0,3)],datetime:d(),checkbox:["Y","N"][c(0,1)]});x.value=p;let R=Date.now()-D;l&&R<e&&setTimeout(()=>f.value=!1,e-R)}_(0,20,!0);function s(t){u.success("请在控制台查看输出")}function g(t){return T(this,null,function*(){var l;const e=yield(l=n.value)==null?void 0:l.removeRows(t.row,!0)})}function A(t){}function B(t){}function E(){n.value.validateTable().then(t=>{t?u.error("验证未通过，请在控制台查看详细"):u.success("验证通过")})}function I(){const t=n.value.getTableData();u.success("获取值成功，请看控制台输出")}function M(){_(1,1e3,!0)}function Y(){const e=n.value.getXTable().getTableData().fullData[0];n.value.removeRows(e)}function j(){n.value.getXTable().removeCheckboxRow()}function J(){u.info("请看控制台")}function q(){n.value.clearSelection()}function P(){f.value=!f.value}function F(){b.value=!b.value}function G(t){let e;return new Promise(l=>{Array.isArray(t)?e=t.filter(d=>d.id):e=t.id,setTimeout(()=>l(!0),1500)})}function H(t){return T(this,null,function*(){const e=u.loading("删除中…",0);try{(yield G(t.deleteRows))?(yield t.confirmRemove(),u.success("删除成功！")):u.warn("删除失败！")}finally{e()}})}return(t,e)=>{const l=k("a-button"),d=k("a-space"),m=k("a-tooltip"),y=k("a-divider"),D=k("JVxeTable");return X(),U(W,null,[o(d,null,{default:a(()=>[o(l,{onClick:P},{default:a(()=>e[0]||(e[0]=[r("切换加载")])),_:1,__:[0]}),o(l,{onClick:F},{default:a(()=>e[1]||(e[1]=[r("切换禁用")])),_:1,__:[1]})]),_:1}),o(D,{ref_key:"tableRef",ref:n,stripe:"",toolbar:"",rowNumber:"",rowSelection:"",rowExpand:"",resizable:"",asyncRemove:"",clickSelectRow:"",height:480,checkboxConfig:{range:!0},disabledRows:{input:["text--16","text--18"]},loading:f.value,disabled:b.value,columns:C.value,dataSource:x.value,onRemoved:H,onValueChange:A,onBlur:B,custom:!0},{toolbarSuffix:a(()=>[o(l,{onClick:E},{default:a(()=>e[2]||(e[2]=[r("表单验证")])),_:1,__:[2]}),o(m,{placement:"top",title:"获取值，忽略表单验证",autoAdjustOverflow:!0},{default:a(()=>[o(l,{onClick:I},{default:a(()=>e[3]||(e[3]=[r("获取数据")])),_:1,__:[3]})]),_:1}),o(m,{placement:"top",title:"模拟加载1000条数据",autoAdjustOverflow:!0},{default:a(()=>[o(l,{onClick:M},{default:a(()=>e[4]||(e[4]=[r("设置值")])),_:1,__:[4]})]),_:1}),o(l,{onClick:J},{default:a(()=>e[5]||(e[5]=[r("获取选中数据")])),_:1,__:[5]}),o(l,{onClick:q},{default:a(()=>e[6]||(e[6]=[r("清空选中")])),_:1,__:[6]}),o(l,{onClick:Y},{default:a(()=>e[7]||(e[7]=[r("删除第一行数据")])),_:1,__:[7]}),o(l,{onClick:j},{default:a(()=>e[8]||(e[8]=[r("删除选中数据")])),_:1,__:[8]})]),expandContent:a(p=>[h("div",ae,[h("span",null,"Hello! My name is: "+z(p.row.input)+"!",1)])]),myAction:a(p=>[h("a",{onClick:S=>s(p)},"查看",8,oe),o(y,{type:"vertical"}),o(Z(K),{title:"确定删除吗？",onConfirm:S=>g(p)},{default:a(()=>e[9]||(e[9]=[h("a",null,"删除",-1)])),_:2,__:[9]},1032,["onConfirm"])]),_:1},8,["loading","disabled","columns","dataSource"])],64)}}});export{de as default};
