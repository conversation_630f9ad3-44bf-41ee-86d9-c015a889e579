import{d as z,e as R,w as Y,ag as m,aq as h,ar as E,at as r,k as n,F as N,aC as j,au as x,aD as v,G as F,u as f,aB as J}from"./vue-vendor-dy9k-Yad.js";import{u as H,c as B}from"./index-CCWaWN5g.js";import{aO as K}from"./antd-vue-vendor-me9YkNVC.js";import{b as W}from"./useSettings-4a774f12-DjycrGR6.js";import{_ as X}from"./VarPicker.vue_vue_type_script_setup_true_lang-5fb9829d-C8jb2Rkc.js";import"./VarListPicker.vue_vue_type_style_index_0_scoped_9a10b0de_lang-4ed993c7-l0sNRNKZ.js";import"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./VarListEditor.vue_vue_type_style_index_0_scoped_407b7ab3_lang-4ed993c7-l0sNRNKZ.js";import{u as Z}from"./VarListShow.vue_vue_type_script_setup_true_lang-9bf001aa-DcNRZKsU.js";import"./VarTextarea.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import"./VarEditable.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import{s as ee}from"./_plugin-vue_export-helper-dad06003-lGy7RumW.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";var te=Object.defineProperty,oe=Object.defineProperties,ie=Object.getOwnPropertyDescriptors,U=Object.getOwnPropertySymbols,re=Object.prototype.hasOwnProperty,ae=Object.prototype.propertyIsEnumerable,D=(l,t,i)=>t in l?te(l,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):l[t]=i,le=(l,t)=>{for(var i in t||(t={}))re.call(t,i)&&D(l,i,t[i]);if(U)for(var i of U(t))ae.call(t,i)&&D(l,i,t[i]);return l},ne=(l,t)=>oe(l,ie(t));function se(l){var t,i;const c=[],I=(i=(t=l.properties)==null?void 0:t.options)!=null?i:{};return Array.isArray(I.if)&&c.push(...I.if.map((T,A)=>({type:A===0?"IF":"ELIF",label:`CASE ${A+1}`,value:T}))),I.else&&c.push({type:"ELSE",value:I.else}),c}function L(l,t,i){const c=t==="IF"?"source_if":t==="ELSE"?"source_else":`case_${i}`;return`${l}_${c}`}function pe(l,t){return L(l,t===-1?"ELSE":t===0?"IF":"ELIF",t+1)}const Bt=Object.freeze(Object.defineProperty({__proto__:null,getAnchorId:L,getAnchorIdByChooseIndex:pe,getCaseList:se},Symbol.toStringTag,{value:"Module"})),de=[{label:"等于",value:"EQUALS"},{label:"不等于",value:"NOT_EQUALS"},{label:"包含",value:"CONTAINS"},{label:"不包含",value:"NOT_CONTAINS"},{label:"小于",value:"LT"},{label:"小于等于",value:"LTE"},{label:"大于",value:"GT"},{label:"大于等于",value:"GTE"},{label:"长度等于",value:"LEN_EQ"},{label:"长度小于",value:"LEN_LT"},{label:"长度小于等于",value:"LEN_LTE"},{label:"长度大于",value:"LEN_GT"},{label:"长度大于等于",value:"LEN_GTE"},{label:"为空",value:"EMPTY"},{label:"不为空",value:"NOT_EMPTY"}],ce={class:"switch-setting"},ue={class:"setting-item"},me={class:"label case-label"},ve={class:"c-type"},fe={class:"c-label"},ge={class:"condition-item"},be={class:"condition-item-logic"},ye=["onClick"],Ee={class:"condition-item-component"},he={class:"field"},Ie={class:"operator"},_e={class:"value"},Oe=["onClick"],Le={class:"condition-action",style:{width:"100%","margin-bottom":"10px"}},Ae=["onClick"],Se={class:"setting-item"},Ne=z({__name:"SwitchSetting",props:{type:{type:String,required:!0},node:{type:Object,required:!0},properties:{type:Object,required:!0},setProperties:{type:Function,required:!0}},setup(l){const{createMessage:t}=H(),i=l,{lfRef:c,prevNodes:I,prevVariables:T,outputParams:A}=W(i),C=R(()=>{var a;const{properties:e}=i;return Array.isArray((a=e==null?void 0:e.options)==null?void 0:a.if)?e.options.if:[]});Y(C,()=>{const{properties:a,setProperties:e}=i,s=ne(le({},a.options),{if:C.value});e({options:s})},{deep:!0});function M(){var a;const{properties:e}=i;Array.isArray((a=e==null?void 0:e.options)==null?void 0:a.if)||(e.options.if=[]),e.options.if.push({logic:"AND",conditions:[],next:""}),$(e.options.if.length-1)}function $(a){i.properties.options.if[a].conditions.push({nodeId:"",field:"",operator:"EQUALS",value:""})}function q(a,e){const{properties:s}=i,g=s.options.if[a];if(g.conditions.length===1){t.warning("请至少保留一个条件");return}g.conditions.splice(e,1)}function G(a,e,s){s!=null&&s.nodeId?(a.nodeId=s.nodeId,a.field=s.field):(a.nodeId="",a.field="")}function Q(a){var e,s;const{properties:g,node:b}=i;if(g.options.if.length===1){t.warning("请至少保留一个 IF 分支");return}if(!c.value)return;const u=c.value.graphModel,{$caseList:_}=b,w=_[a],{type:S}=w,k=L(b.id,S,a+1),y=u.getAnchorOutgoingEdge(k);(e=y==null?void 0:y[0])!=null&&e.id&&u.deleteEdgeById(y[0].id);const d=[];for(let o=a+1;o<_.length;o++){const O=_[o],{type:p}=O;if(p==="ELSE")continue;const V=L(b.id,p,o+1),P=u.getAnchorOutgoingEdge(V);(s=P==null?void 0:P[0])!=null&&s.id&&d.push({edge:P[0],newSourceAnchorId:L(b.id,o===1?"IF":"",o)})}g.options.if.splice(a,1),d.length&&setTimeout(()=>{for(const o of d){const O={id:o.edge.id,type:o.edge.type,sourceNodeId:o.edge.sourceNodeId,targetNodeId:o.edge.targetNodeId,sourceAnchorId:o.newSourceAnchorId,targetAnchorId:o.edge.targetAnchorId};u.deleteEdgeById(o.edge.id),u.addEdge(O)}u.$J.repaintGraph()},10)}return(a,e)=>{const s=m("a-radio-button"),g=m("a-radio-group"),b=m("a-tooltip"),u=m("a-select-option"),_=m("a-select"),w=m("a-input"),S=m("a-divider"),k=m("a-button");return E(),h("div",ce,[(E(!0),h(N,null,j(C.value,(y,d)=>(E(),h(N,null,[r("div",ue,[r("div",me,[r("div",ve,x(l.node.$caseList[d].type),1),r("div",fe,x(l.node.$caseList[d].label),1)]),r("div",ge,[r("div",be,[n(g,{value:y.logic,"onUpdate:value":o=>y.logic=o,"button-style":"solid",size:"small"},{default:v(()=>[n(s,{value:"AND"},{default:v(()=>e[0]||(e[0]=[F("AND")])),_:1}),n(s,{value:"OR"},{default:v(()=>e[1]||(e[1]=[F("OR")])),_:1})]),_:2},1032,["value","onUpdate:value"]),n(b,{title:"移除分支",placement:"left"},{default:v(()=>[r("div",{class:"remove-case",onClick:o=>Q(d)},[n(f(B),{icon:"ant-design:delete"})],8,ye)]),_:2},1024)]),(E(!0),h(N,null,j(y.conditions,(o,O)=>(E(),h("div",Ee,[r("div",he,[n(f(X),{item:o,vars:f(T),onChange:p=>G(o,d,p)},null,8,["item","vars","onChange"])]),r("div",Ie,[n(_,{value:o.operator,"onUpdate:value":p=>o.operator=p,style:{width:"100%"}},{default:v(()=>[(E(!0),h(N,null,j(f(de),p=>(E(),J(u,{value:p.value},{default:v(()=>[F(x(p.label),1)]),_:2},1032,["value"]))),256))]),_:2},1032,["value","onUpdate:value"])]),r("div",_e,[n(w,{value:o.value,"onUpdate:value":p=>o.value=p,placeholder:"请输入值"},null,8,["value","onUpdate:value"])]),n(b,{title:"移除条件",placement:"left"},{default:v(()=>[r("div",{class:"action",onClick:()=>q(d,O)},[n(f(B),{icon:"ant-design:delete"})],8,Oe)]),_:2},1024)]))),256))]),r("div",Le,[r("a",{type:"text",onClick:()=>$(d)},[n(f(K)),e[2]||(e[2]=r("span",{style:{"margin-left":"6px"}},"添加条件",-1))],8,Ae)])]),n(S,{style:{margin:"4px 0 8px 0"}})],64))),256)),r("div",null,[n(k,{block:"",preIcon:"ant-design:plus",onClick:M},{default:v(()=>e[3]||(e[3]=[r("span",null,"添加分支",-1)])),_:1})]),n(S,{style:{margin:"8px 0 8px 0"}}),e[5]||(e[5]=r("div",{class:"setting-item"},[r("div",{class:"label case-label"},[r("div",{class:"c-type"},"ELSE")]),r("div",{style:{color:"#aaaaaa"}},[r("span",null,"当以上条件都不满足时，执行此分支")])],-1)),r("div",Se,[e[4]||(e[4]=r("div",{class:"label"},"输出变量",-1)),n(f(Z),{vars:f(A)},null,8,["vars"])])])}}}),Te=ee(Ne,[["__scopeId","data-v-40f254d6"]]),Ut=Object.freeze(Object.defineProperty({__proto__:null,default:Te},Symbol.toStringTag,{value:"Module"}));export{Bt as S,Te as a,se as b,Ut as c,L as g};
