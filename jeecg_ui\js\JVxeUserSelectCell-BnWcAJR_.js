var v=Object.defineProperty,x=Object.defineProperties;var J=Object.getOwnPropertyDescriptors;var c=Object.getOwnPropertySymbols;var S=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable;var m=(e,t,s)=>t in e?v(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,u=(e,t)=>{for(var s in t||(t={}))S.call(t,s)&&m(e,s,t[s]);if(c)for(var s of c(t))V.call(t,s)&&m(e,s,t[s]);return e},d=(e,t)=>x(e,J(t));import{d as _,e as r,ag as P,aq as k,ar as E,as as U,k as b,aE as T}from"./vue-vendor-dy9k-Yad.js";import{l as $}from"./JSelectUser-COkExGbu.js";import{cj as w,ck as B,cl as j,i as y,R as A,Q as D,a as N}from"./index-CCWaWN5g.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";const q=_({name:"JVxeUserSelectCell",components:{JSelectUser:$},props:w(),setup(e){const{innerValue:t,cellProps:s,handleChangeCommon:n,useCellDesign:l,originColumn:i}=j(e),{prefixCls:o}=l("user-select"),p=r(()=>{let a=t.value;return a==null?a:y(a)?[]:A(a)?a:D(a)?a.split(","):[a]}),f=r(()=>s.value.multi!=!1),C=r(()=>{var a;return d(u({},s.value),{value:p.value,showButton:!1,showSearch:!1,maxTagCount:(a=i.value.maxTagCount)!=null?a:1,maxTagPlaceholder:({length:g})=>"+"+g,isDetailsMode:s.value.disabledTable})});function h(a){n(a.join(","))}return{prefixCls:o,selectedValue:p,multiple:f,cellProps:s,getProps:C,handleChange:h}},enhanced:{switches:{visible:!0},translate:{enabled:!1},aopEvents:{editActived({$event:e}){B({$event:e,props:this.props,className:".ant-select .ant-select-selection-search-input",isClick:!0})}}}});function z(e,t,s,n,l,i){const o=P("JSelectUser");return E(),k("div",{class:U([e.prefixCls])},[b(o,T(e.getProps,{onChange:e.handleChange}),null,16,["onChange"])],2)}const re=N(q,[["render",z]]);export{re as default};
