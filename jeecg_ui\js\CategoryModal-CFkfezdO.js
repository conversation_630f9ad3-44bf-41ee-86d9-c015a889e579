var S=Object.defineProperty;var B=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var b=(e,s,o)=>s in e?S(e,s,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[s]=o,x=(e,s)=>{for(var o in s||(s={}))M.call(s,o)&&b(e,o,s[o]);if(B)for(var o of B(s))I.call(s,o)&&b(e,o,s[o]);return e};var v=(e,s,o)=>new Promise((d,l)=>{var u=n=>{try{y(o.next(n))}catch(p){l(p)}},g=n=>{try{y(o.throw(n))}catch(p){l(p)}},y=n=>n.done?d(n.value):Promise.resolve(n.value).then(u,g);y((o=o.apply(e,s)).next())});import{d as U,f as m,e as O,u as r,aB as R,ar as D,aD as E,k as F,aE as j}from"./vue-vendor-dy9k-Yad.js";import{B as H}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{j as i,ac as K}from"./index-CCWaWN5g.js";import{M as X}from"./antd-vue-vendor-me9YkNVC.js";import{u as J}from"./useForm-CgkFTrrO.js";import{B as N}from"./BasicForm-DBcXiHk0.js";const se=[{title:"分类名称",dataIndex:"name",width:350,align:"left"},{title:"分类编码",dataIndex:"code"}],oe=[{label:"名称",field:"name",component:"JInput",colProps:{span:6}},{label:"编码",field:"code",component:"JInput",colProps:{span:6}}],V=[{label:"",field:"id",component:"Input",show:!1},{label:"父级节点",field:"pid",component:"TreeSelect",componentProps:{fieldNames:{value:"key"},dropdownStyle:{maxHeight:"50vh"},getPopupContainer:()=>document.body},show:({values:e})=>e.pid!=="0",dynamicDisabled:({values:e})=>!!e.id},{label:"分类名称",field:"name",required:!0,component:"Input"}];const ae="/sys/category/exportXls",re="/sys/category/importExcel",le=e=>i.get({url:"/sys/category/rootList",params:e}),ne=(e,s)=>i.delete({url:"/sys/category/delete",params:e},{joinParamsToUrl:!0}).then(()=>{s()}),ce=(e,s)=>{X.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>i.delete({url:"/sys/category/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{s()})})},q=(e,s)=>{let o=s?"/sys/category/edit":"/sys/category/add";return i.post({url:o,params:e})},z=e=>i.get({url:"/sys/category/loadTreeRoot",params:e}),ie=e=>i.get({url:"/sys/category/childList",params:e}),de=e=>i.get({url:"/sys/category/getChildListBatch",params:e},{isTransformResponse:!1}),G=U({__name:"CategoryModal",emits:["register","success"],setup(e,{emit:s}){const o=s,d=m(!0),l=m([]),u=m([]),g=m(!1),[y,{resetFields:n,setFieldsValue:p,validate:w,updateSchema:L}]=J({schemas:V,showActionButtonGroup:!1,labelCol:{xs:{span:24},sm:{span:4}},wrapperCol:{xs:{span:24},sm:{span:18}}}),[_,{setModalProps:f,closeModal:P}]=K(t=>v(null,null,function*(){yield n(),l.value=[],f({confirmLoading:!1,minHeight:80}),d.value=!!(t!=null&&t.isUpdate),g.value=!(t!=null&&t.isUpdate)&&t.record&&t.record.id,t!=null&&t.record&&(yield p(x({},t.record))),u.value=yield z({async:!1,pcode:""}),L({field:"pid",componentProps:{treeData:u}})})),T=O(()=>r(d)?"编辑字典":"新增字典");function h(t,a){if(t&&a&&a.length>0)for(let c=0;c<a.length;c++)a[c].key==t&&r(l).indexOf(t)<0?(C(a[c]),l.value.push(a[c].key),h(a[c].parentId,r(u))):h(t,a[c].children)}function k(){return v(this,null,function*(){try{let t=yield w();f({confirmLoading:!0}),yield q(t,d.value),P(),yield h(t.pid,r(u)),o("success",{isUpdate:r(d),isSubAdd:r(g),values:x({},t),expandedArr:r(l).reverse()})}finally{f({confirmLoading:!1})}})}function C(t){if(t.children&&t.children.length>0)for(const a of t.children)r(l).indexOf(a.key)<0&&a.children&&a.children.length>0&&(C(a),l.value.push(a.key))}return(t,a)=>(D(),R(r(H),j(t.$attrs,{onRegister:r(_),destroyOnClose:"",width:"550px",title:T.value,onOk:k}),{default:E(()=>[F(r(N),{onRegister:r(y)},null,8,["onRegister"])]),_:1},16,["onRegister","title"]))}}),ue=Object.freeze(Object.defineProperty({__proto__:null,default:G},Symbol.toStringTag,{value:"Module"}));export{ue as C,G as _,ie as a,ce as b,re as c,ae as d,se as e,ne as f,de as g,le as l,oe as s};
