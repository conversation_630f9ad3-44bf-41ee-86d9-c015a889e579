var V=(u,f,r)=>new Promise((d,m)=>{var l=e=>{try{n(r.next(e))}catch(i){m(i)}},a=e=>{try{n(r.throw(e))}catch(i){m(i)}},n=e=>e.done?d(e.value):Promise.resolve(e.value).then(l,a);n((r=r.apply(u,f)).next())});import{d as I,f as x,e as q,ag as _,aq as y,ar as N,k as c,aD as v,at as g}from"./vue-vendor-dy9k-Yad.js";import{a as k}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const w={class:"step-panel"},P={class:"form-section"},z={class:"form-row"},B=I({__name:"Step2",props:{modelValue:{}},emits:["update:modelValue"],setup(u,{expose:f,emit:r}){const d=u,m=r,l=x(),a=q({get:()=>d.modelValue,set:t=>m("update:modelValue",t)}),n={contactInfo:{contactName:[{required:!0,message:"请输入联系人姓名",trigger:"blur"},{min:2,max:20,message:"联系人姓名长度应在2-20个字符之间",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]}};return f({validateForm:()=>V(null,null,function*(){var t;try{return yield(t=l.value)==null?void 0:t.validate(),!0}catch(o){const s=document.querySelector(".ant-form-item-has-error");return s&&s.scrollIntoView({behavior:"smooth",block:"center"}),!1}}),clearValidate:()=>{var t;(t=l.value)==null||t.clearValidate()}}),(t,o)=>{const s=_("a-input"),h=_("a-form-item"),b=_("a-form");return N(),y("div",w,[c(b,{ref_key:"formRef",ref:l,layout:"horizontal",model:a.value,rules:n,"label-col":{span:4,style:{textAlign:"left"}},"wrapper-col":{span:20},"scroll-to-first-error":!0},{default:v(()=>[g("div",P,[o[2]||(o[2]=g("h3",{class:"section-title"},"联系人信息",-1)),g("div",z,[c(h,{label:"联系人姓名",name:["contactInfo","contactName"],required:"",class:"form-item-half"},{default:v(()=>[c(s,{value:a.value.contactInfo.contactName,"onUpdate:value":o[0]||(o[0]=p=>a.value.contactInfo.contactName=p),placeholder:"请输入联系人姓名",size:"large"},null,8,["value"])]),_:1}),c(h,{label:"联系电话",name:["contactInfo","contactPhone"],required:"",class:"form-item-half"},{default:v(()=>[c(s,{value:a.value.contactInfo.contactPhone,"onUpdate:value":o[1]||(o[1]=p=>a.value.contactInfo.contactPhone=p),placeholder:"请输入联系电话",maxlength:"11",size:"large"},null,8,["value"])]),_:1})])])]),_:1},8,["model"])])}}}),E=k(B,[["__scopeId","data-v-f99a2335"]]);export{E as default};
