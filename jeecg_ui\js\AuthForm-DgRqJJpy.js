var v=(c,d,i)=>new Promise((u,s)=>{var f=o=>{try{m(i.next(o))}catch(p){s(p)}},I=o=>{try{m(i.throw(o))}catch(p){s(p)}},m=o=>o.done?u(o.value):Promise.resolve(o.value).then(f,I);m((i=i.apply(c,d)).next())});import{d as J,r as _,f as l,e as K,ag as M,aB as O,ar as q,aD as D,k,u as F,n as z}from"./vue-vendor-dy9k-Yad.js";import{u as N,a as V}from"./index-CCWaWN5g.js";import{J as G,e as H,f as Q,p as U}from"./JFormContainer-BUU5aWZC.js";import{a5 as W}from"./antd-vue-vendor-me9YkNVC.js";import"./index-BkGZ5fiW.js";import{useListPage as X}from"./useListPage-Soxgnx9a.js";import{c as Y}from"./OpenApi.data-CeXef7sc.js";import Z from"./BasicTable-xCEZpGLb.js";import"./vxe-table-vendor-B22HppNm.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const $=J({__name:"AuthForm",props:{formDisabled:{type:Boolean,default:!1},formData:{type:Object,default:()=>({})},formBpm:{type:Boolean,default:!0}},emits:["register","ok"],setup(c,{expose:d,emit:i}){const u=_({}),{tableContext:s}=X({tableProps:{title:"授权",api:H,columns:Y,canResize:!1,useSearchForm:!1,beforeFetch:e=>v(null,null,function*(){return Object.assign(e,u)})}}),[f,{reload:I,collapseAll:m,updateTableDataRecord:o,findTableDataRecord:p,getDataSource:tt},{rowSelection:A,selectedRowKeys:g}]=s,h=c,R=l(),B=W.useForm,w=i,n=_({apiAuthId:"",apiIdList:[]}),{createMessage:x}=N(),et=l({xs:{span:24},sm:{span:5}}),ot=l({xs:{span:24},sm:{span:16}}),b=l(!1),C=_({}),{resetFields:L,validate:P,validateInfos:rt}=B(n,C,{immediate:!1}),S=K(()=>h.formBpm===!0?h.formData.disabled!==!1:h.formDisabled);function T(){y({})}function y(e){z(()=>{L(),n.apiAuthId=e.id,Q({apiAuthId:e.id}).then(r=>{if(r&&r.length>0){let t=r.result.records||r.result,a=[];t.forEach(E=>{a.push(E.apiId)}),g.value=a,n.apiIdList=a}})})}function j(){return v(this,null,function*(){if(g.value.length===0)return w("ok");try{yield P()}catch({errorFields:t}){if(t){const a=t[0];a&&R.value.scrollToField(a.name,{behavior:"smooth",block:"center"})}return Promise.reject(t)}b.value=!0;let e=n,r="";g.value.forEach(t=>{r+=t+","}),e.apiId=r,delete e.apiIdList,yield U(e).then(t=>{t.success?(x.success(t.message),w("ok")):x.warning(t.message)}).finally(()=>{b.value=!1})})}return d({add:T,edit:y,submitForm:j}),(e,r)=>{const t=M("a-spin");return q(),O(t,{spinning:b.value},{default:D(()=>[k(G,{disabled:S.value},{detail:D(()=>[k(F(Z),{onRegister:F(f),rowSelection:F(A)},null,8,["onRegister","rowSelection"])]),_:1},8,["disabled"])]),_:1},8,["spinning"])}}}),ce=V($,[["__scopeId","data-v-ce14ace6"]]);export{ce as default};
