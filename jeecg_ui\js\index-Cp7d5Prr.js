var K=Object.defineProperty,O=Object.defineProperties;var U=Object.getOwnPropertyDescriptors;var h=Object.getOwnPropertySymbols;var q=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var B=(e,o,t)=>o in e?K(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,M=(e,o)=>{for(var t in o||(o={}))q.call(o,t)&&B(e,t,o[t]);if(h)for(var t of h(o))z.call(o,t)&&B(e,t,o[t]);return e},b=(e,o)=>O(e,U(o));var _=(e,o,t)=>new Promise((c,n)=>{var f=r=>{try{i(t.next(r))}catch(u){n(u)}},y=r=>{try{i(t.throw(r))}catch(u){n(u)}},i=r=>r.done?c(r.value):Promise.resolve(r.value).then(f,y);i((t=t.apply(e,o)).next())});import{u as m,d as k,e as F,ag as g,aq as H,ar as v,k as a,aD as l,aB as Q,ah as $,at as x,as as G}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{a as J}from"./index-JbqXEynz.js";import{useListPage as W}from"./useListPage-Soxgnx9a.js";import{_ as Y,s as Z,c as A}from"./ManageDrawer-B-q_nHDa.js";import{u as ee,j as R}from"./index-CCWaWN5g.js";import{Q as te}from"./componentMap-Bkie1n3v.js";import oe from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const{createConfirm:se}=ee();var w=(e=>(e.list="/sys/message/sysMessage/list",e.delete="/sys/message/sysMessage/delete",e.deleteBatch="/sys/message/sysMessage/deleteBatch",e.exportXls="sys/message/sysMessage/exportXls",e.importXls="sys/message/sysMessage/importExcel",e.save="/sys/message/sysMessage/add",e.edit="/sys/message/sysMessage/edit",e))(w||{});const re=e=>R.get({url:"/sys/message/sysMessage/list",params:e}),ne=(e,o=!1)=>new Promise((t,c)=>{const n=()=>{t(R.delete({url:"/sys/message/sysMessage/deleteBatch",params:e},{joinParamsToUrl:!0}))};o?se({iconType:"warning",title:"删除",content:"确定要删除吗？",onOk:()=>n(),onCancel:()=>c()}):n()});const ae=k({name:"message-manage"}),gt=k(b(M({},ae),{setup(e){const{prefixCls:o,tableContext:t}=W({designScope:"message-manage",tableProps:{title:"消息中心模板列表数据",api:re,columns:A,formConfig:{schemas:Z}},exportConfig:{url:w.exportXls,name:"消息中心模板列表"},importConfig:{url:w.importXls,success:()=>n()}}),[c,{reload:n,setLoading:f},{rowSelection:y,selectedRowKeys:i}]=t,r=F(()=>i.value.length>0),[u,{openDrawer:S}]=J();function T(s){S(!0,{record:s})}function X(s){s&&C([s.id],!1)}function P(){return _(this,null,function*(){try{yield C(i),i.value=[]}finally{}})}function C(s,p=!0){return _(this,null,function*(){const d=m(s);if(d.length>0)try{f(!0),yield ne({ids:d.join(",")},p),yield n()}finally{f(!1)}})}function j(s){return[{label:"详情",onClick:T.bind(null,s)}]}function L(s){return[{label:"删除",color:"error",popConfirm:{title:"确认要删除吗？",confirm:X.bind(null,s)}}]}return(s,p)=>{const d=g("Icon"),N=g("a-menu-item"),V=g("a-menu"),E=g("a-button"),I=g("a-dropdown");return v(),H("div",{class:G(m(o))},[a(m(oe),{onRegister:m(c),rowSelection:m(y)},{tableTitle:l(()=>[r.value?(v(),Q(I,{key:0},{overlay:l(()=>[a(V,null,{default:l(()=>[a(N,{key:"1",onClick:P},{default:l(()=>[a(d,{icon:"ant-design:delete-outlined"}),p[0]||(p[0]=x("span",null,"删除",-1))]),_:1,__:[0]})]),_:1})]),default:l(()=>[a(E,null,{default:l(()=>[p[1]||(p[1]=x("span",null,"批量操作",-1)),a(d,{icon:"mdi:chevron-down"})]),_:1,__:[1]})]),_:1})):$("",!0)]),action:l(({record:D})=>[a(m(te),{actions:j(D),dropDownActions:L(D)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),a(Y,{onRegister:m(u)},null,8,["onRegister"])],2)}}}));export{gt as default};
