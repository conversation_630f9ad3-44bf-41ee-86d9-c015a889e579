var f=(c,i,e)=>new Promise((p,t)=>{var m=r=>{try{a(e.next(r))}catch(o){t(o)}},n=r=>{try{a(e.throw(r))}catch(o){t(o)}},a=r=>r.done?p(r.value):Promise.resolve(r.value).then(m,n);a((e=e.apply(c,i)).next())});import{d as l,K as u,f as d,aB as D,ar as _,aD as w,k as g,u as s,aE as h}from"./vue-vendor-dy9k-Yad.js";import{u as B,B as k}from"./index-JbqXEynz.js";import{f as x}from"./role.data-BepCB_2a.js";import{D as C}from"./index-Dce_QJ6p.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./role.api-BvRyEQIC.js";const j=l({__name:"RoleDesc",emits:["register"],setup(c,{emit:i}){const e=i,p=u(),t=d({}),[m,{setDrawerProps:n,closeDrawer:a}]=B(r=>f(null,null,function*(){n({confirmLoading:!1}),t.value=r.record}));return(r,o)=>(_(),D(s(k),h(r.$attrs,{onRegister:s(m),title:"角色详情",width:"500px",destroyOnClose:""}),{default:w(()=>[g(s(C),{column:1,data:t.value,schema:s(x)},null,8,["data","schema"])]),_:1},16,["onRegister"]))}});export{j as default};
