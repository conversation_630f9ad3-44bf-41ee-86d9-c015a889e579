import{render as l}from"./renderUtils-D7XVOFwj.js";import{j as r}from"./index-CCWaWN5g.js";import{M as d}from"./antd-vue-vendor-me9YkNVC.js";const c=[{title:"订单号",dataIndex:"orderCode",width:260},{title:"订单类型",dataIndex:"ctype",width:160,customRender:({text:e})=>e=="1"?"国内订单":e=="2"?"国际订单":""},{title:"订单日期",dataIndex:"orderDate",width:300},{title:"订单金额",width:200,dataIndex:"orderMoney"},{title:"订单备注",width:200,dataIndex:"content"}],i=[{label:"订单号",field:"orderCode",component:"Input",colProps:{span:6}},{label:"订单类型",field:"ctype",component:"Select",componentProps:{options:[{label:"国内订单",value:"1",key:"1"},{label:"国际订单",value:"2",key:"2"}]},colProps:{span:6}}],m=[{label:"",field:"id",component:"Input",show:!1},{label:"订单号",field:"orderCode",component:"Input",required:!0},{label:"订单类型",field:"ctype",component:"Select",componentProps:{options:[{label:"国内订单",value:"1",key:"1"},{label:"国际订单",value:"2",key:"2"}]}},{label:"订单日期",field:"orderDate",component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD hh:mm:ss"}},{label:"订单金额",field:"orderMoney",component:"InputNumber"},{label:"订单备注",field:"content",component:"Input"}],u=[{title:"客户名",dataIndex:"name",width:260},{title:"性别",dataIndex:"sex",width:100,customRender:({text:e})=>l.renderDict(e,"sex")},{title:"身份证号",dataIndex:"idcard",width:300},{title:"电话",width:200,dataIndex:"telphone"}],p=[{label:"",field:"id",component:"Input",show:!1},{label:"客户姓名",field:"name",component:"Input",required:!0},{label:"性别",field:"sex",component:"JDictSelectTag",componentProps:{dictCode:"sex",placeholder:"请选择性别"}},{label:"身份证号码",field:"idcard",component:"Input"},{label:"身份证扫描件",field:"idcardPic",component:"JImageUpload",componentProps:{fileMax:2}},{label:"联系方式",field:"telphone",component:"Input",rules:[{required:!1,pattern:/^1[3456789]\d{9}$/,message:"手机号码格式有误"}]},{label:"orderId",field:"orderId",component:"Input",show:!1}],h=[{title:"航班号",dataIndex:"ticketCode"},{title:"航班时间",dataIndex:"tickectDate"},{title:"创建人",dataIndex:"createBy"},{title:"创建时间",dataIndex:"createTime"}],k=[{label:"",field:"id",component:"Input",show:!1},{label:"航班号",field:"ticketCode",component:"Input",required:!0},{label:"航班时间",field:"tickectDate",component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD"}},{label:"orderId",field:"orderId",component:"Input",show:!1}];const f=e=>r.get({url:"/test/order/orderList",params:e}),I=(e,t)=>r.delete({url:"/test/order/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),T=(e,t)=>{d.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>r.delete({url:"/test/order/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},b=(e,t)=>{let o=t?"/test/order/edit":"/test/order/add";return r.post({url:o,params:e})},C=e=>r.get({url:"/test/order/listOrderCustomerByMainId",params:e}),x=(e,t)=>r.delete({url:"/test/order/deleteCustomer",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),P=(e,t)=>{d.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>r.delete({url:"/test/order/deleteBatchCustomer",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},B=(e,t)=>{let o=t?"/test/order/editCustomer":"/test/order/addCustomer";return r.post({url:o,params:e})},w=e=>r.get({url:"/test/order/listOrderTicketByMainId",params:e}),y=(e,t)=>r.delete({url:"/test/order/deleteTicket",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),v=(e,t)=>{d.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>r.delete({url:"/test/order/deleteBatchTicket",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},D=(e,t)=>{let o=t?"/test/order/editTicket":"/test/order/addTicket";return r.post({url:o,params:e})};export{C as a,P as b,u as c,x as d,p as e,w as f,y as g,v as h,k as i,D as j,m as k,b as l,T as m,i as n,c as o,f as p,I as q,B as s,h as t};
