import{d as n,aq as m,ar as c,at as r,k as d,u as s,H as l}from"./vue-vendor-dy9k-Yad.js";import{b as u}from"./useSettings-4a774f12-DjycrGR6.js";import"./antd-vue-vendor-me9YkNVC.js";import"./VarListPicker.vue_vue_type_style_index_0_scoped_9a10b0de_lang-4ed993c7-l0sNRNKZ.js";import v from"./VarListEditor-010e347e-DRyQV5fi.js";import"./index-CCWaWN5g.js";import"./VarTextarea.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";import"./VarEditable.vue_vue_type_style_index_0_lang-4ed993c7-l0sNRNKZ.js";const y={class:"start-setting"},f={class:"switch-setting"},_=n({__name:"StartSetting",props:{type:{type:String,required:!0},node:{type:Object,required:!0},properties:{type:Object,required:!0},setProperties:{type:Function,required:!0}},setup(a){const i=a,{inputParams:t}=u(i),o={content:{allowEditName:!0},history:{tip:"用户的对话历史记录"}};return(q,e)=>(c(),m("div",y,[r("div",f,[e[1]||(e[1]=r("div",{class:"label"},"输入字段",-1)),d(s(v),{vars:s(t),"onUpdate:vars":e[0]||(e[0]=p=>l(t)?t.value=p:null),type:"start",fixedVars:o},null,8,["vars"])])]))}});export{_ as w};
