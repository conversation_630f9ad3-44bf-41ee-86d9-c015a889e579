import{d as P,f as g,r as A,ag as u,aB as T,ar as M,aD as f,at as H,k as v,aA as q,ah as z,G as L}from"./vue-vendor-dy9k-Yad.js";import{cs as U,ac as X,F as Y,j as Q}from"./index-CCWaWN5g.js";import{B as Z}from"./index-Diw57m_E.js";import{f as O,ck as $}from"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import ee from"./JCodeEditor-B-WXz11X.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */var V=(i,r,p)=>new Promise((C,h)=>{var w=a=>{try{d(p.next(a))}catch(s){h(s)}},x=a=>{try{d(p.throw(a))}catch(s){h(s)}},d=a=>a.done?C(a.value):Promise.resolve(a.value).then(w,x);d((p=p.apply(i,r)).next())});const te=P({name:"CodeFileViewModal",components:{BasicModal:Z,InfoCircleTwoTone:$,JCodeEditor:ee},emits:["download","register","close"],setup(i,{emit:r}){const p=g([]),C=g(""),h=g([]),w=g(!1),x=window.innerHeight-142,d=g("java"),a=g("");let s=A({});const[B,{closeModal:D}]=X(t=>V(this,null,function*(){s=A({}),a.value="",p.value=t.codeList,C.value=t.pathKey,N(),w.value=!0})),{prefixCls:y}=Y("online-codeFileViewModal");function N(){let t=W(),n=t[0];b(n,t);let o=[];const e=function(m){if(m.children){let l=m.children;l.length==1?e(l[0]):l.length>1&&o.push(m)}};e(n),h.value=o,setTimeout(()=>{R(n)},300)}function R(t){return V(this,null,function*(){const n=function(e){if(e.isLeaf===!0)return e;if(e.children)return n(e.children[0])};let o=n(t);if(o&&o.isLeaf===!0){let e=o.path;s[e]||(yield G(e)),d.value=E(e),a.value=s[e]}})}function b(t,n){for(let o of n)t.key==o.pid&&(t.children||(t.children=[]),t.children.push(o),b(o,n))}function S(t,n){let o=0,e="";for(;o<=n;)e+=t[o],o++;return e}function W(){let t=[],n=[],o=p.value;for(let e of o){let m=e.replace(new RegExp("\\\\","g"),"/").replace("生成成功：","").trim();if(m){let l=m.split("/");for(let c=0;c<l.length;c++){let _=l[c],F=S(l,c);if(_){let k={title:_,key:F};if(_!=0){let I=S(l,c-1);I&&(k.pid=I)}c==l.length-1&&(k.isLeaf=!0,k.path=m),(n.indexOf(F)<0||c==l.length-1)&&(t.push(k),n.push(F))}}}}return t}function j(){D(),r("close")}function J(){r("download")}function E(t){return t.endsWith("xml")?"application/xml":t.endsWith("sql")?"text/x-sql":t.endsWith("vue")?"text/x-vue":t.endsWith("ts")?"text/typescript":"text/x-java"}function K(t,n){return V(this,null,function*(){let o=n.node.dataRef;if(o.isLeaf){let e=o.path;s[e]||(yield G(e)),d.value=E(e),a.value=s[e]}})}function G(t){return new Promise(n=>{let o={path:encodeURI(t),pathKey:C.value};Q.get({url:"/online/cgform/api/codeView",params:o},{isTransformResponse:!1}).then(e=>{if(!e||e.size===0){O.warning("文件下载失败");return}else if(e.message){O.warning(e.message);return}let m=new Blob([e]),l=new FileReader;l.readAsText(m,"utf8"),l.onload=function(){let c=this.result;s[t]=c,n(1)}})})}return{registerModal:B,codeList:p,onDownloadGenerateCode:J,handleClose:j,treeData:h,showCodeContent:K,activeCodeContent:a,expandStatus:w,height:x,language:d,prefixCls:y,modalHeight:1e3}}});function oe(i,r,p,C,h,w){const x=u("info-circle-two-tone"),d=u("a-directory-tree"),a=u("a-col"),s=u("JCodeEditor"),B=u("a-empty"),D=u("a-row"),y=u("a-button"),N=u("BasicModal");return M(),T(N,{height:i.modalHeight,onRegister:i.registerModal,okText:"",cancelText:"关闭",width:1200,defaultFullscreen:!0,canFullscreen:!1,onOk:i.onDownloadGenerateCode,wrapClassName:i.prefixCls},{title:f(()=>[v(x),r[1]||(r[1]=L(" 代码在线预览 "))]),footer:f(()=>[v(y,{onClick:i.handleClose},{default:f(()=>r[2]||(r[2]=[L("关闭")])),_:1},8,["onClick"]),v(y,{type:"primary",onClick:i.onDownloadGenerateCode},{default:f(()=>r[3]||(r[3]=[L("下载到本地")])),_:1},8,["onClick"])]),default:f(()=>[H("div",null,[v(D,null,{default:f(()=>[v(a,{span:6,gutter:3,style:{"border-right":"1px solid #eee"}},{default:f(()=>[H("div",{style:q({height:i.height+"px",overflowY:"auto"})},[i.treeData.length?(M(),T(d,{key:0,defaultExpandAll:!0,"tree-data":i.treeData,onSelect:i.showCodeContent},null,8,["tree-data","onSelect"])):z("",!0)],4)]),_:1}),v(a,{span:18,gutter:3},{default:f(()=>[i.activeCodeContent?(M(),T(s,{key:0,value:i.activeCodeContent,"onUpdate:value":r[0]||(r[0]=R=>i.activeCodeContent=R),theme:"idea",language:i.language,fullScreen:!1,lineNumbers:!0,height:i.height+"px",disabled:!0,"language-change":!0},null,8,["value","language","height"])):(M(),T(B,{key:1,style:{"margin-top":"50px"},description:"请选择左侧文件，显示详细代码"}))]),_:1})]),_:1})])]),_:1},8,["height","onRegister","onOk","wrapClassName"])}const it=U(te,[["render",oe]]);export{it as default};
