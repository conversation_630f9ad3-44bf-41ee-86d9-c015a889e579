var f=Object.defineProperty,g=Object.defineProperties;var k=Object.getOwnPropertyDescriptors;var m=Object.getOwnPropertySymbols;var x=Object.prototype.hasOwnProperty,w=Object.prototype.propertyIsEnumerable;var p=(r,t,o)=>t in r?f(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o,a=(r,t)=>{for(var o in t||(t={}))x.call(t,o)&&p(r,o,t[o]);if(m)for(var o of m(t))w.call(t,o)&&p(r,o,t[o]);return r},n=(r,t)=>g(r,k(t));import{d as c,aq as S,ar as h,k as s,aD as y,u as e}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as C}from"./useListPage-Soxgnx9a.js";import{Q as R}from"./componentMap-Bkie1n3v.js";import I from"./BasicTable-xCEZpGLb.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const B={class:"p-4"},E=c({name:"basic-table-demo"}),Lt=c(n(a({},E),{setup(r){const t=[{title:"姓名",dataIndex:"name",key:"name",resizable:!0},{title:"年龄",dataIndex:"age",key:"age"},{title:"住址",dataIndex:"address",key:"address"}],{tableContext:o}=C({designScope:"basic-table-demo",tableProps:{title:"可选择表格",dataSource:[{id:"1",name:"胡歌",age:32,address:"朝阳区林萃路1号"},{id:"2",name:"刘诗诗",age:32,address:"昌平区白沙路1号"}],columns:t,rowSelection:{type:"checkbox"},useSearchForm:!1}}),[d,{reload:K},{rowSelection:l,selectedRows:P,selectedRowKeys:T}]=o;function u(i){return[{label:"编辑",onClick:_.bind(null,i)}]}function _(i){}return(i,q)=>(h(),S("div",B,[s(e(I),{onRegister:e(d),rowSelection:e(l)},{action:y(({record:b})=>[s(e(R),{actions:u(b)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"])]))}}));export{Lt as default};
