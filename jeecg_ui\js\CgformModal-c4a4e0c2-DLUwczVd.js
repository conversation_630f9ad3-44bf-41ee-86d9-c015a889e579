import{d as ze,f as v,e as re,p as se,r as Ge,n as W,J as Ye,ag as m,aB as Z,ar as O,aE as Xe,aD as b,k as s,at as N,G as x,ah as ee,aq as te,F as We,aC as Ze,au as et}from"./vue-vendor-dy9k-Yad.js";import{cs as tt,j as y,u as at,ac as ot,aS as lt,cr as U,$ as nt,ad as it,c as rt}from"./index-CCWaWN5g.js";import{B as st}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{F as dt}from"./useSchemas-b074f3a1-CF0HohIK.js";import ut from"./DBAttributeTable-1a45c7b7-CnLXQ4iF.js";import ct from"./PageAttributeTable-66e7b485-dF0mTrKY.js";import bt from"./CheckDictTable-8a938e3a-CfhOeyF-.js";import mt from"./ForeignKeyTable-92decaea-C-yd8U8L.js";import pt from"./IndexTable-2ded2014-BljD8O97.js";import ft from"./QueryTable-65d3f54f-Dvsxk92z.js";import gt from"./ExtendConfigModal-7d70f362-B_U9Wz5C.js";import{y as Tt,m as vt,c as yt,r as _}from"./cgform.data-0ca62d09-CBB13rBO.js";import{J as ht}from"./useOnlineTest-e4bd8be3-hnhsV9Hd.js";import{g as Ct}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{i as kt}from"./utils-9fce7606-LGhdtth6.js";import{u as Dt}from"./useForm-CgkFTrrO.js";import{B as Rt}from"./BasicForm-DBcXiHk0.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";import"./useTableSync-075826a1-CL-4GwR8.js";import"./LinkTableConfigModal-7eeb3e58-DvcKZSWX.js";import"./LinkTableFieldConfigModal-b078fcef-ClSEfCRc.js";import"./FieldExtendJsonModal-bf04d70e-CPf_A5jR.js";import"./SetSwitchOptions-f914bc17-Cp2St2Ye.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./index-BOX6--gq.js";import"./index-B4ez5KWV.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";var xt=Object.defineProperty,It=Object.defineProperties,Ft=Object.getOwnPropertyDescriptors,de=Object.getOwnPropertySymbols,wt=Object.prototype.hasOwnProperty,St=Object.prototype.propertyIsEnumerable,ue=(t,o,c)=>o in t?xt(t,o,{enumerable:!0,configurable:!0,writable:!0,value:c}):t[o]=c,q=(t,o)=>{for(var c in o||(o={}))wt.call(o,c)&&ue(t,c,o[c]);if(de)for(var c of de(o))St.call(o,c)&&ue(t,c,o[c]);return t},Ot=(t,o)=>It(t,Ft(o)),D=(t,o,c)=>new Promise((B,h)=>{var f=g=>{try{C(c.next(g))}catch(p){h(p)}},k=g=>{try{C(c.throw(g))}catch(p){h(p)}},C=g=>g.done?B(g.value):Promise.resolve(g.value).then(f,k);C((c=c.apply(t,o)).next())});const To=t=>y.get({url:"/online/cgform/head/list",params:t}),vo=t=>ce(t,0),yo=t=>y.delete({url:"/online/cgform/head/removeRecord",params:{id:t}},{joinParamsToUrl:!0}),ho=t=>ce(t,1),Co=t=>y.delete({url:"/online/cgform/head/delete",params:{id:t}},{joinParamsToUrl:!0});function ce(t,o){return y.delete({url:"/online/cgform/head/deleteBatch",params:{ids:t.join(","),flag:o}},{joinParamsToUrl:!0})}const ko=(t,o)=>y.post({url:`/online/cgform/api/doDbSynch/${t}/${o}`,timeout:12e3,timeoutErrorMessage:"同步数据库超时，已自动刷新"}),Do=t=>y.post({url:`/online/cgform/head/copyOnline?code=${t}`}),Ro=(t,o,c)=>y.get({url:`/online/cgform/head/copyOnlineTable/${t}`,params:q({tableName:o},c)}),Q={doQueryField:(t,o)=>y.get({url:"/online/cgform/field/listByHeadId",params:q({headId:t},o)}),doQueryIndexes:(t,o)=>y.get({url:"/online/cgform/index/listByHeadId",params:q({headId:t},o)}),doSaveOrUpdate:(t,o)=>o?y.put({url:"/online/cgform/api/editAll",params:t}):y.post({url:"/online/cgform/api/addAll",params:t}),editHead:t=>y.put({url:"/online/cgform/head/edit",params:t})},Bt=ze({name:"CgformModal",components:{BasicModal:st,BasicForm:Rt,DBAttributeTable:ut,PageAttributeTable:ct,CheckDictTable:bt,ForeignKeyTable:mt,IndexTable:pt,QueryTable:ft,ExtendConfigModal:gt,Icon:rt},emits:["success","register"],props:{actionButton:{type:Boolean,default:!0,required:!1}},setup(t,{emit:o}){const{createMessage:c}=at(),B=v(),h=v(!1);let f={};const k=re(()=>h.value?"编辑":"新增"),C=v(!0),g=v(!1),p=v("dbTable"),P=v(!0),d={dbTable:v(),pageTable:v(),checkTable:v(),fkTable:v(),idxTable:v(),queryTable:v()},J=re(()=>{var e,a;return(a=(e=B.value)==null?void 0:e.fullScreenRef)!=null?a:!1});se("tables",d),se("fullScreenRef",J);const j={value:{}},I=v(""),{formSchemas:K}=dt(t,j,{onTableTypeChange:Re,onIsTreeChange:xe,ifShowOfSubTableStr:()=>oe}),[H,w]=Dt({schemas:K,showActionButtonGroup:!1,labelAlign:"right"}),{resetFields:M,setFieldsValue:A,validate:V}=w,[z,{closeModal:G}]=ot(e=>{var a;h.value=(a=e==null?void 0:e.isUpdate)!=null?a:!1,h.value?le(e==null?void 0:e.record):ve()}),L=v("");let u=Ge({});const Y=lt(()=>Ie(),150);let ae=[],oe=!1,E=!1,F=[];const{aiTestMode:be,aiTestTable:me,aiTableList:pe,initVirtualData:fe,tableJsonGetHelper:ge,refreshCacheTableName:Te}=ht();function ve(){le({})}function le(e){return D(this,null,function*(){var a;if(C.value=!1,p.value="dbTable",yield M(),f=Object.assign({},e),ke(f),I.value="",ge(f),Ce(f),A(f),L.value=f.tableName,U(1,()=>P.value=!1),h.value)(a=d.dbTable.value)==null||a.setDataSource([]),yield ye(f.id),yield he(f.id),Ct(d.pageTable).then(()=>{d.pageTable.value.changePageType(f.tableType==3)});else{let{initialData:l,tempIds:i}=Tt();yield ne(l,!0),ae=i}})}function ye(e){return D(this,null,function*(){g.value=!0;try{let a=yield Q.doQueryField(e);g.value=!1,yield ne(a)}finally{g.value=!1}})}function he(e){return D(this,null,function*(){let a=yield Q.doQueryIndexes(e);d.idxTable.value.setDataSource(a)})}function Ce(e){let a=kt(e);u=Object.assign({},vt,a,{isDesForm:e.isDesForm||"N",desFormCode:e.desFormCode||""}),j.value=u}function ke(e){E=e.isTree=="Y",oe=e.tableType===2}function ne(e,a){return D(this,null,function*(){const{dbTable:l,pageTable:i,checkTable:r,fkTable:n,queryTable:T}=d;l.value||(yield W(),yield U(1)),l.value.setDataSource(e,a),setTimeout(()=>{i.value.setDataSource(e,a),r.value.setDataSource(e,a),n.value.setDataSource(e,a),T.value.setDataSource(e,a)},10)})}function De(e){if(["pageTable","checkTable","fkTable","idxTable","queryTable"].indexOf(e)!==-1){const a=d.dbTable,l=d[e];a.value.tableRef.resetScrollTop(),l.value.syncTable(a)}}function Re(e){e===1&&A({themeTemplate:"normal"}),d.pageTable.value.changePageType(e==3)}function xe(e){e==="Y"?Me():Ae()}function X(){Y()}function Ie(){return D(this,null,function*(){let{dbTable:e,pageTable:a,checkTable:l,fkTable:i,queryTable:r}=d;yield a.value.syncTable(e),yield l.value.syncTable(e),yield i.value.syncTable(e),yield r.value.syncTable(e)})}function Fe(){X()}function we(){X()}function Se(e){let{oldIndex:a,newIndex:l}=e;Be(a,l)}function Oe(e){return D(this,null,function*(){let{insertIndex:a,row:l}=e,{pageTable:i,checkTable:r,fkTable:n,queryTable:T}=d;i.value.tableRef.insertRows(l,a),r.value.tableRef.insertRows(l,a),n.value.tableRef.insertRows(l,a),T.value.tableRef.insertRows(l,a)})}function Be(e,a){let{pageTable:l,checkTable:i,fkTable:r,queryTable:n}=d;l.value.tableRef.rowResort(e,a),i.value.tableRef.rowResort(e,a),r.value.tableRef.rowResort(e,a),n.value.tableRef.rowResort(e,a)}function Pe(e){d.pageTable.value.syncFieldShowType(e.row)}function je(e){d.pageTable.value.syncIsQuery(e.row)}function Ne(e){d.checkTable.value.syncFieldMustInput(e.row)}function _e(e){d.pageTable.value.enableQuery(e)}function Me(){if(!E){let{dbTable:e,pageTable:a,checkTable:l}=d,i=yt();i=i.filter(r=>!e.value.tableRef.getTableData().map(n=>n.dbFieldName).includes(r.dbFieldName)),F=[],i.forEach(r=>{let n=nt()+"__tempId";F.push(n),r.id=n}),e.value.tableRef.addRows(i,{setActive:!1}),a.value.tableRef.addRows(i,{setActive:!1}),l.value.tableRef.addRows(i,{setActive:!1}),W(()=>X()),E=!0}W(()=>{w.setFieldsValue({treeIdField:"has_child",treeParentIdField:"pid"})})}function Ae(){if(F&&F.length>0){let{dbTable:e}=d;e.value.tableDeleteLines(F),F=[],E=!1}}function Le(){let e={};return new Promise((a,l)=>{V().then(i=>a({values:i}),()=>l(_))}).then(a=>(Object.assign(e,a),Ee())).then(a=>{Object.assign(e,a);let l=$e(e);return Ue(l)}).catch(a=>(a===_||(a==null?void 0:a.code)===_?c.warning("校验未通过"):a!=null&&a.msg&&c.warning(a.msg),Promise.reject(null)))}function Ee(){return new Promise((e,a)=>D(this,null,function*(){let l=Object.keys(d),i={};for(let r=0;r<l.length;r++){let n=l[r],T=d[n];try{i[n]=yield T.value.validateData(n)}catch(R){R.code===_&&(p.value=R.activeKey),a(R);return}}e(i)}))}function $e(e){let a={head:{},fields:[],indexs:[],deleteFieldIds:[],deleteIndexIds:[]};return a.head=Object.assign(f,e.values),a.head.isDesForm=u.isDesForm,a.head.desFormCode=u.desFormCode,delete u.isDesForm,delete u.desFormCode,a.head.extConfigJson=JSON.stringify(u),e.dbTable.tableData.forEach((l,i)=>{let r=l.id,n=Object.assign({},l),T=e.pageTable.tableData[i];n=Object.assign(T,n);let R=e.checkTable.tableData[i];n=Object.assign(R,n);let $=e.fkTable.tableData[i];n=Object.assign($,n);let S=e.queryTable.tableData[i];n=Object.assign(S,n),r==null||r===""?delete n.id:n.id=r,[].concat(ae,F).includes(n.id)&&delete n.id,a.fields.push(n)}),a.deleteFieldIds=e.dbTable.deleteIds,a.indexs=e.idxTable.tableData,a.deleteIndexIds=e.idxTable.deleteIds,a}function Ue(e){return new Promise((a,l)=>{let i=e.fields,r=!0;if(i&&i.length>0){let n=0;for(let T=0;T<i.length;T++)if((i[T].mainField||i[T].mainTable)&&(n+=1),n>1){r=!1;break}}r?a(e):l({code:-1,msg:"外键只允许配置一个!",error:_})})}function Qe(){C.value=!0,Le().then(e=>D(this,null,function*(){var a;if(e.fields&&e.fields.length>0)for(let l of e.fields)l.dbFieldName=l.dbFieldName.toLowerCase().trim();(a=e.head)!=null&&a.tableName&&(e.head.tableName=e.head.tableName.toLowerCase().trim()),yield Q.doSaveOrUpdate(e,h.value),Te(L.value,e.head.tableName),o("success"),U(1,()=>ie())}),e=>{}).finally(()=>{C.value=!1})}const[qe,Je]=it();function Ke(e){return D(this,null,function*(){if(j.value=e,e.joinQuery==0&&w.validateFields(["themeTemplate"]),u=e,h.value==!0){let a=Ye(u);const l={id:f.id,extConfigJson:JSON.stringify(a)};yield Q.editHead(l),o("success")}})}function He(){Je.openModal(!0,{extConfigJson:u})}function ie(){P.value=!0,U(1,()=>G())}const Ve=()=>{const e=I.value.trim();if(e.length){const a=d[p.value].value.tableRef.getXTable(),l=a.getTableData().fullData,i=l.findIndex(n=>e===n.dbFieldName||e===n.dbFieldTxt);let r=-1;if(i==-1?r=l.findIndex(n=>n.dbFieldName.includes(I.value)||n.dbFieldTxt.includes(I.value)):r=i,r!=-1){const n=l[r];a.scrollToRow(n).then(()=>{const{refTableBody:T}=a.getRefMaps(),R=T.value,$=R?R.$el:null;if($){const S=$.querySelector(`[rowid="${a.getRowid(n)}"]`);S&&(S.classList.add("customHighlight"),setTimeout(()=>{S==null||S.classList.remove("customHighlight")},1e3))}})}else c.warning("没搜到相关字段名称或字段备注~")}else c.warning("请输入字段名称或字段备注~")};return Ot(q({},d),{modalRef:B,title:k,confirmLoading:C,tableLoading:g,activeKey:p,onCancel:ie,extConfigJson:u,formAction:w,hideTabs:P,onSubmit:Qe,onTabsChange:De,onTableAdded:Fe,onTableRemoved:we,onTableDragged:Se,onTableInserted:Oe,onTableSyncDbType:Pe,onTableQuery:_e,onOpenExtConfig:He,onExtConfigOk:Ke,registerForm:H,registerModal:z,registerExtendConfigModal:qe,aiTestMode:be,aiTestTable:me,aiTableList:pe,initVirtualData:fe,onTableSyncDbIsPersist:je,onTableSyncDbIsNull:Ne,isUpdate:h,positioning:I,handlePositioning:Ve})}}),Pt={style:{flex:"1","text-align":"right"}},jt={class:"footer-area"},Nt={class:"rightArea"},_t={class:"leftArea"},Mt={key:0},At={key:1,class:"positioning-area"};function Lt(t,o,c,B,h,f){const k=m("a-button"),C=m("BasicForm"),g=m("DBAttributeTable"),p=m("a-tab-pane"),P=m("PageAttributeTable"),d=m("CheckDictTable"),J=m("ForeignKeyTable"),j=m("IndexTable"),I=m("Icon"),K=m("a-tooltip"),H=m("QueryTable"),w=m("a-tabs"),M=m("a-spin"),A=m("a-select-option"),V=m("a-select"),z=m("a-input"),G=m("ExtendConfigModal"),L=m("BasicModal");return O(),Z(L,Xe({ref:"modalRef",title:t.title,width:1200,maskClosable:!1,defaultFullscreen:!0,confirmLoading:t.confirmLoading},t.$attrs,{onCancel:t.onCancel,onRegister:t.registerModal}),{footer:b(()=>[N("div",jt,[N("div",Nt,[s(k,{onClick:t.onCancel},{default:b(()=>o[6]||(o[6]=[x("关闭")])),_:1},8,["onClick"]),s(k,{type:"primary",loading:t.confirmLoading,preIcon:"ant-design:save",onClick:t.onSubmit},{default:b(()=>o[7]||(o[7]=[x("保存")])),_:1},8,["loading","onClick"])]),N("div",_t,[t.aiTestMode&&!t.isUpdate?(O(),te("div",Mt,[s(V,{value:t.aiTestTable,"onUpdate:value":o[1]||(o[1]=u=>t.aiTestTable=u),placeholder:"请选择测试的数据模型",getPopupContainer:u=>u==null?void 0:u.parentElement,style:{width:"300px",margin:"0 10px 0 0","text-align":"left"}},{default:b(()=>[(O(!0),te(We,null,Ze(t.aiTableList,(u,Y)=>(O(),Z(A,{key:Y,value:u.name},{default:b(()=>[x(et(u.title+"（"+u.name+"）"),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","getPopupContainer"]),s(k,{type:"primary",ghost:"",onClick:t.initVirtualData},{default:b(()=>o[8]||(o[8]=[x("生成数据>>")])),_:1},8,["onClick"])])):ee("",!0),t.isUpdate?(O(),te("div",At,[s(z,{value:t.positioning,"onUpdate:value":o[2]||(o[2]=u=>t.positioning=u),placeholder:"请输入字段名称或字段备注",allowClear:"",onPressEnter:t.handlePositioning},null,8,["value","onPressEnter"]),s(k,{type:"primary",ghost:"",onClick:t.handlePositioning},{default:b(()=>o[9]||(o[9]=[x("定位")])),_:1},8,["onClick"])])):ee("",!0)])])]),default:b(()=>[s(M,{wrapperClassName:"p-2",spinning:t.confirmLoading},{default:b(()=>[s(C,{onRegister:t.registerForm},{extConfigButton:b(()=>[N("div",Pt,[s(k,{preIcon:"ant-design:setting",onClick:t.onOpenExtConfig},{default:b(()=>o[3]||(o[3]=[x("扩展配置")])),_:1},8,["onClick"])])]),_:1},8,["onRegister"]),s(M,{spinning:t.tableLoading||t.hideTabs},{default:b(()=>[t.hideTabs?ee("",!0):(O(),Z(w,{key:0,activeKey:t.activeKey,"onUpdate:activeKey":o[0]||(o[0]=u=>t.activeKey=u),animated:"",onChange:t.onTabsChange},{default:b(()=>[s(p,{tab:"数据库属性",key:"dbTable",forceRender:""},{default:b(()=>[s(g,{ref:"dbTable",actionButton:t.actionButton,onAdded:t.onTableAdded,onRemoved:t.onTableRemoved,onDragged:t.onTableDragged,onInserted:t.onTableInserted,onSyncDbType:t.onTableSyncDbType,onSyncDbIsPersist:t.onTableSyncDbIsPersist,onSyncDbIsNull:t.onTableSyncDbIsNull},null,8,["actionButton","onAdded","onRemoved","onDragged","onInserted","onSyncDbType","onSyncDbIsPersist","onSyncDbIsNull"])]),_:1}),s(p,{tab:"页面属性",key:"pageTable",forceRender:""},{default:b(()=>[s(P,{ref:"pageTable"},null,512)]),_:1}),s(p,{tab:"校验字段",key:"checkTable",forceRender:""},{default:b(()=>[s(d,{ref:"checkTable"},null,512)]),_:1}),s(p,{tab:"外键",key:"fkTable",forceRender:""},{default:b(()=>[s(J,{ref:"fkTable",actionButton:t.actionButton},null,8,["actionButton"])]),_:1}),s(p,{tab:"索引",key:"idxTable",forceRender:""},{default:b(()=>[s(j,{ref:"idxTable",actionButton:t.actionButton},null,8,["actionButton"])]),_:1}),s(p,{key:"queryTable",forceRender:""},{tab:b(()=>[N("span",null,[o[5]||(o[5]=x(" 个性查询配置 ")),s(K,null,{title:b(()=>o[4]||(o[4]=[x("允许自定义，查询表单字段控件类型！")])),default:b(()=>[s(I,{icon:"bx:help-circle"})]),_:1})])]),default:b(()=>[s(H,{ref:"queryTable",onQuery:t.onTableQuery},null,8,["onQuery"])]),_:1})]),_:1},8,["activeKey","onChange"]))]),_:1},8,["spinning"])]),_:1},8,["spinning"]),s(G,{onRegister:t.registerExtendConfigModal,parentForm:t.formAction,onOk:t.onExtConfigOk},null,8,["onRegister","parentForm","onOk"])]),_:1},16,["title","confirmLoading","onCancel","onRegister"])}const Et=tt(Bt,[["render",Lt],["__scopeId","data-v-44d8bb3b"]]),xo=Object.freeze(Object.defineProperty({__proto__:null,default:Et},Symbol.toStringTag,{value:"Module"}));export{Et as C,ko as a,Ro as b,Co as c,Do as d,yo as e,vo as f,ho as g,xo as h,To as l};
