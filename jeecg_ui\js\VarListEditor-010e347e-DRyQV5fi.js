import{d as Q,e as C,r as U,f as D,ag as q,aq as f,ar as d,F as _,aB as X,ah as O,k as x,aC as Y,as as Z,at as v,au as P,A as S,u as N,aD as ee}from"./vue-vendor-dy9k-Yad.js";import{o as te,aO as re}from"./antd-vue-vendor-me9YkNVC.js";import{B as ae}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{u as ie,ad as oe}from"./index-CCWaWN5g.js";import{f as ne}from"./index-a84962f9-BJDiPJBg.js";import"./VarListEditor.vue_vue_type_style_index_0_scoped_407b7ab3_lang-4ed993c7-l0sNRNKZ.js";import{s as le}from"./_plugin-vue_export-helper-dad06003-lGy7RumW.js";import{u as se}from"./useForm-CgkFTrrO.js";import{B as me}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";var pe=Object.defineProperty,de=Object.defineProperties,ue=Object.getOwnPropertyDescriptors,R=Object.getOwnPropertySymbols,ce=Object.prototype.hasOwnProperty,fe=Object.prototype.propertyIsEnumerable,T=(a,t,r)=>t in a?pe(a,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[t]=r,F=(a,t)=>{for(var r in t||(t={}))ce.call(t,r)&&T(a,r,t[r]);if(R)for(var r of R(t))fe.call(t,r)&&T(a,r,t[r]);return a},E=(a,t)=>de(a,ue(t)),V=(a,t,r)=>new Promise((s,w)=>{var u=p=>{try{n(r.next(p))}catch(g){w(g)}},k=p=>{try{n(r.throw(p))}catch(g){w(g)}},n=p=>p.done?s(p.value):Promise.resolve(p.value).then(u,k);n((r=r.apply(a,t)).next())});const ve=["onClick"],ye={class:"icon"},be={class:"name"},ge={style:{color:"#999999"}},he={key:0},xe={key:1},we={key:0,class:"action"},ke={key:0},Be=["onClick"],Ne={key:1,class:"field-add",style:{width:"100%","margin-bottom":"10px"}},je={style:{"margin-left":"6px"}},Oe={style:{padding:"20px"}},Pe=Q({__name:"VarListEditor",props:{vars:{type:Array,required:!0},fixedVars:{type:Object,default:()=>({})},type:{type:String,default:"other"},fieldBeforeText:{type:String,default:""}},emits:["update:vars"],setup(a,{emit:t}){const{createConfirmSync:r}=ie(),s=a,w=t,u=C(()=>s.type==="start"),k=C(()=>s.type==="subflow"),n=U(u.value?{varName:"字段"}:{varName:"变量"}),[p,{openModal:g,closeModal:I}]=oe(),j=D(!1),M=C(()=>j.value?`编辑${n.varName}`:`添加${n.varName}`),y=D([]),[A,b]=se({showActionButtonGroup:!1,schemas:[{field:"idx",label:"",component:"InputNumber",show:!1},{field:"field",label:`${n.varName}名称`,component:"Input",componentProps:{addonBefore:s.fieldBeforeText},dynamicDisabled:()=>y.value.includes("field")||k.value,rules:[{required:!0,message:`请输入${n.varName}名称`},{validator:(e,o)=>{const i=ne(o,{allowDot:!!s.fieldBeforeText});return i.passed?Promise.resolve():Promise.reject(`${n.varName}名称`+i.message)}}]},{field:"name",label:"显示名称",component:"Input",dynamicDisabled:()=>y.value.includes("name"),required:!0},{field:"type",label:`${n.varName}类型`,component:u.value?"RadioGroup":"Select",dynamicDisabled:()=>y.value.includes("type"),componentProps:{options:G(),getPopupContainer:()=>document.body}},{field:"required",label:"是否必填",component:"Switch",dynamicDisabled:()=>y.value.includes("required"),ifShow:()=>u.value}]});function G(){const e=[{label:"文本",value:"string"},{label:"数字",value:"number"}];return u.value?e.push({label:"图片",value:"picture"}):e.push({label:"对象",value:"object"},{label:"文本数组",value:"string[]"},{label:"数字数组",value:"number[]"},{label:"对象数组",value:"object[]"}),e}function L(e){switch(e){case"string":return"memory:format-text";case"number":return"ant-design:number";case"picture":return"ant-design:picture";case"object":return"ic:baseline-data-object";case"string[]":return"carbon:array-strings";case"number[]":return"carbon:array-numbers";case"object[]":return"carbon:array-objects"}return"memory:format-text"}function B(e){return!!s.fixedVars[e]}function $(e){var o,i;return B(e)&&((i=(o=s.fixedVars[e])==null?void 0:o.allowEditName)!=null?i:!1)}function W(e){return V(this,null,function*(){if(!(yield r({title:"删除",content:`确定要删除这个${n.varName}吗？`})))return;const{vars:o}=s,i=[...o];i.splice(e,1),w("update:vars",i)})}function z(){return V(this,null,function*(){j.value=!1,y.value=[],g(),yield b.resetFields(),yield b.setFieldsValue({type:"string",required:!0}),yield b.clearValidate()})}function J(e,o){return V(this,null,function*(){e=F({},e);const{fieldBeforeText:i}=s;i&&e.field.startsWith(i)&&(e.field=e.field.slice(i.length));const m=B(e.field),c=m&&$(e.field);m&&!c||(j.value=!0,g(),m?y.value=["field","type","required"]:y.value=[],yield b.resetFields(),yield b.setFieldsValue(E(F({},e),{idx:o})),yield b.clearValidate())})}function H(){return V(this,null,function*(){try{const e=yield b.validate();if(!e)return;const o=E(F({},te(e,"idx","required")),{required:!!e.required}),{vars:i,fieldBeforeText:m}=s;m&&Object.entries(o).forEach(([l,h])=>{l==="field"&&h&&!h.startsWith(m)&&(o.field=m+h)});const c=[...i];c[e.idx]=e,j.value?c[e.idx]=o:c.push(o),w("update:vars",c),I()}catch(e){}})}return(e,o)=>{const i=q("a-empty"),m=q("Icon"),c=q("a-divider");return d(),f(_,null,[u.value&&a.vars.length===0?(d(),X(i,{key:0,"image-style":{height:"64px"},description:`尚未配置${n.varName}`},null,8,["description"])):O("",!0),(d(!0),f(_,null,Y(a.vars,(l,h)=>(d(),f("div",{class:Z(["field-item",{"field-item-fixed":B(l.field),"field-item-allow-edit-name":B(l.field)&&$(l.field)}]),onClick:K=>J(l,h)},[v("div",ye,[x(m,{icon:L(l.type)},null,8,["icon"])]),v("div",be,[v("span",null,P(l.field),1),x(c,{type:"vertical"}),v("span",ge,[B(l.field)?(d(),f("span",he,P(a.fixedVars[l.field].tip||l.name),1)):(d(),f("span",xe,P(l.name),1))])]),k.value?O("",!0):(d(),f("div",we,[u.value&&l.required?(d(),f("span",ke,"必填")):O("",!0),v("span",{onClick:S(K=>W(h),["stop"])},[x(m,{icon:"ant-design:delete"})],8,Be)]))],10,ve))),256)),k.value?O("",!0):(d(),f("div",Ne,[v("a",{type:"text",style:{"border-radius":"15px"},onClick:S(z,["stop"])},[x(N(re)),v("span",je,"添加"+P(n.varName),1)])])),x(N(ae),{onRegister:N(p),title:M.value,okText:"保存",forceRender:"",onOk:H},{default:ee(()=>[v("div",Oe,[x(N(me),{onRegister:N(A)},null,8,["onRegister"])])]),_:1},8,["onRegister","title"])],64)}}}),Ft=le(Pe,[["__scopeId","data-v-407b7ab3"]]);export{Ft as default};
