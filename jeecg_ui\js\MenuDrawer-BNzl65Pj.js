var T=Object.defineProperty;var C=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var _=(p,e,r)=>e in p?T(p,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):p[e]=r,b=(p,e)=>{for(var r in e||(e={}))L.call(e,r)&&_(p,r,e[r]);if(C)for(var r of C(e))M.call(e,r)&&_(p,r,e[r]);return p};var g=(p,e,r)=>new Promise((h,u)=>{var l=m=>{try{c(r.next(m))}catch(f){u(f)}},a=m=>{try{c(r.throw(m))}catch(f){u(f)}},c=m=>m.done?h(m.value):Promise.resolve(m.value).then(l,a);c((r=r.apply(p,e)).next())});import{d as N,K as V,f as P,e as $,u as n,aB as x,ar as E,aD as O,k as j,aE as q}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{f as G,l as K,C as k,g as z}from"./menu.data-Dh4mGeCS.js";import{u as H,B as J}from"./index-JbqXEynz.js";import{useDrawerAdaptiveWidth as Q}from"./useAdaptiveWidth-SDQVNQ1K.js";import{N as X}from"./index-CCWaWN5g.js";import{u as Y}from"./useForm-CgkFTrrO.js";import{B as Z}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./renderUtils-D7XVOFwj.js";const sr=N({__name:"MenuDrawer",emits:["success","register"],setup(p,{emit:e}){const r=e,{adaptiveWidth:h}=Q(),u=V(),l=P(!0),a=P(0),c=t=>t===2,[m,{setProps:f,resetFields:A,setFieldsValue:w,updateSchema:v,validate:U,clearValidate:W}]=Y({labelCol:{md:{span:4},sm:{span:6}},wrapperCol:{md:{span:20},sm:{span:18}},schemas:G,showActionButtonGroup:!1}),[y,{setDrawerProps:d,closeDrawer:I}]=H(t=>g(null,null,function*(){var i;yield A(),d({confirmLoading:!1}),l.value=!!(t!=null&&t.isUpdate),a.value=(i=t==null?void 0:t.record)==null?void 0:i.menuType;const o=yield K();if(v([{field:"parentId",componentProps:{treeData:F(o,"name")}},{field:"name",label:c(n(a))?"按钮/权限":"菜单名称"},{field:"url",required:!c(n(a)),componentProps:{onChange:s=>D(s.target.value)}}]),typeof t.record=="object"){let s=b({},t.record);w(s),D(s.url)}a.value==2&&W(),f({disabled:!u.showFooter})})),R=$(()=>n(l)?"编辑菜单":"新增菜单");function S(){return g(this,null,function*(){try{const t=yield U();k.IFrame===t.component&&(t.component=t.frameSrc),d({confirmLoading:!0}),yield z(t,n(l)),I(),r("success")}finally{d({confirmLoading:!1})}})}function D(t){let o="",i=t;t!=null&&t!=""?(t.startsWith("/")&&(t=t.substring(1)),t=t.replaceAll("/","-"),t=t.replaceAll(":","@"),o=`${t}`):o="请输入组件名称",v([{field:"componentName",componentProps:{placeholder:o}}]),i!=null&&i!=""&&(i.startsWith("http://")||i.startsWith("https://"))&&w({component:i})}function F(t,o){if(t!=null&&t.length){const{t:i}=X();t.forEach(s=>{var B;s[o]&&s[o].includes("t('")&&i&&(s[o]=new Function("t",`return ${s[o]}`)(i)),(B=s.children)!=null&&B.length&&F(s.children,o)})}return t}return(t,o)=>(E(),x(n(J),q(t.$attrs,{onRegister:n(y),showFooter:"",width:n(h),title:R.value,onOk:S}),{default:O(()=>[j(n(Z),{onRegister:n(m),class:"menuForm"},null,8,["onRegister"])]),_:1},16,["onRegister","width","title"]))}});export{sr as default};
