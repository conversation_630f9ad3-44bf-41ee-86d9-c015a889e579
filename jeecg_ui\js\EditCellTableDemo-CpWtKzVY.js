var l=(t,m,i)=>new Promise((p,n)=>{var s=e=>{try{o(i.next(e))}catch(r){n(r)}},a=e=>{try{o(i.throw(e))}catch(r){n(r)}},o=e=>e.done?p(e.value):Promise.resolve(e.value).then(s,a);o((i=i.apply(t,m)).next())});import{d as u,ag as c,aq as f,ar as b,k as C}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{o as h}from"./select-nVA4yav1.js";import{d as E}from"./table-BDFKJhHv.js";import{t as w}from"./tree-C9AW4Mg9.js";import{u as x,a as _}from"./index-CCWaWN5g.js";import{useListPage as I}from"./useListPage-Soxgnx9a.js";import k from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const v=[{title:"输入框",dataIndex:"name",edit:!0,editComponentProps:{prefix:"$"},width:200},{title:"默认输入状态",dataIndex:"name7",edit:!0,editable:!0,width:200},{title:"输入框校验",dataIndex:"name1",edit:!0,editRule:!0,width:200},{title:"输入框函数校验",dataIndex:"name2",edit:!0,editRule:t=>l(null,null,function*(){return t==="2"?"不能输入该值":""}),width:200},{title:"数字输入框",dataIndex:"id",edit:!0,editRule:!0,editComponent:"InputNumber",width:200},{title:"下拉框",dataIndex:"name3",edit:!0,editComponent:"Select",editComponentProps:{options:[{label:"Option1",value:"1"},{label:"Option2",value:"2"}]},width:200},{title:"远程下拉",dataIndex:"name4",edit:!0,editComponent:"ApiSelect",editComponentProps:{api:h,resultField:"list",labelField:"name",valueField:"id"},width:200},{title:"远程下拉树",dataIndex:"name71",edit:!0,editComponent:"ApiTreeSelect",editRule:!1,editComponentProps:{api:w,resultField:"list"},width:200},{title:"日期选择",dataIndex:"date",edit:!0,editComponent:"DatePicker",editComponentProps:{valueFormat:"YYYY-MM-DD",format:"YYYY-MM-DD"},width:200},{title:"时间选择",dataIndex:"time",edit:!0,editComponent:"TimePicker",editComponentProps:{valueFormat:"HH:mm",format:"HH:mm"},width:200},{title:"勾选框",dataIndex:"name5",edit:!0,editComponent:"Checkbox",editValueMap:t=>t?"是":"否",width:200},{title:"开关",dataIndex:"name6",edit:!0,editComponent:"Switch",editValueMap:t=>t?"开":"关",width:200}],P=u({components:{BasicTable:k},setup(){const{tableContext:t}=I({designScope:"basic-table-demo",tableProps:{title:"可编辑单元格示例",api:E,columns:v,showIndexColumn:!1,bordered:!0,showActionColumn:!1,useSearchForm:!1}}),[m]=t,{createMessage:i}=x();function p({record:o,index:e,key:r,value:d}){return!1}function n({value:o,key:e,id:r}){return i.loading({content:`正在模拟保存${e}`,key:"_save_fake_data",duration:0}),new Promise(d=>{setTimeout(()=>{o===""?(i.error({content:"保存失败：不能为空",key:"_save_fake_data",duration:2}),d(!1)):(i.success({content:`记录${r}的${e}已保存`,key:"_save_fake_data",duration:2}),d(!0))},2e3)})}function s(Lt){return l(this,arguments,function*({record:o,index:e,key:r,value:d}){return yield n({id:o.id,key:r,value:d})})}function a(){}return{registerTable:m,handleEditEnd:p,handleEditCancel:a,beforeEditSubmit:s}}}),S={class:"p-4"};function g(t,m,i,p,n,s){const a=c("BasicTable");return b(),f("div",S,[C(a,{onRegister:t.registerTable,onEditEnd:t.handleEditEnd,onEditCancel:t.handleEditCancel,beforeEditSubmit:t.beforeEditSubmit},null,8,["onRegister","onEditEnd","onEditCancel","beforeEditSubmit"])])}const yt=_(P,[["render",g]]);export{yt as default};
