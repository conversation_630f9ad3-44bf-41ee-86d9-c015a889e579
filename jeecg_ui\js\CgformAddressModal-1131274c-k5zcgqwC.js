import{d as M,e as v,f as x,r as I,ag as d,aq as E,ar as N,F as V,k as t,u as b,aD as a,at as r,aB as F,ah as j,G as _,aE as K}from"./vue-vendor-dy9k-Yad.js";import{B as U}from"./index-Diw57m_E.js";import{i as R}from"./utils-9fce7606-LGhdtth6.js";import{cs as S,u as q,aZ as z,ac as D,$ as G,bJ as Q}from"./index-CCWaWN5g.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const H={class:"content"},J=["href"],P=["href"],W=["href"],X=M({__name:"CgformAddressModal",setup(Y){const{createMessage:y}=q(),k=v(()=>z()),c=x(["def"]),p=x(!1),s=I({title:"",content:"",copyText:"",copyTitle:"",formId:""}),[B]=D(l=>{Object.assign(s,l,{formId:l.record.id});const e=R(l.record);p.value=e.enableExternalLink===1,p.value?c.value=["def","external"]:c.value=["def"]}),n=v(()=>{const l={};if(p.value){const e=`/online/cgform/share/${s.formId}`;l.add=`${e}/add`,l.edit=`${e}/u/{dataId}`,l.detail=`${e}/d/{dataId}`}return{list:s.content,extLink:l}});function T(){const l=`-- 插入菜单
INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external)
VALUES ('${G()}', NULL, '${s.copyTitle}', '${s.copyText}', '1', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 0, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', null, NULL, NULL, 0)
`;C(l)}function C(l){const e=Q(l);return e?y.success("复制成功！"):y.error("复制失败！"),e}const o=I({base:"",value:"",visible:!1}),$=v(()=>o.value?o.base.replace(/{dataId}/,o.value)+"?token="+k.value:void 0),w=v(()=>({title:"请输入dataId",minHeight:120,centered:!0,canFullscreen:!1,onOk:()=>o.visible=!1,onCancel:()=>o.visible=!1}));function L(l){o.base=l,o.value="",o.visible=!0}function O(){if(!o.value){y.warn("请输入dataId");return}o.visible=!1}return(l,e)=>{const u=d("a-input"),f=d("a-col"),m=d("a-row"),g=d("a-button"),h=d("a-collapse-panel"),A=d("a-collapse");return N(),E(V,null,[t(b(U),{onRegister:b(B),title:"配置地址",width:750,canFullscreen:!1,showOkBtn:!1,cancelText:"关闭"},{default:a(()=>[r("div",H,[t(A,{activeKey:c.value,"onUpdate:activeKey":e[2]||(e[2]=i=>c.value=i),class:"j-collapse",bordered:!1,ghost:""},{default:a(()=>[t(h,{key:"def",header:"配置地址",class:"j-collapse-panel no-header"},{default:a(()=>[t(m,{style:{"margin-bottom":"8px"}},{default:a(()=>[t(f,{span:24},{default:a(()=>[t(u,{readOnly:!0,addonBefore:"数据列表地址",value:n.value.list},{addonAfter:a(()=>[r("a",{href:n.value.list,target:"_blank"},"打开",8,J)]),_:1},8,["value"])]),_:1})]),_:1}),t(g,{class:"copy-sql",type:"primary",size:"small",onClick:T},{default:a(()=>e[5]||(e[5]=[_("复制菜单SQL")])),_:1})]),_:1}),p.value?(N(),F(h,{key:"external",header:"外部链接"},{default:a(()=>[t(m,{style:{"margin-bottom":"8px"}},{default:a(()=>[t(f,{span:24},{default:a(()=>[t(u,{readOnly:!0,addonBefore:"外部新增地址",value:n.value.extLink.add},{addonAfter:a(()=>[r("a",{href:n.value.extLink.add+"?token="+k.value,target:"_blank"},"打开",8,P)]),_:1},8,["value"])]),_:1})]),_:1}),t(m,{style:{"margin-bottom":"8px"}},{default:a(()=>[t(f,{span:24},{default:a(()=>[t(u,{readOnly:!0,addonBefore:"外部修改地址",value:n.value.extLink.edit},{addonAfter:a(()=>[r("a",{onClick:e[0]||(e[0]=i=>L(n.value.extLink.edit))},"打开")]),_:1},8,["value"])]),_:1})]),_:1}),t(m,{style:{"margin-bottom":"8px"}},{default:a(()=>[t(f,{span:24},{default:a(()=>[t(u,{readOnly:!0,addonBefore:"外部详情地址",value:n.value.extLink.detail},{addonAfter:a(()=>[r("a",{onClick:e[1]||(e[1]=i=>L(n.value.extLink.detail))},"打开")]),_:1},8,["value"])]),_:1})]),_:1}),e[6]||(e[6]=r("div",{style:{"text-align":"right",color:"red"}},[_("注意："),r("span",{style:{"font-weight":"bold"}},"{dataId}"),_(" 为数据id")],-1))]),_:1})):j("",!0)]),_:1},8,["activeKey"])])]),_:1},8,["onRegister"]),t(b(U),K({visible:o.visible,"onUpdate:visible":e[4]||(e[4]=i=>o.visible=i)},w.value),{footer:a(()=>[r("a",{href:$.value,target:"_blank"},[t(g,{type:"primary",onClick:O},{default:a(()=>e[7]||(e[7]=[_("确定")])),_:1})],8,W)]),default:a(()=>[t(u,{placeholder:"请输入dataId",value:o.value,"onUpdate:value":e[3]||(e[3]=i=>o.value=i)},null,8,["value"])]),_:1},16,["visible"])],64)}}}),_e=S(X,[["__scopeId","data-v-f786f675"]]);export{_e as default};
