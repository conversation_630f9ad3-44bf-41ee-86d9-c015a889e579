import{d as N,f as S,u as $,ag as u,aB as g,ar as A,aD as o,at as f,k as s,G as a}from"./vue-vendor-dy9k-Yad.js";import{_ as E}from"./index-BtIdS_Qz.js";import{t as G}from"./data-B6s_JPbS.js";import{u as T,a as b}from"./index-CCWaWN5g.js";import{P as w}from"./index-CtJ0w2CP.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./vxe-table-vendor-B22HppNm.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const L=N({components:{BasicTree:E,PageWrapper:w},setup(){const t=S(null),{createMessage:e}=T();function r(){const n=$(t);if(!n)throw new Error("tree is null!");return n}function m(n){r().filterByLevel(n)}function k(){r().setCheckedKeys(["0-0"])}function y(){const n=r().getCheckedKeys();e.success(JSON.stringify(n))}function l(){r().setSelectedKeys(["0-0"])}function d(){const n=r().getSelectedKeys();e.success(JSON.stringify(n))}function p(){r().setExpandedKeys(["0-0"])}function i(){const n=r().getExpandedKeys();e.success(JSON.stringify(n))}function C(n){r().checkAll(n)}function B(n){r().expandAll(n)}function v(n=null){r().insertNodeByKey({parentKey:n,node:{title:"新增节点",key:"2-2-2"},push:"push"})}function D(n){r().deleteNodeByKey(n)}function K(n){r().updateNodeByKey(n,{title:"parent2-new"})}return{treeData:G,treeRef:t,handleLevel:m,handleSetCheckData:k,handleGetCheckData:y,handleSetSelectData:l,handleGetSelectData:d,handleSetExpandData:p,handleGetExpandData:i,appendNodeByKey:v,deleteNodeByKey:D,updateNodeByKey:K,checkAll:C,expandAll:B}}}),P={class:"mb-4"},_={class:"mb-4"},J={class:"mb-4"};function O(t,e,r,m,k,y){const l=u("a-button"),d=u("BasicTree"),p=u("PageWrapper");return A(),g(p,{title:"Tree函数操作示例",contentBackground:"",contentClass:"p-4"},{default:o(()=>[f("div",P,[s(l,{onClick:e[0]||(e[0]=i=>t.expandAll(!0)),class:"mr-2"},{default:o(()=>e[10]||(e[10]=[a(" 展开全部 ")])),_:1,__:[10]}),s(l,{onClick:e[1]||(e[1]=i=>t.expandAll(!1)),class:"mr-2"},{default:o(()=>e[11]||(e[11]=[a(" 折叠全部 ")])),_:1,__:[11]}),s(l,{onClick:e[2]||(e[2]=i=>t.checkAll(!0)),class:"mr-2"},{default:o(()=>e[12]||(e[12]=[a(" 全选 ")])),_:1,__:[12]}),s(l,{onClick:e[3]||(e[3]=i=>t.checkAll(!1)),class:"mr-2"},{default:o(()=>e[13]||(e[13]=[a(" 全不选 ")])),_:1,__:[13]}),s(l,{onClick:e[4]||(e[4]=i=>t.handleLevel(2)),class:"mr-2"},{default:o(()=>e[14]||(e[14]=[a(" 显示到第2级 ")])),_:1,__:[14]}),s(l,{onClick:e[5]||(e[5]=i=>t.handleLevel(1)),class:"mr-2"},{default:o(()=>e[15]||(e[15]=[a(" 显示到第1级 ")])),_:1,__:[15]})]),f("div",_,[s(l,{onClick:t.handleSetCheckData,class:"mr-2"},{default:o(()=>e[16]||(e[16]=[a(" 设置勾选数据 ")])),_:1,__:[16]},8,["onClick"]),s(l,{onClick:t.handleGetCheckData,class:"mr-2"},{default:o(()=>e[17]||(e[17]=[a(" 获取勾选数据 ")])),_:1,__:[17]},8,["onClick"]),s(l,{onClick:t.handleSetSelectData,class:"mr-2"},{default:o(()=>e[18]||(e[18]=[a(" 设置选中数据 ")])),_:1,__:[18]},8,["onClick"]),s(l,{onClick:t.handleGetSelectData,class:"mr-2"},{default:o(()=>e[19]||(e[19]=[a(" 获取选中数据 ")])),_:1,__:[19]},8,["onClick"]),s(l,{onClick:t.handleSetExpandData,class:"mr-2"},{default:o(()=>e[20]||(e[20]=[a(" 设置展开数据 ")])),_:1,__:[20]},8,["onClick"]),s(l,{onClick:t.handleGetExpandData,class:"mr-2"},{default:o(()=>e[21]||(e[21]=[a(" 获取展开数据 ")])),_:1,__:[21]},8,["onClick"])]),f("div",J,[s(l,{onClick:e[6]||(e[6]=i=>t.appendNodeByKey(null)),class:"mr-2"},{default:o(()=>e[22]||(e[22]=[a(" 添加根节点 ")])),_:1,__:[22]}),s(l,{onClick:e[7]||(e[7]=i=>t.appendNodeByKey("2-2")),class:"mr-2"},{default:o(()=>e[23]||(e[23]=[a(" 添加在parent3内添加节点 ")])),_:1,__:[23]}),s(l,{onClick:e[8]||(e[8]=i=>t.deleteNodeByKey("2-2")),class:"mr-2"},{default:o(()=>e[24]||(e[24]=[a(" 删除parent3节点 ")])),_:1,__:[24]}),s(l,{onClick:e[9]||(e[9]=i=>t.updateNodeByKey("1-1")),class:"mr-2"},{default:o(()=>e[25]||(e[25]=[a(" 更新parent2节点 ")])),_:1,__:[25]})]),s(d,{treeData:t.treeData,title:"函数操作",ref:"treeRef",checkable:!0},null,8,["treeData"])]),_:1})}const le=b(L,[["render",O]]);export{le as default};
