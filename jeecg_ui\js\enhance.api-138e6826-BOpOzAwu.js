import{j as t,R as h}from"./index-CCWaWN5g.js";var d=Object.defineProperty,g=Object.defineProperties,y=Object.getOwnPropertyDescriptors,m=Object.getOwnPropertySymbols,v=Object.prototype.hasOwnProperty,b=Object.prototype.propertyIsEnumerable,f=(n,r,e)=>r in n?d(n,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[r]=e,j=(n,r)=>{for(var e in r||(r={}))v.call(r,e)&&f(n,e,r[e]);if(m)for(var e of m(r))b.call(r,e)&&f(n,e,r[e]);return n},O=(n,r)=>g(n,y(r)),p=(n,r,e)=>new Promise((a,o)=>{var l=s=>{try{u(e.next(s))}catch(i){o(i)}},c=s=>{try{u(e.throw(s))}catch(i){o(i)}},u=s=>s.done?a(s.value):Promise.resolve(s.value).then(l,c);u((e=e.apply(n,r)).next())});function w(n,r,e){return p(this,null,function*(){let{success:a,result:o}=yield t.get({url:"/online/cgform/head/enhanceJs/"+n,params:O(j({},e),{type:r})},{isTransformResponse:!1});return a||(o={cgJs:""}),o})}const J=(n,r,e)=>{let a=`/online/cgform/head/enhanceJs/${n}`;return e?t.put({url:a,params:r},{successMessageMode:"none"}):t.post({url:a,params:r},{successMessageMode:"none"})};function T(n,r){return p(this,null,function*(){let e=yield t.get({url:"/online/cgform/head/enhanceButton/"+n},{isTransformResponse:!1}),a=[];e.success&&h(e.result)&&(a=e.result.filter(c=>c.optType=="action"));let o=`/online/cgform/head/enhanceJava/${n}`,l=yield t.get({url:o,params:r});return{btnList:a,dataSource:l}})}function S(n){return t.delete({url:"/online/cgform/head/deleteBatchEnhanceJava",params:{ids:n.join(",")}},{joinParamsToUrl:!0})}const $=(n,r,e)=>{let a=`/online/cgform/head/enhanceJava/${n}`;return e?t.put({url:a,params:r}):t.post({url:a,params:r})};function M(n,r){return p(this,null,function*(){let e=yield t.get({url:"/online/cgform/head/enhanceButton/"+n},{isTransformResponse:!1}),a=[];e.success&&h(e.result)&&(a=e.result.filter(c=>c.optType=="action"));let o=`/online/cgform/head/enhanceSql/${n}`,l=yield t.get({url:o,params:r});return{btnList:a,dataSource:l}})}function R(n){return t.delete({url:"/online/cgform/head/deletebatchEnhanceSql",params:{ids:n.join(",")}},{joinParamsToUrl:!0})}const q=(n,r,e)=>{let a=`/online/cgform/head/enhanceSql/${n}`;return e?t.put({url:a,params:r}):t.post({url:a,params:r})};export{R as C,q as D,M,S as R,w as b,$ as j,J as q,T as w};
