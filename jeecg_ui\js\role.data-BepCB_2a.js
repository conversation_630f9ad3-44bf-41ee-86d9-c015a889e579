import{i as r}from"./role.api-BvRyEQIC.js";const s=[{title:"角色名称",dataIndex:"roleName",width:100},{title:"角色编码",dataIndex:"roleCode",width:100},{title:"创建时间",dataIndex:"createTime",width:100},{title:"角色类型",dataIndex:"roleType_dictText",width:100}],c=[{title:"用户账号",dataIndex:"username"},{title:"用户姓名",dataIndex:"realname"},{title:"状态",dataIndex:"status_dictText",width:80}],m=[{field:"roleName",label:"角色名称",component:"Input",colProps:{span:6}},{field:"roleCode",label:"角色编码",component:"Input",colProps:{span:6}}],p=[{field:"username",label:"用户账号",component:"Input",colProps:{span:12},labelWidth:74}],u=[{field:"id",label:"",component:"Input",show:!1},{field:"roleName",label:"角色名称",required:!0,component:"Input"},{field:"roleCode",label:"角色编码",required:!0,component:"Input",dynamicDisabled:({values:e})=>!!e.id,dynamicRules:({values:e,model:l})=>[{required:!0,validator:(i,o)=>o?e?new Promise((a,n)=>{r({id:l.id,roleCode:o}).then(t=>{t.success?a():n(t.message||"校验失败")}).catch(t=>{n(t.message||"验证失败")})}):Promise.resolve():Promise.reject("请输入角色编码")}]},{label:"备注",field:"description",component:"InputTextArea"},{label:"角色类型",field:"roleType",component:"JDictSelectTag",componentProps:{dictCode:"roleTypeDict"},dynamicRules:({model:e,schema:l})=>[{required:!0,message:"请输入角色类型(系统,企业,2:个人)"}]}],f=[{field:"roleName",label:"角色名称"},{field:"roleCode",label:"角色编码"},{label:"备注",field:"description"}],h=[{field:"id",label:"",component:"Input",show:!1},{label:"角色编码",field:"roleCode",component:"Input",dynamicDisabled:!0},{label:"首页路由",field:"url",component:"Input",required:!0,helpMessage:"首页路由的访问地址"},{label:"组件地址",field:"component",component:"Input",helpMessage:"首页路由的组件地址",componentProps:{placeholder:"请输入前端组件"},required:!0},{field:"route",label:"是否路由菜单",helpMessage:"非路由菜单设置成首页，需开启",component:"Switch",defaultValue:!0},{label:"优先级",field:"priority",component:"InputNumber"},{label:"是否开启",field:"status",component:"JSwitch",componentProps:{options:["1","0"]}}];export{u as a,p as b,s as c,f,h as r,m as s,c as u};
