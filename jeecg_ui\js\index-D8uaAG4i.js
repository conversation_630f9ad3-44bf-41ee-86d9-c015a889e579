var A=Object.defineProperty,K=Object.defineProperties;var L=Object.getOwnPropertyDescriptors;var w=Object.getOwnPropertySymbols;var X=Object.prototype.hasOwnProperty,q=Object.prototype.propertyIsEnumerable;var x=(e,o,t)=>o in e?A(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,I=(e,o)=>{for(var t in o||(o={}))X.call(o,t)&&x(e,t,o[t]);if(w)for(var t of w(o))q.call(o,t)&&x(e,t,o[t]);return e},S=(e,o)=>K(e,L(o));var k=(e,o,t)=>new Promise((c,_)=>{var f=p=>{try{u(t.next(p))}catch(a){_(a)}},g=p=>{try{u(t.throw(p))}catch(a){_(a)}},u=p=>p.done?c(p.value):Promise.resolve(p.value).then(f,g);u((t=t.apply(e,o)).next())});import{d as M,ag as l,aq as F,ar as v,k as i,aD as r,u as m,aB as G,ah as H,G as d}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{_ as Q,d as $,b as z,g as J,a as O,s as W,c as Y,e as Z}from"./PositionModal-D8_yIP0x.js";import{ad as tt,u as ot}from"./index-CCWaWN5g.js";import{useListPage as et}from"./useListPage-Soxgnx9a.js";import{Q as it}from"./componentMap-Bkie1n3v.js";import nt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const rt=M({name:"system-position"}),co=M(S(I({},rt),{setup(e){const{createMessage:o}=ot(),[t,{openModal:c}]=tt(),{prefixCls:_,onExportXls:f,onImportXls:g,tableContext:u}=et({designScope:"position-template",tableProps:{title:"职务列表",api:Z,columns:Y,formConfig:{schemas:W},actionColumn:{width:180},showIndexColumn:!0},exportConfig:{name:"职务列表",url:O},importConfig:{url:J}}),[p,{reload:a},{rowSelection:P,selectedRowKeys:C}]=u;function R(s){return[{label:"编辑",onClick:B.bind(null,s)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:D.bind(null,s)}}]}function h(){c(!0,{isUpdate:!1})}function B(s){c(!0,{record:s,isUpdate:!0})}function D(s){return k(this,null,function*(){yield $({id:s.id},a)})}function E(){return k(this,null,function*(){yield z({ids:C.value},()=>{C.value=[],a()})})}return(s,n)=>{const b=l("a-button"),U=l("j-upload-button"),y=l("Icon"),N=l("a-menu-item"),T=l("a-menu"),V=l("a-dropdown");return v(),F("div",null,[i(m(nt),{onRegister:m(p),rowSelection:m(P)},{tableTitle:r(()=>[i(b,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:h},{default:r(()=>n[0]||(n[0]=[d("新增")])),_:1,__:[0]}),i(b,{type:"primary",preIcon:"ant-design:export-outlined",onClick:m(f)},{default:r(()=>n[1]||(n[1]=[d(" 导出")])),_:1,__:[1]},8,["onClick"]),i(U,{type:"primary",preIcon:"ant-design:import-outlined",onClick:m(g)},{default:r(()=>n[2]||(n[2]=[d("导入")])),_:1,__:[2]},8,["onClick"]),m(C).length>0?(v(),G(V,{key:0},{overlay:r(()=>[i(T,null,{default:r(()=>[i(N,{key:"1",onClick:E},{default:r(()=>[i(y,{icon:"ant-design:delete-outlined"}),n[3]||(n[3]=d(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:r(()=>[i(b,null,{default:r(()=>[n[4]||(n[4]=d("批量操作 ")),i(y,{icon:"ant-design:down-outlined"})]),_:1,__:[4]})]),_:1})):H("",!0)]),action:r(({record:j})=>[i(m(it),{actions:R(j)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"]),i(Q,{onRegister:m(t),onSuccess:m(a)},null,8,["onRegister","onSuccess"])])}}}));export{co as default};
