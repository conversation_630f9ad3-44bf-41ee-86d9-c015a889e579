var R=Object.defineProperty,I=Object.defineProperties;var v=Object.getOwnPropertyDescriptors;var g=Object.getOwnPropertySymbols;var y=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var h=(r,e,t)=>e in r?R(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,f=(r,e)=>{for(var t in e||(e={}))y.call(e,t)&&h(r,t,e[t]);if(g)for(var t of g(e))C.call(e,t)&&h(r,t,e[t]);return r},T=(r,e)=>I(r,v(e));var _=(r,e,t)=>new Promise((c,m)=>{var p=o=>{try{s(t.next(o))}catch(i){m(i)}},l=o=>{try{s(t.throw(o))}catch(i){m(i)}},s=o=>o.done?c(o.value):Promise.resolve(o.value).then(p,l);s((t=t.apply(r,e)).next())});import{d as k,aq as Y,ar as z,k as F,aD as w,at as P,au as u,as as S,u as b}from"./vue-vendor-dy9k-Yad.js";import{u as q}from"./index-BkGZ5fiW.js";import{aX as M,a as B}from"./index-CCWaWN5g.js";import V from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const H={class:"p-4"},L={class:"font-medium"},N=k({name:"AccountIntegral"}),A=k(T(f({},N),{setup(r){const e=[{title:"积分途径",dataIndex:"source",width:200,ellipsis:!0},{title:"变化",dataIndex:"change",width:120,slots:{customRender:"change"}},{title:"累计积分",dataIndex:"totalPoints",width:120,slots:{customRender:"totalPoints"}},{title:"时间",dataIndex:"createTime",width:160,customRender:({text:o})=>M(o)}];function t(o){return _(this,null,function*(){const i=[{id:1,source:"完成资产处置委托",change:500,totalPoints:16546,createTime:"2024-12-09 10:30:00"},{id:2,source:"参与竞价活动",change:200,totalPoints:16046,createTime:"2024-12-09 09:15:00"},{id:3,source:"积分兑换商品",change:-300,totalPoints:15846,createTime:"2024-12-08 16:45:00"},{id:4,source:"每日签到",change:10,totalPoints:16146,createTime:"2024-12-08 14:20:00"},{id:5,source:"推荐新用户",change:1e3,totalPoints:16136,createTime:"2024-12-07 11:30:00"},{id:6,source:"完成实名认证",change:100,totalPoints:15136,createTime:"2024-12-06 15:20:00"},{id:7,source:"积分过期扣除",change:-50,totalPoints:15036,createTime:"2024-12-05 00:00:00"}],{current:a=1,size:n=10}=o,d=(a-1)*n,D=d+n,x=i.slice(d,D);return Promise.resolve({records:x,total:i.length,current:a,size:n})})}const c=[{label:"今天",value:"today"},{label:"近7日",value:"week"},{label:"近1月",value:"month"}];function m(o){const i=new Date;let a,n=new Date(i);switch(o){case"today":a=new Date(i.getFullYear(),i.getMonth(),i.getDate());break;case"week":a=new Date(i.getTime()-7*24*60*60*1e3);break;case"month":a=new Date(i.getFullYear(),i.getMonth()-1,i.getDate());break;default:return}s().setFieldsValue({timeRange:[a,n]}),l()}const[p,{reload:l,getForm:s}]=q({api:t,columns:e,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,inset:!0,formConfig:{labelWidth:80,size:"large",schemas:[{field:"quickTime",label:"",component:"RadioGroup",componentProps:{options:c,onChange:o=>m(o.target.value),optionType:"button",buttonStyle:"solid",size:"small"},colProps:{span:6}},{field:"timeRange",label:"时间区间",component:"RangePicker",componentProps:{showTime:!0,format:"YYYY-MM-DD HH:mm:ss"},colProps:{span:12}}]}});return(o,i)=>(z(),Y("div",H,[F(b(V),{onRegister:b(p)},{change:w(({record:a})=>[P("span",{class:S(a.change>0?"text-green-600":"text-red-600")},u(a.change>0?"+":"")+u(a.change),3)]),totalPoints:w(({text:a})=>[P("span",L,u(a.toLocaleString()),1)]),_:1},8,["onRegister"])]))}})),Qt=B(A,[["__scopeId","data-v-74cd0f66"]]);export{Qt as default};
