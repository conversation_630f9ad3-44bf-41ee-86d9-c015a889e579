import{d as c,ap as p,u as m,aq as i,ar as u}from"./vue-vendor-dy9k-Yad.js";import{c5 as _}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const x=c({__name:"index",setup(d){const{currentRoute:o,replace:t}=p(),{params:s,query:n}=m(o),{path:r}=s,e=_().redirectPageParam,a=Array.isArray(r)?r.join("/"):r;return e&&(e.redirect_type==="name"?t({name:e.name,query:e.query,params:e.params}):t({path:a.startsWith("/")?a:"/"+a,query:n})),(l,y)=>(u(),i("div"))}});export{x as default};
