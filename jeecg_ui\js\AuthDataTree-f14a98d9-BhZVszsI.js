import{d as N,f as d,e as M,w as S,ag as h,aq as g,ar as f,aB as x,F as _,at as q,k as v,aD as I,G as K}from"./vue-vendor-dy9k-Yad.js";import{cs as z,u as F}from"./index-CCWaWN5g.js";import{A as T,B as V,F as A}from"./auth.api-53df4c33-CWNFk1-w.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var y=(e,n,t)=>new Promise((u,s)=>{var i=a=>{try{r(t.next(a))}catch(c){s(c)}},o=a=>{try{r(t.throw(a))}catch(c){s(c)}},r=a=>a.done?u(a.value):Promise.resolve(a.value).then(i,o);r((t=t.apply(e,n)).next())});const J=N({name:"AuthDataTree",props:{cgformId:{type:String,required:!0}},setup(e){const{createMessage:n}=F(),t=d(""),u=d(3),s=d(!0),i=d([]),o=d([]),r=d([]),a=d(""),c=M(()=>!t.value);S(()=>e.cgformId,m,{immediate:!0});function m(){return y(this,null,function*(){if(!e.cgformId)return;let l=yield T(e.cgformId,u.value);r.value=l.map(p=>({key:p.id,title:p.ruleName}))})}function k(l,p){return y(this,null,function*(){t.value=l,a.value=p,o.value=[],yield m();let P=yield V({roleId:l,cgformId:e.cgformId,type:u.value,authMode:p});o.value=P.map(w=>w.authId)})}function C(){t.value="",m()}function E(){m(),k(t.value,a.value)}function b(){return y(this,null,function*(){yield A(t.value,e.cgformId,{authId:JSON.stringify(o.value),authMode:a.value}),n.success("保存成功")})}function D(l){i.value=l,s.value=!1}function B(){t.value="",o.value=[]}return{loadChecked:k,clear:B,expandedKeys:i,autoExpandParent:s,checkedKeys:o,treeData:r,disabled:c,onSave:b,onExpand:D,onRefresh:E,clearChecked:C}}}),R={class:"onl-auth-tree-btns"};function G(e,n,t,u,s,i){const o=h("a-empty"),r=h("a-button"),a=h("a-tree");return f(),g("div",null,[e.disabled?(f(),x(o,{key:0,description:"请先选中左侧角色/部门/用户"})):e.treeData.length===0?(f(),x(o,{key:1,description:"无权限信息"})):(f(),g(_,{key:2},[q("div",R,[v(r,{onClick:e.onRefresh,size:"small",type:"primary",preIcon:"ant-design:redo",ghost:""},{default:I(()=>n[1]||(n[1]=[K("刷新")])),_:1},8,["onClick"]),v(r,{onClick:e.onSave,size:"small",type:"primary",preIcon:"ant-design:save",ghost:""},{default:I(()=>n[2]||(n[2]=[K("保存")])),_:1},8,["onClick"])]),v(a,{checkable:"",checkedKeys:e.checkedKeys,"onUpdate:checkedKeys":n[0]||(n[0]=c=>e.checkedKeys=c),expandedKeys:e.expandedKeys,autoExpandParent:e.autoExpandParent,treeData:e.treeData,onExpand:e.onExpand},null,8,["checkedKeys","expandedKeys","autoExpandParent","treeData","onExpand"])],64))])}const L=z(J,[["render",G],["__scopeId","data-v-c6be2157"]]);export{L as default};
