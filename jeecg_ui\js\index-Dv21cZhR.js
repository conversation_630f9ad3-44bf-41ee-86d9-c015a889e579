import{d as c,ag as i,aB as m,ar as t,aD as p,at as l,as as f,k as d,aq as u,F as b,aC as _,aQ as B}from"./vue-vendor-dy9k-Yad.js";import{a4 as a}from"./antd-vue-vendor-me9YkNVC.js";import{S as C}from"./index-LCGLvkB3.js";import{c as S}from"./data-DwTukD7a.js";import g from"./BaseSetting-DB_70zj1.js";import y from"./SecureSetting-qFx_OTBZ.js";import T from"./AccountBind-BHxXZZDy.js";import k from"./MsgNotify-D15TYnts.js";import{a as x}from"./index-CCWaWN5g.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-BPAdgtaT.js";import"./header-OZa5fSDc.js";import"./upload-Dd1Qafin.js";const P=c({components:{ScrollContainer:C,Tabs:a,TabPane:a.TabPane,BaseSetting:g,SecureSetting:y,AccountBind:T,MsgNotify:k},setup(){return{prefixCls:"account-setting",settingList:S,tabBarStyle:{width:"220px",marginBottom:"200px"}}}});function $(o,h,v,w,L,N){const e=i("TabPane"),n=i("Tabs"),s=i("ScrollContainer");return t(),m(s,null,{default:p(()=>[l("div",{ref:"wrapperRef",class:f(o.prefixCls)},[d(n,{"tab-position":"left",tabBarStyle:o.tabBarStyle},{default:p(()=>[(t(!0),u(b,null,_(o.settingList,r=>(t(),m(e,{key:r.key,tab:r.name},{default:p(()=>[(t(),m(B(r.component)))]),_:2},1032,["tab"]))),128))]),_:1},8,["tabBarStyle"])],2)]),_:1})}const jt=x(P,[["render",$]]);export{jt as default};
