var le=Object.defineProperty,oe=Object.defineProperties;var ie=Object.getOwnPropertyDescriptors;var Q=Object.getOwnPropertySymbols;var se=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable;var U=(e,n,i)=>n in e?le(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i,S=(e,n)=>{for(var i in n||(n={}))se.call(n,i)&&U(e,i,n[i]);if(Q)for(var i of Q(n))re.call(n,i)&&U(e,i,n[i]);return e},I=(e,n)=>oe(e,ie(n));var z=(e,n,i)=>new Promise((g,a)=>{var h=r=>{try{f(i.next(r))}catch(u){a(u)}},c=r=>{try{f(i.throw(r))}catch(u){a(u)}},f=r=>r.done?g(r.value):Promise.resolve(r.value).then(h,c);f((i=i.apply(e,n)).next())});import{d as x,f as H,w as W,e as F,u as l,h as V,o as ue,b as ce,n as y,ag as b,v as de,aB as ee,ar as G,aD as m,q as fe,aq as ge,aA as me,aG as N,g as he,z as pe,aE as q,aH as X,k as M,aC as Y,aJ as Z,aK as _}from"./vue-vendor-dy9k-Yad.js";import{b as He,M as ve,a as be,c as Ce,d as Se}from"./ModalHeader-BJG9dHtK.js";import{useWindowSizeFn as Me}from"./useWindowSizeFn-DDbrQbks.js";import{S as Fe}from"./index-LCGLvkB3.js";import{l as ye,m as Pe,aQ as $e,a as te,F as ke,ap as we,au as Re,n as Be}from"./index-CCWaWN5g.js";import{o as L}from"./antd-vue-vendor-me9YkNVC.js";const ne=Symbol();function Ne(e){return ye(e,ne)}function Ue(){return Pe(ne)}const We={loading:{type:Boolean},useWrapper:{type:Boolean,default:!0},modalHeaderHeight:{type:Number,default:57},modalFooterHeight:{type:Number,default:74},minHeight:{type:Number,default:null},maxHeight:{type:Number,default:null},height:{type:Number},footerOffset:{type:Number,default:0},visible:{type:Boolean},fullScreen:{type:Boolean},loadingTip:{type:String}},Ee=x({name:"ModalWrapper",components:{ScrollContainer:Fe},inheritAttrs:!1,props:We,emits:["height-change","ext-height"],setup(e,{emit:n}){const i=H(null),g=H(null),a=H(0),h=H(0);let c=0,f=()=>{};Me(d.bind(null,!1));let r,u={};W(()=>e.visible,()=>{e.visible&&!r&&!(e.maxHeight||e.height)?(u={},r=$e(g,()=>{d({source:"muob",callBack:o=>{const s=u[o];s?(u[o]=++u[o],s>10&&(r.stop(),u={},r=null)):(u={},u[o]=1)}})},{attributes:!0,subtree:!0})):r&&(r.stop(),r=null)},{immediate:!0}),Ne({redoModalHeight:d});const v=F(()=>{if(e.fullScreen)return{height:`${l(a)}px`};if(e.height!=null){let s=e.height;return e.minHeight===null?{height:`${s}px`}:{height:`${e.minHeight>s?e.minHeight:s}px`}}else return{minHeight:`${e.minHeight===null?200:e.minHeight}px`,maxHeight:`${e.maxHeight?e.maxHeight:l(a)}px`}});V(()=>{e.useWrapper&&d()}),W(()=>e.fullScreen,o=>{d(),o?h.value=a.value:a.value=h.value}),ue(()=>{const{modalHeaderHeight:o,modalFooterHeight:s}=e;n("ext-height",o+s)}),ce(()=>{f&&f()});function C(){return z(this,null,function*(){y(()=>{var s;const o=l(i);o&&((s=o==null?void 0:o.scrollTo)==null||s.call(o,0))})})}function d(o){return z(this,null,function*(){var O,T;const s=o||{},E=s.source,p=s.callBack;if(!e.visible)return;const P=l(i);if(!P)return;const $=(T=(O=P.$el.parentElement)==null?void 0:O.parentElement)==null?void 0:T.parentElement;if($){yield y();try{const k=$.parentElement&&$.parentElement.parentElement;if(!k)return;const A=getComputedStyle(k).top,j=Number.parseInt(A);let w=window.innerHeight-j*2+(e.footerOffset||0)-e.modalFooterHeight-e.modalHeaderHeight;j<40&&(w-=26),yield y();const D=l(g);if(!D)return;yield y(),c=D.scrollHeight,e.fullScreen?a.value=window.innerHeight-e.modalFooterHeight-e.modalHeaderHeight-28:a.value=e.height?e.height:c>w?w:c,E=="muob"&&p(a.value),n("height-change",l(a))}catch(k){}}})}return{wrapperRef:i,spinRef:g,spinStyle:v,scrollTop:C,setModalHeight:d}}}),Oe=["loading-tip"];function Te(e,n,i,g,a,h){const c=b("ScrollContainer"),f=de("loading");return G(),ee(c,{ref:"wrapperRef"},{default:m(()=>[fe((G(),ge("div",{ref:"spinRef",style:me(e.spinStyle),"loading-tip":e.loadingTip},[N(e.$slots,"default")],12,Oe)),[[f,e.loading]])]),_:3},512)}const je=te(Ee,[["render",Te]]);function De(e){const n=H(!1),i=F(()=>{const a=l(e.wrapClassName)||"";return l(n)?`fullscreen-modal ${a} `:l(a)});function g(a){a&&a.stopPropagation(),n.value=!l(n)}return{getWrapClassName:i,handleFullScreen:g,fullScreenRef:n}}const Ie=x({name:"BasicModal",components:{Modal:Se,ModalWrapper:je,ModalClose:Ce,ModalFooter:be,ModalHeader:ve},inheritAttrs:!1,props:He,emits:["visible-change","open-change","height-change","cancel","ok","register","update:visible","update:open","fullScreen","comment-open"],setup(e,{emit:n,attrs:i,slots:g}){const a=H(!1),h=H(null),c=H(null),{prefixCls:f}=ke("basic-modal"),r=H(0),u={setModalProps:k,emitVisible:void 0,redoModalHeight:()=>{y(()=>{l(c)&&l(c).setModalHeight()})}},v=he();v&&n("register",u,v.uid);const{getIsMobile:C}=we(),d=F(()=>{const t=S(S({},e),l(h));return C.value&&(t.canFullscreen=!1,t.defaultFullscreen=!0),t}),o=F(()=>!l(d).title&&!g.title),{handleFullScreen:s,getWrapClassName:E,fullScreenRef:p}=De({modalWrapperRef:c,extHeightRef:r,wrapClassName:pe(d.value,"wrapClassName")}),P=F(()=>{const t=I(S({},l(d)),{visible:l(a),okButtonProps:void 0,cancelButtonProps:void 0,title:void 0});return I(S({},t),{wrapClassName:l(E)})}),$=F(()=>{const t=I(S(S({},i),l(d)),{open:l(a),wrapClassName:l(E)});return l(p)?L(t,["height","title","visible"]):L(t,["title","visible"])}),O=F(()=>{if(!l(p))return l(P).height});V(()=>{p.value=!!e.defaultFullscreen,C.value&&(p.value=!0)}),V(()=>{a.value=!!e.visible}),V(()=>{a.value=!!e.open}),W(()=>l(a),t=>{var B;n("visible-change",t),n("open-change",t),n("update:visible",t),n("update:open",t),v&&((B=u.emitVisible)==null||B.call(u,t,v.uid)),y(()=>{e.scrollTop&&t&&l(c)&&l(c).scrollTop()})},{immediate:!1});function T(t){return z(this,null,function*(){var B,K;if(t==null||t.stopPropagation(),!((K=(B=t.target)==null?void 0:B.classList)!=null&&K.contains(f+"-close--custom"))){if(e.closeFunc&&Be(e.closeFunc)){const ae=yield e.closeFunc();a.value=!ae;return}a.value=!1,n("cancel",t)}})}function k(t){h.value=Re(l(h)||{},t),Reflect.has(t,"visible")&&(a.value=!!t.visible),Reflect.has(t,"open")&&(a.value=!!t.open),Reflect.has(t,"defaultFullscreen")&&(p.value=!!t.defaultFullscreen,C.value&&(p.value=!0))}function A(t){n("ok",t)}function j(t){n("height-change",t)}function w(t){r.value=t}function D(t){e.canFullscreen&&(t.stopPropagation(),s(t))}const R=H(0);W(()=>e.enableComment,t=>{J(t)},{immediate:!0});function J(t){t===!0?R.value=6:R.value=0,n("comment-open",R.value===0,R.value)}return W(p,t=>{n("fullScreen",t)}),{handleCancel:T,getBindValue:$,getProps:P,handleFullScreen:s,fullScreenRef:p,getMergeProps:d,handleOk:A,visibleRef:a,omit:L,modalWrapperRef:c,handleExtHeight:w,handleHeightChange:j,handleTitleDbClick:D,getWrapperHeight:O,commentSpan:R,handleComment:J,isNoTitle:o}}});function ze(e,n,i,g,a,h){const c=b("ModalClose"),f=b("ModalHeader"),r=b("ModalFooter"),u=b("ModalWrapper"),v=b("a-col"),C=b("a-row"),d=b("Modal");return G(),ee(d,q(e.getBindValue,{onCancel:e.handleCancel}),X({default:m(()=>[M(C,{class:"jeecg-modal-wrapper"},{default:m(()=>[M(v,{span:24-e.commentSpan,class:"jeecg-modal-content"},{default:m(()=>[M(u,q({useWrapper:e.getProps.useWrapper,footerOffset:e.wrapperFooterOffset,fullScreen:e.fullScreenRef,ref:"modalWrapperRef",loading:e.getProps.loading,"loading-tip":e.getProps.loadingTip,minHeight:e.getProps.minHeight,maxHeight:e.getProps.maxHeight,height:e.getWrapperHeight,visible:e.visibleRef,modalHeaderHeight:e.getProps.modalHeaderHeight,modalFooterHeight:e.footer!==void 0&&!e.footer?0:e.getProps.modalFooterHeight},e.omit(e.getProps.wrapperProps,"visible","height","modalFooterHeight"),{onExtHeight:e.handleExtHeight,onHeightChange:e.handleHeightChange}),{default:m(()=>[N(e.$slots,"default")]),_:3},16,["useWrapper","footerOffset","fullScreen","loading","loading-tip","minHeight","maxHeight","height","visible","modalHeaderHeight","modalFooterHeight","onExtHeight","onHeightChange"])]),_:3},8,["span"]),M(v,{span:e.commentSpan,class:"jeecg-comment-outer"},{default:m(()=>[N(e.$slots,"comment")]),_:3},8,["span"])]),_:3})]),_:2},[e.$slots.closeIcon?void 0:{name:"closeIcon",fn:m(()=>[M(c,{canFullscreen:e.getProps.canFullscreen,fullScreen:e.fullScreenRef,commentSpan:e.commentSpan,enableComment:e.getProps.enableComment,onComment:e.handleComment,onCancel:e.handleCancel,onFullscreen:e.handleFullScreen},null,8,["canFullscreen","fullScreen","commentSpan","enableComment","onComment","onCancel","onFullscreen"])]),key:"0"},e.isNoTitle?void 0:{name:"title",fn:m(()=>[M(f,{helpMessage:e.getProps.helpMessage,title:e.getMergeProps.title,onDblclick:e.handleTitleDbClick},null,8,["helpMessage","title","onDblclick"])]),key:"1"},e.$slots.footer?void 0:{name:"footer",fn:m(()=>[M(r,q(e.getBindValue,{onOk:e.handleOk,onCancel:e.handleCancel}),X({_:2},[Y(Object.keys(e.$slots),o=>({name:o,fn:m(s=>[N(e.$slots,o,Z(_(s||{})))])}))]),1040,["onOk","onCancel"])]),key:"2"},Y(Object.keys(e.omit(e.$slots,"default")),o=>({name:o,fn:m(s=>[N(e.$slots,o,Z(_(s||{})))])}))]),1040,["onCancel"])}const Ve=te(Ie,[["render",ze]]),Xe=Object.freeze(Object.defineProperty({__proto__:null,default:Ve},Symbol.toStringTag,{value:"Module"}));export{Xe as B,Ve as I,Ue as u};
