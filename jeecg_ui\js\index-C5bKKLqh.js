var g=(u,p,s)=>new Promise((r,i)=>{var n=e=>{try{a(s.next(e))}catch(t){i(t)}},d=e=>{try{a(s.throw(e))}catch(t){i(t)}},a=e=>e.done?r(e.value):Promise.resolve(e.value).then(n,d);a((s=s.apply(u,p)).next())});import{d as T,aq as h,ar as S,k as c,aD as m,u as l,G as b,au as f,at as _}from"./vue-vendor-dy9k-Yad.js";import{u as x}from"./index-BkGZ5fiW.js";import{X as D,T as w}from"./antd-vue-vendor-me9YkNVC.js";import v from"./BasicTable-xCEZpGLb.js";import{a as P}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const I={class:"p-4"},z={class:"message-detail-cell"},R=T({__name:"index",setup(u){const p=[{title:"消息详情",dataIndex:"messageDetail",width:400,resizable:!0,slots:{customRender:"messageDetail"}},{title:"消息标题",dataIndex:"messageTitle",width:200,resizable:!0},{title:"消息来源",dataIndex:"source",width:120,resizable:!0},{title:"消息状态",dataIndex:"messageStatus",width:120,resizable:!0,slots:{customRender:"messageStatus"}},{title:"发送时间",dataIndex:"sendTime",width:180,resizable:!0}],s=[{field:"source",label:"消息来源",component:"Select",componentProps:{placeholder:"请选择消息来源",options:[{label:"系统通知",value:"system"},{label:"安全提醒",value:"security"},{label:"更新公告",value:"update"},{label:"活动通知",value:"activity"}]},colProps:{span:6}},{field:"messageStatus",label:"消息状态",component:"Select",componentProps:{placeholder:"请选择消息状态",options:[{label:"未读",value:"unread"},{label:"已读",value:"read"}]},colProps:{span:6}},{field:"timeRange",label:"时间区间",component:"RangePicker",componentProps:{showTime:!0,format:"YYYY-MM-DD HH:mm:ss",placeholder:["开始时间","结束时间"]},colProps:{span:6}}],r=[{id:"1",messageDetail:"系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用，请提前做好相关准备工作。",messageTitle:"系统维护通知",source:"系统通知",messageStatus:"unread",sendTime:"2024-01-15 10:30:00"},{id:"2",messageDetail:"检测到您的账户在异地登录，如非本人操作，请立即修改密码并联系客服。",messageTitle:"安全登录提醒",source:"安全提醒",messageStatus:"read",sendTime:"2024-01-15 14:20:00"},{id:"3",messageDetail:"新版本v2.1.0已发布，新增了多项实用功能，包括消息中心、数据导出等，欢迎体验。",messageTitle:"版本更新公告",source:"更新公告",messageStatus:"read",sendTime:"2024-01-16 09:15:00"},{id:"4",messageDetail:"春节特惠活动开始啦！全场商品8折优惠，活动时间：1月20日-2月20日，数量有限，先到先得。",messageTitle:"春节特惠活动",source:"活动通知",messageStatus:"unread",sendTime:"2024-01-16 16:45:00"},{id:"5",messageDetail:"您的VIP会员即将到期，到期时间：2024年2月1日，请及时续费以继续享受会员权益。",messageTitle:"VIP会员到期提醒",source:"系统通知",messageStatus:"unread",sendTime:"2024-01-17 11:30:00"},{id:"6",messageDetail:"数据备份已完成，备份时间：2024-01-17 02:00:00，备份文件已保存至云端存储。",messageTitle:"数据备份完成",source:"系统通知",messageStatus:"read",sendTime:"2024-01-17 08:00:00"}];function i(e){return{unread:"red",read:"green"}[e]||"default"}function n(e){return{unread:"未读",read:"已读"}[e]||e}const d=e=>g(null,null,function*(){return yield new Promise(t=>setTimeout(t,500)),{items:r,total:r.length}}),[a]=x({api:d,columns:p,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,inset:!0,maxHeight:478,rowKey:"id",formConfig:{labelWidth:64,size:"large",schemas:s}});return(e,t)=>(S(),h("div",I,[c(l(v),{onRegister:l(a)},{messageDetail:m(({record:o})=>[c(l(w),{title:o.messageDetail},{default:m(()=>[_("div",z,f(o.messageDetail),1)]),_:2},1032,["title"])]),messageStatus:m(({record:o})=>[c(l(D),{color:i(o.messageStatus)},{default:m(()=>[b(f(n(o.messageStatus)),1)]),_:2},1032,["color"])]),_:1},8,["onRegister"])]))}}),Ve=P(R,[["__scopeId","data-v-accd3e60"]]);export{Ve as default};
