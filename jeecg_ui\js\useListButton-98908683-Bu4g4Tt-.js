import{E as Y,s as Oe}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{f as O,p as De,aN as Pe,J as $,j as xe,n as be,r as G,e as ke}from"./vue-vendor-dy9k-Yad.js";import{u as we,c5 as Fe,V as Re,r as he,j as E,G as Ce,ad as z,aZ as Ie,cx as $e,E as Ee}from"./index-CCWaWN5g.js";import{g as Me,I as Ne}from"./useCustomHook-acb00837-B7NPzH0H.js";import"./index-Diw57m_E.js";import{s as Z}from"./constant-fa63bd66-Ddbq-fz2.js";import{h as je}from"./cgformState-d9f8ec42-C8rx7JjX.js";import{p as Be,M as _e}from"./antd-vue-vendor-me9YkNVC.js";var Ue=Object.defineProperty,Qe=Object.defineProperties,Ke=Object.getOwnPropertyDescriptors,ye=Object.getOwnPropertySymbols,ze=Object.prototype.hasOwnProperty,Je=Object.prototype.propertyIsEnumerable,ve=(l,f,m)=>f in l?Ue(l,f,{enumerable:!0,configurable:!0,writable:!0,value:m}):l[f]=m,_=(l,f)=>{for(var m in f||(f={}))ze.call(f,m)&&ve(l,m,f[m]);if(ye)for(var m of ye(f))Je.call(f,m)&&ve(l,m,f[m]);return l},ge=(l,f)=>Qe(l,Ke(f)),ee=(l,f,m)=>new Promise((b,x)=>{var D=y=>{try{c(m.next(y))}catch(w){x(w)}},k=y=>{try{c(m.throw(y))}catch(w){x(w)}},c=y=>y.done?b(y.value):Promise.resolve(y.value).then(D,k);c((m=m.apply(l,f)).next())});const qe={acceptHrefParams:"<p> 跳转时获取的参数信息",currentPage:"<p> 当前页数",currentTableName:"<p> 当前表名",description:"<p> 当前表描述",hasChildrenField:"<p> 是否有子节点的字段名，仅树形表单下有效",isDesForm:"<p> xx",isTree:"<m> 是否是树形表单 ",loadData:"<m> 加载列表数据",pageSize:"<p> 每一页显示条数",queryParam:"<p> 查询条件对象，每次点击查询后才会更新此数据",selectedRowKeys:"<p> 选中的行的id数组",sortField:"<p> 排序字段",sortType:"<p> 排序规则",total:"<p> 总页数",foreignKeyValue:"<p> Erp一对多子表外键选中对应主表字段的值",isErpSubTable:"<p> 是否Erp一对多子表",foreignKeyField:"<p> Erp一对多子表外键字段",themeTemplate:"<p> 主题模板",isInnerSubTable:"<p> 是否内嵌一对多子表",innerSubTableId:"<p>内嵌一对多子表ID",innerSubTableName:"<p> 内嵌一对多子表名",mTableSelectedRcordId:"<p>内嵌主表展开行的id",innerSubTableFk:"<p>内嵌子表的外键字段"},Le={getColumns:"/online/cgform/api/getColumns/",getQueryInfo:"/online/cgform/api/getQueryInfo/",getData:"/online/cgform/api/getData/",getTreeData:"/online/cgform/api/getTreeData/",optPre:"/online/cgform/api/form/",buttonAction:"/online/cgform/api/doButton",exportXls:"/online/cgform/api/exportXlsOld/",importXls:"/online/cgform/api/importXls/",startProcess:"/act/process/extActProcess/startMutilProcess",getErpColumns:"/online/cgform/api/getErpColumns/",list:"/online/cgform/api/subform/list/"};let Ae={sortField:"id",sortType:"asc",currentPage:1,pageSize:10,total:0,selectedRowKeys:[],queryParam:{},acceptHrefParams:{},description:"",currentTableName:"",isDesForm:!1,desFormCode:"",cache:!1,isTree:!1,hasChildrenField:""};const Te={current:1,pageSize:10,pageSizeOptions:["10","20","30"],showTotal:(l,f)=>f[0]+"-"+f[1]+" 共"+l+"条",showQuickJumper:!0,showSizeChanger:!0,total:0},{createMessage:K,createErrorModal:Ve}=we();function nt(l={}){var f;const m=(f=l.code)!=null?f:"",b=O(m);De("tableId",b);const x=Pe(),D=O(),k=O(),c=O(!1),y=O([]),w=O(!0),J=O(),q=je(),T=Fe();let h={};const P={execButtonEnhance:function(t,r){if(o[Y][t])if(Oe===t)F(t);else{let s=$(r);return o[Y][t].call(o,o,s)}else if(o[Y][t+"_hook"])if(r){let s=$(r);F(t+"_hook",s)}else F(t+"_hook")},isTree:function(t){return typeof t=="boolean"?(o.isTreeTable=t,t):o.isTreeTable}};function F(t,r){let s=o[Y][t].toLocaleString().match(Me);if(s.length>1){let p=s[1];U(p,r)}}const o=new Proxy(qe,{get(t,r){if(typeof P[r]=="function")return P[r];{let s=h[b.value];return s==null?s:Reflect.get(s,r)}},set(t,r,s){let p=ne();return Reflect.set(typeof s=="function"?P:p,r,s)},deleteProperty(t,r){return r===b.value?(delete h[r],!0):!1}}),{executeJsEnhanced:U}=Ne({},o);function te(){let t=x.params.id;return t||(t=""),t}Re(({type:t})=>{!m&&W(),t==="activated"&&q.checkIsChanged(b.value)&&T.refreshPage(he),b.value&&q.removeChangedTable(b.value)}),xe(()=>{delete h[b.value]});function ne(){let t=h[b.value];if(!t){let r=Object.assign({},Ae,{onlineUrl:Le});t=JSON.parse(JSON.stringify(r)),l.themeTemplate==Z&&(t.pageSize=5),h[b.value]=t}return t}function ae(){let t={},r=x.query;r&&(Object.keys(r).map(s=>{t[s]=r[s]}),o.acceptHrefParams=t)}function oe(t=""){let r;return t==Z?r=`${o.onlineUrl.getErpColumns}${b.value}`:r=`${o.onlineUrl.getColumns}${b.value}`,new Promise((s,p)=>{E.get({url:r},{isTransformResponse:!1}).then(d=>{d.success?s(d.result):(K.warning(d.message),p())}).catch(()=>{p()})})}function M(t={}){const{delNum:r}=t;return new Promise((s,p)=>{if(r!=null){const{total:n,pageSize:a,current:i}=c.value,u=Math.ceil(n/a);i===u&&(c.value.current=Math.ceil((n-r)/a))}let d=L(),e=`${o.onlineUrl.getData}${b.value}`;o.isTree()===!0?e=`${o.onlineUrl.getTreeData}${b.value}`:o.isInnerSubTable===!0&&(e=`${o.onlineUrl.getData}${o.innerSubTableId}`,d={pageSize:-521},o.innerSubTableFk&&o.mTableSelectedRcordId&&(d[o.innerSubTableFk]=o.mTableSelectedRcordId)),o.isErpSubTable===!0&&(d[o.foreignKeyField]=o.foreignKeyValue,d.tabletype=3,delete d.hasQuery),E.get({url:e,params:d},{isTransformResponse:!1}).then(n=>{n.success?(A(n.result),s(!0)):(n.message==="NO_DB_SYNC"?Ve({title:"数据库未同步",content:"请先同步数据库再查看此页面！",onOk:()=>he.back()}):K.warning(n.message),p(!1))}).catch(()=>{K.warning("请求列表数据异常!"),p(!1)})})}function L(){const{sortField:t,sortType:r,acceptHrefParams:s,queryParam:p}=o;let d={};o.isTree(),d.hasQuery="true";let e=Object.assign({},d,s,p,{column:t,order:r});c.value?(e.pageNo=c.value.current,e.pageSize=c.value.pageSize):e.pageSize=-521;let n=H();return e.superQueryMatchType=n.matchType||"",e.superQueryParams=n.params||"",Ce(e)}function A(t){let r=0;Number(t.total)>0?(o.isTree()===!0?(y.value=Q(t.records),be(()=>{ce(y.value)})):y.value=t.records,r=Number(t.total)):y.value=[],c.value&&(c.value=ge(_({},c.value),{total:r}))}function V(t,r,s){s&&s.order?(o.sortField=s.field,o.sortType=s.order=="ascend"?"asc":"desc"):(o.sortField="id",o.sortType="asc"),c.value&&(c.value=t),M()}function re(t){o.description=t.description,o.currentTableName=t.currentTableName,o.isDesForm=t.isDesForm,o.desFormCode=t.desFormCode,o.ID=b.value;let{acceptHrefParams:r,queryParam:s,superQuery:p,currentPage:d,pageSize:e}=o;if(ae(),s?D.value&&D.value.initDefaultValues(s,r):o.queryParam={},p?k.value&&k.value.initDefaultValues(p):o.superQuery={params:"",matchType:""},t.paginationFlag=="Y"){let n=Te.pageSizeOptions;l.themeTemplate==Z&&(n=["5","10","30"]),c.value=ge(_({},Te),{current:d,pageSize:e,pageSizeOptions:n})}else c.value=!1}function le(){return ee(this,null,function*(){w.value=!0,yield be(),w.value=!1})}const N={loadData:M,getLoadDataParams:L,reloadTable:le};Object.keys(N).map(t=>{o[t]=N[t]});let ie=O(!1);function se(){return ee(this,arguments,function*(t={}){c.value&&(c.value=ge(_({},c.value),{current:t.mode=="search"||!c.value.current?1:c.value.current})),l.themeTemplate!==Z&&o.clearSelectedRow(),yield M()})}function Q(t){if(t)return t.map(r=>{let s=o.hasChildrenField;if(r[s]=="1"){let p={id:r.id+"_loadChild",name:"loading...",isLoading:!0};p.jeecg_row_key=p.id,r.children=[p]}return r})}const R=O([]);function ue(t){R.value=t}function ce(t){let r=R.value;if(r.length>0){const{sortField:s,sortType:p,pidField:d}=o;let e=Object.assign({},{column:s,order:p});e.hasQuery="in";let n=Object.assign({});n.rule="in",n.type="text",n.val=r.join(","),n.field=d,n=[n],e.superQueryParams=encodeURI(JSON.stringify(n)),e.superQueryMatchType="and",e.batchFlag="true";let a=`${o.onlineUrl.getTreeData}${b.value}`;E.get({url:a,params:e},{isTransformResponse:!1}).then(i=>{if(i.success&&i.result.records&&i.result.records.length>0){let u=i.result.records;const g=new Map;for(let S of u){let C=S[d];if(r.join(",").includes(C)){let B=g.get(C);B==null&&(B=[]),B.push(S),g.set(C,B)}}let v=g,I=S=>{S&&S.forEach(C=>{r.includes(C.id)&&(C.children=Q(v.get(C.id)),I(C.children))})};I(t)}}).catch(()=>{K.warning("loadDataByExpandedRows请求列表数据异常!")})}else return Promise.resolve()}function H(){if(!o.superQuery)return{};const{superQuery:{params:t,matchType:r},currentTableName:s}=o;let p=s+"@",d=[];if(t.length>0)for(let n of t){let a=_({},n),i=a.field;i.startsWith(p)&&(a.field=i.replace(p,"")),d.push(a)}let e=d.length>0?JSON.stringify(d):"";return{params:encodeURIComponent(e),matchType:r}}const j=O(!1);function pe(t,r){o.superQuery={params:t,matchType:r},t.length==0||t.length==null?j.value=!1:j.value=!0,c.value.current=1,M()}const[X,{openModal:de}]=z();function fe(t){if(t||(t={}),!t.row){let r=o.selectedRows;if(!r||r.length==0||r.length>1){K.warning("请选择一条数据");return}t.row=r[0]}t.code=b.value,de(!0,t)}o.openCustomModal=fe;function W(){let t=te();b.value=t}!m&&!b.value&&W();function me(t){let r=t.head.extConfigJson;r&&(J.value=JSON.parse(r))}return _({ID:b,onlineQueryFormOuter:D,superQueryButtonRef:k,loading:ie,reload:se,dataSource:y,pagination:c,tableReloading:w,handleSpecialConfig:re,onlineTableContext:o,handleChangeInTable:V,getColumnList:oe,getTreeDataByResult:Q,expandedRowKeys:R,handleExpandedRowsChange:ue,onlineExtConfigJson:J,handleFormConfig:me,superQueryStatus:j,handleSuperQuery:pe,registerCustomModal:X},N)}const Se="onl_";function at(l,f,m={}){const b={add:!0,addSub:!0,edit:!0,update:!0,delete:!0,batch_delete:!0,import:!0,export:!0,detail:!0,query:!0,reset:!0,super_query:!0,bpm:!0,form_confirm:!0,form_sub_add:!0,form_sub_batch_delete:!0,form_sub_open_add:!0,form_sub_open_edit:!0},[x,{openModal:D}]=z(),[k,{openModal:c}]=z(),[y,{openModal:w}]=z(),[J,{openModal:q}]=z(),{createMessage:T}=we(),h=G(b),P=G([]),F=G([]),o=G({}),U=e=>ke(()=>h[e]===!0?o[e]:{enabled:!1}),te=U("query"),ne=U("reset"),ae=U("form_confirm");function oe(e){if(P.length=0,F.length=0,e&&e.length>0)for(let n=0;n<e.length;n++){let a=Be(e[n],"buttonCode","buttonName","buttonStyle","optType","exp","buttonIcon","buttonStatus","enabled");a.buttonStyle=="button"?F.push(a):a.buttonStyle=="link"?P.push(a):a.buttonStyle=="built-in"&&(a.buttonIcon&&(a.buttonIcon="ant-design:"+a.buttonIcon),a.enabled=a.buttonStatus==="1",o[a.buttonCode]=a)}}function M(e){Object.keys(h).forEach(n=>{h[n]=!0}),e&&e.length>0&&Object.keys(h).forEach(n=>{e.indexOf(n)>=0&&(h[n]=!1)})}function L(e){let n={isUpdate:!1};e&&(n.param=e),D(!0,n)}function A(e){l.beforeEdit(e).then(()=>{D(!0,{isUpdate:!0,record:e})}).catch(n=>{T.warning(n)})}const V=e=>({label:o.delete.buttonName,ifShow:()=>o.delete.enabled,popConfirm:{title:"是否删除？",confirm:re.bind(null,e)}});function re(e){l.beforeDelete(e).then(()=>{j(e.id,!1)}).catch(n=>{T.warning(n)})}function le(e){let n=R(e),a=n&&(n=="1"||n=="3"||n=="4")||!n;return $(h.edit)===!0&&$(h.update)===!0&&a?[{label:o.edit.buttonName,ifShow:()=>o.edit.enabled,onClick:i=>{m.editClickCallback&&m.editClickCallback(e.id,i),A(e)}}]:[]}function N(e){return{label:o.bpm.buttonName,ifShow:()=>o.bpm.enabled,popConfirm:{title:"确认提交流程吗？",confirm:H.bind(null,e)}}}function ie(e){return{label:"审批进度",onClick:se.bind(null,e)}}function se(e){const{currentTableName:n}=l;let a=n;n.includes("$")&&(a=n.split("$")[0]);let i=Se+a,u=e.id;q(!0,{flowCode:i,dataId:u})}function Q(e,n={}){let a=[];if($(h.detail)===!0&&a.push({label:o.detail.buttonName,ifShow:()=>o.detail.enabled,onClick:ue.bind(null,e)}),l.hasBpmStatus===!0&&$(h.bpm)===!0){let v=R(e);!v||v=="1"?a.push(N(e)):a.push(ie(e))}if(f.value){let{reportPrintShow:v,reportPrintUrl:I}=f.value;v&&I&&a.push({label:"打印",onClick(){let S=I,C=e.id,B=Ie();$e(S,C,B)}})}let i=R(e),u=i&&i=="1"||!i;$(h.delete)===!0&&u&&a.push(V(e));let g=P;if(g&&g.length>0)for(let v of g)p(v.exp||"",e)===!0&&a.push({label:v.buttonName,onClick:X.bind(null,e,v.buttonCode,v.optType)});return a}function R(e){const n="bpm_status";let a=e[n];return a||(a=e[n.toUpperCase()]),a}function ue(e){w(!0,{isUpdate:!0,disableSubmit:!0,record:e})}function ce(e){const{currentTableName:n,onlineUrl:{startProcess:a}}=l;let i=n;n.includes("$")&&(i=n.split("$")[0]);let u={url:a,params:{flowCode:Se+i,id:e.id,formUrl:"modules/bpm/task/form/OnlineFormDetail",formUrlMobile:"check/onlineForm/detail"}},g={isTransformResponse:!1};return new Promise((v,I)=>{E.post(u,g).then(S=>{S.success?(v(S),T.success(S.message)):(I(),T.warning(S.message))})})}function H(e){return ee(this,null,function*(){yield ce(e),l.loadData()})}function j(e,n=!0){let a=`${l.onlineUrl.optPre}${l.ID}/${e}`;return l.isErpSubTable===!0&&(a=`${a}?tabletype=3`),new Promise((i,u)=>{E.delete({url:a},{isTransformResponse:!1}).then(g=>{g.success?(T.success(g.message),l.loadData({delNum:e.split(",").length}),n||m.singleDelCallback&&m.singleDelCallback(e),i(!0)):(T.warning(g.message),u())})})}function pe(){let e=l.selectedRowKeys;if(e.length<=0)return T.warning("请选择一条记录！"),!1;{let n=[];e.forEach(function(i){let u=i;u&&u.endsWith("_loadChild")&&(u=u.replace("_loadChild","")),n.indexOf(u)<0&&n.push(u)});let a=n.join(",");_e.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>ee(this,null,function*(){yield j(a),l.clearSelectedRow()})})}}function X(e,n,a){if(a=="js")l.execButtonEnhance(n,e);else if(a=="action"){let i={formId:l.ID,buttonCode:n,dataId:e.id},u=`${l.onlineUrl.buttonAction}`;E.post({url:u,params:i},{isTransformResponse:!1}).then(g=>{g.success?(l.loadData(),T.success("处理完成!")):T.warning(g.message)})}}function de(e){l.execButtonEnhance(e)}function fe(e){let n=l.selectedRowKeys;if(!n||n.length==0)return T.warning("请先选中一条记录"),!1;let a=n.join(","),i={formId:l.ID,buttonCode:e,dataId:a},u=`${l.onlineUrl.buttonAction}`;E.post({url:u,params:i},{isTransformResponse:!1}).then(g=>{g.success?(l.loadData(),l.clearSelectedRow(),T.success("处理完成!")):T.warning(g.message)})}function W(){l.foreignKeyField&&l.foreignKeyValue?c(!0,{[l.foreignKeyField]:l.foreignKeyValue}):c(!0)}const me=()=>{let e=`${l.onlineUrl.importXls}${l.ID}`;return l.isErpSubTable===!0&&(e=`${e}?tabletype=3`),e},{handleExportXlsx:t}=Ee();function r(){let e=l.getLoadDataParams(),n=l.selectedRowKeys;n&&n.length>0&&(e.selections=n.join(","));let a={};l.isErpSubTable===!0&&(a={tabletype:3},l.foreignKeyField&&l.foreignKeyValue&&(e[l.foreignKeyField]=l.foreignKeyValue));let i=JSON.stringify(Ce(e)),u=`${l.onlineUrl.exportXls}${l.ID}`;const g=l.description;return t(g,u,_({paramsStr:i},a))}function s(e,n){const a=[];e.split("||").forEach(u=>{const g=[];u.trim().split("&&").forEach(v=>{g.push(d(v.trim(),n))}),a.push(g.join("&&"))});const i=a.join("||");return new Function(`return ${i}`)()}function p(e,n){return!e||e==""?!0:e.indexOf("||")==-1&&e.indexOf("&&")==-1?d(e,n):s(e,n)}function d(e,n){if(!e||e=="")return!0;let a=e.split("#"),i=n[a[0]],u=a[1].toLowerCase();return u==="eq"?i==a[2]:u==="ne"?i!=a[2]:u==="empty"?a[2]==="true"?!i||i=="":i&&i.length>0:u==="in"?a[2].split(",").indexOf(String(i))>=0:!1}return{buttonSwitch:h,cgLinkButtonList:P,cgBIBtnMap:o,getQueryButtonCfg:te,getResetButtonCfg:ne,getFormConfirmButtonCfg:ae,cgTopButtonList:F,importUrl:me,registerModal:x,handleAdd:L,handleEdit:A,handleBatchDelete:pe,registerImportModal:k,onImportExcel:W,onExportExcel:r,getDropDownActions:Q,getActions:le,cgButtonJsHandler:de,cgButtonActionHandler:fe,cgButtonLinkHandler:X,initButtonList:oe,initButtonSwitch:M,getDeleteButton:V,handleSubmitFlow:H,getSubmitFlowButton:N,registerDetailModal:y,registerBpmModal:J,openDetailModal:w}}export{nt as d,at as m};
