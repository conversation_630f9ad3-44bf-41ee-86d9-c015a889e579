import{d as k,ag as r,aq as h,ar as w,k as l,aD as u,G as C}from"./vue-vendor-dy9k-Yad.js";import{u as _}from"./index-BkGZ5fiW.js";import{Q as g}from"./componentMap-Bkie1n3v.js";import T from"./BasicTable-xCEZpGLb.js";import{a as N}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const E=[{title:"成员姓名",dataIndex:"name",editRow:!0},{title:"工号",dataIndex:"no",editRow:!0},{title:"所属部门",dataIndex:"dept",editRow:!0}],x=[{name:"John Brown",no:"00001",dept:"New York No. 1 Lake Park"},{name:"John Brown2",no:"00002",dept:"New York No. 2 Lake Park"},{name:"John Brown3",no:"00003",dept:"New York No. 3Lake Park"}],B=k({components:{BasicTable:T,TableAction:g},setup(){const[n,{getDataSource:i}]=_({columns:E,showIndexColumn:!1,dataSource:x,actionColumn:{width:160,title:"操作",dataIndex:"action",slots:{customRender:"action"}},pagination:!1});function a(t){var o;(o=t.onEdit)==null||o.call(t,!0)}function s(t){var o;if((o=t.onEdit)==null||o.call(t,!1),t.isNew){const e=i(),f=e.findIndex(b=>b.key===t.key);e.splice(f,1)}}function d(t){var o;(o=t.onEdit)==null||o.call(t,!1,!0)}function c(t){}function p(){const t=i(),o={name:"",no:"",dept:"",editable:!0,isNew:!0,key:`${Date.now()}`};t.push(o)}function m(t,o){return t.editable?[{label:"保存",onClick:d.bind(null,t,o)},{label:"取消",popConfirm:{title:"是否取消编辑",confirm:s.bind(null,t,o)}}]:[{label:"编辑",onClick:a.bind(null,t)},{label:"删除"}]}return{registerTable:n,handleEdit:a,createActions:m,handleAdd:p,getDataSource:i,handleEditChange:c}}});function A(n,i,a,s,d,c){const p=r("TableAction"),m=r("BasicTable"),t=r("a-button");return w(),h("div",null,[l(m,{onRegister:n.registerTable,onEditChange:n.handleEditChange},{action:u(({record:o,column:e})=>[l(p,{actions:n.createActions(o,e)},null,8,["actions"])]),_:1},8,["onRegister","onEditChange"]),l(t,{block:"",class:"mt-5",type:"dashed",onClick:n.handleAdd},{default:u(()=>i[0]||(i[0]=[C(" 新增成员 ")])),_:1,__:[0]},8,["onClick"])])}const yt=N(B,[["render",A]]);export{yt as default};
