import{d as g,f as k,ag as r,aB as y,ar as i,aD as e,k as m,G as p,aq as c,F as b,at as C,au as h}from"./vue-vendor-dy9k-Yad.js";import{u as _}from"./index-BkGZ5fiW.js";import{getFormConfig as T,getBasicColumns as B}from"./tableData-B4J3mkj4.js";import{A as S}from"./antd-vue-vendor-me9YkNVC.js";import{d as w}from"./table-BDFKJhHv.js";import F from"./BasicTable-xCEZpGLb.js";import{a as K}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const A=g({components:{BasicTable:F,AAlert:S},setup(){const o=k([]),[t,{getForm:u}]=_({title:"开启搜索区域",api:w,columns:B(),useSearchForm:!0,formConfig:T(),showTableSetting:!0,tableSetting:{fullScreen:!0},showIndexColumn:!1,rowKey:"id"});function s(){}function n(a){o.value=a}return{registerTable:t,getFormValues:s,checkedKeys:o,onSelectChange:n}}}),V={key:1};function $(o,t,u,s,n,a){const l=r("a-button"),f=r("a-alert"),d=r("BasicTable");return i(),y(d,{onRegister:o.registerTable,rowSelection:{type:"checkbox",selectedRowKeys:o.checkedKeys,onChange:o.onSelectChange}},{"form-custom":e(()=>t[1]||(t[1]=[p(" custom-slot ")])),headerTop:e(()=>[m(f,{type:"info","show-icon":""},{message:e(()=>[o.checkedKeys.length>0?(i(),c(b,{key:0},[C("span",null,"已选中"+h(o.checkedKeys.length)+"条记录(可跨页)",1),m(l,{type:"link",onClick:t[0]||(t[0]=v=>o.checkedKeys=[]),size:"small"},{default:e(()=>t[2]||(t[2]=[p("清空")])),_:1,__:[2]})],64)):(i(),c("span",V,"未选中任何项目"))]),_:1})]),toolbar:e(()=>[m(l,{type:"primary",onClick:o.getFormValues},{default:e(()=>t[3]||(t[3]=[p("获取表单数据")])),_:1,__:[3]},8,["onClick"])]),_:1},8,["onRegister","rowSelection"])}const Go=K(A,[["render",$]]);export{Go as default};
