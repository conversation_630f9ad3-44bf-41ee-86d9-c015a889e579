var x=Object.defineProperty;var y=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable;var L=(s,e,t)=>e in s?x(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,g=(s,e)=>{for(var t in e||(e={}))w.call(e,t)&&L(s,t,e[t]);if(y)for(var t of y(e))B.call(e,t)&&L(s,t,e[t]);return s};var b=(s,e,t)=>new Promise((a,c)=>{var u=l=>{try{r(t.next(l))}catch(n){c(n)}},m=l=>{try{r(t.throw(l))}catch(n){c(n)}},r=l=>l.done?a(l.value):Promise.resolve(l.value).then(u,m);r((t=t.apply(s,e)).next())});import{d as I,f as M,e as S,u as i,aB as F,ar as O,aD as T,at as N,k as P,aE as U}from"./vue-vendor-dy9k-Yad.js";import{B as k}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{j as d,ac as j,a as E}from"./index-CCWaWN5g.js";import{M as R}from"./antd-vue-vendor-me9YkNVC.js";import{u as C}from"./useForm-CgkFTrrO.js";import{B as V}from"./BasicForm-DBcXiHk0.js";const f=[{label:"禁用",value:"0"},{label:"启用",value:"1"}],ee=[{title:"允许的表名",dataIndex:"tableName"},{title:"允许的字段名",dataIndex:"fieldName"},{title:"状态",dataIndex:"status",customRender({text:s}){const e=f.find(t=>t.value===s);return(e==null?void 0:e.label)||"未知"}},{title:"创建时间",dataIndex:"createTime"}],te=[{label:"允许的表名",field:"tableName",component:"Input"},{label:"允许的字段名",field:"fieldName",component:"Input"},{label:"状态",field:"status",component:"Select",componentProps:{options:f}}],X=[{label:"",field:"id",component:"Input",show:!1},{label:"允许的表名",field:"tableName",component:"Input",required:!0},{label:"允许的字段名",field:"fieldName",component:"Input",required:!0,helpMessage:"多个用逗号分割"},{label:"状态",field:"status",component:"Select",defaultValue:"1",componentProps:{options:f}}];const se="/sys/tableWhiteList/exportXls",oe="/sys/tableWhiteList/importExcel",le=s=>d.get({url:"/sys/tableWhiteList/list",params:s}),ae=(s,e)=>d.delete({url:"/sys/tableWhiteList/delete",params:s},{joinParamsToUrl:!0}).then(()=>{e()}),ie=(s,e)=>{R.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>d.delete({url:"/sys/tableWhiteList/deleteBatch",data:s},{joinParamsToUrl:!0}).then(()=>{e()})})},q=(s,e)=>{let t=e?"/sys/tableWhiteList/edit":"/sys/tableWhiteList/add";return d.post({url:t,params:s})},D={class:"content"},$=I({__name:"SysTableWhiteListModal",emits:["register","success"],setup(s,{emit:e}){const t=e,a=M(!0),[c,{resetFields:u,setFieldsValue:m,validate:r,setProps:l}]=C({labelWidth:120,wrapperCol:null,schemas:X,showActionButtonGroup:!1}),[n,{setModalProps:p,closeModal:W}]=j(o=>b(null,null,function*(){yield u(),p({confirmLoading:!1,showCancelBtn:o==null?void 0:o.showFooter,showOkBtn:o==null?void 0:o.showFooter}),a.value=!!(o!=null&&o.isUpdate),i(a)&&(yield m(g({},o.record))),l({disabled:!(o!=null&&o.showFooter)})})),_=S(()=>i(a)?"编辑":"新增");function v(o){return b(this,null,function*(){try{let h=yield r();p({confirmLoading:!0}),yield q(h,a.value),W(),t("success",{isUpdate:a.value,values:h})}finally{p({confirmLoading:!1})}})}return(o,h)=>(O(),F(i(k),U({onRegister:i(n),title:_.value,width:"40%"},o.$attrs,{onOk:v}),{default:T(()=>[N("div",D,[P(i(V),{onRegister:i(c)},null,8,["onRegister"])])]),_:1},16,["onRegister","title"]))}}),z=E($,[["__scopeId","data-v-b46d5b51"]]),re=Object.freeze(Object.defineProperty({__proto__:null,default:z},Symbol.toStringTag,{value:"Module"}));export{z as S,se as a,ie as b,ee as c,ae as d,re as e,oe as g,le as l,te as s};
