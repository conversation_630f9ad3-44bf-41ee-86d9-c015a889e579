const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/DropMenuItem-1e49abea-CIsL8rkB.js","js/antd-vue-vendor-me9YkNVC.js","js/vue-vendor-dy9k-Yad.js","js/index-CCWaWN5g.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css"])))=>i.map(i=>d[i]);
import{cs as x,u as D,F as k,N as w,ah as I,d as A,ca as M,cb as f,bh as U,H as L,aj as b,_ as N}from"./index-CCWaWN5g.js";import{k as C,D as P}from"./antd-vue-vendor-me9YkNVC.js";import{d as $,e as g,ag as p,aB as B,ar as H,aD as d,at as u,as as m,au as O,k as h}from"./vue-vendor-dy9k-Yad.js";import{useHeaderSetting as T}from"./useHeaderSetting-C-h5S52e.js";import{h as v}from"./header-OZa5fSDc.js";import{r as V,q}from"./dict.api-BW6kWzU4.js";import"./vxe-table-vendor-B22HppNm.js";var E=(e,o,r)=>new Promise((n,l)=>{var i=t=>{try{s(r.next(t))}catch(a){l(a)}},c=t=>{try{s(r.throw(t))}catch(a){l(a)}},s=t=>t.done?n(t.value):Promise.resolve(t.value).then(i,c);s((r=r.apply(e,o)).next())});const{createMessage:y}=D(),F=$({name:"UserDropdown",components:{Dropdown:P,Menu:C,MenuItem:b(()=>N(()=>import("./DropMenuItem-1e49abea-CIsL8rkB.js"),__vite__mapDeps([0,1,2,3,4,5]))),MenuDivider:C.Divider},props:{theme:L.oneOf(["dark","light"])},setup(){const{prefixCls:e}=k("header-user-dropdown"),{t:o}=w(),{getUseLockPage:r}=T(),n=I(),l=g(()=>{const{realname:a="NoLogin",avatar:_}=n.getUserInfo||{};return{realname:a,avatar:_||v}}),i=g(()=>{let{avatar:a}=l.value;return a==v?a:A(a)});function c(){n.confirmLoginOut()}function s(){return E(this,null,function*(){if((yield V()).success){const a=yield q();M(f),U(f,a.result),y.success(o("layout.header.refreshCacheComplete")),n.setAllDictItems(a.result)}else y.error(o("layout.header.refreshCacheFailure"))})}function t(a){switch(a.key){case"logout":c();break;case"cache":s();break}}return{prefixCls:e,t:o,getUserInfo:l,getAvatarUrl:i,handleMenuClick:t,getUseLockPage:r}}}),K=["src"];function S(e,o,r,n,l,i){const c=p("MenuItem"),s=p("Menu"),t=p("Dropdown");return H(),B(t,{placement:"bottomLeft",overlayClassName:`${e.prefixCls}-dropdown-overlay`},{overlay:d(()=>[h(s,{onClick:e.handleMenuClick},{default:d(()=>[h(c,{itemKey:"cache",text:e.t("layout.header.dropdownItemRefreshCache"),icon:"ion:sync-outline"},null,8,["text"]),h(c,{itemKey:"logout",text:e.t("layout.header.dropdownItemLoginOut"),icon:"ion:power-outline"},null,8,["text"])]),_:1},8,["onClick"])]),default:d(()=>[u("span",{class:m([[e.prefixCls,`${e.prefixCls}--${e.theme}`],"flex"])},[u("img",{class:m(`${e.prefixCls}__header`),src:e.getAvatarUrl},null,10,K),u("span",{class:m(`${e.prefixCls}__info hidden md:block`)},[u("span",{class:m([`${e.prefixCls}__name  `,"truncate"])},O(e.getUserInfo.realname),3)],2)],2)]),_:1},8,["overlayClassName"])}const W=x(F,[["render",S]]);export{W as default};
