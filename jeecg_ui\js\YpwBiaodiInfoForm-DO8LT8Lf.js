var h=Object.defineProperty;var f=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var u=(o,r,t)=>r in o?h(o,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[r]=t,_=(o,r)=>{for(var t in r||(r={}))w.call(r,t)&&u(o,t,r[t]);if(f)for(var t of f(r))D.call(r,t)&&u(o,t,r[t]);return o};var l=(o,r,t)=>new Promise((n,a)=>{var p=i=>{try{m(t.next(i))}catch(s){a(s)}},e=i=>{try{m(t.throw(i))}catch(s){a(s)}},m=i=>i.done?n(i.value):Promise.resolve(i.value).then(p,e);m((t=t.apply(o,r)).next())});import"./index-L3cSIXth.js";import{d as k,e as C,ag as y,aq as B,ar as F,k as b,ah as I,aD as v,G as V}from"./vue-vendor-dy9k-Yad.js";import{H as g,j as x,a as $}from"./index-CCWaWN5g.js";import{f as j,i as q}from"./YpwBiaodiInfo.api-Bht6QChW.js";import{B as N}from"./BasicForm-DBcXiHk0.js";import{u as G}from"./useForm-CgkFTrrO.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./renderUtils-D7XVOFwj.js";const H=k({name:"YpwBiaodiInfoForm",components:{BasicForm:N},props:{formData:g.object.def({}),formBpm:g.bool.def(!0)},setup(o){const[r,{setFieldsValue:t,setProps:n,getFieldsValue:a}]=G({labelWidth:150,schemas:q(o.formData),showActionButtonGroup:!1,baseColProps:{span:6}}),p=C(()=>o.formData.disabled!==!1);let e={};const m="/ypw/ypwBiaodiInfo/queryById";function i(){return l(this,null,function*(){let d={id:o.formData.dataId};const c=yield x.get({url:m,params:d});e=_({},c),yield t(e),yield n({disabled:p.value})})}function s(){return l(this,null,function*(){let d=a(),c=Object.assign({},e,d);yield j(c,!0)})}return i(),{registerForm:r,formDisabled:p,submitForm:s}}}),O={style:{"min-height":"400px"}},P={key:0,style:{width:"100%","text-align":"center"}};function R(o,r,t,n,a,p){const e=y("BasicForm"),m=y("a-button");return F(),B("div",O,[b(e,{onRegister:o.registerForm},null,8,["onRegister"]),o.formDisabled?I("",!0):(F(),B("div",P,[b(m,{onClick:o.submitForm,"pre-icon":"ant-design:check",type:"primary"},{default:v(()=>r[0]||(r[0]=[V("提 交")])),_:1,__:[0]},8,["onClick"])]))])}const At=$(H,[["render",R]]);export{At as default};
