import{a as l}from"./dict.api-BW6kWzU4.js";import{rules as c}from"./validator-B_KkcUnu.js";import{l as r}from"./vue-vendor-dy9k-Yad.js";const I=[{title:"字典名称",dataIndex:"dictName",width:240},{title:"字典编码",dataIndex:"dictCode",width:240},{title:"描述",dataIndex:"description"}],f=[{title:"字典名称",dataIndex:"dictName",width:120},{title:"字典编码",dataIndex:"dictCode",width:120},{title:"描述",dataIndex:"description",width:120}],h=[{label:"字典名称",field:"dictName",component:"JInput",colProps:{span:6}},{label:"字典编码",field:"dictCode",component:"JInput",colProps:{span:6}}],b=[{label:"",field:"id",component:"Input",show:!1},{label:"字典名称",field:"dictName",required:!0,component:"Input"},{label:"字典编码",field:"dictCode",component:"Input",dynamicDisabled:({values:e})=>!!e.id,dynamicRules:({model:e,schema:i})=>c.duplicateCheckRule("sys_dict","dict_code",e,i,!0)},{label:"描述",field:"description",component:"Input"}],x=[{title:"名称",dataIndex:"itemText",width:80},{title:"数据值",dataIndex:"itemValue",width:80},{title:"字典颜色",dataIndex:"itemColor",width:80,align:"center",customRender:({text:e})=>r("div",{style:{background:e,width:"18px",height:"18px","border-radius":"50%",margin:"0 auto"}})}],w=[{label:"名称",field:"itemText",component:"Input"},{label:"状态",field:"status",component:"JDictSelectTag",componentProps:{dictCode:"dict_item_status",stringToNumber:!0}}],C=[{label:"",field:"id",component:"Input",show:!1},{label:"名称",field:"itemText",required:!0,component:"Input"},{label:"数据值",field:"itemValue",component:"Input",dynamicRules:({values:e,model:i})=>[{required:!0,validator:(m,d)=>d?new RegExp("[`~!@#$^&*()=|{}'.<>《》/?！￥（）—【】‘；：”“。，、？]").test(d)?Promise.reject("数据值不能包含特殊字符！"):new Promise((n,o)=>{let a={dictId:e.dictId,id:i.id,itemValue:d};l(a).then(t=>{t.success?n():o(t.message||"校验失败")}).catch(t=>{o(t.message||"验证失败")})}):Promise.reject("请输入数据值")}]},{label:"颜色值",field:"itemColor",component:"Input",slot:"itemColor"},{label:"描述",field:"description",component:"Input"},{field:"sortOrder",label:"排序",component:"InputNumber",defaultValue:1},{field:"status",label:"是否启用",defaultValue:1,component:"JDictSelectTag",componentProps:{type:"radioButton",dictCode:"dict_item_status",stringToNumber:!0}}];export{x as a,I as c,w as d,b as f,C as i,f as r,h as s};
