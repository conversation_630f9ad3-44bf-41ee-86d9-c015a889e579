import{d as p,ag as l,aq as o,ar as s,as as n,F as d,aC as c,aB as m,aD as f,at as r}from"./vue-vendor-dy9k-Yad.js";import{T as u}from"./antd-vue-vendor-me9YkNVC.js";import{F as y,a as C}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const _=p({name:"MenuTypePicker",components:{Tooltip:u},props:{menuTypeList:{type:Array,defualt:()=>[]},handler:{type:Function,default:()=>({})},def:{type:String,default:""}},setup(){const{prefixCls:e}=y("setting-menu-type-picker");return{prefixCls:e}}}),k=["onClick"];function $(e,a,T,v,g,B){const i=l("Tooltip");return s(),o("div",{class:n(e.prefixCls)},[(s(!0),o(d,null,c(e.menuTypeList||[],t=>(s(),m(i,{key:t.title,title:t.title,placement:"bottom"},{default:f(()=>[r("div",{onClick:F=>e.handler(t),class:n([`${e.prefixCls}__item`,`${e.prefixCls}__item--${t.type}`,{[`${e.prefixCls}__item--active`]:e.def===t.type}])},a[0]||(a[0]=[r("div",{class:"mix-sidebar"},null,-1)]),10,k)]),_:2},1032,["title"]))),128))],2)}const D=C(_,[["render",$],["__scopeId","data-v-05365875"]]);export{D as default};
