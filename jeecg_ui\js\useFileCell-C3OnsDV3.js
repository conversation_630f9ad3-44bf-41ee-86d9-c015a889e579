var V=Object.defineProperty,J=Object.defineProperties;var w=Object.getOwnPropertyDescriptors;var c=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable;var f=(e,a,l)=>a in e?V(e,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[a]=l,r=(e,a)=>{for(var l in a||(a={}))E.call(a,l)&&f(e,l,a[l]);if(c)for(var l of c(a))F.call(a,l)&&f(e,l,a[l]);return e},s=(e,a)=>J(e,w(a));import{e as i}from"./vue-vendor-dy9k-Yad.js";import{cn as O,co as U,c as _,cp as b,aE as D,ad as N}from"./index-CCWaWN5g.js";import"./index-B4ez5KWV.js";import"./index-Diw57m_E.js";import{ai as S,D as j}from"./antd-vue-vendor-me9YkNVC.js";import{_ as k}from"./JUploadModal-C-iKhVFc.js";function B(e,a,l){const m=b(e,r({token:!0,action:D},l)),{innerFile:n,handleChangeCommon:d,originColumn:u}=m,[v,{openModal:h}]=N(),C=i(()=>{let o=5,t=n.value;return!t||!t.name?"":t.name.length>o?t.name.substr(0,o)+"…":t.name}),g=i(()=>{if(n.value){if(n.value.url)return n.value.url;if(n.value.path)return n.value.path}return""}),p=i(()=>{let o=u.value.maxCount;if(u.value&&u.value.fieldExtendJson){let t=JSON.parse(u.value.fieldExtendJson);o=t.uploadnum?t.uploadnum:0}return o!=null?o:0});function x(){h(!0,s(r({removeConfirm:!0,mover:!0,download:!0},u.value.props),{maxCount:p.value,fileType:a}))}function M(o){o?(n.value===null&&(n.value={}),n.value.path=o,d(n.value)):d(null)}return s(r({},m),{modalValue:g,maxCount:p,ellipsisFileName:C,registerModel:v,onModalChange:M,handleMoreOperation:x})}const H={Icon:_,Dropdown:j,LoadingOutlined:S,JUploadModal:k},K={switches:{visible:!0},getValue:e=>U(e),setValue:e=>O(e)};export{H as c,K as e,B as u};
