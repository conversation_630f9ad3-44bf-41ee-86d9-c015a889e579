var a=(u,p,r)=>new Promise((f,m)=>{var l=t=>{try{i(r.next(t))}catch(n){m(n)}},c=t=>{try{i(r.throw(t))}catch(n){m(n)}},i=t=>t.done?f(t.value):Promise.resolve(t.value).then(l,c);i((r=r.apply(u,p)).next())});import{d as B,f as M,ag as L,aB as _,ar as b,aD as s,k as h,u as e,ah as R,G as P,aE as N}from"./vue-vendor-dy9k-Yad.js";import{B as O}from"./index-Diw57m_E.js";import{u as U,a as V}from"./tenant.data-BEsk-IZ-.js";import{e as j,l as x}from"./tenant.api-CTNrRQ_d.js";import{useListPage as F}from"./useListPage-Soxgnx9a.js";import"./index-BkGZ5fiW.js";import{ac as K,a as A}from"./index-CCWaWN5g.js";import D from"./BasicTable-xCEZpGLb.js";import{Q as E}from"./componentMap-Bkie1n3v.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./renderUtils-D7XVOFwj.js";import"./index-QxsVJqiT.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const G="成员",Q=B({__name:"TenantUserList",emits:["register","success"],setup(u,{emit:p}){const r=M(0),{prefixCls:f,tableContext:m}=F({designScope:"tenant-template",tableProps:{api:j,columns:V,immediate:!1,formConfig:{schemas:U,labelWidth:40,actionColOptions:{xs:24,sm:8,md:8,lg:8,xl:8,xxl:8}},beforeFetch:o=>Object.assign(o,{userTenantId:e(r)})}}),[l,{reload:c},{rowSelection:i,selectedRowKeys:t}]=m,n=p,[C,{setModalProps:W,closeModal:v}]=K(o=>a(null,null,function*(){r.value=o.id,d()}));function y(o){return a(this,null,function*(){v()})}function I(o){return[{label:"移除",onClick:w.bind(null,o.id)}]}function d(){(t.value=[])&&c()}function w(o){return a(this,null,function*(){yield x({userIds:o,tenantId:e(r)},d)})}function T(){return a(this,null,function*(){yield x({userIds:t.value.join(","),tenantId:e(r)},d)})}return(o,g)=>{const k=L("a-button");return b(),_(e(O),N(o.$attrs,{onRegister:e(C),title:G,onOk:y,width:"800px"}),{default:s(()=>[h(e(D),{onRegister:e(l),rowSelection:e(i)},{tableTitle:s(()=>[e(t).length>0?(b(),_(k,{key:0,preIcon:"ant-design:delete-outlined",type:"primary",onClick:T,style:{"margin-right":"5px"}},{default:s(()=>g[0]||(g[0]=[P("批量请离")])),_:1,__:[0]})):R("",!0)]),action:s(({record:S})=>[h(e(E),{actions:I(S)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"])]),_:1},16,["onRegister"])}}}),eo=A(Q,[["__scopeId","data-v-cb9960d1"]]);export{eo as default};
