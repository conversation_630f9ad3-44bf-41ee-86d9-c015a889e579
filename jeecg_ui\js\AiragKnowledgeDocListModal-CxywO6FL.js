var we=Object.defineProperty;var W=Object.getOwnPropertySymbols;var Ce=Object.prototype.hasOwnProperty,Te=Object.prototype.propertyIsEnumerable;var Z=(A,t,g)=>t in A?we(A,t,{enumerable:!0,configurable:!0,writable:!0,value:g}):A[t]=g,q=(A,t)=>{for(var g in t||(t={}))Ce.call(t,g)&&Z(A,g,t[g]);if(W)for(var g of W(t))Te.call(t,g)&&Z(A,g,t[g]);return A};var V=(A,t,g)=>new Promise((e,Q)=>{var z=p=>{try{x(g.next(p))}catch(i){Q(i)}},v=p=>{try{x(g.throw(p))}catch(i){Q(i)}},x=p=>p.done?e(p.value):Promise.resolve(p.value).then(z,v);x((g=g.apply(A,t)).next())});import{f as d,l as Se,D as _e,ag as c,aq as y,ar as s,k as l,aD as r,aA as j,ah as f,aB as m,at as n,F as X,aC as $,au as w,G as Y,A as Qe}from"./vue-vendor-dy9k-Yad.js";import{I as ze}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{k as Ie,a as Be,b as De,c as Ue}from"./AiKnowledgeBase.api-Dgaf5KfS.js";import"./index-BkGZ5fiW.js";import be from"./AiragKnowledgeDocTextModal-BlzkYcnY.js";import Me from"./AiTextDescModal-LVvvAihi.js";import{c as Ke,ah as Fe,aC as Ee,b5 as Pe,ad as ee,ac as Ve,u as Ye,d as Re,a as Oe}from"./index-CCWaWN5g.js";import{ai as te,a3 as Ne,x as Le,M as He}from"./antd-vue-vendor-me9YkNVC.js";import{h as Je}from"./header-OZa5fSDc.js";import Ge from"./BasicTable-xCEZpGLb.js";import{Q as We}from"./componentMap-Bkie1n3v.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./BasicForm-DBcXiHk0.js";import"./index-L3cSIXth.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./AiKnowledgeBase.data-BCjuIj-H.js";import"./index-mbACBRQ9.js";import"./injectionKey-DPVn4AgL.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const Ze={name:"AiragKnowledgeDocListModal",components:{Icon:Ke,Pagination:Le,Avatar:Ne,LoadingOutlined:te,TableAction:We,BasicTable:Ge,BasicModal:ze,AiragKnowledgeDocTextModal:be,AiTextDescModal:Me},emits:["success","register"],setup(A,{emit:t}){const g=d("知识库详情"),e=d(""),Q=d(["document"]),z=d("document"),v=d(""),x=d(""),p=d(!1),i=d(.65),D=d(5),h=d([]),C=d(""),U=Fe(),T=d([]),I=d(1),S=d(10),b=d(0),R=d(["10","20","30"]),M=d(""),K=d(!1),k=d(null),O=Ee(),N=Pe(),L=d(N.domainUrl+"/airag/knowledge/doc/import/zip"),H=d([{key:"document",icon:"",label:"文档",title:"文档"},{key:"hitTest",icon:"",label:"命中测试",title:"命中测试"}]),[J,{openModal:o}]=ee(),[B,{openModal:oe}]=ee(),[ne,{closeModal:wt,setModalProps:le}]=Ve(a=>V(null,null,function*(){e.value=a.id,Q.value=["document"],z.value="document",p.value=!1,K.value=!1,yield _(),le({confirmLoading:!1})})),ae={textAlign:"center",height:"100%",width:"80%",background:"#ffffff"},ie={textAlign:"center",width:"20%",background:"#ffffff",borderRight:"1px solid #cecece"},se=Se(te,{style:{fontSize:"16px",marginRight:"2px"},spin:!0}),{createMessage:F}=Ye();function re(){o(!0,{knowledgeId:e.value,type:"text"})}function de(){o(!0,{knowledgeId:e.value,type:"file"})}function Ct(){F.warning("功能正在完善中....")}function ce(a){(a.type==="text"||a.type==="file")&&o(!0,{record:a,isUpdate:!0})}function ue(a){He.confirm({title:"提示",content:"确认要删除该文档吗?",okText:"确认",cancelText:"取消",onOk:()=>{Ie({ids:a},_)}})}function ge(a){return V(this,null,function*(){yield Be({docIds:a},G)})}function G(){k.value||_(),clearInterval(k.value),k.value=null,pe()}function pe(){k.value=setInterval(()=>{_()},5e3)}function fe(a){a.key==="document"?setTimeout(()=>{I.value=1,S.value=10,M.value="",_()}):(h.value=[],x.value="",v.value="",C.value="",i.value=.65,D.value=5),z.value=a.key}function me(){v.value&&(p.value=!0,De({queryText:v.value,knowId:e.value,topNumber:D.value,similarity:i.value}).then(a=>{a.success&&(a.result?h.value=a.result:h.value=[]),x.value=v.value,C.value=U.getUserInfo.avatar?Re(U.getUserInfo.avatar):Je,v.value="",K.value=h.value.length==0,p.value=!1}).catch(()=>{p.value=!1}))}function Ae(a){return"score "+a.toFixed(2)}function ve(a){oe(!0,q({},a))}function _(){return V(this,null,function*(){let a={pageNo:I.value,pageSize:S.value,knowledgeId:e.value,title:"*"+M.value+"*",column:"createTime",order:"desc"};yield Ue(a).then(u=>{if(u.success){if(u.result.records){let E=!0;for(const P of u.result.records)P.status&&P.status==="building"?(E=!1,P.loading=!0):P.loading=!1;E&&clearInterval(k.value)}T.value=u.result.records,b.value=u.result.total}else T.value=[],b.value=0})})}function ke(a,u){I.value=a,S.value=u,_()}function xe(a){if(a){let u=JSON.parse(a).filePath;const E=u.lastIndexOf(".");return E>0?u.substring(E+1).toLowerCase():""}return""}function he(a){return a.type!=="application/x-zip-compressed"?(F.warning("请上传zip文件"),!1):!0}function ye(a){let{file:u}=a;if(u.status==="error"&&F.error(u.response.message||`${u.name} 上传失败.`),u.status==="done"){if(!u.response.success){F.warning(u.response.message);return}F.success(u.response.message),G()}}return _e(()=>{clearInterval(k.value),k.value=null}),{registerModal:ne,title:g,docTextRegister:J,handleCreateText:re,beforeUpload:he,handleCreateUpload:de,handleSuccess:G,contentStyle:ae,siderStyle:ie,selectedKeys:Q,menuItems:H,handleMenuClick:fe,selectedKey:z,hitTestClick:me,hitText:v,spinning:p,similarity:i,topNumber:D,hitShowSearchText:x,avatar:C,hitTextList:h,getTagTxt:Ae,docTextDescRegister:B,hitTextDescClick:ve,knowledgeDocDataList:T,handleEdit:ce,handleDelete:ue,handleVectorization:ge,pageNo:I,pageSize:S,pageSizeOptions:R,total:b,handlePageChange:ke,searchText:M,reload:_,cardBodyStyle:{textAlign:"left",width:"100%"},getFileSuffix:xe,notHit:K,indicator:se,headers:O,uploadUrl:L,handleUploadChange:ye,knowledgeId:e}}},qe="data:image/png;base64,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",je={class:"p-2"},Xe={key:0},$e={class:"knowledge-header"},et={class:"header-text flex"},tt={class:"ellipsis header-title"},ot={class:"card-description"},nt={class:"flex",style:{"justify-content":"space-between"}},lt={class:"card-text"},at={key:0,class:"card-text-status"},it={key:1,class:"card-text-status"},st={key:2,class:"card-text-status"},rt={key:1,style:{padding:"16px"}},dt={class:"content"},ct={class:"content-title"},ut={class:"content-card"},gt={class:"card-title"},pt={style:{display:"flex"}},ft={style:{"margin-left":"4px"}},mt={style:{"margin-left":"10px"}},At={class:"card-description"},vt={class:"card-footer"},kt={key:1},xt={class:"param"},ht={class:"hit-test-footer"};function yt(A,t,g,e,Q,z){const v=c("a-menu"),x=c("a-layout-sider"),p=c("a-input"),i=c("Icon"),D=c("a-upload"),h=c("a-card"),C=c("a-col"),U=c("a-spin"),T=c("a-menu-item"),I=c("a-dropdown"),S=c("a-row"),b=c("Pagination"),R=c("Avatar"),M=c("a-tag"),K=c("a-empty"),k=c("a-input-number"),O=c("a-layout-content"),N=c("a-layout"),L=c("BasicModal"),H=c("AiragKnowledgeDocTextModal"),J=c("AiTextDescModal");return s(),y("div",je,[l(L,{wrapClassName:"airag-knowledge-doc",destroyOnClose:"",onRegister:e.registerModal,canFullscreen:!1,defaultFullscreen:"",title:e.title,footer:null},{default:r(()=>[l(N,{style:{height:"100%"}},{default:r(()=>[l(x,{style:j(e.siderStyle)},{default:r(()=>[l(v,{selectedKeys:e.selectedKeys,"onUpdate:selectedKeys":t[0]||(t[0]=o=>e.selectedKeys=o),mode:"vertical",style:{border:"none"},items:e.menuItems,onClick:e.handleMenuClick},null,8,["selectedKeys","items","onClick"])]),_:1},8,["style"]),l(O,{style:j(e.contentStyle)},{default:r(()=>[e.selectedKey==="document"?(s(),y("div",Xe,[l(p,{value:e.searchText,"onUpdate:value":t[1]||(t[1]=o=>e.searchText=o),placeholder:"请输入文档名称，回车搜索",class:"search-title",onPressEnter:e.reload},null,8,["value","onPressEnter"]),l(S,{span:24,class:"knowledge-row"},{default:r(()=>[l(C,{xxl:4,xl:6,lg:6,md:6,sm:12,xs:24},{default:r(()=>[l(h,{class:"add-knowledge-card",bodyStyle:e.cardBodyStyle},{default:r(()=>[t[12]||(t[12]=n("span",{style:{"line-height":"18px","font-weight":"500",color:"#676f83","font-size":"12px"}},"创建文档",-1)),n("div",{class:"add-knowledge-doc",onClick:t[2]||(t[2]=(...o)=>e.handleCreateText&&e.handleCreateText(...o))},[l(i,{icon:"ant-design:form-outlined",size:"13"}),t[9]||(t[9]=n("span",null,"手动录入",-1))]),n("div",{class:"add-knowledge-doc",onClick:t[3]||(t[3]=(...o)=>e.handleCreateUpload&&e.handleCreateUpload(...o))},[l(i,{icon:"ant-design:cloud-upload-outlined",size:"13"}),t[10]||(t[10]=n("span",null,"文件上传",-1))]),n("div",{class:"add-knowledge-doc",onClick:t[4]||(t[4]=(...o)=>A.handleCreateUploadLibrary&&A.handleCreateUploadLibrary(...o))},[l(D,{accept:".zip",name:"file",data:{knowId:e.knowledgeId},showUploadList:!1,headers:e.headers,beforeUpload:e.beforeUpload,action:e.uploadUrl,onChange:e.handleUploadChange},{default:r(()=>[l(i,{style:{"margin-left":"0"},icon:"ant-design:project-outlined",size:"13"}),t[11]||(t[11]=n("span",null,"文档库上传",-1))]),_:1,__:[11]},8,["data","headers","beforeUpload","action","onChange"])])]),_:1,__:[12]},8,["bodyStyle"])]),_:1}),(s(!0),y(X,null,$(e.knowledgeDocDataList,o=>(s(),m(C,{xxl:4,xl:6,lg:6,md:6,sm:12,xs:24},{default:r(()=>[l(h,{class:"knowledge-card pointer",onClick:B=>e.handleEdit(o)},{default:r(()=>[n("div",$e,[n("div",et,[o.type==="text"?(s(),m(i,{key:0,icon:"ant-design:file-text-outlined",size:"32",color:"#00a7d0"})):f("",!0),o.type==="file"&&e.getFileSuffix(o.metadata)==="pdf"?(s(),m(i,{key:1,icon:"ant-design:file-pdf-outlined",size:"32",color:"rgb(211, 47, 47)"})):f("",!0),o.type==="file"&&e.getFileSuffix(o.metadata)==="docx"?(s(),m(i,{key:2,icon:"ant-design:file-word-outlined",size:"32",color:"rgb(68, 138, 255)"})):f("",!0),o.type==="file"&&e.getFileSuffix(o.metadata)==="pptx"?(s(),m(i,{key:3,icon:"ant-design:file-ppt-outlined",size:"32",color:"rgb(245, 124, 0)"})):f("",!0),o.type==="file"&&e.getFileSuffix(o.metadata)==="xlsx"?(s(),m(i,{key:4,icon:"ant-design:file-excel-outlined",size:"32",color:"rgb(98, 187, 55)"})):f("",!0),o.type==="file"&&e.getFileSuffix(o.metadata)==="txt"?(s(),m(i,{key:5,icon:"ant-design:file-text-outlined",size:"32",color:"#00a7d0"})):f("",!0),o.type==="file"&&e.getFileSuffix(o.metadata)==="md"?(s(),m(i,{key:6,icon:"ant-design:file-markdown-outlined",size:"32",color:"#292929"})):f("",!0),o.type==="file"&&e.getFileSuffix(o.metadata)===""?(s(),m(i,{key:7,icon:"ant-design:file-unknown-outlined",size:"32",color:"#f5f5dc"})):f("",!0),n("span",tt,w(o.title),1)])]),n("div",ot,[n("span",null,w(o.content),1)]),n("div",nt,[n("div",lt,[t[16]||(t[16]=Y(" 状态： ")),o.status==="complete"?(s(),y("div",at,[l(i,{icon:"ant-design:check-circle-outlined",size:"16",color:"#56D1A7"}),t[13]||(t[13]=n("span",{class:"ml-2"},"已完成",-1))])):o.status==="building"?(s(),y("div",it,[o.loading?(s(),m(U,{key:0,spinning:o.loading,indicator:e.indicator},null,8,["spinning","indicator"])):f("",!0),t[14]||(t[14]=n("span",{class:"ml-2"},"构建中",-1))])):o.status==="draft"?(s(),y("div",st,t[15]||(t[15]=[n("img",{src:qe,style:{width:"16px",height:"16px"}},null,-1),n("span",{class:"ml-2"},"草稿",-1)]))):f("",!0)]),l(I,{placement:"bottomRight",trigger:["click"]},{overlay:r(()=>[l(v,null,{default:r(()=>[l(T,{key:"vectorization",onClick:B=>e.handleVectorization(o.id)},{default:r(()=>[l(i,{icon:"ant-design:retweet-outlined",size:"16"}),t[17]||(t[17]=Y(" 向量化 "))]),_:2,__:[17]},1032,["onClick"]),l(T,{key:"edit",onClick:B=>e.handleEdit(o)},{default:r(()=>[l(i,{icon:"ant-design:edit-outlined",size:"16"}),t[18]||(t[18]=Y(" 编辑 "))]),_:2,__:[18]},1032,["onClick"]),l(T,{key:"delete",onClick:B=>e.handleDelete(o.id)},{default:r(()=>[l(i,{icon:"ant-design:delete-outlined",size:"16"}),t[19]||(t[19]=Y(" 删除 "))]),_:2,__:[19]},1032,["onClick"])]),_:2},1024)]),default:r(()=>[n("div",{class:"ant-dropdown-link pointer operation",onClick:t[5]||(t[5]=Qe(()=>{},["prevent","stop"]))},[l(i,{icon:"ant-design:ellipsis-outlined",size:"16"})])]),_:2},1024)])]),_:2},1032,["onClick"])]),_:2},1024))),256))]),_:1}),e.knowledgeDocDataList.length>0?(s(),m(b,{key:0,current:e.pageNo,"page-size":e.pageSize,"page-size-options":e.pageSizeOptions,total:e.total,showQuickJumper:!0,showSizeChanger:!0,onChange:e.handlePageChange,class:"list-footer",size:"small"},null,8,["current","page-size","page-size-options","total","onChange"])):f("",!0)])):f("",!0),e.selectedKey==="hitTest"?(s(),y("div",rt,[l(U,{spinning:e.spinning},{default:r(()=>[t[24]||(t[24]=n("div",{class:"hit-test"},[n("h4",null,"命中测试"),n("span",null,"针对用户提问调试段落匹配情况，保障回答效果。")],-1)),n("div",dt,[n("div",ct,[e.hitShowSearchText?(s(),m(R,{key:0,size:35,src:e.avatar},null,8,["src"])):f("",!0),n("span",null,w(e.hitShowSearchText),1)]),n("div",ut,[e.hitTextList.length>0?(s(),m(S,{key:0,span:24,class:"knowledge-row"},{default:r(()=>[(s(!0),y(X,null,$(e.hitTextList,o=>(s(),m(C,{xxl:6,xl:6,lg:6,md:6,sm:12,xs:24},{default:r(()=>[l(h,{class:"hit-card pointer",style:{"border-color":"#ffffff"},onClick:B=>e.hitTextDescClick(o)},{default:r(()=>[n("div",gt,[n("div",pt,[l(i,{icon:"ant-design:appstore-outlined",size:"14"}),n("span",ft,"Chunk-"+w(o.chunk),1),n("span",mt,w(o.content.length)+" 字符",1)]),l(M,{class:"card-title-tag",color:"#a9c8ff"},{default:r(()=>[n("span",null,w(e.getTagTxt(o.score)),1)]),_:2},1024)]),n("div",At,w(o.content),1),n("div",vt,w(o.docName),1)]),_:2},1032,["onClick"])]),_:2},1024))),256))]),_:1})):e.notHit?(s(),y("div",kt,[l(K,{"image-style":{margin:"0 auto",height:"160px",verticalAlign:"middle",borderStyle:"none"}},{description:r(()=>t[20]||(t[20]=[n("div",{style:{"margin-top":"26px","font-size":"20px",color:"#000","text-align":"center !important"}}," 没有命中的分段 ",-1)])),_:1})])):f("",!0)])]),n("div",xt,[t[23]||(t[23]=n("span",{style:{"font-weight":"bold","font-size":"16px"}},"参数配置",-1)),n("ul",null,[n("li",null,[t[21]||(t[21]=n("span",null,"条数：",-1)),l(k,{min:1,value:e.topNumber,"onUpdate:value":t[6]||(t[6]=o=>e.topNumber=o)},null,8,["value"])]),n("li",null,[t[22]||(t[22]=n("span",null,"Score阈值：",-1)),l(k,{min:0,step:.01,max:1,value:e.similarity,"onUpdate:value":t[7]||(t[7]=o=>e.similarity=o)},null,8,["value"])])])]),n("div",ht,[l(p,{value:e.hitText,"onUpdate:value":t[8]||(t[8]=o=>e.hitText=o),size:"large",placeholder:"请输入",style:{width:"100%"},onPressEnter:e.hitTestClick},{suffix:r(()=>[l(i,{icon:"ant-design:send-outlined",style:{transform:"rotate(-33deg)",cursor:"pointer"},size:"22",onClick:e.hitTestClick},null,8,["onClick"])]),_:1},8,["value","onPressEnter"])])]),_:1,__:[24]},8,["spinning"])])):f("",!0)]),_:1},8,["style"])]),_:1})]),_:1},8,["onRegister","title"]),l(H,{onRegister:e.docTextRegister,onSuccess:e.handleSuccess},null,8,["onRegister","onSuccess"]),l(J,{onRegister:e.docTextDescRegister},null,8,["onRegister"])])}const Ko=Oe(Ze,[["render",yt],["__scopeId","data-v-a5d25bbb"]]);export{Ko as default};
