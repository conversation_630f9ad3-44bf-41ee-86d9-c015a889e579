import{e as i,aq as o,ah as l,ar as n,as as r,at as a}from"./vue-vendor-dy9k-Yad.js";import{cs as p,ap as d}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const m={name:"JModalTip",props:{visible:{type:Boolean,default:!1}},emits:["save","cancel"],setup(s){const{getIsMobile:e}=d();return{flag:i(()=>s.visible),getIsMobile:e}}},v={class:"container"},u={class:"outer"},f={class:"inner"};function b(s,e,g,t,k,I){return t.flag?(n(),o("div",{key:0,class:r(["jeecg-update-tip-bar",{mobile:t.getIsMobile}])},[a("div",v,[a("div",u,[a("div",f,[e[2]||(e[2]=a("span",{class:"tip"},"正在修改表单数据 ···",-1)),a("div",{class:"cancel",onClick:e[0]||(e[0]=c=>s.$emit("cancel"))},"取消"),a("div",{class:"save",onClick:e[1]||(e[1]=c=>s.$emit("save"))},"保存")])])])],2)):l("",!0)}const $=p(m,[["render",b],["__scopeId","data-v-dc9246f4"]]);export{$ as default};
