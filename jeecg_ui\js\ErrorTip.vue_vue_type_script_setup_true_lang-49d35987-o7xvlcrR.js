import{d as p,ag as s,aB as m,ar as d,aD as t,k as r,G as a}from"./vue-vendor-dy9k-Yad.js";import{r as f,bN as T}from"./index-CCWaWN5g.js";const C=p({__name:"ErrorTip",props:{title:{type:String,default:"啊哦，页面出错了"},subTitle:String},setup(n){function i(){f.push(T.BASE_HOME)}function l(){location.reload()}function u(){window.close(),setTimeout(()=>alert("当前页面无法通过按钮关闭，请手动关闭"),1e3)}return(_,e)=>{const o=s("a-button"),c=s("a-result");return d(),m(c,{status:"error",title:n.title,subTitle:n.subTitle},{extra:t(()=>[r(o,{type:"primary",onClick:i},{default:t(()=>e[0]||(e[0]=[a("返回首页")])),_:1}),r(o,{onClick:l},{default:t(()=>e[1]||(e[1]=[a("刷新页面")])),_:1}),r(o,{onClick:u},{default:t(()=>e[2]||(e[2]=[a("关闭")])),_:1})]),_:1},8,["title","subTitle"])}}});export{C as x};
