var w=(y,c,u)=>new Promise((_,n)=>{var f=l=>{try{g(u.next(l))}catch(v){n(v)}},m=l=>{try{g(u.throw(l))}catch(v){n(v)}},g=l=>l.done?_(l.value):Promise.resolve(l.value).then(f,m);g((u=u.apply(y,c)).next())});import{d as B,f as b,r as K,ag as p,aq as T,ar as U,k as a,aD as o,G as i,u as C}from"./vue-vendor-dy9k-Yad.js";import{j as $}from"./index-CCWaWN5g.js";import{B as P}from"./Bar-Tb_Evzfu.js";import{P as V}from"./Pie-BKQXlPsX.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const j={class:"p-4"},Q=B({__name:"index",setup(y){const c=b("bar"),u=b("year"),_=b("year"),n=b([]),f=K({getYearCountInfo:"/mock/api/report/getYearCountInfo",getMonthCountInfo:"/mock/api/report/getMonthCountInfo",getCntrNoCountInfo:"/mock/api/report/getCntrNoCountInfo",getCabinetCountInfo:"/mock/api/report/getCabinetCountInfo"});function m(t,e,r){return w(this,null,function*(){const s=yield $.get({url:t,params:r},{isTransformResponse:!1,errorMessageMode:"none"});if(s.success)switch(n.value=[],e){case"year":g(s.result);break;case"month":l(s.result);break;case"category":v(s.result);break;case"cabinet":x(s.result);break;default:break}})}function g(t){for(let e=0;e<t.length;e++)n.value.push({name:`${t[e].year}年`,value:t[e].yearcount})}function l(t){for(let e=0;e<t.length;e++)n.value.push({name:`${t[e].month}`,value:t[e].monthcount})}function v(t){for(let e=0;e<t.length;e++)n.value.push({name:`${t[e].classifyname}`,value:t[e].cntrnocount})}function x(t){for(let e=0;e<t.length;e++)n.value.push({name:`${t[e].cabinetname}`,value:t[e].cabinetcocunt})}function h(t){C(c)==="pie"?m(k(C(_)),C(_),{}):m(k(C(u)),C(u),{})}function k(t){if(t==="year")return f.getYearCountInfo;if(t==="month")return f.getMonthCountInfo;if(t==="category")return f.getCntrNoCountInfo;if(t==="cabinet")return f.getCabinetCountInfo}function N(t){}return m(f.getYearCountInfo,"year",{}),(t,e)=>{const r=p("a-radio-button"),s=p("a-radio-group"),I=p("a-col"),D=p("a-row"),M=p("a-tab-pane"),S=p("a-tabs"),Y=p("a-card");return U(),T("div",j,[a(Y,{bordered:!1,style:{height:"100%"}},{default:o(()=>[a(S,{activeKey:c.value,"onUpdate:activeKey":e[2]||(e[2]=d=>c.value=d),animated:"",onChange:N},{default:o(()=>[a(M,{key:"bar",tab:"柱状图"},{default:o(()=>[a(D,null,{default:o(()=>[a(I,{span:10},{default:o(()=>[a(s,{value:u.value,"onUpdate:value":e[0]||(e[0]=d=>u.value=d),onChange:h},{default:o(()=>[a(r,{value:"year"},{default:o(()=>e[3]||(e[3]=[i("按年统计")])),_:1,__:[3]}),a(r,{value:"month"},{default:o(()=>e[4]||(e[4]=[i("按月统计")])),_:1,__:[4]}),a(r,{value:"category"},{default:o(()=>e[5]||(e[5]=[i("按类别统计")])),_:1,__:[5]}),a(r,{value:"cabinet"},{default:o(()=>e[6]||(e[6]=[i("按柜号统计")])),_:1,__:[6]})]),_:1},8,["value"])]),_:1})]),_:1}),a(P,{chartData:n.value,height:"50vh"},null,8,["chartData"])]),_:1}),a(M,{key:"pie",tab:"饼状图","force-render":""},{default:o(()=>[a(D,{gutter:24},{default:o(()=>[a(I,{span:10},{default:o(()=>[a(s,{value:_.value,"onUpdate:value":e[1]||(e[1]=d=>_.value=d),onChange:h},{default:o(()=>[a(r,{value:"year"},{default:o(()=>e[7]||(e[7]=[i("按年统计")])),_:1,__:[7]}),a(r,{value:"month"},{default:o(()=>e[8]||(e[8]=[i("按月统计")])),_:1,__:[8]}),a(r,{value:"category"},{default:o(()=>e[9]||(e[9]=[i("按类别统计")])),_:1,__:[9]}),a(r,{value:"cabinet"},{default:o(()=>e[10]||(e[10]=[i("按柜号统计")])),_:1,__:[10]})]),_:1},8,["value"])]),_:1}),a(V,{chartData:n.value,height:"40vh"},null,8,["chartData"])]),_:1})]),_:1})]),_:1},8,["activeKey"])]),_:1})])}}});export{Q as default};
