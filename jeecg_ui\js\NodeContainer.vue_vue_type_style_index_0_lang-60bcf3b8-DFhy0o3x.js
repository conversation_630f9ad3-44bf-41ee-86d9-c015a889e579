import{d as q,f as Y,r as T,c as K,e as w,h as Q,w as V,n as W,o as X,b as Z,ag as y,aq as C,ar as r,aA as ee,as as F,at as o,ah as _,k as i,aG as J,au as S,aB as k,aD as s,A as te,u as g,G as ae}from"./vue-vendor-dy9k-Yad.js";import{x as ne}from"./runStore-e1feeee0-BrPjIiN4.js";import{addResizeListener as oe,removeResizeListener as ie}from"./index-D6l0IxOU.js";import{F as se,c as f}from"./index-CCWaWN5g.js";import{U as le}from"./AddNodeDialog.vue_vue_type_style_index_0_lang-675acbfd-CytYr8Kw.js";import{d as de}from"./NodeIcon.vue_vue_type_script_setup_true_lang-0fd734b2-CEYYkV5U.js";const re={class:"header"},ce=["title"],pe={class:"text airag-node-label"},ue={class:"extra"},ge={class:"shortcut-keys-tip"},fe={key:0,class:"content"},ve=["title"],we=q({__name:"NodeContainer",props:{node:{type:Object},graph:{type:Object},hideAction:{type:Boolean,default:!1},onUpdateNode:Function},setup(N,{expose:M}){const{prefixCls:O}=se("airag-base-node-container"),x=N,u=()=>({node:x.node,graph:x.graph}),v=Y(),a=T({id:"",type:"",width:0,height:0,isSelected:!1,properties:{}}),D=K("editConfigModel",{}),j=w(()=>D==null?void 0:D.isSilentMode),c=T({visible:!1,payload:{}}),$=ne(),l=w(()=>{if(!($.isRunning||$.isFinished))return["",""];const t=$.nodeSteps.find(e=>e.node.id===a.id);return t?[t.status,t.timeText]:["waiting",""]}),G=w(()=>{const t=[];if(l.value[0]){const e=`run-status-${l.value[0]}`;t.push(e)}return[O,{selected:a.isSelected},t]});Q(()=>{const{graph:t,node:e}=u();t.$J.setEdgeRunStatus(e,l.value[0])});const H=w(()=>({width:`${a.width-20}px`}));V(()=>a.isSelected,(t,e)=>{e&&!t&&c.visible&&z(!1)}),V(()=>a.properties.remarks,()=>W(()=>b())),X(()=>{const{graph:t,node:e}=u();t.$J.register(e,{onNodeUpdated(){E()},onNodeClick(n){},onGraphUpdated(n){},onPropertiesChange(n){a.properties=n.properties},toggleAddNodeDialog(n){z(!c.visible),c.payload=n}}),oe(v.value,U),b()}),Z(()=>{ie(v.value,U)});function U(){b()}function z(t){c.visible=t;const{node:e}=u();e.draggable=!t}function b(t){if(!v.value)return;const{node:e,graph:n}=u();t==null&&(t=v.value.offsetHeight);const p=e.height;let d,m;p>t?(d=(p-t)/2,m=e.y-d):(d=(t-p)/2,m=e.y+d),e.setProperties({height:t}),e.moveTo(e.x,m);const A=e.getDefaultAnchor();for(const h of A){const P=h.type==="right"?["getAnchorOutgoingEdge","updateStartPoint"]:["getAnchorIncomingEdge","updateEndPoint"],B=n[P[0]](h.id),R=B==null?void 0:B[0];R&&R[P[1]]({x:h.x,y:h.y})}}function E(){const{node:t}=u();a.id=t.id,a.type=t.type,a.width=t.width,a.height=t.height,a.isSelected=t.isSelected,a.properties=t.properties,typeof x.onUpdateNode=="function"&&x.onUpdateNode(a)}E();function L({key:t}){const{graph:e,node:n}=u();e.$J.doAction(t,{node:n})}function I(t){var e;const{graph:n,node:p}=u(),d={nodeType:t,prevData:{node:p}};(e=c.payload)!=null&&e.anchor&&(d.prevData.anchor=c.payload.anchor),n.$J.doAction("add-node",d),z(!1),c.payload={}}return M({$node:a,updateHeight:b}),(t,e)=>{const n=y("a-space"),p=y("a-menu-item"),d=y("a-menu-divider"),m=y("a-tooltip"),A=y("a-menu"),h=y("a-dropdown");return r(),C("div",{ref_key:"containerRef",ref:v,class:F(G.value),style:ee(H.value)},[o("div",re,[o("div",{class:"icon",title:a.id},[J(t.$slots,"icon",{},()=>[i(de,{type:a.type},null,8,["type"])])],8,ce),o("div",pe,[o("span",null,S(a.properties.text),1)]),o("div",ue,[!j.value&&!N.hideAction?(r(),k(h,{key:0,trigger:["click"],placement:"bottomRight",overlayClassName:"airag-node-action-dropdown",getPopupContainer:()=>v.value},{overlay:s(()=>[i(A,{style:{width:"240px"},onClick:L},{default:s(()=>[i(p,{key:"copy"},{default:s(()=>[i(n,null,{default:s(()=>[i(g(f),{icon:"ant-design:copy",size:16}),e[1]||(e[1]=o("span",null,"复制",-1))]),_:1})]),_:1}),i(d),i(p,{key:"delete",class:"hover-red"},{default:s(()=>[i(n,null,{default:s(()=>[i(g(f),{icon:"ant-design:delete",size:16}),e[2]||(e[2]=o("span",null,"删除",-1))]),_:1}),o("div",ge,[i(m,{title:"选中节点时按下 Delete 键即可删除"},{default:s(()=>e[3]||(e[3]=[ae("Del")])),_:1})])]),_:1})]),_:1})]),default:s(()=>[o("div",{class:"action-item dropdown","data-is-node-action":"",onClick:e[0]||(e[0]=te(()=>{},["stop"]))},[i(g(f),{icon:"ant-design:ellipsis",size:24})])]),_:1},8,["getPopupContainer"])):_("",!0)])]),t.$slots.default?(r(),C("div",fe,[J(t.$slots,"default")])):_("",!0),l.value[0]?(r(),C("div",{key:1,class:F(["run-status",l.value[0]])},[l.value[0]==="waiting"?(r(),k(n,{key:0},{default:s(()=>[i(g(f),{icon:"tabler:clock",size:12}),e[4]||(e[4]=o("span",null,"等待中",-1))]),_:1})):l.value[0]==="running"?(r(),k(n,{key:1},{default:s(()=>[i(g(f),{icon:"eos-icons:bubble-loading",size:12}),e[5]||(e[5]=o("span",null,"运行中",-1))]),_:1})):l.value[0]==="success"?(r(),k(n,{key:2},{default:s(()=>[i(g(f),{icon:"ix:success",size:12}),e[6]||(e[6]=o("span",null,"运行成功",-1)),o("span",null,"耗时："+S(l.value[1]),1)]),_:1})):l.value[0]==="fail"?(r(),k(n,{key:3},{default:s(()=>[i(g(f),{icon:"ix:namur-failure-filled",size:12}),e[7]||(e[7]=o("span",null,"运行失败",-1)),o("span",null,"耗时："+S(l.value[1]),1)]),_:1})):_("",!0)],2)):a.properties.remarks?(r(),C("div",{key:2,class:"remarks",title:a.properties.remarks},[o("span",null,S(a.properties.remarks),1)],8,ve)):_("",!0),i(le,{visible:c.visible,onAdd:I},null,8,["visible"])],6)}}});export{we as x};
