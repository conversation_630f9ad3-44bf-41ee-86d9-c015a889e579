var L=Object.defineProperty,T=Object.defineProperties;var H=Object.getOwnPropertyDescriptors;var F=Object.getOwnPropertySymbols;var z=Object.prototype.hasOwnProperty,q=Object.prototype.propertyIsEnumerable;var w=(a,e,t)=>e in a?L(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,p=(a,e)=>{for(var t in e||(e={}))z.call(e,t)&&w(a,t,e[t]);if(F)for(var t of F(e))q.call(e,t)&&w(a,t,e[t]);return a},b=(a,e)=>T(a,H(e));var m=(a,e,t)=>w(a,typeof e!="symbol"?e+"":e,t);var f=(a,e,t)=>new Promise((n,r)=>{var s=o=>{try{d(t.next(o))}catch(h){r(h)}},u=o=>{try{d(t.throw(o))}catch(h){r(h)}},d=o=>o.done?n(o.value):Promise.resolve(o.value).then(s,u);d((t=t.apply(a,e)).next())});import{d as B,ap as U,f as R,p as P,o as N,b as J,aq as G,ar as O,at as c,k as v,as as $}from"./vue-vendor-dy9k-Yad.js";import K from"./VisualHeader-BzFqf3lc.js";import V from"./TargetRanking-DQFbJbx2.js";import W from"./MainKPICards-CZfg9q_T.js";import X from"./SmallCards-DrPY0cXp.js";import j from"./AssetRanking-ClQIK-_m.js";import Y from"./BottomCharts-ClFSCSBZ.js";import{a as Q}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./SupplyDemand-DK00S9Ao.js";import"./areaDataUtil-BXVjRArW.js";import"./vxe-table-vendor-B22HppNm.js";import"./CountTo-Bf9dGyG1.js";import"./index-C5ZumRS6.js";import"./renderers-CGMjx3X9.js";const y=1658,D=994,Z=.5,ee=2;function M(){const a=document.querySelector(".visual-container");let e=window.innerWidth,t=window.innerHeight;if(a){const u=a.getBoundingClientRect(),d=a.parentElement;if(d){const o=d.getBoundingClientRect();e=o.width,t=o.height}if(u.width>0&&u.height>0&&(e=u.width,t=u.height),a.classList.contains("fullscreen"))return{scale:1,width:y,height:D}}const n=e/y,r=t/D;let s=Math.min(n,r);return s=Math.max(Z,Math.min(ee,s)),{scale:s,width:y*s,height:D*s}}function te(a){if(!a)return;const{scale:e}=M(),t=a.closest(".visual-container");(t==null?void 0:t.classList.contains("fullscreen"))?(a.style.transform=`translate(-50%, -50%) scale(${e})`,a.style.transformOrigin="center center"):(a.style.transform=`translate(-50%, -50%) scale(${e})`,a.style.transformOrigin="center center",t&&(t.style.position="relative",t.style.overflow="hidden"))}function x(){const a=window.innerWidth;return a>=1024?"desktop":a>=768?"tablet":"mobile"}function ae(){const a=x(),{scale:e}=M();switch(a){case"mobile":return{containerClass:"mobile-layout",showMobileLayout:!0,chartHeight:200};case"tablet":return{containerClass:"tablet-layout",showMobileLayout:!1,chartHeight:250};default:return{containerClass:e<.8?"compact-layout":"desktop-layout",showMobileLayout:!1,chartHeight:300}}}function ne(a,e){let t;return function(...r){const s=()=>{clearTimeout(t),a(...r)};clearTimeout(t),t=setTimeout(s,e)}}function se(a,e){let t;return function(...r){t||(a.apply(this,r),t=!0,setTimeout(()=>t=!1,e))}}function re(){const a=()=>{const n=document.querySelector(".visual-content");te(n)},e=ne(a,100),t=se(a,16);return{updateScale:a,debouncedUpdateScale:e,throttledUpdateScale:t,calculateScale:M,getDeviceType:x,adjustLayoutForDevice:ae}}class ie{constructor(){m(this,"kpiData",{totalAmount:59645956,premiumAmount:5956,premiumRate:95,totalTargets:5956,assetDisposalTotal:2956,targetAmount:35956,assetAmount:23689,targetPremiumRate:87.5});m(this,"chartData",{trendData:[{month:"1月",value:85},{month:"2月",value:92},{month:"3月",value:78},{month:"4月",value:95},{month:"5月",value:88},{month:"6月",value:96},{month:"7月",value:89},{month:"8月",value:93},{month:"9月",value:87},{month:"10月",value:91}],barData:[{name:"项目A",value:5969},{name:"项目B",value:4569},{name:"项目C",value:3969},{name:"项目D",value:2969},{name:"项目E",value:1969},{name:"项目F",value:969},{name:"项目G",value:869},{name:"项目H",value:769},{name:"项目I",value:669},{name:"项目J",value:569}],premiumAmountRanking:[{name:"标的某某某项目A",value:5969,rank:1},{name:"标的某某某项目B",value:4569,rank:2},{name:"标的某某某项目C",value:3969,rank:3},{name:"标的某某某项目D",value:2969,rank:4},{name:"标的某某某项目E",value:1969,rank:5},{name:"标的某某某项目F",value:969,rank:6},{name:"标的某某某项目G",value:869,rank:7},{name:"标的某某某项目H",value:769,rank:8},{name:"标的某某某项目I",value:669,rank:9},{name:"标的某某某项目J",value:569,rank:10}],premiumRateRanking:[{name:"标的某某某项目A",value:95.5,rank:1},{name:"标的某某某项目B",value:92.3,rank:2},{name:"标的某某某项目C",value:89.7,rank:3},{name:"标的某某某项目D",value:87.2,rank:4},{name:"标的某某某项目E",value:85.8,rank:5},{name:"标的某某某项目F",value:83.4,rank:6},{name:"标的某某某项目G",value:81.9,rank:7},{name:"标的某某某项目H",value:79.6,rank:8},{name:"标的某某某项目I",value:77.3,rank:9},{name:"标的某某某项目J",value:75.1,rank:10}],assetDisposalRanking:[{name:"资产处置项目A",value:88.9,rank:1},{name:"资产处置项目B",value:86.5,rank:2},{name:"资产处置项目C",value:84.2,rank:3},{name:"资产处置项目D",value:82.7,rank:4},{name:"资产处置项目E",value:80.3,rank:5},{name:"资产处置项目F",value:78.9,rank:6},{name:"资产处置项目G",value:76.4,rank:7},{name:"资产处置项目H",value:74.8,rank:8},{name:"资产处置项目I",value:72.5,rank:9},{name:"资产处置项目J",value:70.2,rank:10}]})}getKPIData(){return new Promise(e=>{setTimeout(()=>{e(p({},this.kpiData))},100)})}getChartData(){return new Promise(e=>{setTimeout(()=>{e(p({},this.chartData))},200)})}updateData(){this.kpiData.totalAmount+=Math.floor(Math.random()*1e4)-5e3,this.kpiData.premiumAmount+=Math.floor(Math.random()*100)-50,this.kpiData.premiumRate+=(Math.random()-.5)*2,this.kpiData.totalTargets+=Math.floor(Math.random()*10)-5,this.kpiData.totalAmount=Math.max(0,this.kpiData.totalAmount),this.kpiData.premiumAmount=Math.max(0,this.kpiData.premiumAmount),this.kpiData.premiumRate=Math.max(0,Math.min(100,this.kpiData.premiumRate)),this.kpiData.totalTargets=Math.max(0,this.kpiData.totalTargets),this.chartData.trendData=this.chartData.trendData.map(e=>b(p({},e),{value:Math.max(50,Math.min(100,e.value+(Math.random()-.5)*10))})),this.chartData.premiumAmountRanking=this.chartData.premiumAmountRanking.map(e=>b(p({},e),{value:Math.max(100,e.value+Math.floor(Math.random()*200)-100)}))}startAutoUpdate(e=3e4){return setInterval(()=>{this.updateData()},e)}stopAutoUpdate(e){clearInterval(e)}getRealtimeData(e){const t=setInterval(()=>f(this,null,function*(){this.updateData();const n=yield this.getKPIData(),r=yield this.getChartData();e({kpi:n,chart:r})}),5e3);return()=>clearInterval(t)}formatNumber(e,t=0){return e.toLocaleString("zh-CN",{minimumFractionDigits:t,maximumFractionDigits:t})}formatPercentage(e,t=2){return`${e.toFixed(t)}%`}formatAmount(e,t="万元"){return`${this.formatNumber(e)}${t}`}}const oe=new ie;function A(){if("memory"in performance){const a=performance.memory;return{used:a.usedJSHeapSize,total:a.totalJSHeapSize,percentage:a.usedJSHeapSize/a.totalJSHeapSize*100}}return null}class le{constructor(){m(this,"marks",new Map);m(this,"measures",new Map)}mark(e){this.marks.set(e,performance.now()),performance.mark(e)}measure(e,t,n){if(n)performance.measure(e,t,n);else{const s=this.marks.get(t);if(s){const u=performance.now()-s;return this.measures.set(e,u),u}}const r=performance.getEntriesByName(e,"measure");if(r.length>0){const s=r[r.length-1].duration;return this.measures.set(e,s),s}return 0}getAllMeasures(){return new Map(this.measures)}clear(){this.marks.clear(),this.measures.clear(),performance.clearMarks(),performance.clearMeasures()}getReport(){return{marks:Array.from(this.marks.keys()),measures:Array.from(this.measures.entries()).map(([e,t])=>({name:e,duration:t})),memory:A()}}}class ue{constructor(){m(this,"loadedImages",new Map);m(this,"loadingPromises",new Map)}preloadImage(e){if(this.loadedImages.has(e))return Promise.resolve(this.loadedImages.get(e));if(this.loadingPromises.has(e))return this.loadingPromises.get(e);const t=new Promise((n,r)=>{const s=new Image;s.onload=()=>{this.loadedImages.set(e,s),this.loadingPromises.delete(e),n(s)},s.onerror=()=>{this.loadingPromises.delete(e),r(new Error(`Failed to load image: ${e}`))},s.src=e});return this.loadingPromises.set(e,t),t}preloadImages(e){return f(this,null,function*(){const t=e.map(n=>this.preloadImage(n));return Promise.all(t)})}getLoadedImage(e){return this.loadedImages.get(e)||null}clear(){this.loadedImages.clear(),this.loadingPromises.clear()}}class ce{constructor(){m(this,"resources",new Map);m(this,"cleanupFunctions",new Map)}register(e,t,n){this.resources.set(e,t),n&&this.cleanupFunctions.set(e,n)}get(e){return this.resources.get(e)||null}release(e){const t=this.cleanupFunctions.get(e);t&&(t(),this.cleanupFunctions.delete(e)),this.resources.delete(e)}releaseAll(){for(const[e]of this.resources)this.release(e)}getUsage(){return{resourceCount:this.resources.size,cleanupCount:this.cleanupFunctions.size,keys:Array.from(this.resources.keys())}}}const k=new le,C=new ue,Be=new ce;function me(){const a=[],e=A(),t=window.devicePixelRatio||1,n=navigator.hardwareConcurrency||4;let r=!0;return t>2&&(a.push("高分辨率屏幕，建议降低图表渲染质量"),r=!1),n<4&&(a.push("CPU核心数较少，建议减少动画效果"),r=!1),e&&e.percentage>80&&(a.push("内存使用率较高，建议减少数据缓存"),r=!1),{isHighPerformance:r,devicePixelRatio:t,hardwareConcurrency:n,memory:e,recommendations:a}}function de(){const{isHighPerformance:a,devicePixelRatio:e,hardwareConcurrency:t}=me();return{enableAnimations:a&&t>=4,chartRenderMode:e>1.5?"canvas":"svg",updateInterval:a?1e3:3e3,batchSize:Math.max(5,Math.min(20,t*2)),cacheSize:a?100:50}}const he={class:"visual-content"},ve={class:"visual-header"},pe={class:"visual-middle"},ge={class:"visual-left"},fe={class:"visual-center"},ke={class:"center-top"},we={class:"center-bottom"},be={class:"visual-right"},ye={class:"visual-footer"},De=B({__name:"index",setup(a){const e=U(),t=R(!1),n=R(null),r=de();P("dataService",oe),P("optimizationConfig",r);const s=()=>{const i=document.documentElement;i.requestFullscreen?i.requestFullscreen():i.webkitRequestFullscreen?i.webkitRequestFullscreen():i.msRequestFullscreen&&i.msRequestFullscreen(),t.value=!0},u=()=>{document.exitFullscreen?document.exitFullscreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.msExitFullscreen&&document.msExitFullscreen(),t.value=!1},d=i=>{const l=document.querySelector(".visual-container");i?l&&l.classList.add("fullscreen"):l&&l.classList.remove("fullscreen")},o=()=>{const i=!!(document.fullscreenElement||document.webkitFullscreenElement||document.msFullscreenElement);t.value=i,d(i);const l=new URLSearchParams(window.location.search);!i&&l.get("fullscreen")==="true"&&e.push("/dashboard/analysis")},h=i=>{i.key==="Escape"&&t.value?u():i.key==="F11"&&(i.preventDefault(),t.value?u():s())},{debouncedUpdateScale:E}=re(),g=()=>{setTimeout(()=>{E()},50)},S=(i,l)=>{},I=(i,l)=>{};return N(()=>f(null,null,function*(){k.mark("visual-init-start");try{yield C.preloadImages(["/src/assets/visual/visual-bg.png","/src/assets/visual/header.png","/src/assets/visual/left-bg.png","/src/assets/visual/center-bg.png","/src/assets/visual/right-bg.png","/src/assets/visual/bottom-bg.png","/src/assets/visual/head-card.png","/src/assets/visual/address-card.png"])}catch(_){}new URLSearchParams(window.location.search).get("fullscreen")==="true"&&s(),document.addEventListener("fullscreenchange",o),document.addEventListener("webkitfullscreenchange",o),document.addEventListener("msfullscreenchange",o),document.addEventListener("keydown",h),window.addEventListener("resize",g);const l=document.querySelector(".visual-container");l&&window.ResizeObserver&&(n.value=new ResizeObserver(()=>{g()}),n.value.observe(l)),setTimeout(()=>{g(),k.mark("visual-init-end");const _=k.measure("visual-init","visual-init-start","visual-init-end")},100)})),J(()=>{document.removeEventListener("fullscreenchange",o),document.removeEventListener("webkitfullscreenchange",o),document.removeEventListener("msfullscreenchange",o),document.removeEventListener("keydown",h),window.removeEventListener("resize",g),n.value&&(n.value.disconnect(),n.value=null),k.clear(),C.clear()}),(i,l)=>(O(),G("div",{class:$(["visual-container",{fullscreen:t.value}])},[l[1]||(l[1]=c("div",{class:"visual-bg"},null,-1)),c("div",he,[c("div",ve,[v(K,{onMaterialChange:S,onAreaChange:I})]),c("div",pe,[c("div",ge,[v(V)]),c("div",fe,[c("div",null,[c("div",ke,[v(W)]),c("div",we,[v(X)]),l[0]||(l[0]=c("div",{class:"center-bg"},null,-1))])]),c("div",be,[v(j)])]),c("div",ye,[v(Y)])])],2))}}),Ue=Q(De,[["__scopeId","data-v-191833be"]]);export{Ue as default};
