var te=Object.defineProperty,re=Object.defineProperties;var oe=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var ne=Object.prototype.hasOwnProperty,se=Object.prototype.propertyIsEnumerable;var M=(s,o,n)=>o in s?te(s,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):s[o]=n,d=(s,o)=>{for(var n in o||(o={}))ne.call(o,n)&&M(s,n,o[n]);if(U)for(var n of U(o))se.call(o,n)&&M(s,n,o[n]);return s},w=(s,o)=>re(s,oe(o));var T=(s,o,n)=>new Promise((O,P)=>{var x=i=>{try{c(n.next(i))}catch(y){P(y)}},h=i=>{try{c(n.throw(i))}catch(y){P(y)}},c=i=>i.done?O(i.value):Promise.resolve(i.value).then(x,h);c((n=n.apply(s,o)).next())});import{d as X,f as E,ag as ae,aq as g,ar as f,k as v,aD as m,au as I,u as p,G as q}from"./vue-vendor-dy9k-Yad.js";import{u as ie}from"./index-BkGZ5fiW.js";import{u as V,j as K,aX as j,E as ue,a as ce}from"./index-CCWaWN5g.js";import{D as le}from"./index-CyU3vcHV.js";import{Q as de}from"./componentMap-Bkie1n3v.js";import me from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFilePreview-CazplhRu.js";import"./SupplyDemand-DK00S9Ao.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const{createConfirm:Rt}=V();const pe=s=>K.post({url:"/hgy/entrustService/hgyProcurement/queryPageAll",params:s});const he=s=>K.delete({url:"/hgy/entrustService/hgyProcurement/customProcurementDelete",params:s},{joinParamsToUrl:!0});const ye={class:"p-4"},ge={key:0},fe={key:1},ve={key:0},Pe={key:1},xe=X({name:"ProcurementInfo"}),Te=X(w(d({},xe),{setup(s){const{createMessage:o}=V(),{handleExportXls:n}=ue(),O=[{title:"序号",dataIndex:"index",width:60,customRender:({index:e})=>e+1},{title:"服务单ID",dataIndex:"entrustOrderId",width:160,customRender:({record:e})=>{var t;return((t=e.hgyEntrustOrder)==null?void 0:t.id)||"-"}},{title:"公告名称",dataIndex:"noticeName",width:200,ellipsis:!0,customRender:({record:e})=>{var t;return((t=e.hgyProcurement)==null?void 0:t.noticeName)||"-"}},{title:"省份",dataIndex:"province",width:100,customRender:({record:e})=>{var t;return((t=e.hgyProcurement)==null?void 0:t.province)||"-"}},{title:"城市",dataIndex:"city",width:100,customRender:({record:e})=>{var t;return((t=e.hgyProcurement)==null?void 0:t.city)||"-"}},{title:"区县",dataIndex:"district",width:100,customRender:({record:e})=>{var t;return((t=e.hgyProcurement)==null?void 0:t.district)||"-"}},{title:"详细地址",dataIndex:"address",width:200,ellipsis:!0,customRender:({record:e})=>{var t;return((t=e.hgyProcurement)==null?void 0:t.address)||"-"}},{title:"特殊说明",dataIndex:"specialNotes",width:150,ellipsis:!0,customRender:({record:e})=>{var t;return((t=e.hgyProcurement)==null?void 0:t.specialNotes)||"-"}},{title:"审核状态",dataIndex:"status",width:100,slots:{customRender:"status"}},{title:"委托方式",dataIndex:"entrustType",width:100,slots:{customRender:"entrustType"}},{title:"创建时间",dataIndex:"createTime",width:150,slots:{customRender:"createTime"}},{title:"更新时间",dataIndex:"updateTime",width:150,slots:{customRender:"updateTime"}},{title:"创建人",dataIndex:"createBy",width:100,customRender:({record:e})=>{var t;return((t=e.hgyEntrustOrder)==null?void 0:t.createBy)||"-"}},{title:"更新人",dataIndex:"updateBy",width:100,customRender:({record:e})=>{var t;return((t=e.hgyEntrustOrder)==null?void 0:t.updateBy)||"-"}}],P=[{key:"all",label:"全部采购",icon:""},{key:"draft",label:"未审核",icon:""},{key:"pending",label:"审核中",icon:""},{key:"approved",label:"已通过",icon:""},{key:"rejected",label:"未通过",icon:""}],x=E("all"),h=E({}),c=E(!1),i=E(null);function y(e){x.value=e;let t={};switch(e){case"draft":t={status:1};break;case"pending":t={status:2};break;case"approved":t={status:3};break;case"rejected":t={status:4};break;default:t={}}h.value=t,_()}function z(e){return T(this,null,function*(){const t={column:"createTime",order:"desc"},u=d(d(d({},e),t),h.value);return pe(u)})}function F(){return T(this,null,function*(){try{let e={};try{e=yield Q().validate()}catch(r){}const t=w(d(d({},h.value),e),{column:"createTime",order:"desc"});yield n("采购信息列表","/hgy/entrustService/hgyProcurement/customProcurementExportXls",t),o.success("导出成功")}catch(e){o.error("导出失败")}})}const[H,{reload:_,getForm:Q}]=ie({api:z,columns:O,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,showNavigation:!0,navigationItems:P,activeNavigationKey:x.value,showExportButton:!0,inset:!0,maxHeight:478,actionColumn:{width:220,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"},formConfig:{labelWidth:64,size:"large",schemas:[{field:"hgyEntrustOrder.id",label:"服务单ID",component:"Input",colProps:{span:6}},{field:"hgyProcurement.noticeName",label:"公告名称",component:"Input",colProps:{span:6}},{field:"hgyProcurement.province",label:"省份",component:"Input",colProps:{span:6}}]}});function G(e){return[{label:"查看详情",onClick:A.bind(null,e)},{label:"删除",color:"error",popConfirm:{title:"确认删除该采购信息吗？",confirm:L.bind(null,e)}}]}function W(e){return[]}function A(e){var u,r,a,l,b,S,R,k,D,B,C,N;const t={id:((u=e.hgyEntrustOrder)==null?void 0:u.id)||e.entrustOrderId,entrustType:((r=e.hgyEntrustOrder)==null?void 0:r.entrustType)||1,serviceType:3,status:((a=e.hgyEntrustOrder)==null?void 0:a.status)||1,projectName:((l=e.hgyProcurement)==null?void 0:l.noticeName)||"-",relationUser:((b=e.hgyEntrustOrder)==null?void 0:b.relationUser)||((S=e.hgyEntrustOrder)==null?void 0:S.createBy)||"-",relationPhone:((R=e.hgyEntrustOrder)==null?void 0:R.relationPhone)||"-",applicantUser:((k=e.hgyEntrustOrder)==null?void 0:k.createBy)||"-",auditUser:((D=e.hgyEntrustOrder)==null?void 0:D.auditUser)||"-",submitTime:((B=e.hgyEntrustOrder)==null?void 0:B.createTime)||"-",auditTime:((C=e.hgyEntrustOrder)==null?void 0:C.auditTime)||((N=e.hgyEntrustOrder)==null?void 0:N.updateTime)||"-"};i.value=t,c.value=!0}function J(){c.value=!1,i.value=null}function L(e){return T(this,null,function*(){var t;try{yield he({id:((t=e.hgyEntrustOrder)==null?void 0:t.id)||e.entrustOrderId}),_()}catch(u){}})}function Y(e){return{1:"草稿",2:"待审核",3:"审核通过",4:"审核拒绝"}[e]||"未知"}function Z(e){return{2:"processing",3:"success",4:"error"}[e]||"default"}function $(e){return{1:"自主",2:"增值"}[e]||"未知"}function ee(e){return{1:"blue",2:"orange"}[e]||"default"}return(e,t)=>{const u=ae("a-tag");return f(),g("div",ye,[v(p(me),{onRegister:p(H),onNavigationChange:y,onExport:F},{action:m(({record:r})=>[v(p(de),{actions:G(r),dropDownActions:W(r)},null,8,["actions","dropDownActions"])]),status:m(({record:r})=>{var a;return[v(u,{color:Z((a=r.hgyEntrustOrder)==null?void 0:a.status)},{default:m(()=>{var l;return[q(I(Y((l=r.hgyEntrustOrder)==null?void 0:l.status)),1)]}),_:2},1032,["color"])]}),entrustType:m(({record:r})=>{var a;return[v(u,{color:ee((a=r.hgyEntrustOrder)==null?void 0:a.entrustType)},{default:m(()=>{var l;return[q(I($((l=r.hgyEntrustOrder)==null?void 0:l.entrustType)),1)]}),_:2},1032,["color"])]}),createTime:m(({record:r})=>{var a;return[(a=r.hgyEntrustOrder)!=null&&a.createTime?(f(),g("span",ge,I(p(j)(r.hgyEntrustOrder.createTime)),1)):(f(),g("span",fe,"-"))]}),updateTime:m(({record:r})=>{var a;return[(a=r.hgyEntrustOrder)!=null&&a.updateTime?(f(),g("span",ve,I(p(j)(r.hgyEntrustOrder.updateTime)),1)):(f(),g("span",Pe,"-"))]}),_:1},8,["onRegister"]),v(p(le),{open:c.value,"onUpdate:open":t[0]||(t[0]=r=>c.value=r),record:i.value,"entrust-type":1,"service-type":3,onClose:J},null,8,["open","record"])])}}})),kt=ce(Te,[["__scopeId","data-v-4b3dfe01"]]);export{kt as default};
