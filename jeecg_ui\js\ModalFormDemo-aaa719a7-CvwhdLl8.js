import{d as s,ag as n,aq as a,ar as l,k as c}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{cs as d,j as u}from"./index-CCWaWN5g.js";import{p as f}from"./antd-vue-vendor-me9YkNVC.js";import{u as g}from"./useForm-CgkFTrrO.js";import{B as b}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const h=()=>[{field:"name",component:"Input",label:"名称"},{field:"age",component:"InputNumber",label:"年龄",componentProps:{style:{width:"100%"}}},{field:"sex",label:"性别",component:"JDictSelectTag",componentProps:{dictCode:"sex"}},{field:"birthday",component:"DatePicker",label:"生日",componentProps:{valueFormat:"YYYY-MM-DD",style:{width:"100%"}}},{field:"email",component:"Input",label:"邮箱"}],F=s({components:{BasicForm:b},props:["id"],setup(o){const[r,{setFieldsValue:m}]=g({schemas:h(),showActionButtonGroup:!1,baseColProps:{span:24}});let i={id:o.id};return u.get({url:"/test/jeecgDemo/queryById",params:i},{isTransformResponse:!1}).then(t=>{if(t.success){let e=f(t.result,"name","age","birthday","sex","email");m(e)}}),{registerForm:r}}}),B={style:{margin:"50px auto",width:"800px"}};function x(o,r,m,i,t,e){const p=n("BasicForm");return l(),a("div",B,[c(p,{onRegister:o.registerForm},null,8,["onRegister"])])}const ko=d(F,[["render",x]]);export{ko as default};
