var w=Object.defineProperty;var u=Object.getOwnPropertySymbols;var B=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var f=(o,r,t)=>r in o?w(o,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[r]=t,_=(o,r)=>{for(var t in r||(r={}))B.call(r,t)&&f(o,t,r[t]);if(u)for(var t of u(r))C.call(r,t)&&f(o,t,r[t]);return o};var d=(o,r,t)=>new Promise((n,a)=>{var p=e=>{try{m(t.next(e))}catch(s){a(s)}},i=e=>{try{m(t.throw(e))}catch(s){a(s)}},m=e=>e.done?n(e.value):Promise.resolve(e.value).then(p,i);m((t=t.apply(o,r)).next())});import"./index-L3cSIXth.js";import{d as D,e as k,ag as y,aq as F,ar as b,k as g,ah as v,aD as V,G as x}from"./vue-vendor-dy9k-Yad.js";import{H as h,j as $,a as j}from"./index-CCWaWN5g.js";import{i as q,m as I}from"./YpwMallCate.api-BlqWuhnE.js";import{B as M}from"./BasicForm-DBcXiHk0.js";import{u as N}from"./useForm-CgkFTrrO.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./renderUtils-D7XVOFwj.js";const G=D({name:"YpwMallCateForm",components:{BasicForm:M},props:{formData:h.object.def({}),formBpm:h.bool.def(!0)},setup(o){const[r,{setFieldsValue:t,setProps:n,getFieldsValue:a}]=N({labelWidth:150,schemas:I(o.formData),showActionButtonGroup:!1,baseColProps:{span:24}}),p=k(()=>o.formData.disabled!==!1);let i={};const m="/ypw/ypwMallCate/queryById";function e(){return d(this,null,function*(){let l={id:o.formData.dataId};const c=yield $.get({url:m,params:l});i=_({},c),yield t(i),yield n({disabled:p.value})})}function s(){return d(this,null,function*(){let l=a(),c=Object.assign({},i,l);yield q(c,!0)})}return e(),{registerForm:r,formDisabled:p,submitForm:s}}}),H={style:{"min-height":"400px"}},O={key:0,style:{width:"100%","text-align":"center"}};function P(o,r,t,n,a,p){const i=y("BasicForm"),m=y("a-button");return b(),F("div",H,[g(i,{onRegister:o.registerForm},null,8,["onRegister"]),o.formDisabled?v("",!0):(b(),F("div",O,[g(m,{onClick:o.submitForm,"pre-icon":"ant-design:check",type:"primary"},{default:V(()=>r[0]||(r[0]=[x("提 交")])),_:1,__:[0]},8,["onClick"])]))])}const Yt=j(G,[["render",P]]);export{Yt as default};
