import{d as l,e as o,aq as n,ar as c,k as d,at as e,aT as i,u as a,au as m}from"./vue-vendor-dy9k-Yad.js";import{a3 as x}from"./antd-vue-vendor-me9YkNVC.js";import{ah as f}from"./index-CCWaWN5g.js";import{h as p}from"./header-OZa5fSDc.js";import"./vxe-table-vendor-B22HppNm.js";const u={class:"lg:flex"},v={class:"md:ml-6 flex flex-col justify-center md:mt-0 mt-2"},y={class:"md:text-lg text-md"},N=l({__name:"WorkbenchHeader",setup(_){const r=f(),t=o(()=>r.getUserInfo);return(g,s)=>(c(),n("div",u,[d(a(x),{src:t.value.avatar||a(p),size:72,class:"!mx-auto !block"},null,8,["src"]),e("div",v,[e("h1",y,"早安, "+m(t.value.realname)+", 开始您一天的工作吧！",1),s[0]||(s[0]=e("span",{class:"text-secondary"}," 今日晴，20℃ - 32℃！ ",-1))]),s[1]||(s[1]=i('<div class="flex flex-1 justify-end md:mt-0 mt-4"><div class="flex flex-col justify-center text-right"><span class="text-secondary"> 待办 </span><span class="text-2xl">2/10</span></div><div class="flex flex-col justify-center text-right md:mx-16 mx-12"><span class="text-secondary"> 项目 </span><span class="text-2xl">8</span></div><div class="flex flex-col justify-center text-right md:mr-10 mr-4"><span class="text-secondary"> 团队 </span><span class="text-2xl">300</span></div></div>',1))]))}});export{N as default};
