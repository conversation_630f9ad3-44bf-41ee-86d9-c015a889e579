import{d as W,ag as D,v as M,aB as X,ar as L,aD as Y,q,aq as S,G as A}from"./vue-vendor-dy9k-Yad.js";import{P as G}from"./index-CtJ0w2CP.js";import{a as H}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const l={event:"mousedown",transition:400},b={beforeMount:(e,t)=>{if(t.value===!1)return;const o=e.getAttribute("ripple-background");V(Object.keys(t.modifiers),l);const s=o||b.background,r=b.zIndex;e.addEventListener(l.event,d=>{I({event:d,el:e,background:s,zIndex:r})})},updated(e,t){var s,r;if(!t.value){(s=e==null?void 0:e.clearRipple)==null||s.call(e);return}const o=e.getAttribute("ripple-background");(r=e==null?void 0:e.setBackground)==null||r.call(e,o)}};function I({event:e,el:t,zIndex:o,background:s}){var E,R;const r=parseInt(getComputedStyle(t).borderWidth.replace("px","")),d=e.clientX||e.touches[0].clientX,f=e.clientY||e.touches[0].clientY,g=t.getBoundingClientRect(),{left:C,top:T}=g,{offsetWidth:x,offsetHeight:y}=t,{transition:B}=l,m=d-C,u=f-T,_=Math.max(m,x-m),$=Math.max(u,y-u),N=window.getComputedStyle(t),h=Math.sqrt(_*_+$*$),w=r>0?r:0,a=document.createElement("div"),n=document.createElement("div");a.className="ripple",Object.assign((E=a.style)!=null?E:{},{marginTop:"0px",marginLeft:"0px",width:"1px",height:"1px",transition:`all ${B}ms cubic-bezier(0.4, 0, 0.2, 1)`,borderRadius:"50%",pointerEvents:"none",position:"relative",zIndex:o!=null?o:"9999",backgroundColor:s!=null?s:"rgba(0, 0, 0, 0.12)"}),n.className="ripple-container",Object.assign((R=n.style)!=null?R:{},{position:"absolute",left:`${0-w}px`,top:`${0-w}px`,height:"0",width:"0",pointerEvents:"none",overflow:"hidden"});const v=t.style.position.length>0?t.style.position:getComputedStyle(t).position;v!=="relative"&&(t.style.position="relative"),n.appendChild(a),t.appendChild(n),Object.assign(a.style,{marginTop:`${u}px`,marginLeft:`${m}px`});const{borderTopLeftRadius:k,borderTopRightRadius:P,borderBottomLeftRadius:j,borderBottomRightRadius:O}=N;Object.assign(n.style,{width:`${x}px`,height:`${y}px`,direction:"ltr",borderTopLeftRadius:k,borderTopRightRadius:P,borderBottomLeftRadius:j,borderBottomRightRadius:O}),setTimeout(()=>{var c;const i=`${h*2}px`;Object.assign((c=a.style)!=null?c:{},{width:i,height:i,marginLeft:`${m-h}px`,marginTop:`${u-h}px`})},0);function p(){setTimeout(()=>{a.style.backgroundColor="rgba(0, 0, 0, 0)"},250),setTimeout(()=>{var i;(i=n==null?void 0:n.parentNode)==null||i.removeChild(n)},850),t.removeEventListener("mouseup",p,!1),t.removeEventListener("mouseleave",p,!1),t.removeEventListener("dragstart",p,!1),setTimeout(()=>{let i=!0;for(let c=0;c<t.childNodes.length;c++)t.childNodes[c].className==="ripple-container"&&(i=!1);i&&(t.style.position=v!=="static"?v:"")},l.transition+260)}e.type==="mousedown"?(t.addEventListener("mouseup",p,!1),t.addEventListener("mouseleave",p,!1),t.addEventListener("dragstart",p,!1)):p(),t.setBackground=i=>{i&&(a.style.backgroundColor=i)}}function V(e,t){e.forEach(o=>{isNaN(Number(o))?t.event=o:t.transition=o})}const z=W({components:{PageWrapper:G},directives:{Ripple:b}}),F={class:"demo-box"};function J(e,t,o,s,r,d){const f=D("PageWrapper"),g=M("ripple");return L(),X(f,{title:"Ripple示例"},{default:Y(()=>[q((L(),S("div",F,t[0]||(t[0]=[A(" content ")]))),[[g]])]),_:1})}const rt=H(z,[["render",J],["__scopeId","data-v-8eaede1b"]]);export{rt as default};
