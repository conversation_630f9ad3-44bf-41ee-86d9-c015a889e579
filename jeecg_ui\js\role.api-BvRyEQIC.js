import{j as s}from"./index-CCWaWN5g.js";import{M as n}from"./antd-vue-vendor-me9YkNVC.js";const i="/sys/role/exportXls",u="/sys/role/importExcel",y=e=>s.get({url:"/sys/role/list",params:e}),c=e=>s.get({url:"/sys/role/listByTenant",params:e}),R=(e,o)=>s.delete({url:"/sys/role/delete",params:e},{joinParamsToUrl:!0}).then(()=>{o()}),m=(e,o)=>{n.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>s.delete({url:"/sys/role/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{o()})})},g=(e,o)=>{let t=o?"/sys/role/edit":"/sys/role/add";return s.post({url:t,params:e})};let l;const x=e=>new Promise((o,t)=>{clearTimeout(l),l=setTimeout(()=>{s.get({url:"/sys/role/checkRoleCode",params:e},{isTransformResponse:!1}).then(r=>{o(r)}).catch(r=>{t(r)})},500)}),D=()=>s.get({url:"/sys/role/queryTreeList"}),T=e=>s.get({url:"/sys/permission/queryRolePermission",params:e}),U=e=>s.post({url:"/sys/permission/saveRolePermission",params:e}),h=e=>s.get({url:`/sys/role/datarule/${e.functionId}/${e.roleId}`},{isTransformResponse:!1}),f=e=>s.post({url:"/sys/role/datarule",params:e});const q=e=>s.get({url:"/sys/user/userRoleList",params:e}),P=(e,o)=>s.delete({url:"/sys/user/deleteUserRole",params:e},{joinParamsToUrl:!0}).then(()=>{o()}),I=(e,o)=>{n.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>s.delete({url:"/sys/user/deleteUserRoleBatch",params:e},{joinParamsToUrl:!0}).then(()=>{o()})})},L=(e,o)=>s.post({url:"/sys/user/addSysUserRole",params:e}).then(()=>{o()}),B=(e,o)=>{let t=o?"/sys/sysRoleIndex/edit":"/sys/sysRoleIndex/add";return s.post({url:t,params:e})},v=e=>s.get({url:"/sys/sysRoleIndex/queryByCode",params:e},{isTransformResponse:!1});export{i as a,m as b,P as c,R as d,I as e,L as f,u as g,f as h,x as i,v as j,B as k,c as l,D as m,T as n,U as o,y as p,h as q,g as s,q as u};
