var M=(U,_,b)=>new Promise((y,a)=>{var j=v=>{try{g(b.next(v))}catch(p){a(p)}},x=v=>{try{g(b.throw(v))}catch(p){a(p)}},g=v=>v.done?y(v.value):Promise.resolve(v.value).then(j,x);g((b=b.apply(U,_)).next())});import{d as B,r as D,f as z,ag as f,aB as V,ar as L,aD as u,k as e,aE as i,u as t,n as X}from"./vue-vendor-dy9k-Yad.js";import{ak as Z,u as G,e as K,j as Q,a as h}from"./index-CCWaWN5g.js";import{a5 as c,F as ee}from"./antd-vue-vendor-me9YkNVC.js";import{_ as F,h as le,f as ae,b as oe,e as ue,t as re,c as pe,d as se,o as te,J as ne,a as de,i as ie,g as me,j as ve}from"./JAddInput-CxJ-JBK-.js";import{_ as fe}from"./JUpload-CRos0F1P.js";import{J as Ce}from"./JSearchSelect-c_lfTydU.js";import{u as be}from"./JPopup-CeU6ry6r.js";import we from"./JMarkdownEditor-CxtN1OHq.js";import{a as ge}from"./JSelectDept-I-NqkbOH.js";import{l as ze}from"./JSelectUser-COkExGbu.js";import{i as je}from"./JAreaSelect-Db7Nhhc_.js";import xe from"./JAreaLinkage-DFCdF3cr.js";import _e from"./JCodeEditor-B-WXz11X.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./useFormItem-CHvpjy4o.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./index-mbACBRQ9.js";import"./areaDataUtil-BXVjRArW.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */const ye=B({__name:"OneNativeForm",emits:["register","ok"],setup(U,{expose:_,emit:b}){const y=b,a=D({name:"",miMa:"",ywzz:"",xiala:"",danxuan:"",duoxuan:"",riqi:"",shijian:"",wenjian:"",tupian:"",dhwb:"",xlss:"",popup:"",flzds:"",yhxz:"",fwb:"",shq:"",ldzje:"",ldzjs:"",zddtjxl:"",yongHu:"",zhiWu:"",jueSe:"",zdys:"",jssq:"",zdbxl:"",zdmrz:"",jsonParam:"",bmxz:"",yuanjia:"",nyrsfm:""}),{createMessage:j}=G(),x=z(),g=c.useForm,v=D({duplicateCheck:"/sys/duplicate/check",add:"/test/jeecgDemo/oneNative/add",edit:"/test/jeecgDemo/oneNative/edit"}),p=z({xs:{span:24},sm:{span:5}}),n=z({xs:{span:24},sm:{span:16}}),q=z(!1),k={name:[{required:!1,message:"请输入文本!"}],miMa:[{required:!1,message:"请输入密码!"}],ywzz:[{required:!1},{pattern:"^[a-z|A-Z]{2,10}$",message:"不符合校验规则!"}],xiala:[{required:!1,message:"请选择下拉组件!"}],danxuan:[{required:!1,message:"请选择单选组件!"}],duoxuan:[{required:!1,message:"请选择多选组件!"}],riqi:[{required:!1,message:"请选择日期!"}],shijian:[{required:!1,message:"请选择时间!"}],wenjian:[{required:!1,message:"请上传文件!"}],tupian:[{required:!1,message:"请上传图片!"}],dhwb:[{required:!1,message:"请填写多行文本!"}],xlss:[{required:!1,message:"请选择字典下拉搜索!"}],popup:[{required:!1,message:"请选择popup弹窗!"}],flzds:[{required:!1,message:"请选择分类字典树!"}],yhxz:[{required:!1,message:"请选择用户!"}],fwb:[{required:!1,message:"请填写富文本!"}],shq:[{required:!1,message:"请选择省市级!"}],ldzje:[{required:!1,message:"请输入JInputPop!"}],ldzjs:[{required:!1,message:"请选择下拉输入框!"}],zddtjxl:[{required:!1,message:"请选择多选输入框!"}],yongHu:[{required:!1,message:"请选择用户!"}],zhiWu:[{required:!1,message:"请选择职务!"}],jueSe:[{required:!1,message:"请选择角色!"}],zdys:[{required:!1,message:"请选择自定义树!"}],jssq:[{required:!1,message:"请选择三级联动!"}],zdbxl:[{required:!1,message:"请选择JTreeDict!"}],zdmrz:[{required:!1,message:"请输入JCodeEditor!"}],jsonParam:[{required:!1,message:"请输入参数!"}],bmxz:[{required:!1,message:"请选择部门!"}],yuanjia:[{required:!1,message:"请输入数值!"}],nyrsfm:[{required:!1,message:"请选择年月日时分秒!"}]},{resetFields:P,validate:Y,validateInfos:d}=g(a,k,{immediate:!1}),I=z([{label:"男",value:"1"},{label:"女",value:"2"}]);function T(){J({})}function J(m){X(()=>{P(),Object.assign(a,m)})}function H(){return M(this,null,function*(){yield Y(),q.value=!0;let m="",l="",w=a;w.id?(m+=v.edit,l="put"):(m+=v.add,l="post");for(let r in a)a[r]instanceof Array&&K(x.value.getProps,r)==="string"&&(a[r]=a[r].join(","));Q.request({url:m,params:w,method:l},{isTransformResponse:!1}).then(r=>{r.success?(j.success(r.message),y("ok")):j.warning(r.message)}).finally(()=>{q.value=!1})})}function qe(m){Object.assign(a,m)}function E(m){Object.keys(m).map(l=>{a[l]=m[l]})}function C(m,l){a[m]=l}return _({add:T,edit:J,submitForm:H}),(m,l)=>{const w=f("a-input"),r=f("a-form-item"),s=f("a-col"),O=f("a-input-password"),S=f("a-date-picker"),W=f("a-textarea"),$=f("a-input-number"),A=f("a-row"),N=f("a-form"),R=f("a-spin");return L(),V(R,{spinning:q.value},{default:u(()=>[e(N,{class:"antd-modal-form",ref_key:"formRef",ref:x,model:a,rules:k},{default:u(()=>[e(A,null,{default:u(()=>[e(s,{span:24},{default:u(()=>[e(r,i({label:"文本",labelCol:p.value,wrapperCol:n.value},t(d).name),{default:u(()=>[e(w,{value:a.name,"onUpdate:value":l[0]||(l[0]=o=>a.name=o),placeholder:"请输入文本"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"密码",labelCol:p.value,wrapperCol:n.value},t(d).miMa),{default:u(()=>[e(O,{value:a.miMa,"onUpdate:value":l[1]||(l[1]=o=>a.miMa=o),placeholder:"请输入密码"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"字典下拉",labelCol:p.value,wrapperCol:n.value},t(d).xiala),{default:u(()=>[e(F,{type:"select",value:a.xiala,"onUpdate:value":l[2]||(l[2]=o=>a.xiala=o),dictCode:"sex",placeholder:"请选择字典下拉"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"字典单选",labelCol:p.value,wrapperCol:n.value},t(d).danxuan),{default:u(()=>[e(F,{type:"radio",value:a.danxuan,"onUpdate:value":l[3]||(l[3]=o=>a.danxuan=o),dictCode:"sex",placeholder:"请选择字典单选"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"字典多选",labelCol:p.value,wrapperCol:n.value},t(d).duoxuan),{default:u(()=>[e(le,{value:a.duoxuan,"onUpdate:value":l[4]||(l[4]=o=>a.duoxuan=o),dictCode:"urgent_level",placeholder:"请选择字典多选"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"开关",labelCol:p.value,wrapperCol:n.value},t(d).kaiguan),{default:u(()=>[e(ae,{value:a.kaiguan,"onUpdate:value":l[5]||(l[5]=o=>a.kaiguan=o),options:["1","0"]},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"日期",labelCol:p.value,wrapperCol:n.value},t(d).riqi),{default:u(()=>[e(S,{placeholder:"请选择日期",format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",value:a.riqi,"onUpdate:value":l[6]||(l[6]=o=>a.riqi=o),style:{width:"100%"}},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"年月日时分秒",labelCol:p.value,wrapperCol:n.value},t(d).nyrsfm),{default:u(()=>[e(S,{"show-time":"",value:a.nyrsfm,"onUpdate:value":l[7]||(l[7]=o=>a.nyrsfm=o),style:{width:"100%"},valueFormat:"YYYY-MM-DD HH:mm:ss"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"时间",labelCol:p.value,wrapperCol:n.value},t(d).shijian),{default:u(()=>[e(t(ee),{placeholder:"请选择时间",value:a.shijian,"onUpdate:value":l[8]||(l[8]=o=>a.shijian=o),style:{width:"100%"}},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"文件",labelCol:p.value,wrapperCol:n.value},t(d).wenjian),{default:u(()=>[e(fe,{value:a.wenjian,"onUpdate:value":l[9]||(l[9]=o=>a.wenjian=o)},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"图片",labelCol:p.value,wrapperCol:n.value},t(d).tupian),{default:u(()=>[e(oe,{fileMax:2,value:a.tupian,"onUpdate:value":l[10]||(l[10]=o=>a.tupian=o)},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"多行文本框",labelCol:p.value,wrapperCol:n.value},t(d).dhwb),{default:u(()=>[e(W,{value:a.dhwb,"onUpdate:value":l[11]||(l[11]=o=>a.dhwb=o),rows:"4",placeholder:"请输入多行文本框"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"字典表下拉搜索框",labelCol:p.value,wrapperCol:n.value},t(d).xlss),{default:u(()=>[e(Ce,{value:a.xlss,"onUpdate:value":l[12]||(l[12]=o=>a.xlss=o),dict:"sys_user,realname,username"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"popup弹窗",labelCol:p.value,wrapperCol:n.value},t(d).popup),{default:u(()=>[e(be,{value:a.popup,"onUpdate:value":l[13]||(l[13]=o=>a.popup=o),fieldConfig:[{source:"name",target:"popup"},{source:"id",target:"popback"}],code:"report_user",multi:!0,setFieldsValue:E},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"popback",labelCol:p.value,wrapperCol:n.value},t(d).popback),{default:u(()=>[e(w,{value:a.popback,"onUpdate:value":l[14]||(l[14]=o=>a.popback=o)},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"分类字典树",labelCol:p.value,wrapperCol:n.value},t(d).flzds),{default:u(()=>[e(ue,{onChange:l[15]||(l[15]=o=>C("flzds",o)),value:a.flzds,"onUpdate:value":l[16]||(l[16]=o=>a.flzds=o),pcode:"B02",placeholder:"请选择分类字典树"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"部门选择",labelCol:p.value,wrapperCol:n.value},t(d).bmxz),{default:u(()=>[e(ge,{value:a.bmxz,"onUpdate:value":l[17]||(l[17]=o=>a.bmxz=o),multi:!0,type:"array"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"用户选择",labelCol:p.value,wrapperCol:n.value},t(d).yhxz),{default:u(()=>[e(re,{value:a.yhxz,"onUpdate:value":l[18]||(l[18]=o=>a.yhxz=o),multi:!0},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"富文本",labelCol:p.value,wrapperCol:n.value},t(d).fwb),{default:u(()=>[e(pe,{value:a.fwb,"onUpdate:value":l[19]||(l[19]=o=>a.fwb=o)},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"markdown",labelCol:p.value,wrapperCol:n.value},t(d).markdownString),{default:u(()=>[e(we,{value:a.markdownString,"onUpdate:value":l[20]||(l[20]=o=>a.markdownString=o)},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"省市区JAreaSelect",labelCol:p.value,wrapperCol:n.value},t(d).shq),{default:u(()=>[e(je,{value:a.shq,"onUpdate:value":l[21]||(l[21]=o=>a.shq=o),placeholder:"请输入省市区"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"省市区JAreaLinkage",labelCol:p.value,wrapperCol:n.value},t(d).jssq),{default:u(()=>[e(xe,{value:a.jssq,"onUpdate:value":l[22]||(l[22]=o=>a.jssq=o),placeholder:"请输入省市区"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"JInputPop",labelCol:p.value,wrapperCol:n.value},t(d).ldzje),{default:u(()=>[e(Z,{value:a.ldzje,"onUpdate:value":l[23]||(l[23]=o=>a.ldzje=o),placeholder:"请输入JInputPop",onChange:l[24]||(l[24]=o=>C("ldzje",o))},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"JSelectInput",labelCol:p.value,wrapperCol:n.value},t(d).ldzjs),{default:u(()=>[e(se,{value:a.ldzjs,"onUpdate:value":l[25]||(l[25]=o=>a.ldzjs=o),placeholder:"请选择JSelectInput",options:I.value,onChange:l[26]||(l[26]=o=>C("ldzjs",o))},null,8,["value","options"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"下拉多选",labelCol:p.value,wrapperCol:n.value},t(d).zddtjxl),{default:u(()=>[e(te,{value:a.zddtjxl,"onUpdate:value":l[27]||(l[27]=o=>a.zddtjxl=o),placeholder:"请选择下拉多选",dictCode:"sex"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"用户",labelCol:p.value,wrapperCol:n.value},t(d).yongHu),{default:u(()=>[e(ze,{value:a.yongHu,"onUpdate:value":l[28]||(l[28]=o=>a.yongHu=o),placeholder:"请选择用户"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"职务",labelCol:p.value,wrapperCol:n.value},t(d).zhiWu),{default:u(()=>[e(ne,{value:a.zhiWu,"onUpdate:value":l[29]||(l[29]=o=>a.zhiWu=o),placeholder:"请选择职务",onChange:l[30]||(l[30]=o=>C("zhiWu",o))},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"角色",labelCol:p.value,wrapperCol:n.value},t(d).jueSe),{default:u(()=>[e(de,{value:a.jueSe,"onUpdate:value":l[31]||(l[31]=o=>a.jueSe=o),placeholder:"请选择角色",onChange:l[32]||(l[32]=o=>C("jueSe",o))},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"自定义树",labelCol:p.value,wrapperCol:n.value},t(d).zdys),{default:u(()=>[e(ie,{ref:"treeSelect",placeholder:"请选择自定义树",value:a.zdys,"onUpdate:value":l[33]||(l[33]=o=>a.zdys=o),dict:"sys_category,name,id",pidValue:"0",loadTriggleChange:""},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"数值",labelCol:p.value,wrapperCol:n.value},t(d).yuanjia),{default:u(()=>[e($,{value:a.yuanjia,"onUpdate:value":l[34]||(l[34]=o=>a.yuanjia=o),placeholder:"请输入double类型",style:{width:"100%"}},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"输入2到10位的字母",labelCol:p.value,wrapperCol:n.value},t(d).ywzz),{default:u(()=>[e(w,{value:a.ywzz,"onUpdate:value":l[35]||(l[35]=o=>a.ywzz=o),placeholder:"请输入2到10位的字母"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"JTreeDict",labelCol:p.value,wrapperCol:n.value},t(d).zdbxl),{default:u(()=>[e(me,{value:a.zdbxl,"onUpdate:value":l[36]||(l[36]=o=>a.zdbxl=o),placeholder:"请选择JTreeDict",onChange:l[37]||(l[37]=o=>C("zdbxl",o))},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"JCodeEditor",labelCol:p.value,wrapperCol:n.value},t(d).zdmrz),{default:u(()=>[e(_e,{value:a.zdmrz,"onUpdate:value":l[38]||(l[38]=o=>a.zdmrz=o),placeholder:"请输入JCodeEditor",onChange:l[39]||(l[39]=o=>C("zdmrz",o))},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1}),e(s,{span:24},{default:u(()=>[e(r,i({label:"参数",labelCol:p.value,wrapperCol:n.value},t(d).jsonParam),{default:u(()=>[e(ve,{value:a.jsonParam,"onUpdate:value":l[40]||(l[40]=o=>a.jsonParam=o),placeholder:"参数"},null,8,["value"])]),_:1},16,["labelCol","wrapperCol"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])}}}),il=h(ye,[["__scopeId","data-v-19e06482"]]);export{il as default};
