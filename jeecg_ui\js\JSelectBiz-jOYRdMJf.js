import{d as B,c as y,f as g,w as S,ag as l,aq as h,ar as t,aB as s,at as O,au as V,aD as d,k as v,ah as M,as as $,aE as A,G as C}from"./vue-vendor-dy9k-Yad.js";import{H as i,a9 as I,a as T}from"./index-CCWaWN5g.js";import{ai as _}from"./antd-vue-vendor-me9YkNVC.js";const D=B({name:"JSelectBiz",components:{LoadingOutlined:_},inheritAttrs:!1,props:{showButton:i.bool.def(!0),disabled:i.bool.def(!1),placeholder:{type:String,default:"请选择"},multiple:{type:String,default:"multiple"},loading:i.bool.def(!1),maxTagCount:i.number,buttonIcon:i.string.def(""),isDetailsMode:i.bool.def(!1)},emits:["handleOpen","change"],setup(e,{emit:a,refs:k}){const r=y("selectOptions")||g([]),n=y("selectValues")||g({}),b=I(),u=g("");function m(o){e.showButton&&o&&a("handleOpen"),!e.showButton&&!o&&a("handleOpen")}function c(o){n.value=o,n.change=!0,a("change",o)}return S([n,r],()=>{if(e.isDetailsMode&&Array.isArray(n.value)&&Array.isArray(r.value)){const o=r.value.map(p=>p.label);u.value=o.join(",")}},{immediate:!0}),{attrs:b,selectValues:n,options:r,handleChange:c,openModal:m,detailStr:u}}}),N={key:0},j=["title"];function z(e,a,k,r,n,b){const u=l("LoadingOutlined"),m=l("a-input"),c=l("a-select"),o=l("a-col"),p=l("a-button"),w=l("a-row");return t(),h("div",null,[e.isDetailsMode?(t(),h("div",N,[O("p",{class:"detailStr",title:e.detailStr},V(e.detailStr),9,j)])):(t(),s(w,{key:1,class:"j-select-row",type:"flex",gutter:8},{default:d(()=>[v(o,{class:$(["left",{full:!e.showButton}])},{default:d(()=>[e.loading?(t(),s(m,{key:0,readOnly:"",placeholder:"加载中…"},{prefix:d(()=>[v(u)]),_:1})):(t(),s(c,A({key:1,ref:"select",value:e.selectValues.value,"onUpdate:value":a[0]||(a[0]=f=>e.selectValues.value=f),placeholder:e.placeholder,mode:e.multiple,open:!1,disabled:e.disabled,options:e.options,maxTagCount:e.maxTagCount,onChange:e.handleChange,style:{width:"100%"},onClick:a[1]||(a[1]=f=>!e.disabled&&e.openModal(!1))},e.attrs),null,16,["value","placeholder","mode","disabled","options","maxTagCount","onChange"]))]),_:1},8,["class"]),e.showButton?(t(),s(o,{key:0,class:"right"},{default:d(()=>[e.buttonIcon?(t(),s(p,{key:0,preIcon:e.buttonIcon,type:"primary",onClick:a[2]||(a[2]=f=>e.openModal(!0)),disabled:e.disabled},{default:d(()=>a[4]||(a[4]=[C("选择")])),_:1,__:[4]},8,["preIcon","disabled"])):(t(),s(p,{key:1,type:"primary",onClick:a[3]||(a[3]=f=>e.openModal(!0)),disabled:e.disabled},{default:d(()=>a[5]||(a[5]=[C("选择")])),_:1,__:[5]},8,["disabled"]))]),_:1})):M("",!0)]),_:1}))])}const q=T(D,[["render",z],["__scopeId","data-v-47954ca9"]]);export{q as J};
