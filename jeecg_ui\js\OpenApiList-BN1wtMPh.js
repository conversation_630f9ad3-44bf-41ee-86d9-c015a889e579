var Y=Object.defineProperty,Z=Object.defineProperties;var $=Object.getOwnPropertyDescriptors;var E=Object.getOwnPropertySymbols;var oo=Object.prototype.hasOwnProperty,to=Object.prototype.propertyIsEnumerable;var I=(i,o,t)=>o in i?Y(i,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[o]=t,R=(i,o)=>{for(var t in o||(o={}))oo.call(o,t)&&I(i,t,o[t]);if(E)for(var t of E(o))to.call(o,t)&&I(i,t,o[t]);return i},T=(i,o)=>Z(i,$(o));var v=(i,o,t)=>new Promise((y,l)=>{var k=p=>{try{c(t.next(p))}catch(d){l(d)}},S=p=>{try{c(t.throw(p))}catch(d){l(d)}},c=p=>p.done?y(p.value):Promise.resolve(p.value).then(k,S);c((t=t.apply(i,o)).next())});import{d as M,r as B,f as eo,ag as s,v as no,aq as io,ar as u,k as a,aD as r,u as m,q as h,aB as f,ah as ro,G as _}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as po}from"./useListPage-Soxgnx9a.js";import"./index-Diw57m_E.js";import ao from"./OpenApiModal-Dcfp-dhv.js";import{s as mo,a as so,c as lo}from"./OpenApi.data-CeXef7sc.js";import{b as uo,g as co,a as fo,l as _o,d as bo}from"./OpenApi.api-DrT-pxFg.js";import{ad as go,ah as ho,a as yo}from"./index-CCWaWN5g.js";import{Q as Co}from"./componentMap-Bkie1n3v.js";import wo from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./useJvxeMethods-CdpRH_1y.js";import"./vxeUtils-B1NxCh07.js";const xo=M({name:"openapi-openApi"}),vo=M(T(R({},xo),{setup(i){const o=B({}),t=eo([]),[y,{openModal:l}]=go(),k=ho(),{prefixCls:S,tableContext:c,onExportXls:p,onImportXls:d}=po({tableProps:{title:"接口管理",api:_o,columns:lo,canResize:!1,formConfig:{schemas:so,autoSubmitOnEnter:!0,showAdvancedButton:!0,fieldMapToNumber:[],fieldMapToTime:[]},actionColumn:{width:120,fixed:"right"},beforeFetch:e=>Object.assign(e,o)},exportConfig:{name:"接口管理",url:fo,params:o},importConfig:{url:co,success:b}}),[O,{reload:D},{rowSelection:U,selectedRowKeys:C}]=c,q=B(mo);function F(e){Object.keys(e).map(n=>{o[n]=e[n]}),D()}function j(e,n){t.value=[],e===!0&&t.value.push(n.id)}function N(){l(!0,{isUpdate:!1,showFooter:!0})}function Q(e){l(!0,{record:e,isUpdate:!0,showFooter:!0})}function K(e){l(!0,{record:e,isUpdate:!0,showFooter:!1})}function L(e){return v(this,null,function*(){yield bo({id:e.id},b)})}function P(){return v(this,null,function*(){yield uo({ids:C.value},b)})}function b(){(C.value=[])&&D()}function V(e){return[{label:"编辑",onClick:Q.bind(null,e),auth:"openapi:open_api:edit"}]}function X(e){return[{label:"详情",onClick:K.bind(null,e)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:L.bind(null,e),placement:"topLeft"},auth:"openapi:open_api:delete"}]}return(e,n)=>{const w=s("a-button"),z=s("j-upload-button"),A=s("Icon"),G=s("a-menu-item"),H=s("a-menu"),J=s("a-dropdown"),W=s("super-query"),g=no("auth");return u(),io("div",null,[a(m(wo),{onRegister:m(O),rowSelection:m(U),onExpand:j},{tableTitle:r(()=>[h((u(),f(w,{type:"primary",onClick:N,preIcon:"ant-design:plus-outlined"},{default:r(()=>n[0]||(n[0]=[_(" 新增")])),_:1,__:[0]})),[[g,"openapi:open_api:add"]]),h((u(),f(w,{type:"primary",preIcon:"ant-design:export-outlined",onClick:m(p)},{default:r(()=>n[1]||(n[1]=[_(" 导出")])),_:1,__:[1]},8,["onClick"])),[[g,"openapi:open_api:exportXls"]]),h((u(),f(z,{type:"primary",preIcon:"ant-design:import-outlined",onClick:m(d)},{default:r(()=>n[2]||(n[2]=[_("导入")])),_:1,__:[2]},8,["onClick"])),[[g,"openapi:open_api:importExcel"]]),m(C).length>0?(u(),f(J,{key:0},{overlay:r(()=>[a(H,null,{default:r(()=>[a(G,{key:"1",onClick:P},{default:r(()=>[a(A,{icon:"ant-design:delete-outlined"}),n[3]||(n[3]=_(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:r(()=>[h((u(),f(w,null,{default:r(()=>[n[4]||(n[4]=_("批量操作 ")),a(A,{icon:"mdi:chevron-down"})]),_:1,__:[4]})),[[g,"openapi:open_api:deleteBatch"]])]),_:1})):ro("",!0),a(W,{config:q,onSearch:F},null,8,["config"])]),action:r(({record:x})=>[a(m(Co),{actions:V(x),dropDownActions:X(x)},null,8,["actions","dropDownActions"])]),bodyCell:r(({column:x,record:ko,index:So,text:Do})=>n[5]||(n[5]=[])),_:1},8,["onRegister","rowSelection"]),a(ao,{onRegister:m(y),onSuccess:b},null,8,["onRegister"])])}}})),Ft=yo(vo,[["__scopeId","data-v-5e7b2b05"]]);export{Ft as default};
