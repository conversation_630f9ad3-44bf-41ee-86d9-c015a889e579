var f=(a,o,s)=>new Promise((u,n)=>{var l=t=>{try{e(s.next(t))}catch(r){n(r)}},m=t=>{try{e(s.throw(t))}catch(r){n(r)}},e=t=>t.done?u(t.value):Promise.resolve(t.value).then(l,m);e((s=s.apply(a,o)).next())});import{a7 as C,a6 as B,B as h}from"./antd-vue-vendor-me9YkNVC.js";import{d as I,o as A,e as b,ag as p,aB as U,ar as w,aD as c,k as i,at as _,G as y}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{C as F}from"./index-LCGLvkB3.js";import{C as S}from"./index-BPAdgtaT.js";import{ah as k,d as R,u as V,j as g,a as $}from"./index-CCWaWN5g.js";import{h as E}from"./header-OZa5fSDc.js";import{b as M}from"./data-DwTukD7a.js";import{a as N}from"./upload-Dd1Qafin.js";import{B as j}from"./BasicForm-DBcXiHk0.js";import{u as x}from"./useForm-CgkFTrrO.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";const G=I({components:{BasicForm:j,CollapseContainer:F,Button:h,ARow:B,ACol:C,CropperAvatar:S},setup(){const{createMessage:a}=V(),o=k(),[s,{setFieldsValue:u,validate:n}]=x({labelWidth:120,schemas:M,showActionButtonGroup:!1});A(()=>f(null,null,function*(){const t=o.getUserInfo;u(t)}));const l=b(()=>{const{avatar:t}=o.getUserInfo;return R(t)||E});function m(t,r){const d=o.getUserInfo;d.avatar=r,o.setUserInfo(d),r&&g.post({url:"/sys/user/appEdit",params:{avatar:r}})}function e(){return f(this,null,function*(){try{let t=yield n();t.avatar=o.getUserInfo.avatar,g.post({url:"/sys/user/appEdit",params:t});const r=o.getUserInfo;Object.assign(r,t),o.setUserInfo(r),a.success("更新成功")}catch(t){}})}return{avatar:l,register:s,uploadImg:N,updateAvatar:m,handleSubmit:e}}}),H={class:"change-avatar"};function T(a,o,s,u,n,l){const m=p("BasicForm"),e=p("a-col"),t=p("CropperAvatar"),r=p("a-row"),d=p("Button"),v=p("CollapseContainer");return w(),U(v,{title:"基本设置",canExpan:!1},{default:c(()=>[i(r,{gutter:24},{default:c(()=>[i(e,{span:14},{default:c(()=>[i(m,{onRegister:a.register},null,8,["onRegister"])]),_:1}),i(e,{span:10},{default:c(()=>[_("div",H,[o[0]||(o[0]=_("div",{class:"mb-2"}," 头像 ",-1)),i(t,{uploadApi:a.uploadImg,value:a.avatar,btnText:"更换头像",btnProps:{preIcon:"ant-design:cloud-upload-outlined"},onChange:a.updateAvatar,width:"150"},null,8,["uploadApi","value","onChange"])])]),_:1})]),_:1}),i(d,{type:"primary",onClick:a.handleSubmit},{default:c(()=>o[1]||(o[1]=[y(" 更新基本信息 ")])),_:1,__:[1]},8,["onClick"])]),_:1})}const Kt=$(G,[["render",T],["__scopeId","data-v-1e4d7ab9"]]);export{Kt as default};
