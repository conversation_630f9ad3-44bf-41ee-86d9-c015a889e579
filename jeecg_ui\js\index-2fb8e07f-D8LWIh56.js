import{d as M,ap as O,ag as u,aq as V,ar as v,k as a,u as m,aE as q,aD as s,aB as K,ah as Q,G as L}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as F}from"./useListPage-Soxgnx9a.js";import{s as G,c as W,l as X,d as Y,b as j,g as z,C as H}from"./CgreportModal-8a84aae5-CI5RGWRu.js";import"./index-Diw57m_E.js";import{C as N}from"./clipboard-Bi9yotgY.js";import{ad as J,$ as Z}from"./index-CCWaWN5g.js";import"./index-L3cSIXth.js";import"./useJvxeMethods-CdpRH_1y.js";import"./user.api-mLAlJze4.js";import ee from"./BasicTable-xCEZpGLb.js";import{Q as te}from"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useFormItem-CHvpjy4o.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";import"./vxeUtils-B1NxCh07.js";var P=(C,y,l)=>new Promise((_,c)=>{var k=i=>{try{r(l.next(i))}catch(d){c(d)}},g=i=>{try{r(l.throw(i))}catch(d){c(d)}},r=i=>i.done?_(i.value):Promise.resolve(i.value).then(k,g);r((l=l.apply(C,y)).next())});const ct=M({__name:"index",setup(C,{expose:y}){let l=O();const[_,{openModal:c}]=J(),{prefixCls:k,tableContext:g,createMessage:r,createConfirm:i}=F({designScope:"online-cgreport-list",pagination:!0,tableProps:{title:"Online报表",api:X,rowKey:"id",columns:W,formConfig:{autoSubmitOnEnter:!0,showAdvancedButton:!0,schemas:G}}}),[d,{reload:w},{rowSelection:S,selectedRowKeys:b}]=g;function x(){c(!0,{isUpdate:!1,showFooter:!0})}function R(t){c(!0,{record:t,isUpdate:!0,showFooter:!0})}function $(t){return P(this,null,function*(){yield Y({id:t.id},w)})}function B(){return P(this,null,function*(){yield j({ids:b.value},()=>{w(),b.value=[]})})}function T(){w()}function A(t){return[{label:"编辑",onClick:R.bind(null,t)}]}function I(t){return[{label:"功能测试",class:["low-app-hide"],onClick:()=>D(t.id)},{label:"配置地址",class:["low-app-hide"],onClick:()=>E(t)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:$.bind(null,t)}}]}function D(t){l.push({path:"/online/cgreport/"+t})}function E(t){let n=t.id,p=`/online/cgreport/${n}`,f=`INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) 
                         VALUES ('${Z()}', NULL, '${t.name}', '${p}', '1', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 0, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', null, NULL, NULL, 0)`;z(n).then(o=>{let e="";if(o&&o.length>0)for(let h of o)e+=h.paramName+"=${"+h.paramName+"}&";e.length>0&&(e=e.substring(0,e.length-1),p+="?"+e)}).catch(()=>{r.warning("获取参数失败!")}).finally(()=>{i({iconType:"info",width:500,title:`菜单链接【${t.name}】`,content:p,closable:!0,maskClosable:!0,cancelText:"复制SQL",okText:"复制URL",cancelButtonProps:{class:"copy-this-sql","data-clipboard-text":f},okButtonProps:{class:"copy-this-text","data-clipboard-text":p},onOk(){return new Promise(o=>{const e=new N(".copy-this-text");e.on("success",()=>{e.destroy(),r.success("复制URL成功"),o()}),e.on("error",()=>{r.error("该浏览器不支持自动复制"),e.destroy(),o()})})},onCancel(){return new Promise(o=>{const e=new N(".copy-this-sql");e.on("success",()=>{e.destroy(),r.success("复制插入菜单SQL成功"),o()}),e.on("error",()=>{r.error("该浏览器不支持自动复制"),e.destroy(),o()})})}})})}return y({handleAdd:x}),(t,n)=>{const p=u("a-button"),f=u("Icon"),o=u("a-menu-item"),e=u("a-menu"),h=u("a-dropdown");return v(),V("div",null,[a(m(ee),q({onRegister:m(d),rowSelection:m(S)},t.$attrs),{tableTitle:s(()=>[a(p,{preIcon:"ant-design:plus-outlined",type:"primary",onClick:x,style:{"margin-right":"5px"}},{default:s(()=>n[0]||(n[0]=[L("录入")])),_:1}),m(b).length>0?(v(),K(h,{key:0},{overlay:s(()=>[a(e,null,{default:s(()=>[a(o,{key:"1",onClick:B},{default:s(()=>[a(f,{icon:"ant-design:delete-outlined"}),n[1]||(n[1]=L(" 删除 "))]),_:1})]),_:1})]),default:s(()=>[a(p,null,{default:s(()=>[n[2]||(n[2]=L("批量操作 ")),a(f,{icon:"mdi:chevron-down"})]),_:1})]),_:1})):Q("",!0)]),action:s(({record:U})=>[a(m(te),{actions:A(U),dropDownActions:I(U)},null,8,["actions","dropDownActions"])]),_:1},16,["onRegister","rowSelection"]),a(H,{onRegister:m(_),onSuccess:T},null,8,["onRegister"])])}}});export{ct as default};
