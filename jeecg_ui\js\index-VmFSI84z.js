var J=Object.defineProperty,Z=Object.defineProperties;var $=Object.getOwnPropertyDescriptors;var x=Object.getOwnPropertySymbols;var tt=Object.prototype.hasOwnProperty,et=Object.prototype.propertyIsEnumerable;var _=(r,n,s)=>n in r?J(r,n,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[n]=s,l=(r,n)=>{for(var s in n||(n={}))tt.call(n,s)&&_(r,s,n[s]);if(x)for(var s of x(n))et.call(n,s)&&_(r,s,n[s]);return r},k=(r,n)=>Z(r,$(n));var g=(r,n,s)=>new Promise((w,h)=>{var R=i=>{try{o(s.next(i))}catch(u){h(u)}},f=i=>{try{o(s.throw(i))}catch(u){h(u)}},o=i=>i.done?w(i.value):Promise.resolve(i.value).then(R,f);o((s=s.apply(r,n)).next())});import{d as I,ap as nt,f as y,ag as st,aq as m,ar as c,k as b,aD as p,au as A,G as at,u as E}from"./vue-vendor-dy9k-Yad.js";import{u as rt}from"./index-BkGZ5fiW.js";import{u as it,E as ot,aX as ut,a as lt}from"./index-CCWaWN5g.js";import{c as dt,a as mt,g as ct}from"./autonomouslyDispose-Bd4dTgT4.js";import{D as pt}from"./index-CyU3vcHV.js";import{Q as ht}from"./componentMap-Bkie1n3v.js";import ft from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFilePreview-CazplhRu.js";import"./SupplyDemand-DK00S9Ao.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const gt={class:"p-4"},yt={key:0},bt={key:1},Et={key:0},wt={key:1},Rt=I({name:"EntrustDispose"}),At=I(k(l({},Rt),{setup(r){const{createMessage:n}=it(),{handleExportXls:s}=ot(),w=nt(),h=[{title:"序号",dataIndex:"index",width:60,customRender:({index:t})=>t+1},{title:"委托单号",dataIndex:"entrustOrderId",width:160,customRender:({record:t})=>t.hgyAssetEntrust.entrustOrderId||"-"},{title:"资产名称",dataIndex:"assetName",width:200,ellipsis:!0,customRender:({record:t})=>t.hgyAssetEntrust.assetName||"-"},{title:"资产编号",dataIndex:"assetNo",width:180,customRender:({record:t})=>t.hgyAssetEntrust.assetNo||"-"},{title:"委托单位",dataIndex:"entrustCompanyName",width:150,ellipsis:!0,customRender:({record:t})=>t.hgyEntrustOrder.entrustCompanyName||"-"},{title:"受委托单位",dataIndex:"onEntrustCompanyName",width:150,ellipsis:!0,customRender:({record:t})=>t.hgyEntrustOrder.onEntrustCompanyName||"-"},{title:"资产数量",dataIndex:"quantity",width:100,customRender:({record:t})=>t.hgyAssetEntrust.quantity||"-"},{title:"计量单位",dataIndex:"unit",width:80,customRender:({record:t})=>t.hgyAssetEntrust.unit||"-"},{title:"资产所在地",dataIndex:"address",width:200,ellipsis:!0,customRender:({record:t})=>t.hgyAssetEntrust.address||"-"},{title:"委托时间",dataIndex:"createTime",width:150,customRender:({record:t})=>t.hgyAssetEntrust.createTime?ut(t.hgyAssetEntrust.createTime):"-"},{title:"保留价",dataIndex:"disposalPrice",width:120,customRender:({record:t})=>t.hgyAssetEntrust.disposalPrice||"-"},{title:"成交价",dataIndex:"transactionPrice",width:120,customRender:({record:t})=>t.hgyAssetEntrust.transactionPrice||"-"},{title:"实际数量",dataIndex:"actualQuantity",width:100,customRender:({record:t})=>t.hgyAssetEntrust.actualQuantity||"-"},{title:"溢价额",dataIndex:"premiumAmount",width:120,customRender:({record:t})=>t.hgyAssetEntrust.premiumAmount||"-"},{title:"溢价率",dataIndex:"premiumRate",width:80,customRender:({record:t})=>t.hgyAssetEntrust.premiumRate||"-"},{title:"审核状态",dataIndex:"status",width:100,slots:{customRender:"status"}}],R=[{key:"all",label:"全部委托",icon:""},{key:"draft",label:"未审核",icon:""},{key:"pending",label:"审核中",icon:""},{key:"approved",label:"已通过",icon:""},{key:"rejected",label:"未通过",icon:""}],f=y("all"),o=y({}),i=y(!1),u=y(null);function N(t){f.value=t;let e={};switch(t){case"draft":e={status:1};break;case"pending":e={status:2};break;case"approved":e={status:3};break;case"rejected":e={status:4};break;default:e={}}o.value=e,T()}function D(t){return g(this,null,function*(){const d=l(l(l({},{entrustType:2,serviceType:2}),o.value),t);return mt(d)})}function Tt(){return g(this,null,function*(){try{let t={};try{t=yield P().validate()}catch(d){}const e=l(l({},o.value),t);e.entrustTimeRange&&Array.isArray(e.entrustTimeRange)&&e.entrustTimeRange.length===2&&(e.entrustTimeStart=e.entrustTimeRange[0],e.entrustTimeEnd=e.entrustTimeRange[1],delete e.entrustTimeRange),e.biddingTimeRange&&Array.isArray(e.biddingTimeRange)&&e.biddingTimeRange.length===2&&(e.biddingTimeStart=e.biddingTimeRange[0],e.biddingTimeEnd=e.biddingTimeRange[1],delete e.biddingTimeRange),yield s("委托竞价列表",ct,e),n.success("导出成功")}catch(t){n.error("导出失败")}})}const[S,{reload:T,getForm:P}]=rt({api:D,columns:h,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,showNavigation:!0,navigationItems:R,activeNavigationKey:f.value,showExportButton:!0,inset:!0,maxHeight:478,actionColumn:{width:220,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"},formConfig:{labelWidth:64,size:"large",schemas:[{field:"assetName",label:"资产名称",component:"Input",colProps:{span:6}}]}});function M(t){return[{label:"编辑",onClick:V.bind(null,t)},{label:"查看详情",onClick:U.bind(null,t)},{label:"删除",color:"error",popConfirm:{title:"确认删除该委托吗？",confirm:W.bind(null,t)}}]}function O(t){return[{label:"撤拍",popConfirm:{title:"确认撤拍该委托吗？",confirm:B.bind(null,t)},ifShow:!1},{label:"报名管理",onClick:F.bind(null,t),ifShow:!1},{label:"数企详情",onClick:Q.bind(null,t),ifShow:!1},{label:"竞价记录",onClick:j.bind(null,t),ifShow:!1},{label:"结算信息",onClick:z.bind(null,t),ifShow:!1},{label:"成交确认书",onClick:K.bind(null,t),ifShow:!1},{label:"竞买人列表",onClick:X.bind(null,t),ifShow:!1},{label:"工作报告书",onClick:G.bind(null,t),ifShow:!1}]}function vt(t){return[1,2,4].includes(t.hgyAssetEntrust.status)}function Ct(t){return t.hgyAssetEntrust.status===5}function xt(t){return t.hgyAssetEntrust.status===1}function _t(t){return t.hgyAssetEntrust.status===5}function kt(t){return t.hgyAssetEntrust.status===6}function It(t){return t.hgyAssetEntrust.status===6}function Nt(t){return t.hgyAssetEntrust.status===6}function V(t){const e=t.hgyEntrustOrder.id;w.push({path:"/entrust/selfEntrust",query:{id:e,serviceType:2}})}function U(t){const e={id:t.hgyEntrustOrder.id,entrustType:2,serviceType:2,status:t.hgyAssetEntrust.status||2,projectName:t.hgyAssetEntrust.assetName||"-",relationUser:t.hgyEntrustOrder.relationUser||"-",relationPhone:t.hgyEntrustOrder.relationPhone||"-",applicantUser:t.hgyEntrustOrder.entrustCompanyName||"-",auditUser:"-",submitTime:t.hgyAssetEntrust.createTime||"-",auditTime:"-"};u.value=e,i.value=!0}function q(){i.value=!1,u.value=null}function B(t){n.info("撤拍功能开发中...")}function W(t){return g(this,null,function*(){try{yield dt(t.hgyEntrustOrder.id),n.success("删除成功"),T()}catch(e){n.error("删除失败")}})}function F(t){n.info("报名管理功能开发中...")}function Q(t){n.info("数企详情功能开发中...")}function j(t){n.info("竞价记录功能开发中...")}function z(t){n.info("结算信息功能开发中...")}function K(t){n.info("成交确认书功能开发中...")}function X(t){n.info("竞买人列表功能开发中...")}function G(t){n.info("工作报告书功能开发中...")}function H(t){return{1:"草稿",2:"待审核",3:"审核通过",4:"审核拒绝",5:"已发布",6:"已成交",7:"已撤拍"}[t]||"未知"}function L(t){return{1:"default",2:"processing",3:"success",4:"error",5:"cyan",6:"green",7:"red"}[t]||"default"}function Y(t){return!t&&t!==0?"-":new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY",minimumFractionDigits:2}).format(Number(t))}return(t,e)=>{const d=st("a-tag");return c(),m("div",gt,[b(E(ft),{onRegister:E(S),onNavigationChange:N},{action:p(({record:a})=>[b(E(ht),{actions:M(a),dropDownActions:O(a)},null,8,["actions","dropDownActions"])]),status:p(({record:a})=>{var v;return[b(d,{color:L((v=a.hgyAssetEntrust)==null?void 0:v.status)},{default:p(()=>{var C;return[at(A(H((C=a.hgyAssetEntrust)==null?void 0:C.status)),1)]}),_:2},1032,["color"])]}),amount:p(({text:a})=>[a?(c(),m("span",yt,A(Y(a)),1)):(c(),m("span",bt,"-"))]),premiumRate:p(({text:a})=>[a!=null?(c(),m("span",Et,A(a)+"%",1)):(c(),m("span",wt,"-"))]),_:1},8,["onRegister"]),b(E(pt),{open:i.value,"onUpdate:open":e[0]||(e[0]=a=>i.value=a),record:u.value,"entrust-type":2,"service-type":2,onClose:q},null,8,["open","record"])])}}})),Be=lt(At,[["__scopeId","data-v-c990fe90"]]);export{Be as default};
