import{f as te,e as ie,w as le,d as ne,ag as D,aq as m,ar as u,as as E,k as I,aD as $,F as M,aC as B,aB as U,at as c,au as J}from"./vue-vendor-dy9k-Yad.js";import{cs as ae,u as re,cA as oe,f as _,b as se,aF as V,j as de,aH as pe,ax as ue,av as me,cD as ce,d as fe,aL as ge,cB as he,H as N}from"./index-CCWaWN5g.js";import{cd as ye,ce as ve,bw as ke}from"./antd-vue-vendor-me9YkNVC.js";import{L as we}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{a as be}from"./index-mbACBRQ9.js";import"./componentMap-Bkie1n3v.js";import"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./vxe-table-vendor-B22HppNm.js";import"./JUpload-CRos0F1P.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";var xe=Object.defineProperty,W=Object.getOwnPropertySymbols,Ce=Object.prototype.hasOwnProperty,Fe=Object.prototype.propertyIsEnumerable,A=(a,d,p)=>d in a?xe(a,d,{enumerable:!0,configurable:!0,writable:!0,value:p}):a[d]=p,De=(a,d)=>{for(var p in d||(d={}))Ce.call(d,p)&&A(a,p,d[p]);if(W)for(var p of W(d))Fe.call(d,p)&&A(a,p,d[p]);return a},h=(a,d,p)=>new Promise((w,b)=>{var L=s=>{try{k(p.next(s))}catch(g){b(g)}},x=s=>{try{k(p.throw(s))}catch(g){b(g)}},k=s=>s.done?w(s.value):Promise.resolve(s.value).then(L,x);k((p=p.apply(a,d)).next())});function Le(a){const d={},p=[],w=te({}),{createMessage:b}=re(),L=ie(()=>a.containerClass?`jeecg-detail-form ${a.containerClass}`:"jeecg-detail-form");le(()=>a.data,e=>h(this,null,function*(){if(e){let n=a.schemas,l={};if(n&&n.length>0)for(let i of n){let t=i.field;try{l[t]=yield x(i)}catch(o){}}w.value=l}}),{deep:!0,immediate:!0});function x(e){return h(this,null,function*(){let n=a.data;if(n){let l=n[e.field];if(!l&&l!=="0"&&l!==0)return"";let i=l,t=e.view;if(t=="list"||t=="radio"||t=="checkbox"||t=="list_multi")i=yield k(e,n);else if(t=="sel_search")i=yield g(e,n);else if(t=="cat_tree")i=yield O(e,n);else if(t=="link_table")i=yield X(e,n);else if(t=="sel_depart")i=yield y(e,n);else if(t=="sel_user")i=yield j(e,n);else if(t=="pca")i=oe(l);else if(t=="link_down")i=yield S(e,n);else if(t=="sel_tree")i=yield v(e,n);else if(t=="switch")i=yield T(e,n);else if(t=="image"||t=="file")i=q(e,n);else if(t=="popup_dict"){const o=n[`${e.field}_dictText`];o!==void 0&&(i=o)}else if(p.indexOf(e.field)>=0){let o=d[e.field];o&&o.length>0&&(i=_(o,l))}return i}return""})}function k(e,n){return h(this,null,function*(){let l=s(e),i=n[e.field];if(!l)return i;let t=se(l);if(t&&t.length>0)return _(t,i);{let o=[];if(d[l]?o=d[l]:o=(yield V(l))||[],o&&o.length>0)return d[l]=o,_(o,i)}return""})}function s(e){let n="",{dictCode:l,dictTable:i,dictText:t}=e;return i?n=encodeURI(`${i},${t},${l}`):n=l,n}function g(e,n){return h(this,null,function*(){let l=s(e),i=n[e.field];if(!i)return"";let t=[];return d[l+i]?t=d[l+i]:t=(yield de.get({url:`/sys/dict/loadDictItem/${l}`,params:{key:i}}))||[],t&&t.length>0?(d[l+i]=t,t.join(",")):""})}function O(e,n){return h(this,null,function*(){let l=n[e.field];if(!l)return"";let i=(yield pe({ids:l}))||[];return i&&i.length>0?i.join(","):""})}function y(e,n){return h(this,null,function*(){let l=n[e.field];if(!l)return"";let i=r(e),t=i.store||"id",o=i.text||"departName",f=(yield ue({ids:l,primaryKey:t}))||[];if(f&&f.length>0){let C=[];for(let F of f)F[o]?C.push(F[o]):C.push(F.title);return C.join(",")}return""})}function j(e,n){return h(this,null,function*(){let l=n[e.field];if(!l)return"";let i=r(e),t={[i.store||"username"]:l},o=((yield me(t))||{}).records||[];if(o&&o.length>0){let f=[],C=i.text||"realname";for(let F of o)f.push(F[C]);return f.join(",")}return""})}function r(e){let n={},{fieldExtendJson:l}=e;if(l&&typeof l=="string")try{let i=JSON.parse(l);n=De({},i)}catch(i){}return n}function S(e,n){return h(this,null,function*(){let{dictTable:l,field:i}=e,t=[];if(d[i])t=d[i];else if(l){let o=JSON.parse(l);if(o){let{table:f,txt:C,key:F,linkField:H}=o,Z=`${f},${C},${F}`;if(t=[...(yield V(Z))||[]],t&&t.length>0&&(d[i]=t,H)){let ee=H.split(",");for(let P of ee)d[P]=t,p.push(P)}}}if(t&&t.length>0){let o=n[i];return _(t,o)}return""})}function v(e,n){return h(this,null,function*(){let{dict:l,field:i}=e,t=[];if(d[i]?t=d[i]:l&&(t=yield V(l)),t&&t.length>0){let o=n[i];return _(t,o)}return""})}function T(e,n){return h(this,null,function*(){let{fieldExtendJson:l,field:i}=e,t=["Y","N"];l&&(t=JSON.parse(l));let o=[{value:t[0],text:"是"},{value:t[1],text:"否"},{value:t[0]+"",text:"是"},{value:t[1]+"",text:"否"}],f=n[i];return ce(o,f)})}function Y(e){return e.span?e.span:a.span}function q(e,n){let l=n[e.field];if(!l)return[];let i=l.split(","),t=[];for(let o of i){let f=fe(o)||"";f&&t.push(f)}return t}function z(e){e&&window.open(e)}function Q(e){let n=w.value[e];if(!n||n.length==0){b.warning("无图片!");return}ge({imageList:n})}function R(e){return e?e.substring(e.lastIndexOf("/")+1):""}const G=["file","image","markdown","umeditor"];function K(e){return G.indexOf(e.view)>=0?a.span==12?"span12":a.span==8?"span8":a.span==6?"span6":"span24":""}function X(e,n){return h(this,null,function*(){let l=n[e.field];return r(e).showType=="select"?l?n[e.field+"_dictText"]:"":l?n[e.field]:""})}return{formContainerClass:L,detailFormData:w,getItemSpan:Y,handleDownloadFile:z,handleViewImage:Q,getFilename:R,getLabelWidthClass:K}}const Ie=ne({name:"DetailForm",components:{DownloadOutlined:ke,EyeOutlined:ve,PaperClipOutlined:ye,LinkTableCard:we,MarkdownViewer:be},props:{span:N.number.def(24),schemas:N.array.def([]),data:N.object.def({}),containerClass:N.string.def("")},setup(a){const{formContainerClass:d,detailFormData:p,getItemSpan:w,handleDownloadFile:b,handleViewImage:L,getFilename:x,getLabelWidthClass:k}=Le(a);return{formContainerClass:d,detailFormData:p,getItemSpan:w,handleDownloadFile:b,handleViewImage:L,getFilename:x,getLabelWidthClass:k,filterLable:s=>{if(s.fieldExtendJson){const g=JSON.parse(s.fieldExtendJson);if(g.labelLength&&s.label.length>4)return s.label.substr(0,g.labelLength)}return s.label},filter:(s,g,O)=>{if(g=="date"&&typeof s=="string"){if(!s)return"";let y=O.fieldExtendJson;return y&&(y=JSON.parse(y),y.picker&&y.picker!="default")?he(s)[y.picker]:s.split(" ").shift()}else return s},textareaLineBreak:s=>(s&&s.includes(`
`)&&(s=s.replace(/\n/g,"<br>")),s)}}}),Oe={key:1,class:"detail-item"},Te=["title"],_e={key:0,class:"item-content"},Me=["innerHTML"],Ne=["innerHTML"],je={key:3,class:"item-content",style:{display:"block","padding-top":"10px"}},Se={key:0},Ee={key:4,class:"item-content"},Be={class:"ant-upload-list ant-upload-list-picture-card"},Je={class:"ant-upload-list-picture-card-container",style:{"margin-top":"8px"}},Ve={class:"ant-upload-list-item ant-upload-list-item-done ant-upload-list-item-list-type-picture-card","data-has-actions":"true"},He={class:"ant-upload-list-item-info"},Pe=["src","onClick"],$e={class:"ant-upload-list-item-actions"},Ue={key:5,class:"item-content"},We={class:"ant-upload-list ant-upload-list-text"},Ae={class:""},Ye={class:"ant-upload-list-item ant-upload-list-item-done ant-upload-list-item-list-type-text"},qe={class:"ant-upload-list-item-info"},ze={class:"ant-upload-span"},Qe={class:"ant-upload-text-icon"},Re=["href"],Ge={class:"ant-upload-list-item-card-actions"},Ke={key:6,class:"item-content"};function Xe(a,d,p,w,b,L){const x=D("MarkdownViewer"),k=D("link-table-card"),s=D("download-outlined"),g=D("eye-outlined"),O=D("paper-clip-outlined"),y=D("a-col"),j=D("a-row");return u(),m("div",{class:E(a.formContainerClass)},[I(j,null,{default:$(()=>[(u(!0),m(M,null,B(a.schemas,(r,S)=>(u(),U(y,{key:S,span:a.getItemSpan(r)},{default:$(()=>[r.hidden?(u(),m(M,{key:0},[],64)):(u(),m("div",Oe,[c("div",{class:E(["item-title",a.getLabelWidthClass(r)]),title:r.label},J(a.filterLable(r))+"： ",11,Te),r.view==="markdown"?(u(),m("div",_e,[I(x,{value:a.detailFormData[r.field],"onUpdate:value":v=>a.detailFormData[r.field]=v,placeholder:""},null,8,["value","onUpdate:value"])])):r.isHtml?(u(),m("div",{key:1,class:E(["item-content",r.view]),innerHTML:a.detailFormData[r.field]},null,10,Me)):r.view=="textarea"?(u(),m("div",{key:2,class:"item-content",innerHTML:a.textareaLineBreak(a.detailFormData[r.field])},null,8,Ne)):r.isCard?(u(),m("div",je,[a.detailFormData[r.field]?(u(),U(k,{key:1,disabled:"",detail:"",value:a.detailFormData[r.field],valueField:r.dictCode,textField:r.dictText,tableName:r.dictTable,multi:r.multi},null,8,["value","valueField","textField","tableName","multi"])):(u(),m("span",Se))])):r.isImage?(u(),m("div",Ee,[c("div",Be,[(u(!0),m(M,null,B(a.detailFormData[r.field],v=>(u(),m("div",Je,[c("span",null,[c("div",Ve,[c("div",He,[c("img",{src:v,alt:"图片不存在",class:"ant-upload-list-item-image",onClick:T=>a.handleViewImage(r.field)},null,8,Pe)]),c("span",$e,[I(s,{onClick:T=>a.handleDownloadFile(v)},null,8,["onClick"]),I(g,{onClick:T=>a.handleViewImage(r.field)},null,8,["onClick"])])])])]))),256))])])):r.isFile?(u(),m("div",Ue,[c("div",We,[(u(!0),m(M,null,B(a.detailFormData[r.field],v=>(u(),m("div",Ae,[c("span",null,[c("div",Ye,[c("div",qe,[c("span",ze,[c("div",Qe,[I(O)]),c("a",{href:v,target:"_blank",rel:"noopener noreferrer",class:"ant-upload-list-item-name"},J(a.getFilename(v)),9,Re),c("span",Ge,[I(s,{onClick:T=>a.handleDownloadFile(v)},null,8,["onClick"])])])])])])]))),256))])])):(u(),m("div",Ke,J(a.filter(a.detailFormData[r.field],r.view,r)),1))]))]),_:2},1032,["span"]))),128))]),_:1})],2)}const mi=ae(Ie,[["render",Xe],["__scopeId","data-v-05ca0a61"]]);export{mi as default};
