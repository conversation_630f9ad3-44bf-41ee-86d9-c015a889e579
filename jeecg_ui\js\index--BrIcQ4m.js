import{d as _,ag as o,aB as s,ar as f,aD as t,k as e,at as m,G as i,u as l}from"./vue-vendor-dy9k-Yad.js";import{P as c}from"./index-CtJ0w2CP.js";import d from"./JVxeDemo1-CSxiDPvw.js";import u from"./JVxeDemo2-rX3dh1Bc.js";import b from"./JVxeDemo3-BpSt38L6.js";import k from"./JVxeDemo4-CnsshP6P.js";import x from"./JVxeDemo5-Co8UkEDr.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const G=_({__name:"index",setup(y){return(V,r)=>{const a=o("a-tab-pane"),n=o("a-tabs"),p=o("a-card");return f(),s(l(c),null,{default:t(()=>[e(p,{bordered:!1},{title:t(()=>r[0]||(r[0]=[m("span",null,[i(" JVXETable是专门为大数据和各种ERP风格的复杂操作研发的的高性能表格组件，底层采用vxe-table组件，可以完美弥补antd默认table性能不足问题。 "),m("a",{href:"https://help.jeecg.com/component/JVxeTable.html"},"API文档")],-1)])),default:t(()=>[e(n,{defaultActiveKey:"1"},{default:t(()=>[e(a,{tab:"基础示例",key:"1"},{default:t(()=>[e(d)]),_:1}),e(a,{tab:"高级示例",key:"2"},{default:t(()=>[e(u)]),_:1}),e(a,{tab:"排序示例",key:"3"},{default:t(()=>[e(b)]),_:1}),e(a,{tab:"联动示例",key:"4"},{default:t(()=>[e(k)]),_:1}),e(a,{tab:"键盘操作",key:"5"},{default:t(()=>[e(x)]),_:1})]),_:1})]),_:1})]),_:1})}}});export{G as default};
