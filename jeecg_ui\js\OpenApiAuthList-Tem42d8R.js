var se=Object.defineProperty,me=Object.defineProperties;var ue=Object.getOwnPropertyDescriptors;var j=Object.getOwnPropertySymbols;var de=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable;var q=(r,i,t)=>i in r?se(r,i,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[i]=t,E=(r,i)=>{for(var t in i||(i={}))de.call(i,t)&&q(r,t,i[t]);if(j)for(var t of j(i))ce.call(i,t)&&q(r,t,i[t]);return r},O=(r,i)=>me(r,ue(i));var S=(r,i,t)=>new Promise((c,p)=>{var f=s=>{try{w(t.next(s))}catch(g){p(g)}},R=s=>{try{w(t.throw(s))}catch(g){p(g)}},w=s=>s.done?c(s.value):Promise.resolve(s.value).then(f,R);w((t=t.apply(r,i)).next())});import{d as Q,f as I,r as D,ag as l,v as _e,aq as fe,ar as _,at as v,k as n,aD as o,G as u,au as ge,aO as ye,u as d,q as A,aB as x,ah as be}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as ve}from"./useListPage-Soxgnx9a.js";import{b as xe,g as we,a as he,l as Ce,d as ke}from"./JFormContainer-BUU5aWZC.js";import Se from"./OpenApiAuthModal-D0yQ_ukp.js";import Ie from"./AuthModal-B6ayTN1L.js";import{ah as De,a as Ae}from"./index-CCWaWN5g.js";import{J as Ue}from"./JSearchSelect-c_lfTydU.js";import{Q as Be}from"./componentMap-Bkie1n3v.js";import Ke from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./OpenApiAuthForm-B8oZ9C_1.js";import"./JModal-CqNGp5k5.js";import"./AuthForm-DgRqJJpy.js";import"./OpenApi.data-CeXef7sc.js";const Re=[{title:"授权名称",align:"center",dataIndex:"name"},{title:"AK",align:"center",dataIndex:"ak"},{title:"SK",align:"center",dataIndex:"sk"},{title:"创建人",align:"center",dataIndex:"createBy"},{title:"创建时间",align:"center",dataIndex:"createTime"},{title:"关联系统用户名",align:"center",dataIndex:"systemUserId_dictText"}],Te={name:{title:"授权名称",order:0,view:"text",type:"string"},ak:{title:"AK",order:1,view:"text",type:"string"},sk:{title:"SK",order:2,view:"text",type:"string"},createBy:{title:"创建人",order:3,view:"text",type:"string"},createTime:{title:"创建时间",order:4,view:"datetime",type:"string"},systemUserId:{title:"关联系统用户名",order:5,view:"text",type:"string"}},je={class:"p-2"},qe={class:"jeecg-basic-table-form-container"},Ee={style:{float:"left",overflow:"hidden"},class:"table-page-search-submitButtons"},Oe=Q({name:"openapi-openApiAuth"}),Qe=Q(O(E({},Oe),{setup(r){const i=I(),t=D({}),c=I(!1),p=I(),f=I(),R=De(),{prefixCls:w,tableContext:s,onExportXls:g,onImportXls:M}=ve({tableProps:{title:"授权管理",api:Ce,columns:Re,canResize:!1,useSearchForm:!1,actionColumn:{width:200,fixed:"right"},beforeFetch:a=>S(null,null,function*(){return Object.assign(a,t)})},exportConfig:{name:"授权管理",url:he,params:t},importConfig:{url:we,success:y}}),[N,{reload:U,updateTableDataRecord:Me,getDataSource:Ne},{rowSelection:V,selectedRowKeys:h}]=s,F=D({xs:24,sm:10,xl:6,xxl:10}),L=D({xs:24,sm:20}),P=D(Te);function X(a){Object.keys(a).map(e=>{t[e]=a[e]}),B()}function $(){p.value.disableSubmit=!1,p.value.add()}function J(a){f.value.disableSubmit=!1,f.value.edit(a)}function z(a){p.value.disableSubmit=!1,p.value.edit(a)}function G(a){p.value.disableSubmit=!0,p.value.edit(a)}function H(a){return S(this,null,function*(){yield ke({id:a.id},y)})}function W(){return S(this,null,function*(){yield xe({ids:h.value},y)})}function y(){(h.value=[])&&U()}function Y(a){return[{label:"授权",onClick:J.bind(null,a),auth:"openapi:open_api_auth:edit"},{label:"编辑",onClick:z.bind(null,a),auth:"openapi:open_api_auth:edit"}]}function Z(a){return[{label:"详情",onClick:G.bind(null,a)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:H.bind(null,a),placement:"topLeft"},auth:"openapi:open_api_auth:delete"}]}function B(){U()}function ee(){i.value.resetFields(),h.value=[],U()}return(a,e)=>{const te=l("a-input"),T=l("a-form-item"),C=l("a-col"),b=l("a-button"),K=l("Icon"),oe=l("a-row"),ne=l("a-form"),ae=l("j-upload-button"),ie=l("a-menu-item"),re=l("a-menu"),le=l("a-dropdown"),pe=l("super-query"),k=_e("auth");return _(),fe("div",je,[v("div",qe,[n(ne,{ref_key:"formRef",ref:i,onKeyup:ye(B,["enter","native"]),model:t,"label-col":F,"wrapper-col":L},{default:o(()=>[n(oe,{gutter:24},{default:o(()=>[n(C,{lg:6},{default:o(()=>[n(T,{name:"name"},{label:o(()=>e[3]||(e[3]=[v("span",{title:"授权名称"},"授权名称",-1)])),default:o(()=>[n(te,{placeholder:"请输入授权名称",value:t.name,"onUpdate:value":e[0]||(e[0]=m=>t.name=m),"allow-clear":""},null,8,["value"])]),_:1})]),_:1}),n(C,{lg:6},{default:o(()=>[n(T,{name:"systemUserId"},{label:o(()=>e[4]||(e[4]=[v("span",{title:"关联系统用户名"},"关联系统用户名",-1)])),default:o(()=>[n(Ue,{dict:"sys_user,username,id",value:t.systemUserId,"onUpdate:value":e[1]||(e[1]=m=>t.systemUserId=m),placeholder:"请输入关联系统用户名","allow-clear":""},null,8,["value"])]),_:1})]),_:1}),n(C,{xl:6,lg:7,md:8,sm:24},{default:o(()=>[v("span",Ee,[n(C,{lg:6},{default:o(()=>[n(b,{type:"primary",preIcon:"ant-design:search-outlined",onClick:B},{default:o(()=>e[5]||(e[5]=[u("查询")])),_:1,__:[5]}),n(b,{type:"primary",preIcon:"ant-design:reload-outlined",onClick:ee,style:{"margin-left":"8px"}},{default:o(()=>e[6]||(e[6]=[u("重置")])),_:1,__:[6]}),v("a",{onClick:e[2]||(e[2]=m=>c.value=!c.value),style:{"margin-left":"8px"}},[u(ge(c.value?"收起":"展开")+" ",1),n(K,{icon:c.value?"ant-design:up-outlined":"ant-design:down-outlined"},null,8,["icon"])])]),_:1})])]),_:1})]),_:1})]),_:1},8,["model","label-col","wrapper-col"])]),n(d(Ke),{onRegister:d(N),rowSelection:d(V)},{tableTitle:o(()=>[A((_(),x(b,{type:"primary",onClick:$,preIcon:"ant-design:plus-outlined"},{default:o(()=>e[7]||(e[7]=[u(" 新增")])),_:1,__:[7]})),[[k,"openapi:open_api_auth:add"]]),A((_(),x(b,{type:"primary",preIcon:"ant-design:export-outlined",onClick:d(g)},{default:o(()=>e[8]||(e[8]=[u(" 导出")])),_:1,__:[8]},8,["onClick"])),[[k,"openapi:open_api_auth:exportXls"]]),A((_(),x(ae,{type:"primary",preIcon:"ant-design:import-outlined",onClick:d(M)},{default:o(()=>e[9]||(e[9]=[u("导入")])),_:1,__:[9]},8,["onClick"])),[[k,"openapi:open_api_auth:importExcel"]]),d(h).length>0?(_(),x(le,{key:0},{overlay:o(()=>[n(re,null,{default:o(()=>[n(ie,{key:"1",onClick:W},{default:o(()=>[n(K,{icon:"ant-design:delete-outlined"}),e[10]||(e[10]=u(" 删除 "))]),_:1,__:[10]})]),_:1})]),default:o(()=>[A((_(),x(b,null,{default:o(()=>[e[11]||(e[11]=u("批量操作 ")),n(K,{icon:"mdi:chevron-down"})]),_:1,__:[11]})),[[k,"openapi:open_api_auth:deleteBatch"]])]),_:1})):be("",!0),n(pe,{config:P,onSearch:X},null,8,["config"])]),action:o(({record:m})=>[n(d(Be),{actions:Y(m),dropDownActions:Z(m)},null,8,["actions","dropDownActions"])]),bodyCell:o(({column:m,record:Ve,index:Fe,text:Le})=>e[12]||(e[12]=[])),_:1},8,["onRegister","rowSelection"]),n(Se,{ref_key:"registerModal",ref:p,onSuccess:y},null,512),n(Ie,{ref_key:"authModal",ref:f,onSuccess:y},null,512)])}}})),oo=Ae(Qe,[["__scopeId","data-v-8ec38b93"]]);export{oo as default};
