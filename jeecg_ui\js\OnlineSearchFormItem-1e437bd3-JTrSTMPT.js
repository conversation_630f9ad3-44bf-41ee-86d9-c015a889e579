import{d as I,f as O,w as C,aI as L,ag as n,aB as r,ar as i,as as G,aD as S,aq as m,F as s,k as y,at as k,aC as W,G as z,au as B,aE as j}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import $ from"./JOnlineSearchSelect-04ec87d0-Bkc9wau7.js";import{cs as H}from"./index-CCWaWN5g.js";import{o as Q,t as R,e as X,i as Z,_ as ee}from"./JAddInput-CxJ-JBK-.js";import{i as te}from"./JAreaSelect-Db7Nhhc_.js";import le from"./JAreaLinkage-DFCdF3cr.js";import{u as ae}from"./JPopup-CeU6ry6r.js";import{a as ie}from"./JSelectDept-I-NqkbOH.js";import{l as re}from"./JSelectUser-COkExGbu.js";import"./BasicForm-DBcXiHk0.js";import"./antd-vue-vendor-me9YkNVC.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JEllipsis-BsXuWNHJ.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectBiz-jOYRdMJf.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./props-CCT78mKr.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";var oe=Object.defineProperty,E=Object.getOwnPropertySymbols,ne=Object.prototype.hasOwnProperty,ue=Object.prototype.propertyIsEnumerable,K=(e,t,o)=>t in e?oe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,pe=(e,t)=>{for(var o in t||(t={}))ne.call(t,o)&&K(e,o,t[o]);if(E)for(var o of E(t))ue.call(t,o)&&K(e,o,t[o]);return e};const M=I({name:"OnlineSearchFormItem",components:{JOnlineSearchSelect:$,JDictSelectTag:ee,JTreeSelect:Z,JCategorySelect:X,JSelectUser:re,JSelectUserByDept:R,JSelectDept:ie,JPopup:ae,JAreaLinkage:le,JAreaSelect:te,JSelectMultiple:Q},props:{value:{type:String,default:""},item:{type:Object,default:()=>{},required:!0},dictOptions:{type:Object,default:()=>{},required:!1},onlineForm:{type:Object,default:()=>{},required:!1}},emits:["update:value","change"],setup(e,{emit:t}){const o="120px",P={style:{"max-width":o}},q="single";let f=O(""),u=O(""),g=O("");C(()=>e.value,()=>{V()?f.value=e.value?e.value:void 0:f.value=e.value,e.value||(u.value="",g.value="")},{deep:!0,immediate:!0}),C(f,a=>{t("update:value",a)},{immediate:!0}),C(u,a=>{t("change",e.item.field+"_begin",a),t("update:value","1")}),C(g,a=>{t("change",e.item.field+"_end",a),t("update:value","1")});function D(a){return a.dbField?a.dbField:a.field}function V(){let a=e.item;return a?a.view=="list"||a.view=="radio"||a.view=="switch":!1}function T(){let a=e.item;return a.dictTable&&a.dictTable.length>0?a.dictTable+","+a.dictText+","+a.dictCode:a.dictCode}function x(){let a=e.item,{dictTable:l,dictCode:p,dictText:v}=a,d=l.toLowerCase().split("where"),h="";return d.length>1&&(h=" where"+d[1]),"select "+p+" as 'value', "+v+" as 'text' from "+d[0]+h}function F(a){let{dictText:l,dictCode:p}=a;if(!l||l.length==0)return[];let v=l.split(","),d=p.split(","),h=[];for(let c=0;c<v.length;c++)h.push({target:v[c],source:d[c]});return h}function Y(a){let{dictText:l}=e.item,p=l.split(",")[0];t("change",p,a[p])}function U(a){t("update:value",a)}function w(a,l,p){let v={labelKey:l,rowKey:p},d=a.fieldExtendJson;if(d&&typeof d=="string"){let h=JSON.parse(d),c=pe({},h);c.text&&(v.labelKey=c.text),c.store&&(v.rowKey=c.store)}return v}let J=w(e.item,"realname","username"),_=w(e.item,"departName","id");function b(a){a&&a.length>0?t("update:value",a.join(",")):t("update:value","")}return{getPopupFieldConfig:F,userSelectProp:J,depSelectProp:_,handleSelectChange:b,setFieldsValue:Y,innerValue:f,beginValue:u,endValue:g,isEasySelect:V,getDictOptionKey:D,getDictCode:T,labelTextMaxWidth:o,labelCol:P,single_mode:q,getSqlByDictCode:x,handleCategoryTreeChange:U}}}),N=()=>{L(e=>({"6524b8a9":e.labelTextMaxWidth}))},A=M.setup;M.setup=A?(e,t)=>(N(),A(e,t)):N;const de=M,ce=["title"];function me(e,t,o,P,q,f){const u=n("a-date-picker"),g=n("JDictSelectTag"),D=n("a-select-option"),V=n("a-select"),T=n("JTreeSelect"),x=n("JCategorySelect"),F=n("JOnlineSearchSelect"),Y=n("JSelectUser"),U=n("JSelectDept"),w=n("JPopup"),J=n("JAreaSelect"),_=n("JSelectMultiple"),b=n("a-input"),a=n("a-form-item");return i(),r(a,{labelCol:e.labelCol,class:G("jeecg-online-search")},{label:S(()=>[k("span",{title:e.item.label,class:"label-text"},B(e.item.label),9,ce)]),default:S(()=>[e.item.view=="date"?(i(),m(s,{key:0},[e.single_mode===e.item.mode?(i(),r(u,{key:0,style:{width:"100%"},showTime:!1,valueFormat:"YYYY-MM-DD",placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":t[0]||(t[0]=l=>e.innerValue=l)},null,8,["placeholder","value"])):(i(),m(s,{key:1},[y(u,{showTime:!1,valueFormat:"YYYY-MM-DD",placeholder:"开始日期",value:e.beginValue,"onUpdate:value":t[1]||(t[1]=l=>e.beginValue=l),style:{width:"calc(50% - 15px)"}},null,8,["value"]),t[20]||(t[20]=k("span",{class:"group-query-strig"},"~",-1)),y(u,{showTime:!1,valueFormat:"YYYY-MM-DD",placeholder:"结束日期",value:e.endValue,"onUpdate:value":t[2]||(t[2]=l=>e.endValue=l),style:{width:"calc(50% - 15px)"}},null,8,["value"])],64))],64)):e.item.view=="datetime"?(i(),m(s,{key:1},[e.single_mode===e.item.mode?(i(),r(u,{key:0,style:{width:"100%"},showTime:!0,valueFormat:"YYYY-MM-DD hh:mm:ss",placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":t[3]||(t[3]=l=>e.innerValue=l)},null,8,["placeholder","value"])):(i(),m(s,{key:1},[y(u,{showTime:!0,valueFormat:"YYYY-MM-DD hh:mm:ss",placeholder:"开始时间",value:e.beginValue,"onUpdate:value":t[4]||(t[4]=l=>e.beginValue=l),style:{width:"calc(50% - 15px)"}},null,8,["value"]),t[21]||(t[21]=k("span",{class:"group-query-strig"},"~",-1)),y(u,{showTime:!0,valueFormat:"YYYY-MM-DD hh:mm:ss",placeholder:"结束时间",value:e.endValue,"onUpdate:value":t[5]||(t[5]=l=>e.endValue=l),style:{width:"calc(50% - 15px)"}},null,8,["value"])],64))],64)):e.isEasySelect()?(i(),m(s,{key:2},[e.item.config==="1"?(i(),r(g,{key:0,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":t[6]||(t[6]=l=>e.innerValue=l),dictCode:e.getDictCode()},null,8,["placeholder","value","dictCode"])):(i(),r(V,{key:1,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":t[7]||(t[7]=l=>e.innerValue=l)},{default:S(()=>[(i(!0),m(s,null,W(e.dictOptions[e.getDictOptionKey(e.item)],(l,p)=>(i(),r(D,{key:p,value:l.value},{default:S(()=>[z(B(l.text),1)]),_:2},1032,["value"]))),128))]),_:1},8,["placeholder","value"]))],64)):e.item.view==="sel_tree"?(i(),r(T,{key:3,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":t[8]||(t[8]=l=>e.innerValue=l),dict:e.item.dict,pidField:e.item.pidField,pidValue:e.item.pidValue,hasChildField:e.item.hasChildField,"load-triggle-change":""},null,8,["placeholder","value","dict","pidField","pidValue","hasChildField"])):e.item.view==="cat_tree"?(i(),r(x,{key:4,onChange:e.handleCategoryTreeChange,loadTriggleChange:!0,pcode:e.item.pcode,value:e.innerValue,"onUpdate:value":t[9]||(t[9]=l=>e.innerValue=l),placeholder:"请选择"+e.item.label},null,8,["onChange","pcode","value","placeholder"])):e.item.view==="sel_search"?(i(),m(s,{key:5},[e.item.config==="1"?(i(),r(g,{key:0,value:e.innerValue,"onUpdate:value":t[10]||(t[10]=l=>e.innerValue=l),placeholder:"请选择"+e.item.label,dict:e.getDictCode()},null,8,["value","placeholder","dict"])):(i(),r(F,{key:1,value:e.innerValue,"onUpdate:value":t[11]||(t[11]=l=>e.innerValue=l),placeholder:"请选择"+e.item.label,sql:e.getSqlByDictCode()},null,8,["value","placeholder","sql"]))],64)):e.item.view=="sel_user"?(i(),r(Y,j({key:6},e.userSelectProp,{value:e.innerValue,"onUpdate:value":t[12]||(t[12]=l=>e.innerValue=l),placeholder:"请选择"+e.item.label}),null,16,["value","placeholder"])):e.item.view=="sel_depart"?(i(),r(U,j({key:7,showButton:!1},e.depSelectProp,{value:e.innerValue,"onUpdate:value":t[13]||(t[13]=l=>e.innerValue=l),placeholder:"请选择"+e.item.label}),null,16,["value","placeholder"])):e.item.view=="popup"?(i(),r(w,{key:8,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":t[14]||(t[14]=l=>e.innerValue=l),code:e.item.dictTable,setFieldsValue:e.setFieldsValue,"field-config":e.getPopupFieldConfig(e.item),multi:!0},null,8,["placeholder","value","code","setFieldsValue","field-config"])):e.item.view=="pca"?(i(),r(J,{key:9,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":t[15]||(t[15]=l=>e.innerValue=l)},null,8,["placeholder","value"])):e.item.view=="checkbox"||e.item.view=="list_multi"?(i(),r(_,{key:10,dictCode:e.getDictCode(),placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":t[16]||(t[16]=l=>e.innerValue=l)},null,8,["dictCode","placeholder","value"])):(i(),m(s,{key:11},[e.single_mode===e.item.mode?(i(),r(b,{key:0,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":t[17]||(t[17]=l=>e.innerValue=l)},null,8,["placeholder","value"])):(i(),m(s,{key:1},[y(b,{placeholder:"开始值",value:e.beginValue,"onUpdate:value":t[18]||(t[18]=l=>e.beginValue=l),style:{width:"calc(50% - 15px)"}},null,8,["value"]),t[22]||(t[22]=k("span",{class:"group-query-strig"},"~",-1)),y(b,{placeholder:"结束值",value:e.endValue,"onUpdate:value":t[19]||(t[19]=l=>e.endValue=l),style:{width:"calc(50% - 15px)"}},null,8,["value"])],64))],64))]),_:1},8,["labelCol"])}const vt=H(de,[["render",me],["__scopeId","data-v-e62a9629"]]);export{vt as default};
