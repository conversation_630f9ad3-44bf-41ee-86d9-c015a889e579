var c=(T,e,n)=>new Promise((o,y)=>{var f=d=>{try{A(n.next(d))}catch(m){y(m)}},P=d=>{try{A(n.throw(d))}catch(m){y(m)}},A=d=>d.done?o(d.value):Promise.resolve(d.value).then(f,P);A((n=n.apply(T,e)).next())});import{d as S,f as I,ag as B,aq as O,ar as $,at as p,k as r,aD as u,G as a,au as h,u as D}from"./vue-vendor-dy9k-Yad.js";import{f as l}from"./antd-vue-vendor-me9YkNVC.js";import{a as E,g as j,b as x,c as J,d as V,e as v}from"./index-CyU3vcHV.js";import{a as M}from"./index-CCWaWN5g.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFilePreview-CazplhRu.js";import"./SupplyDemand-DK00S9Ao.js";const R={class:"audit-test"},q={class:"test-section"},G={class:"test-buttons"},z={class:"test-section"},F={class:"test-buttons"},H={class:"test-result"},K=S({__name:"AuditTest",setup(T){const e=I(""),n=I(!1),o=I(null);function y(){return c(this,null,function*(){try{e.value="正在测试增值委托竞价审核API...";const s=yield j("test-appreciation-bidding-001");e.value=`增值委托竞价审核API测试结果:
${JSON.stringify(s,null,2)}`,s&&s.hgyEntrustOrder&&s.hgyAuctionItemTemp?e.value+=`

✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyAuctionItemTemp`:e.value+=`

❌ 数据结构异常：缺少必要字段`,l.success("增值委托竞价审核API测试完成")}catch(s){e.value=`增值委托竞价审核API测试失败: ${s}`,l.error("增值委托竞价审核API测试失败")}})}function f(){return c(this,null,function*(){try{e.value="正在测试自主委托竞价审核API...";const s=yield x("test-self-bidding-001");e.value=`自主委托竞价审核API测试结果:
${JSON.stringify(s,null,2)}`,s&&s.hgyEntrustOrder&&s.hgyAuctionItemTemp?(e.value+=`

✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyAuctionItemTemp`,s.hgyAuction&&(e.value+=`
✅ 包含 hgyAuction（自主委托特有）`)):e.value+=`

❌ 数据结构异常：缺少必要字段`,l.success("自主委托竞价审核API测试完成")}catch(s){e.value=`自主委托竞价审核API测试失败: ${s}`,l.error("自主委托竞价审核API测试失败")}})}function P(){return c(this,null,function*(){try{e.value="正在测试资产处置审核API（通用）...";const s=yield J("test-asset-001");e.value=`资产处置审核API测试结果:
${JSON.stringify(s,null,2)}`,e.value+=`

📝 说明：资产处置审核不论是增值委托还是自主委托，都使用同一个接口`,l.success("资产处置审核API测试完成")}catch(s){e.value=`资产处置审核API测试失败: ${s}`,l.error("资产处置审核API测试失败")}})}function A(){return c(this,null,function*(){try{e.value="正在测试采购审核API（通用）...";const s=yield V("test-procurement-001");e.value=`采购审核API测试结果:
${JSON.stringify(s,null,2)}`,e.value+=`

📝 说明：采购审核不论是增值委托还是自主委托，都使用同一个接口`,s&&s.hgyEntrustOrder&&s.hgyProcurement?e.value+=`

✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyProcurement`:e.value+=`

❌ 数据结构异常：缺少必要字段`,l.success("采购审核API测试完成")}catch(s){e.value=`采购审核API测试失败: ${s}`,l.error("采购审核API测试失败")}})}function d(){return c(this,null,function*(){try{e.value=`正在测试统一API调用逻辑...

`,e.value+=`1. 测试增值委托竞价（entrustType=1, serviceType=1）
`;const s=yield v("test-001",1,1);e.value+=`   结果：调用了增值委托竞价API

`,e.value+=`2. 测试自主委托竞价（entrustType=2, serviceType=1）
`;const t=yield v("test-002",2,1);e.value+=`   结果：调用了自主委托竞价API

`,e.value+=`3. 测试增值委托资产处置（entrustType=1, serviceType=2）
`;const i=yield v("test-003",1,2);e.value+=`   结果：调用了资产处置通用API

`,e.value+=`4. 测试自主委托资产处置（entrustType=2, serviceType=2）
`;const _=yield v("test-004",2,2);e.value+=`   结果：调用了资产处置通用API

`,e.value+=`5. 测试增值委托采购（entrustType=1, serviceType=3）
`;const L=yield v("test-005",1,3);e.value+=`   结果：调用了采购通用API

`,e.value+=`6. 测试自主委托采购（entrustType=2, serviceType=3）
`;const Q=yield v("test-006",2,3);e.value+=`   结果：调用了采购通用API

`,e.value+=`✅ 统一API调用逻辑测试完成！
`,e.value+=`📝 总结：
`,e.value+=`   - 竞价委托：根据委托类型调用不同API
`,e.value+=`   - 资产处置：不论委托类型，都使用同一个API
`,e.value+="   - 采购信息：不论委托类型，都使用同一个API",l.success("统一API调用逻辑测试完成")}catch(s){e.value+=`
❌ 测试失败: ${s}`,l.error("统一API调用逻辑测试失败")}})}function m(){o.value={id:"test-appreciation-bidding-001",entrustType:1,serviceType:1,projectName:"测试增值委托竞价项目",relationUser:"张三",relationPhone:"13800138000",applicantUser:"李四",submitTime:"2024-01-15 10:30:00",status:2},n.value=!0,e.value="打开增值委托竞价审核弹窗"}function C(){o.value={id:"test-self-bidding-001",entrustType:2,serviceType:1,projectName:"测试自主委托竞价项目",relationUser:"王五",relationPhone:"13900139000",applicantUser:"赵六",submitTime:"2024-01-15 14:30:00",status:2},n.value=!0,e.value="打开自主委托竞价审核弹窗"}function b(){o.value={id:"test-appreciation-asset-001",entrustType:1,serviceType:2,projectName:"测试增值委托资产处置项目",relationUser:"孙七",relationPhone:"13700137000",applicantUser:"周八",submitTime:"2024-01-15 16:30:00",status:2},n.value=!0,e.value="打开增值委托资产处置审核弹窗"}function k(){o.value={id:"test-self-asset-001",entrustType:2,serviceType:2,projectName:"测试自主委托资产处置项目",relationUser:"吴九",relationPhone:"13600136000",applicantUser:"郑十",submitTime:"2024-01-15 18:30:00",status:2},n.value=!0,e.value="打开自主委托资产处置审核弹窗"}function U(){o.value={id:"test-appreciation-procurement-001",entrustType:1,serviceType:3,projectName:"测试增值委托采购项目",relationUser:"陈十一",relationPhone:"13500135000",applicantUser:"刘十二",submitTime:"2024-01-15 20:30:00",status:2},n.value=!0,e.value="打开增值委托采购审核弹窗"}function N(){o.value={id:"test-self-procurement-001",entrustType:2,serviceType:3,projectName:"测试自主委托采购项目",relationUser:"黄十三",relationPhone:"13400134000",applicantUser:"杨十四",submitTime:"2024-01-15 22:30:00",status:2},n.value=!0,e.value="打开自主委托采购审核弹窗"}function g(){n.value=!1,o.value=null}function w(){g(),e.value+=`

✅ 审核操作完成`,l.success("审核操作完成")}return(s,t)=>{const i=B("a-button");return $(),O("div",R,[t[15]||(t[15]=p("h2",null,"审核功能测试",-1)),p("div",q,[t[6]||(t[6]=p("h3",null,"API接口测试",-1)),p("div",G,[r(i,{type:"primary",onClick:y},{default:u(()=>t[1]||(t[1]=[a(" 测试增值委托竞价审核API ")])),_:1,__:[1]}),r(i,{type:"primary",onClick:f},{default:u(()=>t[2]||(t[2]=[a(" 测试自主委托竞价审核API ")])),_:1,__:[2]}),r(i,{type:"default",onClick:P},{default:u(()=>t[3]||(t[3]=[a(" 测试资产处置审核API（通用） ")])),_:1,__:[3]}),r(i,{type:"default",onClick:A},{default:u(()=>t[4]||(t[4]=[a(" 测试采购审核API（通用） ")])),_:1,__:[4]}),r(i,{type:"dashed",onClick:d},{default:u(()=>t[5]||(t[5]=[a(" 测试统一API调用逻辑 ")])),_:1,__:[5]})])]),p("div",z,[t[13]||(t[13]=p("h3",null,"审核弹窗测试",-1)),p("div",F,[r(i,{type:"primary",onClick:m},{default:u(()=>t[7]||(t[7]=[a(" 增值委托竞价审核 ")])),_:1,__:[7]}),r(i,{type:"primary",onClick:C},{default:u(()=>t[8]||(t[8]=[a(" 自主委托竞价审核 ")])),_:1,__:[8]}),r(i,{type:"default",onClick:b},{default:u(()=>t[9]||(t[9]=[a(" 增值委托资产处置审核 ")])),_:1,__:[9]}),r(i,{type:"default",onClick:k},{default:u(()=>t[10]||(t[10]=[a(" 自主委托资产处置审核 ")])),_:1,__:[10]}),r(i,{type:"default",onClick:U},{default:u(()=>t[11]||(t[11]=[a(" 增值委托采购审核 ")])),_:1,__:[11]}),r(i,{type:"default",onClick:N},{default:u(()=>t[12]||(t[12]=[a(" 自主委托采购审核 ")])),_:1,__:[12]})])]),p("div",H,[t[14]||(t[14]=p("h3",null,"测试结果",-1)),p("pre",null,h(e.value),1)]),r(D(E),{open:n.value,"onUpdate:open":t[0]||(t[0]=_=>n.value=_),record:o.value,onClose:g,onSuccess:w},null,8,["open","record"])])}}}),me=M(K,[["__scopeId","data-v-7ca45005"]]);export{me as default};
