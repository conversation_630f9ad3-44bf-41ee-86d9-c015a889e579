import{a7 as m,a6 as u,L as i,ah as B}from"./antd-vue-vendor-me9YkNVC.js";import{d as L,ag as n,aB as d,ar as p,as as r,aD as o,at as s,k as l,aq as f,F as y,aC as N,au as c,G as g,ah as v}from"./vue-vendor-dy9k-Yad.js";import{c as V,a as W}from"./index-CCWaWN5g.js";import{cardList as b}from"./data-DOjTOWVF.js";import{P as z}from"./index-CtJ0w2CP.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const D=L({components:{Icon:V,Progress:B,PageWrapper:z,[i.name]:i,[i.Item.name]:i.Item,AListItemMeta:i.Item.Meta,[u.name]:u,[m.name]:m},setup(){return{prefixCls:"list-basic",list:b,pagination:{show:!0,pageSize:3}}}}),F={key:0,class:"extra"},M={class:"description"},S={class:"info"},q={class:"progress"};function A(e,a,E,G,O,R){const _=n("a-col"),C=n("a-row"),$=n("Icon"),x=n("Progress"),I=n("a-list-item-meta"),k=n("a-list-item"),P=n("a-list"),w=n("PageWrapper");return p(),d(w,{class:r(e.prefixCls),title:"标准列表"},{default:o(()=>[s("div",{class:r(`${e.prefixCls}__top`)},[l(C,{gutter:12},{default:o(()=>[l(_,{span:8,class:r(`${e.prefixCls}__top-col`)},{default:o(()=>a[0]||(a[0]=[s("div",null,"我的待办",-1),s("p",null,"8个任务",-1)])),_:1,__:[0]},8,["class"]),l(_,{span:8,class:r(`${e.prefixCls}__top-col`)},{default:o(()=>a[1]||(a[1]=[s("div",null,"本周任务平均处理时间",-1),s("p",null,"32分钟",-1)])),_:1,__:[1]},8,["class"]),l(_,{span:8,class:r(`${e.prefixCls}__top-col`)},{default:o(()=>a[2]||(a[2]=[s("div",null,"本周完成任务数",-1),s("p",null,"24个任务",-1)])),_:1,__:[2]},8,["class"])]),_:1})],2),s("div",{class:r(`${e.prefixCls}__content`)},[l(P,{pagination:e.pagination},{default:o(()=>[(p(!0),f(y,null,N(e.list,t=>(p(),d(k,{key:t.id,class:"list"},{default:o(()=>[l(I,null,{avatar:o(()=>[t.icon?(p(),d($,{key:0,class:"icon",icon:t.icon,color:t.color},null,8,["icon","color"])):v("",!0)]),title:o(()=>[s("span",null,c(t.title),1),t.extra?(p(),f("div",F,c(t.extra),1)):v("",!0)]),description:o(()=>[s("div",M,c(t.description),1),s("div",S,[s("div",null,[a[3]||(a[3]=s("span",null,"Owner",-1)),g(c(t.author),1)]),s("div",null,[a[4]||(a[4]=s("span",null,"开始时间",-1)),g(c(t.datetime),1)])]),s("div",q,[l(x,{percent:t.percent,status:"active"},null,8,["percent"])])]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["pagination"])],2)]),_:1},8,["class"])}const ss=W(D,[["render",A],["__scopeId","data-v-7011cfb0"]]);export{ss as default};
