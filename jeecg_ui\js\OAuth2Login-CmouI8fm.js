import{d as m,f as u,ap as v,aq as _,ar as y}from"./vue-vendor-dy9k-Yad.js";import{c1 as l,r as c,bN as d,c2 as p,ah as T,u as L,N as O,bf as E,j as x,c3 as q,aZ as w,bo as N,c4 as S}from"./index-CCWaWN5g.js";import{d as b}from"./index-5UjQUK5f.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const W=m({__name:"OAuth2Login",setup(B){const g=u(l()),t=u({thirdApp:!1,wxWork:!1,dingtalk:!1}),{currentRoute:f}=v(),a=f.value;l()||c.replace({path:d.BASE_LOGIN,query:a.query}),g.value&&h();function h(){/wxwork/i.test(navigator.userAgent)&&(t.value.thirdApp=!0,t.value.wxWork=!0),/dingtalk/i.test(navigator.userAgent)&&(t.value.thirdApp=!0,t.value.dingtalk=!0),k()}function k(){if(t.value.thirdApp)if(a.query.oauth2LoginToken){let s=a.query.oauth2LoginToken;A({token:s,thirdType:a.query.thirdType,tenantId:N})}else t.value.wxWork?p("wechat_enterprise"):t.value.dingtalk&&I()}function A(s){const i=T(),{notification:e}=L(),{t:o}=O();i.ThirdLogin(s).then(n=>{n&&n.userInfo?e.success({message:o("sys.login.loginSuccessTitle"),description:`${o("sys.login.loginSuccessDesc")}: ${n.userInfo.realname}`,duration:3}):e.error({message:o("sys.login.errorTip"),description:((n.response||{}).data||{}).message||n.message||o("sys.login.networkExceptionMsg"),duration:4})})}function I(){let i=`/sys/thirdLogin/get/corpId/clientId?tenantId=${E(S)||0}`;x.get({url:i},{isTransformResponse:!1}).then(e=>{e.success&&e.result&&e.result.corpId&&e.result.clientId?b.requestAuthCode({corpId:e.result.corpId,clientId:e.result.clientId}).then(o=>{let{code:n}=o;q(n)}):r()}).catch(e=>{r()})}function r(){w()?c.replace({path:d.BASE_HOME}):p("dingtalk")}return(s,i)=>(y(),_("div"))}});export{W as default};
