import{h as a,b as f,e as p,u as k}from"./vue-vendor-dy9k-Yad.js";import{D as l,ah as T,B as g,v as S}from"./index-CCWaWN5g.js";import{u as d}from"./lock-CRalNsZJ.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function y(){const{getLockTime:u}=l(),c=d(),t=T(),i=g();let r;function e(){window.clearTimeout(r)}function n(){if(!t.getToken){e();return}const o=i.getProjectConfig.lockTime;if(!o||o<1){e();return}e(),r=setTimeout(()=>{m()},o*60*1e3)}function m(){c.setLockInfo({isLock:!0,pwd:void 0})}a(o=>{t.getToken?n():e(),o(()=>{e()})}),f(()=>{e()});const s=S(n,2e3);return p(()=>k(u)?{onKeyup:s,onMousemove:s}:(e(),{}))}export{y as useLockPage};
