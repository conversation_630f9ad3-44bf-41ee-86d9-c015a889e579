var k=(f,n,a)=>new Promise((t,i)=>{var L=e=>{try{l(a.next(e))}catch(r){i(r)}},u=e=>{try{l(a.throw(e))}catch(r){i(r)}},l=e=>e.done?t(e.value):Promise.resolve(e.value).then(L,u);l((a=a.apply(f,n)).next())});import{f as v,ag as m,aq as d,ar as c,k as p,aD as w,at as o,F as y,aC as M,au as z,ah as g}from"./vue-vendor-dy9k-Yad.js";import{I as B}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{p as b}from"./AiApp.api-CU5CuCbf.js";import{ac as q,a as R}from"./index-CCWaWN5g.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const I={name:"AiAppGeneratedPrompt",components:{BasicModal:B},emits:["ok","register"],setup(f,{emit:n}){const a=v(""),t=v(!1),i=v(""),L=v([{name:"python代码助手",value:"python",icon:"ant-design:code-outlined"},{name:"翻译器",value:"translator",icon:"ant-design:translation-outlined"},{name:"会议助手",value:"meeting",icon:"ant-design:team-outlined"},{name:"润色文章",value:"article",icon:"ant-design:profile-outlined"},{name:"sql生成器",value:"sql",icon:"ant-design:console-sql-outlined"},{name:"旅行规划师",value:"travel",icon:"ant-design:car-outlined"},{name:"linux专家",value:"linux",icon:"ant-design:fund-projection-screen-outlined"},{name:"内容提炼器",value:"content",icon:"ant-design:read-outlined"}]),u=v({python:"你是一个python专家，可以帮助用户编写和纠错代码。",translator:"一个可以将多种语言翻译为中文的翻译器。",meeting:"将会议内容提炼总结，包括讨论主题、关键要点和待办事项。",article:"用高超的编辑技巧改进我的文章。",sql:"根据用户的描述，生成sql语句，要支持引导用户提供表结构",travel:"你是一个旅行规划师，擅长帮助用户轻松规划他们的旅行",linux:"你是一个linux专家，擅长解决各种linux相关的问题。",content:"你是一个阅读理解大师，可以阅读用户提供的文章，并提炼主要内容输出给用户。"}),[l,{closeModal:e,setModalProps:r}]=q(C=>k(null,null,function*(){i.value="",t.value=!1,a.value="",r({height:500})}));function _(){return k(this,null,function*(){n("ok",i.value),x()})}function h(){i.value="",t.value=!0,b({prompt:a.value}).then(C=>{C.success&&(i.value=C.result),t.value=!1}).catch(()=>{t.value=!1})}function s(C){a.value=u.value[C]}function x(){e()}return{registerModal:l,handleOk:_,handleCancel:x,prompt:a,generatedPrompt:h,instructionsList:L,loading:t,instructionsClick:s,content:i}}},O={class:"p-2"},N={class:"prompt"},P={class:"prompt-left"},A={class:"instructions"},F=["onClick"],S={class:"instructions-name"},V={class:"prompt-left-textarea"},D={key:0,style:{"align-items":"center",display:"flex"}},G={class:"prompt-right"},U={key:0},j={key:1},E={key:2};function T(f,n,a,t,i,L){const u=m("a-divider"),l=m("Icon"),e=m("a-textarea"),r=m("a-button"),_=m("a-spin"),h=m("BasicModal");return c(),d("div",O,[p(h,{destroyOnClose:"",onRegister:t.registerModal,canFullscreen:!1,width:"1000px",onOk:t.handleOk,onCancel:t.handleCancel,okText:"替换",wrapClassName:"ai-rag-generate-prompt-modal"},{default:w(()=>[o("div",N,[o("div",P,[n[4]||(n[4]=o("div",{class:"prompt-left-title"},"提示词生成器",-1)),n[5]||(n[5]=o("div",{class:"prompt-left-desc"},"提示词生成器使用配置的模型来优化提示词，以获得更高的质量和更好的结构。请写出清晰详细的说明。",-1)),p(u),n[6]||(n[6]=o("div",{class:"prompt-left-try"},[o("div",{class:"prompt-left-try-title"},"试一试")],-1)),o("div",A,[(c(!0),d(y,null,M(t.instructionsList,s=>(c(),d("div",{class:"instructions-content",onClick:x=>t.instructionsClick(s.value)},[p(l,{icon:s.icon,size:"14",color:"#676f83"},null,8,["icon"]),o("div",S,z(s.name),1)],8,F))),256))]),o("div",V,[n[2]||(n[2]=o("div",{class:"command"},"指令",-1)),p(e,{value:t.prompt,"onUpdate:value":n[0]||(n[0]=s=>t.prompt=s),autoSize:{minRows:8,maxRows:8}},null,8,["value"])]),p(r,{onClick:t.generatedPrompt,class:"prompt-left-btn",type:"primary",loading:t.loading},{default:w(()=>[t.loading?g("",!0):(c(),d("span",D,n[3]||(n[3]=[o("svg",{width:"1em",height:"1em",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},[o("path",{d:"M18.9839 1.85931C19.1612 1.38023 19.8388 1.38023 20.0161 1.85931L20.5021 3.17278C20.5578 3.3234 20.6766 3.44216 20.8272 3.49789L22.1407 3.98392C22.6198 4.1612 22.6198 4.8388 22.1407 5.01608L20.8272 5.50211C20.6766 5.55784 20.5578 5.6766 20.5021 5.82722L20.0161 7.14069C19.8388 7.61977 19.1612 7.61977 18.9839 7.14069L18.4979 5.82722C18.4422 5.6766 18.3234 5.55784 18.1728 5.50211L16.8593 5.01608C16.3802 4.8388 16.3802 4.1612 16.8593 3.98392L18.1728 3.49789C18.3234 3.44216 18.4422 3.3234 18.4979 3.17278L18.9839 1.85931zM13.5482 4.07793C13.0164 2.64069 10.9836 2.64069 10.4518 4.07793L8.99368 8.01834C8.82648 8.47021 8.47021 8.82648 8.01834 8.99368L4.07793 10.4518C2.64069 10.9836 2.64069 13.0164 4.07793 13.5482L8.01834 15.0063C8.47021 15.1735 8.82648 15.5298 8.99368 15.9817L10.4518 19.9221C10.9836 21.3593 13.0164 21.3593 13.5482 19.9221L15.0063 15.9817C15.1735 15.5298 15.5298 15.1735 15.9817 15.0063L19.9221 13.5482C21.3593 13.0164 21.3593 10.9836 19.9221 10.4518L15.9817 8.99368C15.5298 8.82648 15.1735 8.47021 15.0063 8.01834L13.5482 4.07793zM5.01608 16.8593C4.8388 16.3802 4.1612 16.3802 3.98392 16.8593L3.49789 18.1728C3.44216 18.3234 3.3234 18.4422 3.17278 18.4979L1.85931 18.9839C1.38023 19.1612 1.38023 19.8388 1.85931 20.0161L3.17278 20.5021C3.3234 20.5578 3.44216 20.6766 3.49789 20.8272L3.98392 22.1407C4.1612 22.6198 4.8388 22.6198 5.01608 22.1407L5.50211 20.8272C5.55784 20.6766 5.6766 20.5578 5.82722 20.5021L7.14069 20.0161C7.61977 19.8388 7.61977 19.1612 7.14069 18.9839L5.82722 18.4979C5.6766 18.4422 5.55784 18.3234 5.50211 18.1728L5.01608 16.8593z"})],-1),o("span",{style:{"margin-left":"4px"}},"生成",-1)])))]),_:1},8,["onClick","loading"])]),o("div",G,[!t.loading&&!t.content?(c(),d("div",U,n[7]||(n[7]=[o("svg",{width:"6em",height:"6em",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},[o("path",{d:"M18.9839 1.85931C19.1612 1.38023 19.8388 1.38023 20.0161 1.85931L20.5021 3.17278C20.5578 3.3234 20.6766 3.44216 20.8272 3.49789L22.1407 3.98392C22.6198 4.1612 22.6198 4.8388 22.1407 5.01608L20.8272 5.50211C20.6766 5.55784 20.5578 5.6766 20.5021 5.82722L20.0161 7.14069C19.8388 7.61977 19.1612 7.61977 18.9839 7.14069L18.4979 5.82722C18.4422 5.6766 18.3234 5.55784 18.1728 5.50211L16.8593 5.01608C16.3802 4.8388 16.3802 4.1612 16.8593 3.98392L18.1728 3.49789C18.3234 3.44216 18.4422 3.3234 18.4979 3.17278L18.9839 1.85931zM13.5482 4.07793C13.0164 2.64069 10.9836 2.64069 10.4518 4.07793L8.99368 8.01834C8.82648 8.47021 8.47021 8.82648 8.01834 8.99368L4.07793 10.4518C2.64069 10.9836 2.64069 13.0164 4.07793 13.5482L8.01834 15.0063C8.47021 15.1735 8.82648 15.5298 8.99368 15.9817L10.4518 19.9221C10.9836 21.3593 13.0164 21.3593 13.5482 19.9221L15.0063 15.9817C15.1735 15.5298 15.5298 15.1735 15.9817 15.0063L19.9221 13.5482C21.3593 13.0164 21.3593 10.9836 19.9221 10.4518L15.9817 8.99368C15.5298 8.82648 15.1735 8.47021 15.0063 8.01834L13.5482 4.07793zM5.01608 16.8593C4.8388 16.3802 4.1612 16.3802 3.98392 16.8593L3.49789 18.1728C3.44216 18.3234 3.3234 18.4422 3.17278 18.4979L1.85931 18.9839C1.38023 19.1612 1.38023 19.8388 1.85931 20.0161L3.17278 20.5021C3.3234 20.5578 3.44216 20.6766 3.49789 20.8272L3.98392 22.1407C4.1612 22.6198 4.8388 22.6198 5.01608 22.1407L5.50211 20.8272C5.55784 20.6766 5.6766 20.5578 5.82722 20.5021L7.14069 20.0161C7.61977 19.8388 7.61977 19.1612 7.14069 18.9839L5.82722 18.4979C5.6766 18.4422 5.55784 18.3234 5.50211 18.1728L5.01608 16.8593z"})],-1),o("div",null,"在左侧描述您的用例，",-1),o("div",null,"编排预览将在此处显示。",-1)]))):g("",!0),t.loading?(c(),d("div",j,[p(_,{spinning:t.loading,tip:"为您编排应用程序中…"},null,8,["spinning"])])):g("",!0),t.content?(c(),d("div",E,[p(e,{value:t.content,"onUpdate:value":n[1]||(n[1]=s=>t.content=s),autoSize:{minRows:18,maxRows:18}},null,8,["value"])])):g("",!0)])])]),_:1},8,["onRegister","onOk","onCancel"])])}const r1=R(I,[["render",T],["__scopeId","data-v-bebb3b97"]]);export{r1 as default};
