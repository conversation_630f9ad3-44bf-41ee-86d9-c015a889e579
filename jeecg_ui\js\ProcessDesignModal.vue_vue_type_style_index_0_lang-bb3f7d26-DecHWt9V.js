import{d as se,f as g,e as A,p as ue,u as y,n as O,ag as k,aq as b,ar as w,F as ce,k as c,aJ as ge,aK as de,aD as p,aB as ve,aA as Ae,aE as T,at as B,G as j}from"./vue-vendor-dy9k-Yad.js";import{B as pe}from"./index-Diw57m_E.js";import{a9 as Ie,u as fe,ac as Ee,cr as R,ad as Ce,d as me}from"./index-CCWaWN5g.js";import{f as Qe,P as he}from"./ProcessAddBeforeModal-5ab4ccac-DlZK87E0.js";import{T as ye}from"./TitleEditor.vue_vue_type_style_index_0_lang-f6eddb57-D8gWOKwz.js";import{z as ke,p as Be}from"./WorkflowView.vue_vue_type_style_index_0_lang-8fd6a0cf-BV4bcPm7.js";var xe=Object.defineProperty,Ue=Object.defineProperties,Se=Object.getOwnPropertyDescriptors,W=Object.getOwnPropertySymbols,De=Object.prototype.hasOwnProperty,Je=Object.prototype.propertyIsEnumerable,N=(i,o,n)=>o in i?xe(i,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[o]=n,z=(i,o)=>{for(var n in o||(o={}))De.call(o,n)&&N(i,n,o[n]);if(W)for(var n of W(o))Je.call(o,n)&&N(i,n,o[n]);return i},Me=(i,o)=>Ue(i,Se(o)),x=(i,o,n)=>new Promise((U,C)=>{var m=a=>{try{s(n.next(a))}catch(d){C(d)}},S=a=>{try{s(n.throw(a))}catch(d){C(d)}},s=a=>a.done?U(a.value):Promise.resolve(a.value).then(m,S);s((n=n.apply(i,o)).next())});const Pe="data:image/png;base64,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",we={class:"process-header-container"},Ke={class:"header-icon-container"},Le=["src"],G=55,We=se({__name:"ProcessDesignModal",emits:["register","close"],setup(i,{expose:o,emit:n}){const U=n,C=Ie(),{createMessage:m,createConfirmSync:S}=fe(),s=g(),a=g(),d=g({nodes:[],edges:[]}),I=g(!1),f=g(!0),Q=g(!1),H=A(()=>{var e;return(e=a.value)==null?void 0:e.id});ue("$flowId",H);const E=A({get:()=>{var e;return(e=a.value)==null?void 0:e.name},set:e=>a.value.name=e}),X=A(()=>{var e;return ie((e=a.value)==null?void 0:e.icon)}),[Y,{closeModal:Z}]=Ee(_),q=A(()=>{let e={title:E.value,width:800,zIndex:999,bodyStyle:{padding:"0"},wrapClassName:"ai-rag-process-design-modal",confirmLoading:I.value,footer:null,draggable:!1,maskClosable:!1,canFullscreen:!1,destroyOnClose:!0,defaultFullscreen:!0,closeFunc:ee,modalHeaderHeight:G-26};return Me(z(z({keyboard:!1},e),y(C)),{onCancel:F,"onUpdate:open":le,onRegister:Y})}),h=A(()=>({data:d.value,width:window.innerWidth,height:window.innerHeight-G})),K=g(),V=A(()=>({title:E.value,"onUpdate:title":e=>E.value=e,handleCustomEdit(){re()},promptProps:{title:"修改流程名称",defaultValue:E.value,placeholder:"请输入流程名称",rules:[{required:!0,message:"请输入流程名称！"},{pattern:/^(?!未命名$).+$/,message:"请输入流程名称！"},{max:12,message:"流程名称不能超过12个字符！"}]}}));let D="";function _(e){return x(this,null,function*(){var l;if(f.value=!0,a.value=e==null?void 0:e.record,Q.value=!!y(e==null?void 0:e.isUpdate),!Q.value){const t=Math.round(h.value.height/2);a.value={name:e.record.name?e.record.name:"未命名",descr:e.record.descr?e.record.descr:"",icon:e.record.icon?e.record.icon:"",design:JSON.stringify({nodes:[ke({x:300,y:t})],edges:[]}),chain:""}}d.value=JSON.parse(a.value.design),yield O(),yield R(300),f.value=!1,yield O(),yield R(300),D=JSON.stringify((l=s.value)==null?void 0:l.getGraphData())})}function $(){s.value&&s.value.doDebugRun()}function J(e){return x(this,null,function*(){var l,t;if(s.value)try{const r=s.value.validateData();if(((l=e==null?void 0:e.needName)==null||l)&&E.value==="未命名"){K.value.showPrompt(()=>L(r,e),()=>{var u;return(u=e==null?void 0:e.onError)==null?void 0:u.call(e)});return}yield L(r,e)}catch(r){m.error((r==null?void 0:r.message)||r),(t=e==null?void 0:e.onError)==null||t.call(e,r)}})}function L(e,l){return x(this,null,function*(){var t;I.value=!0;try{a.value.design=JSON.stringify(e);try{a.value.chain=s.value.getLiteFlowData()}catch(P){m.error((P==null?void 0:P.message)||P);return}const r=l==null?void 0:l.silent,u=(t=l==null?void 0:l.saveFn)!=null?t:Qe.saveOrUpdate,v=l==null?void 0:l.savedFb,M=yield u(a.value,Q.value,{silent:r});M&&(a.value=M,Q.value=!0,D=a.value.design,v&&v({result:M}))}finally{I.value=!1}})}function ee(){return x(this,null,function*(){var e;const l=(e=s.value)==null?void 0:e.getGraphData();return JSON.stringify(l)!==D?yield S({title:"尚未保存",content:"流程已修改，并且尚未保存，确定要关闭吗？",okText:"确定",cancelText:"取消"}):!0})}function F(){Z()}function ae(){f.value=!0,U("close")}function le(e){e||ae()}o({onSubmit:J});const[oe,ne]=Ce();function re(){var e,l,t,r,u;const v=!!((e=a.value)!=null&&e.id);ne.openModal(!0,{isUpdate:v,record:{id:(l=a.value)==null?void 0:l.id,name:(t=a.value)==null?void 0:t.name,icon:(r=a.value)==null?void 0:r.icon,descr:(u=a.value)==null?void 0:u.descr}})}function te(e){a.value||(a.value={}),a.value.name=e.name,a.value.descr=e.descr,a.value.icon=e.icon}function ie(e){return e?me(e):Pe}return(e,l)=>{const t=k("a-button"),r=k("a-space"),u=k("a-skeleton"),v=k("a-spin");return w(),b(ce,null,[c(y(pe),ge(de(q.value)),{title:p(()=>[B("div",we,[B("div",Ke,[B("img",{src:X.value},null,8,Le)]),l[5]||(l[5]=B("div",{class:"header-icon-placeholder"},null,-1)),c(ye,T({ref_key:"titleEditorRef",ref:K},V.value),null,16),c(r,{class:"action-container"},{default:p(()=>[c(t,{preIcon:"codicon:debug-start",onClick:$},{default:p(()=>l[3]||(l[3]=[j("调试")])),_:1}),c(t,{type:"primary",loading:I.value,preIcon:"ant-design:save",onClick:l[0]||(l[0]=()=>J())},{default:p(()=>l[4]||(l[4]=[j("保存 ")])),_:1},8,["loading"])]),_:1})])]),default:p(()=>[c(v,{spinning:I.value&&!f.value},{default:p(()=>[f.value?(w(),ve(u,{key:0,active:!0})):(w(),b("div",{key:1,class:"ai-rag-lf-container",style:Ae({width:h.value.width+"px",height:h.value.height+"px"})},[c(Be,T({ref_key:"flowRef",ref:s},h.value,{onSave:l[1]||(l[1]=()=>J()),onClose:l[2]||(l[2]=()=>F())}),null,16)],4))]),_:1},8,["spinning"])]),_:1},16),c(he,{onRegister:y(oe),onOk:te},null,8,["onRegister"])],64)}}});export{Pe as M,We as b};
