var B=(e,a,i)=>new Promise((_,o)=>{var y=u=>{try{n(i.next(u))}catch(d){o(d)}},v=u=>{try{n(i.throw(u))}catch(d){o(d)}},n=u=>u.done?_(u.value):Promise.resolve(u.value).then(y,v);n((i=i.apply(e,a)).next())});import{d as R,f as p,r as x,e as ee,u as l,ag as g,aB as S,ar as b,aE as D,aD as s,k as C,ah as z,aH as N,as as U,aq as F,F as q,aC as A,G as T,au as H,at as L}from"./vue-vendor-dy9k-Yad.js";import{a3 as ae}from"./antd-vue-vendor-me9YkNVC.js";import{B as te}from"./index-Diw57m_E.js";import{ah as se,ac as ne,j as oe,u as re,a as le}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const ie=R({name:"loginSelect",components:{Avatar:ae,BasicModal:te},emits:["success","register"],setup(e,{emit:a}){const i=se(),{notification:_}=re(),o=p(!1),y=p([]),v=p(""),n=p(!1),u=p([]),d=p(""),k=p(!1),h=p(""),M=p(),f=x({orgCode:void 0,tenantId:null}),r={maskClosable:!1,closable:!1,canFullscreen:!1,width:"500px",minHeight:20,maxHeight:20},[V,{closeModal:$}]=ne(),E=ee(()=>{if(l(n)&&l(o))return"请选择租户和部门";if(l(n)&&!l(o))return"请选择部门";if(!l(n)&&l(o))return"请选择租户"}),P=p({tenantId:[{required:l(o),type:"number",message:"请选择租户",trigger:"change"}],orgCode:[{required:l(n),message:"请选择部门",trigger:"change"}]}),j={labelCol:{span:4},wrapperCol:{span:18}};function G(t){var I,c;if((I=t.userInfo)!=null&&I.orgCode&&((c=t.userInfo)==null?void 0:c.orgCode)!==""){n.value=!1;return}let m=t.multi_depart;m==0?(_.warn({message:"提示",description:"您尚未归属部门,请确认账号信息",duration:3}),n.value=!1):m==2?(n.value=!0,u.value=t.departs):n.value=!1}function J(t){var I,c;if((I=t.userInfo)!=null&&I.loginTenantId&&((c=t.userInfo)==null?void 0:c.loginTenantId)!==0){o.value=!1;return}let m=t.tenantList;Array.isArray(m)&&(m.length===0?(o.value=!1,i.setTenant(f.tenantId)):m.length===1?(f.tenantId=m[0].id,o.value=!1,i.setTenant(f.tenantId)):(o.value=!0,y.value=m))}function K(){if(l(o)&&!f.tenantId)return v.value="error",!1;if(l(n)&&!f.orgCode)return d.value="error",!1;M.value.validate().then(()=>{O().then(()=>{i.setTenant(f.tenantId),a("success")}).catch(t=>{}).finally(()=>{W()})}).catch(t=>{})}function O(){return new Promise((t,m)=>{if(!l(n)&&!l(o))t();else{let I={orgCode:f.orgCode,loginTenantId:f.tenantId,username:l(h)};oe.put({url:"/sys/selectDepart",params:I}).then(c=>{c.userInfo?(i.setUserInfo(c.userInfo),t()):(Q(c),i.logout(),m())})}})}function Q(t){_.error({message:"登录失败",description:((t.response||{}).data||{}).message||t.message||"请求出现错误，请稍后再试",duration:4})}function W(){$(),w()}function X(t){return B(this,null,function*(){t&&(h.value=i.username,yield w(),yield G(t),yield J(t),!l(n)&&!l(o)?a("success",i.getUserInfo):k.value=!0),t.isLogin=!1,i.setLoginInfo(t)})}function w(){y.value=[],v.value="",u.value=[],d.value=""}function Y(t){v.value=""}function Z(t){d.value=""}return{registerModal:V,visible:k,tenantList:y,isMultiTenant:o,validate_status:v,isMultiDepart:n,departList:u,validate_status1:d,formState:f,rules:P,layout:j,formRef:M,currTitle:E,config:r,handleTenantChange:Y,handleDepartChange:Z,show:X,handleSubmit:K}}});function ue(e,a,i,_,o,y){const v=g("a-avatar"),n=g("a-tooltip"),u=g("a-select-option"),d=g("a-select"),k=g("a-form-item"),h=g("a-form"),M=g("a-button"),f=g("BasicModal");return b(),S(f,D(e.config,{onRegister:e.registerModal,title:e.currTitle,wrapClassName:"loginSelectModal",visible:e.visible,"onUpdate:visible":a[2]||(a[2]=r=>e.visible=r)}),{footer:s(()=>[C(M,{onClick:e.handleSubmit,type:"primary"},{default:s(()=>a[9]||(a[9]=[T("确认")])),_:1,__:[9]},8,["onClick"])]),default:s(()=>[C(h,D({ref:"formRef",model:e.formState,rules:e.rules},e.layout,{colon:!1,class:"loginSelectForm"}),{default:s(()=>[e.isMultiTenant?(b(),S(k,{key:0,name:"tenantId","validate-status":e.validate_status},N({label:s(()=>[C(n,{placement:"topLeft"},{title:s(()=>a[3]||(a[3]=[L("span",null,"您隶属于多租户，请选择登录租户",-1)])),default:s(()=>[C(v,{style:{"background-color":"#87d068"},size:30},{default:s(()=>a[4]||(a[4]=[T(" 租户 ")])),_:1,__:[4]})]),_:1})]),default:s(()=>[C(d,{value:e.formState.tenantId,"onUpdate:value":a[0]||(a[0]=r=>e.formState.tenantId=r),onChange:e.handleTenantChange,placeholder:"请选择登录租户",class:U({"valid-error":e.validate_status=="error"})},{default:s(()=>[(b(!0),F(q,null,A(e.tenantList,r=>(b(),S(u,{key:r.id,value:r.id},{default:s(()=>[T(H(r.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange","class"])]),_:2},[e.validate_status=="error"?{name:"extra",fn:s(()=>[a[5]||(a[5]=L("span",{style:{color:"#ed6f6f"}},"请选择登录租户",-1))]),key:"0"}:void 0]),1032,["validate-status"])):z("",!0),e.isMultiDepart?(b(),S(k,{key:1,"validate-status":e.validate_status1,colon:!1},N({label:s(()=>[C(n,{placement:"topLeft"},{title:s(()=>a[6]||(a[6]=[L("span",null,"您隶属于多部门，请选择登录部门",-1)])),default:s(()=>[C(v,{style:{"background-color":"rgb(104, 208, 203)"},size:30},{default:s(()=>a[7]||(a[7]=[T(" 部门 ")])),_:1,__:[7]})]),_:1})]),default:s(()=>[C(d,{value:e.formState.orgCode,"onUpdate:value":a[1]||(a[1]=r=>e.formState.orgCode=r),onChange:e.handleDepartChange,placeholder:"请选择登录部门",class:U({"valid-error":e.validate_status1=="error"})},{default:s(()=>[(b(!0),F(q,null,A(e.departList,r=>(b(),S(u,{key:r.orgCode,value:r.orgCode},{default:s(()=>[T(H(r.departName),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","onChange","class"])]),_:2},[e.validate_status1=="error"?{name:"extra",fn:s(()=>[a[8]||(a[8]=L("span",{style:{color:"#ed6f6f"}},"请选择登录部门",-1))]),key:"0"}:void 0]),1032,["validate-status"])):z("",!0)]),_:1},16,["model","rules"])]),_:1},16,["onRegister","title","visible"])}const Me=le(ie,[["render",ue],["__scopeId","data-v-456d5286"]]);export{Me as default};
