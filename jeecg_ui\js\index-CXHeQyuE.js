import{N as D,aW as _,P as O,Q as h,k as N,aX as w,Y as E,h as p,H as r,a as M,w as Y}from"./index-CCWaWN5g.js";import{d as b,f as I,w as S,aq as y,ar as B,au as H}from"./vue-vendor-dy9k-Yad.js";const c=1e3,l=c*60,u=l*60,T=u*24,$=b({name:"Time",props:{value:r.oneOfType([r.number,r.instanceOf(Date),r.string]).isRequired,step:r.number.def(60),mode:r.oneOf(["date","datetime","relative"]).def("relative")},setup(i){const m=I(""),{t:s}=D();_(d,i.step*c),S(()=>i.value,()=>{d()},{immediate:!0});function v(){const{value:e}=i;let n=0;if(O(e)){const o=e.toString().length>10?e:e*1e3;n=new Date(o).getTime()}else h(e)?n=new Date(e).getTime():N(e)&&(n=e.getTime());return n}function d(){const{mode:e,value:n}=i,o=v();e==="relative"?m.value=g(o):e==="datetime"?m.value=w(n):e==="date"&&(m.value=E(n))}function g(e){const n=new Date().getTime(),o=p(e).isBefore(n);let t=n-e;o||(t=-t);let a="",f=s(o?"component.time.before":"component.time.after");return t<c?a=s("component.time.just"):t<l?a=parseInt(t/c)+s("component.time.seconds")+f:t>=l&&t<u?a=Math.floor(t/l)+s("component.time.minutes")+f:t>=u&&t<T?a=Math.floor(t/u)+s("component.time.hours")+f:t>=T&&t<262386e4?a=Math.floor(t/T)+s("component.time.days")+f:t>=262386e4&&t<=3156786e4&&o?a=p(e).format("MM-DD-HH-mm"):a=p(e).format("YYYY"),a}return{date:m}}});function k(i,m,s,v,d,g){return B(),y("span",null,H(i.date),1)}const R=M($,[["render",k]]),q=Y(R);export{q as T};
