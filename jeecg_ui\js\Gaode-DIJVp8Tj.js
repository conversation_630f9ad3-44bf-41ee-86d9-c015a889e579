var c=(e,r,t)=>new Promise((o,n)=>{var i=a=>{try{p(t.next(a))}catch(s){n(s)}},f=a=>{try{p(t.throw(a))}catch(s){n(s)}},p=a=>a.done?o(a.value):Promise.resolve(a.value).then(i,f);p((t=t.apply(e,r)).next())});import{d,f as m,o as u,n as l,u as w,aq as h,ar as M,aA as _}from"./vue-vendor-dy9k-Yad.js";import{useScript as y}from"./useScript-C3Sw6r4a.js";import{a as A}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const g="https://webapi.amap.com/maps?v=2.0&key=06313eb9c6563b674a8fd789db0692c3",b=d({name:"AMap",props:{width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"}},setup(){const e=m(null),{toPromise:r}=y({src:g});function t(){return c(this,null,function*(){yield r(),yield l();const o=w(e);if(!o)return;const n=window.AMap;new n.Map(o,{zoom:11,center:[116.397428,39.90923],viewMode:"3D"})})}return u(()=>{t()}),{wrapRef:e}}});function k(e,r,t,o,n,i){return M(),h("div",{ref:"wrapRef",style:_({height:e.height,width:e.width})},null,4)}const B=A(b,[["render",k]]);export{B as default};
