import{d as l,f as t,r as c,o as u,aq as d,ar as m,at as p}from"./vue-vendor-dy9k-Yad.js";import{u as f}from"./index-CCWaWN5g.js";import{useECharts as h}from"./useECharts-BU6FzBZi.js";import"./echarts-D8q0NfgS.js";import{an as g}from"./renderers-CGMjx3X9.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useTimeout-CeTdFD_D.js";const A=l({__name:"gauge",props:{data:{}},setup(o){const a=o,v=t([]),s=t(null),{setOptions:r,echarts:i}=h(s),w=t(!1),{createMessage:_}=f(),e=c({series:[{type:"gauge",progress:{show:!0,width:18},axisLine:{lineStyle:{width:18}},axisTick:{show:!0},splitLine:{length:15,lineStyle:{width:2,color:"#999"}},axisLabel:{distance:25,color:"#999",fontSize:15},anchor:{show:!0,showAbove:!0,size:25,itemStyle:{borderWidth:10}},title:{},detail:{valueAnimation:!0,fontSize:50,formatter:"{value}%",offsetCenter:[0,"80%"]},data:[{value:70,name:"本地磁盘"}]}]});function n(){e.series[0].data[0].name=a.data.name,e.series[0].data[0].value=a.data.restPPT,r(e)}return u(()=>{i.use(g),n()}),(x,y)=>(m(),d("div",null,[p("div",{ref_key:"chartRef",ref:s,style:{width:"100%",height:"400px"}},null,512)]))}});export{A as default};
