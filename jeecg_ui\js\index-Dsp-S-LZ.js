var _=Object.defineProperty;var f=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var h=(n,a,t)=>a in n?_(n,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[a]=t,g=(n,a)=>{for(var t in a||(a={}))w.call(a,t)&&h(n,t,a[t]);if(f)for(var t of f(a))k.call(a,t)&&h(n,t,a[t]);return n};var m=(n,a,t)=>new Promise((i,c)=>{var e=r=>{try{s(t.next(r))}catch(u){c(u)}},o=r=>{try{s(t.throw(r))}catch(u){c(u)}},s=r=>r.done?i(r.value):Promise.resolve(r.value).then(e,o);s((t=t.apply(n,a)).next())});import{x as V,B as b,Q as B,w as E}from"./index-CCWaWN5g.js";import{d as C,f as M,w as y,n as v,h as N,u as j,o as D,b as F,aq as x,ar as O,e as J,k as T}from"./vue-vendor-dy9k-Yad.js";import{useWindowSizeFn as q}from"./useWindowSizeFn-DDbrQbks.js";import{d as z,C as W}from"./htmlmixed-CmvhkW5V.js";/* empty css             */import{a as P}from"./antd-vue-vendor-me9YkNVC.js";var X=z();const A=C({__name:"CodeMirror",props:{mode:{type:String,default:"application/json"},value:{type:String,default:""},readonly:{type:Boolean,default:!1}},emits:["change"],setup(n,{emit:a}){const t=n,i=a,c=M();let e;const o=V(u,100),s=b();y(()=>t.value,l=>m(null,null,function*(){yield v();const p=e==null?void 0:e.getValue();l!==p&&(e==null||e.setValue(l||""))}),{flush:"post"}),N(()=>{e==null||e.setOption("mode",t.mode)}),y(()=>s.getDarkMode,()=>m(null,null,function*(){r()}),{immediate:!0});function r(){var l;(l=j(e))==null||l.setOption("theme",s.getDarkMode==="light"?"idea":"material-palenight")}function u(){e==null||e.refresh()}function d(){return m(this,null,function*(){const l={autoCloseBrackets:!0,autoCloseTags:!0,foldGutter:!0,gutters:["CodeMirror-linenumbers"]};e=W(c.value,g({value:"",mode:t.mode,readOnly:t.readonly,tabSize:2,theme:"material-palenight",lineWrapping:!0,lineNumbers:!0},l)),e==null||e.setValue(t.value),r(),e==null||e.on("change",()=>{i("change",e==null?void 0:e.getValue())})})}return D(()=>m(null,null,function*(){yield v(),d(),q(o)})),F(()=>{e=null}),(l,p)=>(O(),x("div",{class:"relative !h-full w-full overflow-hidden",ref_key:"el",ref:c},null,512))}}),G={class:"h-full"},S={JSON:"application/json",html:"htmlmixed",js:"javascript"},H=C({__name:"CodeEditor",props:{value:{type:[Object,String]},mode:{type:String,default:S.JSON},readonly:{type:Boolean},autoFormat:{type:Boolean,default:!0}},emits:["change","update:value","format-error"],setup(n,{emit:a}){const t=n,i=a,c=J(()=>{const{value:o,mode:s,autoFormat:r}=t;if(!r||s!==S.JSON)return o;let u=o;if(B(o))try{u=JSON.parse(o)}catch(d){return i("format-error",o),o}return JSON.stringify(u,null,2)});function e(o){i("update:value",o),i("change",o)}return(o,s)=>(O(),x("div",G,[T(A,{value:c.value,onChange:e,mode:n.mode,readonly:n.readonly},null,8,["value","mode","readonly"])]))}}),Y=E(H);export{Y as C};
