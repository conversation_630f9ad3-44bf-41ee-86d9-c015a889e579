import{j as H}from"./index-CCWaWN5g.js";import{o as X,l as tt}from"./_commonjsHelpers-ce4d82cc-RqGMvybJ.js";var N=(h,_,d)=>new Promise((y,x)=>{var b=v=>{try{$(d.next(v))}catch(M){x(M)}},O=v=>{try{$(d.throw(v))}catch(M){x(M)}},$=v=>v.done?y(v.value):Promise.resolve(v.value).then(b,O);$((d=d.apply(h,_)).next())}),G={exports:{}};(function(h,_){(function(d,y){h.exports=y()})(X,function(){var d=1e3,y=6e4,x=36e5,b="millisecond",O="second",$="minute",v="hour",M="day",I="week",S="month",R="quarter",w="year",u="date",f="Invalid Date",k=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,A=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,J={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(i){var n=["th","st","nd","rd"],t=i%100;return"["+i+(n[(t-20)%10]||n[t]||n[0])+"]"}},P=function(i,n,t){var r=String(i);return!r||r.length>=n?i:""+Array(n+1-r.length).join(t)+i},K={s:P,z:function(i){var n=-i.utcOffset(),t=Math.abs(n),r=Math.floor(t/60),e=t%60;return(n<=0?"+":"-")+P(r,2,"0")+":"+P(e,2,"0")},m:function i(n,t){if(n.date()<t.date())return-i(t,n);var r=12*(t.year()-n.year())+(t.month()-n.month()),e=n.clone().add(r,S),s=t-e<0,a=n.clone().add(r+(s?-1:1),S);return+(-(r+(t-e)/(s?e-a:a-e))||0)},a:function(i){return i<0?Math.ceil(i)||0:Math.floor(i)},p:function(i){return{M:S,y:w,w:I,d:M,D:u,h:v,m:$,s:O,ms:b,Q:R}[i]||String(i||"").toLowerCase().replace(/s$/,"")},u:function(i){return i===void 0}},j="en",W={};W[j]=J;var q="$isDayjsObject",B=function(i){return i instanceof V||!(!i||!i[q])},Z=function i(n,t,r){var e;if(!n)return j;if(typeof n=="string"){var s=n.toLowerCase();W[s]&&(e=s),t&&(W[s]=t,e=s);var a=n.split("-");if(!e&&a.length>1)return i(a[0])}else{var c=n.name;W[c]=n,e=c}return!r&&e&&(j=e),e||!r&&j},m=function(i,n){if(B(i))return i.clone();var t=typeof n=="object"?n:{};return t.date=i,t.args=arguments,new V(t)},o=K;o.l=Z,o.i=B,o.w=function(i,n){return m(i,{locale:n.$L,utc:n.$u,x:n.$x,$offset:n.$offset})};var V=function(){function i(t){this.$L=Z(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[q]=!0}var n=i.prototype;return n.parse=function(t){this.$d=function(r){var e=r.date,s=r.utc;if(e===null)return new Date(NaN);if(o.u(e))return new Date;if(e instanceof Date)return new Date(e);if(typeof e=="string"&&!/Z$/i.test(e)){var a=e.match(k);if(a){var c=a[2]-1||0,l=(a[7]||"0").substring(0,3);return s?new Date(Date.UTC(a[1],c,a[3]||1,a[4]||0,a[5]||0,a[6]||0,l)):new Date(a[1],c,a[3]||1,a[4]||0,a[5]||0,a[6]||0,l)}}return new Date(e)}(t),this.init()},n.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},n.$utils=function(){return o},n.isValid=function(){return this.$d.toString()!==f},n.isSame=function(t,r){var e=m(t);return this.startOf(r)<=e&&e<=this.endOf(r)},n.isAfter=function(t,r){return m(t)<this.startOf(r)},n.isBefore=function(t,r){return this.endOf(r)<m(t)},n.$g=function(t,r,e){return o.u(t)?this[r]:this.set(e,t)},n.unix=function(){return Math.floor(this.valueOf()/1e3)},n.valueOf=function(){return this.$d.getTime()},n.startOf=function(t,r){var e=this,s=!!o.u(r)||r,a=o.p(t),c=function(Y,D){var F=o.w(e.$u?Date.UTC(e.$y,D,Y):new Date(e.$y,D,Y),e);return s?F:F.endOf(M)},l=function(Y,D){return o.w(e.toDate()[Y].apply(e.toDate("s"),(s?[0,0,0,0]:[23,59,59,999]).slice(D)),e)},p=this.$W,g=this.$M,T=this.$D,E="set"+(this.$u?"UTC":"");switch(a){case w:return s?c(1,0):c(31,11);case S:return s?c(1,g):c(0,g+1);case I:var L=this.$locale().weekStart||0,C=(p<L?p+7:p)-L;return c(s?T-C:T+(6-C),g);case M:case u:return l(E+"Hours",0);case v:return l(E+"Minutes",1);case $:return l(E+"Seconds",2);case O:return l(E+"Milliseconds",3);default:return this.clone()}},n.endOf=function(t){return this.startOf(t,!1)},n.$set=function(t,r){var e,s=o.p(t),a="set"+(this.$u?"UTC":""),c=(e={},e[M]=a+"Date",e[u]=a+"Date",e[S]=a+"Month",e[w]=a+"FullYear",e[v]=a+"Hours",e[$]=a+"Minutes",e[O]=a+"Seconds",e[b]=a+"Milliseconds",e)[s],l=s===M?this.$D+(r-this.$W):r;if(s===S||s===w){var p=this.clone().set(u,1);p.$d[c](l),p.init(),this.$d=p.set(u,Math.min(this.$D,p.daysInMonth())).$d}else c&&this.$d[c](l);return this.init(),this},n.set=function(t,r){return this.clone().$set(t,r)},n.get=function(t){return this[o.p(t)]()},n.add=function(t,r){var e,s=this;t=Number(t);var a=o.p(r),c=function(g){var T=m(s);return o.w(T.date(T.date()+Math.round(g*t)),s)};if(a===S)return this.set(S,this.$M+t);if(a===w)return this.set(w,this.$y+t);if(a===M)return c(1);if(a===I)return c(7);var l=(e={},e[$]=y,e[v]=x,e[O]=d,e)[a]||1,p=this.$d.getTime()+t*l;return o.w(p,this)},n.subtract=function(t,r){return this.add(-1*t,r)},n.format=function(t){var r=this,e=this.$locale();if(!this.isValid())return e.invalidDate||f;var s=t||"YYYY-MM-DDTHH:mm:ssZ",a=o.z(this),c=this.$H,l=this.$m,p=this.$M,g=e.weekdays,T=e.months,E=e.meridiem,L=function(D,F,U,z){return D&&(D[F]||D(r,s))||U[F].slice(0,z)},C=function(D){return o.s(c%12||12,D,"0")},Y=E||function(D,F,U){var z=D<12?"AM":"PM";return U?z.toLowerCase():z};return s.replace(A,function(D,F){return F||function(U){switch(U){case"YY":return String(r.$y).slice(-2);case"YYYY":return o.s(r.$y,4,"0");case"M":return p+1;case"MM":return o.s(p+1,2,"0");case"MMM":return L(e.monthsShort,p,T,3);case"MMMM":return L(T,p);case"D":return r.$D;case"DD":return o.s(r.$D,2,"0");case"d":return String(r.$W);case"dd":return L(e.weekdaysMin,r.$W,g,2);case"ddd":return L(e.weekdaysShort,r.$W,g,3);case"dddd":return g[r.$W];case"H":return String(c);case"HH":return o.s(c,2,"0");case"h":return C(1);case"hh":return C(2);case"a":return Y(c,l,!0);case"A":return Y(c,l,!1);case"m":return String(l);case"mm":return o.s(l,2,"0");case"s":return String(r.$s);case"ss":return o.s(r.$s,2,"0");case"SSS":return o.s(r.$ms,3,"0");case"Z":return a}return null}(D)||a.replace(":","")})},n.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},n.diff=function(t,r,e){var s,a=this,c=o.p(r),l=m(t),p=(l.utcOffset()-this.utcOffset())*y,g=this-l,T=function(){return o.m(a,l)};switch(c){case w:s=T()/12;break;case S:s=T();break;case R:s=T()/3;break;case I:s=(g-p)/6048e5;break;case M:s=(g-p)/864e5;break;case v:s=g/x;break;case $:s=g/y;break;case O:s=g/d;break;default:s=g}return e?s:o.a(s)},n.daysInMonth=function(){return this.endOf(S).$D},n.$locale=function(){return W[this.$L]},n.locale=function(t,r){if(!t)return this.$L;var e=this.clone(),s=Z(t,r,!0);return s&&(e.$L=s),e},n.clone=function(){return o.w(this.$d,this)},n.toDate=function(){return new Date(this.valueOf())},n.toJSON=function(){return this.isValid()?this.toISOString():null},n.toISOString=function(){return this.$d.toISOString()},n.toString=function(){return this.$d.toUTCString()},i}(),Q=V.prototype;return m.prototype=Q,[["$ms",b],["$s",O],["$m",$],["$H",v],["$W",M],["$M",S],["$y",w],["$D",u]].forEach(function(i){Q[i[1]]=function(n){return this.$g(n,i[0],i[1])}}),m.extend=function(i,n){return i.$i||(i(n,V,m),i.$i=!0),m},m.locale=Z,m.isDayjs=B,m.unix=function(i){return m(1e3*i)},m.en=W[j],m.Ls=W,m.p={},m})})(G);var et=G.exports;const nt=tt(et),rt=5*60*1e3;function at(h,_){const d=(u,f)=>[`%c${u}`,`color: ${f};`],y=()=>d("[INFO]","#2196F3"),x=()=>d("[WARN]","#FFC107"),b=()=>d("[ERRO]","#F44336"),O=d("[stream-run]","#999999"),$=(u,...f)=>{const k=u();`${O[0]}${k[0]}`,O[1],k[1],nt().format("HH:mm:ss.SSS")};function v(){return N(this,null,function*(){const u=(yield H.post({url:"/airag/flow/debug",params:{flow:h,inputParams:_,responseMode:"streaming"},adapter:"fetch",responseType:"stream",timeout:rt},{isTransformResponse:!1})).getReader(),f=new TextDecoder;for(;;){const{done:k,value:A}=yield u.read();if(k)break;const J=f.decode(A,{stream:!0});try{$(y,"收到 chunkText:",{chunkText:J}),M(J)}catch(P){}}})}function M(u){if(!u){$(b,"chunkText 为空:",{chunkText:u});return}u.split(`
`).flatMap(f=>{if(f=f?f.trim():"",!f)return[];if(f.startsWith("data:")&&(f=f.slice(5)),!f)return $(b,"chunk 为空:",{chunk:f,chunkText:u}),[];try{return[JSON.parse(f)]}catch(k){$(b,"chunk 解析失败:",{chunk:f,chunkText:u})}return[]}).forEach(R)}const I=new Map,S=new Set;function R(u){var f;const k=`${u.event}-${((f=u.data)==null?void 0:f.id)||""}`;if(S.has(k)){$(x,"chunk 重复执行:",{key:k,data:u});return}S.add(k);const A=I.get(u.event);typeof A=="function"?($(y,` ------ 处理 ${u.event} 事件:`,{key:k,data:u}),A(u.data,u)):$(x,`${u.event} 事件对应的回调不存在:`,{key:k,data:u})}function w(u,f){I.set(u,f)}return{run:v,onFlowStarted:u=>w("FLOW_STARTED",u),onFlowFinished:u=>w("FLOW_FINISHED",u),onNodeStarted:u=>w("NODE_STARTED",u),onNodeFinished:u=>w("NODE_FINISHED",u)}}function ut(h){return N(this,null,function*(){return H.get({url:"/airag/flow/list",params:h})})}function ot(h,_){return N(this,null,function*(){var d;const y=(d=_==null?void 0:_.silent)!=null?d:!1;return H.post({url:"/airag/flow/add",params:h},{successMessageMode:y?"none":"success"})})}function ct(h){return N(this,null,function*(){return H.put({url:"/airag/flow/edit",params:h})})}function ft(h,_){return N(this,null,function*(){var d;const y=(d=_==null?void 0:_.silent)!=null?d:!1;return H.put({url:"/airag/flow/design/save",params:{id:h.id,name:h.name,chain:h.chain,design:h.design}},{successMessageMode:y?"none":"success"})})}function ht(h){return N(this,null,function*(){return H.delete({url:"/airag/flow/delete",data:{id:h}},{joinParamsToUrl:!0})})}function lt(h){return N(this,null,function*(){return H.delete({url:"/airag/flow/deleteBatch",data:{ids:h.join(",")}},{joinParamsToUrl:!0})})}function dt(h){return N(this,null,function*(){return H.get({url:"/airag/flow/querySubflowById",params:{subflowId:h}})})}export{dt as $,ot as c,ft as d,ct as f,lt as h,ht as l,nt as n,ut as o,at as u};
