var l=(t,s,e)=>new Promise((n,a)=>{var r=o=>{try{p(e.next(o))}catch(c){a(c)}},i=o=>{try{p(e.throw(o))}catch(c){a(c)}},p=o=>o.done?n(o.value):Promise.resolve(o.value).then(r,i);p((e=e.apply(t,s)).next())});import{d as m,f,o as g,n as d,u,aq as w,ar as h,aA as _}from"./vue-vendor-dy9k-Yad.js";import{useScript as y}from"./useScript-C3Sw6r4a.js";import{a as M}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const k="https://maps.googleapis.com/maps/api/js?key=AIzaSyBQWrGwj4gAzKndcbwD5favT9K0wgty_0&signed_in=true",S=m({name:"GoogleMap",props:{width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"}},setup(){const t=f(null),{toPromise:s}=y({src:k});function e(){return l(this,null,function*(){yield s(),yield d();const n=u(t);if(!n)return;const a=window.google,r={lat:116.404,lng:39.915},i=new a.maps.Map(n,{zoom:4,center:r});new a.maps.Marker({position:r,map:i,title:"Hello World!"})})}return g(()=>{e()}),{wrapRef:t}}});function z(t,s,e,n,a,r){return h(),w("div",{ref:"wrapRef",style:_({height:t.height,width:t.width})},null,4)}const R=M(S,[["render",z]]);export{R as default};
