var d=(m,c,o)=>new Promise((r,a)=>{var s=t=>{try{i(o.next(t))}catch(n){a(n)}},e=t=>{try{i(o.throw(t))}catch(n){a(n)}},i=t=>t.done?r(t.value):Promise.resolve(t.value).then(s,e);i((o=o.apply(m,c)).next())});import{f as u,ag as l,aB as M,ar as x,aD as _,at as p,k as f,au as h}from"./vue-vendor-dy9k-Yad.js";import{I as D}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{B as g}from"./BasicForm-DBcXiHk0.js";import{a as v}from"./index-mbACBRQ9.js";import{ac as B,a as w}from"./index-CCWaWN5g.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const k={name:"AiTextDescModal",components:{MarkdownViewer:v,BasicForm:g,BasicModal:D},emits:["success","register"],setup(m,{emit:c}){let o=u({});const[r,{closeModal:a,setModalProps:s}]=B(e=>d(null,null,function*(){o.value.source="score "+e.score.toFixed(2),o.value.content=e.content,s({header:"300px"})}));return{registerModal:r,hitTextDescData:o}}},T={class:"p-2"},V={class:"header"},y={class:"content"};function C(m,c,o,r,a,s){const e=l("a-tag"),i=l("MarkdownViewer"),t=l("BasicModal");return x(),M(t,{title:"段落详情",destroyOnClose:"",onRegister:r.registerModal,canFullscreen:!1,width:"600px",footer:null},{default:_(()=>[p("div",T,[p("div",V,[f(e,{color:"#a9c8ff"},{default:_(()=>[p("span",null,h(r.hitTextDescData.source),1)]),_:1})]),p("div",y,[f(i,{value:r.hitTextDescData.content},null,8,["value"])])])]),_:1},8,["onRegister"])}const wo=w(k,[["render",C],["__scopeId","data-v-8c3cfb92"]]);export{wo as default};
