import{d as C,f as _,u as l,ag as t,aB as v,ar as y,aD as n,k as r,at as g,G as b}from"./vue-vendor-dy9k-Yad.js";import{C as k}from"./index-LCGLvkB3.js";import{br as w,u as x,a as h}from"./index-CCWaWN5g.js";import{P as B}from"./index-CtJ0w2CP.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const P=C({name:"Copy",components:{CollapseContainer:k,PageWrapper:B},setup(){const o=_(""),{createMessage:e}=x(),{clipboardRef:p,copiedRef:s}=w();function i(){const a=l(o);if(!a){e.warning("请输入要拷贝的内容！");return}p.value=a,l(s)&&e.warning("copy success！")}return{handleCopy:i,value:o}}}),$={class:"flex justify-center"};function N(o,e,p,s,i,a){const u=t("a-input"),m=t("a-button"),c=t("CollapseContainer"),d=t("PageWrapper");return y(),v(d,{title:"文本复制示例"},{default:n(()=>[r(c,{class:"w-full h-32 bg-white rounded-md",title:"Copy Example"},{default:n(()=>[g("div",$,[r(u,{placeholder:"请输入",value:o.value,"onUpdate:value":e[0]||(e[0]=f=>o.value=f)},null,8,["value"]),r(m,{type:"primary",onClick:o.handleCopy},{default:n(()=>e[1]||(e[1]=[b(" Copy ")])),_:1,__:[1]},8,["onClick"])])]),_:1})]),_:1})}const J=h(P,[["render",N]]);export{J as default};
