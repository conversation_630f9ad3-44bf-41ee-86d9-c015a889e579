var r=(i,s,n)=>new Promise((o,f)=>{var y=t=>{try{a(n.next(t))}catch(l){f(l)}},e=t=>{try{a(n.throw(t))}catch(l){f(l)}},a=t=>t.done?o(t.value):Promise.resolve(t.value).then(y,e);a((n=n.apply(i,s)).next())});import{h as F}from"./BasicForm-DBcXiHk0.js";import{f as h,u as c,n as w,b,w as g}from"./vue-vendor-dy9k-Yad.js";import{al as S,am as v,an as T,K as V,I as p,J as P,ao as j}from"./index-CCWaWN5g.js";import{h as u}from"./componentMap-Bkie1n3v.js";function C(i){const s=h(null),n=h(!1);u("OnlineSelectCascade",S),u("LinkTableCard",v),u("LinkTableSelect",T);function o(){return r(this,null,function*(){const e=c(s);return e||V("The form instance has not been obtained, please make sure that the form has been rendered when performing the form operation!"),yield w(),e})}function f(e){p()&&b(()=>{s.value=null,n.value=null}),!(c(n)&&p()&&e===c(s))&&(s.value=e,n.value=!0,g(()=>i,()=>{i&&e.setProps(P(i))},{immediate:!0,deep:!0}))}return[f,{scrollToField:(e,a)=>r(null,null,function*(){(yield o()).scrollToField(e,a)}),setProps:e=>r(null,null,function*(){(yield o()).setProps(e)}),updateSchema:e=>r(null,null,function*(){(yield o()).updateSchema(e)}),resetSchema:e=>r(null,null,function*(){(yield o()).resetSchema(e)}),clearValidate:e=>r(null,null,function*(){(yield o()).clearValidate(e)}),resetFields:()=>r(null,null,function*(){o().then(e=>r(null,null,function*(){yield e.resetFields()}))}),removeSchemaByFiled:e=>r(null,null,function*(){var a;(a=c(s))==null||a.removeSchemaByFiled(e)}),getFieldsValue:()=>{var a;let e=(a=c(s))==null?void 0:a.getFieldsValue();return e&&Object.keys(e).map(t=>{e[t]instanceof Array&&(typeof(e[t][0]||"")=="object"||(e[t]=e[t].join(",")))}),e},setFieldsValue:e=>r(null,null,function*(){(yield o()).setFieldsValue(e)}),appendSchemaByField:(e,a,t)=>r(null,null,function*(){(yield o()).appendSchemaByField(e,a,t)}),submit:()=>r(null,null,function*(){return(yield o()).submit()}),validate:e=>r(null,null,function*(){const a=yield o();let t=i||a.getProps;return a.validate(e).then(m=>{for(let d in m)m[d]instanceof Array&&j(a.getSchemaByField(d))==="string"&&(m[d]=m[d].join(","));return F(t,m)})}),validateFields:(e,a)=>r(null,null,function*(){return(yield o()).validateFields(e,a)})}]}export{C as u};
