import{aZ as i,bc as f}from"./index-CCWaWN5g.js";import"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";let r;const n=new Map;function y(e){const s=i()||"";r=f(e,{autoReconnect:{retries:10,delay:5e3},heartbeat:{message:"ping",interval:55e3},protocols:[s],onConnected:function(o){},onDisconnected:function(o,t){},onError:function(o,t){},onMessage:function(o,t){try{if(t.data==="ping")return;const c=JSON.parse(t.data);for(const a of n.keys())try{a(c)}catch(u){}}catch(c){}}})}function b(e){n.has(e)||typeof e=="function"&&n.set(e,null)}function g(e){n.delete(e)}function S(){return r}export{y as connectWebSocket,g as offWebSocket,b as onWebSocket,S as useMyWebSocket};
