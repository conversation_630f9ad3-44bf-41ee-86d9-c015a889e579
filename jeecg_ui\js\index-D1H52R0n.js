import{d as L,ap as h,f as k,e as p,u as t,aI as S,ag as g,aB as $,ah as w,ar as b,as as f,aD as U,at as n,k as G,au as F}from"./vue-vendor-dy9k-Yad.js";import{bv as I,a9 as B}from"./antd-vue-vendor-me9YkNVC.js";import{S as H,G as T,D as V}from"./siteSetting-DoyCDlSB.js";import{N as E,D as v,F as N,ay as W,o as O,a as j}from"./index-CCWaWN5g.js";import{u as z}from"./useContentViewHeight-Md7r1NIg.js";import"./vxe-table-vendor-B22HppNm.js";import"./usePageContext-CxiNGbPs.js";import"./useWindowSizeFn-DDbrQbks.js";const d=L({name:"LayoutFooter",components:{Footer:<PERSON><PERSON>,GithubFilled:I},setup(){const{t:o}=E(),{getShowFooter:e}=v(),{currentRoute:m}=h(),{prefixCls:C}=N("layout-footer"),r=k(null),{setFooterHeight:a}=z(),{getDarkMode:i}=v(),u=p(()=>i.value===W.DARK),s=p(()=>{var c,_;if(t(e)){const l=(c=t(r))==null?void 0:c.$el;a((l==null?void 0:l.offsetHeight)||0)}else a(0);return t(e)&&!((_=t(m).meta)!=null&&_.hiddenFooter)}),D=p(()=>t(u)?"rgba(255, 255, 255, 1)":"rgba(0, 0, 0, 0.85)");return{getShowLayoutFooter:s,prefixCls:C,t:o,DOC_URL:V,GITHUB_URL:T,SITE_URL:H,openWindow:O,footerRef:r,hoverColor:D}}}),y=()=>{S(o=>({"735092e7":o.hoverColor}))},R=d.setup;d.setup=R?(o,e)=>(y(),R(o,e)):y;function A(o,e,m,C,r,a){const i=g("GithubFilled"),u=g("Footer");return o.getShowLayoutFooter?(b(),$(u,{key:0,class:f(o.prefixCls),ref:"footerRef"},{default:U(()=>[n("div",{class:f(`${o.prefixCls}__links`)},[n("a",{onClick:e[0]||(e[0]=s=>o.openWindow(o.SITE_URL))},F(o.t("layout.footer.onlinePreview")),1),G(i,{onClick:e[1]||(e[1]=s=>o.openWindow(o.GITHUB_URL)),class:f(`${o.prefixCls}__github`)},null,8,["class"]),n("a",{onClick:e[2]||(e[2]=s=>o.openWindow(o.DOC_URL))},F(o.t("layout.footer.onlineDocument")),1)],2),e[3]||(e[3]=n("div",null,"Copyright ©2021 JEECG开源社区 出品",-1))]),_:1,__:[3]},8,["class"])):w("",!0)}const Z=j(d,[["render",A],["__scopeId","data-v-4e54f13b"]]);export{Z as default};
