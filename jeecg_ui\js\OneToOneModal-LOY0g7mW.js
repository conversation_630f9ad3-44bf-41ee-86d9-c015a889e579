var k=(t,a,s)=>new Promise((M,m)=>{var g=e=>{try{d(s.next(e))}catch(u){m(u)}},i=e=>{try{d(s.throw(e))}catch(u){m(u)}},d=e=>e.done?M(e.value):Promise.resolve(e.value).then(g,i);d((s=s.apply(t,a)).next())});import{d as T,f as L,r as b,e as U,u as j,ag as n,aB as D,ar as Y,aE as B,aD as r,k as o,G as y}from"./vue-vendor-dy9k-Yad.js";import{B as R}from"./index-Diw57m_E.js";import{s as $}from"./jvxetable.api-C3sIybRa.js";import{o as H,a as I}from"./api-drClL0vf.js";import{ac as F,a as N}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const P=T({name:"OneToOneModal",components:{BasicModal:R},emits:["success","register"],setup(t,{emit:a}){const s=L(!0),M=L(""),m=L(),g=b({xs:{span:24},sm:{span:5}}),i=b({xs:{span:24},sm:{span:16}}),d={orderCode:[{required:!0,message:"订单号不能为空",trigger:"blur"}]},e=b({id:null,orderCode:"",orderMoney:"",ctype:"",content:"",jeecgOrderCustomerList:{name:"",telphone:""},jeecgOrderTicketList:{ticketCode:"",tickectDate:""}}),[u,{setModalProps:f,closeModal:v}]=F(p=>k(null,null,function*(){if(f({confirmLoading:!1}),_(),s.value=!!(p!=null&&p.isUpdate),j(s)){M.value=p.record.id,Object.assign(e,p.record);let O={id:e.id};const l=yield H(O);e.jeecgOrderCustomerList=l[0]?l[0]:{};const w=yield I(O);e.jeecgOrderTicketList=w[0]?w[0]:{}}})),c=U(()=>j(s)?"编辑":"新增");function _(){e.id=null,e.orderCode="",e.orderMoney="",e.orderDate=null,e.ctype="",e.content="",e.jeecgOrderCustomerList={},e.jeecgOrderTicketList={}}function C(){return k(this,null,function*(){m.value.validate().then(()=>k(null,null,function*(){try{f({confirmLoading:!0}),e.jeecgOrderCustomerList=Object.keys(e.jeecgOrderCustomerList).length>0?[e.jeecgOrderCustomerList]:[],e.jeecgOrderTicketList=Object.keys(e.jeecgOrderTicketList).length>0?[e.jeecgOrderTicketList]:[],yield $(e,j(s)),v(),a("success")}finally{f({confirmLoading:!1})}})).catch(p=>{})})}return{formRef:m,validatorRules:d,orderMainModel:e,registerModal:u,getTitle:c,labelCol:g,wrapperCol:i,handleSubmit:C}}});function S(t,a,s,M,m,g){const i=n("a-input"),d=n("a-form-item"),e=n("a-col"),u=n("a-select-option"),f=n("a-select"),v=n("a-date-picker"),c=n("a-row"),_=n("a-tab-pane"),C=n("a-tabs"),p=n("a-form"),O=n("BasicModal");return Y(),D(O,B(t.$attrs,{onRegister:t.registerModal,title:t.getTitle,onOk:t.handleSubmit,width:"70%"}),{default:r(()=>[o(p,{ref:"formRef",model:t.orderMainModel,"label-col":t.labelCol,"wrapper-col":t.wrapperCol,rules:t.validatorRules},{default:r(()=>[o(c,{class:"form-row",gutter:16},{default:r(()=>[o(e,{lg:8},{default:r(()=>[o(d,{label:"订单号",name:"orderCode"},{default:r(()=>[o(i,{value:t.orderMainModel.orderCode,"onUpdate:value":a[0]||(a[0]=l=>t.orderMainModel.orderCode=l),placeholder:"请输入订单号"},null,8,["value"])]),_:1})]),_:1}),o(e,{lg:8},{default:r(()=>[o(d,{label:"订单类型"},{default:r(()=>[o(f,{placeholder:"请选择订单类型",value:t.orderMainModel.ctype,"onUpdate:value":a[1]||(a[1]=l=>t.orderMainModel.ctype=l)},{default:r(()=>[o(u,{value:"1"},{default:r(()=>a[9]||(a[9]=[y("国内订单")])),_:1,__:[9]}),o(u,{value:"2"},{default:r(()=>a[10]||(a[10]=[y("国际订单")])),_:1,__:[10]})]),_:1},8,["value"])]),_:1})]),_:1}),o(e,{lg:8},{default:r(()=>[o(d,{label:"订单日期"},{default:r(()=>[o(v,{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss",value:t.orderMainModel.orderDate,"onUpdate:value":a[2]||(a[2]=l=>t.orderMainModel.orderDate=l)},null,8,["value"])]),_:1})]),_:1})]),_:1}),o(c,{class:"form-row",gutter:16},{default:r(()=>[o(e,{lg:8},{default:r(()=>[o(d,{label:"订单金额"},{default:r(()=>[o(i,{value:t.orderMainModel.orderMoney,"onUpdate:value":a[3]||(a[3]=l=>t.orderMainModel.orderMoney=l),placeholder:"请输入订单金额"},null,8,["value"])]),_:1})]),_:1}),o(e,{lg:8},{default:r(()=>[o(d,{label:"订单备注"},{default:r(()=>[o(i,{value:t.orderMainModel.content,"onUpdate:value":a[4]||(a[4]=l=>t.orderMainModel.content=l),placeholder:"请输入订单备注"},null,8,["value"])]),_:1})]),_:1})]),_:1}),o(C,{defaultActiveKey:"1"},{default:r(()=>[o(_,{tab:"客户信息",key:"1"},{default:r(()=>[o(c,{class:"form-row",gutter:16},{default:r(()=>[o(e,{lg:8},{default:r(()=>[o(d,{label:"客户姓名"},{default:r(()=>[o(i,{value:t.orderMainModel.jeecgOrderCustomerList.name,"onUpdate:value":a[5]||(a[5]=l=>t.orderMainModel.jeecgOrderCustomerList.name=l),placeholder:"请输入客户姓名"},null,8,["value"])]),_:1})]),_:1}),o(e,{lg:8},{default:r(()=>[o(d,{label:"手机号"},{default:r(()=>[o(i,{value:t.orderMainModel.jeecgOrderCustomerList.telphone,"onUpdate:value":a[6]||(a[6]=l=>t.orderMainModel.jeecgOrderCustomerList.telphone=l),placeholder:"请输入手机号"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1}),o(_,{tab:"机票信息",key:"2",forceRender:""},{default:r(()=>[o(c,{class:"form-row",gutter:16},{default:r(()=>[o(e,{lg:8},{default:r(()=>[o(d,{label:"航班号"},{default:r(()=>[o(i,{value:t.orderMainModel.jeecgOrderTicketList.ticketCode,"onUpdate:value":a[7]||(a[7]=l=>t.orderMainModel.jeecgOrderTicketList.ticketCode=l),placeholder:"请输入航班号"},null,8,["value"])]),_:1})]),_:1}),o(e,{lg:8},{default:r(()=>[o(d,{label:"起飞时间"},{default:r(()=>[o(v,{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss",value:t.orderMainModel.jeecgOrderTicketList.tickectDate,"onUpdate:value":a[8]||(a[8]=l=>t.orderMainModel.jeecgOrderTicketList.tickectDate=l)},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model","label-col","wrapper-col","rules"])]),_:1},16,["onRegister","title","onOk"])}const ae=N(P,[["render",S],["__scopeId","data-v-5a8c4a23"]]);export{ae as default};
