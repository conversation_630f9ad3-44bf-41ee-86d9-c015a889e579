import{d as p,aB as e,ar as a,u as o}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as s}from"./useForm-CgkFTrrO.js";import{B as n}from"./BasicForm-DBcXiHk0.js";import{a as l}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const c=p({__name:"BasicFiledsLayotForm",setup(u){const t=[{label:"姓名",field:"name",component:"Input"},{label:"年龄",field:"password",component:"InputNumber"},{label:"生日",field:"birthday",component:"DatePicker"},{label:"头像",field:"avatar",component:"JImageUpload"}],[r]=s({schemas:t,showActionButtonGroup:!1,actionColOptions:{span:12},labelCol:{xs:2,sm:2,md:2,lg:9,xl:3,xxl:2},wrapperCol:{xs:15,sm:14,md:16,lg:17,xl:19,xxl:20}});function m(i){}return(i,d)=>(a(),e(o(n),{onRegister:o(r),onSubmit:m,style:{margin:"20px auto"}},null,8,["onRegister"]))}}),xo=l(c,[["__scopeId","data-v-4a146af5"]]);export{xo as default};
