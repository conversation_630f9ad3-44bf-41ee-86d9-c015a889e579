var L=Object.defineProperty,W=Object.defineProperties;var Q=Object.getOwnPropertyDescriptors;var I=Object.getOwnPropertySymbols;var U=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var T=(e,n,a)=>n in e?L(e,n,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[n]=a,u=(e,n)=>{for(var a in n||(n={}))U.call(n,a)&&T(e,a,n[a]);if(I)for(var a of I(n))X.call(n,a)&&T(e,a,n[a]);return e},M=(e,n)=>W(e,Q(n));var H=(e,n,a)=>new Promise((k,r)=>{var s=i=>{try{o(a.next(i))}catch(f){r(f)}},d=i=>{try{o(a.throw(i))}catch(f){r(f)}},o=i=>i.done?k(i.value):Promise.resolve(i.value).then(s,d);o((a=a.apply(e,n)).next())});import{d as Y,f as b,e as O,u as t,z as Z,g as x,h as S,w as ee,ag as g,aB as B,ar as P,aE as V,aH as j,aD as c,aG as N,aC as D,aq as le,ah as ne,k as C,aJ as E,aK as _}from"./vue-vendor-dy9k-Yad.js";import{b as ae,d as oe,a as te,c as se,M as ie}from"./ModalHeader-BJG9dHtK.js";import{aH as ce,aI as ue,T as re,ao as de,o as R}from"./antd-vue-vendor-me9YkNVC.js";import{ap as fe,au as me,N as pe,n as ge,a as ve}from"./index-CCWaWN5g.js";const he=Y({name:"JModal",methods:{omit:R},components:{CloseOutlined:de,Tooltip:re,FullscreenExitOutlined:ue,FullscreenOutlined:ce,ModalHeader:ie,ModalClose:se,ModalFooter:te,Modal:oe},props:u({fullscreen:{type:Boolean,default:!1}},ae),emits:["visible-change","open-change","height-change","cancel","ok","register","update:visible","update:open","fullScreen"],setup(e,{emit:n,attrs:a,slots:k}){const{getIsMobile:r}=fe(),s=b(!1),d=b(null),o=b(e.fullscreen),i=b(!1),{t:f}=pe(),$=b({height:e.maxHeight?e.maxHeight:"600px","overflow-y":"auto"}),v={setModalProps:K,emitVisible:void 0},m=O(()=>{const l=u(u({},e),t(d));return r.value&&(l.fullscreen=!1),l}),p=O(()=>{const l=M(u(u({},a),t(m)),{open:t(s),wrapClassName:t(h)});return t(o)?R(l,["height","visible"]):R(l,["visible"])}),h=O(()=>{let l=Z(m.value,"wrapClassName").value||"";return l=`${l} jeecg-modal-code-generate`,t(o)?`jeecg-full-screen-modal-code-generate ${l} `:t(l)}),J=O(()=>{const l=M(u({},t(m)),{visible:t(s),okButtonProps:void 0,cancelButtonProps:void 0,title:void 0});return M(u({},l),{wrapClassName:t(h)})}),y=x();y&&n("register",v,y.uid);function z(){return!t(m).title&&!k.title}function w(l){l==null||l.stopPropagation(),l==null||l.preventDefault(),o.value=!t(o)}function q(l){return H(this,null,function*(){if(l==null||l.stopPropagation(),e.closeFunc&&ge(e.closeFunc)){const F=yield e.closeFunc();s.value=!F;return}s.value=!1,n("cancel",l)})}function A(l){n("ok",l)}function G(l){e.fullscreen&&(l.stopPropagation(),w(l))}function K(l){d.value=me(t(d)||{},l),Reflect.has(l,"visible")&&(s.value=!!l.visible),Reflect.has(l,"open")&&(s.value=!!l.open),Reflect.has(l,"defaultFullscreen")&&(o.value=!!l.defaultFullscreen,r.value&&(o.value=!0))}return S(()=>{o.value=e.fullscreen,r.value&&(o.value=!0)}),S(()=>{s.value=!!e.visible}),S(()=>{s.value=!!e.open}),ee(()=>t(s),l=>{var F;n("visible-change",l),n("open-change",l),n("update:visible",l),n("update:open",l),y&&((F=v.emitVisible)==null||F.call(v,l,y.uid))},{immediate:!1}),{isNoTitle:z,getBindValue:p,fullScreenRef:o,handleFullScreen:w,fullScreen:i,t:f,handleCancel:q,handleOk:A,getProps:J,getMergeProps:m,handleTitleDbClick:G,bodyStyle:$}}}),Ce={key:0,class:"jeecg-basic-modal-close"};function be(e,n,a,k,r,s){const d=g("FullscreenExitOutlined"),o=g("Tooltip"),i=g("FullscreenOutlined"),f=g("CloseOutlined"),$=g("ModalHeader"),v=g("ModalFooter"),m=g("a-modal");return P(),B(m,V(e.getBindValue,{onCancel:e.handleCancel,"body-style":e.fullScreenRef?{}:e.bodyStyle}),j({default:c(()=>[N(e.$slots,"default",{},void 0,!0)]),_:2},[e.$slots.closeIcon?void 0:{name:"closeIcon",fn:c(()=>[e.canFullscreen?(P(),le("div",Ce,[e.fullScreenRef?(P(),B(o,{key:0,title:e.t("component.modal.restore"),placement:"bottom"},{default:c(()=>[C(d,{role:"full",onClick:e.handleFullScreen},null,8,["onClick"])]),_:1},8,["title"])):(P(),B(o,{key:1,title:e.t("component.modal.maximize"),placement:"bottom"},{default:c(()=>[C(i,{role:"close",onClick:e.handleFullScreen},null,8,["onClick"])]),_:1},8,["title"])),C(o,{title:e.t("component.modal.close"),placement:"bottom"},{default:c(()=>[C(f,{onClick:e.handleCancel},null,8,["onClick"])]),_:1},8,["title"])])):ne("",!0)]),key:"0"},e.isNoTitle?void 0:{name:"title",fn:c(()=>[C($,{helpMessage:e.getProps.helpMessage,title:e.getMergeProps.title,onDblclick:e.handleTitleDbClick},null,8,["helpMessage","title","onDblclick"])]),key:"1"},e.$slots.footer?void 0:{name:"footer",fn:c(()=>[C(v,V(e.getBindValue,{onOk:e.handleOk,onCancel:e.handleCancel}),j({_:2},[D(Object.keys(e.$slots),p=>({name:p,fn:c(h=>[N(e.$slots,p,E(_(h||{})),void 0,!0)])}))]),1040,["onOk","onCancel"])]),key:"2"},D(Object.keys(e.omit(e.$slots,"default")),p=>({name:p,fn:c(h=>[N(e.$slots,p,E(_(h||{})),void 0,!0)])}))]),1040,["onCancel","body-style"])}const Pe=ve(he,[["render",be],["__scopeId","data-v-aaa945cd"]]);export{Pe as J};
