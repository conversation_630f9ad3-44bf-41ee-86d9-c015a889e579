import{d as S,f as b,ag as f,aB as A,ar as k,u as x,aD as y,aq as F,ah as N,at as B,k as h,G as T}from"./vue-vendor-dy9k-Yad.js";import{u as _,b5 as D,ac as E,aZ as U,bJ as J}from"./index-CCWaWN5g.js";import{B as V}from"./index-Diw57m_E.js";var $=(M,w,r)=>new Promise((p,i)=>{var c=l=>{try{v(r.next(l))}catch(m){i(m)}},C=l=>{try{v(r.throw(l))}catch(m){i(m)}},v=l=>l.done?p(l.value):Promise.resolve(l.value).then(c,C);v((r=r.apply(M,w)).next())});const q={key:0,class:"call-code-modal-content"},z={style:{display:"flex","justify-content":"flex-end","margin-top":"8px"}},X=S({__name:"CallCodeModal",setup(M){const{createMessage:w}=_(),r=b("curl"),p=b(""),i=b(""),c=b(""),{domainUrl:C}=D(),[v,{closeModal:l,getOpen:m}]=E(n=>$(this,null,function*(){c.value="";const{record:e}=n;if(!e){c.value="[错误] 没有传入流程数据！";return}const u=U(),{id:o,design:a}=e,s=JSON.parse(a),{nodes:d}=s,t=d.find(g=>g.id==="start-node");if(!t){c.value="[错误] 没有找到开始节点！";return}R(o,t,u),I(o,t)}));function R(n,e,u){var o;let a='"inputParams": {';const s="      ",d=[],t=(o=e==null?void 0:e.properties)==null?void 0:o.inputParams;if(Array.isArray(t)&&t.length>0)for(const{field:g}of t)P(g)||d.push(`${s}  "${g}": ""`);d.length>0?(a+=`
`,a+=d.join(`,
`),a+=`
`,a+=s+"},"):a+="},",p.value=`
curl --request POST \\
  --url ${C}/airag/flow/run \\
  --header 'Accept: */*' \\
  --header 'Accept-Encoding: gzip, deflate, br' \\
  --header 'Connection: keep-alive' \\
  --header 'Content-Type: application/json' \\
  --header 'Cookie: JSESSIONID=442C48D3D1D0B2878A597AB6EBF2A07E' \\
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \\
  --header 'X-Access-Token: ${u}' \\
  --data '{
      "flowId": "${n}",
      ${a}
      "responseMode":"blocking"
    }'
    `.trim()}function I(n,e){var u;const o=[],a=(u=e==null?void 0:e.properties)==null?void 0:u.inputParams;if(Array.isArray(a)&&a.length>0)for(const{field:s}of a)P(s)||o.push(`inputs.put("${s}", "");`);i.value=`
FlowRunParams params = new FlowRunParams();
params.setFlowId("${n}");
Map<String, Object> inputs = new HashMap<>();
${o.join(`
`)}
params.setInputParams(inputs);
params.setResponseMode("blocking");
Result<Object> o = (Result<Object>) airagFlowService.runFlow(params);
    `.trim()}function P(n){return["history"].includes(n)}function j(){return $(this,null,function*(){const n=r.value==="curl"?p.value:i.value;J(n)?w.success("复制成功"):prompt("复制失败，请手动复制",n)})}function O(){l()}return(n,e)=>{const u=f("a-empty"),o=f("a-textarea"),a=f("a-tab-pane"),s=f("a-tabs"),d=f("a-button");return k(),A(x(V),{onRegister:x(v),width:"600px",header:null,footer:null,canFullscreen:!1,wrapClassName:"call-code-modal",onOk:j,onClose:O},{default:y(()=>[x(m)?(k(),F("div",q,[e[4]||(e[4]=B("p",{class:"call-code-modal-title"},"查看调用代码",-1)),c.value?(k(),A(u,{key:0,description:c.value},null,8,["description"])):(k(),A(s,{key:1,activeKey:r.value,"onUpdate:activeKey":e[2]||(e[2]=t=>r.value=t),animated:""},{default:y(()=>[h(a,{key:"curl",tab:"curl"},{default:y(()=>[h(o,{value:p.value,"onUpdate:value":e[0]||(e[0]=t=>p.value=t),rows:20,style:{resize:"none"}},null,8,["value"])]),_:1}),h(a,{key:"java",tab:"java"},{default:y(()=>[h(o,{value:i.value,"onUpdate:value":e[1]||(e[1]=t=>i.value=t),rows:20,style:{resize:"none"}},null,8,["value"])]),_:1})]),_:1},8,["activeKey"])),B("div",z,[h(d,{type:"primary",preIcon:"ant-design:copy-outlined",onClick:j},{default:y(()=>e[3]||(e[3]=[T("复制")])),_:1})])])):N("",!0)]),_:1},8,["onRegister"])}}});export{X as Y};
