var Q=Object.defineProperty,S=Object.defineProperties;var W=Object.getOwnPropertyDescriptors;var h=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var k=(o,t,s)=>t in o?Q(o,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):o[t]=s,_=(o,t)=>{for(var s in t||(t={}))b.call(t,s)&&k(o,s,t[s]);if(h)for(var s of h(t))D.call(t,s)&&k(o,s,t[s]);return o},B=(o,t)=>S(o,W(t));import{d as z,f as u,w as E,o as I,j as $,ag as j,aq as c,ar as r,ah as x,at as i,as as y,F,aC as N,aB as R,au as T}from"./vue-vendor-dy9k-Yad.js";import{a as V}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const O={class:"presetQuestion-wrap"},U={class:"content"},A=["onClick"],G={class:"question-descr"},H={key:1,width:"14px",height:"14px",viewBox:"0 0 24 24",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},J=z({name:"presetQuestion"}),K=z(B(_({},J),{props:{quickCommandData:{type:Object}},emits:["outQuestion"],setup(o,{emit:t}){const s=t,f=o,L=u(f.quickCommandData),C=u(""),p=u(""),P=u(""),n=u(null),d=u(!1);let w=null;const m=l=>{clearTimeout(w),w=setTimeout(()=>{const e=l.target.scrollLeft,v=l.target.offsetWidth,a=l.target.scrollWidth;a>v?d.value=!0:d.value=!1,e<=0?C.value="disabled":a-v==e?p.value="disabled":(C.value="",p.value="")},100)},g=l=>{const e=n.value.offsetWidth;l=="prev"?n.value.scrollLeft=n.value.scrollLeft-e:l=="next"&&(n.value.scrollLeft=n.value.scrollLeft+e)},q=l=>{s("outQuestion",l)};return E(()=>f.quickCommandData,l=>{L.value=f.quickCommandData}),I(()=>{n.value.addEventListener("scroll",m,!1),m({target:n.value})}),$(()=>{n.value.removeEventListener("scroll",m)}),(l,e)=>{const v=j("Icon");return r(),c("div",O,[d.value?(r(),c("svg",{key:0,class:y(["leftBtn",C.value]),t:"1710296339017",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"5070",onClick:e[0]||(e[0]=a=>g("prev"))},e[2]||(e[2]=[i("path",{d:"M970.496 543.829333l30.165333-30.165333-415.829333-415.914667a42.837333 42.837333 0 0 0-60.288 0 42.538667 42.538667 0 0 0 0 60.330667l355.413333 355.498667-355.413333 355.285333a42.496 42.496 0 0 0 0 60.288c16.64 16.64 43.861333 16.469333 60.288 0.042667l383.914667-383.701334 1.749333-1.664z",fill:"currentColor","p-id":"5071"},null,-1)]),2)):x("",!0),i("div",U,[i("ul",{ref_key:"ulElemRef",ref:n},[(r(!0),c(F,null,N(L.value,(a,M)=>(r(),c("li",{key:M,class:"item",onClick:X=>q(a.descr)},[i("div",G,[a.icon?(r(),R(v,{key:0,icon:a.icon,size:"20"},null,8,["icon"])):(r(),c("svg",H,e[3]||(e[3]=[i("path",{d:"M18.9839 1.85931C19.1612 1.38023 19.8388 1.38023 20.0161 1.85931L20.5021 3.17278C20.5578 3.3234 20.6766 3.44216 20.8272 3.49789L22.1407 3.98392C22.6198 4.1612 22.6198 4.8388 22.1407 5.01608L20.8272 5.50211C20.6766 5.55784 20.5578 5.6766 20.5021 5.82722L20.0161 7.14069C19.8388 7.61977 19.1612 7.61977 18.9839 7.14069L18.4979 5.82722C18.4422 5.6766 18.3234 5.55784 18.1728 5.50211L16.8593 5.01608C16.3802 4.8388 16.3802 4.1612 16.8593 3.98392L18.1728 3.49789C18.3234 3.44216 18.4422 3.3234 18.4979 3.17278L18.9839 1.85931zM13.5482 4.07793C13.0164 2.64069 10.9836 2.64069 10.4518 4.07793L8.99368 8.01834C8.82648 8.47021 8.47021 8.82648 8.01834 8.99368L4.07793 10.4518C2.64069 10.9836 2.64069 13.0164 4.07793 13.5482L8.01834 15.0063C8.47021 15.1735 8.82648 15.5298 8.99368 15.9817L10.4518 19.9221C10.9836 21.3593 13.0164 21.3593 13.5482 19.9221L15.0063 15.9817C15.1735 15.5298 15.5298 15.1735 15.9817 15.0063L19.9221 13.5482C21.3593 13.0164 21.3593 10.9836 19.9221 10.4518L15.9817 8.99368C15.5298 8.82648 15.1735 8.47021 15.0063 8.01834L13.5482 4.07793zM5.01608 16.8593C4.8388 16.3802 4.1612 16.3802 3.98392 16.8593L3.49789 18.1728C3.44216 18.3234 3.3234 18.4422 3.17278 18.4979L1.85931 18.9839C1.38023 19.1612 1.38023 19.8388 1.85931 20.0161L3.17278 20.5021C3.3234 20.5578 3.44216 20.6766 3.49789 20.8272L3.98392 22.1407C4.1612 22.6198 4.8388 22.6198 5.01608 22.1407L5.50211 20.8272C5.55784 20.6766 5.6766 20.5578 5.82722 20.5021L7.14069 20.0161C7.61977 19.8388 7.61977 19.1612 7.14069 18.9839L5.82722 18.4979C5.6766 18.4422 5.55784 18.3234 5.50211 18.1728L5.01608 16.8593z"},null,-1)]))),i("span",null,T(a.name),1)])],8,A))),128))],512)]),d.value?(r(),c("svg",{key:1,class:y(["rightBtn",p.value]),t:"1710296339017",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"5070",onClick:e[1]||(e[1]=a=>g("next"))},e[4]||(e[4]=[i("path",{d:"M970.496 543.829333l30.165333-30.165333-415.829333-415.914667a42.837333 42.837333 0 0 0-60.288 0 42.538667 42.538667 0 0 0 0 60.330667l355.413333 355.498667-355.413333 355.285333a42.496 42.496 0 0 0 0 60.288c16.64 16.64 43.861333 16.469333 60.288 0.042667l383.914667-383.701334 1.749333-1.664z",fill:"currentColor","p-id":"5071"},null,-1)]),2)):x("",!0)])}}})),o1=V(K,[["__scopeId","data-v-14b02c9a"]]);export{o1 as default};
