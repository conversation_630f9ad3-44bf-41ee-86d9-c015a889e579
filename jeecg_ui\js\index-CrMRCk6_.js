import{a7 as $,a6 as b,a4 as g,X as w}from"./antd-vue-vendor-me9YkNVC.js";import{d as B,e as P,ag as l,aq as r,ar as t,as as n,k as s,at as p,aD as o,F as m,aC as f,G as T,au as u,aB as d,aQ as x}from"./vue-vendor-dy9k-Yad.js";import{C as D}from"./index-LCGLvkB3.js";import{c as E,ah as L,a as N}from"./index-CCWaWN5g.js";import S from"./Article-CpXZd0IO.js";import V from"./Application-fUDlsiTy.js";import A from"./Project-SHfvlXUA.js";import{h as F}from"./header-OZa5fSDc.js";import{achieveList as U,details as j,teams as q,tags as z}from"./data-BsYYuWeG.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";const G=B({components:{CollapseContainer:D,Icon:E,Tag:w,Tabs:g,TabPane:g.TabPane,Article:S,Application:V,Project:A,[b.name]:b,[$.name]:$},setup(){const a=L();return{prefixCls:"account-center",avatar:P(()=>a.getUserInfo.avatar||F),tags:z,teams:q,details:j,achieveList:U}}}),J=["src"];function Q(a,i,R,X,H,K){const c=l("a-col"),_=l("Icon"),C=l("a-row"),k=l("Tag"),v=l("CollapseContainer"),y=l("TabPane"),I=l("Tabs");return t(),r("div",{class:n(a.prefixCls)},[s(C,{class:n(`${a.prefixCls}-top`)},{default:o(()=>[s(c,{span:9,class:n(`${a.prefixCls}-col`)},{default:o(()=>[s(C,null,{default:o(()=>[s(c,{span:8},{default:o(()=>[p("div",{class:n(`${a.prefixCls}-top__avatar`)},[p("img",{width:"70",src:a.avatar},null,8,J),i[0]||(i[0]=p("span",null,"Jeecg",-1)),i[1]||(i[1]=p("div",null,"海纳百川，有容乃大",-1))],2)]),_:1}),s(c,{span:16},{default:o(()=>[p("div",{class:n(`${a.prefixCls}-top__detail`)},[(t(!0),r(m,null,f(a.details,e=>(t(),r("p",{key:e.title},[s(_,{icon:e.icon},null,8,["icon"]),T(" "+u(e.title),1)]))),128))],2)]),_:1})]),_:1})]),_:1},8,["class"]),s(c,{span:7,class:n(`${a.prefixCls}-col`)},{default:o(()=>[s(v,{title:"标签",canExpan:!1},{default:o(()=>[(t(!0),r(m,null,f(a.tags,e=>(t(),d(k,{key:e,class:"mb-2"},{default:o(()=>[T(u(e),1)]),_:2},1024))),128))]),_:1})]),_:1},8,["class"]),s(c,{span:8,class:n(`${a.prefixCls}-col`)},{default:o(()=>[s(v,{class:n(`${a.prefixCls}-top__team`),title:"团队",canExpan:!1},{default:o(()=>[(t(!0),r(m,null,f(a.teams,(e,h)=>(t(),r("div",{key:h,class:n(`${a.prefixCls}-top__team-item`)},[s(_,{icon:e.icon,color:e.color},null,8,["icon","color"]),p("span",null,u(e.title),1)],2))),128))]),_:1},8,["class"])]),_:1},8,["class"])]),_:1},8,["class"]),p("div",{class:n(`${a.prefixCls}-bottom`)},[s(I,null,{default:o(()=>[(t(!0),r(m,null,f(a.achieveList,e=>(t(),d(y,{key:e.key,tab:e.name},{default:o(()=>[(t(),d(x(e.component)))]),_:2},1032,["tab"]))),128))]),_:1})],2)],2)}const ca=N(G,[["render",Q],["__scopeId","data-v-f5596f52"]]);export{ca as default};
