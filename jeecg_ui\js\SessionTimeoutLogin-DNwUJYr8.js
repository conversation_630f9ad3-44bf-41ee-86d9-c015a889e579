import{d as a,f as p,o as n,j as u,aB as c,ar as d,aD as l,at as f,k as _,as as g,u as B,m as S}from"./vue-vendor-dy9k-Yad.js";import C from"./Login-BbE4lNlw.js";import{F as I,ah as v,bD as x,B as M,bC as T,a as k}from"./index-CCWaWN5g.js";import"./LoginForm-Cv1KIRFA.js";import"./checkcode-DLY3GIII.js";import"./antd-vue-vendor-me9YkNVC.js";import"./LoginFormTitle-Bms3O5Qx.js";import"./vxe-table-vendor-B22HppNm.js";import"./ThirdModal-1GWAIf70.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFormItemSingle-Cw668yj5.js";import"./useThirdLogin-C4qxd7Ms.js";import"./ForgetPasswordForm-D5tOYC3b.js";import"./step1-CkQhLHRg.js";import"./step2-tEElob-Y.js";import"./index-CBCjSSNZ.js";import"./step3-dZT9Navo.js";import"./RegisterForm-DI32cwRE.js";import"./MobileForm-BL07NIax.js";import"./QrCodeForm-CVY8RjiB.js";import"./index-C1YxH9KC.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";const U=a({__name:"SessionTimeoutLogin",setup(b){const{prefixCls:r}=I("st-login"),e=v(),s=x(),i=M(),o=p(0),m=()=>i.getProjectConfig.permissionMode===T.BACK;return n(()=>{var t;o.value=(t=e.getUserInfo)==null?void 0:t.userId}),u(()=>{(o.value&&o.value!==e.getUserInfo.userId||m()&&s.getLastBuildMenuTime===0)&&document.location.reload()}),(t,h)=>(d(),c(S,null,{default:l(()=>[f("div",{class:g(B(r))},[_(C,{sessionTimeout:""})],2)]),_:1}))}}),uo=k(U,[["__scopeId","data-v-401b5eea"]]);export{uo as default};
