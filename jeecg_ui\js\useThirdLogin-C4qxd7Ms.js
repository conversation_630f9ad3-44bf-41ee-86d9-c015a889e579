var z=Object.defineProperty;var C=Object.getOwnPropertySymbols;var A=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable;var I=(t,n,i)=>n in t?z(t,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[n]=i,k=(t,n)=>{for(var i in n||(n={}))A.call(n,i)&&I(t,i,n[i]);if(C)for(var i of C(n))B.call(n,i)&&I(t,i,n[i]);return t};import{f as o,u as a}from"./vue-vendor-dy9k-Yad.js";import{u as D,N as G,b5 as J,ah as K,j as b,b6 as Q}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function W(){const{createMessage:t,notification:n}=D(),{t:i}=G(),M=J(),$=K(),y=o(""),l=o({}),g=o(!1),h=o(!1),T=o(""),u=o(!1),m=o(!1),d=o(""),U=o(""),p=o(!1),f=o(""),v=o("");function j(e){let s=`${M.uploadUrl}/sys/thirdLogin/render/${e}`;const w=window.open(s,`login ${e}`,"height=500, width=500, top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no");y.value=e,l.value={},g.value=!1;let L=function(N){let r=N.data;if(typeof r=="string")if(r==="登录失败")t.warning(r);else if(r.includes("绑定手机号")){h.value=!0;let q=r.split(",");T.value=q[1]}else c(r);else typeof r=="object"?r.isObj===!0&&(u.value=!0,l.value=k({},r)):t.warning("不识别的信息传递");w!=null&&w.closed&&window.removeEventListener("message",L,!1)};window.removeEventListener("message",L,!1),window.addEventListener("message",L,!1)}function c(e){a(g)===!1&&(g.value=!0,$.ThirdLogin({token:e,thirdType:a(y)}).then(s=>{s&&s.userInfo?n.success({message:i("sys.login.loginSuccessTitle"),description:`${i("sys.login.loginSuccessDesc")}: ${s.userInfo.realname}`,duration:3}):E(s)}))}function E(e){n.error({message:"登录失败",description:((e.response||{}).data||{}).message||e.message||"请求出现错误，请稍后再试",duration:4})}function O(){d.value="",U.value=l.value.uuid,u.value=!1,m.value=!0}function R(){p.value=!0,l.value.suffix=parseInt(Math.random()*98+1),b.post({url:"/sys/third/user/create",params:{thirdLoginInfo:a(l)}},{isTransformResponse:!1}).then(e=>{if(e.success){let s=e.result;c(s),u.value=!1}else t.warning(e.message)}).finally(()=>{p.value=!1})}function x(){let e=Object.assign({},a(l),{password:a(d)});b.post({url:"/sys/third/user/checkPassword",params:e},{isTransformResponse:!1}).then(s=>{s.success?(P(),c(s.result)):t.warning(s.message)})}function P(){m.value=!1,d.value="",U.value=""}function F(){return Q({mobile:a(f),smsmode:"0"})}function H(){a(f)||S("请输入手机号"),a(v)||S("请输入验证码");let e={mobile:a(f),captcha:a(v),thirdUserUuid:a(T)};b.post({url:"/sys/thirdLogin/bindingThirdPhone",params:e},{isTransformResponse:!1}).then(s=>{s.success?(h.value=!1,c(s.result)):t.warning(s.message)})}function S(e){n.error({message:"登录失败",description:e,duration:4})}return{thirdPasswordShow:m,thirdLoginCheckPassword:x,thirdLoginNoPassword:P,thirdLoginPassword:d,thirdConfirmShow:u,thirdCreateUserLoding:p,thirdLoginUserCreate:R,thirdLoginUserBind:O,bindingPhoneModal:h,thirdHandleOk:H,thirdPhone:f,thirdCaptcha:v,onThirdLogin:j,sendCodeApi:F}}export{W as useThirdLogin};
