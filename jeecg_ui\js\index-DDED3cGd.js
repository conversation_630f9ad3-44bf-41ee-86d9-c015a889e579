import{d as f,ag as e,aB as T,ar as h,aD as g,k as t}from"./vue-vendor-dy9k-Yad.js";import{D as b}from"./index-Dce_QJ6p.js";import{u as c}from"./index-BkGZ5fiW.js";import{P as _}from"./index-CtJ0w2CP.js";import{V as d}from"./antd-vue-vendor-me9YkNVC.js";import{personData as D,personSchema as S,refundData as B,refundSchema as R,refundTableSchema as v,refundTableData as y,refundTimeTableData as C,refundTimeTableSchema as k}from"./data-jOGkvSdb.js";import w from"./BasicTable-xCEZpGLb.js";import{a as P}from"./index-CCWaWN5g.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const $=f({components:{Description:b,BasicTable:w,PageWrapper:_,[d.name]:d},setup(){const[o]=c({title:"退货商品",dataSource:y,columns:v,pagination:!1,showIndexColumn:!1,scroll:{y:300},showSummary:!0,summaryFunc:n}),[p]=c({title:"退货进度",columns:k,pagination:!1,dataSource:C,showIndexColumn:!1,scroll:{y:300}});function n(s){let i=0,m=0;return s.forEach(r=>{i+=r.t5,m+=r.t6}),[{t1:"总计",t5:i,t6:m}]}return{registerRefundTable:o,registerTimeTable:p,refundSchema:R,refundData:B,personSchema:S,personData:D}}});function I(o,p,n,s,i,m){const r=e("Description"),a=e("a-divider"),l=e("BasicTable"),u=e("PageWrapper");return h(),T(u,{title:"基础详情页",contentBackground:""},{default:g(()=>[t(r,{size:"middle",title:"退款申请",bordered:!1,column:3,data:o.refundData,schema:o.refundSchema},null,8,["data","schema"]),t(a),t(r,{size:"middle",title:"用户信息",bordered:!1,column:3,data:o.personData,schema:o.personSchema},null,8,["data","schema"]),t(a),t(l,{onRegister:o.registerRefundTable},null,8,["onRegister"]),t(a),t(l,{onRegister:o.registerTimeTable},null,8,["onRegister"])]),_:1})}const Ao=P($,[["render",I],["__scopeId","data-v-76931493"]]);export{Ao as default};
