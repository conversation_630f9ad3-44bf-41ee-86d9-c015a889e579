var $=Object.defineProperty,O=Object.defineProperties;var D=Object.getOwnPropertyDescriptors;var C=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,S=Object.prototype.propertyIsEnumerable;var g=(e,t,o)=>t in e?$(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,k=(e,t)=>{for(var o in t||(t={}))N.call(t,o)&&g(e,o,t[o]);if(C)for(var o of C(t))S.call(t,o)&&g(e,o,t[o]);return e},w=(e,t)=>O(e,D(t));import{i as P,k as c,d as V,f as I,e as A,o as F,n as H,b as L,u as y,F as R,y as W,g as B}from"./vue-vendor-dy9k-Yad.js";import{c as T,b2 as U}from"./index-CCWaWN5g.js";import{k as h,V as X}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function Y(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!P(e)}const v="context-menu",q={width:{type:Number,default:156},customEvent:{type:Object,default:null},styles:{type:Object},showIcon:{type:Boolean,default:!0},axis:{type:Object,default(){return{x:0,y:0}}},items:{type:Array,default(){return[]}}},M=e=>{const{item:t}=e;return c("span",{style:"display: inline-block; width: 100%; ",class:"px-4",onClick:e.handler.bind(null,t)},[e.showIcon&&t.icon&&c(T,{class:"mr-2",icon:t.icon},null),c("span",null,[t.label])])},z=V({name:"ContextMenu",props:q,setup(e){const t=I(null),o=I(!1),i=A(()=>{const{axis:s,items:n,styles:r,width:l}=e,{x:a,y:m}=s||{x:0,y:0},p=(n||[]).length*40,b=l,x=document.body,_=x.clientWidth<a+b?a-b:a,j=x.clientHeight<m+p?m-p:m;return w(k({},r),{width:`${l}px`,left:`${_+1}px`,top:`${j+1}px`})});F(()=>{H(()=>o.value=!0)}),L(()=>{const s=y(t);s&&document.body.removeChild(s)});function f(s,n){const{handler:r,disabled:l}=s;l||(o.value=!1,n==null||n.stopPropagation(),n==null||n.preventDefault(),r==null||r())}function u(s){return s.map(n=>{const{disabled:r,label:l,children:a,divider:m=!1}=n,p={item:n,handler:f,showIcon:e.showIcon};return!a||a.length===0?c(R,null,[c(h.Item,{disabled:r,class:`${v}__item`,key:l},{default:()=>[c(M,p,null)]}),m?c(X,{key:`d-${l}`},null):null]):y(o)?c(h.SubMenu,{key:l,disabled:r,popupClassName:`${v}__popup`},{title:()=>c(M,p,null),default:()=>u(a)}):null})}return()=>{let s;if(!y(o))return null;const{items:n}=e;return c(h,{inlineIndent:12,mode:"vertical",class:v,ref:t,style:y(i)},Y(s=u(n))?s:{default:()=>[s]})}}}),d={domList:[],resolve:()=>{}},G=function(e){const{event:t}=e||{};if(t&&(t==null||t.preventDefault()),!!U)return new Promise(o=>{const i=document.body,f=document.createElement("div"),u={};e.styles&&(u.styles=e.styles),e.items&&(u.items=e.items),e.event&&(u.customEvent=t,u.axis={x:t.clientX,y:t.clientY});const s=c(z,u);W(s,f);const n=function(){d.resolve("")};d.domList.push(f);const r=function(){d.domList.forEach(l=>{try{l&&i.removeChild(l)}catch(a){}}),i.removeEventListener("click",n),i.removeEventListener("scroll",n)};d.resolve=function(l){r(),o(l)},r(),i.appendChild(f),i.addEventListener("click",n),i.addEventListener("scroll",n)})},E=function(){d&&(d.resolve(""),d.domList=[])};function te(e=!0){return B()&&e&&L(()=>{E()}),[G,E]}export{te as useContextMenu};
