var b=(q,T,f)=>new Promise((U,g)=>{var v=d=>{try{i(f.next(d))}catch(c){g(c)}},_=d=>{try{i(f.throw(d))}catch(c){g(c)}},i=d=>d.done?U(d.value):Promise.resolve(d.value).then(v,_);i((f=f.apply(q,T)).next())});import{d as E,f as z,e as L,o as N,ag as m,aq as R,ar as S,k as a,aD as t,at as o,G as s,u as w}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{g as A}from"./SupplyDemand-DK00S9Ao.js";import{i as F}from"./JAreaSelect-Db7Nhhc_.js";import{_ as D}from"./JUpload-CRos0F1P.js";import{J}from"./JEditorTiptap-BwAoWsi9.js";import{a as O}from"./index-CCWaWN5g.js";import"./BasicForm-DBcXiHk0.js";import"./antd-vue-vendor-me9YkNVC.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./areaDataUtil-BXVjRArW.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-ByPySmGo.js";const $={class:"step-panel"},G={class:"form-section"},j={class:"form-row"},H={class:"form-section"},K={class:"form-row"},P={class:"form-row basic-three-row"},Q={class:"form-section"},W={class:"form-row location-row"},X={class:"location-selects"},Y={class:"form-row specification-second-row"},Z={class:"form-section"},h={class:"form-row"},ee={class:"upload-container"},ae={class:"form-row"},te={class:"upload-container"},ie={class:"form-row"},le={class:"form-row"},oe={class:"rich-text-container"},re=E({__name:"Step1",props:{modelValue:{},locationLoading:{type:Boolean,default:!1},isEditMode:{type:Boolean,default:!1}},emits:["update:modelValue","area-change","get-current-location"],setup(q,{expose:T,emit:f}){const U=q,g=f,v=z(),_=z([]),i=L({get:()=>U.modelValue,set:n=>g("update:modelValue",n)}),d={materialType:[{required:!0,message:"请选择物资类型",trigger:"change"}],basicInfo:{infoTitle:[{required:!0,message:"请输入信息标题",trigger:"blur"},{min:2,max:100,message:"信息标题长度应在2-100个字符之间",trigger:"blur"}],depreciationDegree:[{required:!0,message:"请选择新旧程度",trigger:"change"}]},specification:{province:[{required:!0,message:"请选择省份",trigger:"change"}],storageMethod:[{required:!0,message:"请选择存放方式",trigger:"change"}],quantity:[{required:!0,message:"请输入物资数量",trigger:"blur"}],unit:[{required:!0,message:"请选择物资单位",trigger:"change"}],price:[{required:!0,message:"请输入物资价格",trigger:"blur"}]}},c=()=>b(null,null,function*(){try{const n=yield A();n&&Array.isArray(n)&&(_.value=n)}catch(n){}}),M=n=>b(null,null,function*(){const e=n[n.length-1];e.loading=!0;try{e.loading=!1}catch(p){e.loading=!1}}),V=(n,e)=>e.some(p=>p.name.toLowerCase().indexOf(n.toLowerCase())>-1),B=()=>b(null,null,function*(){var n;try{return yield(n=v.value)==null?void 0:n.validate(),!0}catch(e){const p=document.querySelector(".ant-form-item-has-error");return p&&p.scrollIntoView({behavior:"smooth",block:"center"}),!1}}),k=()=>{var n;(n=v.value)==null||n.clearValidate()};return N(()=>{c()}),T({validateForm:B,clearValidate:k}),(n,e)=>{const p=m("a-cascader"),u=m("a-form-item"),y=m("a-input"),r=m("a-select-option"),I=m("a-select"),x=m("a-input-number"),C=m("a-form");return S(),R("div",$,[a(C,{ref_key:"formRef",ref:v,model:i.value,rules:d,"validate-trigger":["change","blur"],"scroll-to-first-error":!0},{default:t(()=>[o("div",G,[e[16]||(e[16]=o("h3",{class:"section-title"},"求购物资类型",-1)),o("div",j,[a(u,{name:"materialType",class:"material-type-item"},{default:t(()=>[a(p,{value:i.value.materialType,"onUpdate:value":e[0]||(e[0]=l=>i.value.materialType=l),options:_.value,"load-data":M,placeholder:"请选择物资类型",size:"large","show-search":"","filter-option":V,"field-names":{label:"name",value:"id",children:"children"},"change-on-select":"",class:"material-type-cascader"},null,8,["value","options"])]),_:1})])]),o("div",H,[e[26]||(e[26]=o("h3",{class:"section-title"},"基本求购信息",-1)),o("div",K,[a(u,{label:"信息标题",name:["basicInfo","infoTitle"],required:"",class:"info-title-item"},{default:t(()=>[a(y,{value:i.value.basicInfo.infoTitle,"onUpdate:value":e[1]||(e[1]=l=>i.value.basicInfo.infoTitle=l),placeholder:"格式建议:地区+求购+设备名称",size:"large"},null,8,["value"])]),_:1})]),o("div",P,[a(u,{label:"物资品牌",name:["basicInfo","brand"],class:"basic-three-item"},{default:t(()=>[a(y,{value:i.value.basicInfo.brand,"onUpdate:value":e[2]||(e[2]=l=>i.value.basicInfo.brand=l),placeholder:"请输入物资品牌",size:"large"},null,8,["value"])]),_:1}),a(u,{label:"物资型号",name:["basicInfo","model"],class:"basic-three-item"},{default:t(()=>[a(y,{value:i.value.basicInfo.model,"onUpdate:value":e[3]||(e[3]=l=>i.value.basicInfo.model=l),placeholder:"请输入物资型号",size:"large"},null,8,["value"])]),_:1}),a(u,{label:"新旧程度",name:["basicInfo","depreciationDegree"],required:"",class:"basic-three-item"},{default:t(()=>[a(I,{value:i.value.basicInfo.depreciationDegree,"onUpdate:value":e[4]||(e[4]=l=>i.value.basicInfo.depreciationDegree=l),placeholder:"请选择新旧程度",size:"large"},{default:t(()=>[a(r,{value:1},{default:t(()=>e[17]||(e[17]=[s("一成新")])),_:1,__:[17]}),a(r,{value:2},{default:t(()=>e[18]||(e[18]=[s("二成新")])),_:1,__:[18]}),a(r,{value:3},{default:t(()=>e[19]||(e[19]=[s("三成新")])),_:1,__:[19]}),a(r,{value:4},{default:t(()=>e[20]||(e[20]=[s("四成新")])),_:1,__:[20]}),a(r,{value:5},{default:t(()=>e[21]||(e[21]=[s("五成新")])),_:1,__:[21]}),a(r,{value:6},{default:t(()=>e[22]||(e[22]=[s("六成新")])),_:1,__:[22]}),a(r,{value:7},{default:t(()=>e[23]||(e[23]=[s("七成新")])),_:1,__:[23]}),a(r,{value:8},{default:t(()=>e[24]||(e[24]=[s("八成新")])),_:1,__:[24]}),a(r,{value:9},{default:t(()=>e[25]||(e[25]=[s("九成新")])),_:1,__:[25]})]),_:1},8,["value"])]),_:1})])]),o("div",Q,[e[38]||(e[38]=o("h3",{class:"section-title"},"规格与存放",-1)),o("div",W,[o("div",X,[a(u,{name:["specification","province"],class:"location-area-item"},{default:t(()=>[a(w(F),{province:i.value.specification.province,"onUpdate:province":e[5]||(e[5]=l=>i.value.specification.province=l),city:i.value.specification.city,"onUpdate:city":e[6]||(e[6]=l=>i.value.specification.city=l),area:i.value.specification.area,"onUpdate:area":e[7]||(e[7]=l=>i.value.specification.area=l),placeholder:"请选择省市区",level:3},null,8,["province","city","area"])]),_:1})])]),o("div",Y,[a(u,{label:"存放方式",name:["specification","storageMethod"],required:"",class:"specification-item"},{default:t(()=>[a(I,{value:i.value.specification.storageMethod,"onUpdate:value":e[8]||(e[8]=l=>i.value.specification.storageMethod=l),placeholder:"请选择存放方式",size:"large"},{default:t(()=>[a(r,{value:1},{default:t(()=>e[27]||(e[27]=[s("仓库")])),_:1,__:[27]}),a(r,{value:2},{default:t(()=>e[28]||(e[28]=[s("露天堆放")])),_:1,__:[28]})]),_:1},8,["value"])]),_:1}),a(u,{label:"物资数量",name:["specification","quantity"],required:"",class:"specification-item"},{default:t(()=>[a(x,{value:i.value.specification.quantity,"onUpdate:value":e[9]||(e[9]=l=>i.value.specification.quantity=l),placeholder:"请输入数量",style:{width:"100%"},size:"large",min:1},null,8,["value"])]),_:1}),a(u,{label:"物资单位",name:["specification","unit"],required:"",class:"specification-item"},{default:t(()=>[a(I,{value:i.value.specification.unit,"onUpdate:value":e[10]||(e[10]=l=>i.value.specification.unit=l),placeholder:"请选择单位",size:"large"},{default:t(()=>[a(r,{value:"台"},{default:t(()=>e[29]||(e[29]=[s("台")])),_:1,__:[29]}),a(r,{value:"辆"},{default:t(()=>e[30]||(e[30]=[s("辆")])),_:1,__:[30]}),a(r,{value:"套"},{default:t(()=>e[31]||(e[31]=[s("套")])),_:1,__:[31]}),a(r,{value:"个"},{default:t(()=>e[32]||(e[32]=[s("个")])),_:1,__:[32]}),a(r,{value:"件"},{default:t(()=>e[33]||(e[33]=[s("件")])),_:1,__:[33]}),a(r,{value:"批"},{default:t(()=>e[34]||(e[34]=[s("批")])),_:1,__:[34]}),a(r,{value:"米"},{default:t(()=>e[35]||(e[35]=[s("米")])),_:1,__:[35]}),a(r,{value:"吨"},{default:t(()=>e[36]||(e[36]=[s("吨")])),_:1,__:[36]}),a(r,{value:"公斤"},{default:t(()=>e[37]||(e[37]=[s("公斤")])),_:1,__:[37]})]),_:1},8,["value"])]),_:1}),a(u,{label:"物资价格",name:["specification","price"],required:"",class:"specification-item"},{default:t(()=>[a(x,{value:i.value.specification.price,"onUpdate:value":e[11]||(e[11]=l=>i.value.specification.price=l),placeholder:"请输入价格",style:{width:"100%"},size:"large",min:0,precision:2,formatter:l=>`￥ ${l}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:l=>l.replace(/￥\s?|(,*)/g,"")},null,8,["value","formatter","parser"])]),_:1})])]),o("div",Z,[e[41]||(e[41]=o("h3",{class:"section-title"},"亮点与展示",-1)),o("div",h,[a(u,{label:"物资照片",name:["display","images"],class:"upload-item"},{default:t(()=>[o("div",ee,[a(w(D),{value:i.value.display.images,"onUpdate:value":e[12]||(e[12]=l=>i.value.display.images=l),multiple:!0,"max-count":15,accept:"image/*","list-type":"picture-card","file-type":"image","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[39]||(e[39]=o("div",{class:"upload-tip"},"建议尺寸800*800像素，最多上传15张",-1))])]),_:1})]),o("div",ae,[a(u,{label:"物资视频",name:["display","videos"],class:"upload-item"},{default:t(()=>[o("div",te,[a(w(D),{value:i.value.display.videos,"onUpdate:value":e[13]||(e[13]=l=>i.value.display.videos=l),multiple:!1,"max-count":1,accept:"video/*","return-url":!1,class:"upload-component upload-video"},null,8,["value"]),e[40]||(e[40]=o("div",{class:"upload-tip"},"建议视频宽高比16:9，突出商品核心卖点，时长9~30秒",-1))])]),_:1})]),o("div",ie,[a(u,{label:"求购亮点",name:["display","highlights"],class:"highlights-item"},{default:t(()=>[a(y,{value:i.value.display.highlights,"onUpdate:value":e[14]||(e[14]=l=>i.value.display.highlights=l),placeholder:"请输入求购亮点",size:"large"},null,8,["value"])]),_:1})]),o("div",le,[a(u,{label:"物资详细描述",name:["display","materialDesc"],class:"material-desc-item"},{default:t(()=>[o("div",oe,[a(w(J),{value:i.value.display.materialDesc,"onUpdate:value":e[15]||(e[15]=l=>i.value.display.materialDesc=l),placeholder:"请详细描述物资信息...","auto-focus":!1},null,8,["value"])])]),_:1})])])]),_:1},8,["model"])])}}}),ua=O(re,[["__scopeId","data-v-4c5cb2dd"]]);export{ua as default};
