import{d as $,f as p,r as j,ag as d,aB as H,ar as k,aD as l,at as o,k as t,G as i,u,aq as J,ah as K,F as O}from"./vue-vendor-dy9k-Yad.js";import{P as Q}from"./index-CtJ0w2CP.js";import{C as m}from"./index-Diw57m_E.js";import{u as R,a as S}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const X={class:"p-4"},Y={class:"demo-buttons"},Z={class:"custom-content"},h={class:"no-footer-content"},ee={class:"custom-actions"},oe={class:"large-content"},te=$({__name:"index",setup(le){const{createMessage:a}=R(),f=p(!1),C=p(!1),v=p(!1),_=p(!1),c=p(!1),r=j({username:"",email:"",description:""}),b=[{title:"姓名",dataIndex:"name",key:"name"},{title:"年龄",dataIndex:"age",key:"age"},{title:"地址",dataIndex:"address",key:"address"},{title:"操作",key:"action"}],M=[{key:"1",name:"张三",age:32,address:"北京市朝阳区"},{key:"2",name:"李四",age:28,address:"上海市浦东新区"},{key:"3",name:"王五",age:35,address:"广州市天河区"}],x=()=>{f.value=!0},w=()=>{C.value=!0},B=()=>{v.value=!0},V=()=>{_.value=!0},U=()=>{c.value=!0},F=()=>{a.success("基础弹窗确认"),f.value=!1},N=()=>{a.info("基础弹窗取消")},D=()=>{a.success("确认选择自主服务"),C.value=!1},I=()=>{a.info("取消自主服务")},z=()=>{a.success("自定义内容弹窗确认"),v.value=!1},L=()=>{a.info("自定义内容弹窗取消")},P=()=>{a.info("无底部弹窗关闭")},q=()=>{a.success("大尺寸弹窗确认"),c.value=!1},A=()=>{a.info("大尺寸弹窗取消")};return(ne,e)=>{const s=d("a-button"),E=d("a-card"),y=d("a-input"),g=d("a-form-item"),G=d("a-textarea"),T=d("a-form"),W=d("a-table");return k(),H(u(Q),{title:"自定义弹窗组件演示"},{default:l(()=>[o("div",X,[t(E,{title:"CustomModal 组件演示"},{default:l(()=>[o("div",Y,[t(s,{type:"primary",onClick:x},{default:l(()=>e[9]||(e[9]=[i("基础弹窗")])),_:1,__:[9]}),t(s,{type:"primary",onClick:w},{default:l(()=>e[10]||(e[10]=[i("确认弹窗")])),_:1,__:[10]}),t(s,{type:"primary",onClick:B},{default:l(()=>e[11]||(e[11]=[i("自定义内容弹窗")])),_:1,__:[11]}),t(s,{type:"primary",onClick:V},{default:l(()=>e[12]||(e[12]=[i("无底部弹窗")])),_:1,__:[12]}),t(s,{type:"primary",onClick:U},{default:l(()=>e[13]||(e[13]=[i("大尺寸弹窗")])),_:1,__:[13]})]),e[14]||(e[14]=o("div",{class:"demo-description"},[o("h3",null,"组件特性"),o("ul",null,[o("li",null,"✅ 固定48px高度的渐变头部"),o("li",null,"✅ 左侧logo + 中间标题 + 右侧关闭按钮布局"),o("li",null,"✅ 标题使用PingFang Bold字体，18px，白色"),o("li",null,"✅ 头部背景渐变：rgba(0, 76, 102, 0.9) → rgba(0, 76, 102, 0.6)"),o("li",null,"✅ 支持自定义内容和底部按钮"),o("li",null,"✅ 完全基于Ant Design Modal封装")])],-1))]),_:1,__:[14]}),t(u(m),{open:f.value,"onUpdate:open":e[0]||(e[0]=n=>f.value=n),title:"基础弹窗",onConfirm:F,onCancel:N},{default:l(()=>e[15]||(e[15]=[o("p",null,"这是一个基础的自定义弹窗示例。",-1),o("p",null,"头部使用了渐变背景色，左侧有logo，中间是标题，右侧是关闭按钮。",-1),o("p",null,"底部有取消和确认按钮。",-1)])),_:1,__:[15]},8,["open"]),t(u(m),{open:C.value,"onUpdate:open":e[1]||(e[1]=n=>C.value=n),title:"自主委托",width:"600",onConfirm:D,onCancel:I},{default:l(()=>e[16]||(e[16]=[o("div",{class:"confirm-content"},[o("p",null,"我方仅将客户资产信息在我网进行公示，我司不会对客户所发布信息做任何协助"),o("p",null,"请确认是否选择自主服务")],-1)])),_:1,__:[16]},8,["open"]),t(u(m),{open:v.value,"onUpdate:open":e[5]||(e[5]=n=>v.value=n),title:"自定义内容弹窗",width:"700",onConfirm:z,onCancel:L},{default:l(()=>[o("div",Z,[t(T,{model:r,layout:"vertical"},{default:l(()=>[t(g,{label:"用户名"},{default:l(()=>[t(y,{value:r.username,"onUpdate:value":e[2]||(e[2]=n=>r.username=n),placeholder:"请输入用户名"},null,8,["value"])]),_:1}),t(g,{label:"邮箱"},{default:l(()=>[t(y,{value:r.email,"onUpdate:value":e[3]||(e[3]=n=>r.email=n),placeholder:"请输入邮箱"},null,8,["value"])]),_:1}),t(g,{label:"描述"},{default:l(()=>[t(G,{value:r.description,"onUpdate:value":e[4]||(e[4]=n=>r.description=n),placeholder:"请输入描述",rows:4},null,8,["value"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["open"]),t(u(m),{open:_.value,"onUpdate:open":e[7]||(e[7]=n=>_.value=n),title:"无底部弹窗","show-footer":!1,onClose:P},{default:l(()=>[o("div",h,[e[18]||(e[18]=o("p",null,"这是一个没有底部按钮的弹窗。",-1)),e[19]||(e[19]=o("p",null,"只能通过右上角的关闭按钮或点击遮罩层关闭。",-1)),o("div",ee,[t(s,{onClick:e[6]||(e[6]=n=>_.value=!1)},{default:l(()=>e[17]||(e[17]=[i("自定义关闭")])),_:1,__:[17]})])])]),_:1},8,["open"]),t(u(m),{open:c.value,"onUpdate:open":e[8]||(e[8]=n=>c.value=n),title:"大尺寸弹窗",width:"900",onConfirm:q,onCancel:A},{default:l(()=>[o("div",oe,[e[22]||(e[22]=o("h4",null,"这是一个大尺寸的弹窗",-1)),e[23]||(e[23]=o("p",null,"可以容纳更多的内容，比如表格、图表等复杂组件。",-1)),t(W,{columns:b,"data-source":M,pagination:!1,size:"small"},{bodyCell:l(({column:n,record:ae})=>[n.key==="action"?(k(),J(O,{key:0},[t(s,{type:"link",size:"small"},{default:l(()=>e[20]||(e[20]=[i("编辑")])),_:1,__:[20]}),t(s,{type:"link",size:"small",danger:""},{default:l(()=>e[21]||(e[21]=[i("删除")])),_:1,__:[21]})],64)):K("",!0)]),_:1})])]),_:1},8,["open"])])]),_:1})}}}),Ve=S(te,[["__scopeId","data-v-1f2223b2"]]);export{Ve as default};
