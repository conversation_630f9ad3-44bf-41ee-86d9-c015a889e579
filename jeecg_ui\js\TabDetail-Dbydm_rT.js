import{d as p,aN as n,ag as i,aB as m,ar as c,aD as u,at as d,au as l}from"./vue-vendor-dy9k-Yad.js";import{P as f}from"./index-CtJ0w2CP.js";import{bt as _,a as b}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const g=p({name:"TabDetail",components:{PageWrapper:f},setup(){var e,a;const t=(a=(e=n().params)==null?void 0:e.id)!=null?a:-1,{setTitle:r}=_();return r(`No.${t} - 详情信息`),{index:t}}});function x(o,t,r,e,a,T){const s=i("PageWrapper");return c(),m(s,{title:"Tab详情页面"},{default:u(()=>[d("div",null,l(o.index)+" - 详情页内容在此",1)]),_:1})}const w=b(g,[["render",x]]);export{w as default};
