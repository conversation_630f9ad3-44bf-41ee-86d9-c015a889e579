import{b7 as o}from"./index-CCWaWN5g.js";var u=Object.defineProperty,h=Object.defineProperties,p=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,m=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,r=(e,t,s)=>t in e?u(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,f=(e,t)=>{for(var s in t||(t={}))m.call(t,s)&&r(e,s,t[s]);if(n)for(var s of n(t))d.call(t,s)&&r(e,s,t[s]);return e},b=(e,t)=>h(e,p(t));const l=o({id:"airag-flow-run",state:()=>({status:"",inputParams:{},outputParams:{},nodeSteps:[],time:0,timeText:"",beginTime:0,resMessage:""}),getters:{isRunning(){return this.status==="running"},isFinished(){return this.status==="finished"||this.status==="failed"},isFailed(){return this.status==="failed"}},actions:{initStatus(){this.time=0,this.timeText="",this.status="",this.nodeSteps=[],this.inputParams={},this.outputParams={},this.resMessage=""},start(e){this.initStatus(),this.status="running",this.beginTime=Date.now(),this.inputParams=e},finish(e,t,s){const i=e?this.nodeSteps.some(a=>a.status==="fail"):!0;this.status=i?"failed":"finished",this.time=Date.now()-this.beginTime,this.timeText=`${(this.time/1e3).toFixed(3)}s`,this.outputParams=s,this.resMessage=t},end(){this.initStatus()},addStep(e){this.nodeSteps.push(b(f({},e),{time:0,timeText:"",beginTime:Date.now(),expansion:!1}))},updateStepStatus(e,t,s){const i=this.nodeSteps.find(a=>a.node.id===e);i&&(i.status=t,i.outputParams=s,(t==="success"||t==="fail")&&(i.time=Date.now()-i.beginTime,i.timeText=`${(i.time/1e3).toFixed(3)}s`))},getStep(e){return this.nodeSteps.find(t=>t.node.id===e)}}});export{l as x};
