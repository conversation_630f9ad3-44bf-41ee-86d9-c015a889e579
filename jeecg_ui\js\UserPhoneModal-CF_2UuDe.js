var X=Object.defineProperty,Y=Object.defineProperties;var Z=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var ee=Object.prototype.hasOwnProperty,se=Object.prototype.propertyIsEnumerable;var I=(u,n,t)=>n in u?X(u,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[n]=t,S=(u,n)=>{for(var t in n||(n={}))ee.call(n,t)&&I(u,t,n[t]);if(U)for(var t of U(n))se.call(n,t)&&I(u,t,n[t]);return u},N=(u,n)=>Y(u,Z(n));var w=(u,n,t)=>new Promise((i,d)=>{var s=p=>{try{g(t.next(p))}catch(y){d(y)}},h=p=>{try{g(t.throw(p))}catch(y){d(y)}},g=p=>p.done?i(p.value):Promise.resolve(p.value).then(s,h);g((t=t.apply(u,n)).next())});import{d as H,r as q,f as v,ag as R,aB as x,ar as b,aD as r,ah as D,aq as $,k as a,at as f,au as oe,u as z,G as E,aE as ae}from"./vue-vendor-dy9k-Yad.js";import{I as ne}from"./BasicModal-BLFvpBuk.js";import{C as M}from"./index-DFrpKMGa.js";import{ah as te,ac as le,b6 as re,j as de,aV as ue,u as ie,bS as pe,a as me}from"./index-CCWaWN5g.js";import"./index-Diw57m_E.js";import{rules as G}from"./validator-B_KkcUnu.js";import{a5 as ce}from"./antd-vue-vendor-me9YkNVC.js";import{u as fe,a as he}from"./UserSetting.api-BJ086Ekj.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./CustomModal-BakuIxQv.js";import"./user.api-mLAlJze4.js";const _e={key:0},ve={class:"phone-padding"},ge={key:1},ye={class:"phone-padding"},Pe=H({name:"user-replace-phone-modal"}),ke=H(N(S({},Pe),{emits:["register","success"],setup(u,{emit:n}){const t=te(),{createMessage:i}=ie(),d=q({phone:"",smscode:""}),s=q({phone:"",smscode:"",newPhone:"",phoneText:"",newSmsCode:""}),h=v(),g=v({}),p={phone:[S({},G.duplicateCheckRule("sys_user","phone",d,{label:"手机号"})[0]),{pattern:/^1[3456789]\d{9}$/,message:"手机号码格式有误"}],smscode:[{required:!0,message:"请输入验证码"}]},y={newPhone:[S({},G.duplicateCheckRule("sys_user","phone",d,{label:"手机号"})[0]),{pattern:/^1[3456789]\d{9}$/,message:"手机号码格式有误"}],smscode:[{required:!0,message:"请输入验证码"}],newSmsCode:[{required:!0,message:"请输入验证码"}]},Ce=ce.useForm,F=v(""),A=n,P=v("updatePhone"),[L,{setModalProps:j,closeModal:V}]=le(l=>w(null,null,function*(){j({confirmLoading:!1}),l.record.phone?(s.phone="",s.smscode="",k.value=0,F.value="修改手机号",P.value="updatePhone",Object.assign(s,l.record)):(F.value="绑定手机号",P.value="bindPhone",l.record.smscode="",Object.assign(d,l.record),setTimeout(()=>{h.value.resetFields(),h.value.clearValidate()},300)),g.value=l.record}));function J(){return re({mobile:d.phone,smsmode:pe.REGISTER})}function B(l){let e="";k.value===0?e=s.phone:e=s.newPhone;let o={phone:e,type:l};return new Promise((C,_)=>{de.post({url:"/sys/user/sendChangePhoneSms",params:o},{isTransformResponse:!1}).then(m=>{m.success?C(!0):(m.code!=ue.PHONE_SMS_FAIL_CODE&&(i.error(m.message||"未知问题"),_()),_(m))}).catch(m=>{i.error(m.message||"未知问题"),_()})})}function K(){return w(this,null,function*(){yield h.value.validateFields(),fe(d).then(l=>{l.success?(i.success(P.value==="updatePhone"?"修改手机号成功":"绑定手机号成功"),A("success"),V()):i.warning(l.message)})})}const k=v(0),O=v();function Q(){return w(this,null,function*(){let l={phone:s.phone,smscode:s.smscode,type:"verifyOriginalPhone"};T(l,1)})}function W(){T({phone:s.phone,newPhone:s.newPhone,smscode:s.smscode,type:"updatePhone"},0)}function T(l,e){return w(this,null,function*(){yield O.value.validateFields(),he(l).then(o=>{o.success?(k.value=e,e==0&&(i.success(o.message),A("success"),V()),s.smscode=""):i.warn(o.message)}).catch(o=>{i.warn(o.message)})})}return(l,e)=>{const o=R("a-form-item"),C=R("a-button"),_=R("a-input"),m=R("a-form");return b(),x(ne,ae(l.$attrs,{onRegister:z(L),width:"500px",title:F.value,showCancelBtn:!1,showOkBtn:!1}),{default:r(()=>[P.value==="updatePhone"?(b(),x(m,{key:0,class:"antd-modal-form",ref_key:"updateFormRef",ref:O,model:s,rules:y},{default:r(()=>[k.value===0?(b(),$("div",_e,[a(o,{name:"phoneText"},{default:r(()=>[e[5]||(e[5]=f("span",{class:"black font-size-13"},"原手机号",-1)),f("div",ve,[f("span",null,oe(s.phoneText),1)])]),_:1,__:[5]}),a(o,{name:"smscode"},{default:r(()=>[e[6]||(e[6]=f("span",{class:"black font-size-13"},"验证码",-1)),a(z(M),{class:"phone-padding",size:"large",value:s.smscode,"onUpdate:value":e[0]||(e[0]=c=>s.smscode=c),placeholder:"输入6位验证码",sendCodeApi:()=>B("verifyOriginalPhone")},null,8,["value","sendCodeApi"])]),_:1,__:[6]}),a(o,null,{default:r(()=>[a(C,{size:"large",type:"primary",block:"",onClick:Q},{default:r(()=>e[7]||(e[7]=[E(" 下一步 ")])),_:1,__:[7]})]),_:1})])):k.value===1?(b(),$("div",ge,[a(o,{name:"newPhone"},{default:r(()=>[e[8]||(e[8]=f("span",{class:"black font-size-13"},"新手机号",-1)),f("div",ye,[a(_,{value:s.newPhone,"onUpdate:value":e[1]||(e[1]=c=>s.newPhone=c),placeholder:"请输入新手机号"},null,8,["value"])])]),_:1,__:[8]}),a(o,{name:"smscode"},{default:r(()=>[e[9]||(e[9]=f("span",{class:"black font-size-13"},"验证码",-1)),a(z(M),{class:"phone-padding",size:"large",value:s.smscode,"onUpdate:value":e[2]||(e[2]=c=>s.smscode=c),placeholder:"输入6位验证码",sendCodeApi:()=>B("updatePhone")},null,8,["value","sendCodeApi"])]),_:1,__:[9]}),a(o,null,{default:r(()=>[a(C,{size:"large",type:"primary",block:"",onClick:W},{default:r(()=>e[10]||(e[10]=[E(" 完成 ")])),_:1,__:[10]})]),_:1})])):D("",!0)]),_:1},8,["model"])):P.value==="bindPhone"?(b(),x(m,{key:1,class:"antd-modal-form",ref_key:"formRef",ref:h,model:d,rules:p},{default:r(()=>[a(o,{name:"phone"},{default:r(()=>[a(_,{size:"large",value:d.phone,"onUpdate:value":e[3]||(e[3]=c=>d.phone=c),placeholder:"请输入手机号"},null,8,["value"])]),_:1}),a(o,{name:"smscode"},{default:r(()=>[a(z(M),{size:"large",value:d.smscode,"onUpdate:value":e[4]||(e[4]=c=>d.smscode=c),placeholder:"输入6位验证码",sendCodeApi:J},null,8,["value"])]),_:1}),a(o,null,{default:r(()=>[a(C,{size:"large",type:"primary",block:"",onClick:K},{default:r(()=>e[11]||(e[11]=[E(" 确认 ")])),_:1,__:[11]})]),_:1})]),_:1},8,["model"])):D("",!0)]),_:1},16,["onRegister","title"])}}})),Le=me(ke,[["__scopeId","data-v-695b1004"]]);export{Le as default};
