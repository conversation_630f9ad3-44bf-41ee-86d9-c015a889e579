import{d as S,f as c,e as E,u as _,ag as t,aB as C,ar as M,as as k,aD as n,k as a}from"./vue-vendor-dy9k-Yad.js";import{J as x}from"./index-L3cSIXth.js";import{X as v,x as D,u as w,a as A}from"./index-CCWaWN5g.js";import{o as I}from"./select-nVA4yav1.js";import{B}from"./index-Xq00qzUB.js";import{_ as j}from"./JEllipsis-BsXuWNHJ.js";import{y as T,A as U}from"./componentMap-Bkie1n3v.js";import{h as V}from"./JAddInput-CxJ-JBK-.js";import{i as Q}from"./JAreaSelect-Db7Nhhc_.js";import{u as L}from"./JPopup-CeU6ry6r.js";import R from"./JAreaLinkage-DFCdF3cr.js";import{B as F}from"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JSelectUser-COkExGbu.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./areaDataUtil-BXVjRArW.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";const{isDisabledAuth:K}=v(),Y=[{field:"jdst",component:"JDictSelectTag",label:"性别下拉",helpMessage:["component模式"],componentProps:{dictCode:"sex"},colProps:{span:12}},{field:"jdst",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"jdst1",component:"JDictSelectTag",label:"性别选择",helpMessage:["component模式"],componentProps:{dictCode:"sex",type:"radioButton"},colProps:{span:12}},{field:"jdst1",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"jdst2",component:"JDictSelectTag",label:"字典表下拉",helpMessage:["component模式"],componentProps:{dictCode:"sys_user,realname,id"},colProps:{span:12}},{field:"jdst2",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"jdst3",component:"JDictSelectTag",label:"字典表下拉(带条件)",helpMessage:["component模式"],componentProps:{dictCode:"sys_user,realname,id,username!='admin' order by create_time"},colProps:{span:12}},{field:"jdst3",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"jsst",component:"JSearchSelect",label:"字典搜索(同步)",colProps:{span:12},componentProps:{dictOptions:[{text:"选项一",value:"1"},{text:"选项二",value:"2"},{text:"选项三",value:"3"}]}},{field:"jsst",component:"JEllipsis",label:"选择值",colProps:{span:12}},{field:"jsst2",component:"JSearchSelect",label:"字典搜索(异步)",colProps:{span:12},componentProps:{dict:"sys_depart,depart_name,id",pageSize:6,async:!0}},{field:"jsst2",component:"JEllipsis",label:"选择值",colProps:{span:12}},{field:"xldx",component:"JDictSelectTag",label:"字典下拉多选",colProps:{span:12},componentProps:{dictCode:"sex",mode:"multiple"}},{field:"xldx",component:"JEllipsis",label:"选择值",colProps:{span:12}},{field:"xldx2",component:"JSelectMultiple",label:"字典下拉多选2",colProps:{span:12},componentProps:{dictCode:"sex"}},{field:"xldx2",component:"JEllipsis",label:"选择值",colProps:{span:12}},{field:"dxxlk",component:"JDictSelectTag",label:"字典下拉单选",colProps:{span:12},componentProps:{dictCode:"sex"}},{field:"dxxlk",component:"JEllipsis",label:"选择值",colProps:{span:12}},{label:"可输入下拉",field:"selectInput",component:"JSelectInput",componentProps:{options:[{label:"选项一",value:"1"},{label:"选项二",value:"2"},{label:"选项三",value:"3"}]},colProps:{span:12}},{field:"selectInput",component:"JEllipsis",label:"选择值",colProps:{span:12}},{field:"depart3",component:"JSelectDept",label:"选择部门—自定义值",helpMessage:["component模式"],componentProps:{showButton:!1,rowKey:"orgCode",primaryKey:"orgCode"},colProps:{span:12}},{field:"depart3",component:"JEllipsis",label:"选中部门",colProps:{span:12}},{field:"depart2",component:"JSelectDept",label:"选择部门",helpMessage:["component模式"],componentProps:{showButton:!1},colProps:{span:12}},{field:"depart2",component:"JEllipsis",label:"选中部门",colProps:{span:12}},{field:"user2",component:"JSelectUser",label:"用户选择组件",helpMessage:["component模式"],componentProps:{labelKey:"realname",rowKey:"id",showSelected:!0},colProps:{span:12}},{field:"user2",component:"JEllipsis",label:"选中用户",colProps:{span:12}},{field:"user3",component:"JSelectUserByDept",label:"部门选择用户",helpMessage:["component模式"],componentProps:{labelKey:"realname",rowKey:"username"},colProps:{span:12}},{field:"user3",component:"JEllipsis",label:"选中用户",colProps:{span:12}},{field:"user4",component:"JSelectUserByDepartment",label:"部门选择用户",helpMessage:["component模式"],defaultValue:"",componentProps:{labelKey:"realname",rowKey:"username"},colProps:{span:12}},{field:"role2",component:"JSelectRole",label:"角色选择组件",helpMessage:["component模式"],colProps:{span:12}},{field:"role2",component:"JEllipsis",label:"选中角色",colProps:{span:12}},{field:"position2",component:"JSelectPosition",label:"职务选择组件",helpMessage:["component模式"],colProps:{span:12},componentProps:{async:!0,showSelectTable:!0}},{field:"position2",component:"JEllipsis",label:"选中职务",colProps:{span:12}},{field:"checkbox1",component:"JCheckbox",label:"JCheckbox组件1",helpMessage:["component模式"],defaultValue:"1,2",componentProps:{options:[{label:"男",value:"1"},{label:"女",value:"2"}]},colProps:{span:12}},{field:"checkbox1",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"checkbox2",component:"Input",label:"JCheckbox组件2",defaultValue:"1",helpMessage:["插槽模式"],slot:"JCheckbox",colProps:{span:12}},{field:"checkbox2",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"data1",label:"日期选择",component:"DatePicker",componentProps:{showTime:!0,valueFormat:"YYYY-MM-DD HH:mm:ss"},colProps:{span:12}},{field:"data1",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"data2",label:"年份范围选择",component:"RangePicker",componentProps:{picker:"year",valueFormat:"YYYY"},colProps:{span:12}},{field:"data2",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"hk",component:"Input",label:"滑块验证码",helpMessage:["插槽模式"],slot:"dargVerify",colProps:{span:12}},{field:"hk",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"JTreeDict",component:"JTreeDict",label:"树字典",helpMessage:["component模式"],colProps:{span:12}},{field:"JTreeDict",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"ts",component:"JTreeSelect",label:"下拉树选择",helpMessage:["component模式"],componentProps:{dict:"sys_permission,name,id",pidField:"parent_id",hasChildField:"is_leaf",converIsLeafVal:0},colProps:{span:12}},{field:"ts",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"ts1",component:"JTreeSelect",label:"下拉树多选",helpMessage:["component模式"],componentProps:{dict:"sys_permission,name,id",pidField:"parent_id",hasChildField:"is_leaf",converIsLeafVal:0,multiple:!0},colProps:{span:12}},{field:"ts1",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"category",component:"JCategorySelect",label:"分类字典树",helpMessage:["component模式"],defaultValue:"",componentProps:{pcode:"B01",multiple:!0},colProps:{span:12}},{field:"category",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"JEasyCron",component:"JEasyCron",label:"JEasyCron",helpMessage:["component模式"],colProps:{span:12},defaultValue:"* * * * * ? *",rules:[{validator:x}]},{field:"JEasyCron",component:"JEllipsis",label:"选择值",colProps:{span:12}},{field:"JInput",component:"JInput",label:"特殊查询组件",helpMessage:["插槽模式"],slot:"JInput",colProps:{span:12}},{field:"jinputtype",component:"Select",label:"查询类型",componentProps:{options:[{value:"like",label:"模糊（like）"},{value:"ne",label:"不等于（ne）"},{value:"ge",label:"大于等于（ge）"},{value:"le",label:"小于等于（le)"}]},colProps:{span:6}},{field:"JInput",component:"JEllipsis",label:"输入值",colProps:{span:6}},{field:"field1",component:"Select",label:"省市区选择",helpMessage:["插槽模式"],slot:"jAreaLinkage",colProps:{span:12},defaultValue:["130000","130200"]},{field:"field1",component:"JEllipsis",label:"选中值",colProps:{span:12},componentProps:{isAreaCode:!0}},{field:"field0",component:"Select",label:"禁用组件(方式一)",helpMessage:["插槽模式"],slot:"jAreaLinkage1",colProps:{span:12},defaultValue:["130000","130200"]},{field:"field0",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"field2",component:"JAreaLinkage",label:"禁用组件(方式二)",helpMessage:["component模式"],colProps:{span:12},dynamicDisabled:({values:l})=>K(["demo.dbarray"]),defaultValue:["140000","140300","140302"]},{field:"field2",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"pca1",component:"JAreaSelect",label:"省市区级联",helpMessage:["component模式"],defaultValue:"140302",colProps:{span:12}},{field:"pca1",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"pop1",component:"Input",label:"JPopup示例",helpMessage:["插槽模式"],slot:"JPopup",colProps:{span:12}},{field:"pop1",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"JInputPop",component:"JInputPop",label:"JInputPop",helpMessage:["component模式"],colProps:{span:12}},{field:"JInputPop",component:"JEllipsis",label:"输入值",colProps:{span:12}},{field:"JTreeDictAsync",component:"JTreeDict",label:"异步JTreeDict",helpMessage:["component模式"],colProps:{span:12},componentProps:{async:!0}},{field:"JTreeDictAsync",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"JSwitch",component:"JSwitch",label:"JSwitch",helpMessage:["component模式"],colProps:{span:12}},{field:"JSwitch",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"JSwitchSelect",component:"JSwitch",label:"JSwitchSelect",helpMessage:["component模式"],colProps:{span:12},componentProps:{query:!0}},{field:"JSwitchSelect",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"userSelect2",component:"UserSelect",label:"高级用户选择",helpMessage:["component模式"],colProps:{span:12}},{field:"userSelect2",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"superQuery",component:"Input",label:"高级查询",helpMessage:["插槽模式"],slot:"superQuery",colProps:{span:12}},{field:"superQuery",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"superQuery1",component:"Input",label:"高级查询",helpMessage:["插槽模式-自己保存查询条件"],slot:"superQuery1",colProps:{span:12}},{field:"superQuery1",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"pop2",component:"JPopupDict",label:"JPopupDict示例",colProps:{span:12},componentProps:{placeholder:"请选择",dictCode:"report_user,username,id",multi:!0}},{field:"pop2",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"sex",component:"JDictSelectTag",label:"性别(控制下方课程options)",helpMessage:["component模式","性别不同，下方课程展示选项不同"],componentProps:{dictCode:"sex",type:"radioButton",onChange:l=>{}},colProps:{span:12}},{field:"sex",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"course",component:"Select",label:"课程",dynamicPropskey:"options",dynamicPropsVal:({model:l})=>{let u;return l.sex==1?[{value:"0",label:"java - 男"},{value:"1",label:"vue - 男"}]:l.sex==2?[{value:"2",label:"瑜伽 - 女"},{value:"3",label:"美甲 - 女"}]:[]},componentProps:{disabled:!1},colProps:{span:12}},{field:"course",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"field100",component:"JInputSelect",label:"JInputSelect",helpMessage:["component模式"],componentProps:{selectPlaceholder:"可选择系统变量",inputPlaceholder:"请输入",selectWidth:"200px",options:[{label:"登录用户账号",value:"#{sys_user_code}"},{label:"登录用户名称",value:"#{sys_user_name}"},{label:"当前日期",value:"#{sys_date}"},{label:"当前时间",value:"#{sys_time}"},{label:"登录用户部门",value:"#{sys_org_code}"},{label:"用户拥有部门",value:"#{sys_multi_org_code}"},{label:"登录用户租户",value:"#{tenant_id}"}]},colProps:{span:12}},{field:"field100",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"JAreaLinkage",component:"JAreaLinkage",label:"省市区选择",colProps:{span:12}},{field:"JAreaLinkage",component:"JEllipsis",label:"选中值",colProps:{span:12}},{field:"orderAuth",component:"Input",label:"指令权限",helpMessage:['有权限右侧的"选中值"可见，否则不可见'],colProps:{span:12}},{field:"orderAuth",auth:"demo:order:auth",component:"JEllipsis",label:"选中值",colProps:{span:12}}],q=S({components:{BasicForm:F,ApiSelect:U,JAreaLinkage:R,JPopup:L,JAreaSelect:Q,JCheckbox:V,JInput:T,JEllipsis:j,BasicDragVerify:B},name:"JeecgComponents",setup(){const{isDisabledAuth:l}=v(),u=c(null),h=c(null),{createMessage:g}=w(),i=c(""),y=c({text:"确定"}),m=E(()=>({keyword:_(i)}));function f(p){i.value=p}const b={name:{title:"名称",view:"text",type:"string",order:1},birthday:{title:"生日",view:"date",type:"string",order:2},age:{title:"年龄",view:"number",type:"number",order:4},sex:{title:"性别",view:"list",type:"string",dictCode:"sex",order:5},bpmStatus:{title:"流程状态",view:"list_multi",type:"string",dictCode:"bpm_status",order:6}};function P(p,r,e){if(p){let o=decodeURI(p.superQueryParams);r[e]=o}}const d=c([{content:'[{"field":"age","rule":"eq","val":14}]',title:"豆蔻年华",type:"and"},{content:'[{"field":"name","rule":"eq","val":"张三"}]',title:"项目经理",type:"and"}]),J=p=>new Promise((r,e)=>{setTimeout(()=>{Math.random()>.5?(d.value=p,r()):e()},1e3)});return{schemas:Y,formElRef:h,isDisabledAuth:l,optionsListApi:I,submitButtonOptions:y,onSearch:D(f,300),searchParams:m,superQueryConfig:b,handleSuperQuery:P,handleReset:()=>{i.value=""},handleSubmit:p=>{g.success("click search,values:"+JSON.stringify(p))},check:u,handleSuperQuerySave:J,saveSearchData:d}}});function $(l,u,h,g,i,y){const m=t("JAreaLinkage"),f=t("JPopup"),b=t("JAreaSelect"),P=t("JCheckbox"),d=t("JInput"),J=t("BasicDragVerify"),p=t("super-query"),r=t("BasicForm");return M(),C(r,{ref:"formElRef",class:k("jee-select-demo-form"),labelCol:{span:6},wrapperCol:{span:14},showResetButton:!1,showSubmitButton:!1,schemas:l.schemas,actionColOptions:{span:24},onSubmit:l.handleSubmit,onReset:l.handleReset,style:{height:"100%"}},{jAreaLinkage:n(({model:e,field:o})=>[a(m,{value:e[o],"onUpdate:value":s=>e[o]=s,showArea:!0,showAll:!1},null,8,["value","onUpdate:value"])]),jAreaLinkage1:n(({model:e,field:o})=>[a(m,{disabled:l.isDisabledAuth(["demo.dbarray"]),value:e[o],"onUpdate:value":s=>e[o]=s,showArea:!0,showAll:!1},null,8,["disabled","value","onUpdate:value"])]),JPopup:n(({model:e,field:o})=>[a(f,{value:e[o],"onUpdate:value":s=>e[o]=s,formElRef:l.formElRef,code:"report_user",fieldConfig:[{source:"username",target:"pop1"}]},null,8,["value","onUpdate:value","formElRef"])]),JAreaSelect:n(({model:e,field:o})=>[a(b,{value:e[o],"onUpdate:value":s=>e[o]=s},null,8,["value","onUpdate:value"])]),JCheckbox:n(({model:e,field:o})=>[a(P,{value:e[o],"onUpdate:value":s=>e[o]=s,dictCode:"remindMode"},null,8,["value","onUpdate:value"])]),JInput:n(({model:e,field:o})=>[a(d,{value:e[o],"onUpdate:value":s=>e[o]=s,type:e.jinputtype},null,8,["value","onUpdate:value","type"])]),dargVerify:n(({model:e,field:o})=>[a(J,{value:e[o],"onUpdate:value":s=>e[o]=s},null,8,["value","onUpdate:value"])]),superQuery:n(({model:e,field:o})=>[a(p,{config:l.superQueryConfig,onSearch:s=>l.handleSuperQuery(s,e,o)},null,8,["config","onSearch"])]),superQuery1:n(({model:e,field:o})=>[a(p,{config:l.superQueryConfig,onSearch:s=>l.handleSuperQuery(s,e,o),isCustomSave:!0,saveSearchData:l.saveSearchData,save:l.handleSuperQuerySave},null,8,["config","onSearch","saveSearchData","save"])]),_:1},8,["schemas","onSubmit","onReset"])}const He=A(q,[["render",$],["__scopeId","data-v-422a7068"]]);export{He as default};
