import{d as _,f as u,e as h,u as m,ag as r,aB as w,ar as L,aD as i,k as p}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{C as M}from"./index-LCGLvkB3.js";import{x as D,u as O,a as U}from"./index-CCWaWN5g.js";import{P as V}from"./index-CtJ0w2CP.js";import{o as f}from"./select-nVA4yav1.js";import{t as j}from"./tree-C9AW4Mg9.js";import{w as q,h as A}from"./antd-vue-vendor-me9YkNVC.js";import G from"./JAreaLinkage-DFCdF3cr.js";import{A as J}from"./componentMap-Bkie1n3v.js";import{B as $}from"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JSelectUser-COkExGbu.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const C=u([]),k=u([]),b=u([]);for(let e=1;e<10;e++)b.value.push({label:"选项"+e,value:`${e}`});const I=h(()=>A(m(b)).map(e=>(e.disabled=m(k).indexOf(e.value)!==-1,e))),W=h(()=>A(m(b)).map(e=>(e.disabled=m(C).indexOf(e.value)!==-1,e))),v=[{id:"guangdong",label:"广东省",value:"1",key:"1"},{id:"jiangsu",label:"江苏省",value:"2",key:"2"}],S={guangdong:[{label:"珠海市",value:"1",key:"1"},{label:"深圳市",value:"2",key:"2"},{label:"广州市",value:"3",key:"3"}],jiangsu:[{label:"南京市",value:"1",key:"1"},{label:"无锡市",value:"2",key:"2"},{label:"苏州市",value:"3",key:"3"}]},x=[{field:"divider-basic",component:"Divider",label:"基础字段"},{field:"field1",component:"Input",label:"字段1",colProps:{span:8},componentProps:({schema:e,formModel:s})=>({placeholder:"自定义placeholder",onChange:n=>{}}),renderComponentContent:()=>({prefix:()=>"pSlot",suffix:()=>"sSlot"})},{field:"field2",component:"Input",label:"字段2",defaultValue:"111",colProps:{span:8},componentProps:{onChange:e=>{}},suffix:"天"},{field:"field3",component:"DatePicker",label:"字段3",colProps:{span:8}},{field:"field4",component:"Select",label:"字段4",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1",key:"1"},{label:"选项2",value:"2",key:"2"}]}},{field:"field5",component:"CheckboxGroup",label:"字段5",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]}},{field:"field7",component:"RadioGroup",label:"字段7",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]}},{field:"field8",component:"Checkbox",label:"字段8",colProps:{span:8},renderComponentContent:"Check"},{field:"field9",component:"Switch",label:"字段9",colProps:{span:8}},{field:"field10",component:"RadioButtonGroup",label:"字段10",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]}},{field:"field11",component:"Cascader",label:"字段11",colProps:{span:8},componentProps:{options:[{value:"zhejiang",label:"Zhejiang",children:[{value:"hangzhou",label:"Hangzhou",children:[{value:"xihu",label:"West Lake"}]}]},{value:"jiangsu",label:"Jiangsu",children:[{value:"nanjing",label:"Nanjing",children:[{value:"zhonghuamen",label:"Zhong Hua Men"}]}]}]}},{field:"divider-api-select",component:"Divider",label:"远程下拉演示"},{field:"field30",component:"ApiSelect",label:"懒加载远程下拉",required:!0,componentProps:{api:f,params:{id:1},resultField:"list",labelField:"name",valueField:"id",immediate:!1,onChange:e=>{},onOptionsChange:e=>{}},colProps:{span:8},defaultValue:"0"},{field:"field311",component:"JAreaLinkage",label:"省市区选择",helpMessage:["JAreaLinkage组件","省市区选择"],required:!0,slot:"jAreaLinkage",colProps:{span:8},defaultValue:["130000","130200"]},{field:"field31",component:"Input",label:"下拉本地搜索",helpMessage:["ApiSelect组件","远程数据源本地搜索","只发起一次请求获取所有选项"],required:!0,slot:"localSearch",colProps:{span:8},defaultValue:"0"},{field:"field32",component:"Input",label:"下拉远程搜索",helpMessage:["ApiSelect组件","将关键词发送到接口进行远程搜索"],required:!0,slot:"remoteSearch",colProps:{span:8},defaultValue:"0"},{field:"field33",component:"ApiTreeSelect",label:"远程下拉树",helpMessage:["ApiTreeSelect组件","使用接口提供的数据生成选项"],required:!0,componentProps:{api:j,resultField:"list"},colProps:{span:8}},{field:"field34",component:"ApiRadioGroup",label:"远程Radio",helpMessage:["ApiRadioGroup组件","使用接口提供的数据生成选项"],required:!0,componentProps:{api:f,params:{count:2},resultField:"list",labelField:"name",valueField:"id"},defaultValue:"1",colProps:{span:8}},{field:"field35",component:"ApiRadioGroup",label:"远程Radio",helpMessage:["ApiRadioGroup组件","使用接口提供的数据生成选项"],required:!0,componentProps:{api:f,params:{count:2},resultField:"list",labelField:"name",valueField:"id",isBtn:!0},colProps:{span:8}},{field:"divider-linked",component:"Divider",label:"字段联动"},{field:"province",component:"Select",label:"省份",colProps:{span:8},componentProps:({formModel:e,formActionType:s})=>({options:v,placeholder:"省份与城市联动",onChange:n=>{let c=n==1?S[v[0].id]:S[v[1].id];n===void 0&&(c=[]),e.city=void 0;const{updateSchema:d}=s;d({field:"city",componentProps:{options:c}})}})},{field:"city",component:"Select",label:"城市",colProps:{span:8},componentProps:{options:[],placeholder:"省份与城市联动"}},{field:"divider-selects",component:"Divider",label:"互斥多选",helpMessage:["两个Select共用数据源","但不可选择对方已选中的项目"]},{field:"selectA",component:"Select",label:"互斥SelectA",slot:"selectA",defaultValue:[],colProps:{span:8}},{field:"selectB",component:"Select",label:"互斥SelectB",slot:"selectB",defaultValue:[],colProps:{span:8}},{field:"divider-others",component:"Divider",label:"其它"},{field:"field20",component:"InputNumber",label:"字段20",required:!0,colProps:{span:8}},{field:"field21",component:"Slider",label:"字段21",componentProps:{min:0,max:100,range:!0,marks:{20:"20°C",60:"60°C"}},colProps:{span:8}},{field:"field22",component:"Rate",label:"字段22",defaultValue:3,colProps:{span:8},componentProps:{disabled:!1,allowHalf:!0}}],z=_({components:{BasicForm:$,CollapseContainer:M,PageWrapper:V,ApiSelect:J,JAreaLinkage:G,ASelect:q},setup(){const e=u(null),{createMessage:s}=O(),n=u(""),c=h(()=>({keyword:m(n)}));function d(t){n.value=t}function F(t){alert(t)}return{schemas:x,optionsListApi:f,optionsA:I,optionsB:W,valueSelectA:C,valueSelectB:k,onSearch:D(d,300),searchParams:c,handleReset:()=>{n.value=""},handleSubmit:t=>{s.success("click search,values:"+JSON.stringify(t))},check:e}}});function H(e,s,n,c,d,F){const t=r("JAreaLinkage"),g=r("ApiSelect"),P=r("a-select"),B=r("BasicForm"),y=r("CollapseContainer"),R=r("PageWrapper");return L(),w(R,{title:"表单基础示例",contentFullHeight:""},{default:i(()=>[p(y,{title:"基础示例"},{default:i(()=>[p(B,{autoFocusFirstItem:"",labelWidth:200,schemas:e.schemas,actionColOptions:{span:24},labelCol:{span:8},onSubmit:e.handleSubmit,onReset:e.handleReset},{jAreaLinkage:i(({model:o,field:l})=>[p(t,{value:o[l],"onUpdate:value":a=>o[l]=a,showArea:!0,showAll:!1},null,8,["value","onUpdate:value"])]),localSearch:i(({model:o,field:l})=>[p(g,{api:e.optionsListApi,showSearch:"",value:o[l],"onUpdate:value":a=>o[l]=a,optionFilterProp:"label",resultField:"list",labelField:"name",valueField:"id"},null,8,["api","value","onUpdate:value"])]),selectA:i(({model:o,field:l})=>[p(P,{options:e.optionsA,mode:"multiple",value:o[l],"onUpdate:value":a=>o[l]=a,onChange:a=>e.valueSelectA=o[l],allowClear:""},null,8,["options","value","onUpdate:value","onChange"])]),selectB:i(({model:o,field:l})=>[p(P,{options:e.optionsB,mode:"multiple",value:o[l],"onUpdate:value":a=>o[l]=a,onChange:a=>e.valueSelectB=o[l],allowClear:""},null,8,["options","value","onUpdate:value","onChange"])]),remoteSearch:i(({model:o,field:l})=>[p(g,{api:e.optionsListApi,showSearch:"",value:o[l],"onUpdate:value":a=>o[l]=a,filterOption:!1,resultField:"list",labelField:"name",valueField:"id",onSearch:e.onSearch,params:e.searchParams},null,8,["api","value","onUpdate:value","onSearch","params"])]),_:1},8,["schemas","onSubmit","onReset"])]),_:1})]),_:1})}const Ye=U(z,[["render",H]]);export{Ye as default};
