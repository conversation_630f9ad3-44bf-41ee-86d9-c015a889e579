var w=Object.defineProperty;var h=Object.getOwnPropertySymbols;var P=Object.prototype.hasOwnProperty,j=Object.prototype.propertyIsEnumerable;var b=(e,t,o)=>t in e?w(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,D=(e,t)=>{for(var o in t||(t={}))P.call(t,o)&&b(e,o,t[o]);if(h)for(var o of h(t))j.call(t,o)&&b(e,o,t[o]);return e};var g=(e,t,o)=>new Promise((s,d)=>{var m=a=>{try{r(o.next(a))}catch(i){d(i)}},p=a=>{try{r(o.throw(a))}catch(i){d(i)}},r=a=>a.done?s(a.value):Promise.resolve(a.value).then(m,p);r((o=o.apply(e,t)).next())});import{d as B,f as T,e as M,u as n,aB as v,ar as k,aD as z,k as _,aE as F}from"./vue-vendor-dy9k-Yad.js";import{B as Y}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{render as S}from"./renderUtils-D7XVOFwj.js";import{j as c,ac as U}from"./index-CCWaWN5g.js";import{M as O}from"./antd-vue-vendor-me9YkNVC.js";import{u as R}from"./useForm-CgkFTrrO.js";import{B as C}from"./BasicForm-DBcXiHk0.js";const Z=[{title:"姓名",dataIndex:"name",width:170,align:"left",resizable:!0,sorter:{multiple:1}},{title:"关键词",dataIndex:"keyWord",width:130,resizable:!0},{title:"打卡时间",dataIndex:"punchTime",width:140,resizable:!0},{title:"工资",dataIndex:"salaryMoney",width:140,resizable:!0,sorter:{multiple:2}},{title:"奖金",dataIndex:"bonusMoney",width:140,resizable:!0},{title:"性别",dataIndex:"sex",sorter:{multiple:3},customRender:({record:e})=>S.renderDict(e.sex,"sex"),width:120,resizable:!0},{title:"生日",dataIndex:"birthday",width:120,resizable:!0},{title:"邮箱",dataIndex:"email",width:120,resizable:!0},{title:"个人简介",dataIndex:"content",width:120,resizable:!0}],A=[{field:"name",label:"姓名",component:"Input",componentProps:{trim:!0},colProps:{span:8}},{field:"birthday",label:"生日",component:"RangePicker",componentProps:{valueType:"Date"},colProps:{span:8}},{field:"age",label:"年龄",component:"Input",slot:"age",colProps:{span:8}},{field:"sex",label:"性别",colProps:{span:8},component:"JDictSelectTag",componentProps:{dictCode:"sex",placeholder:"请选择性别"}}],E=[{field:"id",label:"id",component:"Input",show:!1},{field:"createBy",label:"createBy",component:"Input",show:!1},{field:"createTime",label:"createTime",component:"Input",show:!1},{field:"name",label:"名字",component:"Input",required:!0,componentProps:{placeholder:"请输入名字"}},{field:"keyWord",label:"关键词",component:"Input",componentProps:{placeholder:"请输入关键词"}},{field:"punchTime",label:"打卡时间",component:"DatePicker",componentProps:{showTime:!0,valueFormat:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择打卡时间"}},{field:"salaryMoney",label:"工资",component:"Input",componentProps:{placeholder:"请输入工资"}},{field:"sex",label:"性别",component:"JDictSelectTag",defaultValue:"1",componentProps:{type:"radio",dictCode:"sex",placeholder:"请选择性别"}},{field:"age",label:"年龄",component:"InputNumber",defaultValue:1,componentProps:{placeholder:"请输入年龄"}},{field:"birthday",label:"生日",component:"DatePicker",defaultValue:"",componentProps:{valueFormat:"YYYY-MM-DD",placeholder:"请选择生日"}},{field:"email",label:"邮箱",component:"Input",rules:[{required:!1,type:"email",message:"邮箱格式不正确",trigger:"blur"}],componentProps:{placeholder:"请输入邮箱"}},{field:"content",label:"个人简介 - To introduce myself",component:"InputTextArea",labelLength:4,componentProps:{placeholder:"请输入个人简介"}},{field:"updateCount",label:"乐观锁",show:!1,component:"Input"}];const ee="/test/jeecgDemo/exportXls",te="/test/jeecgDemo/importExcel",oe=e=>c.get({url:"/test/jeecgDemo/list",params:e}),L=(e,t)=>{let o=t?"/test/jeecgDemo/edit":"/test/jeecgDemo/add";return c.post({url:o,params:e})},V=e=>c.get({url:"/test/jeecgDemo/queryById",params:e}),le=(e,t)=>c.delete({url:"/test/jeecgDemo/delete",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),ae=(e,t)=>{O.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>c.delete({url:"/test/jeecgDemo/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},q=B({__name:"DemoModal",props:{isDisabled:{type:Boolean,default:!1}},emits:["register","success"],setup(e,{emit:t}){const o=t,s=T(!0),d=e,[m,{resetFields:p,setFieldsValue:r,validate:a}]=R({schemas:E,showActionButtonGroup:!1}),[i,{setModalProps:u,closeModal:y}]=U(l=>g(null,null,function*(){yield p(),u({confirmLoading:!1,showOkBtn:!d.isDisabled}),s.value=!!(l!=null&&l.isUpdate),l.createBy&&(yield r({createBy:l.createBy})),l.createTime&&(yield r({createTime:l.createTime})),n(s)&&(l.record=yield V({id:l.record.id}),yield r(D({},l.record)))})),x=M(()=>n(s)?"编辑":"新增");function I(l){return g(this,null,function*(){try{let f=yield a();u({confirmLoading:!0}),yield L(f,s.value),y(),o("success",f)}finally{u({confirmLoading:!1})}})}return(l,f)=>(k(),v(n(Y),F(l.$attrs,{onRegister:n(i),title:x.value,onOk:I,width:"40%"}),{default:z(()=>[_(n(C),{onRegister:n(m),disabled:e.isDisabled},null,8,["onRegister","disabled"])]),_:1},16,["onRegister","title"]))}}),re=Object.freeze(Object.defineProperty({__proto__:null,default:q},Symbol.toStringTag,{value:"Module"}));export{re as D,q as _,ee as a,ae as b,Z as c,le as d,oe as e,te as g,A as s};
