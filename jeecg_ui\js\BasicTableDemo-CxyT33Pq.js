var _=Object.defineProperty,b=Object.defineProperties;var g=Object.getOwnPropertyDescriptors;var m=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable;var p=(r,t,o)=>t in r?_(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o,a=(r,t)=>{for(var o in t||(t={}))k.call(t,o)&&p(r,o,t[o]);if(m)for(var o of m(t))h.call(t,o)&&p(r,o,t[o]);return r},s=(r,t)=>b(r,g(t));import{d,aq as x,ar as y,k as n,aD as C,u as e}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as I}from"./useListPage-Soxgnx9a.js";import{Q as S}from"./componentMap-Bkie1n3v.js";import w from"./BasicTable-xCEZpGLb.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const z={class:"p-4"},A=d({name:"basic-table-demo"}),Dt=d(s(a({},A),{setup(r){const t=[{title:"姓名",dataIndex:"name",key:"name",resizable:!1},{title:"年龄",dataIndex:"age",key:"age"},{title:"住址",dataIndex:"address",key:"address"}],{tableContext:o}=I({designScope:"basic-table-demo",tableProps:{title:"用户列表",dataSource:[{key:"1",name:"胡歌",age:32,address:"朝阳区林萃路1号"},{key:"2",name:"刘诗诗",age:32,address:"昌平区白沙路1号"}],columns:t,size:"large",striped:!1,showActionColumn:!0,useSearchForm:!1}}),[l,B]=o;function c(i){return[{label:"编辑",onClick:u.bind(null,i)}]}function u(i){}return(i,E)=>(y(),x("div",z,[n(e(w),{onRegister:e(l)},{action:C(({record:f})=>[n(e(S),{actions:c(f)},null,8,["actions"])]),_:1},8,["onRegister"])]))}}));export{Dt as default};
