var D=(z,y,i)=>new Promise((p,f)=>{var m=n=>{try{u(i.next(n))}catch(d){f(d)}},o=n=>{try{u(i.throw(n))}catch(d){f(d)}},u=n=>n.done?p(n.value):Promise.resolve(n.value).then(m,o);u((i=i.apply(z,y)).next())});import{d as O,f as g,e as $,o as q,ag as _,aq as C,ar as U,at as e,k as r,u as b,au as l,aD as T,G as k,as as G,F as P}from"./vue-vendor-dy9k-Yad.js";import{C as J}from"./index-BPAdgtaT.js";import{b as K,ah as L,F as Q,ad as W,d as X,u as Z}from"./index-CCWaWN5g.js";import{h as tt}from"./header-OZa5fSDc.js";import{a as et}from"./upload-Dd1Qafin.js";import{i as ot,h as st}from"./antd-vue-vendor-me9YkNVC.js";import{b as at,g as nt}from"./UserSetting.api-BJ086Ekj.js";import{_ as it}from"./UserAccountModal-EpftHlah.js";import rt from"./UnsubscribeModal-Ck67K_CM.js";import"./index-Diw57m_E.js";import"./base64Conver-24EVOS6V.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";import"./user-Cjsg8yWg.js";import"./CustomModal-BakuIxQv.js";const lt={class:"user-setting-top"},ut={class:"account-avatar"},pt={class:"account-right"},mt={key:0},dt={class:"font-size-17 account-name"},ct={key:1},ft={class:"use-day"},vt={class:"account-data"},gt={class:"account-detail"},_t={class:"margin-bottom-10 font-size-13"},bt={class:"gray-3"},yt={class:"margin-bottom-10 font-size-13"},xt={class:"gray-3"},ht={class:"margin-bottom-10 nowarp font-size-13"},Dt={class:"gray-3"},Ct={class:"account-info"},Ut={class:"margin-bottom-10 font-size-13"},zt={class:"gray-3"},It={class:"margin-bottom-10 font-size-13"},Mt={class:"gray-3"},Tt={class:"danger-zone"},kt={class:"danger-item"},je=O({__name:"BaseSetting",setup(z){const y=K("sex")||[{text:"男",value:"1"},{text:"女",value:"2"}],{createMessage:i}=Z(),p=L(),{prefixCls:f}=Q("j-base-setting-container"),m=g(!1),o=g({}),u=g(),[n,{openModal:d}]=W(),v=g(!1),w=$(()=>X(o.value.avatar)||tt);function B(s,t){const a=p.getUserInfo;a.avatar=t,p.setUserInfo(a),t&&I({avatar:t,id:a.id})}function I(s){at(s).then(t=>{t.success||i.warn(t.message)})}function S(){m.value=!0,setTimeout(()=>{u.value.focus()},100)}function E(){o.value.realname?(I({realname:o.value.realname,id:o.value.id}),p.setUserInfo(o.value)):i.warn("请输入姓名"),m.value=!1}function N(s){return s?ot(s).format("YYYY-MM-DD"):"未填写"}function A(s){let t=y.find(c=>parseInt(c.value)===s),a="未填写";return t&&(a=t.text),a}function F(){let s=st(o.value);d(!0,{record:s})}function M(){nt().then(s=>D(null,null,function*(){s.success&&(s.result?(s.result.sexText=A(s.result.sex),s.result.birthday=N(s.result.birthday),s.result.createTimeText=V(s.result.createTime),o.value=s.result):o.value={})}))}function V(s){let t,a,c=Date.parse(s),x=new Date().getTime();return a=Math.abs(x-c),t=Math.floor(a/(1e3*3600*24)),t+" 天"}function Y(){v.value=!0}function R(){v.value=!1}function j(){i.success("账号注销成功，即将跳转到登录页")}return q(()=>D(null,null,function*(){M()})),(s,t)=>{const a=_("Icon"),c=_("a-tooltip"),x=_("a-input"),H=_("a-button");return U(),C(P,null,[e("div",{class:G(["account-padding",[`${b(f)}`]])},[e("div",lt,[e("div",ut,[r(b(J),{uploadApi:b(et),showBtn:!1,value:w.value,btnProps:{preIcon:"ant-design:cloud-upload-outlined"},onChange:B,width:"80"},null,8,["uploadApi","value"]),e("div",pt,[m.value?(U(),C("div",ct,[r(x,{ref_key:"accountNameEdit",ref:u,maxlength:100,value:o.value.realname,"onUpdate:value":t[0]||(t[0]=h=>o.value.realname=h),onBlur:E},null,8,["value"])])):(U(),C("div",mt,[e("span",dt,l(o.value.realname),1),r(c,{content:"编辑姓名"},{default:T(()=>[r(a,{class:"pointer font-size-17 gray-bd account-icon",icon:"ant-design:edit-outlined",onClick:S})]),_:1})])),e("div",ft,[t[2]||(t[2]=k(" 使用：")),e("span",null,l(o.value.createTimeText),1)])])])]),e("div",vt,[e("div",gt,[t[7]||(t[7]=e("div",{class:"font-size-15 font-bold font-color-gray",style:{"margin-bottom":"16px"}},"详细资料",-1)),e("div",_t,[t[3]||(t[3]=e("span",{class:"gray-75 item-label"},"生日",-1)),e("span",bt,l(o.value.birthday),1)]),e("div",yt,[t[4]||(t[4]=e("span",{class:"gray-75 item-label"},"性别",-1)),e("span",xt,l(o.value.sexText),1)]),e("div",ht,[t[5]||(t[5]=e("span",{class:"gray-75 item-label"},"职位",-1)),e("span",Dt,l(o.value.postText?o.value.postText:"未填写"),1)]),e("div",{class:"font-size-13"},[t[6]||(t[6]=e("span",{class:"item-label"},null,-1)),e("span",{class:"item-label pointer",style:{color:"#1e88e5"},onClick:F},"编辑")])]),e("div",Ct,[t[10]||(t[10]=e("div",{class:"font-size-15 font-bold font-color-gray",style:{"margin-bottom":"16px"}},"联系信息",-1)),e("div",Ut,[t[8]||(t[8]=e("span",{class:"gray-75 item-label"},"邮箱",-1)),e("span",zt,l(o.value.email?o.value.email:"未填写"),1)]),e("div",It,[t[9]||(t[9]=e("span",{class:"gray-75 item-label"},"手机",-1)),e("span",Mt,l(o.value.phone?o.value.phone:"未填写"),1)])])]),e("div",Tt,[t[13]||(t[13]=e("div",{class:"font-size-15 font-bold font-color-gray"},"危险操作",-1)),e("div",kt,[t[12]||(t[12]=e("div",{class:"danger-info"},[e("div",{class:"danger-title"},"注销账号"),e("div",{class:"danger-desc"},"注销后账号数据将被永久删除，无法恢复")],-1)),r(H,{danger:"",onClick:Y,class:"btn"},{default:T(()=>t[11]||(t[11]=[k("注销账号")])),_:1,__:[11]})])])],2),r(it,{onRegister:b(n),onSuccess:M},null,8,["onRegister"]),r(rt,{open:v.value,"onUpdate:open":t[1]||(t[1]=h=>v.value=h),onClose:R,onSuccess:j},null,8,["open"])],64)}}});export{je as default};
