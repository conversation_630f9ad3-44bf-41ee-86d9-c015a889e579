import{d as i,aq as o,ar as e,at as s,k as a}from"./vue-vendor-dy9k-Yad.js";import d from"./CountTo-Bf9dGyG1.js";import{a as n}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const r={class:"main-kpi-cards"},l={class:"cards-content"},c={class:"kpi-card"},p={class:"card-value"},m={class:"kpi-group"},v={class:"kpi-card"},u={class:"card-value"},_={class:"kpi-card",style:{"margin-left":"10px"}},f={class:"card-value"},k=i({__name:"MainKPICards",setup(x){return(b,t)=>(e(),o("div",r,[t[6]||(t[6]=s("div",{class:"cards-bg"},null,-1)),s("div",l,[s("div",c,[t[1]||(t[1]=s("div",{class:"card-label"},"成交总额",-1)),s("div",p,[a(d,{start:0,end:59645956,duration:2e3,decimals:0}),t[0]||(t[0]=s("span",{class:"card-unit"},"万",-1))])]),s("div",m,[s("div",v,[t[3]||(t[3]=s("div",{class:"card-label",style:{"font-size":"16px"}},"溢价总额",-1)),s("div",u,[a(d,{start:0,end:5956,duration:2e3,decimals:0,style:{"font-size":"20px"}}),t[2]||(t[2]=s("span",{class:"card-unit"},"万",-1))])]),s("div",_,[t[5]||(t[5]=s("div",{class:"card-label",style:{"font-size":"16px"}},"总溢价率",-1)),s("div",f,[a(d,{start:0,end:95,duration:2e3,decimals:2,style:{"font-size":"20px"}}),t[4]||(t[4]=s("span",{class:"card-unit"},"%",-1))])])])])]))}}),I=n(k,[["__scopeId","data-v-cb367709"]]);export{I as default};
