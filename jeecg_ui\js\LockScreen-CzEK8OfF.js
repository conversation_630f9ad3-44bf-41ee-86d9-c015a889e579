const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/LockModal-O29-zQyI.js","js/vue-vendor-dy9k-Yad.js","js/index-CCWaWN5g.js","js/antd-vue-vendor-me9YkNVC.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/index-Diw57m_E.js","js/BasicModal-BLFvpBuk.js","js/ModalHeader-BJG9dHtK.js","js/useTimeout-CeTdFD_D.js","js/index-CImCetrx.js","assets/index-BObJM2Lc.css","assets/ModalHeader-HwQKX-UU.css","js/useWindowSizeFn-DDbrQbks.js","js/index-LCGLvkB3.js","js/index-De_W6s5g.js","js/index-D6l0IxOU.js","js/useIntersectionObserver-C4LVxQJW.js","assets/index-zj-Vfn3Q.css","assets/BasicModal-ByeTDAzn.css","js/CustomModal-BakuIxQv.js","assets/CustomModal-DWxHZmza.css","assets/index-yRxe3SQ1.css","js/BasicForm-DBcXiHk0.js","js/componentMap-Bkie1n3v.js","js/useFormItem-CHvpjy4o.js","js/download-CZ-9H9a3.js","js/base64Conver-24EVOS6V.js","js/index-CBCjSSNZ.js","assets/index-NmxXH94f.css","js/index-DFrpKMGa.js","js/useCountdown-CCWNeb_r.js","js/useFormItemSingle-Cw668yj5.js","assets/index-BB9COjV3.css","js/JSelectUser-COkExGbu.js","js/props-CCT78mKr.js","js/JSelectBiz-jOYRdMJf.js","assets/JSelectBiz-CYw1rOZ6.css","assets/JSelectUser-CQvjZTEr.css","js/JAddInput-CxJ-JBK-.js","js/index-QxsVJqiT.js","js/index-BtIdS_Qz.js","js/bem-sRx7x0Ii.js","js/props-qAqCef5R.js","js/useContextMenu-BU2ycxls.js","assets/useContextMenu-DRJLeHo9.css","assets/index-D8VMPii6.css","js/depart.api-BoGnt_ZX.js","assets/JAddInput-i6a6KIoQ.css","js/JSelectDept-I-NqkbOH.js","assets/JSelectDept-WHP406xL.css","js/JAreaSelect-Db7Nhhc_.js","js/areaDataUtil-BXVjRArW.js","assets/JAreaSelect-Pwl_5U28.css","js/JEditorTiptap-BwAoWsi9.js","js/index-ByPySmGo.js","assets/index-BrdQT4ew.css","js/JPopup-CeU6ry6r.js","assets/JPopup-Dn0_YeSX.css","js/JEllipsis-BsXuWNHJ.js","js/JUpload-CRos0F1P.js","assets/JUpload-CsrjJkIs.css","js/JSearchSelect-c_lfTydU.js","js/index-CXHeQyuE.js","js/index-Dyko68ZT.js","assets/index-CTbO_Zqi.css","assets/componentMap-Degzw4_e.css","assets/BasicForm-DTEnYz8c.css","js/useForm-CgkFTrrO.js","js/lock-CRalNsZJ.js","js/header-OZa5fSDc.js","assets/LockModal-CxRaDJpF.css"])))=>i.map(i=>d[i]);
var p=(e,s,t)=>new Promise((n,a)=>{var c=o=>{try{r(t.next(o))}catch(l){a(l)}},i=o=>{try{r(t.throw(o))}catch(l){a(l)}},r=o=>o.done?n(o.value):Promise.resolve(o.value).then(c,i);r((t=t.apply(e,s)).next())});import{aj as f,c as _,ad as L,N as g,_ as h,cP as C,a as M}from"./index-CCWaWN5g.js";import{d as R,f as d,ag as m,aq as y,ar as u,F as E,k,aB as T,ah as V,aD as b}from"./vue-vendor-dy9k-Yad.js";import{bE as v,T as B}from"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const O=R({name:"LockScreen",inheritAttrs:!1,components:{Icon:_,Tooltip:B,LockOutlined:v,LockModal:f(()=>h(()=>import("./LockModal-O29-zQyI.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71])))},setup(){const{t:e}=g(),[s,{openModal:t}]=L(),n=d(!1),a=d(null);function c(){return p(this,null,function*(){n.value=!0,yield C(a),t(!0)})}return{t:e,register:s,handleLock:c,lockModalVisible:n,modalRef:a}}});function P(e,s,t,n,a,c){const i=m("LockOutlined"),r=m("Tooltip"),o=m("LockModal");return u(),y(E,null,[k(r,{title:e.t("layout.header.tooltipLock"),placement:"bottom",mouseEnterDelay:.5,onClick:e.handleLock},{default:b(()=>[k(i)]),_:1},8,["title","onClick"]),e.lockModalVisible?(u(),T(o,{key:0,ref:"modalRef",onRegister:e.register},null,8,["onRegister"])):V("",!0)],64)}const Q=M(O,[["render",P]]);export{Q as default};
