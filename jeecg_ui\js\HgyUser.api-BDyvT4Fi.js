var c=Object.defineProperty;var r=Object.getOwnPropertySymbols;var l=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;var s=(e,t,n)=>t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,o=(e,t)=>{for(var n in t||(t={}))l.call(t,n)&&s(e,n,t[n]);if(r)for(var n of r(t))u.call(t,n)&&s(e,n,t[n]);return e};import{rules as i}from"./validator-B_KkcUnu.js";import{u as m,j as a}from"./index-CCWaWN5g.js";const M=[{title:"登录账号",align:"center",dataIndex:"username"},{title:"真实姓名",align:"center",dataIndex:"realname"},{title:"电话",align:"center",dataIndex:"phone"}],y=[{label:"登录账号",field:"username",component:"Input"},{label:"真实姓名",field:"realname",component:"Input"},{label:"电话",field:"phone",component:"Input"}],d=[{label:"登录账号",field:"username",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入登录账号!"},o({},i.duplicateCheckRule("hgy_user","username",e,t)[0])]},{label:"真实姓名",field:"realname",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入真实姓名!"}]},{label:"电话",field:"phone",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入电话!"},{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号码!"}]},{label:"",field:"id",component:"Input",show:!1}],f={username:{title:"登录账号",order:0,view:"text",type:"string"},realname:{title:"真实姓名",order:1,view:"text",type:"string"},phone:{title:"电话",order:2,view:"text",type:"string"}};function x(e){return d}const{createConfirm:h}=m();const I="/auctionMan/auctionUserMan/hgyUser/exportXls",b="/auctionMan/auctionUserMan/hgyUser/importExcel",v=e=>a.get({url:"/auctionMan/auctionUserMan/hgyUser/list",params:e}),w=(e,t)=>a.delete({url:"/auctionMan/auctionUserMan/hgyUser/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),T=(e,t)=>{h({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>a.delete({url:"/auctionMan/auctionUserMan/hgyUser/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},B=(e,t)=>{let n=t?"/auctionMan/auctionUserMan/hgyUser/edit":"/auctionMan/auctionUserMan/hgyUser/add";return a.post({url:n,params:e})};export{I as a,T as b,y as c,M as d,w as e,B as f,b as g,d as h,x as i,v as l,f as s};
