import{d as p,e as d,ag as i,aq as c,ar as u,G as s,k as t,aD as a,au as l}from"./vue-vendor-dy9k-Yad.js";import{B as f,X as _,bC as C,a as M}from"./index-CCWaWN5g.js";import{V as g}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const P=p({name:"CurrentPermissionMode",components:{Divider:g},setup(){const o=f(),e=d(()=>o.getProjectConfig.permissionMode),{togglePermissionMode:n}=_();return{permissionMode:e,PermissionModeEnum:C,togglePermissionMode:n}}}),k={class:"mt-2"};function v(o,e,n,D,y,B){const r=i("a-button"),m=i("Divider");return u(),c("div",k,[e[1]||(e[1]=s(" 当前权限模式： ")),t(r,{type:"link"},{default:a(()=>[s(l(o.permissionMode===o.PermissionModeEnum.BACK?"后台权限模式":"前端角色权限模式"),1)]),_:1}),t(r,{class:"ml-4",onClick:o.togglePermissionMode,type:"primary"},{default:a(()=>e[0]||(e[0]=[s(" 切换权限模式 ")])),_:1,__:[0]},8,["onClick"]),t(m)])}const V=M(P,[["render",v]]);export{V as default};
