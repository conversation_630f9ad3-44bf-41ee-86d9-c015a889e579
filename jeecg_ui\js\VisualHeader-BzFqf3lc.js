var C=(g,f,d)=>new Promise((m,n)=>{var u=o=>{try{v(d.next(o))}catch(c){n(c)}},l=o=>{try{v(d.throw(o))}catch(c){n(c)}},v=o=>o.done?m(o.value):Promise.resolve(o.value).then(u,l);v((d=d.apply(g,f)).next())});import{d as D,f as p,e as w,o as N,b as H,ag as L,aq as V,ar as Y,at as r,k as y,aD as x,au as _}from"./vue-vendor-dy9k-Yad.js";import{i as U}from"./antd-vue-vendor-me9YkNVC.js";import{g as j}from"./SupplyDemand-DK00S9Ao.js";import{a as M}from"./areaDataUtil-BXVjRArW.js";import{a as O}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const q={class:"visual-header"},E={class:"header-content"},I={class:"header-left"},S={class:"nav-button material-select"},z={class:"button-text"},G={class:"nav-button address-select"},J={class:"button-text"},K={class:"header-right"},P={class:"time-info"},Q=D({__name:"VisualHeader",emits:["materialChange","areaChange"],setup(g,{emit:f}){const d=f,m=p(""),n=p([]),u=p([]),l=p([]),v=p(M),o=w(()=>{if(n.value&&n.value.length>0){const e=(t,a,s=0)=>{if(!a||a.length===0)return"";for(const i of t)if(i.id===a[s])return s===a.length-1?i.name:i.children&&s<a.length-1?e(i.children,a,s+1):i.name;return""};return e(u.value,n.value)}return""}),c=w(()=>l.value&&l.value.length>0?(t=>{const a=[];for(const s of t){const i=M.find(B=>B.value===s);i&&a.push(i.label)}return a.join(" / ")})(l.value):""),b=()=>{m.value=U().format("上午HH:mm YYYY年MM月DD日 dddd")},T=()=>C(null,null,function*(){try{const e=yield j();e&&Array.isArray(e)&&(u.value=e)}catch(e){}}),k=(e,t)=>t.some(a=>a.name.toLowerCase().includes(e.toLowerCase())),A=e=>{n.value=e,d("materialChange",e,o.value)},F=e=>{l.value=e,d("areaChange",e,c.value)};let h;return N(()=>{b(),h=setInterval(b,1e3),T()}),H(()=>{h&&clearInterval(h)}),(e,t)=>{const a=L("a-cascader");return Y(),V("div",q,[r("div",E,[r("div",I,[r("div",S,[y(a,{value:n.value,"onUpdate:value":t[0]||(t[0]=s=>n.value=s),options:u.value,placeholder:"请选择物资种类","show-search":"","field-names":{label:"name",value:"id",children:"children"},"change-on-select":"","filter-option":k,onChange:A,"dropdown-style":{background:"rgba(0, 76, 102, 0.8)",border:"1px solid #2BCCFF",backdropFilter:"blur(10px)",color:"#fff"},"dropdown-class-name":"visual-cascader-dropdown"},{default:x(()=>[r("div",z,_(o.value||"请选择物资种类"),1)]),_:1},8,["value","options"])]),r("div",G,[y(a,{value:l.value,"onUpdate:value":t[1]||(t[1]=s=>l.value=s),options:v.value,placeholder:"选择省市区",onChange:F,"dropdown-style":{background:"rgba(0, 76, 102, 0.8)",border:"1px solid #2BCCFF",backdropFilter:"blur(10px)",color:"#fff"},"dropdown-class-name":"visual-cascader-dropdown"},{default:x(()=>[r("div",J,_(c.value||"选择省市区"),1)]),_:1},8,["value","options"])])]),t[2]||(t[2]=r("div",{class:"header-center"},[r("h1",{class:"main-title"},"灰谷网经营数据可视化大屏")],-1)),r("div",K,[r("div",P,_(m.value),1)])])])}}}),te=O(Q,[["__scopeId","data-v-8f54f752"]]);export{te as default};
