import{d as r,f as i,w as s,aB as l,ar as n,u as d,aD as m,at as f,aA as c}from"./vue-vendor-dy9k-Yad.js";import{J as h}from"./antd-vue-vendor-me9YkNVC.js";import{useECharts as u}from"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const v=r({__name:"SaleRadar",props:{loading:Boolean,width:{type:String,default:"100%"},height:{type:String,default:"400px"}},setup(a){const e=a,t=i(null),{setOptions:o}=u(t);return s(()=>e.loading,()=>{e.loading||o({legend:{bottom:0,data:["Visits","Sales"]},tooltip:{},radar:{radius:"60%",splitNumber:8,indicator:[{name:"2017"},{name:"2017"},{name:"2018"},{name:"2019"},{name:"2020"},{name:"2021"}]},series:[{type:"radar",symbolSize:0,areaStyle:{shadowBlur:0,shadowColor:"rgba(0,0,0,.2)",shadowOffsetX:0,shadowOffsetY:10,opacity:1},data:[{value:[90,50,86,40,50,20],name:"Visits",itemStyle:{color:"#b6a2de"}},{value:[70,75,70,76,20,85],name:"Sales",itemStyle:{color:"#67e0e3"}}]}]})},{immediate:!0}),(p,g)=>(n(),l(d(h),{title:"销售统计",loading:a.loading},{default:m(()=>[f("div",{ref_key:"chartRef",ref:t,style:c({width:a.width,height:a.height})},null,4)]),_:1},8,["loading"]))}});export{v as default};
