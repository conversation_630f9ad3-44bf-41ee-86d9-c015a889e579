import{d as a,ag as u,aq as d,ar as f,at as k,k as r,aD as p,G as e,u as o,F as C}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as v}from"./useForm-CgkFTrrO.js";import{B as g}from"./BasicForm-DBcXiHk0.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const b={style:{margin:"20px auto","text-align":"center"}},Bt=a({__name:"BasicFormCompact",setup(x){const n=[{field:"visitor",label:"来访人员",component:"Input"},{field:"accessed",label:"来访日期",component:"DatePicker"},{field:"phone",label:"来访人手机号",component:"Input"}],[l,{setProps:i}]=v({schemas:n,labelWidth:"150px",showActionButtonGroup:!1,autoFocusFirstItem:!0});return(F,t)=>{const m=u("a-button");return f(),d(C,null,[k("div",b,[r(m,{onClick:t[0]||(t[0]=s=>o(i)({size:"large"})),class:"mr-2"},{default:p(()=>t[7]||(t[7]=[e(" 更改大小为最大 ")])),_:1,__:[7]}),r(m,{onClick:t[1]||(t[1]=s=>o(i)({size:"default"})),class:"mr-2"},{default:p(()=>t[8]||(t[8]=[e(" 还原大小 ")])),_:1,__:[8]}),r(m,{onClick:t[2]||(t[2]=s=>o(i)({size:"small"})),class:"mr-2"},{default:p(()=>t[9]||(t[9]=[e(" 更改大小为最小 ")])),_:1,__:[9]}),r(m,{onClick:t[3]||(t[3]=s=>o(i)({disabled:!0})),class:"mr-2"},{default:p(()=>t[10]||(t[10]=[e(" 禁用表单 ")])),_:1,__:[10]}),r(m,{onClick:t[4]||(t[4]=s=>o(i)({disabled:!1})),class:"mr-2"},{default:p(()=>t[11]||(t[11]=[e(" 解除禁用 ")])),_:1,__:[11]}),r(m,{onClick:t[5]||(t[5]=s=>o(i)({compact:!0})),class:"mr-2"},{default:p(()=>t[12]||(t[12]=[e(" 紧凑表单 ")])),_:1,__:[12]}),r(m,{onClick:t[6]||(t[6]=s=>o(i)({compact:!1})),class:"mr-2"},{default:p(()=>t[13]||(t[13]=[e(" 还原正常间距 ")])),_:1,__:[13]})]),r(o(g),{onRegister:o(l),style:{"margin-top":"20px"}},null,8,["onRegister"])],64)}}});export{Bt as default};
