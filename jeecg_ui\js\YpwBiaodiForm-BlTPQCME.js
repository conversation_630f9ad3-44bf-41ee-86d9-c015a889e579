var h=Object.defineProperty;var u=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var f=(o,r,t)=>r in o?h(o,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[r]=t,_=(o,r)=>{for(var t in r||(r={}))w.call(r,t)&&f(o,t,r[t]);if(u)for(var t of u(r))D.call(r,t)&&f(o,t,r[t]);return o};var l=(o,r,t)=>new Promise((n,a)=>{var p=i=>{try{m(t.next(i))}catch(s){a(s)}},e=i=>{try{m(t.throw(i))}catch(s){a(s)}},m=i=>i.done?n(i.value):Promise.resolve(i.value).then(p,e);m((t=t.apply(o,r)).next())});import"./index-L3cSIXth.js";import{d as k,e as C,ag as y,aq as B,ar as F,k as b,ah as v,aD as V,G as x}from"./vue-vendor-dy9k-Yad.js";import{H as g,j as $,a as j}from"./index-CCWaWN5g.js";import{f as q,i as I}from"./YpwBiaodi.api-5rGT4IC-.js";import{B as N}from"./BasicForm-DBcXiHk0.js";import{u as G}from"./useForm-CgkFTrrO.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const H=k({name:"YpwBiaodiForm",components:{BasicForm:N},props:{formData:g.object.def({}),formBpm:g.bool.def(!0)},setup(o){const[r,{setFieldsValue:t,setProps:n,getFieldsValue:a}]=G({labelWidth:150,schemas:I(o.formData),showActionButtonGroup:!1,baseColProps:{span:8}}),p=C(()=>o.formData.disabled!==!1);let e={};const m="/ypw/ypwBiaodi/queryById";function i(){return l(this,null,function*(){let d={id:o.formData.dataId};const c=yield $.get({url:m,params:d});e=_({},c),yield t(e),yield n({disabled:p.value})})}function s(){return l(this,null,function*(){let d=a(),c=Object.assign({},e,d);yield q(c,!0)})}return i(),{registerForm:r,formDisabled:p,submitForm:s}}}),O={style:{"min-height":"400px"}},P={key:0,style:{width:"100%","text-align":"center"}};function R(o,r,t,n,a,p){const e=y("BasicForm"),m=y("a-button");return F(),B("div",O,[b(e,{onRegister:o.registerForm},null,8,["onRegister"]),o.formDisabled?v("",!0):(F(),B("div",P,[b(m,{onClick:o.submitForm,"pre-icon":"ant-design:check",type:"primary"},{default:V(()=>r[0]||(r[0]=[x("提 交")])),_:1,__:[0]},8,["onClick"])]))])}const Yt=j(H,[["render",R]]);export{Yt as default};
