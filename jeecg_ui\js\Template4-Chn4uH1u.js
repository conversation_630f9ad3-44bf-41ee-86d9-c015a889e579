import{a as b,j as r,bx as a}from"./index-CCWaWN5g.js";import{ag as p,aB as w,ar as S,aD as g,k as s}from"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const m={name:"Template4",data(){return{table1:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0},lastRow:null,selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:a.input},{key:"call",title:"呼叫",width:"80px",type:a.input},{key:"len",title:"长",width:"80px",type:a.input},{key:"ton",title:"吨",width:"120px",type:a.input},{key:"payer",title:"付款方",width:"120px",type:a.input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:a.input},{key:"trend",title:"动向",width:"120px",type:a.input}]},table2:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:a.input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:a.input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:a.input},{key:"type",title:"船舶分类",width:"120px",type:a.input},{key:"port_area",title:"所属港区",width:"120px",type:a.input}]},table3:{lastRow:null,selectedRows:[]},table4:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:a.input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:a.input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:a.input},{key:"type",title:"船舶分类",width:"120px",type:a.input},{key:"port_area",title:"所属港区",width:"120px",type:a.input}]},url:{getData:"/mock/vxe/getData"}}},watch:{"table1.lastRow"(){this.loadTable2Data()},"table3.lastRow"(){this.loadTable4Data()}},created(){this.loadTable1Data()},methods:{loadTable1Data(){let e={pageNo:this.table1.pagination.current,pageSize:this.table1.pagination.pageSize};this.table1.loading=!0,r.get({url:this.url.getData,params:e}).then(l=>{this.table1.pagination.total=l.total,this.table1.dataSource=l.records,this.table1.selectedRows=[]}).finally(()=>{this.table1.loading=!1})},handleTable1PageChange(e){this.table1.pagination.current=e.current,this.table1.pagination.pageSize=e.pageSize,this.loadTable1Data()},handleTable1SelectRowChange(e){this.handleTableSelectRowChange(this.table1,e)},loadTable2Data(){let e=this.table1.selectedRows;if(!e||e.length===0){this.table2.pagination.total=0,this.table2.dataSource=[],this.table2.selectedRows=[];return}else this.table1.lastRow==null&&(this.table1.lastRow=e[e.length-1]);let l={parentId:this.table1.lastRow.id,pageNo:this.table2.pagination.current,pageSize:this.table2.pagination.pageSize};this.table2.loading=!0,r.get({url:this.url.getData,params:l}).then(i=>{this.table2.pagination.total=i.total,this.table2.dataSource=i.records,this.table2.selectedRows=[]}).finally(()=>{this.table2.loading=!1})},handleTable2PageChange(e){this.table2.pagination.current=e.current,this.table2.pagination.pageSize=e.pageSize,this.loadTable2Data()},handleTable3SelectRowChange(e){this.handleTableSelectRowChange(this.table3,e)},loadTable4Data(){let e=[],l=this.table3.selectedRows;if(!l||l.length===0){this.table4.pagination.total=0,this.table4.dataSource=[],this.table4.selectedRows=[];return}else this.table3.lastRow==null&&(this.table3.lastRow=l[l.length-1]);let i={parentId:this.table3.lastRow.id,pageNo:this.table4.pagination.current,pageSize:this.table4.pagination.pageSize};this.table4.loading=!0,r.get({url:this.url.getData,params:i}).then(o=>{this.table4.pagination.total=o.total,this.table4.dataSource=o.records,this.table4.selectedRows=[]}).finally(()=>{this.table4.loading=!1})},handleTable4PageChange(e){this.table4.pagination.current=e.current,this.table4.pagination.pageSize=e.pageSize,this.loadTable4Data()},handleTableSelectRowChange(e,l){let{row:i,action:o,selectedRows:t,$table:h}=l,n=t[t.length-1];o==="selected"?e.lastRow=i:o==="selected-all"?t.length===0?e.lastRow=null:e.lastRow||(e.lastRow=n):o==="unselected"&&i===e.lastRow&&(e.lastRow=n),h.setCurrentRow(e.lastRow),e.selectedRows=t}}};function R(e,l,i,o,t,h){const n=p("JVxeTable"),c=p("a-col"),d=p("a-row"),u=p("a-card");return S(),w(u,{bordered:!1},{default:g(()=>[s(d,{gutter:8},{default:g(()=>[s(c,{span:12},{default:g(()=>[s(n,{toolbar:"",rowNumber:"",rowSelection:"",clickSelectRow:"",highlightCurrentRow:"",radioConfig:{highlight:!1},checkboxConfig:{highlight:!1},height:340,loading:t.table1.loading,columns:t.table1.columns,dataSource:t.table1.dataSource,pagination:t.table1.pagination,style:{"margin-bottom":"8px"},onPageChange:h.handleTable1PageChange,onSelectRowChange:h.handleTable1SelectRowChange},null,8,["loading","columns","dataSource","pagination","onPageChange","onSelectRowChange"]),s(n,{toolbar:"",rowNumber:"",rowSelection:"",clickSelectRow:"",height:350,loading:t.table2.loading,columns:t.table2.columns,dataSource:t.table2.dataSource,pagination:t.table2.pagination,onPageChange:h.handleTable2PageChange},null,8,["loading","columns","dataSource","pagination","onPageChange"])]),_:1}),s(c,{span:12},{default:g(()=>[s(n,{rowNumber:"",rowSelection:"",clickSelectRow:"",highlightCurrentRow:"",radioConfig:{highlight:!1},checkboxConfig:{highlight:!1},height:340,columns:t.table1.columns,dataSource:t.table1.selectedRows,style:{margin:"52px 0 8px"},onSelectRowChange:h.handleTable3SelectRowChange},null,8,["columns","dataSource","onSelectRowChange"]),s(n,{toolbar:"",rowNumber:"",rowSelection:"",clickSelectRow:"",height:350,loading:t.table4.loading,columns:t.table4.columns,dataSource:t.table4.dataSource,pagination:t.table4.pagination,style:{margin:"48px 0 0"}},null,8,["loading","columns","dataSource","pagination"])]),_:1})]),_:1})]),_:1})}const k=b(m,[["render",R]]);export{k as default};
