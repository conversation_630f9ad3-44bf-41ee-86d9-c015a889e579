var T=Object.defineProperty,V=Object.defineProperties;var G=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var P=(e,t,r)=>t in e?T(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,y=(e,t)=>{for(var r in t||(t={}))L.call(t,r)&&P(e,r,t[r]);if(R)for(var r of R(t))O.call(t,r)&&P(e,r,t[r]);return e},B=(e,t)=>V(e,G(t));var D=(e,t,r)=>new Promise((h,p)=>{var g=u=>{try{f(r.next(u))}catch(c){p(c)}},d=u=>{try{f(r.throw(u))}catch(c){p(c)}},f=u=>u.done?h(u.value):Promise.resolve(u.value).then(g,d);f((r=r.apply(e,t)).next())});import{d as q,f as z,ap as F,h as H,ag as I,aq as E,ar as b,as as Q,k as U,aD as M,aB as w,ah as W,au as A,G as j}from"./vue-vendor-dy9k-Yad.js";import{y as N}from"./antd-vue-vendor-me9YkNVC.js";import{H as J,c as K,F as X,D as Y,bu as Z,cW as x,cT as ee,cU as te,b0 as ne,N as ae,Q as re,a as se}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const oe=q({name:"LayoutBreadcrumb",components:{Icon:K,[N.name]:N},props:{theme:J.oneOf(["dark","light"])},setup(){const e=z([]),{currentRoute:t}=F(),{prefixCls:r}=X("layout-breadcrumb"),{getShowBreadCrumbIcon:h}=Y(),p=Z(),{t:g}=ae();H(()=>D(null,null,function*(){var v,_,$;if(t.value.name===x)return;const s=yield ee(),n=t.value.matched,a=n==null?void 0:n[n.length-1];let o=t.value.path;a&&((v=a==null?void 0:a.meta)!=null&&v.currentActiveMenu)&&(o=a.meta.currentActiveMenu);const i=te(s,o),m=s.filter(S=>S.path===i[0]),l=d(m,i);if(!l||l.length===0)return;const k=f(l);(_=t.value.meta)!=null&&_.currentActiveMenu&&k.push(B(y({},t.value),{name:(($=t.value.meta)==null?void 0:$.title)||t.value.name})),e.value=k}));function d(s,n){const a=[];return s.forEach(o=>{var i,m;n.includes(o.path)&&a.push(B(y({},o),{name:((i=o.meta)==null?void 0:i.title)||o.name})),(m=o.children)!=null&&m.length&&a.push(...d(o.children,n))}),a}function f(s){return ne(s,n=>{const{meta:a,name:o}=n;if(!a)return!!o;const{title:i,hideBreadcrumb:m}=a;return!(!i||m)}).filter(n=>{var a;return!((a=n.meta)!=null&&a.hideBreadcrumb)})}function u(s,n,a){a==null||a.preventDefault();const{children:o,redirect:i,meta:m}=s;if(o!=null&&o.length&&!i){a==null||a.stopPropagation();return}if(!(m!=null&&m.carryParam))if(i&&re(i))p(i);else{let l="";n.length===1?l=n[0]:l=`${n.slice(1).pop()||""}`,l=/^\//.test(l)?l:`/${l}`,p(l)}}function c(s,n){return s.indexOf(n)!==s.length-1}function C(s){var n;return s.icon||((n=s.meta)==null?void 0:n.icon)}return{routes:e,t:g,prefixCls:r,getIcon:C,getShowBreadCrumbIcon:h,handleClick:u,hasRedirect:c}}}),ce={key:1};function ie(e,t,r,h,p,g){const d=I("Icon"),f=I("router-link"),u=I("a-breadcrumb");return b(),E("div",{class:Q([e.prefixCls,`${e.prefixCls}--${e.theme}`])},[U(u,{routes:e.routes},{itemRender:M(({route:c,routes:C,paths:s})=>[e.getShowBreadCrumbIcon&&e.getIcon(c)?(b(),w(d,{key:0,icon:e.getIcon(c)},null,8,["icon"])):W("",!0),e.hasRedirect(C,c)?(b(),w(f,{key:2,to:"",onClick:n=>e.handleClick(c,s,n),style:{color:"inherit"}},{default:M(()=>[j(A(e.t(c.name||c.meta.title)),1)]),_:2},1032,["onClick"])):(b(),E("span",ce,A(e.t(c.name||c.meta.title)),1))]),_:1},8,["routes"])],2)}const de=se(oe,[["render",ie]]);export{de as default};
