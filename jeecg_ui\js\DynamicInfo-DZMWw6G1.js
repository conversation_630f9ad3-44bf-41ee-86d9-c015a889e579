import{d as u,ag as p,aB as _,ar as f,u as a,aE as I,aD as t,k as e,G as s,at as L,au as i}from"./vue-vendor-dy9k-Yad.js";import{J as y,L as n}from"./antd-vue-vendor-me9YkNVC.js";import{d as k}from"./data-BJoU8O08.js";import{c as x}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const B=["innerHTML"],N=u({__name:"DynamicInfo",setup(C){const l=n.Item,m=n.Item.Meta;return(c,r)=>{const d=p("a-button");return f(),_(a(y),I({title:"最新动态"},c.$attrs),{extra:t(()=>[e(d,{type:"link",size:"small"},{default:t(()=>r[0]||(r[0]=[s("更多")])),_:1,__:[0]})]),default:t(()=>[e(a(n),{"item-layout":"horizontal","data-source":a(k)},{renderItem:t(({item:o})=>[e(a(l),null,{default:t(()=>[e(a(m),null,{description:t(()=>[s(i(o.date),1)]),title:t(()=>[s(i(o.name)+" ",1),L("span",{innerHTML:o.desc},null,8,B)]),avatar:t(()=>[e(a(x),{icon:o.avatar,size:30},null,8,["icon"])]),_:2},1024)]),_:2},1024)]),_:1},8,["data-source"])]),_:1},16)}}});export{N as default};
