import{d as y,f as C,ag as u,aq as x,ar as U,k as t,aD as e,G as a,u as p,F as B}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import"./index-B4ez5KWV.js";import"./index-Diw57m_E.js";import{ad as F}from"./index-CCWaWN5g.js";import{u as k}from"./useForm-CgkFTrrO.js";import{B as w}from"./BasicForm-DBcXiHk0.js";import{_ as J}from"./JUploadModal-C-iKhVFc.js";import{U as l}from"./JUpload-CRos0F1P.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./CustomModal-BakuIxQv.js";const So=y({__name:"JUploadDemo",setup(R){const n=C(""),d=[{field:"uploadFile",component:"JUpload",helpMessage:"无限制上传",label:"上传文件"},{field:"uploadFileMax",component:"JUpload",helpMessage:"最多上传3个文件",label:"上传文件（3）",componentProps:{maxCount:3}},{field:"uploadImage",component:"JUpload",label:"上传图片",helpMessage:"无限制上传",componentProps:{fileType:l.image}},{field:"uploadImageMax",component:"JUpload",label:"上传图片（1）",helpMessage:"最多上传1张图片",componentProps:{fileType:l.image,maxCount:1}}],[f,{openModal:g}]=F(),[b,{setProps:_,validate:T,setFieldsValue:P}]=k({labelWidth:120,schemas:d,actionColOptions:{span:24},compact:!0,showResetButton:!1,showSubmitButton:!1,showAdvancedButton:!1,disabled:!1});function c(r){}function s(r){_({disabled:!!r})}function v(){g(!0,{maxCount:9,fileType:l.image})}return(r,o)=>{const i=u("a-button"),M=u("a-button-group");return U(),x(B,null,[t(M,{class:"j-table-operator"},{default:e(()=>[t(i,{type:"primary",onClick:o[0]||(o[0]=m=>s(0))},{default:e(()=>o[3]||(o[3]=[a("启用")])),_:1,__:[3]}),t(i,{type:"primary",onClick:o[1]||(o[1]=m=>s(1))},{default:e(()=>o[4]||(o[4]=[a("禁用")])),_:1,__:[4]}),t(i,{type:"primary",onClick:v},{default:e(()=>o[5]||(o[5]=[a("文件弹窗")])),_:1,__:[5]})]),_:1}),t(p(w),{onRegister:p(b),onSubmit:c},null,8,["onRegister"]),t(p(J),{value:n.value,"onUpdate:value":o[2]||(o[2]=m=>n.value=m),onRegister:p(f)},null,8,["value","onRegister"])],64)}}});export{So as default};
