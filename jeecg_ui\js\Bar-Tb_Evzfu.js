import{d as l,f,r as h,h as p,aq as d,ar as u,aA as m}from"./vue-vendor-dy9k-Yad.js";import{useECharts as y}from"./useECharts-BU6FzBZi.js";import{h as g}from"./antd-vue-vendor-me9YkNVC.js";import{a as x}from"./index-CCWaWN5g.js";const b=l({name:"bar",props:{chartData:{type:Array,default:()=>[]},option:{type:Object,default:()=>({})},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"},seriesColor:{type:String,default:"#1890ff"}},setup(t){const a=f(null),{setOptions:o,echarts:i}=y(a),e=h({tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0,backgroundColor:"#333"}}},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"bar",type:"bar",data:[],color:t.seriesColor}]});p(()=>{t.chartData&&s()});function s(){t.option&&Object.assign(e,g(t.option));let n=t.chartData.map(r=>r.value),c=t.chartData.map(r=>r.name);e.series[0].data=n,e.series[0].color=t.seriesColor,e.xAxis.data=c,o(e)}return{chartRef:a}}});function C(t,a,o,i,e,s){return u(),d("div",{ref:"chartRef",style:m({height:t.height,width:t.width})},null,4)}const v=x(b,[["render",C]]);export{v as B};
