import{d as h,e as g,aq as w,ar as a,aB as n,ah as c,at as t,u as o,k as s,au as p,as as x}from"./vue-vendor-dy9k-Yad.js";import{b5 as y,F as v,bV as b,bO as k,bW as B,bX as L,bY as d,N as S}from"./index-CCWaWN5g.js";import T from"./LoginForm-Cv1KIRFA.js";import $ from"./ForgetPasswordForm-D5tOYC3b.js";import C from"./RegisterForm-DI32cwRE.js";import D from"./MobileForm-BL07NIax.js";import N from"./QrCodeForm-CVY8RjiB.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./checkcode-DLY3GIII.js";import"./LoginFormTitle-Bms3O5Qx.js";import"./ThirdModal-1GWAIf70.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFormItemSingle-Cw668yj5.js";import"./useThirdLogin-C4qxd7Ms.js";import"./step1-CkQhLHRg.js";import"./step2-tEElob-Y.js";import"./index-CBCjSSNZ.js";import"./step3-dZT9Navo.js";import"./index-C1YxH9KC.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";const V="/assets/login-box-bg-BwcHql23.svg",A={class:"-enter-x xl:hidden"},I={class:"container relative h-full py-2 mx-auto sm:px-10"},q={class:"flex h-full"},P={class:"hidden min-h-full pl-4 mr-4 xl:flex xl:flex-col xl:w-6/12"},z={class:"my-auto"},E=["alt"],F={class:"mt-10 font-medium text-white -enter-x"},G={class:"inline-block mt-4 text-3xl"},H={class:"mt-5 font-normal text-white text-md dark:text-gray-500 -enter-x"},M={class:"flex w-full h-full py-5 xl:h-auto xl:py-0 xl:my-0 xl:w-6/12"},Lt=h({__name:"Login",props:{sessionTimeout:{type:Boolean}},setup(l){const e=y(),{prefixCls:r}=v("login"),{t:m}=S(),u=b().getShowPicker,_=g(()=>{var i;return(i=e==null?void 0:e.title)!=null?i:""}),{handleBackLogin:f}=k();return f(),(i,W)=>(a(),w("div",{class:x([o(r),"relative w-full h-full px-4"])},[!l.sessionTimeout&&o(u)?(a(),n(o(B),{key:0,class:"absolute text-white top-4 right-4 enter-x xl:text-gray-600",showText:!1})):c("",!0),l.sessionTimeout?c("",!0):(a(),n(o(L),{key:1,class:"absolute top-3 right-7 enter-x"})),t("span",A,[s(o(d),{alwaysShowTitle:!0})]),t("div",I,[t("div",q,[t("div",P,[s(o(d),{class:"-enter-x"}),t("div",z,[t("img",{alt:_.value,src:V,class:"w-1/2 -mt-16 -enter-x"},null,8,E),t("div",F,[t("span",G,p(o(m)("sys.login.signInTitle")),1)]),t("div",H,p(o(m)("sys.login.signInDesc")),1)])]),t("div",M,[t("div",{class:x([`${o(r)}-form`,"relative w-full px-5 py-8 mx-auto my-auto rounded-md shadow-md xl:ml-16 xl:bg-transparent sm:px-8 xl:p-4 xl:shadow-none sm:w-3/4 lg:w-2/4 xl:w-auto enter-x"])},[s(T),s($),s(C),s(D),s(N)],2)])])])],2))}});export{Lt as default};
