import{d as g,ag as t,aB as l,ar as r,as as a,aD as s,k as n,aq as $,F as I,aC as k,at as c,au as i}from"./vue-vendor-dy9k-Yad.js";import{a7 as m,a6 as p,J as B,L as _}from"./antd-vue-vendor-me9YkNVC.js";import{projectList as h}from"./data-BsYYuWeG.js";import{a as v}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const w="/assets/demo-1i9Hn8PF.png",j=g({components:{List:_,ListItem:_.Item,Card:B,[p.name]:p,[m.name]:m},setup(){return{prefixCls:"account-center-project",list:h,demoImg:w}}}),x=["src"];function F(e,y,D,N,P,V){const d=t("Card"),f=t("ListItem"),u=t("a-col"),C=t("a-row"),L=t("List");return r(),l(L,{class:a(e.prefixCls)},{default:s(()=>[n(C,{gutter:16},{default:s(()=>[(r(!0),$(I,null,k(e.list,o=>(r(),l(u,{key:o.title,span:6},{default:s(()=>[n(f,null,{default:s(()=>[n(d,{hoverable:!0,class:a(`${e.prefixCls}__card`)},{default:s(()=>[c("img",{src:e.demoImg},null,8,x),c("div",{class:a(`${e.prefixCls}__card-title`)},i(o.title),3),c("div",{class:a(`${e.prefixCls}__card-content`)},i(o.content),3)]),_:2},1032,["class"])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["class"])}const J=v(j,[["render",F]]);export{J as default};
