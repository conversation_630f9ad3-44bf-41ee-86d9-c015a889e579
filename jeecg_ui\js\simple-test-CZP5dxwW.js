import{d,f as u,ag as _,aq as f,ar as c,at as t,k as s,aD as n,G as C,u as v}from"./vue-vendor-dy9k-Yad.js";import{C as x}from"./index-Diw57m_E.js";import{a as b}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const k={class:"p-4"},w={class:"test-buttons"},B=d({__name:"simple-test",setup(M){const e=u(!1),a=()=>{e.value=!0},l=()=>{e.value=!1},r=()=>{},p=()=>{};return(N,o)=>{const i=_("a-button");return c(),f("div",k,[o[3]||(o[3]=t("h2",null,"CustomModal 简单测试",-1)),t("div",w,[s(i,{type:"primary",onClick:a},{default:n(()=>o[1]||(o[1]=[C("显示弹窗")])),_:1,__:[1]})]),s(v(x),{open:e.value,"onUpdate:open":o[0]||(o[0]=m=>e.value=m),title:"自主委托",width:"600",onConfirm:l,onCancel:r,onClose:p},{default:n(()=>o[2]||(o[2]=[t("div",{class:"modal-content"},[t("p",null,"我方仅将客户资产信息在我网进行公示，我司不会对客户所发布信息做任何协助"),t("p",null,"请确认是否选择自主服务")],-1)])),_:1,__:[2]},8,["open"])])}}}),H=b(B,[["__scopeId","data-v-13242118"]]);export{H as default};
