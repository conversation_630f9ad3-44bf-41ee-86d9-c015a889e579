import{d as i,aq as l,ar as n,at as d,k as t}from"./vue-vendor-dy9k-Yad.js";import a from"./CountTo-Bf9dGyG1.js";import{a as o}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const r={class:"data-cards"},v={class:"data-card"},c={class:"card-content"},u={class:"card-value"},e={class:"data-card"},p={class:"card-content"},m={class:"card-value"},b={class:"data-card"},g={class:"card-content"},f={class:"card-value"},C={class:"data-card"},k={class:"card-content"},x={class:"card-value"},B={class:"data-card"},D={class:"card-content"},N={class:"card-value"},V={class:"data-card"},q={class:"card-content"},E={class:"card-value"},I={class:"data-card"},T={class:"card-content"},j={class:"card-value"},w={class:"data-card"},y={class:"card-content"},z={class:"card-value"},A=i({__name:"DataCards",setup(F){return(G,s)=>(n(),l("div",r,[d("div",v,[s[3]||(s[3]=d("div",{class:"card-bg"},null,-1)),d("div",c,[s[1]||(s[1]=d("div",{class:"card-title"},"成交总额",-1)),d("div",u,[t(a,{start:0,end:59645956,duration:2e3,decimals:0}),s[0]||(s[0]=d("span",{class:"card-unit"},"万元",-1))]),s[2]||(s[2]=d("div",{class:"card-icon"},[d("div",{class:"icon-bg"})],-1))])]),d("div",e,[s[7]||(s[7]=d("div",{class:"card-bg"},null,-1)),d("div",p,[s[5]||(s[5]=d("div",{class:"card-title"},"溢价总额",-1)),d("div",m,[t(a,{start:0,end:5956,duration:2e3,decimals:0}),s[4]||(s[4]=d("span",{class:"card-unit"},"万元",-1))]),s[6]||(s[6]=d("div",{class:"card-icon"},[d("div",{class:"icon-bg"})],-1))])]),d("div",b,[s[11]||(s[11]=d("div",{class:"card-bg"},null,-1)),d("div",g,[s[9]||(s[9]=d("div",{class:"card-title"},"总溢价率",-1)),d("div",f,[t(a,{start:0,end:95,duration:2e3,decimals:2}),s[8]||(s[8]=d("span",{class:"card-unit"},"%",-1))]),s[10]||(s[10]=d("div",{class:"card-icon"},[d("div",{class:"icon-bg"})],-1))])]),d("div",C,[s[15]||(s[15]=d("div",{class:"card-bg"},null,-1)),d("div",k,[s[13]||(s[13]=d("div",{class:"card-title"},"标的总量",-1)),d("div",x,[t(a,{start:0,end:5956,duration:2e3,decimals:0}),s[12]||(s[12]=d("span",{class:"card-unit"},"个",-1))]),s[14]||(s[14]=d("div",{class:"card-icon"},[d("div",{class:"icon-bg"})],-1))])]),d("div",B,[s[19]||(s[19]=d("div",{class:"card-bg"},null,-1)),d("div",D,[s[17]||(s[17]=d("div",{class:"card-title"},"资产处置总量",-1)),d("div",N,[t(a,{start:0,end:2956,duration:2e3,decimals:0}),s[16]||(s[16]=d("span",{class:"card-unit"},"个",-1))]),s[18]||(s[18]=d("div",{class:"card-icon"},[d("div",{class:"icon-bg"})],-1))])]),d("div",V,[s[23]||(s[23]=d("div",{class:"card-bg"},null,-1)),d("div",q,[s[21]||(s[21]=d("div",{class:"card-title"},"标的成交额",-1)),d("div",E,[t(a,{start:0,end:35956,duration:2e3,decimals:0}),s[20]||(s[20]=d("span",{class:"card-unit"},"万",-1))]),s[22]||(s[22]=d("div",{class:"card-icon"},[d("div",{class:"icon-bg"})],-1))])]),d("div",I,[s[27]||(s[27]=d("div",{class:"card-bg"},null,-1)),d("div",T,[s[25]||(s[25]=d("div",{class:"card-title"},"资产处置成交额",-1)),d("div",j,[t(a,{start:0,end:23689,duration:2e3,decimals:0}),s[24]||(s[24]=d("span",{class:"card-unit"},"万",-1))]),s[26]||(s[26]=d("div",{class:"card-icon"},[d("div",{class:"icon-bg"})],-1))])]),d("div",w,[s[31]||(s[31]=d("div",{class:"card-bg"},null,-1)),d("div",y,[s[29]||(s[29]=d("div",{class:"card-title"},"标的溢价率",-1)),d("div",z,[t(a,{start:0,end:87.5,duration:2e3,decimals:2}),s[28]||(s[28]=d("span",{class:"card-unit"},"%",-1))]),s[30]||(s[30]=d("div",{class:"card-icon"},[d("div",{class:"icon-bg"})],-1))])])]))}}),O=o(A,[["__scopeId","data-v-6911e069"]]);export{O as default};
