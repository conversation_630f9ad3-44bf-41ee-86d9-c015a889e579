var le=Object.defineProperty;var At=Object.getOwnPropertySymbols;var fe=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable;var Tt=(r,o,i)=>o in r?le(r,o,{enumerable:!0,configurable:!0,writable:!0,value:i}):r[o]=i,It=(r,o)=>{for(var i in o||(o={}))fe.call(o,i)&&Tt(r,i,o[i]);if(At)for(var i of At(o))de.call(o,i)&&Tt(r,i,o[i]);return r};var St=(r,o,i)=>new Promise((n,t)=>{var e=u=>{try{a(i.next(u))}catch(c){t(c)}},s=u=>{try{a(i.throw(u))}catch(c){t(c)}},a=u=>u.done?n(u.value):Promise.resolve(u.value).then(e,s);a((i=i.apply(r,o)).next())});import{Q as ge,a as he,w as me}from"./index-CCWaWN5g.js";import{d as we,f as Ce,u as $,o as Ee,w as ye,aq as pe,ar as Mt,aB as Be,aQ as Re}from"./vue-vendor-dy9k-Yad.js";import{a as on,h as Ae}from"./antd-vue-vendor-me9YkNVC.js";import{downloadByUrl as Te}from"./download-CZ-9H9a3.js";var z={},W,Pt;function Ie(){return Pt||(Pt=1,W=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),W}var X={},_={},Nt;function U(){if(Nt)return _;Nt=1;let r;const o=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return _.getSymbolSize=function(n){if(!n)throw new Error('"version" cannot be null or undefined');if(n<1||n>40)throw new Error('"version" should be in range from 1 to 40');return n*4+17},_.getSymbolTotalCodewords=function(n){return o[n]},_.getBCHDigit=function(i){let n=0;for(;i!==0;)n++,i>>>=1;return n},_.setToSJISFunction=function(n){if(typeof n!="function")throw new Error('"toSJISFunc" is not a valid function.');r=n},_.isKanjiModeEnabled=function(){return typeof r!="undefined"},_.toSJIS=function(n){return r(n)},_}var Z={},bt;function Et(){return bt||(bt=1,function(r){r.L={bit:1},r.M={bit:0},r.Q={bit:3},r.H={bit:2};function o(i){if(typeof i!="string")throw new Error("Param is not a string");switch(i.toLowerCase()){case"l":case"low":return r.L;case"m":case"medium":return r.M;case"q":case"quartile":return r.Q;case"h":case"high":return r.H;default:throw new Error("Unknown EC Level: "+i)}}r.isValid=function(n){return n&&typeof n.bit!="undefined"&&n.bit>=0&&n.bit<4},r.from=function(n,t){if(r.isValid(n))return n;try{return o(n)}catch(e){return t}}}(Z)),Z}var x,Lt;function Se(){if(Lt)return x;Lt=1;function r(){this.buffer=[],this.length=0}return r.prototype={get:function(o){const i=Math.floor(o/8);return(this.buffer[i]>>>7-o%8&1)===1},put:function(o,i){for(let n=0;n<i;n++)this.putBit((o>>>i-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(o){const i=Math.floor(this.length/8);this.buffer.length<=i&&this.buffer.push(0),o&&(this.buffer[i]|=128>>>this.length%8),this.length++}},x=r,x}var tt,vt;function Me(){if(vt)return tt;vt=1;function r(o){if(!o||o<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=o,this.data=new Uint8Array(o*o),this.reservedBit=new Uint8Array(o*o)}return r.prototype.set=function(o,i,n,t){const e=o*this.size+i;this.data[e]=n,t&&(this.reservedBit[e]=!0)},r.prototype.get=function(o,i){return this.data[o*this.size+i]},r.prototype.xor=function(o,i,n){this.data[o*this.size+i]^=n},r.prototype.isReserved=function(o,i){return this.reservedBit[o*this.size+i]},tt=r,tt}var et={},Dt;function Pe(){return Dt||(Dt=1,function(r){const o=U().getSymbolSize;r.getRowColCoords=function(n){if(n===1)return[];const t=Math.floor(n/7)+2,e=o(n),s=e===145?26:Math.ceil((e-13)/(2*t-2))*2,a=[e-7];for(let u=1;u<t-1;u++)a[u]=a[u-1]-s;return a.push(6),a.reverse()},r.getPositions=function(n){const t=[],e=r.getRowColCoords(n),s=e.length;for(let a=0;a<s;a++)for(let u=0;u<s;u++)a===0&&u===0||a===0&&u===s-1||a===s-1&&u===0||t.push([e[a],e[u]]);return t}}(et)),et}var nt={},qt;function Ne(){if(qt)return nt;qt=1;const r=U().getSymbolSize,o=7;return nt.getPositions=function(n){const t=r(n);return[[0,0],[t-o,0],[0,t-o]]},nt}var rt={},_t;function be(){return _t||(_t=1,function(r){r.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const o={N1:3,N2:3,N3:40,N4:10};r.isValid=function(t){return t!=null&&t!==""&&!isNaN(t)&&t>=0&&t<=7},r.from=function(t){return r.isValid(t)?parseInt(t,10):void 0},r.getPenaltyN1=function(t){const e=t.size;let s=0,a=0,u=0,c=null,l=null;for(let d=0;d<e;d++){a=u=0,c=l=null;for(let g=0;g<e;g++){let f=t.get(d,g);f===c?a++:(a>=5&&(s+=o.N1+(a-5)),c=f,a=1),f=t.get(g,d),f===l?u++:(u>=5&&(s+=o.N1+(u-5)),l=f,u=1)}a>=5&&(s+=o.N1+(a-5)),u>=5&&(s+=o.N1+(u-5))}return s},r.getPenaltyN2=function(t){const e=t.size;let s=0;for(let a=0;a<e-1;a++)for(let u=0;u<e-1;u++){const c=t.get(a,u)+t.get(a,u+1)+t.get(a+1,u)+t.get(a+1,u+1);(c===4||c===0)&&s++}return s*o.N2},r.getPenaltyN3=function(t){const e=t.size;let s=0,a=0,u=0;for(let c=0;c<e;c++){a=u=0;for(let l=0;l<e;l++)a=a<<1&2047|t.get(c,l),l>=10&&(a===1488||a===93)&&s++,u=u<<1&2047|t.get(l,c),l>=10&&(u===1488||u===93)&&s++}return s*o.N3},r.getPenaltyN4=function(t){let e=0;const s=t.data.length;for(let u=0;u<s;u++)e+=t.data[u];return Math.abs(Math.ceil(e*100/s/5)-10)*o.N4};function i(n,t,e){switch(n){case r.Patterns.PATTERN000:return(t+e)%2===0;case r.Patterns.PATTERN001:return t%2===0;case r.Patterns.PATTERN010:return e%3===0;case r.Patterns.PATTERN011:return(t+e)%3===0;case r.Patterns.PATTERN100:return(Math.floor(t/2)+Math.floor(e/3))%2===0;case r.Patterns.PATTERN101:return t*e%2+t*e%3===0;case r.Patterns.PATTERN110:return(t*e%2+t*e%3)%2===0;case r.Patterns.PATTERN111:return(t*e%3+(t+e)%2)%2===0;default:throw new Error("bad maskPattern:"+n)}}r.applyMask=function(t,e){const s=e.size;for(let a=0;a<s;a++)for(let u=0;u<s;u++)e.isReserved(u,a)||e.xor(u,a,i(t,u,a))},r.getBestMask=function(t,e){const s=Object.keys(r.Patterns).length;let a=0,u=1/0;for(let c=0;c<s;c++){e(c),r.applyMask(c,t);const l=r.getPenaltyN1(t)+r.getPenaltyN2(t)+r.getPenaltyN3(t)+r.getPenaltyN4(t);r.applyMask(c,t),l<u&&(u=l,a=c)}return a}}(rt)),rt}var J={},Ut;function re(){if(Ut)return J;Ut=1;const r=Et(),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return J.getBlocksCount=function(t,e){switch(e){case r.L:return o[(t-1)*4+0];case r.M:return o[(t-1)*4+1];case r.Q:return o[(t-1)*4+2];case r.H:return o[(t-1)*4+3];default:return}},J.getTotalCodewordsCount=function(t,e){switch(e){case r.L:return i[(t-1)*4+0];case r.M:return i[(t-1)*4+1];case r.Q:return i[(t-1)*4+2];case r.H:return i[(t-1)*4+3];default:return}},J}var ot={},H={},Ft;function Le(){if(Ft)return H;Ft=1;const r=new Uint8Array(512),o=new Uint8Array(256);return function(){let n=1;for(let t=0;t<255;t++)r[t]=n,o[n]=t,n<<=1,n&256&&(n^=285);for(let t=255;t<512;t++)r[t]=r[t-255]}(),H.log=function(n){if(n<1)throw new Error("log("+n+")");return o[n]},H.exp=function(n){return r[n]},H.mul=function(n,t){return n===0||t===0?0:r[o[n]+o[t]]},H}var kt;function ve(){return kt||(kt=1,function(r){const o=Le();r.mul=function(n,t){const e=new Uint8Array(n.length+t.length-1);for(let s=0;s<n.length;s++)for(let a=0;a<t.length;a++)e[s+a]^=o.mul(n[s],t[a]);return e},r.mod=function(n,t){let e=new Uint8Array(n);for(;e.length-t.length>=0;){const s=e[0];for(let u=0;u<t.length;u++)e[u]^=o.mul(t[u],s);let a=0;for(;a<e.length&&e[a]===0;)a++;e=e.slice(a)}return e},r.generateECPolynomial=function(n){let t=new Uint8Array([1]);for(let e=0;e<n;e++)t=r.mul(t,new Uint8Array([1,o.exp(e)]));return t}}(ot)),ot}var it,zt;function De(){if(zt)return it;zt=1;const r=ve();function o(i){this.genPoly=void 0,this.degree=i,this.degree&&this.initialize(this.degree)}return o.prototype.initialize=function(n){this.degree=n,this.genPoly=r.generateECPolynomial(this.degree)},o.prototype.encode=function(n){if(!this.genPoly)throw new Error("Encoder not initialized");const t=new Uint8Array(n.length+this.degree);t.set(n);const e=r.mod(t,this.genPoly),s=this.degree-e.length;if(s>0){const a=new Uint8Array(this.degree);return a.set(e,s),a}return e},it=o,it}var st={},ut={},at={},Vt;function oe(){return Vt||(Vt=1,at.isValid=function(o){return!isNaN(o)&&o>=1&&o<=40}),at}var L={},Ht;function ie(){if(Ht)return L;Ht=1;const r="[0-9]+",o="[A-Z $%*+\\-./:]+";let i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";i=i.replace(/u/g,"\\u");const n="(?:(?![A-Z0-9 $%*+\\-./:]|"+i+`)(?:.|[\r
]))+`;L.KANJI=new RegExp(i,"g"),L.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),L.BYTE=new RegExp(n,"g"),L.NUMERIC=new RegExp(r,"g"),L.ALPHANUMERIC=new RegExp(o,"g");const t=new RegExp("^"+i+"$"),e=new RegExp("^"+r+"$"),s=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return L.testKanji=function(u){return t.test(u)},L.testNumeric=function(u){return e.test(u)},L.testAlphanumeric=function(u){return s.test(u)},L}var Kt;function F(){return Kt||(Kt=1,function(r){const o=oe(),i=ie();r.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},r.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},r.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},r.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},r.MIXED={bit:-1},r.getCharCountIndicator=function(e,s){if(!e.ccBits)throw new Error("Invalid mode: "+e);if(!o.isValid(s))throw new Error("Invalid version: "+s);return s>=1&&s<10?e.ccBits[0]:s<27?e.ccBits[1]:e.ccBits[2]},r.getBestModeForData=function(e){return i.testNumeric(e)?r.NUMERIC:i.testAlphanumeric(e)?r.ALPHANUMERIC:i.testKanji(e)?r.KANJI:r.BYTE},r.toString=function(e){if(e&&e.id)return e.id;throw new Error("Invalid mode")},r.isValid=function(e){return e&&e.bit&&e.ccBits};function n(t){if(typeof t!="string")throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return r.NUMERIC;case"alphanumeric":return r.ALPHANUMERIC;case"kanji":return r.KANJI;case"byte":return r.BYTE;default:throw new Error("Unknown mode: "+t)}}r.from=function(e,s){if(r.isValid(e))return e;try{return n(e)}catch(a){return s}}}(ut)),ut}var Jt;function qe(){return Jt||(Jt=1,function(r){const o=U(),i=re(),n=Et(),t=F(),e=oe(),s=7973,a=o.getBCHDigit(s);function u(g,f,w){for(let S=1;S<=40;S++)if(f<=r.getCapacity(S,w,g))return S}function c(g,f){return t.getCharCountIndicator(g,f)+4}function l(g,f){let w=0;return g.forEach(function(S){const N=c(S.mode,f);w+=N+S.getBitsLength()}),w}function d(g,f){for(let w=1;w<=40;w++)if(l(g,w)<=r.getCapacity(w,f,t.MIXED))return w}r.from=function(f,w){return e.isValid(f)?parseInt(f,10):w},r.getCapacity=function(f,w,S){if(!e.isValid(f))throw new Error("Invalid QR Code version");typeof S=="undefined"&&(S=t.BYTE);const N=o.getSymbolTotalCodewords(f),T=i.getTotalCodewordsCount(f,w),M=(N-T)*8;if(S===t.MIXED)return M;const p=M-c(S,f);switch(S){case t.NUMERIC:return Math.floor(p/10*3);case t.ALPHANUMERIC:return Math.floor(p/11*2);case t.KANJI:return Math.floor(p/13);case t.BYTE:default:return Math.floor(p/8)}},r.getBestVersionForData=function(f,w){let S;const N=n.from(w,n.M);if(Array.isArray(f)){if(f.length>1)return d(f,N);if(f.length===0)return 1;S=f[0]}else S=f;return u(S.mode,S.getLength(),N)},r.getEncodedBits=function(f){if(!e.isValid(f)||f<7)throw new Error("Invalid QR Code version");let w=f<<12;for(;o.getBCHDigit(w)-a>=0;)w^=s<<o.getBCHDigit(w)-a;return f<<12|w}}(st)),st}var ct={},Ot;function _e(){if(Ot)return ct;Ot=1;const r=U(),o=1335,i=21522,n=r.getBCHDigit(o);return ct.getEncodedBits=function(e,s){const a=e.bit<<3|s;let u=a<<10;for(;r.getBCHDigit(u)-n>=0;)u^=o<<r.getBCHDigit(u)-n;return(a<<10|u)^i},ct}var lt={},ft,jt;function Ue(){if(jt)return ft;jt=1;const r=F();function o(i){this.mode=r.NUMERIC,this.data=i.toString()}return o.getBitsLength=function(n){return 10*Math.floor(n/3)+(n%3?n%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(n){let t,e,s;for(t=0;t+3<=this.data.length;t+=3)e=this.data.substr(t,3),s=parseInt(e,10),n.put(s,10);const a=this.data.length-t;a>0&&(e=this.data.substr(t),s=parseInt(e,10),n.put(s,a*3+1))},ft=o,ft}var dt,Yt;function Fe(){if(Yt)return dt;Yt=1;const r=F(),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(n){this.mode=r.ALPHANUMERIC,this.data=n}return i.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let s=o.indexOf(this.data[e])*45;s+=o.indexOf(this.data[e+1]),t.put(s,11)}this.data.length%2&&t.put(o.indexOf(this.data[e]),6)},dt=i,dt}var gt,Qt;function ke(){if(Qt)return gt;Qt=1;const r=F();function o(i){this.mode=r.BYTE,typeof i=="string"?this.data=new TextEncoder().encode(i):this.data=new Uint8Array(i)}return o.getBitsLength=function(n){return n*8},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(i){for(let n=0,t=this.data.length;n<t;n++)i.put(this.data[n],8)},gt=o,gt}var ht,Gt;function ze(){if(Gt)return ht;Gt=1;const r=F(),o=U();function i(n){this.mode=r.KANJI,this.data=n}return i.getBitsLength=function(t){return t*13},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(n){let t;for(t=0;t<this.data.length;t++){let e=o.toSJIS(this.data[t]);if(e>=33088&&e<=40956)e-=33088;else if(e>=57408&&e<=60351)e-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);e=(e>>>8&255)*192+(e&255),n.put(e,13)}},ht=i,ht}var O={exports:{}},un=O.exports,$t;function Ve(){return $t||($t=1,function(r){"use strict";var o={single_source_shortest_paths:function(i,n,t){var e={},s={};s[n]=0;var a=o.PriorityQueue.make();a.push(n,0);for(var u,c,l,d,g,f,w,S,N;!a.empty();){u=a.pop(),c=u.value,d=u.cost,g=i[c]||{};for(l in g)g.hasOwnProperty(l)&&(f=g[l],w=d+f,S=s[l],N=typeof s[l]=="undefined",(N||S>w)&&(s[l]=w,a.push(l,w),e[l]=c))}if(typeof t!="undefined"&&typeof s[t]=="undefined"){var T=["Could not find a path from ",n," to ",t,"."].join("");throw new Error(T)}return e},extract_shortest_path_from_predecessor_list:function(i,n){for(var t=[],e=n,s;e;)t.push(e),s=i[e],e=i[e];return t.reverse(),t},find_path:function(i,n,t){var e=o.single_source_shortest_paths(i,n,t);return o.extract_shortest_path_from_predecessor_list(e,t)},PriorityQueue:{make:function(i){var n=o.PriorityQueue,t={},e;i=i||{};for(e in n)n.hasOwnProperty(e)&&(t[e]=n[e]);return t.queue=[],t.sorter=i.sorter||n.default_sorter,t},default_sorter:function(i,n){return i.cost-n.cost},push:function(i,n){var t={value:i,cost:n};this.queue.push(t),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};r.exports=o}(O)),O.exports}var Wt;function He(){return Wt||(Wt=1,function(r){const o=F(),i=Ue(),n=Fe(),t=ke(),e=ze(),s=ie(),a=U(),u=Ve();function c(T){return unescape(encodeURIComponent(T)).length}function l(T,M,p){const B=[];let b;for(;(b=T.exec(p))!==null;)B.push({data:b[0],index:b.index,mode:M,length:b[0].length});return B}function d(T){const M=l(s.NUMERIC,o.NUMERIC,T),p=l(s.ALPHANUMERIC,o.ALPHANUMERIC,T);let B,b;return a.isKanjiModeEnabled()?(B=l(s.BYTE,o.BYTE,T),b=l(s.KANJI,o.KANJI,T)):(B=l(s.BYTE_KANJI,o.BYTE,T),b=[]),M.concat(p,B,b).sort(function(R,y){return R.index-y.index}).map(function(R){return{data:R.data,mode:R.mode,length:R.length}})}function g(T,M){switch(M){case o.NUMERIC:return i.getBitsLength(T);case o.ALPHANUMERIC:return n.getBitsLength(T);case o.KANJI:return e.getBitsLength(T);case o.BYTE:return t.getBitsLength(T)}}function f(T){return T.reduce(function(M,p){const B=M.length-1>=0?M[M.length-1]:null;return B&&B.mode===p.mode?(M[M.length-1].data+=p.data,M):(M.push(p),M)},[])}function w(T){const M=[];for(let p=0;p<T.length;p++){const B=T[p];switch(B.mode){case o.NUMERIC:M.push([B,{data:B.data,mode:o.ALPHANUMERIC,length:B.length},{data:B.data,mode:o.BYTE,length:B.length}]);break;case o.ALPHANUMERIC:M.push([B,{data:B.data,mode:o.BYTE,length:B.length}]);break;case o.KANJI:M.push([B,{data:B.data,mode:o.BYTE,length:c(B.data)}]);break;case o.BYTE:M.push([{data:B.data,mode:o.BYTE,length:c(B.data)}])}}return M}function S(T,M){const p={},B={start:{}};let b=["start"];for(let m=0;m<T.length;m++){const R=T[m],y=[];for(let h=0;h<R.length;h++){const I=R[h],C=""+m+h;y.push(C),p[C]={node:I,lastCount:0},B[C]={};for(let A=0;A<b.length;A++){const E=b[A];p[E]&&p[E].node.mode===I.mode?(B[E][C]=g(p[E].lastCount+I.length,I.mode)-g(p[E].lastCount,I.mode),p[E].lastCount+=I.length):(p[E]&&(p[E].lastCount=I.length),B[E][C]=g(I.length,I.mode)+4+o.getCharCountIndicator(I.mode,M))}}b=y}for(let m=0;m<b.length;m++)B[b[m]].end=0;return{map:B,table:p}}function N(T,M){let p;const B=o.getBestModeForData(T);if(p=o.from(M,B),p!==o.BYTE&&p.bit<B.bit)throw new Error('"'+T+'" cannot be encoded with mode '+o.toString(p)+`.
 Suggested mode is: `+o.toString(B));switch(p===o.KANJI&&!a.isKanjiModeEnabled()&&(p=o.BYTE),p){case o.NUMERIC:return new i(T);case o.ALPHANUMERIC:return new n(T);case o.KANJI:return new e(T);case o.BYTE:return new t(T)}}r.fromArray=function(M){return M.reduce(function(p,B){return typeof B=="string"?p.push(N(B,null)):B.data&&p.push(N(B.data,B.mode)),p},[])},r.fromString=function(M,p){const B=d(M,a.isKanjiModeEnabled()),b=w(B),m=S(b,p),R=u.find_path(m.map,"start","end"),y=[];for(let h=1;h<R.length-1;h++)y.push(m.table[R[h]].node);return r.fromArray(f(y))},r.rawSplit=function(M){return r.fromArray(d(M,a.isKanjiModeEnabled()))}}(lt)),lt}var Xt;function Ke(){if(Xt)return X;Xt=1;const r=U(),o=Et(),i=Se(),n=Me(),t=Pe(),e=Ne(),s=be(),a=re(),u=De(),c=qe(),l=_e(),d=F(),g=He();function f(m,R){const y=m.size,h=e.getPositions(R);for(let I=0;I<h.length;I++){const C=h[I][0],A=h[I][1];for(let E=-1;E<=7;E++)if(!(C+E<=-1||y<=C+E))for(let P=-1;P<=7;P++)A+P<=-1||y<=A+P||(E>=0&&E<=6&&(P===0||P===6)||P>=0&&P<=6&&(E===0||E===6)||E>=2&&E<=4&&P>=2&&P<=4?m.set(C+E,A+P,!0,!0):m.set(C+E,A+P,!1,!0))}}function w(m){const R=m.size;for(let y=8;y<R-8;y++){const h=y%2===0;m.set(y,6,h,!0),m.set(6,y,h,!0)}}function S(m,R){const y=t.getPositions(R);for(let h=0;h<y.length;h++){const I=y[h][0],C=y[h][1];for(let A=-2;A<=2;A++)for(let E=-2;E<=2;E++)A===-2||A===2||E===-2||E===2||A===0&&E===0?m.set(I+A,C+E,!0,!0):m.set(I+A,C+E,!1,!0)}}function N(m,R){const y=m.size,h=c.getEncodedBits(R);let I,C,A;for(let E=0;E<18;E++)I=Math.floor(E/3),C=E%3+y-8-3,A=(h>>E&1)===1,m.set(I,C,A,!0),m.set(C,I,A,!0)}function T(m,R,y){const h=m.size,I=l.getEncodedBits(R,y);let C,A;for(C=0;C<15;C++)A=(I>>C&1)===1,C<6?m.set(C,8,A,!0):C<8?m.set(C+1,8,A,!0):m.set(h-15+C,8,A,!0),C<8?m.set(8,h-C-1,A,!0):C<9?m.set(8,15-C-1+1,A,!0):m.set(8,15-C-1,A,!0);m.set(h-8,8,1,!0)}function M(m,R){const y=m.size;let h=-1,I=y-1,C=7,A=0;for(let E=y-1;E>0;E-=2)for(E===6&&E--;;){for(let P=0;P<2;P++)if(!m.isReserved(I,E-P)){let q=!1;A<R.length&&(q=(R[A]>>>C&1)===1),m.set(I,E-P,q),C--,C===-1&&(A++,C=7)}if(I+=h,I<0||y<=I){I-=h,h=-h;break}}}function p(m,R,y){const h=new i;y.forEach(function(P){h.put(P.mode.bit,4),h.put(P.getLength(),d.getCharCountIndicator(P.mode,m)),P.write(h)});const I=r.getSymbolTotalCodewords(m),C=a.getTotalCodewordsCount(m,R),A=(I-C)*8;for(h.getLengthInBits()+4<=A&&h.put(0,4);h.getLengthInBits()%8!==0;)h.putBit(0);const E=(A-h.getLengthInBits())/8;for(let P=0;P<E;P++)h.put(P%2?17:236,8);return B(h,m,R)}function B(m,R,y){const h=r.getSymbolTotalCodewords(R),I=a.getTotalCodewordsCount(R,y),C=h-I,A=a.getBlocksCount(R,y),E=h%A,P=A-E,q=Math.floor(h/A),V=Math.floor(C/A),ue=V+1,pt=q-V,ae=new u(pt);let j=0;const K=new Array(A),Bt=new Array(A);let Y=0;const ce=new Uint8Array(m.buffer);for(let k=0;k<A;k++){const G=k<P?V:ue;K[k]=ce.slice(j,j+G),Bt[k]=ae.encode(K[k]),j+=G,Y=Math.max(Y,G)}const Q=new Uint8Array(h);let Rt=0,v,D;for(v=0;v<Y;v++)for(D=0;D<A;D++)v<K[D].length&&(Q[Rt++]=K[D][v]);for(v=0;v<pt;v++)for(D=0;D<A;D++)Q[Rt++]=Bt[D][v];return Q}function b(m,R,y,h){let I;if(Array.isArray(m))I=g.fromArray(m);else if(typeof m=="string"){let q=R;if(!q){const V=g.rawSplit(m);q=c.getBestVersionForData(V,y)}I=g.fromString(m,q||40)}else throw new Error("Invalid data");const C=c.getBestVersionForData(I,y);if(!C)throw new Error("The amount of data is too big to be stored in a QR Code");if(!R)R=C;else if(R<C)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+C+`.
`);const A=p(R,y,I),E=r.getSymbolSize(R),P=new n(E);return f(P,R),w(P),S(P,R),T(P,y,0),R>=7&&N(P,R),M(P,A),isNaN(h)&&(h=s.getBestMask(P,T.bind(null,P,y))),s.applyMask(h,P),T(P,y,h),{modules:P,version:R,errorCorrectionLevel:y,maskPattern:h,segments:I}}return X.create=function(R,y){if(typeof R=="undefined"||R==="")throw new Error("No input text");let h=o.M,I,C;return typeof y!="undefined"&&(h=o.from(y.errorCorrectionLevel,o.M),I=c.from(y.version),C=s.from(y.maskPattern),y.toSJISFunc&&r.setToSJISFunction(y.toSJISFunc)),b(R,I,h,C)},X}var mt={},wt={},Zt;function se(){return Zt||(Zt=1,function(r){function o(i){if(typeof i=="number"&&(i=i.toString()),typeof i!="string")throw new Error("Color should be defined as hex string");let n=i.slice().replace("#","").split("");if(n.length<3||n.length===5||n.length>8)throw new Error("Invalid hex color: "+i);(n.length===3||n.length===4)&&(n=Array.prototype.concat.apply([],n.map(function(e){return[e,e]}))),n.length===6&&n.push("F","F");const t=parseInt(n.join(""),16);return{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:t&255,hex:"#"+n.slice(0,6).join("")}}r.getOptions=function(n){n||(n={}),n.color||(n.color={});const t=typeof n.margin=="undefined"||n.margin===null||n.margin<0?4:n.margin,e=n.width&&n.width>=21?n.width:void 0,s=n.scale||4;return{width:e,scale:e?4:s,margin:t,color:{dark:o(n.color.dark||"#000000ff"),light:o(n.color.light||"#ffffffff")},type:n.type,rendererOpts:n.rendererOpts||{}}},r.getScale=function(n,t){return t.width&&t.width>=n+t.margin*2?t.width/(n+t.margin*2):t.scale},r.getImageWidth=function(n,t){const e=r.getScale(n,t);return Math.floor((n+t.margin*2)*e)},r.qrToImageData=function(n,t,e){const s=t.modules.size,a=t.modules.data,u=r.getScale(s,e),c=Math.floor((s+e.margin*2)*u),l=e.margin*u,d=[e.color.light,e.color.dark];for(let g=0;g<c;g++)for(let f=0;f<c;f++){let w=(g*c+f)*4,S=e.color.light;if(g>=l&&f>=l&&g<c-l&&f<c-l){const N=Math.floor((g-l)/u),T=Math.floor((f-l)/u);S=d[a[N*s+T]?1:0]}n[w++]=S.r,n[w++]=S.g,n[w++]=S.b,n[w]=S.a}}}(wt)),wt}var xt;function Je(){return xt||(xt=1,function(r){const o=se();function i(t,e,s){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=s,e.width=s,e.style.height=s+"px",e.style.width=s+"px"}function n(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}r.render=function(e,s,a){let u=a,c=s;typeof u=="undefined"&&(!s||!s.getContext)&&(u=s,s=void 0),s||(c=n()),u=o.getOptions(u);const l=o.getImageWidth(e.modules.size,u),d=c.getContext("2d"),g=d.createImageData(l,l);return o.qrToImageData(g.data,e,u),i(d,c,l),d.putImageData(g,0,0),c},r.renderToDataURL=function(e,s,a){let u=a;typeof u=="undefined"&&(!s||!s.getContext)&&(u=s,s=void 0),u||(u={});const c=r.render(e,s,u),l=u.type||"image/png",d=u.rendererOpts||{};return c.toDataURL(l,d.quality)}}(mt)),mt}var Ct={},te;function Oe(){if(te)return Ct;te=1;const r=se();function o(t,e){const s=t.a/255,a=e+'="'+t.hex+'"';return s<1?a+" "+e+'-opacity="'+s.toFixed(2).slice(1)+'"':a}function i(t,e,s){let a=t+e;return typeof s!="undefined"&&(a+=" "+s),a}function n(t,e,s){let a="",u=0,c=!1,l=0;for(let d=0;d<t.length;d++){const g=Math.floor(d%e),f=Math.floor(d/e);!g&&!c&&(c=!0),t[d]?(l++,d>0&&g>0&&t[d-1]||(a+=c?i("M",g+s,.5+f+s):i("m",u,0),u=0,c=!1),g+1<e&&t[d+1]||(a+=i("h",l),l=0)):u++}return a}return Ct.render=function(e,s,a){const u=r.getOptions(s),c=e.modules.size,l=e.modules.data,d=c+u.margin*2,g=u.color.light.a?"<path "+o(u.color.light,"fill")+' d="M0 0h'+d+"v"+d+'H0z"/>':"",f="<path "+o(u.color.dark,"stroke")+' d="'+n(l,c,u.margin)+'"/>',w='viewBox="0 0 '+d+" "+d+'"',N='<svg xmlns="http://www.w3.org/2000/svg" '+(u.width?'width="'+u.width+'" height="'+u.width+'" ':"")+w+' shape-rendering="crispEdges">'+g+f+`</svg>
`;return typeof a=="function"&&a(null,N),N},Ct}var ee;function je(){if(ee)return z;ee=1;const r=Ie(),o=Ke(),i=Je(),n=Oe();function t(e,s,a,u,c){const l=[].slice.call(arguments,1),d=l.length,g=typeof l[d-1]=="function";if(!g&&!r())throw new Error("Callback required as last argument");if(g){if(d<2)throw new Error("Too few arguments provided");d===2?(c=a,a=s,s=u=void 0):d===3&&(s.getContext&&typeof c=="undefined"?(c=u,u=void 0):(c=u,u=a,a=s,s=void 0))}else{if(d<1)throw new Error("Too few arguments provided");return d===1?(a=s,s=u=void 0):d===2&&!s.getContext&&(u=a,a=s,s=void 0),new Promise(function(f,w){try{const S=o.create(a,u);f(e(S,s,u))}catch(S){w(S)}})}try{const f=o.create(a,u);c(null,e(f,s,u))}catch(f){c(f)}}return z.create=o.create,z.toCanvas=t.bind(null,i.render),z.toDataURL=t.bind(null,i.renderToDataURL),z.toString=t.bind(null,function(e,s,a){return n.render(e,a)}),z}var yt=je();const Ye=({canvas:r,content:o,width:i=0,options:n={}})=>{const t=Ae(n);return t.errorCorrectionLevel=t.errorCorrectionLevel||Ge(o),Qe(o,t).then(e=>(t.scale=i===0?void 0:i/e*4,yt.toCanvas(r,o,t)))};function Qe(r,o){const i=document.createElement("canvas");return yt.toCanvas(i,r,o).then(()=>i.width)}function Ge(r){return r.length>36?"M":r.length>16?"Q":"H"}const $e=({canvas:r,logo:o})=>{if(!o)return new Promise(M=>{M(r.toDataURL())});const i=r.width,{logoSize:n=.15,bgColor:t="#ffffff",borderSize:e=.05,crossOrigin:s,borderRadius:a=8,logoRadius:u=0}=o,c=ge(o)?o:o.src,l=i*n,d=i*(1-n)/2,g=i*(n+e),f=i*(1-n-e)/2,w=r.getContext("2d");if(!w)return;ne(w)(f,f,g,g,a),w.fillStyle=t,w.fill();const S=new Image;(s||u)&&S.setAttribute("crossOrigin",s||"anonymous"),S.src=c;const N=M=>{w.drawImage(M,d,d,l,l)},T=M=>{const p=document.createElement("canvas");p.width=d+l,p.height=d+l;const B=p.getContext("2d");if(!B||!w||(B.drawImage(M,d,d,l,l),ne(w)(d,d,l,l,u),!w))return;const b=w.createPattern(p,"no-repeat");b&&(w.fillStyle=b,w.fill())};return new Promise(M=>{S.onload=()=>{u?T(S):N(S),M(r.toDataURL())}})};function ne(r){return(o,i,n,t,e)=>{const s=Math.min(n,t);return e>s/2&&(e=s/2),r.beginPath(),r.moveTo(o+e,i),r.arcTo(o+n,i,o+n,i+t,e),r.arcTo(o+n,i+t,o,i+t,e),r.arcTo(o,i+t,o,i,e),r.arcTo(o,i,o+n,i,e),r.closePath(),r}}const We=r=>Ye(r).then(()=>r).then($e),Xe=we({name:"QrCode",props:{value:{type:[String,Array],default:null},options:{type:Object,default:null},width:{type:Number,default:200},logo:{type:[String,Object],default:""},tag:{type:String,default:"canvas",validator:r=>["canvas","img"].includes(r)}},emits:{done:r=>!!r,error:r=>!!r},setup(r,{emit:o}){const i=Ce(null);function n(){return St(this,null,function*(){try{const{tag:e,value:s,options:a={},width:u,logo:c}=r,l=String(s),d=$(i);if(l==null||l===""||!d)return;if(e==="canvas"){const g=yield We({canvas:d,width:u,logo:c,content:l,options:a||{}});o("done",{url:g,ctx:d.getContext("2d")});return}if(e==="img"){const g=yield yt.toDataURL(l,It({errorCorrectionLevel:"H",width:u},a));$(i).src=g,o("done",{url:g})}}catch(e){o("error",e)}})}function t(e){let s="";const a=$(i);a instanceof HTMLCanvasElement?s=a.toDataURL():a instanceof HTMLImageElement&&(s=a.src),s&&Te({url:s,fileName:e})}return Ee(n),ye(r,()=>{n()},{deep:!0}),{wrapRef:i,download:t}}});function Ze(r,o,i,n,t,e){return Mt(),pe("div",null,[(Mt(),Be(Re(r.tag),{ref:"wrapRef"},null,512))])}const xe=he(Xe,[["render",Ze]]),an=me(xe);export{an as Q};
