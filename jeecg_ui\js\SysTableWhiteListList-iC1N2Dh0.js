var W=Object.defineProperty,K=Object.defineProperties;var O=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var P=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var x=(e,o,t)=>o in e?W(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,D=(e,o)=>{for(var t in o||(o={}))P.call(o,t)&&x(e,t,o[t]);if(y)for(var t of y(o))X.call(o,t)&&x(e,t,o[t]);return e},S=(e,o)=>K(e,O(o));var d=(e,o,t)=>new Promise((_,c)=>{var b=n=>{try{l(t.next(n))}catch(s){c(s)}},g=n=>{try{l(t.throw(n))}catch(s){c(s)}},l=n=>n.done?_(n.value):Promise.resolve(n.value).then(b,g);l((t=t.apply(e,o)).next())});import{d as A,ag as u,aq as q,ar as v,k as r,aD as a,u as p,aB as z,ah as G,G as f}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{useListPage as H}from"./useListPage-Soxgnx9a.js";import{S as Q,b as j,g as J,a as Y,s as Z,c as $,l as tt,d as ot}from"./SysTableWhiteListModal-o2PvwyOr.js";import{ad as et}from"./index-CCWaWN5g.js";import{Q as it}from"./componentMap-Bkie1n3v.js";import nt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const rt=A({name:"ahh-sysTableWhiteList"}),co=A(S(D({},rt),{setup(e){const[o,{openModal:t}]=et(),{prefixCls:_,tableContext:c,onExportXls:b,onImportXls:g}=H({tableProps:{title:"系统表白名单",api:tt,columns:$,canResize:!1,formConfig:{labelWidth:120,schemas:Z,autoSubmitOnEnter:!0,showAdvancedButton:!0},actionColumn:{width:120}},exportConfig:{name:"系统表白名单",url:Y},importConfig:{url:J}}),[l,{reload:n},{rowSelection:s,selectedRowKeys:h}]=c;function R(){t(!0,{isUpdate:!1,showFooter:!0})}function T(i){t(!0,{record:i,isUpdate:!0,showFooter:!0})}function U(i){t(!0,{record:i,isUpdate:!0,showFooter:!1})}function B(i){return d(this,null,function*(){yield ot({id:i.id},n)})}function E(){return d(this,null,function*(){yield j({ids:h.value},n)})}function I({isUpdate:i,values:m}){n()}function F(i){return[{label:"编辑",onClick:T.bind(null,i)}]}function M(i){return[{label:"详情",onClick:U.bind(null,i)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:B.bind(null,i)}}]}return(i,m)=>{const w=u("a-button"),C=u("Icon"),L=u("a-menu-item"),N=u("a-menu"),V=u("a-dropdown");return v(),q("div",null,[r(p(nt),{onRegister:p(l),rowSelection:p(s)},{tableTitle:a(()=>[r(w,{type:"primary",onClick:R,preIcon:"ant-design:plus-outlined"},{default:a(()=>m[0]||(m[0]=[f(" 新增 ")])),_:1,__:[0]}),p(h).length>0?(v(),z(V,{key:0},{overlay:a(()=>[r(N,null,{default:a(()=>[r(L,{key:"1",onClick:E},{default:a(()=>[r(C,{icon:"ant-design:delete-outlined"}),m[1]||(m[1]=f(" 删除 "))]),_:1,__:[1]})]),_:1})]),default:a(()=>[r(w,null,{default:a(()=>[m[2]||(m[2]=f(" 批量操作 ")),r(C,{icon:"mdi:chevron-down"})]),_:1,__:[2]})]),_:1})):G("",!0)]),action:a(({record:k})=>[r(p(it),{actions:F(k),dropDownActions:M(k)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),r(Q,{onRegister:p(o),onSuccess:I},null,8,["onRegister"])])}}}));export{co as default};
