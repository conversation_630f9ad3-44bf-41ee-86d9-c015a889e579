var A=Object.defineProperty,E=Object.defineProperties;var L=Object.getOwnPropertyDescriptors;var w=Object.getOwnPropertySymbols;var U=Object.prototype.hasOwnProperty,q=Object.prototype.propertyIsEnumerable;var S=(e,o,t)=>o in e?A(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,M=(e,o)=>{for(var t in o||(o={}))U.call(o,t)&&S(e,t,o[t]);if(w)for(var t of w(o))q.call(o,t)&&S(e,t,o[t]);return e},D=(e,o)=>E(e,L(o));var C=(e,o,t)=>new Promise((l,c)=>{var _=n=>{try{u(t.next(n))}catch(a){c(a)}},g=n=>{try{u(t.throw(n))}catch(a){c(a)}},u=n=>n.done?l(n.value):Promise.resolve(n.value).then(_,g);u((t=t.apply(e,o)).next())});import{d as I,f as F,ag as h,aq as G,ar as b,k as m,aD as s,u as p,aB as v,ah as B,G as f}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{c as Q,b as j,g as z}from"./route.api-DBvzaHKw.js";import{_ as H,c as J}from"./RouteRecycleBinModal-Cfdu2d5f.js";import O from"./RouteModal-CWAH-CQB.js";import{ad as W,u as X}from"./index-CCWaWN5g.js";import{a as Y}from"./index-JbqXEynz.js";import{useListPage as Z}from"./useListPage-Soxgnx9a.js";import{Q as tt}from"./componentMap-Bkie1n3v.js";import ot from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const et={class:"p-4"},rt=I({name:"monitor-route"}),yo=I(D(M({},rt),{setup(e){const{createMessage:o}=X(),[t,{openDrawer:l}]=Y(),c=F([]),[_,{openModal:g}]=W(),{prefixCls:u,tableContext:n}=Z({designScope:"router-template",tableProps:{title:"路由列表",api:z,useSearchForm:!1,columns:J}}),[a,{reload:d},{rowSelection:it,selectedRowKeys:nt}]=n,K={dataIndex:"index",width:"15px"};function N(r){return[{label:"编辑",onClick:T.bind(null,r)},{label:"复制",popConfirm:{title:"是否确认复制",confirm:V.bind(null,r)}},{label:"删除",popConfirm:{title:"是否确认删除",confirm:$.bind(null,r)}}]}function pt(r){c.value=r}function P(){l(!0,{isUpdate:!1})}function T(r){l(!0,{record:r,isUpdate:!0})}function V(r){return C(this,null,function*(){yield Q({id:r.id},d),o.success("复制成功")})}function $(r){return C(this,null,function*(){yield j({id:r.id},d)})}return(r,i)=>{const R=h("a-button"),k=h("a-tag");return b(),G("div",et,[m(p(ot),{onRegister:p(a),indexColumnProps:K},{tableTitle:s(()=>[m(R,{preIcon:"ant-design:plus-outlined",type:"primary",onClick:P,style:{"margin-right":"5px"}},{default:s(()=>i[1]||(i[1]=[f("新增")])),_:1,__:[1]}),m(R,{type:"primary",onClick:i[0]||(i[0]=y=>p(g)(!0)),preIcon:"ant-design:hdd-outlined"},{default:s(()=>i[2]||(i[2]=[f(" 回收站")])),_:1,__:[2]})]),status:s(({record:y,text:x})=>[x==0?(b(),v(k,{key:0,color:"pink"},{default:s(()=>i[3]||(i[3]=[f("禁用")])),_:1,__:[3]})):B("",!0),x==1?(b(),v(k,{key:1,color:"#87d068"},{default:s(()=>i[4]||(i[4]=[f("正常")])),_:1,__:[4]})):B("",!0)]),action:s(({record:y})=>[m(p(tt),{actions:N(y)},null,8,["actions"])]),_:1},8,["onRegister"]),m(O,{onRegister:p(t),onSuccess:p(d)},null,8,["onRegister","onSuccess"]),m(H,{onRegister:p(_),onSuccess:p(d)},null,8,["onRegister","onSuccess"])])}}}));export{yo as default};
