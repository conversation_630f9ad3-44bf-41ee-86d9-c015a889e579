import{d as p,o as u,u as m}from"./vue-vendor-dy9k-Yad.js";import{connectWebSocket as d,onWebSocket as f}from"./useWebSocket-B-g1Ud0G.js";import{b5 as l,ah as _,aZ as b,ci as k,a as S}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const g=p({setup(){const t=l(),o=_();u(()=>{s()});function s(){var c;let e=b(),r=k(e),i=m(o.getUserInfo).id+"_"+r,a=((c=t.domainUrl)==null?void 0:c.replace("https://","wss://").replace("http://","ws://"))+"/websocketDemo/"+i;d(a),f(n)}function n(e){(e.cmd==="topic"||e.cmd==="user")&&setTimeout(()=>{},1e3)}}});function h(t,o,s,n,e,r){return null}const U=S(g,[["render",h]]);export{U as default};
