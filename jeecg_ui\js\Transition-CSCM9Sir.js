import{d as i,ag as t,aB as m,ar as c,aD as a,at as o,k as r}from"./vue-vendor-dy9k-Yad.js";import _ from"./TargetContent-BoSrkmuP.js";import{L as d}from"./index-LCGLvkB3.js";import{P as f}from"./index-CtJ0w2CP.js";import{a as l}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const C=i({components:{LazyContainer:d,TargetContent:_,PageWrapper:f}}),u={class:"lazy-base-demo-wrap"},g={class:"lazy-base-demo-box"};function x(y,e,z,B,L,P){const n=t("TargetContent"),s=t("LazyContainer"),p=t("PageWrapper");return c(),m(p,{title:"懒加载自定义动画示例",content:"懒加载组件显示动画"},{default:a(()=>[o("div",u,[e[0]||(e[0]=o("h1",null,"向下滚动",-1)),o("div",g,[r(s,{transitionName:"custom"},{default:a(()=>[r(n)]),_:1})])])]),_:1})}const H=l(C,[["render",x]]);export{H as default};
