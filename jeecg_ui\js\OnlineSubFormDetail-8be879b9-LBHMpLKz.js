import{cs as S,u as g,j as F,bj as y}from"./index-CCWaWN5g.js";import{f as c,w as f,ag as B,aB as w,ar as P}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{m as D,g as j}from"./useExtendComponent-bb98e568-B7LlULaY.js";import x from"./DetailForm-c592b8d8-BIx7KBmJ.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./index-mbACBRQ9.js";import{B as I}from"./BasicForm-DBcXiHk0.js";import"./vxe-table-vendor-B22HppNm.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";var h=(t,e,i)=>new Promise((r,p)=>{var n=o=>{try{s(i.next(o))}catch(m){p(m)}},a=o=>{try{s(i.throw(o))}catch(m){p(m)}},s=o=>o.done?r(o.value):Promise.resolve(o.value).then(n,a);s((i=i.apply(t,e)).next())});const O="/online/cgform/api/subform",T={name:"OnlineSubFormDetail",components:{BasicForm:I,Loading:y,DetailForm:x},props:{properties:{type:Object,required:!0},mainId:{type:String,default:""},table:{type:String,default:""},formTemplate:{type:Number,default:1}},emits:["formChange"],setup(t){const e=c(!1);g();const i=c(""),r=c({}),{detailFormSchemas:p,createFormSchemas:n,formSpan:a}=D(t);f(()=>t.table,()=>{i.value=t.table},{immediate:!0}),f(()=>t.properties,()=>{e.value=!1,n(t.properties),e.value=!0},{deep:!0,immediate:!0}),f(()=>t.mainId,()=>{setTimeout(()=>{s()},100)},{immediate:!0});function s(){return h(this,null,function*(){yield j(e),r.value={};const{table:m,mainId:l}=t;!m||!l||(r.value=(yield o(m,l))||{})})}function o(m,l){return h(this,null,function*(){let b=`${O}/${m}/${l}`;return new Promise((d,v)=>{F.get({url:b},{isTransformResponse:!1}).then(u=>{u.success?d(u.result):v(u.message)})}).catch(d=>Promise.resolve({}))})}return{detailFormSchemas:p,subFormData:r,formSpan:a}}};function $(t,e,i,r,p,n){const a=B("detail-form");return P(),w(a,{schemas:r.detailFormSchemas,data:r.subFormData,span:r.formSpan},null,8,["schemas","data","span"])}const Jt=S(T,[["render",$]]);export{Jt as default};
