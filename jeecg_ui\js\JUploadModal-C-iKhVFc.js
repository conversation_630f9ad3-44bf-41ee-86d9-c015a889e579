var g=(o,u,t)=>new Promise((n,d)=>{var p=e=>{try{r(t.next(e))}catch(i){d(i)}},s=e=>{try{r(t.throw(e))}catch(i){d(i)}},r=e=>e.done?n(e.value):Promise.resolve(e.value).then(p,s);r((t=t.apply(o,u)).next())});import{d as v,f as k,r as M,e as _,aB as B,ar as w,aD as R,k as x,aE as h,u as m,n as C}from"./vue-vendor-dy9k-Yad.js";import{B as O}from"./index-Diw57m_E.js";import{H as l,ac as U,u as E}from"./index-CCWaWN5g.js";import{U as c,_ as V}from"./JUpload-CRos0F1P.js";const I=v({__name:"JUploadModal",props:{value:l.oneOfType([l.string,l.array]),width:l.number.def(520)},emits:["change","update:value","register"],setup(o,{emit:u}){const{createMessage:t}=E(),n=u,d=o,p=k(),s=M({props:{}}),r=_(()=>{var a;return((a=s.props)==null?void 0:a.fileType)===c.image?"图片上传":"文件上传"}),[e,{closeModal:i}]=U(a=>g(null,null,function*(){var f;s.props=m(a)||{},[c.image,"img","picture"].includes((f=s.props)==null?void 0:f.fileType)?s.props.fileType=c.image:s.props.fileType=c.file,C(()=>p.value.addActionsListener())}));function y(){i()}function T(a){n("change",a),n("update:value",a)}return(a,f)=>(w(),B(m(O),h({onRegister:m(e),title:r.value,width:o.width,onOk:y},a.$attrs),{default:R(()=>[x(V,h({ref_key:"uploadRef",ref:p,value:o.value},s.props,{onChange:T}),null,16,["value"])]),_:1},16,["onRegister","title","width"]))}});export{I as _};
