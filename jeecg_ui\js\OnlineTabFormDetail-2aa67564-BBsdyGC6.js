import{cs as G,u as Z,ap as X,j as Y,aZ as ee,cx as te,bj as re}from"./index-CCWaWN5g.js";import{f as m,w as oe,r as ae,ag as u,aq as g,ar as s,ah as N,k as S,aG as ie,aD as D,aB as w,F as ne,aC as le,aA as pe}from"./vue-vendor-dy9k-Yad.js";import{c7 as me}from"./antd-vue-vendor-me9YkNVC.js";import se from"./DetailForm-c592b8d8-BIx7KBmJ.js";import ue from"./OnlineSubFormDetail-8be879b9-LBHMpLKz.js";import{m as ce}from"./useExtendComponent-bb98e568-B7LlULaY.js";import{t as de,s as fe}from"./constant-fa63bd66-Ddbq-fz2.js";import"./index-mbACBRQ9.js";import"./componentMap-Bkie1n3v.js";import"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./index-B4ez5KWV.js";import"./user.api-mLAlJze4.js";import"./customExpression-BHJdu2h2.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./LinkTableListPiece-e016b8e6-D0dAdZNm.js";import"./OnlineSelectCascade-d631ed72-DF6fP885.js";import"./JModalTip-a927f85d-DAi05z-f.js";import"./vxe-table-vendor-B22HppNm.js";import"./JUpload-CRos0F1P.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";var be=Object.defineProperty,A=Object.getOwnPropertySymbols,he=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable,I=(n,a,o)=>a in n?be(n,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[a]=o,ge=(n,a)=>{for(var o in a||(a={}))he.call(a,o)&&I(n,o,a[o]);if(A)for(var o of A(a))ye.call(a,o)&&I(n,o,a[o]);return n},B=(n,a,o)=>new Promise((r,b)=>{var v=l=>{try{c(o.next(l))}catch(d){b(d)}},y=l=>{try{c(o.throw(l))}catch(d){b(d)}},c=l=>l.done?r(l.value):Promise.resolve(l.value).then(v,y);c((o=o.apply(n,a)).next())});const Se={name:"OnlineTabFormDetail",components:{DetailForm:se,Loading:re,PrinterOutlined:me,OnlineSubFormDetail:ue},props:{id:{type:String,default:""},formTemplate:{type:Number,default:1},disabled:{type:Boolean,default:!1},isTree:{type:Boolean,default:!1},pidField:{type:String,default:""},submitTip:{type:Boolean,default:!0},showSub:{type:Boolean,default:!0},themeTemplate:{type:String,default:""},tabIndex:{type:String,default:""}},emits:["success","rendered"],setup(n,{emit:a}){const{createMessage:o}=Z(),{getIsMobile:r}=X(),b=m(""),v=m(!0),y=m(!1),c=m(1),l=m({}),d=m("auto"),k=m(340),T=m("0"),P=m(!r.value);oe(()=>n.tabIndex,(e,t)=>{T.value=e,t&&K()},{immediate:!0});const h=ae({reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:""}),{detailFormSchemas:p,hasSubTable:x,subTabInfo:M,refMap:R,showStatus:F,subDataSource:C,createFormSchemas:_,formSpan:J}=ce(n);function U(e){let t={reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:1,modalMinWidth:""};e&&(t=JSON.parse(e)),Object.keys(t).map(i=>{h[i]=t[i]})}function V(e){return B(this,null,function*(){c.value=e.head.tableType,b.value=e.head.tableName,v.value=e.head.tableType==1,U(e.head.extConfigJson),_(e.schema.properties),a("rendered",h)})}function E(e,t){return B(this,null,function*(){yield L(t),O(!0)})}function H(e){let t=`/online/cgform/api/detail/${n.id}/${e}`;return new Promise((i,f)=>{Y.get({url:t},{isTransformResponse:!1}).then(j=>{j.success?i(j.result):(f(),o.warning(j.message))}).catch(()=>{f()})})}function O(e){Object.keys(F).map(t=>{F[t]=e})}function K(){O(!1),setTimeout(()=>{O(!0)},300)}function L(e){return B(this,null,function*(){let t=yield H(e.id);l.value=ge({},t),z(t)})}function z(e){e||(e={});let t=Object.keys(C.value);if(t&&t.length>0){let i={};for(let f of t)i[f]=e[f]||[];C.value=i}}function $(e){return"online_"+e+":"}function Q(){let e=h.reportPrintUrl,t=l.value;if(t){let i=t.id,f=ee();te(e,i,f)}}function W(e){let t=l.value;return q(t,e)}function q(e,t){if(e){let i=e[t];return!i&&i!==0&&(i=e[t.toLowerCase()],!i&&i!==0&&(i=e[t.toUpperCase()])),i}return""}return{detailFormSchemas:p,formData:l,formSpan:J,tableName:b,loading:y,hasSubTable:x,subTabInfo:M,subFormHeight:d,subTableHeight:k,refMap:R,onTabChange:K,subDataSource:C,getSubTableAuthPre:$,show:E,createRootProperties:V,onOpenReportPrint:Q,onlineExtConfigJson:h,getSubTableForeignKeyValue:W,showStatus:F,ERP:fe,TAB:de,subActiveKey:T,rowNumber:P}}},ve=["id"],Te={key:0,style:{"text-align":"right",position:"absolute",top:"15px",right:"20px","z-index":"999"}},we={key:1};function ke(n,a,o,r,b,v){const y=u("PrinterOutlined"),c=u("detail-form"),l=u("a-tab-pane"),d=u("online-sub-form-detail"),k=u("JVxeTable"),T=u("a-spin"),P=u("a-tabs"),h=u("Loading");return s(),g("div",{id:r.tableName+"_form"},[r.formData.id&&r.onlineExtConfigJson.reportPrintShow?(s(),g("div",Te,[S(y,{title:"打印",onClick:r.onOpenReportPrint,style:{"font-size":"16px"}},null,8,["onClick"])])):N("",!0),S(P,{class:"tabTheme",onChange:r.onTabChange,activeKey:r.subActiveKey,"onUpdate:activeKey":a[0]||(a[0]=p=>r.subActiveKey=p)},{default:D(()=>[(s(),w(l,{tab:"主表",key:"-1"},{default:D(()=>[S(c,{schemas:r.detailFormSchemas,data:r.formData,span:r.formSpan},null,8,["schemas","data","span"])]),_:1})),r.hasSubTable&&o.showSub?(s(!0),g(ne,{key:0},le(r.subTabInfo,(p,x)=>(s(),w(l,{tab:p.describe,key:x+"",forceRender:!0},{default:D(()=>[p.relationType==1?(s(),g("div",{key:0,style:pe({"overflow-y":"auto","overflow-x":"hidden","max-height":r.subFormHeight+"px"})},[S(d,{table:p.key,"form-template":o.formTemplate,"main-id":r.getSubTableForeignKeyValue(p.foreignKey),properties:p.properties},null,8,["table","form-template","main-id","properties"])],4)):(s(),g("div",we,[r.showStatus[p.key]?(s(),w(k,{key:0,ref_for:!0,ref:r.refMap[p.key],toolbar:"","keep-source":"","row-number":r.rowNumber,"row-selection":"",height:r.subTableHeight,disabled:!0,columns:p.columns,dataSource:r.subDataSource[p.key],authPre:r.getSubTableAuthPre(p.key)},null,8,["row-number","height","columns","dataSource","authPre"])):(s(),w(T,{key:1,spinning:!0}))]))]),_:2},1032,["tab"]))),128)):N("",!0)]),_:1},8,["onChange","activeKey"]),S(h,{loading:r.loading,absolute:!1},null,8,["loading"]),ie(n.$slots,"bottom",{},void 0,!0)],8,ve)}const Jt=G(Se,[["render",ke],["__scopeId","data-v-60a1e2da"]]);export{Jt as default};
