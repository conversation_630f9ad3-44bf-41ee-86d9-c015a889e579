import{d as r,e as d,ag as l,aq as c,ar as p,as as m,at as h,k as f,au as u,aE as C}from"./vue-vendor-dy9k-Yad.js";import{H as g}from"./antd-vue-vendor-me9YkNVC.js";import{F as k,N as b,a as y}from"./index-CCWaWN5g.js";import{b as B}from"./index-CI-8_pdX.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-JbqXEynz.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useHeaderSetting-C-h5S52e.js";import"./useMultipleTabSetting-QBbnIi9J.js";const S=r({name:"SwitchItem",components:{Switch:g},props:{event:{type:Number},disabled:{type:Boolean},title:{type:String},def:{type:Boolean}},setup(e){const{prefixCls:t}=k("setting-switch-item"),{t:n}=b(),a=d(()=>e.def?{checked:e.def}:{});function o(s){e.event&&B(e.event,s)}return{prefixCls:t,t:n,handleChange:o,getBindValue:a}}});function v(e,t,n,a,o,s){const i=l("Switch");return p(),c("div",{class:m(e.prefixCls)},[h("span",null,u(e.title),1),f(i,C(e.getBindValue,{onChange:e.handleChange,disabled:e.disabled,checkedChildren:e.t("layout.setting.on"),unCheckedChildren:e.t("layout.setting.off")}),null,16,["onChange","disabled","checkedChildren","unCheckedChildren"])],2)}const A=y(S,[["render",v],["__scopeId","data-v-c09ff5fc"]]);export{A as default};
