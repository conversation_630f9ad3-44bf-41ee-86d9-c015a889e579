import{u as n,j as l}from"./index-CCWaWN5g.js";const c=[{title:"收藏者用户ID",align:"center",dataIndex:"collectUserId"},{title:"收藏标的Id",align:"center",dataIndex:"itemId"},{title:"创建标的者Id",align:"center",dataIndex:"createUserId"},{title:"租户ID",align:"center",dataIndex:"tenantId"}],d=[],o=[{label:"收藏者用户ID",field:"collectUserId",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入收藏者用户ID!"}]},{label:"收藏标的Id",field:"itemId",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入收藏标的Id!"}]},{label:"创建标的者Id",field:"createUserId",component:"Input",dynamicRules:({model:e,schema:t})=>[{required:!0,message:"请输入创建标的者Id!"}]},{label:"租户ID",field:"tenantId",component:"InputNumber"},{label:"",field:"id",component:"Input",show:!1}],g={collectUserId:{title:"收藏者用户ID",order:0,view:"text",type:"string"},itemId:{title:"收藏标的Id",order:1,view:"text",type:"string"},createUserId:{title:"创建标的者Id",order:2,view:"text",type:"string"},tenantId:{title:"租户ID",order:3,view:"number",type:"number"}};function i(e){return o}const{createConfirm:a}=n();const h="/hgy/personalCenter/hgyCollectLog/exportXls",m="/hgy/personalCenter/hgyCollectLog/importExcel",p=e=>l.get({url:"/hgy/personalCenter/hgyCollectLog/list",params:e}),u=(e,t)=>l.delete({url:"/hgy/personalCenter/hgyCollectLog/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),y=(e,t)=>{a({iconType:"warning",title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>l.delete({url:"/hgy/personalCenter/hgyCollectLog/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},I=(e,t)=>{let r=t?"/hgy/personalCenter/hgyCollectLog/edit":"/hgy/personalCenter/hgyCollectLog/add";return l.post({url:r,params:e})};export{h as a,y as b,d as c,c as d,u as e,I as f,m as g,o as h,i,p as l,g as s};
