import{d as V,f as m,r as _,ag as i,aB as f,ar as v,aD as a,k as e,ah as b}from"./vue-vendor-dy9k-Yad.js";import{bx as s,j as k,u as z}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const q=V({__name:"PopupSubTable",setup(B){const{createMessage:h}=z(),r=m(!1),y=m([]),x=m([{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:s.input},{key:"call",title:"呼叫",width:"80px"},{key:"len",title:"长",width:"80px"},{key:"ton",title:"吨",width:"120px"},{key:"payer",title:"付款方",width:"120px"},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",showDetails:!0},{key:"trend",title:"动向",width:"120px"}]),U=m([]),d=_({currentRowId:null,loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:s.input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:s.input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:s.input},{key:"type",title:"船舶分类",width:"120px",type:s.input},{key:"port_area",title:"所属港区",minWidth:"120px",type:s.input}]}),S=_({span:4}),w=_({span:20}),D=_({num:[{required:!0,message:"必须输入序号"}]});C();function C(){r.value=!0,k.get({url:"/mock/vxe/getData",params:{pageNo:1,pageSize:30}}).then(p=>{y.value=p.records,U.value=[]}).finally(()=>{r.value=!1})}function R(p){return p?(d.currentRowId===p.id||(d.currentRowId=p.id,d.loading=!0,k.get({url:"/mock/vxe/getData",params:{pageNo:1,pageSize:30,parentId:p.id}}).then(c=>{d.dataSource=c.records}).finally(()=>{d.loading=!1})),!0):!1}function T({row:p,$table:c,callback:n}){c.validate(p).then(u=>{u?(n(!1),h.warn("校验失败")):(n(!0),r.value=!0,setTimeout(()=>{r.value=!1,h.success("保存成功")},1e3))})}return(p,c)=>{const n=i("a-input"),u=i("a-form-item"),o=i("a-col"),F=i("a-row"),I=i("a-form"),g=i("JVxeTable"),N=i("a-card");return v(),f(N,{title:"弹出子表示例",bordered:!1},{default:a(()=>[e(g,{toolbar:"",rowNumber:"",rowSelection:"",highlightCurrentRow:"",clickRowShowSubForm:"",clickRowShowMainForm:"",height:750,loading:r.value,columns:x.value,dataSource:y.value,onDetailsConfirm:T},{mainForm:a(({row:t})=>[t?(v(),f(I,{key:0,ref:"form2",model:t,rules:D,"label-col":S,"wrapper-col":w},{default:a(()=>[e(F,{gutter:8},{default:a(()=>[e(o,{span:8},{default:a(()=>[e(u,{label:"ID",name:"id"},{default:a(()=>[e(n,{value:t.id,"onUpdate:value":l=>t.id=l,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(o,{span:8},{default:a(()=>[e(u,{label:"序号",name:"num"},{default:a(()=>[e(n,{value:t.num,"onUpdate:value":l=>t.num=l},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(o,{span:8},{default:a(()=>[e(u,{label:"船名",name:"ship_name"},{default:a(()=>[e(n,{value:t.ship_name,"onUpdate:value":l=>t.ship_name=l},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(o,{span:8},{default:a(()=>[e(u,{label:"呼叫",name:"call"},{default:a(()=>[e(n,{value:t.call,"onUpdate:value":l=>t.call=l},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(o,{span:8},{default:a(()=>[e(u,{label:"长",name:"len"},{default:a(()=>[e(n,{value:t.len,"onUpdate:value":l=>t.len=l},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(o,{span:8},{default:a(()=>[e(u,{label:"吨",name:"ton"},{default:a(()=>[e(n,{value:t.ton,"onUpdate:value":l=>t.ton=l},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(o,{span:8},{default:a(()=>[e(u,{label:"付款方",name:"payer"},{default:a(()=>[e(n,{value:t.payer,"onUpdate:value":l=>t.payer=l},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(o,{span:8},{default:a(()=>[e(u,{label:"数",name:"count"},{default:a(()=>[e(n,{value:t.count,"onUpdate:value":l=>t.count=l},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(o,{span:8},{default:a(()=>[e(u,{label:"公司",name:"company"},{default:a(()=>[e(n,{value:t.company,"onUpdate:value":l=>t.company=l},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(o,{span:8},{default:a(()=>[e(u,{label:"动向",name:"trend"},{default:a(()=>[e(n,{value:t.trend,"onUpdate:value":l=>t.trend=l},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1032,["model","rules","label-col","wrapper-col"])):b("",!0)]),subForm:a(({row:t})=>[R(t)?(v(),f(g,{key:0,ref:"subFormTable",height:"auto","max-height":350,loading:d.loading,columns:d.columns,dataSource:d.dataSource},null,8,["loading","columns","dataSource"])):b("",!0)]),_:1},8,["loading","columns","dataSource"])]),_:1})}}});export{q as default};
