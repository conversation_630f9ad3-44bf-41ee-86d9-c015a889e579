import{d as b,f,ag as g,aB as h,ar as x,aE as y}from"./vue-vendor-dy9k-Yad.js";import{cs as v,bx as l}from"./index-CCWaWN5g.js";import{L as S}from"./useTableSync-075826a1-CL-4GwR8.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const R=b({name:"IndexTable",components:{},props:{actionButton:{type:Boolean,default:!0,required:!1}},setup(){const e=f([{title:"索引名称",key:"indexName",width:330,type:l.input,defaultValue:"",placeholder:"请输入${title}",validateRules:[{required:!0,message:"${title}不能为空"},{pattern:/^[a-zA-Z][a-zA-Z0-9_-]*$/,message:"命名规则：只能由字母、数字、下划线组成；必须以字母开头"}]},{title:"索引栏位",key:"indexField",width:330,type:l.selectMultiple,options:[],defaultValue:"",placeholder:"请选择${title}",validateRules:[{required:!0,message:"请选择${title}"}]},{title:"索引类型",key:"indexType",width:330,type:l.select,options:[{title:"normal",value:"normal"},{title:"unique",value:"unique"}],defaultValue:"normal",placeholder:"请选择${title}",validateRules:[{required:!0,message:"请选择${title}"}]}]),o=S(e),{tableRef:r,loading:i,dataSource:n,tableHeight:u,tableProps:t,setDataSource:s,validateData:p}=o;function m(c){let d=[];c.value.tableRef.getTableData().forEach(a=>{a.dbFieldName&&d.push({title:a.dbFieldTxt,value:a.dbFieldName})}),e.value[1].options=d}return{tableRef:r,loading:i,dataSource:n,columns:e,tableHeight:u,tableProps:t,syncTable:m,setDataSource:s,validateData:p}}});function T(e,o,r,i,n,u){const t=g("JVxeTable");return x(),h(t,y({ref:"tableRef",rowNumber:"",rowSelection:"",dragSort:"",keyboardEdit:"",sortKey:"orderNum",maxHeight:e.tableHeight.normal,loading:e.loading,columns:e.columns,dataSource:e.dataSource,toolbar:e.actionButton},e.tableProps),null,16,["maxHeight","loading","columns","dataSource","toolbar"])}const H=v(R,[["render",T]]);export{H as default};
