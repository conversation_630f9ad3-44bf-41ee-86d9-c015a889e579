var a=(_,s,r)=>new Promise((m,p)=>{var n=o=>{try{e(r.next(o))}catch(t){p(t)}},l=o=>{try{e(r.throw(o))}catch(t){p(t)}},e=o=>o.done?m(o.value):Promise.resolve(o.value).then(n,l);e((r=r.apply(_,s)).next())});import{d as v,ag as y,aB as x,ar as B,aD as i,at as c,k as f,G as d,u as b}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as F}from"./useForm-CgkFTrrO.js";import{B as g}from"./BasicForm-DBcXiHk0.js";import{a as k}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const C={style:{margin:"0 auto"}},w=v({__name:"BasicFormFooter",setup(_){const s=[{label:"员工姓名",field:"name",component:"Input"},{label:"性别",field:"sex",component:"Select",componentProps:{options:[{label:"男",value:1},{label:"女",value:2},{label:"未知",value:3}]},defaultValue:3},{label:"年龄",field:"age",component:"Input"},{label:"入职时间",subLabel:"( 选填 )",field:"entryTime",component:"TimePicker"}],[r,{validate:m,resetFields:p}]=F({schemas:s,labelWidth:"150px",showActionButtonGroup:!1});function n(){return a(this,null,function*(){let o=yield m()})}function l(){return a(this,null,function*(){let o=yield m()})}function e(){return a(this,null,function*(){yield p()})}return(o,t)=>{const u=y("a-button");return B(),x(b(g),{onRegister:b(r),style:{"margin-top":"20px"}},{formHeader:i(()=>t[0]||(t[0]=[c("div",{style:{margin:"0 auto 20px"}},[c("span",null,"我是自定义按钮")],-1)])),formFooter:i(()=>[c("div",C,[f(u,{type:"primary",onClick:n,class:"mr-2"},{default:i(()=>t[1]||(t[1]=[d(" 保存 ")])),_:1,__:[1]}),f(u,{type:"primary",onClick:l,class:"mr-2"},{default:i(()=>t[2]||(t[2]=[d(" 保存草稿 ")])),_:1,__:[2]}),f(u,{type:"error",onClick:e,class:"mr-2"},{default:i(()=>t[3]||(t[3]=[d(" 重置 ")])),_:1,__:[3]})])]),_:1},8,["onRegister"])}}}),It=k(w,[["__scopeId","data-v-9643184d"]]);export{It as default};
