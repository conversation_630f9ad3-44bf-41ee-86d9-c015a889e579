var Ie=Object.defineProperty,Ue=Object.defineProperties;var Oe=Object.getOwnPropertyDescriptors;var se=Object.getOwnPropertySymbols;var Pe=Object.prototype.hasOwnProperty,He=Object.prototype.propertyIsEnumerable;var re=(e,k,h)=>k in e?Ie(e,k,{enumerable:!0,configurable:!0,writable:!0,value:h}):e[k]=h,U=(e,k)=>{for(var h in k||(k={}))Pe.call(k,h)&&re(e,h,k[h]);if(se)for(var h of se(k))He.call(k,h)&&re(e,h,k[h]);return e},ie=(e,k)=>Ue(e,Oe(k));var oe=(e,k,h)=>new Promise((A,p)=>{var E=L=>{try{f(h.next(L))}catch(D){p(D)}},r=L=>{try{f(h.throw(L))}catch(D){p(D)}},f=L=>L.done?A(L.value):Promise.resolve(L.value).then(E,r);f((h=h.apply(e,k)).next())});import{d as Ke,f as W,aM as Ye,e as M,w as I,aq as H,ar as w,as as de,u as s,aG as Ve,ah as O,aB as J,aD as Y,G as ue,au as he,k as x,A as je,F as Z,aC as Re,aE as xe,S as Xe,l as $e,r as fe,J as X,h as $,o as Ge,n as qe,q as ye,B as ke,i as ze}from"./vue-vendor-dy9k-Yad.js";import{bc as Je,D as Qe,k as We,bd as Ze,be as et,h as ee,o as tt,bf as nt,n as lt,N as at,Y as ge,aC as ct}from"./antd-vue-vendor-me9YkNVC.js";import{N as st,x as rt,c as te,a$ as pe,R as it,n as Q,b0 as ot,T as dt,b1 as ut,a_ as ht,O as Se,i as ft,a7 as yt}from"./index-CCWaWN5g.js";import{B as kt}from"./index-CImCetrx.js";import{createBEM as Ce}from"./bem-sRx7x0Ii.js";import{buildProps as gt}from"./props-qAqCef5R.js";import{S as pt}from"./index-LCGLvkB3.js";import{useContextMenu as St}from"./useContextMenu-BU2ycxls.js";var T=(e=>(e[e.SELECT_ALL=0]="SELECT_ALL",e[e.UN_SELECT_ALL=1]="UN_SELECT_ALL",e[e.EXPAND_ALL=2]="EXPAND_ALL",e[e.UN_EXPAND_ALL=3]="UN_EXPAND_ALL",e[e.CHECK_STRICTLY=4]="CHECK_STRICTLY",e[e.CHECK_UN_STRICTLY=5]="CHECK_UN_STRICTLY",e))(T||{});const Kt=["update:expandedKeys","update:selectedKeys","update:value","change","check","search","update:searchValue"],xt=gt({value:{type:[Object,Array]},renderIcon:{type:Function},helpMessage:{type:[String,Array],default:""},title:{type:String,default:""},toolbar:Boolean,search:Boolean,searchValue:{type:String,default:""},checkStrictly:Boolean,clickRowToExpand:{type:Boolean,default:!1},checkable:Boolean,defaultExpandLevel:{type:[String,Number],default:""},defaultExpandAll:Boolean,fieldNames:{type:Object},treeData:{type:Array},actionList:{type:Array,default:()=>[]},expandedKeys:{type:Array,default:()=>[]},selectedKeys:{type:Array,default:()=>[]},checkedKeys:{type:Array,default:()=>[]},beforeRightClick:{type:Function,default:void 0},rightMenuList:{type:Array},filterFn:{type:Function,default:void 0},highlight:{type:[Boolean,String],default:!1},expandOnSearch:Boolean,checkOnSearch:Boolean,selectedOnSearch:Boolean,loading:{type:Boolean,default:!1}}),Ct={key:2,class:"flex items-center flex-1 cursor-pointer justify-self-stretch justify-end"},Lt=Ke({__name:"TreeHeader",props:{helpMessage:{type:[String,Array],default:""},title:{type:String,default:""},toolbar:{type:Boolean,default:!1},checkable:{type:Boolean,default:!1},search:{type:Boolean,default:!1},searchText:{type:String,default:""},checkAll:{type:Function,default:void 0},expandAll:{type:Function,default:void 0}},emits:["strictly-change","search","clickSearch"],setup(e,{emit:k}){const h=W(""),[A]=Ce("tree-header"),p=e,E=k,r=Ye(),{t:f}=st(),L=M(()=>["mr-1","w-full",{"ml-5":r.headerTitle||p.title}]),D=M(()=>{const{checkable:t}=p,a=[{label:f("component.tree.expandAll"),value:T.EXPAND_ALL},{label:f("component.tree.unExpandAll"),value:T.UN_EXPAND_ALL,divider:t}];return t?[{label:f("component.tree.selectAll"),value:T.SELECT_ALL},{label:f("component.tree.unSelectAll"),value:T.UN_SELECT_ALL,divider:t},...a,{label:f("component.tree.checkStrictly"),value:T.CHECK_STRICTLY},{label:f("component.tree.checkUnStrictly"),value:T.CHECK_UN_STRICTLY}]:a});function B(t){var l,o,i,y;const{key:a}=t;switch(a){case T.SELECT_ALL:(l=p.checkAll)==null||l.call(p,!0);break;case T.UN_SELECT_ALL:(o=p.checkAll)==null||o.call(p,!1);break;case T.EXPAND_ALL:(i=p.expandAll)==null||i.call(p,!0);break;case T.UN_EXPAND_ALL:(y=p.expandAll)==null||y.call(p,!1);break;case T.CHECK_STRICTLY:E("strictly-change",!1);break;case T.CHECK_UN_STRICTLY:E("strictly-change",!0);break}}function u(t){E("search",t)}const d=rt(u,200);return I(()=>h.value,t=>{d(t)}),I(()=>p.searchText,t=>{t!==h.value&&(h.value=t)}),(t,a)=>(w(),H("div",{class:de([s(A)(),"flex px-2 py-1.5 items-center"])},[s(r).headerTitle?Ve(t.$slots,"headerTitle",{key:0}):O("",!0),!s(r).headerTitle&&t.title?(w(),J(s(kt),{key:1,helpMessage:t.helpMessage},{default:Y(()=>[ue(he(t.title),1)]),_:1},8,["helpMessage"])):O("",!0),t.search||t.toolbar?(w(),H("div",Ct,[t.search?(w(),H("div",{key:0,class:de(L.value)},[x(s(Je),{placeholder:s(f)("common.searchText"),size:"small",allowClear:"",value:h.value,"onUpdate:value":a[0]||(a[0]=l=>h.value=l),onSearch:a[1]||(a[1]=l=>t.$emit("clickSearch",l))},null,8,["placeholder","value"])],2)):O("",!0),t.toolbar?(w(),J(s(Qe),{key:1,onClick:a[2]||(a[2]=je(()=>{},["prevent"]))},{overlay:Y(()=>[x(s(We),{onClick:B},{default:Y(()=>[(w(!0),H(Z,null,Re(D.value,l=>(w(),H(Z,{key:l.value},[x(s(Ze),xe({ref_for:!0},{key:l.value}),{default:Y(()=>[ue(he(l.label),1)]),_:2},1040),l.divider?(w(),J(s(et),{key:0})):O("",!0)],64))),128))]),_:1})]),default:Y(()=>[x(s(te),{icon:"ion:ellipsis-vertical"})]),_:1})):O("",!0)])):O("",!0)],2))}}),At=({icon:e})=>e?Xe(e)?$e(te,{icon:e,class:"mr-1"}):te:null;function vt(e,k){function h(u){const d=[],t=u||s(e),{key:a,children:l}=s(k);if(!l||!a)return d;for(let o=0;o<t.length;o++){const i=t[o];d.push(i[a]);const y=i[l];y&&y.length&&d.push(...h(y))}return d}function A(u){const d=[],t=u||s(e),{key:a,children:l}=s(k);if(!l||!a)return d;for(let o=0;o<t.length;o++){const i=t[o];i.disabled!==!0&&i.selectable!==!1&&d.push(i[a]);const y=i[l];y&&y.length&&d.push(...A(y))}return d}function p(u,d){const t=[],a=d||s(e),{key:l,children:o}=s(k);if(!o||!l)return t;for(let i=0;i<a.length;i++){const y=a[i],v=y[o];u===y[l]?(t.push(y[l]),v&&v.length&&t.push(...h(v))):v&&v.length&&t.push(...p(u,v))}return t}function E(u,d,t){if(!u)return;const a=t||s(e),{key:l,children:o}=s(k);if(!(!o||!l))for(let i=0;i<a.length;i++){const y=a[i],v=y[o];if(y[l]===u){a[i]=U(U({},a[i]),d);break}else v&&v.length&&E(u,d,y[o])}}function r(u=1,d,t=1){if(!u)return[];const a=[],l=d||s(e)||[];for(let o=0;o<l.length;o++){const i=l[o],{key:y,children:v}=s(k),G=y?i[y]:"",P=v?i[v]:[];a.push(G),P&&P.length&&t<u&&(t+=1,a.push(...r(u,P,t)))}return a}function f({parentKey:u=null,node:d,push:t="push"}){const a=ee(s(e));if(!u){a[t](d),e.value=a;return}const{key:l,children:o}=s(k);!o||!l||(pe(a,i=>{if(i[l]===u)return i[o]=i[o]||[],i[o][t](d),!0}),e.value=a)}function L({parentKey:u=null,list:d,push:t="push"}){const a=ee(s(e));if(!(!d||d.length<1))if(u){const{key:l,children:o}=s(k);if(!o||!l)return;pe(a,i=>{if(i[l]===u){i[o]=i[o]||[];for(let y=0;y<d.length;y++)i[o][t](d[y]);return e.value=a,!0}})}else for(let l=0;l<d.length;l++)a[t](d[l])}function D(u,d){if(!u)return;const t=d||s(e),{key:a,children:l}=s(k);if(!(!l||!a))for(let o=0;o<t.length;o++){const i=t[o],y=i[l];if(i[a]===u){t.splice(o,1);break}else y&&y.length&&D(u,i[l])}}function B(u,d,t){return!u&&u!==0?null:((d||s(e)).forEach(l=>{if(t!=null&&t.key||(t==null?void 0:t.key)===0)return t;if(l.key===u){t=l;return}l.children&&l.children.length&&(t=B(u,l.children,t))}),t||null)}return{deleteNodeByKey:D,insertNodeByKey:f,insertNodesByKey:L,filterByLevel:r,updateNodeByKey:E,getAllKeys:h,getChildrenKeys:p,getEnabledKeys:A,getSelectedNode:B}}function Et(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!ze(e)}const Mt=Ke({name:"BasicTree",inheritAttrs:!1,props:xt,emits:Kt,setup(e,{attrs:k,slots:h,emit:A,expose:p}){const[E]=Ce("tree"),r=fe({checkStrictly:e.checkStrictly,expandedKeys:e.expandedKeys||[],selectedKeys:e.selectedKeys||[],checkedKeys:e.checkedKeys||[]}),f=fe({startSearch:!1,searchText:"",searchData:[]}),L=W([]),[D]=St(),B=M(()=>{const{fieldNames:n}=e;return U({children:"children",title:"title",key:"key"},n)}),u=W(null),d=M(()=>{let n=ie(U(U({blockNode:!0},k),e),{expandedKeys:r.expandedKeys,selectedKeys:r.selectedKeys,checkedKeys:r.checkedKeys,checkStrictly:r.checkStrictly,fieldNames:s(B),"onUpdate:expandedKeys":c=>{r.expandedKeys=c,A("update:expandedKeys",c)},"onUpdate:selectedKeys":c=>{r.selectedKeys=c,A("update:selectedKeys",c)},onCheck:(c,g)=>{t(c,g)},onRightClick:Te});return tt(n,"treeData","class")}),t=(n,c)=>{let g=X(r.checkedKeys);if(it(g)&&f.startSearch&&c){const S=c.node.eventKey;g=nt(g,Le(S)),c.checked&&g.push(S),r.checkedKeys=g}else r.checkedKeys=n;const K=X(r.checkedKeys);A("update:value",K),A("check",K,c)},a=M(()=>f.startSearch?f.searchData:s(L)),l=M(()=>!a.value||a.value.length===0),{deleteNodeByKey:o,insertNodeByKey:i,insertNodesByKey:y,filterByLevel:v,updateNodeByKey:G,getAllKeys:P,getChildrenKeys:Le,getEnabledKeys:Ae,getSelectedNode:ve}=vt(L,B);function Ee(n,c){return!c&&e.renderIcon&&Q(e.renderIcon)?e.renderIcon(n):c}function Te(g){return oe(this,arguments,function*({event:n,node:c}){var m;const{rightMenuList:K=[],beforeRightClick:S}=e;let C={event:n,items:[]};if(S&&Q(S)){let b=yield S(c,n);Array.isArray(b)?C.items=b:Object.assign(C,b)}else C.items=K;(m=C.items)!=null&&m.length&&(C.items=C.items.filter(b=>!b.hidden),D(C))})}function V(n){r.expandedKeys=n}function be(){return r.expandedKeys}function ne(n){r.selectedKeys=n}function _e(){return r.selectedKeys}function le(n){r.checkedKeys=n}function Be(){return r.checkedKeys}function ae(n){r.checkedKeys=n?Ae():[]}function q(n){r.expandedKeys=n?P():[]}function De(n){r.checkStrictly=n}I(()=>e.searchValue,n=>{n!==f.searchText&&(f.searchText=n)},{immediate:!0}),I(()=>e.treeData,n=>{n&&z(f.searchText)});function z(n){if(n!==f.searchText&&(f.searchText=n),A("update:searchValue",n),!n){f.startSearch=!1;return}const{filterFn:c,checkable:g,expandOnSearch:K,checkOnSearch:S,selectedOnSearch:C}=s(e);f.startSearch=!0;const{title:m,key:b}=s(B),F=[];if(f.searchData=ot(s(L),_=>{var j,R;const N=c?c(n,_,s(B)):(R=(j=_[m])==null?void 0:j.includes(n))!=null?R:!1;return N&&F.push(_[b]),N},s(B)),K){const _=dt(f.searchData).map(N=>N[b]);_&&_.length&&V(_)}S&&g&&F.length&&le(F),C&&F.length&&ne(F)}function Fe(n,c){if(!(!e.clickRowToExpand||!c||c.length===0))if(!r.expandedKeys.includes(n))V([...r.expandedKeys,n]);else{const g=[...r.expandedKeys],K=g.findIndex(S=>S===n);K!==-1&&g.splice(K,1),V(g)}}$(()=>{L.value=e.treeData}),Ge(()=>{const n=parseInt(e.defaultExpandLevel);n>0?r.expandedKeys=v(n):e.defaultExpandAll&&q(!0)}),$(()=>{r.expandedKeys=e.expandedKeys}),$(()=>{r.selectedKeys=e.selectedKeys}),$(()=>{r.checkedKeys=e.checkedKeys}),I(()=>e.value,()=>{r.checkedKeys=X(e.value||e.checkedKeys||[])},{immediate:!0}),I(()=>r.checkedKeys,()=>{const n=X(r.checkedKeys);A("update:value",n),A("change",n)}),I(()=>e.checkStrictly,()=>{r.checkStrictly=e.checkStrictly,qe(()=>{var c;const n=(c=u.value)==null?void 0:c.checkedKeys;t([...n])})});const me={setExpandedKeys:V,getExpandedKeys:be,setSelectedKeys:ne,getSelectedKeys:_e,setCheckedKeys:le,getCheckedKeys:Be,insertNodeByKey:i,insertNodesByKey:y,deleteNodeByKey:o,updateNodeByKey:G,getSelectedNode:ve,checkAll:ae,expandAll:q,filterByLevel:n=>{r.expandedKeys=v(n)},setSearchValue:n=>{z(n)},getSearchValue:()=>f.searchText};function Ne(n){const{actionList:c}=e;if(!(!c||c.length===0))return c.map((g,K)=>{var C;let S=!0;return Q(g.show)?S=(C=g.show)==null?void 0:C.call(g,n):Se(g.show)&&(S=g.show),S?x("span",{key:K,class:E("action")},[g.render(n)]):null})}const we=M(()=>{const n=ee(a.value);return ut(n,(c,g)=>{var ce;const K=f.searchText,{highlight:S}=s(e),{title:C,key:m,children:b}=s(B),F=Ee(c,c.icon),_=ct(c,C),N=K?_.indexOf(K):-1,j=f.startSearch&&!ft(K)&&S&&N!==-1,R=`color: ${Se(S)?"#f50":S}`,Me=j?x("span",{class:(ce=s(d))!=null&&ce.blockNode?`${E("content")}`:""},[x("span",null,[_.substr(0,N)]),x("span",{style:R},[K]),x("span",null,[_.substr(N+K.length)])]):_;return c[C]=x("span",{class:`${E("title")} pl-2`,onClick:Fe.bind(null,c[m],c[b])},[h!=null&&h.title?yt(h,"title",c):x(Z,null,[F&&x(At,{icon:F},null),Me,x("span",{class:E("actions")},[Ne(c)])])]),c}),n});return p(me),()=>{let n;const{title:c,helpMessage:g,toolbar:K,search:S,checkable:C}=e,m=c||K||S||h.headerTitle,b={height:"calc(100% - 38px)"};return x("div",{class:[E(),"h-full",k.class]},[m&&x(Lt,{checkable:C,checkAll:ae,expandAll:q,title:c,search:S,toolbar:K,helpMessage:g,onStrictlyChange:De,onSearch:z,onClickSearch:F=>A("search",F),searchText:f.searchText},Et(n=ht(h))?n:{default:()=>[n]}),x(lt,{spinning:s(e.loading),tip:"加载中..."},{default:()=>[ye(x(pt,{style:b},{default:()=>[x(at,xe({ref:u},s(d),{showIcon:!1,treeData:we.value}),null)]}),[[ke,!s(l)]]),ye(x(ge,{image:ge.PRESENTED_IMAGE_SIMPLE,class:"!mt-4"},null),[[ke,s(l)]])]})])}}});export{Mt as _};
