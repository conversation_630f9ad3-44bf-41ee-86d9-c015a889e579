import{d as I,f as p,ag as s,aB as k,ar as n,aD as i,k as r,at as c,aq as l,ah as m,au as v}from"./vue-vendor-dy9k-Yad.js";import{P as y}from"./index-CtJ0w2CP.js";import{C as A}from"./index-LCGLvkB3.js";import{C as w,a as B}from"./index-BPAdgtaT.js";import{u as P}from"./upload-Dd1Qafin.js";import{h as $}from"./header-OZa5fSDc.js";import{ah as N,a as S}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./CustomModal-BakuIxQv.js";import"./base64Conver-24EVOS6V.js";const V=I({components:{PageWrapper:y,CropperImage:B,CollapseContainer:A,CropperAvatar:w},setup(){var t;const e=p(""),d=p(""),u=p(""),f=p(""),g=N(),h=p(((t=g.getUserInfo)==null?void 0:t.avatar)||"");function C({imgBase64:a,imgInfo:_}){e.value=_,d.value=a}function o({imgBase64:a,imgInfo:_}){u.value=_,f.value=a}return{img:$,info:e,circleInfo:u,cropperImg:d,circleImg:f,handleCropend:C,handleCircleCropend:o,avatar:h,uploadApi:P}}}),W={class:"container p-4"},D={class:"cropper-container mr-10"},U=["src"],q={key:0},E={class:"container p-4"},b={class:"cropper-container mr-10"},j=["src"],z={key:0};function F(e,d,u,f,g,h){const C=s("CropperAvatar"),o=s("CollapseContainer"),t=s("CropperImage"),a=s("PageWrapper");return n(),k(a,{title:"图片裁剪示例",content:"需要开启测试接口服务才能进行上传测试！"},{default:i(()=>[r(o,{title:"头像裁剪"},{default:i(()=>[r(C,{uploadApi:e.uploadApi,value:e.avatar},null,8,["uploadApi","value"])]),_:1}),r(o,{title:"矩形裁剪",class:"my-4"},{default:i(()=>[c("div",W,[c("div",D,[r(t,{ref:"refCropper",src:e.img,onCropend:e.handleCropend,style:{width:"40vw"}},null,8,["src","onCropend"])]),e.cropperImg?(n(),l("img",{key:0,src:e.cropperImg,class:"croppered",alt:""},null,8,U)):m("",!0)]),e.cropperImg?(n(),l("p",q,"裁剪后图片信息："+v(e.info),1)):m("",!0)]),_:1}),r(o,{title:"圆形裁剪"},{default:i(()=>[c("div",E,[c("div",b,[r(t,{ref:"refCropper",src:e.img,onCropend:e.handleCircleCropend,style:{width:"40vw"},circled:""},null,8,["src","onCropend"])]),e.circleImg?(n(),l("img",{key:0,src:e.circleImg,class:"croppered"},null,8,j)):m("",!0)]),e.circleImg?(n(),l("p",z,"裁剪后图片信息："+v(e.circleInfo),1)):m("",!0)]),_:1})]),_:1})}const le=S(V,[["render",F],["__scopeId","data-v-c768e869"]]);export{le as default};
