import{d as y,ag as P,aq as g,ar as x,as as E,u as k,at as m,au as V,k as q}from"./vue-vendor-dy9k-Yad.js";import{u as w}from"./index-BOX6--gq.js";import{F as B}from"./index-CCWaWN5g.js";var _=(r,u,l)=>new Promise((n,s)=>{var d=e=>{try{a(l.next(e))}catch(o){s(o)}},p=e=>{try{a(l.throw(e))}catch(o){s(o)}},a=e=>e.done?n(e.value):Promise.resolve(e.value).then(d,p);a((l=l.apply(r,u)).next())});const N=y({__name:"TitleEditor",props:{title:{type:String},promptProps:{type:Object,default:()=>({})},handleCustomEdit:{type:Function}},emits:["update:title"],setup(r,{expose:u,emit:l}){const n=r,s=l,{createJPrompt:d}=w(),{prefixCls:p}=B("modal-title-editor");function a(){typeof n.handleCustomEdit=="function"?n.handleCustomEdit():e()}function e(o,c){var i,f;const{promptProps:t}=n,C=(i=t==null?void 0:t.required)!=null?i:!1,v=(t==null?void 0:t.rules)||[];C&&v.push({required:!0,message:"这里是必填的"}),d({title:(t==null?void 0:t.title)||"修改标题",defaultValue:(f=t==null?void 0:t.defaultValue)!=null?f:n.title,placeholder:(t==null?void 0:t.placeholder)||"请输入新标题",rules:v,onOk(h){return _(this,null,function*(){s("update:title",h),o&&(yield o(h))})},onCancel(){c&&c()}})}return u({showPrompt:e}),(o,c)=>{const i=P("Icon");return x(),g("div",{class:E([k(p)])},[m("div",{class:"text-area",onClick:a},[m("span",null,V(r.title),1),m("span",null,[q(i,{icon:"ant-design:edit"})])])],2)}}});export{N as T};
