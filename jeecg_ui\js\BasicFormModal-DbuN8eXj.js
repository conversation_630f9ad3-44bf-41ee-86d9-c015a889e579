var u=(n,p,t)=>new Promise((a,i)=>{var l=o=>{try{e(t.next(o))}catch(r){i(r)}},c=o=>{try{e(t.throw(o))}catch(r){i(r)}},e=o=>o.done?a(o.value):Promise.resolve(o.value).then(l,c);e((t=t.apply(n,p)).next())});import{d as _,ag as b,aq as g,ar as B,at as F,k as s,aD as d,G as v,u as m,F as x}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{B as M}from"./index-Diw57m_E.js";import{ad as k,a as y}from"./index-CCWaWN5g.js";import{u as h}from"./useForm-CgkFTrrO.js";import{B as C}from"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";const R={style:{margin:"20px auto"}},V=_({__name:"BasicFormModal",setup(n){const p=[{label:"员工姓名",field:"name",component:"Input"},{label:"性别",field:"sex",component:"Select",componentProps:{options:[{label:"男",value:1},{label:"女",value:2},{label:"未知",value:3}]},defaultValue:3},{label:"年龄",field:"age",component:"Input"},{label:"入职时间",subLabel:"( 选填 )",field:"entryTime",component:"TimePicker"}],[t,{openModal:a}]=k(),[i,{validate:l,resetFields:c}]=h({schemas:p,showActionButtonGroup:!1});function e(){return u(this,null,function*(){a(!0,{})})}return(o,r)=>{const f=b("a-button");return B(),g(x,null,[F("div",R,[s(f,{type:"primary",onClick:e,class:"mr-2"},{default:d(()=>r[0]||(r[0]=[v(" 打开弹窗 ")])),_:1,__:[0]})]),s(m(M),{onRegister:m(t),title:"弹出层表单"},{default:d(()=>[s(m(C),{onRegister:m(i)},null,8,["onRegister"])]),_:1},8,["onRegister"])],64)}}}),Po=y(V,[["__scopeId","data-v-fa2b9111"]]);export{Po as default};
