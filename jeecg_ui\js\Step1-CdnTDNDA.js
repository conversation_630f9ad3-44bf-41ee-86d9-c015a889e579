var k=(D,P,b)=>new Promise((I,c)=>{var q=f=>{try{U(b.next(f))}catch(w){c(w)}},a=f=>{try{U(b.throw(f))}catch(w){c(w)}},U=f=>f.done?I(f.value):Promise.resolve(f.value).then(q,a);U((b=b.apply(D,P)).next())});import{d as X,f as Y,e as E,w as T,ag as p,aq as m,ar as v,k as t,aD as s,at as o,ah as N,G as n,au as F,q as O,B as A,u as _}from"./vue-vendor-dy9k-Yad.js";import{bB as J}from"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import{i as S}from"./JAreaSelect-Db7Nhhc_.js";import{_ as x}from"./JUpload-CRos0F1P.js";import{a as V}from"./index-CCWaWN5g.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./areaDataUtil-BXVjRArW.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const H={class:"step-panel"},G={class:"form-section"},$={class:"form-row"},h={key:0,class:"edit-mode-tip"},K={class:"form-section"},W={class:"section-title"},Z={key:0,class:"form-row"},ee={key:1,class:"form-row entrust-row"},ae={key:0,class:"form-section"},te={key:0},le={class:"form-row"},se={class:"form-row basic-three-row"},oe={class:"form-row"},ie={class:"form-row reserve-price-row"},re={key:1},ne={class:"form-row asset-row"},ue={class:"form-row asset-row"},de={class:"form-row"},me={class:"form-section"},ve={class:"section-title"},pe={key:0,class:"form-row"},fe={class:"location-selects"},ce={key:1,class:"form-row location-row"},ge={class:"location-selects"},_e={class:"detail-address"},be={class:"form-section"},ye={key:0},Ie={class:"form-row"},qe={class:"upload-container"},Ue={key:1},we={class:"form-row"},Ne={class:"upload-container"},xe={class:"form-row"},De={class:"upload-container"},Pe={key:0,class:"form-row"},ze={class:"upload-container"},Ce={class:"form-row"},Te=X({__name:"Step1",props:{modelValue:{},locationLoading:{type:Boolean},isEditMode:{type:Boolean}},emits:["update:modelValue","areaChange","getCurrentLocation","serviceTypeChange"],setup(D,{expose:P,emit:b}){const I=D,c=b,q=Y(),a=E({get:()=>I.modelValue,set:u=>c("update:modelValue",u)}),U={serviceType:[{required:!0,message:"请选择服务类型",trigger:"change"}],entrustInfo:{noticeName:[{required:!0,message:"请输入公告名称",trigger:"blur"}]},basicInfo:{subjectName:[{required:!0,message:"请输入标的名称",trigger:"blur"}],subjectQuantity:[{required:!0,message:"请输入标的数量",trigger:"blur"}],measurementUnit:[{required:!0,message:"请选择计量单位",trigger:"change"}],auctionDate:[{required:!0,message:"请选择拍卖日期",trigger:"change"}],quantityFlag:[{required:!0,message:"请选择是否展示实际数量",trigger:"change"}],hasReservePrice:[{required:!0,message:"请选择是否设置保留价",trigger:"change"}],reservePrice:[{validator:(u,e)=>{if(a.value.basicInfo.hasReservePrice==="yes"){if(!e&&e!==0)return Promise.reject("请输入保留价");if(e<0)return Promise.reject("保留价不能为负数")}return Promise.resolve()},trigger:"blur"}],assetName:[{required:!0,message:"请输入资产名称",trigger:"blur"}],assetQuantity:[{required:!0,message:"请输入资产数量",trigger:"blur"}],assetMeasurementUnit:[{required:!0,message:"请选择计量单位",trigger:"change"}],assetQuantityFlag:[{required:!0,message:"请选择是否展示实际数量",trigger:"change"}]},location:{province:[{required:!0,message:"请选择省份",trigger:"change"}],detailAddress:[{required:!0,message:"请输入详细地址",trigger:"blur"}]},materials:{entrustDocument:[{required:!0,message:"请上传委托单文件",trigger:"change"}]}};P({validateForm:()=>k(null,null,function*(){var u;try{return yield(u=q.value)==null?void 0:u.validate(),!0}catch(e){const i=document.querySelector(".ant-form-item-has-error");return i&&i.scrollIntoView({behavior:"smooth",block:"center"}),!1}})});const w=u=>{const e=[],i=[];if(u.forEach(d=>{const r={fileName:d.fileName,filePath:d.filePath,fileSize:d.fileSize,fileType:d.fileType};d.fileType==="image"?e.push(r):i.push(r)}),e.length>0){const d=JSON.stringify(e);a.value.materials.images=d}if(i.length>0){const d=JSON.stringify(i);a.value.materials.attachments=d}};T(()=>a.value.hgyAttachmentList,u=>{u&&Array.isArray(u)&&u.length>0&&w(u)},{immediate:!0,deep:!0}),T(()=>[a.value.location.province,a.value.location.city,a.value.location.area],(u,e)=>{if(u.some((i,d)=>i!==(e==null?void 0:e[d]))){const i=[a.value.location.province,a.value.location.city,a.value.location.area];c("areaChange",i),setTimeout(()=>{var d;(d=q.value)==null||d.validateFields([["location","province"],["location","city"],["location","area"]]).catch(()=>{})},100)}},{deep:!0});const M=()=>{c("getCurrentLocation")},Q=u=>{c("serviceTypeChange",u)};return T(()=>a.value.basicInfo.hasReservePrice,u=>{u==="no"&&(a.value.basicInfo.reservePrice=void 0)}),(u,e)=>{const i=p("a-select-option"),d=p("a-select"),r=p("a-form-item"),g=p("a-input"),z=p("a-input-number"),j=p("a-date-picker"),y=p("a-radio"),C=p("a-radio-group"),R=p("a-button"),B=p("a-textarea"),L=p("a-form");return v(),m("div",H,[t(L,{ref_key:"formRef",ref:q,model:a.value,rules:U,"validate-trigger":["change","blur"],"scroll-to-first-error":!0},{default:s(()=>[o("div",G,[e[32]||(e[32]=o("h3",{class:"section-title"},"服务类型",-1)),o("div",$,[t(r,{name:"serviceType",class:"service-type-item"},{default:s(()=>[t(d,{value:a.value.serviceType,"onUpdate:value":e[0]||(e[0]=l=>a.value.serviceType=l),onChange:Q,placeholder:"请选择服务类型",size:"large",class:"service-type-select",disabled:I.isEditMode},{default:s(()=>[t(i,{value:1},{default:s(()=>e[28]||(e[28]=[n("发布竞价委托")])),_:1,__:[28]}),t(i,{value:2},{default:s(()=>e[29]||(e[29]=[n("发布资产处置")])),_:1,__:[29]}),t(i,{value:3},{default:s(()=>e[30]||(e[30]=[n("发布采购信息")])),_:1,__:[30]})]),_:1},8,["value","disabled"]),I.isEditMode?(v(),m("div",h,e[31]||(e[31]=[o("span",{class:"tip-text"},"编辑模式下不允许修改服务类型",-1)]))):N("",!0)]),_:1})])]),o("div",K,[o("h3",W,F(a.value.serviceType===3?"采购信息":"委托信息"),1),a.value.serviceType===3?(v(),m("div",Z,[t(r,{label:"公告名称",name:["entrustInfo","noticeName"],required:"",class:"form-item-full"},{default:s(()=>[t(g,{value:a.value.entrustInfo.noticeName,"onUpdate:value":e[1]||(e[1]=l=>a.value.entrustInfo.noticeName=l),placeholder:"请输入公告名称",size:"large"},null,8,["value"])]),_:1})])):(v(),m("div",ee,[t(r,{label:"委托单位",name:["entrustInfo","title"],required:"",class:"entrust-item"},{default:s(()=>[t(g,{value:a.value.entrustInfo.title,"onUpdate:value":e[2]||(e[2]=l=>a.value.entrustInfo.title=l),placeholder:"委托单位",size:"large",disabled:"",readonly:""},null,8,["value"])]),_:1}),t(r,{label:"受委托单位",name:["entrustInfo","type"],required:"",class:"entrust-item"},{default:s(()=>[t(g,{value:a.value.entrustInfo.type,"onUpdate:value":e[3]||(e[3]=l=>a.value.entrustInfo.type=l),placeholder:"受委托单位",size:"large",disabled:"",readonly:""},null,8,["value"])]),_:1}),e[33]||(e[33]=o("div",{class:"entrust-placeholder"},null,-1))]))]),a.value.serviceType!==3?(v(),m("div",ae,[e[55]||(e[55]=o("h3",{class:"section-title"},"基本信息",-1)),a.value.serviceType===1?(v(),m("div",te,[o("div",le,[t(r,{label:"标的名称",name:["basicInfo","subjectName"],required:"",class:"subject-name-item"},{default:s(()=>[t(g,{value:a.value.basicInfo.subjectName,"onUpdate:value":e[4]||(e[4]=l=>a.value.basicInfo.subjectName=l),placeholder:"请输入标的名称",size:"large"},null,8,["value"])]),_:1})]),o("div",se,[t(r,{label:"标的数量",name:["basicInfo","subjectQuantity"],required:"",class:"basic-three-item"},{default:s(()=>[t(z,{value:a.value.basicInfo.subjectQuantity,"onUpdate:value":e[5]||(e[5]=l=>a.value.basicInfo.subjectQuantity=l),placeholder:"请输入标的数量",style:{width:"100%"},size:"large",min:1},null,8,["value"])]),_:1}),t(r,{label:"计量单位",name:["basicInfo","measurementUnit"],required:"",class:"basic-three-item"},{default:s(()=>[t(d,{value:a.value.basicInfo.measurementUnit,"onUpdate:value":e[6]||(e[6]=l=>a.value.basicInfo.measurementUnit=l),placeholder:"请选择计量单位",size:"large"},{default:s(()=>[t(i,{value:"台"},{default:s(()=>e[34]||(e[34]=[n("台")])),_:1,__:[34]}),t(i,{value:"辆"},{default:s(()=>e[35]||(e[35]=[n("辆")])),_:1,__:[35]}),t(i,{value:"套"},{default:s(()=>e[36]||(e[36]=[n("套")])),_:1,__:[36]}),t(i,{value:"个"},{default:s(()=>e[37]||(e[37]=[n("个")])),_:1,__:[37]}),t(i,{value:"件"},{default:s(()=>e[38]||(e[38]=[n("件")])),_:1,__:[38]}),t(i,{value:"批"},{default:s(()=>e[39]||(e[39]=[n("批")])),_:1,__:[39]}),t(i,{value:"米"},{default:s(()=>e[40]||(e[40]=[n("米")])),_:1,__:[40]})]),_:1},8,["value"])]),_:1}),t(r,{label:"拍卖日期",name:["basicInfo","auctionDate"],required:"",class:"basic-three-item"},{default:s(()=>[t(j,{value:a.value.basicInfo.auctionDate,"onUpdate:value":e[7]||(e[7]=l=>a.value.basicInfo.auctionDate=l),placeholder:"请选择拍卖日期",style:{width:"100%"},size:"large","show-time":"",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["value"])]),_:1})]),o("div",oe,[t(r,{label:"是否展示实际数量",name:["basicInfo","quantityFlag"],required:"",class:"quantity-flag-item"},{default:s(()=>[t(C,{value:a.value.basicInfo.quantityFlag,"onUpdate:value":e[8]||(e[8]=l=>a.value.basicInfo.quantityFlag=l),size:"large"},{default:s(()=>[t(y,{value:1},{default:s(()=>e[41]||(e[41]=[n("是")])),_:1,__:[41]}),t(y,{value:0},{default:s(()=>e[42]||(e[42]=[n("否")])),_:1,__:[42]})]),_:1},8,["value"])]),_:1})]),o("div",ie,[t(r,{label:"是否设置保留价",name:["basicInfo","hasReservePrice"],required:"",class:"reserve-price-radio-item"},{default:s(()=>[t(C,{value:a.value.basicInfo.hasReservePrice,"onUpdate:value":e[9]||(e[9]=l=>a.value.basicInfo.hasReservePrice=l),size:"large"},{default:s(()=>[t(y,{value:"yes"},{default:s(()=>e[43]||(e[43]=[n("是")])),_:1,__:[43]}),t(y,{value:"no"},{default:s(()=>e[44]||(e[44]=[n("否")])),_:1,__:[44]})]),_:1},8,["value"])]),_:1}),O(t(r,{label:"保留价",name:["basicInfo","reservePrice"],required:a.value.basicInfo.hasReservePrice==="yes",class:"reserve-price-input-item"},{default:s(()=>[t(z,{value:a.value.basicInfo.reservePrice,"onUpdate:value":e[10]||(e[10]=l=>a.value.basicInfo.reservePrice=l),placeholder:"请输入保留价",style:{width:"100%"},size:"large",min:0,precision:2,formatter:l=>`￥ ${l}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:l=>l.replace(/￥\s?|(,*)/g,"")},null,8,["value","formatter","parser"])]),_:1},8,["required"]),[[A,a.value.basicInfo.hasReservePrice==="yes"]])])])):N("",!0),a.value.serviceType===2?(v(),m("div",re,[o("div",ne,[t(r,{label:"资产名称",name:["basicInfo","assetName"],required:"",class:"asset-item"},{default:s(()=>[t(g,{value:a.value.basicInfo.assetName,"onUpdate:value":e[11]||(e[11]=l=>a.value.basicInfo.assetName=l),placeholder:"请输入资产名称",size:"large"},null,8,["value"])]),_:1}),t(r,{label:"资产编号",name:["basicInfo","assetCode"],class:"asset-item"},{default:s(()=>[t(g,{value:a.value.basicInfo.assetCode,"onUpdate:value":e[12]||(e[12]=l=>a.value.basicInfo.assetCode=l),placeholder:"由系统自动生成",size:"large",disabled:""},null,8,["value"])]),_:1}),e[45]||(e[45]=o("div",{class:"asset-placeholder"},null,-1))]),o("div",ue,[t(r,{label:"资产数量",name:["basicInfo","assetQuantity"],required:"",class:"asset-item"},{default:s(()=>[t(z,{value:a.value.basicInfo.assetQuantity,"onUpdate:value":e[13]||(e[13]=l=>a.value.basicInfo.assetQuantity=l),placeholder:"请输入资产数量",size:"large",min:1},null,8,["value"])]),_:1}),t(r,{label:"计量单位",name:["basicInfo","assetMeasurementUnit"],required:"",class:"asset-item"},{default:s(()=>[t(d,{value:a.value.basicInfo.assetMeasurementUnit,"onUpdate:value":e[14]||(e[14]=l=>a.value.basicInfo.assetMeasurementUnit=l),placeholder:"请选择计量单位",size:"large"},{default:s(()=>[t(i,{value:"台"},{default:s(()=>e[46]||(e[46]=[n("台")])),_:1,__:[46]}),t(i,{value:"辆"},{default:s(()=>e[47]||(e[47]=[n("辆")])),_:1,__:[47]}),t(i,{value:"套"},{default:s(()=>e[48]||(e[48]=[n("套")])),_:1,__:[48]}),t(i,{value:"个"},{default:s(()=>e[49]||(e[49]=[n("个")])),_:1,__:[49]}),t(i,{value:"件"},{default:s(()=>e[50]||(e[50]=[n("件")])),_:1,__:[50]}),t(i,{value:"批"},{default:s(()=>e[51]||(e[51]=[n("批")])),_:1,__:[51]})]),_:1},8,["value"])]),_:1}),e[52]||(e[52]=o("div",{class:"asset-placeholder"},null,-1))]),o("div",de,[t(r,{label:"是否展示实际数量",name:["basicInfo","assetQuantityFlag"],required:"",class:"asset-quantity-flag-item"},{default:s(()=>[t(C,{value:a.value.basicInfo.assetQuantityFlag,"onUpdate:value":e[15]||(e[15]=l=>a.value.basicInfo.assetQuantityFlag=l),size:"large"},{default:s(()=>[t(y,{value:1},{default:s(()=>e[53]||(e[53]=[n("是")])),_:1,__:[53]}),t(y,{value:0},{default:s(()=>e[54]||(e[54]=[n("否")])),_:1,__:[54]})]),_:1},8,["value"])]),_:1})])])):N("",!0)])):N("",!0),o("div",me,[o("h3",ve,F(a.value.serviceType===3?"所属地区":"存放位置"),1),a.value.serviceType===3?(v(),m("div",pe,[o("div",fe,[t(r,{name:["location","province"],class:"location-area-item"},{default:s(()=>[t(_(S),{province:a.value.location.province,"onUpdate:province":e[16]||(e[16]=l=>a.value.location.province=l),city:a.value.location.city,"onUpdate:city":e[17]||(e[17]=l=>a.value.location.city=l),area:a.value.location.area,"onUpdate:area":e[18]||(e[18]=l=>a.value.location.area=l),placeholder:"请选择所属地区",level:3},null,8,["province","city","area"])]),_:1})])])):(v(),m("div",ce,[o("div",ge,[t(r,{name:["location","province"],class:"location-area-item"},{default:s(()=>[t(_(S),{province:a.value.location.province,"onUpdate:province":e[19]||(e[19]=l=>a.value.location.province=l),city:a.value.location.city,"onUpdate:city":e[20]||(e[20]=l=>a.value.location.city=l),area:a.value.location.area,"onUpdate:area":e[21]||(e[21]=l=>a.value.location.area=l),placeholder:"请选择存放位置",level:3},null,8,["province","city","area"])]),_:1})]),o("div",_e,[t(r,{label:"详细地址",name:["location","detailAddress"],required:"",class:"detail-address-item"},{default:s(()=>[t(g,{value:a.value.location.detailAddress,"onUpdate:value":e[22]||(e[22]=l=>a.value.location.detailAddress=l),placeholder:"请输入详细地址",size:"large"},{suffix:s(()=>[t(R,{type:"text",onClick:M,loading:u.locationLoading,class:"location-btn",size:"small"},{icon:s(()=>[t(_(J))]),_:1},8,["loading"])]),_:1},8,["value"])]),_:1})])]))]),o("div",be,[e[60]||(e[60]=o("h3",{class:"section-title"},"资料上传",-1)),a.value.serviceType===3?(v(),m("div",ye,[o("div",Ie,[t(r,{label:"采购附件",name:["materials","attachments"],required:"",class:"upload-item"},{default:s(()=>[o("div",qe,[t(_(x),{value:a.value.materials.attachments,"onUpdate:value":e[23]||(e[23]=l=>a.value.materials.attachments=l),multiple:!0,"max-count":5,accept:".pdf,.doc,.docx,.xls,.xlsx","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[56]||(e[56]=o("div",{class:"upload-tip"},"支持PDF、DOC、DOCX、XLS、XLSX格式",-1))])]),_:1})])])):(v(),m("div",Ue,[o("div",we,[t(r,{label:"标的图片",name:["materials","images"],class:"upload-item"},{default:s(()=>[o("div",Ne,[t(_(x),{value:a.value.materials.images,"onUpdate:value":e[24]||(e[24]=l=>a.value.materials.images=l),multiple:!0,"max-count":10,accept:"image/*","list-type":"picture-card","file-type":"image","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[57]||(e[57]=o("div",{class:"upload-tip"},"最多可上传10张图片，支持JPG、PNG格式，单个文件不超过5MB",-1))])]),_:1})]),o("div",xe,[t(r,{label:"附件上传",name:["materials","attachments"],class:"upload-item"},{default:s(()=>[o("div",De,[t(_(x),{value:a.value.materials.attachments,"onUpdate:value":e[25]||(e[25]=l=>a.value.materials.attachments=l),multiple:!0,"max-count":5,accept:".pdf,.doc,.docx,.xls,.xlsx","return-url":!1,class:"upload-component upload-normal"},null,8,["value"]),e[58]||(e[58]=o("div",{class:"upload-tip"},"支持PDF、DOC、DOCX、XLS、XLSX格式",-1))])]),_:1})]),a.value.serviceType===1?(v(),m("div",Pe,[t(r,{label:"委托单上传",name:["materials","entrustDocument"],required:"",class:"upload-item"},{default:s(()=>[o("div",ze,[t(_(x),{value:a.value.materials.entrustDocument,"onUpdate:value":e[26]||(e[26]=l=>a.value.materials.entrustDocument=l),multiple:!1,"max-count":1,accept:".pdf,.doc,.docx","return-url":!1,class:"upload-component upload-entrust"},null,8,["value"]),e[59]||(e[59]=o("div",{class:"upload-tip"},"请上传正式的委托单文件",-1))])]),_:1})])):N("",!0),o("div",Ce,[t(r,{label:"特殊说明",name:"materials.specialNote",class:"form-item-full"},{default:s(()=>[t(B,{value:a.value.materials.specialNote,"onUpdate:value":e[27]||(e[27]=l=>a.value.materials.specialNote=l),placeholder:"请输入特殊说明（如有）",rows:3,size:"large"},null,8,["value"])]),_:1})])]))])]),_:1},8,["model"])])}}}),Fa=V(Te,[["__scopeId","data-v-3bd1d00b"]]);export{Fa as default};
