import{d as v,ag as o,aB as g,ar as p,aD as t,k as e,at as i,aq as h,F as z,aC as k,G as S}from"./vue-vendor-dy9k-Yad.js";import{C as A}from"./index-LCGLvkB3.js";import{A as w,bp as P,bq as W,br as $,bs as j,bt as q,bu as B,bv as G}from"./antd-vue-vendor-me9YkNVC.js";import{aU as T,ai as N,c as Q,o as V,a as D}from"./index-CCWaWN5g.js";import{P as E}from"./index-CtJ0w2CP.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const L=v({components:{PageWrapper:E,CollapseContainer:A,GithubFilled:G,QqCircleFilled:B,WechatFilled:q,AlipayCircleFilled:j,IeCircleFilled:$,TaobaoCircleFilled:W,CodepenCircleFilled:P,Icon:Q,Alert:w,IconPicker:N,SvgIcon:T},setup(){return{toIconify:()=>{V("https://iconify.design/")}}}}),U={class:"flex justify-around"},H={class:"flex justify-around flex-wrap"},J={class:"flex justify-around flex-wrap"},K={class:"flex justify-around flex-wrap"},M={class:"flex justify-around flex-wrap"};function O(d,s,R,X,Y,Z){const _=o("GithubFilled"),m=o("QqCircleFilled"),f=o("WechatFilled"),u=o("AlipayCircleFilled"),C=o("IeCircleFilled"),y=o("TaobaoCircleFilled"),F=o("CodepenCircleFilled"),n=o("CollapseContainer"),l=o("Icon"),a=o("SvgIcon"),c=o("IconPicker"),I=o("Alert"),x=o("a-button"),b=o("PageWrapper");return p(),g(b,{title:"Icon组件示例"},{default:t(()=>[e(n,{title:"Antv Icon使用 (直接按需引入相应组件即可)"},{default:t(()=>[i("div",U,[e(_,{style:{fontSize:"30px"}}),e(m,{style:{fontSize:"30px"}}),e(f,{style:{fontSize:"30px"}}),e(u,{style:{fontSize:"30px"}}),e(C,{style:{fontSize:"30px"}}),e(y,{style:{fontSize:"30px"}}),e(F,{style:{fontSize:"30px"}})])]),_:1}),e(n,{title:"IconIfy 组件使用",class:"my-5"},{default:t(()=>[i("div",H,[e(l,{icon:"ion:layers-outline",size:30}),e(l,{icon:"ion:bar-chart-outline",size:30}),e(l,{icon:"ion:tv-outline",size:30}),e(l,{icon:"ion:settings-outline",size:30})])]),_:1}),e(n,{title:"svg 雪碧图",class:"my-5"},{default:t(()=>[i("div",J,[e(a,{name:"test",size:"32"}),(p(),h(z,null,k(6,r=>e(a,{key:r,name:`dynamic-avatar-${r}`,size:"32"},null,8,["name"])),64))])]),_:1}),e(n,{title:"图标选择器(Iconify)",class:"my-5"},{default:t(()=>[i("div",K,[e(c)])]),_:1}),e(n,{title:"图标选择器(Svg)",class:"my-5"},{default:t(()=>[i("div",M,[e(c,{mode:"svg"})])]),_:1}),e(I,{"show-icon":"",message:"推荐使用Iconify组件",description:"Icon组件基本包含所有的图标,在下面网址内你可以查询到你想要的任何图标。并且打包只会打包所用到的图标。"}),e(x,{type:"link",onClick:d.toIconify},{default:t(()=>s[0]||(s[0]=[S(" Iconify 图标大全 ")])),_:1,__:[0]},8,["onClick"])]),_:1})}const Ce=D(L,[["render",O]]);export{Ce as default};
