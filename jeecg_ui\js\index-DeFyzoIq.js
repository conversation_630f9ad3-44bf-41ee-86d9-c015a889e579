import{d as w,f as m,u as g,ag as a,aB as C,ar as V,aD as n,at as u,k as r,G as d}from"./vue-vendor-dy9k-Yad.js";import{a as M,M as D}from"./index-mbACBRQ9.js";import{P as h}from"./index-CtJ0w2CP.js";import{J as b}from"./antd-vue-vendor-me9YkNVC.js";import{a as B}from"./index-CCWaWN5g.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const P=w({components:{MarkDown:D,PageWrapper:h,MarkdownViewer:M,ACard:b},setup(){const o=m(null),e=m(`
# title

# content
`);function l(){const t=g(o);if(!t)return;t.getVditor().setTheme("dark")}function p(t){e.value=t}function s(){e.value=""}return{value:e,toggleTheme:l,markDownRef:o,handleChange:p,clearValue:s}}}),T={class:"mt-2"};function $(o,e,l,p,s,t){const i=a("a-button"),c=a("MarkDown"),f=a("MarkdownViewer"),k=a("a-card"),_=a("PageWrapper");return V(),C(_,{title:"MarkDown组件示例"},{default:n(()=>[u("div",null,[r(i,{onClick:o.toggleTheme,class:"mb-2",type:"primary"},{default:n(()=>e[1]||(e[1]=[d(" 黑暗主题 ")])),_:1,__:[1]},8,["onClick"]),r(i,{onClick:o.clearValue,class:"mb-2",type:"default"},{default:n(()=>e[2]||(e[2]=[d(" 清空内容 ")])),_:1,__:[2]},8,["onClick"]),r(c,{value:o.value,"onUpdate:value":e[0]||(e[0]=v=>o.value=v),onChange:o.handleChange,ref:"markDownRef",placeholder:"这是占位文本"},null,8,["value","onChange"])]),u("div",T,[r(k,{title:"Markdown Viewer 组件演示"},{default:n(()=>[r(f,{value:o.value},null,8,["value"])]),_:1})])]),_:1})}const X=B(P,[["render",$]]);export{X as default};
