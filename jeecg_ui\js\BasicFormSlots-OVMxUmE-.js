import{d as c,ag as u,aB as d,ar as f,aD as m,k as e,at as i,u as a}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{c as _}from"./JAddInput-CxJ-JBK-.js";import{u as v}from"./useForm-CgkFTrrO.js";import{B}from"./BasicForm-DBcXiHk0.js";import{a as b}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";const x=c({__name:"BasicFormSlots",setup(h){const n=[{field:"name",label:"姓名",component:"Input"},{field:"phone",label:"联系方式",component:"Input",slot:"phone"},{field:"feedback",label:"问题反馈",component:"InputTextArea",slot:"feedback"}],[s]=v({schemas:n,showResetButton:!1,labelWidth:"150px",submitButtonOptions:{text:"提交",preIcon:""},actionColOptions:{span:17}});return(k,p)=>{const l=u("a-input");return f(),d(a(B),{onRegister:a(s),style:{"margin-top":"20px"}},{phone:m(({model:o,field:t})=>[e(l,{value:o[t],"onUpdate:value":r=>o[t]=r,placeholder:"请输入手机号"},null,8,["value","onUpdate:value"]),p[0]||(p[0]=i("span",{class:"font-color"},"请输入您的手机号，方便我们联系您",-1))]),feedback:m(({model:o,field:t})=>[e(_,{value:o[t],"onUpdate:value":r=>o[t]=r,placeholder:"请输入问题反馈"},null,8,["value","onUpdate:value"]),p[1]||(p[1]=i("span",{class:"font-color"},"请您图文并茂，方便我们了解问题并及时反馈",-1))]),_:1},8,["onRegister"])}}}),Fo=b(x,[["__scopeId","data-v-d318eb9e"]]);export{Fo as default};
