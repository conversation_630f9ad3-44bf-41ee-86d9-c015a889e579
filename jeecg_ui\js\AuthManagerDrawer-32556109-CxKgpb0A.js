import{d as b,f as i,e as C,ag as o,aB as D,ar as g,aD as m,k as r,aq as F,ah as I,F as v}from"./vue-vendor-dy9k-Yad.js";import{u as w,B as T}from"./index-JbqXEynz.js";import B from"./AuthFieldConfig-f1e224cc-BiNWYu_4.js";import A from"./AuthButtonConfig-d5bffca0-g2rFBgWF.js";import k from"./AuthDataConfig-d3b7afa4-Dem3bbP6.js";import{cs as K}from"./index-CCWaWN5g.js";import"./index-BkGZ5fiW.js";import"./auth.api-53df4c33-CWNFk1-w.js";import"./auth.data-626c5083-DVuUJlaU.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicTable-xCEZpGLb.js";import"./injectionKey-DPVn4AgL.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./BasicForm-DBcXiHk0.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";const R=b({name:"AuthManagerDrawer",components:{BasicDrawer:T,AuthFieldConfig:B,AuthButtonConfig:A,AuthDataConfig:k},props:{tableType:{type:Number,default:1}},emits:["register"],setup(t){const e=i(""),d=i(""),h=i([]),l=i("field"),s=i(1),n=C(()=>t.tableType==1||t.tableType==2),[a,{closeDrawer:u}]=w(p=>{e.value=p.cgformId,d.value=e.value+"?"+new Date().getTime(),l.value="field",s.value=p.tableType});function c(){u()}return{activeKey:l,cgformId:e,headId:d,authFields:h,hasDataAuth:n,onClose:c,registerDrawer:a,curTableType:s}}});function _(t,e,d,h,l,s){const n=o("AuthFieldConfig"),a=o("a-tab-pane"),u=o("AuthButtonConfig"),c=o("AuthDataConfig"),p=o("a-tabs"),y=o("BasicDrawer");return g(),D(y,{onRegister:t.registerDrawer,title:"权限管理",width:800,onClose:t.onClose},{default:m(()=>[r(p,{activeKey:t.activeKey,"onUpdate:activeKey":e[1]||(e[1]=f=>t.activeKey=f)},{default:m(()=>[r(a,{tab:"字段权限",key:"field",forceRender:""},{default:m(()=>[r(n,{headId:t.headId,authFields:t.authFields,"onUpdate:authFields":e[0]||(e[0]=f=>t.authFields=f)},null,8,["headId","authFields"])]),_:1}),t.hasDataAuth?(g(),F(v,{key:0},[r(a,{tab:"按钮权限",key:"button",forceRender:""},{default:m(()=>[r(u,{headId:t.headId,tableType:t.curTableType},null,8,["headId","tableType"])]),_:1}),r(a,{tab:"数据权限",key:"data",forceRender:""},{default:m(()=>[r(c,{cgformId:t.cgformId,authFields:t.authFields},null,8,["cgformId","authFields"])]),_:1})],64)):I("",!0)]),_:1},8,["activeKey"])]),_:1},8,["onRegister","onClose"])}const zt=K(R,[["render",_]]);export{zt as default};
