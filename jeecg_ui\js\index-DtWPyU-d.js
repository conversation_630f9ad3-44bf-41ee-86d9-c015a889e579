var b=(L,m,t)=>new Promise((e,n)=>{var u=r=>{try{s(t.next(r))}catch(p){n(p)}},f=r=>{try{s(t.throw(r))}catch(p){n(p)}},s=r=>r.done?e(r.value):Promise.resolve(r.value).then(u,f);s((t=t.apply(L,m)).next())});import{d as x,f as E,w as D,n as M,ag as R,aq as d,ar as _,k as i,F as T,aC as A,q as I,B as S,u as o,aD as a,G as y,au as h}from"./vue-vendor-dy9k-Yad.js";import V from"./DetailModal-DYkprnjD.js";import{u as j}from"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{j as B,bL as N,N as q,ad as F,u as $}from"./index-CCWaWN5g.js";import{getColumns as G}from"./data-WLa0JRnp.js";import{h as H}from"./antd-vue-vendor-me9YkNVC.js";import{Q as K}from"./componentMap-Bkie1n3v.js";import Q from"./BasicTable-xCEZpGLb.js";import"./index-Dce_QJ6p.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const z=()=>B.get({url:"/error"}),J={class:"p-4"},O=["src"],to=x({__name:"index",setup(L){const m=E(),t=E([]),{t:e}=q(),n=N(),[u,{setTableData:f}]=j({title:e("sys.errorLog.tableTitle"),columns:G(),actionColumn:{width:80,title:"Action",dataIndex:"action",slots:{customRender:"action"}}}),[s,{openModal:r}]=F();D(()=>n.getErrorLogInfoList,l=>{M(()=>{f(H(l))})},{immediate:!0});const{createMessage:p}=$();function w(l){m.value=l,r(!0)}function v(){throw new Error("fire vue error!")}function C(){t.value.push(`${new Date().getTime()}.png`)}function k(){return b(this,null,function*(){yield z()})}return(l,P)=>{const g=R("a-button");return _(),d("div",J,[(_(!0),d(T,null,A(t.value,c=>I((_(),d("img",{key:c,src:c},null,8,O)),[[S,!1]])),128)),i(V,{info:m.value,onRegister:o(s)},null,8,["info","onRegister"]),i(o(Q),{onRegister:o(u),class:"error-handle-table"},{toolbar:a(()=>[i(g,{onClick:v,type:"primary"},{default:a(()=>[y(h(o(e)("sys.errorLog.fireVueError")),1)]),_:1}),i(g,{onClick:C,type:"primary"},{default:a(()=>[y(h(o(e)("sys.errorLog.fireResourceError")),1)]),_:1}),i(g,{onClick:k,type:"primary"},{default:a(()=>[y(h(o(e)("sys.errorLog.fireAjaxError")),1)]),_:1})]),action:a(({record:c})=>[i(o(K),{actions:[{label:o(e)("sys.errorLog.tableActionDesc"),onClick:w.bind(null,c)}]},null,8,["actions"])]),_:1},8,["onRegister"])])}}});export{to as default};
