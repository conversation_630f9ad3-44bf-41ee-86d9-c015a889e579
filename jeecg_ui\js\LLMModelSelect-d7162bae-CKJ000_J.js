import{d as Y,f as Z,e as L,ag as m,aB as $,ar as F,aD as t,k as e,at as a,aq as ee,F as le,aC as ae,au as te,H as x,u as o,aJ as f,aK as v}from"./vue-vendor-dy9k-Yad.js";import{j as ne}from"./index-CCWaWN5g.js";import{h as U,o as se}from"./antd-vue-vendor-me9YkNVC.js";import{s as ce}from"./_plugin-vue_export-helper-dad06003-lGy7RumW.js";import"./vxe-table-vendor-B22HppNm.js";var oe=Object.defineProperty,ie=Object.defineProperties,re=Object.getOwnPropertyDescriptors,N=Object.getOwnPropertySymbols,ue=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable,V=(r,n,s)=>n in r?oe(r,n,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[n]=s,B=(r,n)=>{for(var s in n||(n={}))ue.call(n,s)&&V(r,s,n[s]);if(N)for(var s of N(n))de.call(n,s)&&V(r,s,n[s]);return r},pe=(r,n)=>ie(r,re(n));const me={class:"model-params-popover"},fe={class:"setting-item"},ve={style:{width:"100%","text-align":"right"}},ye={class:"setting-item"},ge={class:"label"},he={class:"setting-item"},be={class:"label"},ke={class:"setting-item"},Pe={class:"label"},_e={class:"setting-item"},xe={class:"label"},qe={class:"setting-item"},we={class:"label"},je=Y({__name:"LLMModelSelect",props:{model:{type:Object,required:!0}},emits:["update:model"],setup(r,{emit:n}){const s=r,H=n,[,q,A]=g("temperature",{min:.1,max:1,step:.1},.7),[,w,M]=g("topP",{min:.1,max:1,step:.1},.7),[,j,D]=g("presencePenalty",{min:-2,max:2,step:.1},0),[,O,R]=g("frequencyPenalty",{min:-2,max:2,step:.1},0),[,z,S]=g("maxTokens",{min:1,max:16e3,step:1},520),T=[{name:"创意",icon:"fxemoji:star",params:{temperature:.8,topP:.9,presencePenalty:.1,frequencyPenalty:.1}},{name:"平衡",icon:"noto:balance-scale",params:{temperature:.5,topP:.8,presencePenalty:.2,frequencyPenalty:.3}},{name:"精确",icon:"twemoji:direct-hit",params:{temperature:.2,topP:.7,presencePenalty:.5,frequencyPenalty:.5}}];function J(i){const l=T[i];if(!l)return;const c=U(s.model);c.params=B(B({},se(c.params,...Object.keys(l.params))),l.params),C(c)}const y={temperature:"值越大，回复内容越赋有多样性创造性、随机性；设为0根据事实回答，希望得到精准答案应该降低该参数；日常聊天建议0.5-0.8。",topP:"值越小，Ai生成的内容越单调也越容易理解；值越大，Ai回复的词汇围越大，越多样化。",presencePenalty:"值越大，越能够让Ai更好地控制新话题的引入，建议微调或不变。",frequencyPenalty:"值越大，越能够让Ai更好地避免重复之前说过的话，建议微调或不变。",maxTokens:"设置Ai最大回复内容大小，会影响返回结果的长度。普通聊天建议500-800；短文生成建议800-2000；代码生成建议2000-3600；长文生成建议4000左右（或选择长回复模型)"},I=Z([]);function K(){ne.get({url:"/sys/dict/getDictItems/airag_model%20where%20model_type%20=%20'LLM',name,id"}).then(l=>{I.value=l,I.value.unshift({label:"请选择模型",value:""})})}K();function G(i,l){const c=U(s.model);c.modeId=i,c.params.model=i?l.label:"",C(c)}function C(i){H("update:model",i)}function g(i,l,c){const u=Q(i),p=L({get:()=>u.value!=null,set:h=>u.value=h?c:null});return[u,p,L(()=>pe(B({},l),{value:u.value,size:"small",disabled:!p.value,"onUpdate:value":h=>u.value=h}))]}function Q(i){return L({get:()=>s.model.params[i],set:l=>{const c=U(s.model);c.params[i]=l,C(c)}})}return(i,l)=>{const c=m("a-select"),u=m("Icon"),p=m("a-space"),h=m("a-select-option"),b=m("a-tooltip"),k=m("a-switch"),P=m("a-slider"),_=m("a-input-number"),W=m("a-button"),X=m("a-popover");return F(),$(p,null,{default:t(()=>[e(c,{value:r.model.modeId,options:I.value,style:{width:"520px"},onChange:G},null,8,["value","options"]),e(X,{trigger:"click",placement:"bottomRight"},{content:t(()=>[a("div",me,[a("div",fe,[a("div",ve,[e(c,{value:"加载预设",style:{width:"96px"},size:"small",onChange:J},{default:t(()=>[(F(),ee(le,null,ae(T,(d,E)=>e(h,{value:E,key:E},{default:t(()=>[e(p,null,{default:t(()=>[e(u,{icon:d.icon},null,8,["icon"]),a("span",null,te(d.name),1)]),_:2},1024)]),_:2},1032,["value"])),64))]),_:1})])]),a("div",ye,[a("div",ge,[l[5]||(l[5]=a("span",null,"模型温度",-1)),e(b,{title:y.temperature},{default:t(()=>[e(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),e(p,null,{default:t(()=>[e(k,{checked:o(q),"onUpdate:checked":l[0]||(l[0]=d=>x(q)?q.value=d:null),size:"small"},null,8,["checked"]),e(P,f(v(o(A))),null,16),e(_,f(v(o(A))),null,16)]),_:1})]),a("div",he,[a("div",be,[l[6]||(l[6]=a("span",null,"词汇属性",-1)),e(b,{title:y.topP},{default:t(()=>[e(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),e(p,null,{default:t(()=>[e(k,{checked:o(w),"onUpdate:checked":l[1]||(l[1]=d=>x(w)?w.value=d:null),size:"small"},null,8,["checked"]),e(P,f(v(o(M))),null,16),e(_,f(v(o(M))),null,16)]),_:1})]),a("div",ke,[a("div",Pe,[l[7]||(l[7]=a("span",null,"话题属性",-1)),e(b,{title:y.presencePenalty},{default:t(()=>[e(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),e(p,null,{default:t(()=>[e(k,{checked:o(j),"onUpdate:checked":l[2]||(l[2]=d=>x(j)?j.value=d:null),size:"small"},null,8,["checked"]),e(P,f(v(o(D))),null,16),e(_,f(v(o(D))),null,16)]),_:1})]),a("div",_e,[a("div",xe,[l[8]||(l[8]=a("span",null,"重复属性",-1)),e(b,{title:y.frequencyPenalty},{default:t(()=>[e(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),e(p,null,{default:t(()=>[e(k,{checked:o(O),"onUpdate:checked":l[3]||(l[3]=d=>x(O)?O.value=d:null),size:"small"},null,8,["checked"]),e(P,f(v(o(R))),null,16),e(_,f(v(o(R))),null,16)]),_:1})]),a("div",qe,[a("div",we,[l[9]||(l[9]=a("span",null,"最大回复",-1)),e(b,{title:y.maxTokens},{default:t(()=>[e(u,{icon:"ant-design:question-circle"})]),_:1},8,["title"])]),e(p,null,{default:t(()=>[e(k,{checked:o(z),"onUpdate:checked":l[4]||(l[4]=d=>x(z)?z.value=d:null),size:"small"},null,8,["checked"]),e(P,f(v(o(S))),null,16),e(_,f(v(o(S))),null,16)]),_:1})])])]),default:t(()=>[e(W,{preIcon:"ant-design:setting",style:{width:"40px"}})]),_:1})]),_:1})}}}),Ue=ce(je,[["__scopeId","data-v-62c1a8a7"]]);export{Ue as default};
