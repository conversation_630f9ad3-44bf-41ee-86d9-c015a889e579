import{d,f as _,ag as l,aq as f,ar as r,aB as p,ah as n,at as v,G as e,k as a,aD as m,F as x}from"./vue-vendor-dy9k-Yad.js";import k from"./IndexDef-BXQE8FHO.js";import g from"./IndexChart-Z0YJWsn_.js";import y from"./IndexBdc-DzrVIEXP.js";import B from"./IndexTask-C7oJyfyK.js";import"./GrowCard-di6_Wans.js";import"./index-CWWAi5V9.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./data-BpJ37qIE.js";import"./SiteAnalysis-BTdEGkmJ.js";import"./VisitAnalysis-DX-4-4QD.js";import"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";import"./props-BGjQktHt.js";import"./VisitAnalysisBar-EwDB5Y_K.js";import"./VisitSource-CJ88VLm9.js";import"./VisitRadar-D0zzxDQN.js";import"./SalesProductPie-kiTmsLZX.js";import"./ChartGroupCard-BvAE9ozT.js";import"./SingleLine-kPL5Z-yi.js";import"./Bar-Tb_Evzfu.js";import"./SaleTabCard-BWBo62rR.js";import"./RankList-gPMCy9y-.js";import"./LineMulti-Bb6oGGI0.js";import"./BdcTabCard-Dqek2Vfi.js";import"./Gauge-DelrgGV_.js";import"./QuickNav-u-J2mPxn.js";import"./JEllipsis-BsXuWNHJ.js";import"./areaDataUtil-BXVjRArW.js";const C={style:{width:"100%","text-align":"right","margin-top":"20px"}},et=d({__name:"index",setup(N){const o=_(0);return(V,t)=>{const i=l("a-radio"),u=l("a-radio-group");return r(),f(x,null,[o.value===0?(r(),p(g,{key:0})):n("",!0),o.value===1?(r(),p(k,{key:1})):n("",!0),o.value==2?(r(),p(y,{key:2})):n("",!0),o.value==3?(r(),p(B,{key:3})):n("",!0),v("div",C,[t[5]||(t[5]=e(" 首页主题： ")),a(u,{value:o.value,"onUpdate:value":t[0]||(t[0]=s=>o.value=s)},{default:m(()=>[a(i,{value:0},{default:m(()=>t[1]||(t[1]=[e("默认")])),_:1,__:[1]}),a(i,{value:1},{default:m(()=>t[2]||(t[2]=[e("销量统计")])),_:1,__:[2]}),a(i,{value:2},{default:m(()=>t[3]||(t[3]=[e("业务统计")])),_:1,__:[3]}),a(i,{value:3},{default:m(()=>t[4]||(t[4]=[e("我的任务")])),_:1,__:[4]})]),_:1},8,["value"])])],64)}}});export{et as default};
