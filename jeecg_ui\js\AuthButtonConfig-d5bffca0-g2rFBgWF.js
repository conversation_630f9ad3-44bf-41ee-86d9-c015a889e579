import{d as $,f as g,w as D,ag as x,aq as L,ar as P,k,aD as I,G as j}from"./vue-vendor-dy9k-Yad.js";import{h as z}from"./antd-vue-vendor-me9YkNVC.js";import{u as K}from"./index-BkGZ5fiW.js";import{h as O,p as U,g as V}from"./auth.api-53df4c33-CWNFk1-w.js";import{y as A,C as E}from"./auth.data-626c5083-DVuUJlaU.js";import{cs as G}from"./index-CCWaWN5g.js";import H from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";var y=(a,p,s)=>new Promise((c,d)=>{var b=r=>{try{l(s.next(r))}catch(m){d(m)}},f=r=>{try{l(s.throw(r))}catch(m){d(m)}},l=r=>r.done?c(r.value):Promise.resolve(r.value).then(b,f);l((s=s.apply(a,p)).next())});const J=$({name:"AuthButtonConfig",components:{BasicTable:H},props:{headId:{type:String,default:"",required:!0},tableType:{type:Number,default:1}},setup(a){const p=g(""),s=g(2),c=g(3),d=g(5),[b,{reload:f,getTableRef:l,setPagination:r}]=K({api:T,rowKey:"code",bordered:!0,columns:A,showIndexColumn:!1});D(()=>a.headId,t=>{p.value=t.split("?")[0],l().value&&r({current:1,pageSize:10}),f().catch(()=>null)},{immediate:!0});const m=t=>{const e=z(E);if(t.mainRelationType!=null&&t.mainThemeTemplate!=null&&a.tableType==3){let i=[];switch(t.mainThemeTemplate){case"normal":case"innerTable":case"tab":t.mainRelationType==1?i=[]:i=e.filter(o=>["add","update","batch_delete"].includes(o.code));break;case"erp":i=e.filter(o=>!["super_query"].includes(o.code));break}return i}else return e};function T(t){return y(this,null,function*(){let e=yield O(p.value,t),{authList:i,buttonList:o}=e,u=[];const v=m(e);for(let n of v){const h=o.findIndex(C=>C.buttonCode===n.code),w={};h!==-1&&(w.title=o[h].buttonName,o.splice(h,1));let _={status:0,page:c.value},q=i.find(C=>C.code==n.code);Object.assign(n,_,q,w),u.push(n)}if(a.tableType==3){const n=u.findIndex(h=>h.code==="super_query");n!=-1&&u.splice(n,1)}return B(i,o,u)})}function B(t,e,i){for(let o of e){let u=t.find(n=>n.code==o.buttonCode),v={code:o.buttonCode,title:o.buttonName,status:0,page:o.buttonStyle=="form"?d.value:c.value};i.push(Object.assign(v,u))}return i}function N(t,e){return y(this,null,function*(){t?R(e):S(e)})}function R(t){return y(this,null,function*(){let e=yield U({id:t.id,code:t.code,page:t.page,cgformId:p.value,type:s.value,control:5,status:1});t.id=e.id,t.status=1})}function S(t){return y(this,null,function*(){yield V(t.id),t.status=0})}return{registerTable:b,onUpdateStatus:N}}}),M={class:"auth-field-config"};function F(a,p,s,c,d,b){const f=x("a-switch"),l=x("BasicTable");return P(),L("div",M,[k(l,{onRegister:a.registerTable},{switch:I(({text:r,record:m})=>[k(f,{size:"small",checked:m.status===1,onChange:T=>a.onUpdateStatus(T,m)},null,8,["checked","onChange"])]),control:I(()=>p[0]||(p[0]=[j(" 可见 ")])),_:1},8,["onRegister"])])}const te=G(J,[["render",F]]);export{te as default};
