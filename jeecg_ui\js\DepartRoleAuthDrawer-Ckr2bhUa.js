var E=(O,j,s)=>new Promise((k,p)=>{var _=r=>{try{f(s.next(r))}catch(y){p(y)}},o=r=>{try{f(s.throw(r))}catch(y){p(y)}},f=r=>r.done?k(r.value):Promise.resolve(r.value).then(_,o);f((s=s.apply(O,j)).next())});import{d as X,f as u,ag as m,aq as Z,ar as x,F as ee,k as c,u as i,aD as n,at as S,aB as B,ah as te,au as v,G as d}from"./vue-vendor-dy9k-Yad.js";import{_ as ae}from"./index-BtIdS_Qz.js";import{u as le,a as ne,B as oe}from"./index-JbqXEynz.js";import{N as se,u as re,c9 as F,c6 as ce,a as ue}from"./index-CCWaWN5g.js";import ie from"./DepartRoleDataRuleDrawer-Qn1FPJB7.js";import{a as de,b as pe,c as fe}from"./depart.user.api-D_abnxSU.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./bem-sRx7x0Ii.js";import"./vxe-table-vendor-B22HppNm.js";import"./props-qAqCef5R.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContextMenu-BU2ycxls.js";const me=X({__name:"DepartRoleAuthDrawer",emits:["register"],setup(O){const{createMessage:j}=re(),s=u(!1),k=u(""),p=u(""),_=u([]),o=u([]),f=u([]),r=u([]),y=u([]),C=u([]),R=u(!1),{t:g}=se(),[V,{closeDrawer:$}]=le(e=>{p.value=e.record.id,k.value=e.record.departId,L({success:t=>{const l=localStorage.getItem(F);if(l){const a=JSON.parse(l);a.level&&N({key:a.level}),a.expand&&N({key:a.expand})}}})}),[q,J]=ne();function L(){return E(this,arguments,function*(e={}){var t;try{s.value=!0;const{ids:l,treeList:a}=yield de({departId:k.value});l.length>0?(C.value=l,(t=e.success)==null||t.call(e,l),_.value=ce(a),o.value=yield pe({roleId:p.value}),f.value=[o.value]):K()}finally{s.value=!1}})}function K(){_.value=[],r.value=[],o.value=[],f.value=[],s.value=!1}function P(e,t){if(R.value)o.value=e.checked?e.checked:e;else{const l=U(t.node,"children","key");if(t.checked)o.value=[...new Set([...o.value,...l])];else{const a=G(o.value,l);o.value=a}}}function G(e,t){const l={};for(const a of t)l[a]=!0;return e.filter(a=>!l[a])}function U(e,t,l){var A;const a=[];a.push(e[l]);const w=b=>{b.forEach(D=>{var I;a.push(D[l]),(I=D[t])!=null&&I.length&&w(D[t])})};return(A=e[t])!=null&&A.length&&w(e[t]),a}function H(e){r.value=e}function Y(e,{selectedNodes:t}){var l;if((l=t[0])!=null&&l.ruleFlag){let a=e[0];J.openDrawer(!0,{roleId:p,departId:k,functionId:a})}y.value=[]}function z(){K(),$()}function Q(){K()}function M(e){return E(this,null,function*(){try{s.value=!0;let t={roleId:p.value,permissionIds:o.value.join(","),lastpermissionIds:f.value.join(",")};yield fe(t),e&&z()}finally{s.value=!1,e||L()}})}function N({key:e}){e==="checkAll"?o.value=C.value:e==="cancelCheck"?o.value=[]:e==="openAll"?(r.value=C.value,h("expand","openAll")):e==="closeAll"?(r.value=[],h("expand","closeAll")):e==="relation"?(R.value=!1,h("level","relation")):(R.value=!0,h("level","standAlone"))}const h=(e,t)=>{const l=localStorage.getItem(F),a=l?JSON.parse(l):{};a[e]=t,localStorage.setItem(F,JSON.stringify(a))};return(e,t)=>{const l=m("Icon"),a=m("a-menu-item"),w=m("a-menu"),A=m("a-dropdown"),b=m("a-empty"),D=m("a-spin"),I=m("a-button");return x(),Z(ee,null,[c(i(oe),{width:650,loading:s.value,showFooter:"",okText:"保存并关闭",onOk:t[1]||(t[1]=T=>M(!0)),onClose:Q,onRegister:i(V)},{title:n(()=>[t[4]||(t[4]=d(" 部门角色权限配置 ")),c(A,null,{overlay:n(()=>[c(w,{onClick:N},{default:n(()=>[c(a,{key:"checkAll"},{default:n(()=>[d(v(i(g)("component.tree.selectAll")),1)]),_:1}),c(a,{key:"cancelCheck"},{default:n(()=>[d(v(i(g)("component.tree.unSelectAll")),1)]),_:1}),t[2]||(t[2]=S("div",{class:"line"},null,-1)),c(a,{key:"openAll"},{default:n(()=>[d(v(i(g)("component.tree.expandAll")),1)]),_:1}),c(a,{key:"closeAll"},{default:n(()=>[d(v(i(g)("component.tree.unExpandAll")),1)]),_:1}),t[3]||(t[3]=S("div",{class:"line"},null,-1)),c(a,{key:"relation"},{default:n(()=>[d(v(i(g)("component.tree.checkStrictly")),1)]),_:1}),c(a,{key:"standAlone"},{default:n(()=>[d(v(i(g)("component.tree.checkUnStrictly")),1)]),_:1})]),_:1,__:[2,3]})]),default:n(()=>[c(l,{icon:"ant-design:more-outlined",class:"more-icon"})]),_:1})]),centerFooter:n(()=>[c(I,{type:"primary",loading:s.value,ghost:"",onClick:t[0]||(t[0]=T=>M(!1))},{default:n(()=>t[5]||(t[5]=[d("仅保存")])),_:1,__:[5]},8,["loading"])]),default:n(()=>[S("div",null,[c(D,{spinning:s.value},{default:n(()=>[_.value.length>0?(x(),B(i(ae),{key:0,title:"所拥有的部门权限",checkable:"",treeData:_.value,checkedKeys:o.value,selectedKeys:y.value,expandedKeys:r.value,checkStrictly:!0,clickRowToExpand:!1,onCheck:P,onExpand:H,onSelect:Y},{title:n(({slotTitle:T,ruleFlag:W})=>[S("span",null,v(T),1),W?(x(),B(l,{key:0,icon:"ant-design:align-left-outlined",style:{"margin-left":"5px",color:"red"}})):te("",!0)]),_:1},8,["treeData","checkedKeys","selectedKeys","expandedKeys"])):(x(),B(b,{key:1,description:"无可配置部门权限"}))]),_:1},8,["spinning"])])]),_:1},8,["loading","onRegister"]),c(ie,{onRegister:i(q)},null,8,["onRegister"])],64)}}}),Ee=ue(me,[["__scopeId","data-v-b3474f0c"]]);export{Ee as default};
