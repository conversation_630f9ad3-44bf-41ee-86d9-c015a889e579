var Y=Object.defineProperty,Z=Object.defineProperties;var tt=Object.getOwnPropertyDescriptors;var v=Object.getOwnPropertySymbols;var et=Object.prototype.hasOwnProperty,ot=Object.prototype.propertyIsEnumerable;var B=(a,o,n)=>o in a?Y(a,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):a[o]=n,M=(a,o)=>{for(var n in o||(o={}))et.call(o,n)&&B(a,n,o[n]);if(v)for(var n of v(o))ot.call(o,n)&&B(a,n,o[n]);return a},L=(a,o)=>Z(a,tt(o));var c=(a,o,n)=>new Promise((R,w)=>{var u=d=>{try{g(n.next(d))}catch(_){w(_)}},S=d=>{try{g(n.throw(d))}catch(_){w(_)}},g=d=>d.done?R(d.value):Promise.resolve(d.value).then(u,S);g((n=n.apply(a,o)).next())});import{d as N,f as it,ag as h,aq as nt,ar as U,k as r,aD as p,u as l,aB as lt,ah as at,G as b}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{_ as rt,b as st,g as pt,a as K,c as dt,d as mt,s as ct,e as ut,l as ft,f as ht}from"./CategoryModal-CFkfezdO.js";import"./index-Diw57m_E.js";import{ad as gt,E as _t}from"./index-CCWaWN5g.js";import{useListPage as Ct}from"./useListPage-Soxgnx9a.js";import{Q as yt}from"./componentMap-Bkie1n3v.js";import bt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const wt=N({name:"system-category"}),ve=N(L(M({},wt),{setup(a){const o=it([]),{handleExportXls:n,handleImportXls:R}=_t(),[w,{openModal:u}]=gt(),{prefixCls:S,onExportXls:g,onImportXls:d,tableContext:_}=Ct({designScope:"category-template",tableProps:{title:"分类字典",api:ft,columns:ut,actionColumn:{width:180},formConfig:{schemas:ct},isTreeTable:!0},exportConfig:{name:"分类字典列表",url:mt},importConfig:{url:dt}}),[X,{reload:D,collapseAll:xt,updateTableDataRecord:E,findTableDataRecord:j,getDataSource:A},{rowSelection:F,selectedRowKeys:k}]=_;function V(){u(!0,{isUpdate:!1})}function P(e){return c(this,null,function*(){u(!0,{record:e,isUpdate:!0})})}function kt(e){return c(this,null,function*(){u(!0,{record:e,isUpdate:!0,hideFooter:!0})})}function q(e){return c(this,null,function*(){yield ht({id:e.id},I)})}function G(){return c(this,null,function*(){const e=k.value.filter(t=>!t.includes("loading"));yield st({ids:e},I)})}function I(){(k.value=[])&&D()}function H(e){u(!0,{record:e,isUpdate:!1})}function O(y){return c(this,arguments,function*({isUpdate:e,isSubAdd:t,values:i,expandedArr:C}){if(e)E(i.id,i);else if(!i.pid)D();else if(t)yield T(i.pid);else for(let m of l(C))yield T(m)})}function Q(e){x(e.items)&&$()}function $(){return c(this,null,function*(){if(l(o).length>0){const e=yield pt({parentIds:l(o).join(",")});if(e.success&&e.result.records.length>0){let t=e.result.records;const i=new Map;for(let m of t){let s=m.pid;if(l(o).includes(s)){let f=i.get(s);f==null&&(f=[]),f.push(m),i.set(s,f)}}let C=i,y=m=>{m&&m.forEach(s=>{l(o).includes(s.id)&&(s.children=x(C.get(s.id)),y(s.children))})};y(A())}}})}function x(e){if(e&&e.length>0)return e.map(t=>{if(t.hasChild=="1"){let i={id:t.id+"_loadChild",name:"loading...",isLoading:!0};t.children=[i]}return t})}function z(e,t){return c(this,null,function*(){if(e){if(o.value.push(t.id),t.children.length>0&&t.children[0].isLoading){let i=yield K({pid:t.id});i&&i.length>0?t.children=x(i):(t.children=null,t.hasChild="0")}}else{let i=o.value.indexOf(t.id);i>=0&&o.value.splice(i,1)}})}function T(e){return c(this,null,function*(){let t=j(e);o.value.includes(e)||o.value.push(e);let i=yield K({pid:e});i&&i.length>0?t.children=x(i):(t.children=null,t.hasChild="0"),E(e,t)})}function J(e){return[{label:"编辑",onClick:P.bind(null,e)},{label:"删除",popConfirm:{title:"确定删除吗?",confirm:q.bind(null,e)}},{label:"添加下级",onClick:H.bind(null,{pid:e.id})}]}return(e,t)=>{const i=h("a-button"),C=h("j-upload-button"),y=h("Icon"),m=h("a-menu-item"),s=h("a-menu"),f=h("a-dropdown");return U(),nt("div",null,[r(l(bt),{onRegister:l(X),rowSelection:l(F),expandedRowKeys:o.value,onExpand:z,onFetchSuccess:Q},{tableTitle:p(()=>[r(i,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:V},{default:p(()=>t[0]||(t[0]=[b(" 新增")])),_:1,__:[0]}),r(i,{type:"primary",preIcon:"ant-design:export-outlined",onClick:l(g)},{default:p(()=>t[1]||(t[1]=[b(" 导出")])),_:1,__:[1]},8,["onClick"]),r(C,{type:"primary",preIcon:"ant-design:import-outlined",onClick:l(d)},{default:p(()=>t[2]||(t[2]=[b("导入")])),_:1,__:[2]},8,["onClick"]),l(k).length>0?(U(),lt(f,{key:0},{overlay:p(()=>[r(s,null,{default:p(()=>[r(m,{key:"1",onClick:G},{default:p(()=>[r(y,{icon:"ant-design:delete-outlined"}),t[3]||(t[3]=b(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:p(()=>[r(i,null,{default:p(()=>[t[4]||(t[4]=b("批量操作 ")),r(y,{icon:"ant-design:down-outlined"})]),_:1,__:[4]})]),_:1})):at("",!0)]),action:p(({record:W})=>[r(l(yt),{actions:J(W)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection","expandedRowKeys"]),r(rt,{onRegister:l(w),onSuccess:O},null,8,["onRegister"])])}}}));export{ve as default};
