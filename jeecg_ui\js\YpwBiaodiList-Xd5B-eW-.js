var J=Object.defineProperty,W=Object.defineProperties;var Z=Object.getOwnPropertyDescriptors;var I=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,tt=Object.prototype.propertyIsEnumerable;var T=(r,t,o)=>t in r?J(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o,A=(r,t)=>{for(var o in t||(t={}))$.call(t,o)&&T(r,o,t[o]);if(I)for(var o of I(t))tt.call(t,o)&&T(r,o,t[o]);return r},E=(r,t)=>W(r,Z(t));var k=(r,t,o)=>new Promise((v,w)=>{var u=p=>{try{d(o.next(p))}catch(c){w(c)}},S=p=>{try{d(o.throw(p))}catch(c){w(c)}},d=p=>p.done?v(p.value):Promise.resolve(p.value).then(u,S);d((o=o.apply(r,t)).next())});import{d as R,r as M,f as ot,ag as m,v as et,aq as it,ar as l,k as a,aD as n,u as s,q as g,aB as _,ah as rt,G as f}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{useListPage as nt}from"./useListPage-Soxgnx9a.js";import pt from"./YpwBiaodiModal-crkzRapF.js";import{s as at,b as st,g as mt,a as lt,c as ut,d as dt,l as ct,e as _t}from"./YpwBiaodi.api-5rGT4IC-.js";import{ah as ft,ad as wt,a as yt}from"./index-CCWaWN5g.js";import{Q as bt}from"./componentMap-Bkie1n3v.js";import gt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const ht=R({name:"ypw-ypwBiaodi"}),Ct=R(E(A({},ht),{setup(r){const t=M({}),o=ot([]),v=ft(),[w,{openModal:u}]=wt(),{prefixCls:S,tableContext:d,onExportXls:p,onImportXls:c}=nt({tableProps:{title:"标的主表",api:ct,columns:dt,canResize:!1,formConfig:{schemas:ut,autoSubmitOnEnter:!0,showAdvancedButton:!0,fieldMapToNumber:[],fieldMapToTime:[]},actionColumn:{width:120,fixed:"right"},beforeFetch:e=>Object.assign(e,t)},exportConfig:{name:"标的主表",url:lt,params:t},importConfig:{url:mt,success:y}}),[U,{reload:D},{rowSelection:q,selectedRowKeys:h}]=d,F=M(at);function j(e){Object.keys(e).map(i=>{t[i]=e[i]}),D()}function N(){u(!0,{isUpdate:!1,showFooter:!0})}function O(e){u(!0,{record:e,isUpdate:!0,showFooter:!0})}function Q(e){u(!0,{record:e,isUpdate:!0,showFooter:!1})}function K(e){return k(this,null,function*(){yield _t({id:e.id},y)})}function L(){return k(this,null,function*(){yield st({ids:h.value},y)})}function y(){(h.value=[])&&D()}function P(e){return[{label:"编辑",onClick:O.bind(null,e),auth:"ypw:ypw_biaodi:edit"}]}function V(e){return[{label:"详情",onClick:Q.bind(null,e)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:K.bind(null,e),placement:"topLeft"},auth:"ypw:ypw_biaodi:delete"}]}return(e,i)=>{const C=m("a-button"),X=m("j-upload-button"),B=m("Icon"),Y=m("a-menu-item"),z=m("a-menu"),G=m("a-dropdown"),H=m("super-query"),b=et("auth");return l(),it("div",null,[a(s(gt),{onRegister:s(U),rowSelection:s(q)},{tableTitle:n(()=>[g((l(),_(C,{type:"primary",onClick:N,preIcon:"ant-design:plus-outlined"},{default:n(()=>i[0]||(i[0]=[f(" 新增")])),_:1,__:[0]})),[[b,"ypw:ypw_biaodi:add"]]),g((l(),_(C,{type:"primary",preIcon:"ant-design:export-outlined",onClick:s(p)},{default:n(()=>i[1]||(i[1]=[f(" 导出")])),_:1,__:[1]},8,["onClick"])),[[b,"ypw:ypw_biaodi:exportXls"]]),g((l(),_(X,{type:"primary",preIcon:"ant-design:import-outlined",onClick:s(c)},{default:n(()=>i[2]||(i[2]=[f("导入")])),_:1,__:[2]},8,["onClick"])),[[b,"ypw:ypw_biaodi:importExcel"]]),s(h).length>0?(l(),_(G,{key:0},{overlay:n(()=>[a(z,null,{default:n(()=>[a(Y,{key:"1",onClick:L},{default:n(()=>[a(B,{icon:"ant-design:delete-outlined"}),i[3]||(i[3]=f(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:n(()=>[g((l(),_(C,null,{default:n(()=>[i[4]||(i[4]=f("批量操作 ")),a(B,{icon:"mdi:chevron-down"})]),_:1,__:[4]})),[[b,"ypw:ypw_biaodi:deleteBatch"]])]),_:1})):rt("",!0),a(H,{config:F,onSearch:j},null,8,["config"])]),action:n(({record:x})=>[a(s(bt),{actions:P(x),dropDownActions:V(x)},null,8,["actions","dropDownActions"])]),bodyCell:n(({column:x,record:xt,index:kt,text:vt})=>i[5]||(i[5]=[])),_:1},8,["onRegister","rowSelection"]),a(pt,{onRegister:s(w),onSuccess:y},null,8,["onRegister"])])}}})),Ro=yt(Ct,[["__scopeId","data-v-add43e7b"]]);export{Ro as default};
