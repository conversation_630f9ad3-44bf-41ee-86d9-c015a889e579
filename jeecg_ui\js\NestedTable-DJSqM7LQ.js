import{d as x,ag as o,aB as g,ar as s,aD as a,k as n,aq as r,ah as l,G as u,at as i}from"./vue-vendor-dy9k-Yad.js";import{a as I}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const N=[{title:"Name",dataIndex:"name",key:"name"},{title:"Platform",dataIndex:"platform",key:"platform"},{title:"Version",dataIndex:"version",key:"version"},{title:"Upgraded",dataIndex:"upgradeNum",key:"upgradeNum"},{title:"Creator",dataIndex:"creator",key:"creator"},{title:"Date",dataIndex:"createdAt",key:"createdAt"},{title:"Action",key:"operation"}],c=[];for(let e=0;e<3;++e)c.push({key:e,name:"<PERSON>reem",platform:"iOS",version:"10.3.4.5654",upgradeNum:500,creator:"<PERSON>",createdAt:"2014-12-24 23:12:00"});const b=[{title:"Date",dataIndex:"date",key:"date"},{title:"Name",dataIndex:"name",key:"name"},{title:"Status",dataIndex:"state",key:"state"},{title:"Upgrade Status",dataIndex:"upgradeNum",key:"upgradeNum"},{title:"Action",dataIndex:"operation",key:"operation"}],_=[];for(let e=0;e<3;++e)_.push({key:e,date:"2014-12-24 23:12:00",name:"This is production name",upgradeNum:"Upgraded: 56"});const C=x({components:{},setup(){return{data:c,columns:N,innerColumns:b,innerData:_}}}),A={key:0},v={key:0},B={key:1,class:"table-operation"};function D(e,t,S,V,w,$){const k=o("a-badge"),m=o("a-menu-item"),y=o("a-menu"),f=o("a-dropdown"),p=o("a-table");return s(),g(p,{columns:e.columns,"data-source":e.data,class:"components-table-demo-nested"},{bodyCell:a(({column:d})=>[d.key==="operation"?(s(),r("a",A,"Publish")):l("",!0)]),expandedRowRender:a(()=>[n(p,{columns:e.innerColumns,"data-source":e.innerData,pagination:!1},{bodyCell:a(({column:d})=>[d.dataIndex==="state"?(s(),r("span",v,[n(k,{status:"success"}),t[0]||(t[0]=u(" Finished "))])):l("",!0),d.dataIndex==="operation"?(s(),r("span",B,[t[4]||(t[4]=i("a",null,"Pause",-1)),t[5]||(t[5]=i("a",null,"Stop",-1)),n(f,null,{overlay:a(()=>[n(y,null,{default:a(()=>[n(m,null,{default:a(()=>t[1]||(t[1]=[u("Action 1")])),_:1,__:[1]}),n(m,null,{default:a(()=>t[2]||(t[2]=[u("Action 2")])),_:1,__:[2]})]),_:1})]),default:a(()=>[t[3]||(t[3]=i("a",null," More ",-1))]),_:1,__:[3]})])):l("",!0)]),_:1},8,["columns","data-source"])]),_:1},8,["columns","data-source"])}const q=I(C,[["render",D]]);export{q as default};
