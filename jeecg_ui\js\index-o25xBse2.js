import{d as f,f as _,e as g,ag as p,aq as u,ar as a,k as o,aD as n,aB as y,aQ as l}from"./vue-vendor-dy9k-Yad.js";import b from"./JeecgComponents-DuTX5o0_.js";import C from"./JEditorDemo-CmoDNr-J.js";import k from"./JCodeEditDemo-DUs9c_YC.js";import D from"./ImgDragSort-BZ9PHP0F.js";import v from"./ImgTurnPage-D3yMUKQ5.js";import J from"./JeecgPdfView-yE-cOYEY.js";import h from"./JUploadDemo-CsyCS9CR.js";import{a as $}from"./index-CCWaWN5g.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./antd-vue-vendor-me9YkNVC.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./select-nVA4yav1.js";import"./index-Xq00qzUB.js";import"./vuedraggable.umd-DpmahwAM.js";import"./index-B4ez5KWV.js";const K=f({name:"comp-jeecg-basic",setup(){const t=_("JeecgComponents"),r=g(()=>({JeecgComponents:b,JEditorDemo:C,JCodeEditDemo:k,ImgDragSort:D,ImgTurnPage:v,JeecgPdfView:J,JUploadDemo:h})[t.value]);function i(m){t.value=m}return{activeKey:t,currentComponent:r,tabChange:i}}}),E={class:"p-4"};function P(t,r,i,m,w,B){const e=p("a-tab-pane"),c=p("a-tabs"),s=p("a-card");return a(),u("div",E,[o(s,{bordered:!1,style:{height:"100%"}},{default:n(()=>[o(c,{activeKey:t.activeKey,"onUpdate:activeKey":r[0]||(r[0]=d=>t.activeKey=d),onChange:t.tabChange},{default:n(()=>[o(e,{key:"JeecgComponents",tab:"下拉选择组件"}),o(e,{key:"JCodeEditDemo",tab:"代码编辑器","force-render":""}),o(e,{key:"JEditorDemo",tab:"富文本&MakeDown"}),o(e,{key:"ImgDragSort",tab:"图片拖拽"}),o(e,{key:"ImgTurnPage",tab:"图片翻页"}),o(e,{key:"JeecgPdfView",tab:"PDF预览"}),o(e,{key:"JUploadDemo",tab:"文件上传"})]),_:1},8,["activeKey","onChange"]),(a(),y(l(t.currentComponent)))]),_:1})])}const zo=$(K,[["render",P]]);export{zo as default};
