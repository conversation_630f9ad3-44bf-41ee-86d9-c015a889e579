var x=Object.defineProperty;var f=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,w=Object.prototype.propertyIsEnumerable;var y=(e,t,r)=>t in e?x(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,g=(e,t)=>{for(var r in t||(t={}))I.call(t,r)&&y(e,r,t[r]);if(f)for(var r of f(t))w.call(t,r)&&y(e,r,t[r]);return e};var b=(e,t,r)=>new Promise((l,d)=>{var m=a=>{try{i(r.next(a))}catch(o){d(o)}},p=a=>{try{i(r.throw(a))}catch(o){d(o)}},i=a=>a.done?l(a.value):Promise.resolve(a.value).then(m,p);i((r=r.apply(e,t)).next())});import{d as T,f as _,e as B,u,aB as v,ar as S,aD as j,k as C,aE as M}from"./vue-vendor-dy9k-Yad.js";import{B as P}from"./index-Diw57m_E.js";import{J as U}from"./index-L3cSIXth.js";import{render as z}from"./renderUtils-D7XVOFwj.js";import{j as n,ac as E,az as Q}from"./index-CCWaWN5g.js";import{M as N}from"./antd-vue-vendor-me9YkNVC.js";import{u as O}from"./useForm-CgkFTrrO.js";import{B as k}from"./BasicForm-DBcXiHk0.js";const A=[{title:"任务类名",dataIndex:"jobClassName",width:200,align:"left"},{title:"Cron表达式",dataIndex:"cronExpression",width:200},{title:"参数",dataIndex:"parameter",width:200},{title:"描述",dataIndex:"description",width:200},{title:"状态",dataIndex:"status",width:100,customRender:({text:e})=>{const t=e=="0"?"green":e=="-1"?"red":"gray";return z.renderTag(z.renderDict(e,"quartz_status"),t)}}],ee=[{field:"jobClassName",label:"任务类名",component:"Input",colProps:{span:8}},{field:"status",label:"任务状态",component:"JDictSelectTag",componentProps:{dictCode:"quartz_status",stringToNumber:!0},colProps:{span:8}}],F=[{field:"id",label:"id",component:"Input",show:!1},{field:"jobClassName",label:"任务类名",component:"Input",required:!0},{field:"cronExpression",label:"Cron表达式",component:"JEasyCron",defaultValue:"* * * * * ? *",rules:[{required:!0,message:"请输入Cron表达式"},{validator:U}]},{field:"paramterType",label:"参数类型",component:"Select",defaultValue:"string",componentProps:{options:[{label:"字符串",value:"string"},{label:"JSON对象",value:"json"}]}},{field:"parameter",label:"参数",component:"InputTextArea",ifShow:({values:e})=>e.paramterType=="string"},{field:"parameter",label:"参数",component:"JAddInput",helpMessage:"键值对形式填写",ifShow:({values:e})=>e.paramterType=="json"},{field:"status",label:"状态",component:"JDictSelectTag",componentProps:{dictCode:"quartz_status",type:"radioButton",stringToNumber:!0,dropdownStyle:{maxHeight:"6vh"}}},{field:"description",label:"描述",component:"InputTextArea"}];const te="/sys/quartzJob/exportXls",re="/sys/quartzJob/importExcel",se=e=>n.get({url:"/sys/quartzJob/list",params:e}),D=(e,t)=>{let r=t?"/sys/quartzJob/edit":"/sys/quartzJob/add";return n.post({url:r,params:e})};const ae=(e,t)=>n.delete({url:"/sys/quartzJob/delete",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),oe=(e,t)=>n.get({url:"/sys/quartzJob/resume",params:e}).then(()=>{t()}),ne=(e,t)=>n.get({url:"/sys/quartzJob/pause",params:e}).then(()=>{t()}),le=(e,t)=>n.get({url:"/sys/quartzJob/execute",params:e}).then(()=>{t()}),ue=(e,t)=>{N.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>n.delete({url:"/sys/quartzJob/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},R=T({__name:"QuartzModal",emits:["register","success"],setup(e,{emit:t}){const r=t,l=_(!0),[d,{resetFields:m,setFieldsValue:p,validate:i}]=O({schemas:F,showActionButtonGroup:!1,labelWidth:100}),[a,{setModalProps:o,closeModal:J}]=E(s=>b(null,null,function*(){var c;if(yield m(),o({confirmLoading:!1}),l.value=!!(s!=null&&s.isUpdate),u(l)){try{s.record.paramterType=Q((c=s==null?void 0:s.record)==null?void 0:c.parameter)?"json":"string"}catch(V){}yield p(g({},s.record))}})),h=B(()=>u(l)?"编辑任务":"新增任务");function q(s){return b(this,null,function*(){try{let c=yield i();o({confirmLoading:!0}),yield D(c,l.value),J(),r("success")}finally{o({confirmLoading:!1})}})}return(s,c)=>(S(),v(u(P),M(s.$attrs,{onRegister:u(a),title:h.value,onOk:q,width:"40%"}),{default:j(()=>[C(u(k),{onRegister:u(d)},null,8,["onRegister"])]),_:1},16,["onRegister","title"]))}}),ie=Object.freeze(Object.defineProperty({__proto__:null,default:R},Symbol.toStringTag,{value:"Module"}));export{ie as Q,R as _,te as a,ue as b,A as c,ae as d,le as e,se as f,re as g,ne as p,oe as r,ee as s};
