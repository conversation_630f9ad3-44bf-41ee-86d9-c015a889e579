var X=Object.defineProperty,$=Object.defineProperties;var J=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var Y=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable;var S=(n,o,a)=>o in n?X(n,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):n[o]=a,p=(n,o)=>{for(var a in o||(o={}))Y.call(o,a)&&S(n,a,o[a]);if(R)for(var a of R(o))Z.call(o,a)&&S(n,a,o[a]);return n},A=(n,o)=>$(n,J(o));var N=(n,o,a)=>new Promise((b,c)=>{var f=l=>{try{d(a.next(l))}catch(m){c(m)}},T=l=>{try{d(a.throw(l))}catch(m){c(m)}},d=l=>l.done?b(l.value):Promise.resolve(l.value).then(f,T);d((a=a.apply(n,o)).next())});import{d as D,f as i,ag as ee,aq as te,ar as oe,k as s,aD as u,G as C,au as x,u as g}from"./vue-vendor-dy9k-Yad.js";import{u as ae}from"./index-BkGZ5fiW.js";import{j as re,u as ne,aX as B,a as le}from"./index-CCWaWN5g.js";import{D as ie,C as se}from"./index-CyU3vcHV.js";import{Q as ue}from"./componentMap-Bkie1n3v.js";import pe from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useFilePreview-CazplhRu.js";import"./SupplyDemand-DK00S9Ao.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const ce=n=>{const o=p({pageNo:1,pageSize:10},n);return re.post({url:"/hgy/entrustService/hgyEntrustOrder/workbench/completedQueryPage",data:o})},de={class:"p-4"},me=D({name:"AlreadyLog"}),ve=D(A(p({},me),{setup(n){const{createMessage:o}=ne(),a=[{title:"序号",dataIndex:"index",width:60,customRender:({index:e})=>e+1},{title:"委托单号",dataIndex:"id",width:160},{title:"项目名称",dataIndex:"projectName",width:200,ellipsis:!0},{title:"委托类型",dataIndex:"entrustType",width:100,slots:{customRender:"entrustType"}},{title:"服务类型",dataIndex:"serviceType",width:120,slots:{customRender:"serviceType"}},{title:"审核状态",dataIndex:"status",width:100,slots:{customRender:"status"}},{title:"联系人",dataIndex:"relationUser",width:100},{title:"联系电话",dataIndex:"relationPhone",width:120},{title:"申请人",dataIndex:"applicantUser",width:100},{title:"审核人",dataIndex:"auditUser",width:150},{title:"提交时间",dataIndex:"submitTime",width:200,customRender:({text:e})=>e?B(e):"-"},{title:"审核时间",dataIndex:"auditTime",width:200,customRender:({text:e})=>e?B(e):"-"}],b=[{key:"all",label:"全部已办",icon:""},{key:"appreciation",label:"增值委托",icon:""},{key:"autonomous",label:"自主委托",icon:""},{key:"supply",label:"供求信息",icon:""}],c=i("all"),f=i({});function T(e){c.value=e;let t={};switch(e){case"appreciation":t={entrustType:1};break;case"autonomous":t={entrustType:2};break;case"supply":t={entrustType:3};break;default:t={}}f.value=t,k()}const d=[{key:"all",label:"全部委托",icon:""},{key:"approved",label:"审核通过",icon:""},{key:"rejected",label:"审核未通过",icon:""}],l=i({});function m(e){I.value=e;let t={};switch(e){case"approved":t={status:3};break;case"rejected":t={status:4};break;default:t={}}l.value=t,k()}const I=i("all");function V(e){return N(this,null,function*(){const t=p(p(p({},e),l.value),f.value);return ce(t)})}const[E,{reload:k}]=ae({api:V,columns:a,striped:!1,useSearchForm:!0,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,showButtonTags:!0,buttonTagItems:b,activeButtonTagKey:c.value,showNavigation:!0,navigationItems:d,activeNavigationKey:I.value,inset:!0,maxHeight:408,actionColumn:{width:200,title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:"right"},formConfig:{labelWidth:64,size:"large",labelAlign:"left",actionColOptions:{span:6,style:{textAlign:"right"}},schemas:[{field:"entrustOrderId",label:"委托单号",component:"Input",componentProps:{placeholder:"请输入委托单号"},colProps:{span:6}},{field:"entrustType",label:"委托类型",component:"Select",componentProps:{placeholder:"请选择委托类型",options:[{label:"增值",value:1},{label:"自主",value:2},{label:"供应",value:3}]},colProps:{span:6}},{field:"serviceType",label:"服务类型",component:"Select",componentProps:{placeholder:"请选择服务类型",options:[{label:"竞价委托",value:1},{label:"资产处置",value:2},{label:"采购信息",value:3},{label:"供应",value:4},{label:"求购",value:5}]},colProps:{span:6}}]}});function K(e){const t=[{label:"查看详情",onClick:j.bind(null,e)}];return e.entrustType===2&&e.serviceType===1&&t.push({label:"查看标的",onClick:O.bind(null,e)}),U(e)&&t.push({label:"审核",onClick:Q.bind(null,e)}),t}function U(e){return e.status===2}const y=i(!1),v=i(null),h=i(!1),P=i("");function j(e){v.value=e,y.value=!0}function z(){y.value=!1,v.value=null}function O(e){P.value=e.id,h.value=!0}function Q(e){o.info("审核功能开发中...")}function q(e){return{1:"增值",2:"自主",3:"供求"}[e]||"未知"}function H(e){return{1:"blue",2:"green",3:"orange"}[e]||"default"}function F(e){return{1:"竞价委托",2:"资产处置",3:"采购信息",4:"供应",5:"求购"}[e]||"未知"}function G(e){return{1:"purple",2:"cyan",3:"geekblue",4:"lime",5:"magenta"}[e]||"default"}function L(e){return{1:"草稿",2:"待审核",3:"已通过",4:"已拒绝"}[e]||"未知"}function W(e){return{2:"processing",3:"success",4:"error"}[e]||"default"}return(e,t)=>{var M,_;const w=ee("a-tag");return oe(),te("div",de,[s(g(pe),{onRegister:g(E),onButtonTagChange:T,onNavigationChange:m},{action:u(({record:r})=>[s(g(ue),{actions:K(r),size:"small"},null,8,["actions"])]),entrustType:u(({text:r})=>[s(w,{color:H(r)},{default:u(()=>[C(x(q(r)),1)]),_:2},1032,["color"])]),serviceType:u(({text:r})=>[s(w,{color:G(r)},{default:u(()=>[C(x(F(r)),1)]),_:2},1032,["color"])]),status:u(({text:r})=>[s(w,{color:W(r)},{default:u(()=>[C(x(L(r)),1)]),_:2},1032,["color"])]),_:1},8,["onRegister"]),s(g(ie),{open:y.value,"onUpdate:open":t[0]||(t[0]=r=>y.value=r),record:v.value,"entrust-type":((M=v.value)==null?void 0:M.entrustType)||1,"service-type":((_=v.value)==null?void 0:_.serviceType)||1,onClose:z},null,8,["open","record","entrust-type","service-type"]),s(g(se),{open:h.value,"onUpdate:open":t[1]||(t[1]=r=>h.value=r),"record-id":P.value,onClose:t[2]||(t[2]=r=>h.value=!1)},null,8,["open","record-id"])])}}})),Ct=le(ve,[["__scopeId","data-v-d6894dc1"]]);export{Ct as default};
