var k=(F,R,s)=>new Promise((P,g)=>{var M=a=>{try{u(s.next(a))}catch(h){g(h)}},E=a=>{try{u(s.throw(a))}catch(h){g(h)}},u=a=>a.done?P(a.value):Promise.resolve(a.value).then(M,E);u((s=s.apply(F,R)).next())});import{f as z,n as N,u as f,H as D,w as I}from"./vue-vendor-dy9k-Yad.js";import{a0 as Q,Q as W,P as X,V as Z}from"./index-CCWaWN5g.js";import{useWindowSizeFn as _}from"./useWindowSizeFn-DDbrQbks.js";import{u as $}from"./useContentViewHeight-Md7r1NIg.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./usePageContext-CxiNGbPs.js";function v(F,R,s,P,g=0,M=z(0)){const E=z(null),{footerHeightRef:u}=$();let a={useLayoutFooter:!0};const h=e=>{a=e};function A(){N(()=>{B()})}function p(e,x="all"){var m,H,l,b;function c(t){return Number(t.replace(/[^\d]/g,""))}let i=0;const r="0px";if(e){const t=getComputedStyle(e),n=c((m=t==null?void 0:t.marginTop)!=null?m:r),o=c((H=t==null?void 0:t.marginBottom)!=null?H:r),d=c((l=t==null?void 0:t.paddingTop)!=null?l:r),V=c((b=t==null?void 0:t.paddingBottom)!=null?b:r);x==="all"?(i+=n,i+=o,i+=d,i+=V):x==="top"?(i+=n,i+=d):(i+=o,i+=V)}return i}function T(e){return e==null?null:e instanceof HTMLDivElement?e:e.$el}function B(){return k(this,null,function*(){var b;if(!F.value)return;yield N();const e=T(f(R));if(!e)return;const{bottomIncludeBody:x}=Q(e);let c=0;s.forEach(t=>{var n,o;c+=(o=(n=T(f(t)))==null?void 0:n.offsetHeight)!=null?o:0});let i=(b=p(e))!=null?b:0;P.forEach(t=>{i+=p(T(f(t)))});let r=0;function m(t,n){if(t&&n){const o=t.parentElement;o&&(W(n)?o.classList.contains(n)?r+=p(o,"bottom"):(r+=p(o,"bottom"),m(o,n)):X(n)&&n>0&&(r+=p(o,"bottom"),m(o,--n)))}}D(g)?m(e,f(g)):m(e,g);let H=x-f(u)-f(M)-c-i-r;const l=()=>{var t;(t=a.elements)==null||t.forEach(n=>{var o,d;H+=(d=(o=T(f(n)))==null?void 0:o.offsetHeight)!=null?d:0})};a.useLayoutFooter&&f(u)>0,l(),E.value=H})}return Z(()=>{N(()=>{B()})}),_(()=>{B()},50,{immediate:!0}),I(()=>[u.value],()=>{B()},{flush:"post",immediate:!0}),{redoHeight:A,setCompensation:h,contentHeight:E}}export{v as useContentHeight};
