import{d as s,aB as n,ar as c,aD as l,k as u,u as r}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{y as f}from"./componentMap-Bkie1n3v.js";import{u as _}from"./useForm-CgkFTrrO.js";import{B as d}from"./BasicForm-DBcXiHk0.js";import{a as b}from"./index-CCWaWN5g.js";import"./JAreaLinkage-DFCdF3cr.js";import"./antd-vue-vendor-me9YkNVC.js";import"./areaDataUtil-BXVjRArW.js";import"./vxe-table-vendor-B22HppNm.js";import"./JSelectUser-COkExGbu.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const B=s({__name:"BasicFormCustomSlots",setup(h){const m=[{field:"name",label:"姓名",component:"Input",slot:"name"},{field:"phone",label:"联系方式",component:"Input"},{field:"feedback",label:"问题反馈",component:"InputTextArea"}],[p]=_({schemas:m,showResetButton:!1,labelWidth:"150px",submitButtonOptions:{text:"提交",preIcon:""},actionColOptions:{span:17}});function i(e){}return(e,x)=>(c(),n(r(d),{onRegister:r(p),style:{"margin-top":"20px"},onSubmit:i},{name:l(({model:t,field:o})=>[u(f,{value:t[o],"onUpdate:value":a=>t[o]=a},null,8,["value","onUpdate:value"])]),_:1},8,["onRegister"]))}}),vt=b(B,[["__scopeId","data-v-6cbf081c"]]);export{vt as default};
