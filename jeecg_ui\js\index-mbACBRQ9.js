var Wa=Object.defineProperty,Fa=Object.defineProperties;var qa=Object.getOwnPropertyDescriptors;var Vn=Object.getOwnPropertySymbols;var Ka=Object.prototype.hasOwnProperty,Ga=Object.prototype.propertyIsEnumerable;var Wn=(Ee,me,ge)=>me in Ee?Wa(Ee,me,{enumerable:!0,configurable:!0,writable:!0,value:ge}):Ee[me]=ge,bt=(Ee,me)=>{for(var ge in me||(me={}))Ka.call(me,ge)&&Wn(Ee,ge,me[ge]);if(Vn)for(var ge of Vn(me))Ga.call(me,ge)&&Wn(Ee,ge,me[ge]);return Ee},Fn=(Ee,me)=>Fa(Ee,qa(me));var qn=(Ee,me,ge)=>new Promise((E,re)=>{var Me=D=>{try{M(ge.next(D))}catch(R){re(R)}},Q=D=>{try{M(ge.throw(D))}catch(R){re(R)}},M=D=>D.done?E(D.value):Promise.resolve(D.value).then(Me,Q);M((ge=ge.apply(Ee,me)).next())});import{bn as $a,D as Xn,aZ as Za,bo as Kn,d as Gn,bp as Ja,V as Xa,a as Qn,ay as Qa,w as Yn}from"./index-CCWaWN5g.js";import{d as er,f as wt,w as Yt,e as tr,u as Dt,n as Ya,j as ei,Q as ti,aq as nr,ar as rr,as as $n,at as ni}from"./vue-vendor-dy9k-Yad.js";import{a as ar}from"./antd-vue-vendor-me9YkNVC.js";import"./index-Diw57m_E.js";import{u as ri}from"./BasicModal-BLFvpBuk.js";var yt={exports:{}};/*!
 * Vditor v3.11.1 - A markdown editor written in TypeScript.
 *
 * MIT License
 *
 * Copyright (c) 2018-present B3log 开源, b3log.org
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *
 */var ai=yt.exports,Zn;function ii(){return Zn||(Zn=1,function(Ee,me){(function(E,re){Ee.exports=re()})(ai,function(){return(()=>{var ge={173:Q=>{var M=function(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32},D=-1,R=1,N=0;M.Diff=function(d,c){return[d,c]},M.prototype.diff_main=function(d,c,_,g){typeof g=="undefined"&&(this.Diff_Timeout<=0?g=Number.MAX_VALUE:g=new Date().getTime()+this.Diff_Timeout*1e3);var y=g;if(d==null||c==null)throw new Error("Null input. (diff_main)");if(d==c)return d?[new M.Diff(N,d)]:[];typeof _=="undefined"&&(_=!0);var p=_,C=this.diff_commonPrefix(d,c),L=d.substring(0,C);d=d.substring(C),c=c.substring(C),C=this.diff_commonSuffix(d,c);var S=d.substring(d.length-C);d=d.substring(0,d.length-C),c=c.substring(0,c.length-C);var i=this.diff_compute_(d,c,p,y);return L&&i.unshift(new M.Diff(N,L)),S&&i.push(new M.Diff(N,S)),this.diff_cleanupMerge(i),i},M.prototype.diff_compute_=function(d,c,_,g){var y;if(!d)return[new M.Diff(R,c)];if(!c)return[new M.Diff(D,d)];var p=d.length>c.length?d:c,C=d.length>c.length?c:d,L=p.indexOf(C);if(L!=-1)return y=[new M.Diff(R,p.substring(0,L)),new M.Diff(N,C),new M.Diff(R,p.substring(L+C.length))],d.length>c.length&&(y[0][0]=y[2][0]=D),y;if(C.length==1)return[new M.Diff(D,d),new M.Diff(R,c)];var S=this.diff_halfMatch_(d,c);if(S){var i=S[0],s=S[1],u=S[2],f=S[3],v=S[4],b=this.diff_main(i,u,_,g),k=this.diff_main(s,f,_,g);return b.concat([new M.Diff(N,v)],k)}return _&&d.length>100&&c.length>100?this.diff_lineMode_(d,c,g):this.diff_bisect_(d,c,g)},M.prototype.diff_lineMode_=function(d,c,_){var g=this.diff_linesToChars_(d,c);d=g.chars1,c=g.chars2;var y=g.lineArray,p=this.diff_main(d,c,!1,_);this.diff_charsToLines_(p,y),this.diff_cleanupSemantic(p),p.push(new M.Diff(N,""));for(var C=0,L=0,S=0,i="",s="";C<p.length;){switch(p[C][0]){case R:S++,s+=p[C][1];break;case D:L++,i+=p[C][1];break;case N:if(L>=1&&S>=1){p.splice(C-L-S,L+S),C=C-L-S;for(var u=this.diff_main(i,s,!1,_),f=u.length-1;f>=0;f--)p.splice(C,0,u[f]);C=C+u.length}S=0,L=0,i="",s="";break}C++}return p.pop(),p},M.prototype.diff_bisect_=function(d,c,_){for(var g=d.length,y=c.length,p=Math.ceil((g+y)/2),C=p,L=2*p,S=new Array(L),i=new Array(L),s=0;s<L;s++)S[s]=-1,i[s]=-1;S[C+1]=0,i[C+1]=0;for(var u=g-y,f=u%2!=0,v=0,b=0,k=0,H=0,j=0;j<p&&!(new Date().getTime()>_);j++){for(var x=-j+v;x<=j-b;x+=2){var I=C+x,z;x==-j||x!=j&&S[I-1]<S[I+1]?z=S[I+1]:z=S[I-1]+1;for(var T=z-x;z<g&&T<y&&d.charAt(z)==c.charAt(T);)z++,T++;if(S[I]=z,z>g)b+=2;else if(T>y)v+=2;else if(f){var B=C+u-x;if(B>=0&&B<L&&i[B]!=-1){var q=g-i[B];if(z>=q)return this.diff_bisectSplit_(d,c,z,T,_)}}}for(var J=-j+k;J<=j-H;J+=2){var B=C+J,q;J==-j||J!=j&&i[B-1]<i[B+1]?q=i[B+1]:q=i[B-1]+1;for(var se=q-J;q<g&&se<y&&d.charAt(g-q-1)==c.charAt(y-se-1);)q++,se++;if(i[B]=q,q>g)H+=2;else if(se>y)k+=2;else if(!f){var I=C+u-J;if(I>=0&&I<L&&S[I]!=-1){var z=S[I],T=C+z-I;if(q=g-q,z>=q)return this.diff_bisectSplit_(d,c,z,T,_)}}}}return[new M.Diff(D,d),new M.Diff(R,c)]},M.prototype.diff_bisectSplit_=function(d,c,_,g,y){var p=d.substring(0,_),C=c.substring(0,g),L=d.substring(_),S=c.substring(g),i=this.diff_main(p,C,!1,y),s=this.diff_main(L,S,!1,y);return i.concat(s)},M.prototype.diff_linesToChars_=function(d,c){var _=[],g={};_[0]="";function y(S){for(var i="",s=0,u=-1,f=_.length;u<S.length-1;){u=S.indexOf(`
`,s),u==-1&&(u=S.length-1);var v=S.substring(s,u+1);(g.hasOwnProperty?g.hasOwnProperty(v):g[v]!==void 0)?i+=String.fromCharCode(g[v]):(f==p&&(v=S.substring(s),u=S.length),i+=String.fromCharCode(f),g[v]=f,_[f++]=v),s=u+1}return i}var p=4e4,C=y(d);p=65535;var L=y(c);return{chars1:C,chars2:L,lineArray:_}},M.prototype.diff_charsToLines_=function(d,c){for(var _=0;_<d.length;_++){for(var g=d[_][1],y=[],p=0;p<g.length;p++)y[p]=c[g.charCodeAt(p)];d[_][1]=y.join("")}},M.prototype.diff_commonPrefix=function(d,c){if(!d||!c||d.charAt(0)!=c.charAt(0))return 0;for(var _=0,g=Math.min(d.length,c.length),y=g,p=0;_<y;)d.substring(p,y)==c.substring(p,y)?(_=y,p=_):g=y,y=Math.floor((g-_)/2+_);return y},M.prototype.diff_commonSuffix=function(d,c){if(!d||!c||d.charAt(d.length-1)!=c.charAt(c.length-1))return 0;for(var _=0,g=Math.min(d.length,c.length),y=g,p=0;_<y;)d.substring(d.length-y,d.length-p)==c.substring(c.length-y,c.length-p)?(_=y,p=_):g=y,y=Math.floor((g-_)/2+_);return y},M.prototype.diff_commonOverlap_=function(d,c){var _=d.length,g=c.length;if(_==0||g==0)return 0;_>g?d=d.substring(_-g):_<g&&(c=c.substring(0,_));var y=Math.min(_,g);if(d==c)return y;for(var p=0,C=1;;){var L=d.substring(y-C),S=c.indexOf(L);if(S==-1)return p;C+=S,(S==0||d.substring(y-C)==c.substring(0,C))&&(p=C,C++)}},M.prototype.diff_halfMatch_=function(d,c){if(this.Diff_Timeout<=0)return null;var _=d.length>c.length?d:c,g=d.length>c.length?c:d;if(_.length<4||g.length*2<_.length)return null;var y=this;function p(b,k,H){for(var j=b.substring(H,H+Math.floor(b.length/4)),x=-1,I="",z,T,B,q;(x=k.indexOf(j,x+1))!=-1;){var J=y.diff_commonPrefix(b.substring(H),k.substring(x)),se=y.diff_commonSuffix(b.substring(0,H),k.substring(0,x));I.length<se+J&&(I=k.substring(x-se,x)+k.substring(x,x+J),z=b.substring(0,H-se),T=b.substring(H+J),B=k.substring(0,x-se),q=k.substring(x+J))}return I.length*2>=b.length?[z,T,B,q,I]:null}var C=p(_,g,Math.ceil(_.length/4)),L=p(_,g,Math.ceil(_.length/2)),S;if(!C&&!L)return null;L?C?S=C[4].length>L[4].length?C:L:S=L:S=C;var i,s,u,f;d.length>c.length?(i=S[0],s=S[1],u=S[2],f=S[3]):(u=S[0],f=S[1],i=S[2],s=S[3]);var v=S[4];return[i,s,u,f,v]},M.prototype.diff_cleanupSemantic=function(d){for(var c=!1,_=[],g=0,y=null,p=0,C=0,L=0,S=0,i=0;p<d.length;)d[p][0]==N?(_[g++]=p,C=S,L=i,S=0,i=0,y=d[p][1]):(d[p][0]==R?S+=d[p][1].length:i+=d[p][1].length,y&&y.length<=Math.max(C,L)&&y.length<=Math.max(S,i)&&(d.splice(_[g-1],0,new M.Diff(D,y)),d[_[g-1]+1][0]=R,g--,g--,p=g>0?_[g-1]:-1,C=0,L=0,S=0,i=0,y=null,c=!0)),p++;for(c&&this.diff_cleanupMerge(d),this.diff_cleanupSemanticLossless(d),p=1;p<d.length;){if(d[p-1][0]==D&&d[p][0]==R){var s=d[p-1][1],u=d[p][1],f=this.diff_commonOverlap_(s,u),v=this.diff_commonOverlap_(u,s);f>=v?(f>=s.length/2||f>=u.length/2)&&(d.splice(p,0,new M.Diff(N,u.substring(0,f))),d[p-1][1]=s.substring(0,s.length-f),d[p+1][1]=u.substring(f),p++):(v>=s.length/2||v>=u.length/2)&&(d.splice(p,0,new M.Diff(N,s.substring(0,v))),d[p-1][0]=R,d[p-1][1]=u.substring(0,u.length-v),d[p+1][0]=D,d[p+1][1]=s.substring(v),p++),p++}p++}},M.prototype.diff_cleanupSemanticLossless=function(d){function c(v,b){if(!v||!b)return 6;var k=v.charAt(v.length-1),H=b.charAt(0),j=k.match(M.nonAlphaNumericRegex_),x=H.match(M.nonAlphaNumericRegex_),I=j&&k.match(M.whitespaceRegex_),z=x&&H.match(M.whitespaceRegex_),T=I&&k.match(M.linebreakRegex_),B=z&&H.match(M.linebreakRegex_),q=T&&v.match(M.blanklineEndRegex_),J=B&&b.match(M.blanklineStartRegex_);return q||J?5:T||B?4:j&&!I&&z?3:I||z?2:j||x?1:0}for(var _=1;_<d.length-1;){if(d[_-1][0]==N&&d[_+1][0]==N){var g=d[_-1][1],y=d[_][1],p=d[_+1][1],C=this.diff_commonSuffix(g,y);if(C){var L=y.substring(y.length-C);g=g.substring(0,g.length-C),y=L+y.substring(0,y.length-C),p=L+p}for(var S=g,i=y,s=p,u=c(g,y)+c(y,p);y.charAt(0)===p.charAt(0);){g+=y.charAt(0),y=y.substring(1)+p.charAt(0),p=p.substring(1);var f=c(g,y)+c(y,p);f>=u&&(u=f,S=g,i=y,s=p)}d[_-1][1]!=S&&(S?d[_-1][1]=S:(d.splice(_-1,1),_--),d[_][1]=i,s?d[_+1][1]=s:(d.splice(_+1,1),_--))}_++}},M.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,M.whitespaceRegex_=/\s/,M.linebreakRegex_=/[\r\n]/,M.blanklineEndRegex_=/\n\r?\n$/,M.blanklineStartRegex_=/^\r?\n\r?\n/,M.prototype.diff_cleanupEfficiency=function(d){for(var c=!1,_=[],g=0,y=null,p=0,C=!1,L=!1,S=!1,i=!1;p<d.length;)d[p][0]==N?(d[p][1].length<this.Diff_EditCost&&(S||i)?(_[g++]=p,C=S,L=i,y=d[p][1]):(g=0,y=null),S=i=!1):(d[p][0]==D?i=!0:S=!0,y&&(C&&L&&S&&i||y.length<this.Diff_EditCost/2&&C+L+S+i==3)&&(d.splice(_[g-1],0,new M.Diff(D,y)),d[_[g-1]+1][0]=R,g--,y=null,C&&L?(S=i=!0,g=0):(g--,p=g>0?_[g-1]:-1,S=i=!1),c=!0)),p++;c&&this.diff_cleanupMerge(d)},M.prototype.diff_cleanupMerge=function(d){d.push(new M.Diff(N,""));for(var c=0,_=0,g=0,y="",p="",C;c<d.length;)switch(d[c][0]){case R:g++,p+=d[c][1],c++;break;case D:_++,y+=d[c][1],c++;break;case N:_+g>1?(_!==0&&g!==0&&(C=this.diff_commonPrefix(p,y),C!==0&&(c-_-g>0&&d[c-_-g-1][0]==N?d[c-_-g-1][1]+=p.substring(0,C):(d.splice(0,0,new M.Diff(N,p.substring(0,C))),c++),p=p.substring(C),y=y.substring(C)),C=this.diff_commonSuffix(p,y),C!==0&&(d[c][1]=p.substring(p.length-C)+d[c][1],p=p.substring(0,p.length-C),y=y.substring(0,y.length-C))),c-=_+g,d.splice(c,_+g),y.length&&(d.splice(c,0,new M.Diff(D,y)),c++),p.length&&(d.splice(c,0,new M.Diff(R,p)),c++),c++):c!==0&&d[c-1][0]==N?(d[c-1][1]+=d[c][1],d.splice(c,1)):c++,g=0,_=0,y="",p="";break}d[d.length-1][1]===""&&d.pop();var L=!1;for(c=1;c<d.length-1;)d[c-1][0]==N&&d[c+1][0]==N&&(d[c][1].substring(d[c][1].length-d[c-1][1].length)==d[c-1][1]?(d[c][1]=d[c-1][1]+d[c][1].substring(0,d[c][1].length-d[c-1][1].length),d[c+1][1]=d[c-1][1]+d[c+1][1],d.splice(c-1,1),L=!0):d[c][1].substring(0,d[c+1][1].length)==d[c+1][1]&&(d[c-1][1]+=d[c+1][1],d[c][1]=d[c][1].substring(d[c+1][1].length)+d[c+1][1],d.splice(c+1,1),L=!0)),c++;L&&this.diff_cleanupMerge(d)},M.prototype.diff_xIndex=function(d,c){var _=0,g=0,y=0,p=0,C;for(C=0;C<d.length&&(d[C][0]!==R&&(_+=d[C][1].length),d[C][0]!==D&&(g+=d[C][1].length),!(_>c));C++)y=_,p=g;return d.length!=C&&d[C][0]===D?p:p+(c-y)},M.prototype.diff_prettyHtml=function(d){for(var c=[],_=/&/g,g=/</g,y=/>/g,p=/\n/g,C=0;C<d.length;C++){var L=d[C][0],S=d[C][1],i=S.replace(_,"&amp;").replace(g,"&lt;").replace(y,"&gt;").replace(p,"&para;<br>");switch(L){case R:c[C]='<ins style="background:#e6ffe6;">'+i+"</ins>";break;case D:c[C]='<del style="background:#ffe6e6;">'+i+"</del>";break;case N:c[C]="<span>"+i+"</span>";break}}return c.join("")},M.prototype.diff_text1=function(d){for(var c=[],_=0;_<d.length;_++)d[_][0]!==R&&(c[_]=d[_][1]);return c.join("")},M.prototype.diff_text2=function(d){for(var c=[],_=0;_<d.length;_++)d[_][0]!==D&&(c[_]=d[_][1]);return c.join("")},M.prototype.diff_levenshtein=function(d){for(var c=0,_=0,g=0,y=0;y<d.length;y++){var p=d[y][0],C=d[y][1];switch(p){case R:_+=C.length;break;case D:g+=C.length;break;case N:c+=Math.max(_,g),_=0,g=0;break}}return c+=Math.max(_,g),c},M.prototype.diff_toDelta=function(d){for(var c=[],_=0;_<d.length;_++)switch(d[_][0]){case R:c[_]="+"+encodeURI(d[_][1]);break;case D:c[_]="-"+d[_][1].length;break;case N:c[_]="="+d[_][1].length;break}return c.join("	").replace(/%20/g," ")},M.prototype.diff_fromDelta=function(d,c){for(var _=[],g=0,y=0,p=c.split(/\t/g),C=0;C<p.length;C++){var L=p[C].substring(1);switch(p[C].charAt(0)){case"+":try{_[g++]=new M.Diff(R,decodeURI(L))}catch(s){throw new Error("Illegal escape in diff_fromDelta: "+L)}break;case"-":case"=":var S=parseInt(L,10);if(isNaN(S)||S<0)throw new Error("Invalid number in diff_fromDelta: "+L);var i=d.substring(y,y+=S);p[C].charAt(0)=="="?_[g++]=new M.Diff(N,i):_[g++]=new M.Diff(D,i);break;default:if(p[C])throw new Error("Invalid diff operation in diff_fromDelta: "+p[C])}}if(y!=d.length)throw new Error("Delta length ("+y+") does not equal source text length ("+d.length+").");return _},M.prototype.match_main=function(d,c,_){if(d==null||c==null||_==null)throw new Error("Null input. (match_main)");return _=Math.max(0,Math.min(_,d.length)),d==c?0:d.length?d.substring(_,_+c.length)==c?_:this.match_bitap_(d,c,_):-1},M.prototype.match_bitap_=function(d,c,_){if(c.length>this.Match_MaxBits)throw new Error("Pattern too long for this browser.");var g=this.match_alphabet_(c),y=this;function p(z,T){var B=z/c.length,q=Math.abs(_-T);return y.Match_Distance?B+q/y.Match_Distance:q?1:B}var C=this.Match_Threshold,L=d.indexOf(c,_);L!=-1&&(C=Math.min(p(0,L),C),L=d.lastIndexOf(c,_+c.length),L!=-1&&(C=Math.min(p(0,L),C)));var S=1<<c.length-1;L=-1;for(var i,s,u=c.length+d.length,f,v=0;v<c.length;v++){for(i=0,s=u;i<s;)p(v,_+s)<=C?i=s:u=s,s=Math.floor((u-i)/2+i);u=s;var b=Math.max(1,_-s+1),k=Math.min(_+s,d.length)+c.length,H=Array(k+2);H[k+1]=(1<<v)-1;for(var j=k;j>=b;j--){var x=g[d.charAt(j-1)];if(v===0?H[j]=(H[j+1]<<1|1)&x:H[j]=(H[j+1]<<1|1)&x|((f[j+1]|f[j])<<1|1)|f[j+1],H[j]&S){var I=p(v,j-1);if(I<=C)if(C=I,L=j-1,L>_)b=Math.max(1,2*_-L);else break}}if(p(v+1,_)>C)break;f=H}return L},M.prototype.match_alphabet_=function(d){for(var c={},_=0;_<d.length;_++)c[d.charAt(_)]=0;for(var _=0;_<d.length;_++)c[d.charAt(_)]|=1<<d.length-_-1;return c},M.prototype.patch_addContext_=function(d,c){if(c.length!=0){if(d.start2===null)throw Error("patch not initialized");for(var _=c.substring(d.start2,d.start2+d.length1),g=0;c.indexOf(_)!=c.lastIndexOf(_)&&_.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)g+=this.Patch_Margin,_=c.substring(d.start2-g,d.start2+d.length1+g);g+=this.Patch_Margin;var y=c.substring(d.start2-g,d.start2);y&&d.diffs.unshift(new M.Diff(N,y));var p=c.substring(d.start2+d.length1,d.start2+d.length1+g);p&&d.diffs.push(new M.Diff(N,p)),d.start1-=y.length,d.start2-=y.length,d.length1+=y.length+p.length,d.length2+=y.length+p.length}},M.prototype.patch_make=function(d,c,_){var g,y;if(typeof d=="string"&&typeof c=="string"&&typeof _=="undefined")g=d,y=this.diff_main(g,c,!0),y.length>2&&(this.diff_cleanupSemantic(y),this.diff_cleanupEfficiency(y));else if(d&&typeof d=="object"&&typeof c=="undefined"&&typeof _=="undefined")y=d,g=this.diff_text1(y);else if(typeof d=="string"&&c&&typeof c=="object"&&typeof _=="undefined")g=d,y=c;else if(typeof d=="string"&&typeof c=="string"&&_&&typeof _=="object")g=d,y=_;else throw new Error("Unknown call format to patch_make.");if(y.length===0)return[];for(var p=[],C=new M.patch_obj,L=0,S=0,i=0,s=g,u=g,f=0;f<y.length;f++){var v=y[f][0],b=y[f][1];switch(!L&&v!==N&&(C.start1=S,C.start2=i),v){case R:C.diffs[L++]=y[f],C.length2+=b.length,u=u.substring(0,i)+b+u.substring(i);break;case D:C.length1+=b.length,C.diffs[L++]=y[f],u=u.substring(0,i)+u.substring(i+b.length);break;case N:b.length<=2*this.Patch_Margin&&L&&y.length!=f+1?(C.diffs[L++]=y[f],C.length1+=b.length,C.length2+=b.length):b.length>=2*this.Patch_Margin&&L&&(this.patch_addContext_(C,s),p.push(C),C=new M.patch_obj,L=0,s=u,S=i);break}v!==R&&(S+=b.length),v!==D&&(i+=b.length)}return L&&(this.patch_addContext_(C,s),p.push(C)),p},M.prototype.patch_deepCopy=function(d){for(var c=[],_=0;_<d.length;_++){var g=d[_],y=new M.patch_obj;y.diffs=[];for(var p=0;p<g.diffs.length;p++)y.diffs[p]=new M.Diff(g.diffs[p][0],g.diffs[p][1]);y.start1=g.start1,y.start2=g.start2,y.length1=g.length1,y.length2=g.length2,c[_]=y}return c},M.prototype.patch_apply=function(d,c){if(d.length==0)return[c,[]];d=this.patch_deepCopy(d);var _=this.patch_addPadding(d);c=_+c+_,this.patch_splitMax(d);for(var g=0,y=[],p=0;p<d.length;p++){var C=d[p].start2+g,L=this.diff_text1(d[p].diffs),S,i=-1;if(L.length>this.Match_MaxBits?(S=this.match_main(c,L.substring(0,this.Match_MaxBits),C),S!=-1&&(i=this.match_main(c,L.substring(L.length-this.Match_MaxBits),C+L.length-this.Match_MaxBits),(i==-1||S>=i)&&(S=-1))):S=this.match_main(c,L,C),S==-1)y[p]=!1,g-=d[p].length2-d[p].length1;else{y[p]=!0,g=S-C;var s;if(i==-1?s=c.substring(S,S+L.length):s=c.substring(S,i+this.Match_MaxBits),L==s)c=c.substring(0,S)+this.diff_text2(d[p].diffs)+c.substring(S+L.length);else{var u=this.diff_main(L,s,!1);if(L.length>this.Match_MaxBits&&this.diff_levenshtein(u)/L.length>this.Patch_DeleteThreshold)y[p]=!1;else{this.diff_cleanupSemanticLossless(u);for(var f=0,v,b=0;b<d[p].diffs.length;b++){var k=d[p].diffs[b];k[0]!==N&&(v=this.diff_xIndex(u,f)),k[0]===R?c=c.substring(0,S+v)+k[1]+c.substring(S+v):k[0]===D&&(c=c.substring(0,S+v)+c.substring(S+this.diff_xIndex(u,f+k[1].length))),k[0]!==D&&(f+=k[1].length)}}}}}return c=c.substring(_.length,c.length-_.length),[c,y]},M.prototype.patch_addPadding=function(d){for(var c=this.Patch_Margin,_="",g=1;g<=c;g++)_+=String.fromCharCode(g);for(var g=0;g<d.length;g++)d[g].start1+=c,d[g].start2+=c;var y=d[0],p=y.diffs;if(p.length==0||p[0][0]!=N)p.unshift(new M.Diff(N,_)),y.start1-=c,y.start2-=c,y.length1+=c,y.length2+=c;else if(c>p[0][1].length){var C=c-p[0][1].length;p[0][1]=_.substring(p[0][1].length)+p[0][1],y.start1-=C,y.start2-=C,y.length1+=C,y.length2+=C}if(y=d[d.length-1],p=y.diffs,p.length==0||p[p.length-1][0]!=N)p.push(new M.Diff(N,_)),y.length1+=c,y.length2+=c;else if(c>p[p.length-1][1].length){var C=c-p[p.length-1][1].length;p[p.length-1][1]+=_.substring(0,C),y.length1+=C,y.length2+=C}return _},M.prototype.patch_splitMax=function(d){for(var c=this.Match_MaxBits,_=0;_<d.length;_++)if(!(d[_].length1<=c)){var g=d[_];d.splice(_--,1);for(var y=g.start1,p=g.start2,C="";g.diffs.length!==0;){var L=new M.patch_obj,S=!0;for(L.start1=y-C.length,L.start2=p-C.length,C!==""&&(L.length1=L.length2=C.length,L.diffs.push(new M.Diff(N,C)));g.diffs.length!==0&&L.length1<c-this.Patch_Margin;){var i=g.diffs[0][0],s=g.diffs[0][1];i===R?(L.length2+=s.length,p+=s.length,L.diffs.push(g.diffs.shift()),S=!1):i===D&&L.diffs.length==1&&L.diffs[0][0]==N&&s.length>2*c?(L.length1+=s.length,y+=s.length,S=!1,L.diffs.push(new M.Diff(i,s)),g.diffs.shift()):(s=s.substring(0,c-L.length1-this.Patch_Margin),L.length1+=s.length,y+=s.length,i===N?(L.length2+=s.length,p+=s.length):S=!1,L.diffs.push(new M.Diff(i,s)),s==g.diffs[0][1]?g.diffs.shift():g.diffs[0][1]=g.diffs[0][1].substring(s.length))}C=this.diff_text2(L.diffs),C=C.substring(C.length-this.Patch_Margin);var u=this.diff_text1(g.diffs).substring(0,this.Patch_Margin);u!==""&&(L.length1+=u.length,L.length2+=u.length,L.diffs.length!==0&&L.diffs[L.diffs.length-1][0]===N?L.diffs[L.diffs.length-1][1]+=u:L.diffs.push(new M.Diff(N,u))),S||d.splice(++_,0,L)}}},M.prototype.patch_toText=function(d){for(var c=[],_=0;_<d.length;_++)c[_]=d[_];return c.join("")},M.prototype.patch_fromText=function(d){var c=[];if(!d)return c;for(var _=d.split(`
`),g=0,y=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;g<_.length;){var p=_[g].match(y);if(!p)throw new Error("Invalid patch string: "+_[g]);var C=new M.patch_obj;for(c.push(C),C.start1=parseInt(p[1],10),p[2]===""?(C.start1--,C.length1=1):p[2]=="0"?C.length1=0:(C.start1--,C.length1=parseInt(p[2],10)),C.start2=parseInt(p[3],10),p[4]===""?(C.start2--,C.length2=1):p[4]=="0"?C.length2=0:(C.start2--,C.length2=parseInt(p[4],10)),g++;g<_.length;){var L=_[g].charAt(0);try{var S=decodeURI(_[g].substring(1))}catch(i){throw new Error("Illegal escape in patch_fromText: "+S)}if(L=="-")C.diffs.push(new M.Diff(D,S));else if(L=="+")C.diffs.push(new M.Diff(R,S));else if(L==" ")C.diffs.push(new M.Diff(N,S));else{if(L=="@")break;if(L!=="")throw new Error('Invalid patch mode "'+L+'" in: '+S)}g++}}return c},M.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0},M.patch_obj.prototype.toString=function(){var d,c;this.length1===0?d=this.start1+",0":this.length1==1?d=this.start1+1:d=this.start1+1+","+this.length1,this.length2===0?c=this.start2+",0":this.length2==1?c=this.start2+1:c=this.start2+1+","+this.length2;for(var _=["@@ -"+d+" +"+c+` @@
`],g,y=0;y<this.diffs.length;y++){switch(this.diffs[y][0]){case R:g="+";break;case D:g="-";break;case N:g=" ";break}_[y+1]=g+encodeURI(this.diffs[y][1])+`
`}return _.join("").replace(/%20/g," ")},Q.exports=M,Q.exports.diff_match_patch=M,Q.exports.DIFF_DELETE=D,Q.exports.DIFF_INSERT=R,Q.exports.DIFF_EQUAL=N},408:(Q,M,D)=>{"use strict";D.d(M,{default:()=>_e});var R=D(135),N=D(840),d=D(775),c=D(428),_=D(325),g=D(483),y=D(999),p=function(U){U===void 0&&(U=document);var ue=function(Z){var G=document.createElement("img");G.src=Z.getAttribute("data-src"),G.addEventListener("load",function(){!Z.getAttribute("style")&&!Z.getAttribute("class")&&!Z.getAttribute("width")&&!Z.getAttribute("height")&&G.naturalHeight>G.naturalWidth&&G.naturalWidth/G.naturalHeight<document.querySelector(".vditor-reset").clientWidth/(window.innerHeight-40)&&G.naturalHeight>window.innerHeight-40&&(Z.style.height=window.innerHeight-40+"px"),Z.src=G.src}),Z.removeAttribute("data-src")};if(!("IntersectionObserver"in window))return U.querySelectorAll("img").forEach(function(Z){Z.getAttribute("data-src")&&ue(Z)}),!1;window.vditorImageIntersectionObserver?(window.vditorImageIntersectionObserver.disconnect(),U.querySelectorAll("img").forEach(function(Z){window.vditorImageIntersectionObserver.observe(Z)})):(window.vditorImageIntersectionObserver=new IntersectionObserver(function(Z){Z.forEach(function(G){(typeof G.isIntersecting=="undefined"?G.intersectionRatio!==0:G.isIntersecting)&&G.target.getAttribute("data-src")&&ue(G.target)})}),U.querySelectorAll("img").forEach(function(Z){window.vditorImageIntersectionObserver.observe(Z)}))},C=D(472),L=D(280),S=D(637),i=D(825),s=D(11),u=D(194),f=D(436),v=D(229),b=D(145),k=D(538),H=D(413),j=D(106),x=D(673),I=function(U){document.querySelectorAll(".vditor-anchor").forEach(function(ue){U===1&&ue.classList.add("vditor-anchor--left"),ue.onclick=function(){var Z=ue.getAttribute("href").substr(1),G=document.getElementById("vditorAnchor-"+Z).offsetTop;document.querySelector("html").scrollTop=G}}),window.onhashchange=function(){var ue=document.getElementById("vditorAnchor-"+decodeURIComponent(window.location.hash.substr(1)));ue&&(document.querySelector("html").scrollTop=ue.offsetTop)}},z=D(214),T=D(810),B=function(U,ue){if(ue===void 0&&(ue="zh_CN"),!(typeof speechSynthesis=="undefined"||typeof SpeechSynthesisUtterance=="undefined")){var Z=function(){var De=speechSynthesis.getVoices(),ie,ke;return De.forEach(function(Be){Be.lang===ue.replace("_","-")&&(ie=Be),Be.default&&(ke=Be)}),ie||(ie=ke),ie},G='<svg><use xlink:href="#vditor-icon-play"></use></svg>',be='<svg><use xlink:href="#vditor-icon-pause"></use></svg>';document.getElementById("vditorIconScript")||(G='<svg viewBox="0 0 32 32"><path d="M3.436 0l25.128 16-25.128 16v-32z"></path></svg>',be='<svg viewBox="0 0 32 32"><path d="M20.617 0h9.128v32h-9.128v-32zM2.255 32v-32h9.128v32h-9.128z"></path></svg>');var Y=document.querySelector(".vditor-speech");Y||(Y=document.createElement("button"),Y.className="vditor-speech",U.insertAdjacentElement("beforeend",Y),speechSynthesis.onvoiceschanged!==void 0&&(speechSynthesis.onvoiceschanged=Z));var Se=Z(),he=new SpeechSynthesisUtterance;he.voice=Se,he.onend=he.onerror=function(){Y.style.display="none",speechSynthesis.cancel(),Y.classList.remove("vditor-speech--current"),Y.innerHTML=G},U.addEventListener(window.ontouchstart!==void 0?"touchend":"click",function(De){var ie=De.target;if(ie.classList.contains("vditor-speech")||ie.parentElement.classList.contains("vditor-speech")){Y.classList.contains("vditor-speech--current")?speechSynthesis.speaking&&(speechSynthesis.paused?(speechSynthesis.resume(),Y.innerHTML=be):(speechSynthesis.pause(),Y.innerHTML=G)):(he.text=Y.getAttribute("data-text"),speechSynthesis.speak(he),Y.classList.add("vditor-speech--current"),Y.innerHTML=be),(0,T.Hc)(window.vditorSpeechRange),U.focus();return}if(Y.style.display="none",speechSynthesis.cancel(),Y.classList.remove("vditor-speech--current"),Y.innerHTML=G,getSelection().rangeCount!==0){var ke=getSelection().getRangeAt(0),Be=ke.toString().trim();if(Be){window.vditorSpeechRange=ke.cloneRange();var Ve=ke.getBoundingClientRect();Y.innerHTML=G,Y.style.display="block",Y.style.top=Ve.top+Ve.height+document.querySelector("html").scrollTop-20+"px",window.ontouchstart!==void 0?Y.style.left=De.changedTouches[De.changedTouches.length-1].pageX+2+"px":Y.style.left=De.clientX+2+"px",Y.setAttribute("data-text",Be)}}})}},q=function(U,ue,Z,G){function be(Y){return Y instanceof Z?Y:new Z(function(Se){Se(Y)})}return new(Z||(Z=Promise))(function(Y,Se){function he(ke){try{ie(G.next(ke))}catch(Be){Se(Be)}}function De(ke){try{ie(G.throw(ke))}catch(Be){Se(Be)}}function ie(ke){ke.done?Y(ke.value):be(ke.value).then(he,De)}ie((G=G.apply(U,ue||[])).next())})},J=function(U,ue){var Z={label:0,sent:function(){if(Y[0]&1)throw Y[1];return Y[1]},trys:[],ops:[]},G,be,Y,Se;return Se={next:he(0),throw:he(1),return:he(2)},typeof Symbol=="function"&&(Se[Symbol.iterator]=function(){return this}),Se;function he(ie){return function(ke){return De([ie,ke])}}function De(ie){if(G)throw new TypeError("Generator is already executing.");for(;Se&&(Se=0,ie[0]&&(Z=0)),Z;)try{if(G=1,be&&(Y=ie[0]&2?be.return:ie[0]?be.throw||((Y=be.return)&&Y.call(be),0):be.next)&&!(Y=Y.call(be,ie[1])).done)return Y;switch(be=0,Y&&(ie=[ie[0]&2,Y.value]),ie[0]){case 0:case 1:Y=ie;break;case 4:return Z.label++,{value:ie[1],done:!1};case 5:Z.label++,be=ie[1],ie=[0];continue;case 7:ie=Z.ops.pop(),Z.trys.pop();continue;default:if(Y=Z.trys,!(Y=Y.length>0&&Y[Y.length-1])&&(ie[0]===6||ie[0]===2)){Z=0;continue}if(ie[0]===3&&(!Y||ie[1]>Y[0]&&ie[1]<Y[3])){Z.label=ie[1];break}if(ie[0]===6&&Z.label<Y[1]){Z.label=Y[1],Y=ie;break}if(Y&&Z.label<Y[2]){Z.label=Y[2],Z.ops.push(ie);break}Y[2]&&Z.ops.pop(),Z.trys.pop();continue}ie=ue.call(U,Z)}catch(ke){ie=[6,ke],be=0}finally{G=Y=0}if(ie[0]&5)throw ie[1];return{value:ie[0]?ie[1]:void 0,done:!0}}},se=function(U){var ue,Z={anchor:0,cdn:b.g.CDN,customEmoji:{},emojiPath:"".concat(b.g.CDN,"/dist/images/emoji"),hljs:b.g.HLJS_OPTIONS,icon:"ant",lang:"zh_CN",markdown:b.g.MARKDOWN_OPTIONS,math:b.g.MATH_OPTIONS,mode:"light",speech:{enable:!1},render:{media:{enable:!0}},theme:b.g.THEME_OPTIONS};return U.cdn&&(!((ue=U.theme)===null||ue===void 0)&&ue.path||(Z.theme.path="".concat(U.cdn,"/dist/css/content-theme")),U.emojiPath||(Z.emojiPath="".concat(U.cdn,"/dist/images/emoji"))),(0,x.T)(Z,U)},X=function(U,ue){var Z=se(ue);return(0,H.G)("".concat(Z.cdn,"/dist/js/lute/lute.min.js"),"vditorLuteScript").then(function(){var G=(0,z.X)({autoSpace:Z.markdown.autoSpace,gfmAutoLink:Z.markdown.gfmAutoLink,codeBlockPreview:Z.markdown.codeBlockPreview,emojiSite:Z.emojiPath,emojis:Z.customEmoji,fixTermTypo:Z.markdown.fixTermTypo,footnotes:Z.markdown.footnotes,headingAnchor:Z.anchor!==0,inlineMathDigit:Z.math.inlineDigit,lazyLoadImage:Z.lazyLoadImage,linkBase:Z.markdown.linkBase,linkPrefix:Z.markdown.linkPrefix,listStyle:Z.markdown.listStyle,mark:Z.markdown.mark,mathBlockPreview:Z.markdown.mathBlockPreview,paragraphBeginningSpace:Z.markdown.paragraphBeginningSpace,sanitize:Z.markdown.sanitize,toc:Z.markdown.toc});return ue!=null&&ue.renderers&&G.SetJSRenderers({renderers:{Md2HTML:ue.renderers}}),G.SetHeadingID(!0),G.Md2HTML(U)})},O=function(U,ue,Z){return q(void 0,void 0,void 0,function(){var G,be,Y,Se;return J(this,function(he){switch(he.label){case 0:return G=se(Z),[4,X(ue,G)];case 1:if(be=he.sent(),G.transform&&(be=G.transform(be)),U.innerHTML=be,U.classList.add("vditor-reset"),G.i18n)return[3,5];if(["de_DE","en_US","fr_FR","pt_BR","ja_JP","ko_KR","ru_RU","sv_SE","zh_CN","zh_TW"].includes(G.lang))return[3,2];throw new Error("options.lang error, see https://ld246.com/article/1549638745630#options");case 2:return Y="vditorI18nScript",Se=Y+G.lang,document.querySelectorAll('head script[id^="'.concat(Y,'"]')).forEach(function(De){De.id!==Se&&document.head.removeChild(De)}),[4,(0,H.G)("".concat(G.cdn,"/dist/js/i18n/").concat(G.lang,".js"),Se)];case 3:he.sent(),he.label=4;case 4:return[3,6];case 5:window.VditorI18n=G.i18n,he.label=6;case 6:return G.icon?[4,(0,H.G)("".concat(G.cdn,"/dist/js/icons/").concat(G.icon,".js"),"vditorIconScript")]:[3,8];case 7:he.sent(),he.label=8;case 8:return(0,k.Z)(G.theme.current,G.theme.path),G.anchor===1&&U.classList.add("vditor-reset--anchor"),(0,c.O)(U,G.hljs),(0,y.s)(G.hljs,U,G.cdn),(0,C.H)(U,{cdn:G.cdn,math:G.math}),(0,S.i)(U,G.cdn,G.mode),(0,i.J)(U,G.cdn,G.mode),(0,s.K)(U,G.cdn),(0,_.P)(U,G.cdn),(0,g.v)(U,G.cdn),(0,d.p)(U,G.cdn,G.mode),(0,u.P)(U,G.cdn,G.mode),(0,v.B)(U,G.cdn),(0,R.Q)(U,G.cdn),G.render.media.enable&&(0,L.Y)(U),G.speech.enable&&B(U),G.anchor!==0&&I(G.anchor),G.after&&G.after(),G.lazyLoadImage&&p(U),U.addEventListener("click",function(De){var ie=(0,j.lG)(De.target,"SPAN");if(ie&&(0,j.fb)(ie,"vditor-toc")){var ke=U.querySelector("#"+ie.getAttribute("data-target-id"));ke&&window.scrollTo(window.scrollX,ke.offsetTop);return}}),[2]}})})},ve=D(190),Pe=D(580),Le=function(){function U(){}return U.adapterRender=N,U.previewImage=ve.E,U.codeRender=c.O,U.graphvizRender=g.v,U.highlightRender=y.s,U.mathRender=C.H,U.mermaidRender=S.i,U.SMILESRender=i.J,U.markmapRender=s.K,U.flowchartRender=_.P,U.chartRender=d.p,U.abcRender=R.Q,U.mindmapRender=u.P,U.plantumlRender=v.B,U.outlineRender=f.k,U.mediaRender=L.Y,U.speechRender=B,U.lazyLoadImageRender=p,U.md2html=X,U.preview=O,U.setCodeTheme=Pe.Y,U.setContentTheme=k.Z,U}();const _e=Le},145:(Q,M,D)=>{"use strict";D.d(M,{H:()=>R,g:()=>N});var R="3.11.1",N=function(){function d(){}return d.ZWSP="​",d.DROP_EDITOR="application/editor",d.MOBILE_WIDTH=520,d.CLASS_MENU_DISABLED="vditor-menu--disabled",d.EDIT_TOOLBARS=["emoji","headings","bold","italic","strike","link","list","ordered-list","outdent","indent","check","line","quote","code","inline-code","insert-after","insert-before","upload","record","table"],d.CODE_THEME=["a11y-dark","agate","an-old-hope","androidstudio","arta","atom-one-dark","atom-one-dark-reasonable","base16/3024","base16/apathy","base16/apprentice","base16/ashes","base16/atelier-cave","base16/atelier-dune","base16/atelier-estuary","base16/atelier-forest","base16/atelier-heath","base16/atelier-lakeside","base16/atelier-plateau","base16/atelier-savanna","base16/atelier-seaside","base16/atelier-sulphurpool","base16/atlas","base16/bespin","base16/black-metal","base16/black-metal-bathory","base16/black-metal-burzum","base16/black-metal-dark-funeral","base16/black-metal-gorgoroth","base16/black-metal-immortal","base16/black-metal-khold","base16/black-metal-marduk","base16/black-metal-mayhem","base16/black-metal-nile","base16/black-metal-venom","base16/brewer","base16/bright","base16/brogrammer","base16/brush-trees-dark","base16/chalk","base16/circus","base16/classic-dark","base16/codeschool","base16/colors","base16/danqing","base16/darcula","base16/dark-violet","base16/darkmoss","base16/darktooth","base16/decaf","base16/default-dark","base16/dracula","base16/edge-dark","base16/eighties","base16/embers","base16/equilibrium-dark","base16/equilibrium-gray-dark","base16/espresso","base16/eva","base16/eva-dim","base16/flat","base16/framer","base16/gigavolt","base16/google-dark","base16/grayscale-dark","base16/green-screen","base16/gruvbox-dark-hard","base16/gruvbox-dark-medium","base16/gruvbox-dark-pale","base16/gruvbox-dark-soft","base16/hardcore","base16/harmonic16-dark","base16/heetch-dark","base16/helios","base16/hopscotch","base16/horizon-dark","base16/humanoid-dark","base16/ia-dark","base16/icy-dark","base16/ir-black","base16/isotope","base16/kimber","base16/london-tube","base16/macintosh","base16/marrakesh","base16/materia","base16/material","base16/material-darker","base16/material-palenight","base16/material-vivid","base16/mellow-purple","base16/mocha","base16/monokai","base16/nebula","base16/nord","base16/nova","base16/ocean","base16/oceanicnext","base16/onedark","base16/outrun-dark","base16/papercolor-dark","base16/paraiso","base16/pasque","base16/phd","base16/pico","base16/pop","base16/porple","base16/qualia","base16/railscasts","base16/rebecca","base16/ros-pine","base16/ros-pine-moon","base16/sandcastle","base16/seti-ui","base16/silk-dark","base16/snazzy","base16/solar-flare","base16/solarized-dark","base16/spacemacs","base16/summercamp","base16/summerfruit-dark","base16/synth-midnight-terminal-dark","base16/tango","base16/tender","base16/tomorrow-night","base16/twilight","base16/unikitty-dark","base16/vulcan","base16/windows-10","base16/windows-95","base16/windows-high-contrast","base16/windows-nt","base16/woodland","base16/xcode-dusk","base16/zenburn","codepen-embed","dark","devibeans","far","felipec","github-dark","github-dark-dimmed","gml","gradient-dark","hybrid","ir-black","isbl-editor-dark","kimbie-dark","lioshi","monokai","monokai-sublime","night-owl","nnfx-dark","nord","obsidian","panda-syntax-dark","paraiso-dark","pojoaque","qtcreator-dark","rainbow","shades-of-purple","srcery","stackoverflow-dark","sunburst","tomorrow-night-blue","tomorrow-night-bright","tokyo-night-dark","vs2015","xt256","ant-design","a11y-light","arduino-light","ascetic","atom-one-light","base16/atelier-cave-light","base16/atelier-dune-light","base16/atelier-estuary-light","base16/atelier-forest-light","base16/atelier-heath-light","base16/atelier-lakeside-light","base16/atelier-plateau-light","base16/atelier-savanna-light","base16/atelier-seaside-light","base16/atelier-sulphurpool-light","base16/brush-trees","base16/classic-light","base16/cupcake","base16/cupertino","base16/default-light","base16/dirtysea","base16/edge-light","base16/equilibrium-gray-light","base16/equilibrium-light","base16/fruit-soda","base16/github","base16/google-light","base16/grayscale-light","base16/gruvbox-light-hard","base16/gruvbox-light-medium","base16/gruvbox-light-soft","base16/harmonic16-light","base16/heetch-light","base16/humanoid-light","base16/horizon-light","base16/ia-light","base16/material-lighter","base16/mexico-light","base16/one-light","base16/papercolor-light","base16/ros-pine-dawn","base16/sagelight","base16/shapeshifter","base16/silk-light","base16/solar-flare-light","base16/solarized-light","base16/summerfruit-light","base16/synth-midnight-terminal-light","base16/tomorrow","base16/unikitty-light","base16/windows-10-light","base16/windows-95-light","base16/windows-high-contrast-light","brown-paper","base16/windows-nt-light","color-brewer","docco","foundation","github","googlecode","gradient-light","grayscale","idea","intellij-light","isbl-editor-light","kimbie-light","lightfair","magula","mono-blue","nnfx-light","panda-syntax-light","paraiso-light","purebasic","qtcreator-light","routeros","school-book","stackoverflow-light","tokyo-night-light","vs","xcode","default"],d.ALIAS_CODE_LANGUAGES=["abc","plantuml","mermaid","flowchart","echarts","mindmap","graphviz","math","markmap","smiles","js","ts","html","toml","c#","bat"],d.CDN="https://unpkg.com/vditor@".concat("3.11.1"),d.MARKDOWN_OPTIONS={autoSpace:!1,gfmAutoLink:!0,codeBlockPreview:!0,fixTermTypo:!1,footnotes:!0,linkBase:"",linkPrefix:"",listStyle:!1,mark:!1,mathBlockPreview:!0,paragraphBeginningSpace:!1,sanitize:!0,toc:!1},d.HLJS_OPTIONS={enable:!0,lineNumber:!1,defaultLang:"",style:"github"},d.MATH_OPTIONS={engine:"KaTeX",inlineDigit:!1,macros:{}},d.THEME_OPTIONS={current:"light",list:{"ant-design":"Ant Design",dark:"Dark",light:"Light",wechat:"WeChat"},path:"".concat(d.CDN,"/dist/css/content-theme")},d}()},825:(Q,M,D)=>{"use strict";D.d(M,{J:()=>_});var R=D(145),N=D(413),d=D(840),c=D(494),_=function(g,y,p){g===void 0&&(g=document),y===void 0&&(y=R.g.CDN);var C=d.SMILESRenderAdapter.getElements(g);C.length>0&&(0,N.G)("".concat(y,"/dist/js/smiles-drawer/smiles-drawer.min.js?v=2.1.7"),"vditorAbcjsScript").then(function(){var L=new SmiDrawer({},{});C.forEach(function(S){var i=d.SMILESRenderAdapter.getCode(S).trim();if(!(S.getAttribute("data-processed")==="true"||i.trim()==="")){var s="smiles"+(0,c.Wb)();S.innerHTML='<svg id="'.concat(s,'"></svg>'),L.draw(i,"#"+s,p==="dark"?"dark":void 0),S.setAttribute("data-processed","true")}})})}},135:(Q,M,D)=>{"use strict";D.d(M,{Q:()=>c});var R=D(145),N=D(413),d=D(840),c=function(_,g){_===void 0&&(_=document),g===void 0&&(g=R.g.CDN);var y=d.abcRenderAdapter.getElements(_);y.length>0&&(0,N.G)("".concat(g,"/dist/js/abcjs/abcjs_basic.min.js"),"vditorAbcjsScript").then(function(){y.forEach(function(p){p.parentElement.classList.contains("vditor-wysiwyg__pre")||p.parentElement.classList.contains("vditor-ir__marker--pre")||p.getAttribute("data-processed")!=="true"&&(ABCJS.renderAbc(p,d.abcRenderAdapter.getCode(p).trim()),p.style.overflowX="auto",p.setAttribute("data-processed","true"))})})}},840:(Q,M,D)=>{"use strict";D.r(M),D.d(M,{mathRenderAdapter:()=>R,SMILESRenderAdapter:()=>N,mermaidRenderAdapter:()=>d,markmapRenderAdapter:()=>c,mindmapRenderAdapter:()=>_,chartRenderAdapter:()=>g,abcRenderAdapter:()=>y,graphvizRenderAdapter:()=>p,flowchartRenderAdapter:()=>C,plantumlRenderAdapter:()=>L});var R={getCode:function(S){return S.textContent},getElements:function(S){return S.querySelectorAll(".language-math")}},N={getCode:function(S){return S.textContent},getElements:function(S){return S.querySelectorAll(".language-smiles")}},d={getCode:function(S){return S.textContent},getElements:function(S){return S.querySelectorAll(".language-mermaid")}},c={getCode:function(S){return S.textContent},getElements:function(S){return S.querySelectorAll(".language-markmap")}},_={getCode:function(S){return S.getAttribute("data-code")},getElements:function(S){return S.querySelectorAll(".language-mindmap")}},g={getCode:function(S){return S.innerText},getElements:function(S){return S.querySelectorAll(".language-echarts")}},y={getCode:function(S){return S.textContent},getElements:function(S){return S.querySelectorAll(".language-abc")}},p={getCode:function(S){return S.textContent},getElements:function(S){return S.querySelectorAll(".language-graphviz")}},C={getCode:function(S){return S.textContent},getElements:function(S){return S.querySelectorAll(".language-flowchart")}},L={getCode:function(S){return S.textContent},getElements:function(S){return S.querySelectorAll(".language-plantuml")}}},775:(Q,M,D)=>{"use strict";D.d(M,{p:()=>y});var R=D(145),N=D(413),d=D(840),c=D(494),_=function(p,C,L,S){function i(s){return s instanceof L?s:new L(function(u){u(s)})}return new(L||(L=Promise))(function(s,u){function f(k){try{b(S.next(k))}catch(H){u(H)}}function v(k){try{b(S.throw(k))}catch(H){u(H)}}function b(k){k.done?s(k.value):i(k.value).then(f,v)}b((S=S.apply(p,C||[])).next())})},g=function(p,C){var L={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},S,i,s,u;return u={next:f(0),throw:f(1),return:f(2)},typeof Symbol=="function"&&(u[Symbol.iterator]=function(){return this}),u;function f(b){return function(k){return v([b,k])}}function v(b){if(S)throw new TypeError("Generator is already executing.");for(;u&&(u=0,b[0]&&(L=0)),L;)try{if(S=1,i&&(s=b[0]&2?i.return:b[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,b[1])).done)return s;switch(i=0,s&&(b=[b[0]&2,s.value]),b[0]){case 0:case 1:s=b;break;case 4:return L.label++,{value:b[1],done:!1};case 5:L.label++,i=b[1],b=[0];continue;case 7:b=L.ops.pop(),L.trys.pop();continue;default:if(s=L.trys,!(s=s.length>0&&s[s.length-1])&&(b[0]===6||b[0]===2)){L=0;continue}if(b[0]===3&&(!s||b[1]>s[0]&&b[1]<s[3])){L.label=b[1];break}if(b[0]===6&&L.label<s[1]){L.label=s[1],s=b;break}if(s&&L.label<s[2]){L.label=s[2],L.ops.push(b);break}s[2]&&L.ops.pop(),L.trys.pop();continue}b=C.call(p,L)}catch(k){b=[6,k],i=0}finally{S=s=0}if(b[0]&5)throw b[1];return{value:b[0]?b[1]:void 0,done:!0}}},y=function(p,C,L){p===void 0&&(p=document),C===void 0&&(C=R.g.CDN);var S=d.chartRenderAdapter.getElements(p);S.length>0&&(0,N.G)("".concat(C,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then(function(){S.forEach(function(i){return _(void 0,void 0,void 0,function(){var s,u,f;return g(this,function(v){switch(v.label){case 0:if(i.parentElement.classList.contains("vditor-wysiwyg__pre")||i.parentElement.classList.contains("vditor-ir__marker--pre"))return[2];if(s=d.chartRenderAdapter.getCode(i).trim(),!s)return[2];v.label=1;case 1:return v.trys.push([1,3,,4]),i.getAttribute("data-processed")==="true"?[2]:[4,(0,c.Qf)(s)];case 2:return u=v.sent(),echarts.init(i,L==="dark"?"dark":void 0).setOption(u),i.setAttribute("data-processed","true"),[3,4];case 3:return f=v.sent(),i.className="vditor-reset--error",i.innerHTML="echarts render error: <br>".concat(f),[3,4];case 4:return[2]}})})})})}},428:(Q,M,D)=>{"use strict";D.d(M,{O:()=>d});var R=D(105),N=D(145),d=function(c,_){Array.from(c.querySelectorAll("pre > code")).filter(function(g,y){return!(g.parentElement.classList.contains("vditor-wysiwyg__pre")||g.parentElement.classList.contains("vditor-ir__marker--pre")||g.classList.contains("language-mermaid")||g.classList.contains("language-flowchart")||g.classList.contains("language-echarts")||g.classList.contains("language-mindmap")||g.classList.contains("language-plantuml")||g.classList.contains("language-markmap")||g.classList.contains("language-abc")||g.classList.contains("language-graphviz")||g.classList.contains("language-math")||g.classList.contains("language-smiles")||g.style.maxHeight.indexOf("px")>-1||c.classList.contains("vditor-preview")&&y>5)}).forEach(function(g){var y,p,C,L=g.innerText;if(g.classList.contains("highlight-chroma")){var S=g.cloneNode(!0);S.querySelectorAll(".highlight-ln").forEach(function(f){f.remove()}),L=S.innerText}else L.endsWith(`
`)&&(L=L.substr(0,L.length-1));var i='<svg><use xlink:href="#vditor-icon-copy"></use></svg>';document.getElementById("vditorIconScript")||(i='<svg viewBox="0 0 32 32"><path d="M22.545-0h-17.455c-1.6 0-2.909 1.309-2.909 2.909v20.364h2.909v-20.364h17.455v-2.909zM26.909 5.818h-16c-1.6 0-2.909 1.309-2.909 2.909v20.364c0 1.6 1.309 2.909 2.909 2.909h16c1.6 0 2.909-1.309 2.909-2.909v-20.364c0-1.6-1.309-2.909-2.909-2.909zM26.909 29.091h-16v-20.364h16v20.364z"></path></svg>');var s=document.createElement("div");s.className="vditor-copy",s.innerHTML='<span aria-label="'.concat(((y=window.VditorI18n)===null||y===void 0?void 0:y.copy)||"复制",`"
onmouseover="this.setAttribute('aria-label', '`).concat(((p=window.VditorI18n)===null||p===void 0?void 0:p.copy)||"复制",`')"
class="vditor-tooltipped vditor-tooltipped__w"
onclick="event.stopPropagation();this.previousElementSibling.select();document.execCommand('copy');this.setAttribute('aria-label', '`).concat(((C=window.VditorI18n)===null||C===void 0?void 0:C.copied)||"已复制",`');this.previousElementSibling.blur()">`).concat(i,"</span>");var u=document.createElement("textarea");u.value=(0,R.X)(L),s.insertAdjacentElement("afterbegin",u),_&&_.renderMenu&&_.renderMenu(g,s),g.before(s),g.style.maxHeight=window.outerHeight-40+"px",g.insertAdjacentHTML("afterend",'<span style="position: absolute">'.concat(N.g.ZWSP,"</span>"))})}},325:(Q,M,D)=>{"use strict";D.d(M,{P:()=>c});var R=D(145),N=D(413),d=D(840),c=function(_,g){g===void 0&&(g=R.g.CDN);var y=d.flowchartRenderAdapter.getElements(_);y.length!==0&&(0,N.G)("".concat(g,"/dist/js/flowchart.js/flowchart.min.js"),"vditorFlowchartScript").then(function(){y.forEach(function(p){if(p.getAttribute("data-processed")!=="true"){var C=flowchart.parse(d.flowchartRenderAdapter.getCode(p));p.innerHTML="",C.drawSVG(p),p.setAttribute("data-processed","true")}})})}},483:(Q,M,D)=>{"use strict";D.d(M,{v:()=>c});var R=D(145),N=D(413),d=D(840),c=function(_,g){g===void 0&&(g=R.g.CDN);var y=d.graphvizRenderAdapter.getElements(_);y.length!==0&&(0,N.G)("".concat(g,"/dist/js/graphviz/viz.js"),"vditorGraphVizScript").then(function(){y.forEach(function(p){var C=d.graphvizRenderAdapter.getCode(p);if(!(p.parentElement.classList.contains("vditor-wysiwyg__pre")||p.parentElement.classList.contains("vditor-ir__marker--pre"))&&!(p.getAttribute("data-processed")==="true"||C.trim()==="")){try{var L=new Blob(["importScripts('".concat(document.getElementById("vditorGraphVizScript").src.replace("viz.js","full.render.js"),"');")],{type:"application/javascript"}),S=window.URL||window.webkitURL,i=S.createObjectURL(L),s=new Worker(i);new Viz({worker:s}).renderSVGElement(C).then(function(u){p.innerHTML=u.outerHTML}).catch(function(u){p.innerHTML="graphviz render error: <br>".concat(u),p.className="vditor-reset--error"})}catch(u){}p.setAttribute("data-processed","true")}})})}},999:(Q,M,D)=>{"use strict";D.d(M,{s:()=>c});var R=D(145),N=D(413),d=D(290),c=function(_,g,y){g===void 0&&(g=document),y===void 0&&(y=R.g.CDN);var p=_.style;R.g.CODE_THEME.includes(p)||(p="github");var C=document.getElementById("vditorHljsStyle"),L="".concat(y,"/dist/js/highlight.js/styles/").concat(p,".min.css");if(C&&C.getAttribute("href")!==L&&C.remove(),(0,d.c)("".concat(y,"/dist/js/highlight.js/styles/").concat(p,".min.css"),"vditorHljsStyle"),_.enable!==!1){var S=g.querySelectorAll("pre > code");S.length!==0&&(0,N.G)("".concat(y,"/dist/js/highlight.js/highlight.min.js?v=11.7.0"),"vditorHljsScript").then(function(){(0,N.G)("".concat(y,"/dist/js/highlight.js/third-languages.js?v=1.0.1"),"vditorHljsThirdScript").then(function(){g.querySelectorAll("pre > code").forEach(function(i){if(!(i.parentElement.classList.contains("vditor-ir__marker--pre")||i.parentElement.classList.contains("vditor-wysiwyg__pre"))&&!(i.classList.contains("language-mermaid")||i.classList.contains("language-flowchart")||i.classList.contains("language-echarts")||i.classList.contains("language-mindmap")||i.classList.contains("language-plantuml")||i.classList.contains("language-smiles")||i.classList.contains("language-abc")||i.classList.contains("language-graphviz")||i.classList.contains("language-math"))){_.defaultLang!==""&&i.className.indexOf("language-")===-1&&i.classList.add("language-"+_.defaultLang);var s=i.className.replace("language-","");if(window.hljs.getLanguage(s)||(s="plaintext"),i.innerHTML=window.hljs.highlight(i.textContent,{language:s,ignoreIllegals:!0}).value,i.classList.add("hljs"),!!_.lineNumber){i.classList.add("vditor-linenumber");var u=i.querySelector(".vditor-linenumber__temp");u||(u=document.createElement("div"),u.className="vditor-linenumber__temp",i.insertAdjacentElement("beforeend",u));var f=getComputedStyle(i).whiteSpace,v=!1;(f==="pre-wrap"||f==="pre-line")&&(v=!0);var b="",k=i.textContent.split(/\r\n|\r|\n/g);k.pop(),k.map(function(H){var j="";v&&(u.textContent=H||`
`,j=' style="height:'.concat(u.getBoundingClientRect().height,'px"')),b+="<span".concat(j,"></span>")}),u.style.display="none",b='<span class="vditor-linenumber__rows">'.concat(b,"</span>"),i.insertAdjacentHTML("beforeend",b)}}})})})}}},11:(Q,M,D)=>{"use strict";D.d(M,{K:()=>y});var R=D(145),N=D(413),d=D(840),c={},_=function(p,C){var L=p.transform(C),S=Object.keys(L.features).filter(function(v){return!c[v]});S.forEach(function(v){c[v]=!0});var i=p.getAssets(S),s=i.styles,u=i.scripts,f=window.markmap;return s&&f.loadCSS(s),u&&f.loadJS(u),L},g=function(p,C){var L=window.markmap,S=L.Transformer,i=L.Markmap,s=L.deriveOptions,u=L.globalCSS,f=new S;p.innerHTML='<svg style="width:100%"></svg>';var v=p.firstChild,b=i.create(v,null),k=_(f,C),H=k.root,j=k.frontmatter,x=j==null?void 0:j.markmap,I=s(x);b.setData(H,I),b.fit()},y=function(p,C){p===void 0&&(p=document),C===void 0&&(C=R.g.CDN);var L=d.markmapRenderAdapter.getElements(p);L.length!==0&&(0,N.G)("".concat(C,"/dist/js/markmap/markmap.min.js"),"vditorMarkerScript").then(function(){L.forEach(function(S){var i=d.markmapRenderAdapter.getCode(S);if(!(S.getAttribute("data-processed")==="true"||i.trim()==="")){var s=document.createElement("div");s.className="language-markmap",S.parentNode.appendChild(s),g(s,i),S.parentNode.childNodes[0].nodeName=="CODE"&&S.parentNode.removeChild(S.parentNode.childNodes[0])}})})}},472:(Q,M,D)=>{"use strict";D.d(M,{H:()=>g});var R=D(145),N=D(413),d=D(290),c=D(105),_=D(840),g=function(y,p){y===void 0&&(y=document);var C=_.mathRenderAdapter.getElements(y);if(C.length!==0){var L={cdn:R.g.CDN,math:{engine:"KaTeX",inlineDigit:!1,macros:{}}};if(p&&p.math&&(p.math=Object.assign({},L.math,p.math)),p=Object.assign({},L,p),p.math.engine==="KaTeX")(0,d.c)("".concat(p.cdn,"/dist/js/katex/katex.min.css?v=0.16.9"),"vditorKatexStyle"),(0,N.G)("".concat(p.cdn,"/dist/js/katex/katex.min.js?v=0.16.9"),"vditorKatexScript").then(function(){(0,N.G)("".concat(p.cdn,"/dist/js/katex/mhchem.min.js?v=0.16.9"),"vditorKatexChemScript").then(function(){C.forEach(function(s){if(!(s.parentElement.classList.contains("vditor-wysiwyg__pre")||s.parentElement.classList.contains("vditor-ir__marker--pre"))&&!s.getAttribute("data-math")){var u=(0,c.X)(_.mathRenderAdapter.getCode(s));s.setAttribute("data-math",u);try{s.innerHTML=katex.renderToString(u,{displayMode:s.tagName==="DIV",output:"html",macros:p.math.macros})}catch(f){s.innerHTML=f.message,s.className="language-math vditor-reset--error"}s.addEventListener("copy",function(f){f.stopPropagation(),f.preventDefault();var v=f.currentTarget.closest(".language-math");f.clipboardData.setData("text/html",v.innerHTML),f.clipboardData.setData("text/plain",v.getAttribute("data-math"))})}})})});else if(p.math.engine==="MathJax"){var S=function(s){if(s.length!==0){var u=0,f=s[s.length-1],v=function(){var b=s[u++];b===f?b():b(v)};v()}};window.MathJax||(window.MathJax={loader:{paths:{mathjax:"".concat(p.cdn,"/dist/js/mathjax")}},startup:{typeset:!1},tex:{macros:p.math.macros}},Object.assign(window.MathJax,p.math.mathJaxOptions)),(0,N.J)("".concat(p.cdn,"/dist/js/mathjax/tex-svg-full.js"),"protyleMathJaxScript");var i=function(s,u){var f=(0,c.X)(s.textContent).trim(),v=window.MathJax.getMetricsFor(s);v.display=s.tagName==="DIV",window.MathJax.tex2svgPromise(f,v).then(function(b){s.innerHTML="",s.setAttribute("data-math",f),s.append(b),window.MathJax.startup.document.clear(),window.MathJax.startup.document.updateDocument();var k=b.querySelector('[data-mml-node="merror"]');k&&k.textContent.trim()!==""&&(s.innerHTML=k.textContent.trim(),s.className="vditor-reset--error"),u&&u()})};window.MathJax.startup.promise.then(function(){for(var s=[],u=function(v){var b=C[v];!b.parentElement.classList.contains("vditor-wysiwyg__pre")&&!b.parentElement.classList.contains("vditor-ir__marker--pre")&&!b.getAttribute("data-math")&&(0,c.X)(b.textContent).trim()&&s.push(function(k){v===C.length-1?i(b):i(b,k)})},f=0;f<C.length;f++)u(f);S(s)})}}}},280:(Q,M,D)=>{"use strict";D.d(M,{Y:()=>_});var R=D(494),N=function(g,y){g.insertAdjacentHTML("afterend",'<video controls="controls" src="'.concat(y,'"></video>')),g.remove()},d=function(g,y){g.insertAdjacentHTML("afterend",'<audio controls="controls" src="'.concat(y,'"></audio>')),g.remove()},c=function(g,y){var p=y.match(/\/\/(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([\w|-]{11})(?:(?:[\?&]t=)(\S+))?/),C=y.match(/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/),L=y.match(/\/\/v\.qq\.com\/x\/cover\/.*\/([^\/]+)\.html\??.*/),S=y.match(/(?:www\.|\/\/)coub\.com\/view\/(\w+)/),i=y.match(/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/),s=y.match(/.+dailymotion.com\/(video|hub)\/(\w+)\?/),u=y.match(/(?:www\.|\/\/)bilibili\.com\/video\/(\w+)/),f=y.match(/(?:www\.|\/\/)ted\.com\/talks\/(\w+)/);if(p&&p[1].length===11)g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//www.youtube.com/embed/'.concat(p[1]+(p[2]?"?start="+p[2]:""),'"></iframe>')),g.remove();else if(C&&C[1])g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//player.youku.com/embed/'.concat(C[1],'"></iframe>')),g.remove();else if(L&&L[1])g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="https://v.qq.com/txp/iframe/player.html?vid='.concat(L[1],'"></iframe>')),g.remove();else if(S&&S[1])g.insertAdjacentHTML("afterend",`<iframe class="iframe__video"
 src="//coub.com/embed/`.concat(S[1],'?muted=false&autostart=false&originalSize=true&startWithHD=true"></iframe>')),g.remove();else if(i&&i[0])g.insertAdjacentHTML("afterend",`<iframe class="iframe__video"
 src="https://www.facebook.com/plugins/video.php?href=`.concat(encodeURIComponent(i[0]),'"></iframe>')),g.remove();else if(s&&s[2])g.insertAdjacentHTML("afterend",`<iframe class="iframe__video"
 src="https://www.dailymotion.com/embed/video/`.concat(s[2],'"></iframe>')),g.remove();else if(y.indexOf("bilibili.com")>-1&&(y.indexOf("bvid=")>-1||u&&u[1])){var v={bvid:(0,R.on)("bvid",y)||u&&u[1],page:"1",high_quality:"1",as_wide:"1",allowfullscreen:"true",autoplay:"0"};new URL(y.startsWith("http")?y:"https:"+y).search.split("&").forEach(function(H,j){if(H){j===0&&(H=H.substr(1));var x=H.split("=");v[x[0]]=x[1]}});var b="https://player.bilibili.com/player.html?",k=Object.keys(v);k.forEach(function(H,j){b+="".concat(H,"=").concat(v[H]),j<k.length-1&&(b+="&")}),g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="'.concat(b,'"></iframe>')),g.remove()}else f&&f[1]&&(g.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//embed.ted.com/talks/'.concat(f[1],'"></iframe>')),g.remove())},_=function(g){g&&g.querySelectorAll("a").forEach(function(y){var p=y.getAttribute("href");p&&(p.match(/^.+.(mp4|m4v|ogg|ogv|webm)$/)?N(y,p):p.match(/^.+.(mp3|wav|flac)$/)?d(y,p):c(y,p))})}},637:(Q,M,D)=>{"use strict";D.d(M,{i:()=>y});var R=D(145),N=D(413),d=D(840),c=D(494),_=function(p,C,L,S){function i(s){return s instanceof L?s:new L(function(u){u(s)})}return new(L||(L=Promise))(function(s,u){function f(k){try{b(S.next(k))}catch(H){u(H)}}function v(k){try{b(S.throw(k))}catch(H){u(H)}}function b(k){k.done?s(k.value):i(k.value).then(f,v)}b((S=S.apply(p,C||[])).next())})},g=function(p,C){var L={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},S,i,s,u;return u={next:f(0),throw:f(1),return:f(2)},typeof Symbol=="function"&&(u[Symbol.iterator]=function(){return this}),u;function f(b){return function(k){return v([b,k])}}function v(b){if(S)throw new TypeError("Generator is already executing.");for(;u&&(u=0,b[0]&&(L=0)),L;)try{if(S=1,i&&(s=b[0]&2?i.return:b[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,b[1])).done)return s;switch(i=0,s&&(b=[b[0]&2,s.value]),b[0]){case 0:case 1:s=b;break;case 4:return L.label++,{value:b[1],done:!1};case 5:L.label++,i=b[1],b=[0];continue;case 7:b=L.ops.pop(),L.trys.pop();continue;default:if(s=L.trys,!(s=s.length>0&&s[s.length-1])&&(b[0]===6||b[0]===2)){L=0;continue}if(b[0]===3&&(!s||b[1]>s[0]&&b[1]<s[3])){L.label=b[1];break}if(b[0]===6&&L.label<s[1]){L.label=s[1],s=b;break}if(s&&L.label<s[2]){L.label=s[2],L.ops.push(b);break}s[2]&&L.ops.pop(),L.trys.pop();continue}b=C.call(p,L)}catch(k){b=[6,k],i=0}finally{S=s=0}if(b[0]&5)throw b[1];return{value:b[0]?b[1]:void 0,done:!0}}},y=function(p,C,L){p===void 0&&(p=document),C===void 0&&(C=R.g.CDN);var S=d.mermaidRenderAdapter.getElements(p);S.length!==0&&(0,N.G)("".concat(C,"/dist/js/mermaid/mermaid.min.js?v=11.6.0"),"vditorMermaidScript").then(function(){var i={securityLevel:"loose",altFontFamily:"sans-serif",fontFamily:"sans-serif",startOnLoad:!1,flowchart:{htmlLabels:!0,useMaxWidth:!0},sequence:{useMaxWidth:!0,diagramMarginX:8,diagramMarginY:8,boxMargin:8,showSequenceNumbers:!0},gantt:{leftPadding:75,rightPadding:20}};L==="dark"&&(i.theme="dark"),mermaid.initialize(i),S.forEach(function(s){return _(void 0,void 0,void 0,function(){var u,f,v,b,k;return g(this,function(H){switch(H.label){case 0:if(u=d.mermaidRenderAdapter.getCode(s),s.getAttribute("data-processed")==="true"||u.trim()==="")return[2];f="mermaid"+(0,c.Wb)(),H.label=1;case 1:return H.trys.push([1,3,,4]),[4,mermaid.render(f,s.textContent)];case 2:return v=H.sent(),s.innerHTML=v.svg,[3,4];case 3:return b=H.sent(),k=document.querySelector("#"+f),s.innerHTML="".concat(k.outerHTML,`<br>
<div style="text-align: left"><small>`).concat(b.message.replace(/\n/,"<br>"),"</small></div>"),k.parentElement.remove(),[3,4];case 4:return s.setAttribute("data-processed","true"),[2]}})})})})}},194:(Q,M,D)=>{"use strict";D.d(M,{P:()=>c});var R=D(145),N=D(413),d=D(840),c=function(_,g,y){_===void 0&&(_=document),g===void 0&&(g=R.g.CDN);var p=d.mindmapRenderAdapter.getElements(_);p.length>0&&(0,N.G)("".concat(g,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then(function(){p.forEach(function(C){if(!(C.parentElement.classList.contains("vditor-wysiwyg__pre")||C.parentElement.classList.contains("vditor-ir__marker--pre"))){var L=d.mindmapRenderAdapter.getCode(C);if(L)try{if(C.getAttribute("data-processed")==="true")return;echarts.init(C,y==="dark"?"dark":void 0).setOption({series:[{data:[JSON.parse(decodeURIComponent(L))],initialTreeDepth:-1,itemStyle:{borderWidth:0,color:"#4285f4"},label:{backgroundColor:"#f6f8fa",borderColor:"#d1d5da",borderRadius:5,borderWidth:.5,color:"#586069",lineHeight:20,offset:[-5,0],padding:[0,5],position:"insideRight"},lineStyle:{color:"#d1d5da",width:1},roam:!0,symbol:function(S,i){var s;return!((s=i==null?void 0:i.data)===null||s===void 0)&&s.children?"circle":"path://"},type:"tree"}],tooltip:{trigger:"item",triggerOn:"mousemove"}}),C.setAttribute("data-processed","true")}catch(S){C.className="vditor-reset--error",C.innerHTML="mindmap render error: <br>".concat(S)}}})})}},436:(Q,M,D)=>{"use strict";D.d(M,{k:()=>d});var R=D(771),N=D(472),d=function(c,_,g){var y="",p=[];if(Array.from(c.children).forEach(function(i,s){if((0,R.W)(i)){if(g){var u=i.id.lastIndexOf("_");i.id=i.id.substring(0,u===-1?void 0:u)+"_"+s}p.push(i.id),y+=i.outerHTML.replace("<wbr>","")}}),y==="")return _.innerHTML="","";var C=document.createElement("div");if(g)g.lute.SetToC(!0),g.currentMode==="wysiwyg"&&!g.preview.element.contains(c)?C.innerHTML=g.lute.SpinVditorDOM("<p>[ToC]</p>"+y):g.currentMode==="ir"&&!g.preview.element.contains(c)?C.innerHTML=g.lute.SpinVditorIRDOM("<p>[ToC]</p>"+y):C.innerHTML=g.lute.HTML2VditorDOM("<p>[ToC]</p>"+y),g.lute.SetToC(g.options.preview.markdown.toc);else{_.classList.add("vditor-outline");var L=Lute.New();L.SetToC(!0),C.innerHTML=L.HTML2VditorDOM("<p>[ToC]</p>"+y)}var S=C.firstElementChild.querySelectorAll("li > span[data-target-id]");return S.forEach(function(i,s){if(i.nextElementSibling&&i.nextElementSibling.tagName==="UL"){var u="<svg class='vditor-outline__action'><use xlink:href='#vditor-icon-down'></use></svg>";document.getElementById("vditorIconScript")||(u='<svg class="vditor-outline__action" viewBox="0 0 32 32"><path d="M3.76 6.12l12.24 12.213 12.24-12.213 3.76 3.76-16 16-16-16 3.76-3.76z"></path></svg>'),i.innerHTML="".concat(u,"<span>").concat(i.innerHTML,"</span>")}else i.innerHTML="<svg></svg><span>".concat(i.innerHTML,"</span>");i.setAttribute("data-target-id",p[s])}),y=C.firstElementChild.innerHTML,S.length===0?(_.innerHTML="",y):(_.innerHTML=y,g&&(0,N.H)(_,{cdn:g.options.cdn,math:g.options.preview.math}),_.firstElementChild.addEventListener("click",function(i){for(var s=i.target;s&&!s.isEqualNode(_);){if(s.classList.contains("vditor-outline__action")){s.classList.contains("vditor-outline__action--close")?(s.classList.remove("vditor-outline__action--close"),s.parentElement.nextElementSibling.setAttribute("style","display:block")):(s.classList.add("vditor-outline__action--close"),s.parentElement.nextElementSibling.setAttribute("style","display:none")),i.preventDefault(),i.stopPropagation();break}else if(s.getAttribute("data-target-id")){i.preventDefault(),i.stopPropagation();var u=document.getElementById(s.getAttribute("data-target-id"));if(!u)return;if(g)if(g.options.height==="auto"){var f=u.offsetTop+g.element.offsetTop;g.options.toolbarConfig.pin||(f+=g.toolbar.element.offsetHeight),window.scrollTo(window.scrollX,f)}else g.element.offsetTop<window.scrollY&&window.scrollTo(window.scrollX,g.element.offsetTop),g.preview.element.contains(c)?c.parentElement.scrollTop=u.offsetTop:c.scrollTop=u.offsetTop;else window.scrollTo(window.scrollX,u.offsetTop);break}s=s.parentElement}}),y)}},229:(Q,M,D)=>{"use strict";D.d(M,{B:()=>c});var R=D(145),N=D(413),d=D(840),c=function(_,g){_===void 0&&(_=document),g===void 0&&(g=R.g.CDN);var y=d.plantumlRenderAdapter.getElements(_);y.length!==0&&(0,N.G)("".concat(g,"/dist/js/plantuml/plantuml-encoder.min.js"),"vditorPlantumlScript").then(function(){y.forEach(function(p){if(!(p.parentElement.classList.contains("vditor-wysiwyg__pre")||p.parentElement.classList.contains("vditor-ir__marker--pre"))){var C=d.plantumlRenderAdapter.getCode(p).trim();if(C)try{p.innerHTML='<object type="image/svg+xml" data="https://www.plantuml.com/plantuml/svg/~1'.concat(plantumlEncoder.encode(C),'"/>')}catch(L){p.className="vditor-reset--error",p.innerHTML="plantuml render error: <br>".concat(L)}}})})}},214:(Q,M,D)=>{"use strict";D.d(M,{X:()=>R});var R=function(N){var d=Lute.New();return d.PutEmojis(N.emojis),d.SetEmojiSite(N.emojiSite),d.SetHeadingAnchor(N.headingAnchor),d.SetInlineMathAllowDigitAfterOpenMarker(N.inlineMathDigit),d.SetAutoSpace(N.autoSpace),d.SetToC(N.toc),d.SetFootnotes(N.footnotes),d.SetFixTermTypo(N.fixTermTypo),d.SetVditorCodeBlockPreview(N.codeBlockPreview),d.SetVditorMathBlockPreview(N.mathBlockPreview),d.SetSanitize(N.sanitize),d.SetChineseParagraphBeginningSpace(N.paragraphBeginningSpace),d.SetRenderListStyle(N.listStyle),d.SetLinkBase(N.linkBase),d.SetLinkPrefix(N.linkPrefix),d.SetMark(N.mark),d.SetGFMAutoLink(N.gfmAutoLink),N.lazyLoadImage&&d.SetImageLazyLoading(N.lazyLoadImage),d}},190:(Q,M,D)=>{"use strict";D.d(M,{E:()=>R});var R=function(N,d,c){d===void 0&&(d="zh_CN"),c===void 0&&(c="classic");var _=N.getBoundingClientRect(),g=36;document.body.insertAdjacentHTML("beforeend",'<div class="vditor vditor-img'.concat(c==="dark"?" vditor--dark":"",`">
    <div class="vditor-img__bar">
      <span class="vditor-img__btn" data-deg="0">
        <svg><use xlink:href="#vditor-icon-redo"></use></svg>
        `).concat(window.VditorI18n.spin,`
      </span>
      <span class="vditor-img__btn"  onclick="this.parentElement.parentElement.outerHTML = '';document.body.style.overflow = ''">
        X &nbsp;`).concat(window.VditorI18n.close,`
      </span>
    </div>
    <div class="vditor-img__img" onclick="this.parentElement.outerHTML = '';document.body.style.overflow = ''">
      <img style="width: `).concat(N.width,"px;height:").concat(N.height,"px;transform: translate3d(").concat(_.left,"px, ").concat(_.top-g,'px, 0)" src="').concat(N.getAttribute("src"),`">
    </div>
</div>`)),document.body.style.overflow="hidden";var y=document.querySelector(".vditor-img img"),p="translate3d(".concat(Math.max(0,window.innerWidth-N.naturalWidth)/2,"px, ").concat(Math.max(0,window.innerHeight-g-N.naturalHeight)/2,"px, 0)");setTimeout(function(){y.setAttribute("style","transition: transform .3s ease-in-out;transform: ".concat(p)),setTimeout(function(){y.parentElement.scrollTo((y.parentElement.scrollWidth-y.parentElement.clientWidth)/2,(y.parentElement.scrollHeight-y.parentElement.clientHeight)/2)},400)});var C=document.querySelector(".vditor-img__btn");C.addEventListener("click",function(){var L=parseInt(C.getAttribute("data-deg"),10)+90;L/90%2===1&&N.naturalWidth>y.parentElement.clientHeight?y.style.transform="translate3d(".concat(Math.max(0,window.innerWidth-N.naturalWidth)/2,"px, ").concat(N.naturalWidth/2-N.naturalHeight/2,"px, 0) rotateZ(").concat(L,"deg)"):y.style.transform="".concat(p," rotateZ(").concat(L,"deg)"),C.setAttribute("data-deg",L.toString()),setTimeout(function(){y.parentElement.scrollTo((y.parentElement.scrollWidth-y.parentElement.clientWidth)/2,(y.parentElement.scrollHeight-y.parentElement.clientHeight)/2)},400)})}},580:(Q,M,D)=>{"use strict";D.d(M,{Y:()=>d});var R=D(145),N=D(290),d=function(c,_){_===void 0&&(_=R.g.CDN),R.g.CODE_THEME.includes(c)||(c="github");var g=document.getElementById("vditorHljsStyle"),y="".concat(_,"/dist/js/highlight.js/styles/").concat(c,".min.css");g?g.getAttribute("href")!==y&&(g.remove(),(0,N.c)(y,"vditorHljsStyle")):(0,N.c)(y,"vditorHljsStyle")}},538:(Q,M,D)=>{"use strict";D.d(M,{Z:()=>N});var R=D(290),N=function(d,c){if(!(!d||!c)){var _=document.getElementById("vditorContentTheme"),g="".concat(c,"/").concat(d,".css");_?_.getAttribute("href")!==g&&(_.remove(),(0,R.c)(g,"vditorContentTheme")):(0,R.c)(g,"vditorContentTheme")}}},413:(Q,M,D)=>{"use strict";D.d(M,{J:()=>R,G:()=>N});var R=function(d,c){if(document.getElementById(c))return!1;var _=new XMLHttpRequest;_.open("GET",d,!1),_.setRequestHeader("Accept","text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01"),_.send("");var g=document.createElement("script");g.type="text/javascript",g.text=_.responseText,g.id=c,document.head.appendChild(g)},N=function(d,c){return new Promise(function(_,g){if(document.getElementById(c))return _(!0),!1;var y=document.createElement("script");y.src=d,y.async=!0,document.head.appendChild(y),y.onerror=function(p){g(p)},y.onload=function(){if(document.getElementById(c))return y.remove(),_(!0),!1;y.id=c,_(!0)}})}},290:(Q,M,D)=>{"use strict";D.d(M,{c:()=>R});var R=function(N,d){if(!document.getElementById(d)){var c=document.createElement("link");c.id=d,c.rel="stylesheet",c.type="text/css",c.href=N,document.getElementsByTagName("head")[0].appendChild(c)}}},105:(Q,M,D)=>{"use strict";D.d(M,{X:()=>R});var R=function(N){return N.replace(/\u00a0/g," ")}},410:(Q,M,D)=>{"use strict";D.d(M,{G6:()=>R,vU:()=>N,pK:()=>d,Le:()=>c,yl:()=>_,ns:()=>g,i7:()=>y});var R=function(){return navigator.userAgent.indexOf("Safari")>-1&&navigator.userAgent.indexOf("Chrome")===-1},N=function(){return navigator.userAgent.toLowerCase().indexOf("firefox")>-1},d=function(){try{return typeof localStorage!="undefined"}catch(p){return!1}},c=function(){return navigator.userAgent.indexOf("iPhone")>-1?"touchstart":"click"},_=function(p){return navigator.platform.toUpperCase().indexOf("MAC")>=0?!!(p.metaKey&&!p.ctrlKey):!!(!p.metaKey&&p.ctrlKey)},g=function(p){return/Mac/.test(navigator.platform)||navigator.platform==="iPhone"?p.indexOf("⇧")>-1&&N()&&(p=p.replace(";",":").replace("=","+").replace("-","_")):(p.startsWith("⌘")?p=p.replace("⌘","⌘+"):p.startsWith("⌥")&&p.substr(1,1)!=="⌘"?p=p.replace("⌥","⌥+"):p=p.replace("⇧⌘","⌘+⇧+").replace("⌥⌘","⌥+⌘+"),p=p.replace("⌘","Ctrl").replace("⇧","Shift").replace("⌥","Alt"),p.indexOf("Shift")>-1&&(p=p.replace(";",":").replace("=","+").replace("-","_"))),p},y=function(){return/Chrome/.test(navigator.userAgent)&&/Google Inc/.test(navigator.vendor)}},494:(Q,M,D)=>{"use strict";D.d(M,{Wb:()=>R,on:()=>N,Qf:()=>d});var R=function(){return([1e7].toString()+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g,function(c){return(parseInt(c,10)^window.crypto.getRandomValues(new Uint32Array(1))[0]&15>>parseInt(c,10)/4).toString(16)})},N=function(c,_){_===void 0&&(_=window.location.search);var g=_.substring(_.indexOf("?")),y=g.indexOf("#"),p=new URLSearchParams(g.substring(0,y>=0?y:void 0));return p.get(c)},d=function(c){return Function('"use strict";return ('.concat(c,")"))()}},106:(Q,M,D)=>{"use strict";D.d(M,{JQ:()=>N,E2:()=>c,O9:()=>_,a1:()=>g,F9:()=>y,lG:()=>p,fb:()=>C,DX:()=>L});var R=D(771),N=function(S,i){for(var s=C(S,i),u=!1,f=!1;s&&!s.classList.contains("vditor-reset")&&!f;)u=C(s.parentElement,i),u?s=u:f=!0;return s||!1},d=function(S,i,s){for(var u=g(S,i,s),f=!1,v=!1;u&&!u.classList.contains("vditor-reset")&&!v;)f=g(u.parentElement,i,s),f?u=f:v=!0;return u||!1},c=function(S,i){for(var s=(0,R.S)(S,i),u=!1,f=!1;s&&!s.classList.contains("vditor-reset")&&!f;)u=(0,R.S)(s.parentElement,i),u?s=u:f=!0;return s||!1},_=function(S){var i=c(S,"UL"),s=c(S,"OL"),u=i;return s&&(!i||i&&s.contains(i))&&(u=s),u},g=function(S,i,s){if(!S)return!1;S.nodeType===3&&(S=S.parentElement);for(var u=S,f=!1;u&&!f&&!u.classList.contains("vditor-reset");)u.getAttribute(i)===s?f=!0:u=u.parentElement;return f&&u},y=function(S){if(!S)return!1;S.nodeType===3&&(S=S.parentElement);var i=S,s=!1,u=g(S,"data-block","0");if(u)return u;for(;i&&!s&&!i.classList.contains("vditor-reset");)i.tagName==="H1"||i.tagName==="H2"||i.tagName==="H3"||i.tagName==="H4"||i.tagName==="H5"||i.tagName==="H6"||i.tagName==="P"||i.tagName==="BLOCKQUOTE"||i.tagName==="OL"||i.tagName==="UL"?s=!0:i=i.parentElement;return s&&i},p=function(S,i){if(!S)return!1;S.nodeType===3&&(S=S.parentElement);for(var s=S,u=!1;s&&!u&&!s.classList.contains("vditor-reset");)s.nodeName===i?u=!0:s=s.parentElement;return u&&s},C=function(S,i){if(!S)return!1;S.nodeType===3&&(S=S.parentElement);for(var s=S,u=!1;s&&!u&&!s.classList.contains("vditor-reset");)s.classList.contains(i)?u=!0:s=s.parentElement;return u&&s},L=function(S){for(;S&&S.lastChild;)S=S.lastChild;return S}},771:(Q,M,D)=>{"use strict";D.d(M,{S:()=>R,W:()=>N});var R=function(d,c){if(!d)return!1;d.nodeType===3&&(d=d.parentElement);for(var _=d,g=!1;_&&!g&&!_.classList.contains("vditor-reset");)_.nodeName.indexOf(c)===0?g=!0:_=_.parentElement;return g&&_},N=function(d){var c=R(d,"H");return c&&c.tagName.length===2&&c.tagName!=="HR"?c:!1}},673:(Q,M,D)=>{"use strict";D.d(M,{T:()=>R});var R=function(){for(var N=[],d=0;d<arguments.length;d++)N[d]=arguments[d];for(var c={},_=function(y){for(var p in y)y.hasOwnProperty(p)&&(Object.prototype.toString.call(y[p])==="[object Object]"?c[p]=R(c[p],y[p]):c[p]=y[p])},g=0;g<N.length;g++)_(N[g]);return c}},810:(Q,M,D)=>{"use strict";D.d(M,{zh:()=>c,Ny:()=>_,Gb:()=>g,Hc:()=>y,im:()=>p,$j:()=>C,ib:()=>L,oC:()=>S});var R=D(145),N=D(410),d=D(106),c=function(i){var s,u=i[i.currentMode].element;return getSelection().rangeCount>0&&(s=getSelection().getRangeAt(0),u.isEqualNode(s.startContainer)||u.contains(s.startContainer))?s:i[i.currentMode].range?i[i.currentMode].range:(u.focus(),s=u.ownerDocument.createRange(),s.setStart(u,0),s.collapse(!0),s)},_=function(i){var s=window.getSelection().getRangeAt(0);if(!i.contains(s.startContainer)&&!(0,d.fb)(s.startContainer,"vditor-panel--none"))return{left:0,top:0};var u=i.parentElement.getBoundingClientRect(),f;if(s.getClientRects().length===0)if(s.startContainer.nodeType===3){var v=s.startContainer.parentElement;if(v&&v.getClientRects().length>0)f=v.getClientRects()[0];else return{left:0,top:0}}else{var b=s.startContainer.children;if(b[s.startOffset]&&b[s.startOffset].getClientRects().length>0)f=b[s.startOffset].getClientRects()[0];else if(s.startContainer.childNodes.length>0){var k=s.cloneRange();s.selectNode(s.startContainer.childNodes[Math.max(0,s.startOffset-1)]),f=s.getClientRects()[0],s.setEnd(k.endContainer,k.endOffset),s.setStart(k.startContainer,k.startOffset)}else f=s.startContainer.getClientRects()[0];if(!f){for(var H=s.startContainer.childNodes[s.startOffset];!H.getClientRects||H.getClientRects&&H.getClientRects().length===0;)H=H.parentElement;f=H.getClientRects()[0]}}else f=s.getClientRects()[0];return{left:f.left-u.left,top:f.top-u.top}},g=function(i,s){if(!s){if(getSelection().rangeCount===0)return!1;s=getSelection().getRangeAt(0)}var u=s.commonAncestorContainer;return i.isEqualNode(u)||i.contains(u)},y=function(i){var s=window.getSelection();s.removeAllRanges(),s.addRange(i)},p=function(i,s,u){var f={end:0,start:0};if(!u){if(getSelection().rangeCount===0)return f;u=window.getSelection().getRangeAt(0)}if(g(s,u)){var v=u.cloneRange();i.childNodes[0]&&i.childNodes[0].childNodes[0]?v.setStart(i.childNodes[0].childNodes[0],0):v.selectNodeContents(i),v.setEnd(u.startContainer,u.startOffset),f.start=v.toString().length,f.end=f.start+u.toString().length}return f},C=function(i,s,u){var f=0,v=0,b=u.childNodes[v],k=!1,H=!1;i=Math.max(0,i),s=Math.max(0,s);var j=u.ownerDocument.createRange();for(j.setStart(b||u,0),j.collapse(!0);!H&&b;){var x=f+b.textContent.length;if(!k&&i>=f&&i<=x&&(i===0?j.setStart(b,0):b.childNodes[0].nodeType===3?j.setStart(b.childNodes[0],i-f):b.nextSibling?j.setStartBefore(b.nextSibling):j.setStartAfter(b),k=!0,i===s)){H=!0;break}k&&s>=f&&s<=x&&(s===0?j.setEnd(b,0):b.childNodes[0].nodeType===3?j.setEnd(b.childNodes[0],s-f):b.nextSibling?j.setEndBefore(b.nextSibling):j.setEndAfter(b),H=!0),f=x,b=u.childNodes[++v]}return!H&&u.childNodes[v-1]&&j.setStartBefore(u.childNodes[v-1]),y(j),j},L=function(i,s){var u=i.querySelector("wbr");if(u){if(!u.previousElementSibling)u.previousSibling?s.setStart(u.previousSibling,u.previousSibling.textContent.length):u.nextSibling?u.nextSibling.nodeType===3?s.setStart(u.nextSibling,0):s.setStartBefore(u.nextSibling):s.setStart(u.parentElement,0);else if(u.previousElementSibling.isSameNode(u.previousSibling))if(u.previousElementSibling.lastChild){s.setStartBefore(u),s.collapse(!0),y(s),(0,N.i7)()&&(u.previousElementSibling.tagName==="EM"||u.previousElementSibling.tagName==="STRONG"||u.previousElementSibling.tagName==="S")&&(s.insertNode(document.createTextNode(R.g.ZWSP)),s.collapse(!1)),u.remove();return}else s.setStartAfter(u.previousElementSibling);else s.setStart(u.previousSibling,u.previousSibling.textContent.length);s.collapse(!0),u.remove(),y(s)}},S=function(i,s){var u=document.createElement("div");u.innerHTML=i;var f=u.querySelectorAll("p");f.length===1&&!f[0].previousSibling&&!f[0].nextSibling&&s[s.currentMode].element.children.length>0&&u.firstElementChild.tagName==="P"&&(i=f[0].innerHTML.trim());var v=document.createElement("div");v.innerHTML=i;var b=c(s);if(b.toString()!==""&&(s[s.currentMode].preventInput=!0,document.execCommand("delete",!1,"")),v.firstElementChild&&v.firstElementChild.getAttribute("data-block")==="0"){v.lastElementChild.insertAdjacentHTML("beforeend","<wbr>");var k=(0,d.F9)(b.startContainer);k?k.insertAdjacentHTML("afterend",v.innerHTML):s[s.currentMode].element.insertAdjacentHTML("beforeend",v.innerHTML),L(s[s.currentMode].element,b)}else{var H=document.createElement("template");H.innerHTML=i,b.insertNode(H.content.cloneNode(!0)),b.collapse(!1),y(b)}}}},E={};function re(Q){var M=E[Q];if(M!==void 0)return M.exports;var D=E[Q]={exports:{}};return ge[Q](D,D.exports,re),D.exports}re.d=(Q,M)=>{for(var D in M)re.o(M,D)&&!re.o(Q,D)&&Object.defineProperty(Q,D,{enumerable:!0,get:M[D]})},re.o=(Q,M)=>Object.prototype.hasOwnProperty.call(Q,M),re.r=Q=>{typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(Q,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(Q,"__esModule",{value:!0})};var Me={};return(()=>{"use strict";re.d(Me,{default:()=>Va});var Q=re(408),M=re(145),D=re(105),R=function(e){return e.currentMode==="sv"?(0,D.X)("".concat(e.sv.element.textContent,`
`).replace(/\n\n$/,`
`)):e.currentMode==="wysiwyg"?e.lute.VditorDOM2Md(e.wysiwyg.element.innerHTML):e.currentMode==="ir"?e.lute.VditorIRDOM2Md(e.ir.element.innerHTML):""},N=re(413),d=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-devtools",this.element.innerHTML='<div class="vditor-reset--error"></div><div style="height: 100%;"></div>'}return e.prototype.renderEchart=function(t){var n=this;t.devtools.element.style.display==="block"&&(0,N.G)("".concat(t.options.cdn,"/dist/js/echarts/echarts.min.js?v=5.5.1"),"vditorEchartsScript").then(function(){n.ASTChart||(n.ASTChart=echarts.init(t.devtools.element.lastElementChild));try{n.element.lastElementChild.style.display="block",n.element.firstElementChild.innerHTML="",n.ASTChart.setOption({series:[{data:JSON.parse(t.lute.RenderEChartsJSON(R(t))),initialTreeDepth:-1,label:{align:"left",backgroundColor:"rgba(68, 77, 86, .68)",borderRadius:3,color:"#d1d5da",fontSize:12,lineHeight:12,offset:[9,12],padding:[2,4,2,4],position:"top",verticalAlign:"middle"},lineStyle:{color:"#4285f4",type:"curve",width:1},orient:"vertical",roam:!0,type:"tree"}],toolbox:{bottom:25,emphasis:{iconStyle:{color:"#4285f4"}},feature:{restore:{show:!0},saveAsImage:{show:!0}},right:15,show:!0}}),n.ASTChart.resize()}catch(r){n.element.lastElementChild.style.display="none",n.element.firstElementChild.innerHTML=r}})},e}(),c=re(410),_=function(e,t){t.forEach(function(n){if(e[n]){var r=e[n].children[0];r&&r.classList.contains("vditor-menu--current")&&r.classList.remove("vditor-menu--current")}})},g=function(e,t){t.forEach(function(n){if(e[n]){var r=e[n].children[0];r&&!r.classList.contains("vditor-menu--current")&&r.classList.add("vditor-menu--current")}})},y=function(e,t){t.forEach(function(n){if(e[n]){var r=e[n].children[0];r&&r.classList.contains(M.g.CLASS_MENU_DISABLED)&&r.classList.remove(M.g.CLASS_MENU_DISABLED)}})},p=function(e,t){t.forEach(function(n){if(e[n]){var r=e[n].children[0];r&&!r.classList.contains(M.g.CLASS_MENU_DISABLED)&&r.classList.add(M.g.CLASS_MENU_DISABLED)}})},C=function(e,t){t.forEach(function(n){e[n]&&e[n]&&(e[n].style.display="none")})},L=function(e,t){t.forEach(function(n){e[n]&&e[n]&&(e[n].style.display="block")})},S=function(e,t,n){t.includes("subToolbar")&&(e.toolbar.element.querySelectorAll(".vditor-hint").forEach(function(r){n&&r.isEqualNode(n)||(r.style.display="none")}),e.toolbar.elements.emoji&&(e.toolbar.elements.emoji.lastElementChild.style.display="none")),t.includes("hint")&&(e.hint.element.style.display="none"),e.wysiwyg.popover&&t.includes("popover")&&(e.wysiwyg.popover.style.display="none")},i=function(e,t,n,r){n.addEventListener((0,c.Le)(),function(a){a.preventDefault(),a.stopPropagation(),!n.classList.contains(M.g.CLASS_MENU_DISABLED)&&(e.toolbar.element.querySelectorAll(".vditor-hint--current").forEach(function(o){o.classList.remove("vditor-hint--current")}),t.style.display==="block"?t.style.display="none":(S(e,["subToolbar","hint","popover"],n.parentElement.parentElement),n.classList.contains("vditor-tooltipped")||n.classList.add("vditor-hint--current"),t.style.display="block",e.toolbar.element.getBoundingClientRect().right-n.getBoundingClientRect().right<250?t.classList.add("vditor-panel--left"):t.classList.remove("vditor-panel--left")))})},s=re(106),u=re(771),f=function(e,t,n,r){},v=re(135),b=re(775),k=re(428),H=re(325),j=re(483),x=re(999),I=re(472),z=re(637),T=re(11),B=re(194),q=re(229),J=re(825),se=function(e,t,n){n===void 0&&(n="sv");var r=document.createElement("div");r.innerHTML=e;var a=!1;r.childElementCount===1&&r.lastElementChild.style.fontFamily.indexOf("monospace")>-1&&(a=!0);var o=r.querySelectorAll("pre");if(r.childElementCount===1&&o.length===1&&o[0].className!=="vditor-wysiwyg"&&o[0].className!=="vditor-sv"&&(a=!0),e.indexOf(`
<p class="p1">`)===0&&(a=!0),r.childElementCount===1&&r.firstElementChild.tagName==="TABLE"&&r.querySelector(".line-number")&&r.querySelector(".line-content")&&(a=!0),a){var l=t||e;return/\n/.test(l)||o.length===1?n==="wysiwyg"?'<div class="vditor-wysiwyg__block" data-block="0" data-type="code-block"><pre><code>'.concat(l.replace(/&/g,"&amp;").replace(/</g,"&lt;"),"<wbr></code></pre></div>"):"\n```\n"+l.replace(/&/g,"&amp;").replace(/</g,"&lt;")+"\n```":n==="wysiwyg"?"<code>".concat(l.replace(/&/g,"&amp;").replace(/</g,"&lt;"),"</code><wbr>"):"`".concat(l,"`")}return!1},X=function(e,t){if(e){if(e.parentElement.getAttribute("data-type")==="html-block"){e.setAttribute("data-render","1");return}var n=e.firstElementChild.className.replace("language-","");if(n==="abc")(0,v.Q)(e,t.options.cdn);else if(n==="mermaid")(0,z.i)(e,t.options.cdn,t.options.theme);else if(n==="smiles")(0,J.J)(e,t.options.cdn,t.options.theme);else if(n==="markmap")(0,T.K)(e,t.options.cdn);else if(n==="flowchart")(0,H.P)(e,t.options.cdn);else if(n==="echarts")(0,b.p)(e,t.options.cdn,t.options.theme);else if(n==="mindmap")(0,B.P)(e,t.options.cdn,t.options.theme);else if(n==="plantuml")(0,q.B)(e,t.options.cdn);else if(n==="graphviz")(0,j.v)(e,t.options.cdn);else if(n==="math")(0,I.H)(e,{cdn:t.options.cdn,math:t.options.preview.math});else{var r=t.options.customRenders.find(function(a){if(a.language===n)return a.render(e,t),!0});r||((0,x.s)(Object.assign({},t.options.preview.hljs),e,t.options.cdn),(0,k.O)(e,t.options.preview.hljs))}e.setAttribute("data-render","1")}},O=re(810),ve=function(e){if(e.currentMode!=="sv"){var t=e[e.currentMode].element,n=e.outline.render(e);n===""&&(n="[ToC]"),t.querySelectorAll('[data-type="toc-block"]').forEach(function(r){r.innerHTML=n,(0,I.H)(r,{cdn:e.options.cdn,math:e.options.preview.math})})}},Pe=function(e,t){var n=(0,s.lG)(e.target,"SPAN");if(n&&(0,s.fb)(n,"vditor-toc")){var r=t[t.currentMode].element.querySelector("#"+n.getAttribute("data-target-id"));if(r)if(t.options.height==="auto"){var a=r.offsetTop+t.element.offsetTop;t.options.toolbarConfig.pin||(a+=t.toolbar.element.offsetHeight),window.scrollTo(window.scrollX,a)}else t.element.offsetTop<window.scrollY&&window.scrollTo(window.scrollX,t.element.offsetTop),t[t.currentMode].element.scrollTop=r.offsetTop;return}},Le=function(e,t,n,r){if(e.previousElementSibling&&e.previousElementSibling.classList.contains("vditor-toc")){if(n.key==="Backspace"&&(0,O.im)(e,t[t.currentMode].element,r).start===0)return e.previousElementSibling.remove(),le(t),!0;if(at(t,n,r,e,e.previousElementSibling))return!0}if(e.nextElementSibling&&e.nextElementSibling.classList.contains("vditor-toc")){if(n.key==="Delete"&&(0,O.im)(e,t[t.currentMode].element,r).start>=e.textContent.trimRight().length)return e.nextElementSibling.remove(),le(t),!0;if(pt(t,n,r,e,e.nextElementSibling))return!0}if(n.key==="Backspace"||n.key==="Delete"){var a=(0,s.fb)(r.startContainer,"vditor-toc");if(a)return a.remove(),le(t),!0}},_e=function(e,t,n,r){n===void 0&&(n=!1);var a=(0,s.F9)(t.startContainer);if(a&&!n&&a.getAttribute("data-type")!=="code-block"){if(Kt(a.innerHTML)&&a.previousElementSibling||Gt(a.innerHTML))return;for(var o=(0,O.im)(a,e.ir.element,t).start,l=!0,m=o-1;m>a.textContent.substr(0,o).lastIndexOf(`
`);m--)if(a.textContent.charAt(m)!==" "&&a.textContent.charAt(m)!=="	"){l=!1;break}o===0&&(l=!1);for(var w=!0,m=o-1;m<a.textContent.length;m++)if(a.textContent.charAt(m)!==" "&&a.textContent.charAt(m)!==`
`){w=!1;break}if(l){typeof e.options.input=="function"&&e.options.input(R(e));return}if(w&&/^#{1,6} $/.test(a.textContent)&&(w=!1),w){var h=(0,s.fb)(t.startContainer,"vditor-ir__marker");if(!h){var A=t.startContainer.previousSibling;A&&A.nodeType!==3&&A.classList.contains("vditor-ir__node--expand")&&A.classList.remove("vditor-ir__node--expand"),typeof e.options.input=="function"&&e.options.input(R(e));return}}}if(e.ir.element.querySelectorAll(".vditor-ir__node--expand").forEach(function(ce){ce.classList.remove("vditor-ir__node--expand")}),a||(a=e.ir.element),!a.querySelector("wbr")){var P=(0,s.fb)(t.startContainer,"vditor-ir__preview");P?P.previousElementSibling.insertAdjacentHTML("beforeend","<wbr>"):t.insertNode(document.createElement("wbr"))}a.querySelectorAll("[style]").forEach(function(ce){ce.removeAttribute("style")}),a.getAttribute("data-type")==="link-ref-defs-block"&&(a=e.ir.element);var K=a.isEqualNode(e.ir.element),W=(0,s.a1)(a,"data-type","footnotes-block"),V="";if(K)V=a.innerHTML;else{var ee=(0,u.S)(t.startContainer,"BLOCKQUOTE"),$=(0,s.O9)(t.startContainer);if($&&(a=$),ee&&(!$||$&&!ee.contains($))&&(a=ee),W&&(a=W),V=a.outerHTML,a.tagName==="UL"||a.tagName==="OL"){var F=a.previousElementSibling,oe=a.nextElementSibling;F&&(F.tagName==="UL"||F.tagName==="OL")&&(V=F.outerHTML+V,F.remove()),oe&&(oe.tagName==="UL"||oe.tagName==="OL")&&(V=V+oe.outerHTML,oe.remove()),V=V.replace("<div><wbr><br></div>","<li><p><wbr><br></p></li>")}else a.previousElementSibling&&a.previousElementSibling.textContent.replace(M.g.ZWSP,"")!==""&&r&&r.inputType==="insertParagraph"&&(V=a.previousElementSibling.outerHTML+V,a.previousElementSibling.remove());a.innerText.startsWith("```")||(e.ir.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(ce){ce&&!a.isEqualNode(ce)&&(V+=ce.outerHTML,ce.remove())}),e.ir.element.querySelectorAll("[data-type='footnotes-block']").forEach(function(ce){ce&&!a.isEqualNode(ce)&&(V+=ce.outerHTML,ce.remove())}))}if(f("SpinVditorIRDOM",V,"argument",e.options.debugger),V=e.lute.SpinVditorIRDOM(V),f("SpinVditorIRDOM",V,"result",e.options.debugger),K)a.innerHTML=V;else if(a.outerHTML=V,W){var ne=(0,s.a1)(e.ir.element.querySelector("wbr"),"data-type","footnotes-def");if(ne){var ae=ne.textContent,pe=ae.substring(1,ae.indexOf("]:")),fe=e.ir.element.querySelector('sup[data-type="footnotes-ref"][data-footnotes-label="'.concat(pe,'"]'));fe&&fe.setAttribute("aria-label",ae.substr(pe.length+3).trim().substr(0,24))}}var ye,je=e.ir.element.querySelectorAll("[data-type='link-ref-defs-block']");je.forEach(function(ce,Ie){Ie===0?ye=ce:(ye.insertAdjacentHTML("beforeend",ce.innerHTML),ce.remove())}),je.length>0&&e.ir.element.insertAdjacentElement("beforeend",je[0]);var Oe,Re=e.ir.element.querySelectorAll("[data-type='footnotes-block']");Re.forEach(function(ce,Ie){Ie===0?Oe=ce:(Oe.insertAdjacentHTML("beforeend",ce.innerHTML),ce.remove())}),Re.length>0&&e.ir.element.insertAdjacentElement("beforeend",Re[0]),(0,O.ib)(e.ir.element,t),e.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach(function(ce){X(ce,e)}),ve(e),Ze(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},U=function(e,t){if(e==="")return!1;if(e.indexOf("⇧")===-1&&e.indexOf("⌘")===-1&&e.indexOf("⌥")===-1)return!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey&&t.code===e;if(e==="⇧Tab")return!!(!(0,c.yl)(t)&&!t.altKey&&t.shiftKey&&t.code==="Tab");var n=e.split("");if(e.startsWith("⌥")){var r=n.length===3?n[2]:n[1];return!!((n.length===3?(0,c.yl)(t):!(0,c.yl)(t))&&t.altKey&&!t.shiftKey&&t.code===(/^[0-9]$/.test(r)?"Digit":"Key")+r)}e==="⌘Enter"&&(n=["⌘","Enter"]);var a=n.length>2&&n[0]==="⇧",o=a?n[2]:n[1];return a&&((0,c.vU)()||!/Mac/.test(navigator.platform))&&(o==="-"?o="_":o==="="&&(o="+")),!!((0,c.yl)(t)&&t.key.toLowerCase()===o.toLowerCase()&&!t.altKey&&(!a&&!t.shiftKey||a&&t.shiftKey))},ue=function(e){var t=e.startContainer;if(t.nodeType===3&&t.nodeValue.length!==e.startOffset)return!1;for(var n=t.nextSibling;n&&n.textContent==="";)n=n.nextSibling;if(n){if(n&&n.nodeType!==3&&n.classList.contains("vditor-ir__node")&&!n.getAttribute("data-block"))return n}else{var r=(0,s.fb)(t,"vditor-ir__marker");if(r&&!r.nextSibling){var a=t.parentElement.parentElement.nextSibling;if(a&&a.nodeType!==3&&a.classList.contains("vditor-ir__node"))return a}return!1}return!1},Z=function(e){var t=e.startContainer,n=t.previousSibling;return t.nodeType===3&&e.startOffset===0&&n&&n.nodeType!==3&&n.classList.contains("vditor-ir__node")&&!n.getAttribute("data-block")?n:!1},G=function(e,t){t.ir.element.querySelectorAll(".vditor-ir__node--expand").forEach(function(l){l.classList.remove("vditor-ir__node--expand")});var n=(0,s.JQ)(e.startContainer,"vditor-ir__node"),r=!e.collapsed&&(0,s.JQ)(e.endContainer,"vditor-ir__node");if(!(!e.collapsed&&(!n||n!==r))){n&&(n.classList.add("vditor-ir__node--expand"),n.classList.remove("vditor-ir__node--hidden"),(0,O.Hc)(e));var a=ue(e);if(a){a.classList.add("vditor-ir__node--expand"),a.classList.remove("vditor-ir__node--hidden");return}var o=Z(e);if(o){o.classList.add("vditor-ir__node--expand"),o.classList.remove("vditor-ir__node--hidden");return}}},be=function(e,t){if(e.ir.composingLock=t.isComposing,t.isComposing)return!1;t.key.indexOf("Arrow")===-1&&t.key!=="Meta"&&t.key!=="Control"&&t.key!=="Alt"&&t.key!=="Shift"&&t.key!=="CapsLock"&&t.key!=="Escape"&&!/^F\d{1,2}$/.test(t.key)&&e.undo.recordFirstPosition(e,t);var n=(0,O.zh)(e),r=n.startContainer;if(!fn(t,e,r)||(pn(n,e,t),Tn(n),t.key!=="Enter"&&t.key!=="Tab"&&t.key!=="Backspace"&&t.key.indexOf("Arrow")===-1&&!(0,c.yl)(t)&&t.key!=="Escape"&&t.key!=="Delete"))return!1;var a=(0,s.a1)(r,"data-newline","1");if(!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey&&t.key==="Enter"&&a&&n.startOffset<a.textContent.length){var o=a.previousElementSibling;o&&(n.insertNode(document.createTextNode(o.textContent)),n.collapse(!1));var l=a.nextSibling;l&&(n.insertNode(document.createTextNode(l.textContent)),n.collapse(!0))}var m=(0,s.lG)(r,"P");if(wn(t,e,m,n)||bn(n,e,m,t)||Cn(e,n,t,m))return!0;var w=(0,s.fb)(r,"vditor-ir__marker--pre");if(w&&w.tagName==="PRE"){var h=w.firstChild;if(Sn(e,t,w,n)||(h.getAttribute("data-type")==="math-block"||h.getAttribute("data-type")==="html-block")&&at(e,t,n,h,w.parentElement)||pt(e,t,n,h,w.parentElement))return!0}var A=(0,s.a1)(r,"data-type","code-block-info");if(A){if(t.key==="Enter"||t.key==="Tab")return n.selectNodeContents(A.nextElementSibling.firstChild),n.collapse(!0),t.preventDefault(),S(e,["hint"]),!0;if(t.key==="Backspace"){var P=(0,O.im)(A,e.ir.element).start;P===1&&n.setStart(r,0),P===2&&(e.hint.recentLanguage="")}if(at(e,t,n,A,A.parentElement))return S(e,["hint"]),!0}var K=(0,s.lG)(r,"TD")||(0,s.lG)(r,"TH");if(t.key.indexOf("Arrow")>-1&&K){var W=Mr(K);if(W&&at(e,t,n,K,W))return!0;var V=Lr(K);if(V&&pt(e,t,n,K,V))return!0}if(En(e,t,n)||Mn(e,n,t)||$t(e,n,t))return!0;var ee=(0,u.W)(r);if(ee){if(U("⌘=",t)){var $=ee.querySelector(".vditor-ir__marker--heading");return $&&$.textContent.trim().length>1&&ht(e,$.textContent.substr(1)),t.preventDefault(),!0}if(U("⌘-",t)){var $=ee.querySelector(".vditor-ir__marker--heading");return $&&$.textContent.trim().length<6&&ht(e,$.textContent.trim()+"# "),t.preventDefault(),!0}}var F=(0,s.F9)(r);if(t.key==="Backspace"&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&n.toString()===""){if(Ln(e,n,t,m))return!0;if(F&&F.previousElementSibling&&F.tagName!=="UL"&&F.tagName!=="OL"&&(F.previousElementSibling.getAttribute("data-type")==="code-block"||F.previousElementSibling.getAttribute("data-type")==="math-block")){var oe=(0,O.im)(F,e.ir.element,n).start;if(oe===0||oe===1&&F.innerText.startsWith(M.g.ZWSP))return n.selectNodeContents(F.previousElementSibling.querySelector(".vditor-ir__marker--pre code")),n.collapse(!1),G(n,e),F.textContent.trim().replace(M.g.ZWSP,"")===""&&(F.remove(),Ze(e)),t.preventDefault(),!0}if(ee){var ne=ee.firstElementChild.textContent.length;(0,O.im)(ee,e.ir.element).start===ne&&ne!==0&&(n.setStart(ee.firstElementChild.firstChild,ne-1),n.collapse(!0),(0,O.Hc)(n))}}return(t.key==="ArrowUp"||t.key==="ArrowDown")&&F&&(F.querySelectorAll(".vditor-ir__node").forEach(function(ae){ae.contains(r)||ae.classList.add("vditor-ir__node--hidden")}),An(t,F,n))?!0:(hn(n,t.key),F&&Le(F,e,t,n)?(t.preventDefault(),!0):!1)},Y=re(190),Se=function(e,t){e.querySelectorAll("[data-type=footnotes-link]").forEach(function(n){for(var r=n.parentElement,a=r.nextSibling;a&&a.textContent.startsWith("    ");){var o=a;o.childNodes.forEach(function(l){r.append(l.cloneNode(!0))}),a=a.nextSibling,o.remove()}t&&t(r)})},he=function(e,t){var n,r=getSelection().getRangeAt(0).cloneRange(),a=r.startContainer;r.startContainer.nodeType!==3&&r.startContainer.tagName==="DIV"&&(a=r.startContainer.childNodes[r.startOffset-1]);var o=(0,s.a1)(a,"data-block","0");if(o&&t&&(t.inputType==="deleteContentBackward"||t.data===" ")){for(var l=(0,O.im)(o,e.sv.element,r).start,m=!0,w=l-1;w>o.textContent.substr(0,l).lastIndexOf(`
`);w--)if(o.textContent.charAt(w)!==" "&&o.textContent.charAt(w)!=="	"){m=!1;break}if(l===0&&(m=!1),m){Ae(e);return}if(t.inputType==="deleteContentBackward"){var h=(0,s.a1)(a,"data-type","code-block-open-marker")||(0,s.a1)(a,"data-type","code-block-close-marker");if(h){if(h.getAttribute("data-type")==="code-block-close-marker"){var A=kt(a,"code-block-open-marker");if(A){A.textContent=h.textContent,Ae(e);return}}if(h.getAttribute("data-type")==="code-block-open-marker"){var A=kt(a,"code-block-close-marker",!1);if(A){A.textContent=h.textContent,Ae(e);return}}}var P=(0,s.a1)(a,"data-type","math-block-open-marker");if(P){var K=P.nextElementSibling.nextElementSibling;K&&K.getAttribute("data-type")==="math-block-close-marker"&&(K.remove(),Ae(e));return}o.querySelectorAll('[data-type="code-block-open-marker"]').forEach(function(F){F.textContent.length===1&&F.remove()}),o.querySelectorAll('[data-type="code-block-close-marker"]').forEach(function(F){F.textContent.length===1&&F.remove()});var W=(0,s.a1)(a,"data-type","heading-marker");if(W&&W.textContent.indexOf("#")===-1){Ae(e);return}}if((t.data===" "||t.inputType==="deleteContentBackward")&&((0,s.a1)(a,"data-type","padding")||(0,s.a1)(a,"data-type","li-marker")||(0,s.a1)(a,"data-type","task-marker")||(0,s.a1)(a,"data-type","blockquote-marker"))){Ae(e);return}}if(o&&o.textContent.trimRight()==="$$"){Ae(e);return}o||(o=e.sv.element),((n=o.firstElementChild)===null||n===void 0?void 0:n.getAttribute("data-type"))==="link-ref-defs-block"&&(o=e.sv.element),(0,s.a1)(a,"data-type","footnotes-link")&&(o=e.sv.element),o.textContent.indexOf(Lute.Caret)===-1&&r.insertNode(document.createTextNode(Lute.Caret)),o.querySelectorAll("[style]").forEach(function(F){F.removeAttribute("style")}),o.querySelectorAll("font").forEach(function(F){F.outerHTML=F.innerHTML});var V=o.textContent,ee=o.isEqualNode(e.sv.element);if(ee)V=o.textContent;else{o.previousElementSibling&&(V=o.previousElementSibling.textContent+V,o.previousElementSibling.remove()),o.previousElementSibling&&V.indexOf(`---
`)===0&&(V=o.previousElementSibling.textContent+V,o.previousElementSibling.remove());var $="";e.sv.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(F,oe){F&&!o.isEqualNode(F.parentElement)&&($+=F.parentElement.textContent+`
`,F.parentElement.remove())}),e.sv.element.querySelectorAll("[data-type='footnotes-link']").forEach(function(F,oe){F&&!o.isEqualNode(F.parentElement)&&($+=F.parentElement.textContent+`
`,F.parentElement.remove())}),V=$+V}V=un(V,e),ee?o.innerHTML=V:o.outerHTML=V,e.sv.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(F){e.sv.element.insertAdjacentElement("beforeend",F.parentElement)}),Se(e.sv.element,function(F){e.sv.element.insertAdjacentElement("beforeend",F)}),(0,O.ib)(e.sv.element,r),Te(e),Ae(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},De=function(e,t){var n,r,a,o,l;if(e.sv.composingLock=t.isComposing,t.isComposing||(t.key.indexOf("Arrow")===-1&&t.key!=="Meta"&&t.key!=="Control"&&t.key!=="Alt"&&t.key!=="Shift"&&t.key!=="CapsLock"&&t.key!=="Escape"&&!/^F\d{1,2}$/.test(t.key)&&e.undo.recordFirstPosition(e,t),t.key!=="Enter"&&t.key!=="Tab"&&t.key!=="Backspace"&&t.key.indexOf("Arrow")===-1&&!(0,c.yl)(t)&&t.key!=="Escape"))return!1;var m=(0,O.zh)(e),w=m.startContainer;m.startContainer.nodeType!==3&&m.startContainer.tagName==="DIV"&&(w=m.startContainer.childNodes[m.startOffset-1]);var h=(0,s.a1)(w,"data-type","text"),A=(0,s.a1)(w,"data-type","blockquote-marker");if(!A&&m.startOffset===0&&h&&h.previousElementSibling&&h.previousElementSibling.getAttribute("data-type")==="blockquote-marker"&&(A=h.previousElementSibling),A&&t.key==="Enter"&&!(0,c.yl)(t)&&!t.altKey&&A.nextElementSibling.textContent.trim()===""&&(0,O.im)(A,e.sv.element,m).start===A.textContent.length)return((n=A.previousElementSibling)===null||n===void 0?void 0:n.getAttribute("data-type"))==="padding"&&A.previousElementSibling.setAttribute("data-action","enter-remove"),A.remove(),Ae(e),t.preventDefault(),!0;var P=(0,s.a1)(w,"data-type","li-marker"),K=(0,s.a1)(w,"data-type","task-marker"),W=P;if(W||K&&K.nextElementSibling.getAttribute("data-type")!=="task-marker"&&(W=K),!W&&m.startOffset===0&&h&&h.previousElementSibling&&(h.previousElementSibling.getAttribute("data-type")==="li-marker"||h.previousElementSibling.getAttribute("data-type")==="task-marker")&&(W=h.previousElementSibling),W){var V=(0,O.im)(W,e.sv.element,m).start,ee=W.getAttribute("data-type")==="task-marker",$=W;if(ee&&($=W.previousElementSibling.previousElementSibling.previousElementSibling),V===W.textContent.length){if(t.key==="Enter"&&!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey&&W.nextElementSibling.textContent.trim()==="")return((r=$.previousElementSibling)===null||r===void 0?void 0:r.getAttribute("data-type"))==="padding"?($.previousElementSibling.remove(),he(e)):(ee&&($.remove(),W.previousElementSibling.previousElementSibling.remove(),W.previousElementSibling.remove()),W.nextElementSibling.remove(),W.remove(),Ae(e)),t.preventDefault(),!0;if(t.key==="Tab")return t.shiftKey?$.previousElementSibling.getAttribute("data-type")==="padding"&&$.previousElementSibling.remove():$.insertAdjacentHTML("beforebegin",'<span data-type="padding">'.concat($.textContent.replace(/\S/g," "),"</span>")),/^\d/.test($.textContent)&&($.textContent=$.textContent.replace(/^\d{1,}/,"1"),m.selectNodeContents(W.firstChild),m.collapse(!1)),he(e),t.preventDefault(),!0}}if($t(e,m,t))return!0;var F=(0,s.a1)(w,"data-block","0"),oe=(0,u.S)(w,"SPAN");if(t.key==="Enter"&&!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey&&F){var ne=!1,ae=F.textContent.match(/^\n+/);(0,O.im)(F,e.sv.element).start<=(ae?ae[0].length:0)&&(ne=!0);var pe=`
`;if(oe){if(((a=oe.previousElementSibling)===null||a===void 0?void 0:a.getAttribute("data-action"))==="enter-remove")return oe.previousElementSibling.remove(),Ae(e),t.preventDefault(),!0;pe+=gr(oe)}return m.insertNode(document.createTextNode(pe)),m.collapse(!1),F&&F.textContent.trim()!==""&&!ne?he(e):Ae(e),t.preventDefault(),!0}if(t.key==="Backspace"&&!(0,c.yl)(t)&&!t.altKey&&!t.shiftKey){if(oe&&((o=oe.previousElementSibling)===null||o===void 0?void 0:o.getAttribute("data-type"))==="newline"&&(0,O.im)(oe,e.sv.element,m).start===1&&oe.getAttribute("data-type").indexOf("code-block-")===-1)return m.setStart(oe,0),m.extractContents(),oe.textContent.trim()!==""?he(e):Ae(e),t.preventDefault(),!0;if(F&&(0,O.im)(F,e.sv.element,m).start===0&&F.previousElementSibling){m.extractContents();var fe=F.previousElementSibling.lastElementChild;return fe.getAttribute("data-type")==="newline"&&(fe.remove(),fe=F.previousElementSibling.lastElementChild),fe.getAttribute("data-type")!=="newline"&&(fe.insertAdjacentHTML("afterend",F.innerHTML),F.remove()),F.textContent.trim()!==""&&!(!((l=F.previousElementSibling)===null||l===void 0)&&l.querySelector('[data-type="code-block-open-marker"]'))?he(e):(fe.getAttribute("data-type")!=="newline"&&(m.selectNodeContents(fe.lastChild),m.collapse(!1)),Ae(e)),t.preventDefault(),!0}}return!1},ie=re(538),ke=function(e){e.options.theme==="dark"?e.element.classList.add("vditor--dark"):e.element.classList.remove("vditor--dark")},Be=function(e){e.element.innerHTML="",e.element.classList.add("vditor"),e.options.rtl&&e.element.setAttribute("dir","rtl"),ke(e),(0,ie.Z)(e.options.preview.theme.current,e.options.preview.theme.path),typeof e.options.height=="number"?e.element.style.height=e.options.height+"px":e.element.style.height=e.options.height,typeof e.options.minHeight=="number"&&(e.element.style.minHeight=e.options.minHeight+"px"),typeof e.options.width=="number"?e.element.style.width=e.options.width+"px":e.element.style.width=e.options.width,e.element.appendChild(e.toolbar.element);var t=document.createElement("div");if(t.className="vditor-content",e.options.outline.position==="left"&&t.appendChild(e.outline.element),t.appendChild(e.wysiwyg.element.parentElement),t.appendChild(e.sv.element),t.appendChild(e.ir.element.parentElement),t.appendChild(e.preview.element),e.toolbar.elements.devtools&&t.appendChild(e.devtools.element),e.options.outline.position==="right"&&(e.outline.element.classList.add("vditor-outline--right"),t.appendChild(e.outline.element)),e.upload&&t.appendChild(e.upload.element),e.options.resize.enable&&t.appendChild(e.resize.element),t.appendChild(e.hint.element),t.appendChild(e.tip.element),e.element.appendChild(t),t.addEventListener("click",function(){S(e,["subToolbar"])}),e.toolbar.elements.export&&e.element.insertAdjacentHTML("beforeend",'<iframe id="vditorExportIframe" style="width: 100%;height: 0;border: 0"></iframe>'),et(e,e.options.mode,ir(e)),document.execCommand("DefaultParagraphSeparator",!1,"p"),navigator.userAgent.indexOf("iPhone")>-1&&typeof window.visualViewport!="undefined"){var n=!1,r=function(a){n||(n=!0,requestAnimationFrame(function(){n=!1;var o=e.toolbar.element;o.style.transform="none",o.getBoundingClientRect().top<0&&(o.style.transform="translate(0, ".concat(-o.getBoundingClientRect().top,"px)"))}))};window.visualViewport.addEventListener("scroll",r),window.visualViewport.addEventListener("resize",r)}},Ve=function(e){var t=window.innerWidth<=M.g.MOBILE_WIDTH?10:35;if(e.wysiwyg.element.parentElement.style.display!=="none"){var n=(e.wysiwyg.element.parentElement.clientWidth-e.options.preview.maxWidth)/2;e.wysiwyg.element.style.padding="10px ".concat(Math.max(t,n),"px")}if(e.ir.element.parentElement.style.display!=="none"){var n=(e.ir.element.parentElement.clientWidth-e.options.preview.maxWidth)/2;e.ir.element.style.padding="10px ".concat(Math.max(t,n),"px")}e.preview.element.style.display!=="block"?e.toolbar.element.style.paddingLeft=Math.max(5,parseInt(e[e.currentMode].element.style.paddingLeft||"0",10)+(e.options.outline.position==="left"?e.outline.element.offsetWidth:0))+"px":e.toolbar.element.style.paddingLeft=5+(e.options.outline.position==="left"?e.outline.element.offsetWidth:0)+"px"},vt=function(e){if(e.options.typewriterMode){var t=window.innerHeight;typeof e.options.height=="number"?(t=e.options.height,typeof e.options.minHeight=="number"&&(t=Math.max(t,e.options.minHeight)),t=Math.min(window.innerHeight,t)):t=e.element.clientHeight,e.element.classList.contains("vditor--fullscreen")&&(t=window.innerHeight),e[e.currentMode].element.style.setProperty("--editor-bottom",(t-e.toolbar.element.offsetHeight)/2+"px")}},en;function tn(){window.removeEventListener("resize",en)}var ir=function(e){vt(e),tn(),window.addEventListener("resize",en=function(){Ve(e),vt(e)});var t=(0,c.pK)()&&localStorage.getItem(e.options.cache.id);return(!e.options.cache.enable||!t)&&(e.options.value?t=e.options.value:e.originalInnerHTML?t=e.lute.HTML2Md(e.originalInnerHTML):e.options.cache.enable||(t="")),t||""},ut=function(e){clearTimeout(e[e.currentMode].hlToolbarTimeoutId),e[e.currentMode].hlToolbarTimeoutId=window.setTimeout(function(){if(e[e.currentMode].element.getAttribute("contenteditable")!=="false"&&(0,O.Gb)(e[e.currentMode].element)){_(e.toolbar.elements,M.g.EDIT_TOOLBARS),y(e.toolbar.elements,M.g.EDIT_TOOLBARS);var t=(0,O.zh)(e),n=t.startContainer;t.startContainer.nodeType===3&&(n=t.startContainer.parentElement),n.classList.contains("vditor-reset")&&(n=n.childNodes[t.startOffset]);var r=e.currentMode==="sv"?(0,s.a1)(n,"data-type","heading"):(0,u.W)(n);r&&g(e.toolbar.elements,["headings"]);var a=e.currentMode==="sv"?(0,s.a1)(n,"data-type","blockquote"):(0,s.lG)(n,"BLOCKQUOTE");a&&g(e.toolbar.elements,["quote"]);var o=(0,s.a1)(n,"data-type","strong");o&&g(e.toolbar.elements,["bold"]);var l=(0,s.a1)(n,"data-type","em");l&&g(e.toolbar.elements,["italic"]);var m=(0,s.a1)(n,"data-type","s");m&&g(e.toolbar.elements,["strike"]);var w=(0,s.a1)(n,"data-type","a");w&&g(e.toolbar.elements,["link"]);var h=(0,s.lG)(n,"LI");h?(h.classList.contains("vditor-task")?g(e.toolbar.elements,["check"]):h.parentElement.tagName==="OL"?g(e.toolbar.elements,["ordered-list"]):h.parentElement.tagName==="UL"&&g(e.toolbar.elements,["list"]),y(e.toolbar.elements,["outdent","indent"])):p(e.toolbar.elements,["outdent","indent"]);var A=(0,s.a1)(n,"data-type","code-block");A&&(p(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","inline-code","upload","link","table","record"]),g(e.toolbar.elements,["code"]));var P=(0,s.a1)(n,"data-type","code");P&&(p(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","upload","link","table","record"]),g(e.toolbar.elements,["inline-code"]));var K=(0,s.a1)(n,"data-type","table");K&&p(e.toolbar.elements,["headings","list","ordered-list","check","line","quote","code","table"])}},200)},we=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&e.hint.render(e),clearTimeout(e.wysiwyg.afterRenderTimeoutId),e.wysiwyg.afterRenderTimeoutId=window.setTimeout(function(){if(!e.wysiwyg.composingLock){var n=R(e);typeof e.options.input=="function"&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&(0,c.pK)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),t.enableAddUndoStack&&e.undo.addToUndoStack(e)}},e.options.undoDelay)},sr=function(e){for(var t=e.previousSibling;t;){if(t.nodeType!==3&&t.tagName==="A"&&!t.previousSibling&&t.innerHTML.replace(M.g.ZWSP,"")===""&&t.nextSibling)return t;t=t.previousSibling}return!1},or=function(e){for(var t=e.startContainer.nextSibling;t&&t.textContent==="";)t=t.nextSibling;return!!(t&&t.nodeType!==3&&(t.tagName==="CODE"||t.getAttribute("data-type")==="math-inline"||t.getAttribute("data-type")==="html-entity"||t.getAttribute("data-type")==="html-inline"))},nn=function(e){for(var t="",n=e.nextSibling;n;)n.nodeType===3?t+=n.textContent:t+=n.outerHTML,n=n.nextSibling;return t},rn=function(e){for(var t="",n=e.previousSibling;n;)n.nodeType===3?t=n.textContent+t:t=n.outerHTML+t,n=n.previousSibling;return t},ur=function(e){for(var t=e;t&&!t.nextSibling;)t=t.parentElement;return t.nextSibling},lr=function(e){var t=rn(e.startContainer),n=nn(e.startContainer),r=e.startContainer.textContent,a=e.startOffset,o="",l="";return(r.substr(0,a)!==""&&r.substr(0,a)!==M.g.ZWSP||t)&&(o="".concat(t).concat(r.substr(0,a))),(r.substr(a)!==""&&r.substr(a)!==M.g.ZWSP||n)&&(l="".concat(r.substr(a)).concat(n)),{afterHTML:l,beforeHTML:o}},Pt=function(e,t){Array.from(e.wysiwyg.element.childNodes).find(function(n){if(n.nodeType===3){var r=document.createElement("p");r.setAttribute("data-block","0"),r.textContent=n.textContent;var a=t.startContainer.nodeType===3?t.startOffset:n.textContent.length;return n.parentNode.insertBefore(r,n),n.remove(),t.setStart(r.firstChild,Math.min(r.firstChild.textContent.length,a)),t.collapse(!0),(0,O.Hc)(t),!0}else if(!n.getAttribute("data-block"))return n.tagName==="P"?n.remove():(n.tagName==="DIV"?(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'.concat(n.innerHTML,"</p>")):n.tagName==="BR"?n.outerHTML='<p data-block="0">'.concat(n.outerHTML,"<wbr></p>"):(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'.concat(n.outerHTML,"</p>")),(0,O.ib)(e.wysiwyg.element,t),t=getSelection().getRangeAt(0)),!0})},_t=function(e,t){var n=(0,O.zh)(e),r=(0,s.F9)(n.startContainer);r||(r=n.startContainer.childNodes[n.startOffset]),!r&&e.wysiwyg.element.children.length===0&&(r=e.wysiwyg.element),r&&!r.classList.contains("vditor-wysiwyg__block")&&(n.insertNode(document.createElement("wbr")),r.innerHTML.trim()==="<wbr>"&&(r.innerHTML="<wbr><br>"),r.tagName==="BLOCKQUOTE"||r.classList.contains("vditor-reset")?r.innerHTML="<".concat(t,' data-block="0">').concat(r.innerHTML.trim(),"</").concat(t,">"):r.outerHTML="<".concat(t,' data-block="0">').concat(r.innerHTML.trim(),"</").concat(t,">"),(0,O.ib)(e.wysiwyg.element,n),ve(e))},Nt=function(e){var t=getSelection().getRangeAt(0),n=(0,s.F9)(t.startContainer);n||(n=t.startContainer.childNodes[t.startOffset]),n&&(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'.concat(n.innerHTML,"</p>"),(0,O.ib)(e.wysiwyg.element,t)),e.wysiwyg.popover.style.display="none"},lt=function(e,t,n){n===void 0&&(n=!0);var r=e.previousElementSibling,a=r.ownerDocument.createRange();r.tagName==="CODE"?(r.style.display="inline-block",n?a.setStart(r.firstChild,1):a.selectNodeContents(r)):(r.style.display="block",r.firstChild.firstChild||r.firstChild.appendChild(document.createTextNode("")),a.selectNodeContents(r.firstChild)),n?a.collapse(!0):a.collapse(!1),(0,O.Hc)(a),!e.firstElementChild.classList.contains("language-mindmap")&&Te(t)},cr=function(e,t){if(e.wysiwyg.composingLock=t.isComposing,t.isComposing)return!1;t.key.indexOf("Arrow")===-1&&t.key!=="Meta"&&t.key!=="Control"&&t.key!=="Alt"&&t.key!=="Shift"&&t.key!=="CapsLock"&&t.key!=="Escape"&&!/^F\d{1,2}$/.test(t.key)&&e.undo.recordFirstPosition(e,t);var n=(0,O.zh)(e),r=n.startContainer;if(!fn(t,e,r)||(pn(n,e,t),Tn(n),t.key!=="Enter"&&t.key!=="Tab"&&t.key!=="Backspace"&&t.key.indexOf("Arrow")===-1&&!(0,c.yl)(t)&&t.key!=="Escape"&&t.key!=="Delete"))return!1;var a=(0,s.F9)(r),o=(0,s.lG)(r,"P");if(wn(t,e,o,n)||bn(n,e,o,t)||En(e,t,n))return!0;var l=(0,s.fb)(r,"vditor-wysiwyg__block");if(l){if(t.key==="Escape"&&l.children.length===2)return e.wysiwyg.popover.style.display="none",l.firstElementChild.style.display="none",e.wysiwyg.element.blur(),t.preventDefault(),!0;if(!(0,c.yl)(t)&&!t.shiftKey&&t.altKey&&t.key==="Enter"&&l.getAttribute("data-type")==="code-block"){var m=e.wysiwyg.popover.querySelector(".vditor-input");return m.focus(),m.select(),t.preventDefault(),!0}if(l.getAttribute("data-block")==="0"&&(Sn(e,t,l.firstElementChild,n)||pt(e,t,n,l.firstElementChild,l)||l.getAttribute("data-type")!=="yaml-front-matter"&&at(e,t,n,l.firstElementChild,l)))return!0}if(Cn(e,n,t,o))return!0;var w=(0,s.E2)(r,"BLOCKQUOTE");if(w&&!t.shiftKey&&t.altKey&&t.key==="Enter"){(0,c.yl)(t)?n.setStartBefore(w):n.setStartAfter(w),(0,O.Hc)(n);var h=document.createElement("p");return h.setAttribute("data-block","0"),h.innerHTML=`
`,n.insertNode(h),n.collapse(!0),(0,O.Hc)(n),we(e),Te(e),t.preventDefault(),!0}var A=(0,u.W)(r);if(A){if(A.tagName==="H6"&&r.textContent.length===n.startOffset&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&t.key==="Enter"){var P=document.createElement("p");return P.textContent=`
`,P.setAttribute("data-block","0"),r.parentElement.insertAdjacentElement("afterend",P),n.setStart(P,0),(0,O.Hc)(n),we(e),Te(e),t.preventDefault(),!0}if(U("⌘=",t)){var K=parseInt(A.tagName.substr(1),10)-1;return K>0&&(_t(e,"h".concat(K)),we(e)),t.preventDefault(),!0}if(U("⌘-",t)){var K=parseInt(A.tagName.substr(1),10)+1;return K<7&&(_t(e,"h".concat(K)),we(e)),t.preventDefault(),!0}t.key==="Backspace"&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&A.textContent.length===1&&Nt(e)}if(Mn(e,n,t))return!0;if(t.altKey&&t.key==="Enter"&&!(0,c.yl)(t)&&!t.shiftKey){var W=(0,s.lG)(r,"A"),V=(0,s.a1)(r,"data-type","link-ref"),ee=(0,s.a1)(r,"data-type","footnotes-ref");if(W||V||ee||A&&A.tagName.length===2){var $=e.wysiwyg.popover.querySelector("input");$.focus(),$.select()}}if(Ne(e,t))return!0;if(U("⇧⌘U",t)){var F=e.wysiwyg.popover.querySelector('[data-type="up"]');if(F)return F.click(),t.preventDefault(),!0}if(U("⇧⌘D",t)){var F=e.wysiwyg.popover.querySelector('[data-type="down"]');if(F)return F.click(),t.preventDefault(),!0}if($t(e,n,t))return!0;if(!(0,c.yl)(t)&&t.shiftKey&&!t.altKey&&t.key==="Enter"&&r.parentElement.tagName!=="LI"&&r.parentElement.tagName!=="P")return["STRONG","STRIKE","S","I","EM","B"].includes(r.parentElement.tagName)?n.insertNode(document.createTextNode(`
`+M.g.ZWSP)):n.insertNode(document.createTextNode(`
`)),n.collapse(!1),(0,O.Hc)(n),we(e),Te(e),t.preventDefault(),!0;if(t.key==="Backspace"&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&n.toString()===""){if(Ln(e,n,t,o))return!0;if(a){if(a.previousElementSibling&&a.previousElementSibling.classList.contains("vditor-wysiwyg__block")&&a.previousElementSibling.getAttribute("data-block")==="0"&&a.tagName!=="UL"&&a.tagName!=="OL"){var oe=(0,O.im)(a,e.wysiwyg.element,n).start;if(oe===0&&n.startOffset===0||oe===1&&a.innerText.startsWith(M.g.ZWSP))return lt(a.previousElementSibling.lastElementChild,e,!1),a.innerHTML.trim().replace(M.g.ZWSP,"")===""&&(a.remove(),we(e)),t.preventDefault(),!0}var ne=n.startOffset;if(n.toString()===""&&r.nodeType===3&&r.textContent.charAt(ne-2)===`
`&&r.textContent.charAt(ne-1)!==M.g.ZWSP&&["STRONG","STRIKE","S","I","EM","B"].includes(r.parentElement.tagName))return r.textContent=r.textContent.substring(0,ne-1)+M.g.ZWSP,n.setStart(r,ne),n.collapse(!0),we(e),t.preventDefault(),!0;r.textContent===M.g.ZWSP&&n.startOffset===1&&!r.previousSibling&&or(n)&&(r.textContent=""),a.querySelectorAll("span.vditor-wysiwyg__block[data-type='math-inline']").forEach(function(pe){pe.firstElementChild.style.display="inline",pe.lastElementChild.style.display="none"}),a.querySelectorAll("span.vditor-wysiwyg__block[data-type='html-entity']").forEach(function(pe){pe.firstElementChild.style.display="inline",pe.lastElementChild.style.display="none"})}}if((0,c.vU)()&&n.startOffset===1&&r.textContent.indexOf(M.g.ZWSP)>-1&&r.previousSibling&&r.previousSibling.nodeType!==3&&r.previousSibling.tagName==="CODE"&&(t.key==="Backspace"||t.key==="ArrowLeft"))return n.selectNodeContents(r.previousSibling),n.collapse(!1),t.preventDefault(),!0;if(An(t,a,n))return t.preventDefault(),!0;if(hn(n,t.key),t.key==="ArrowDown"){var ae=r.nextSibling;ae&&ae.nodeType!==3&&ae.getAttribute("data-type")==="math-inline"&&n.setStartAfter(ae)}return a&&Le(a,e,t,n)?(t.preventDefault(),!0):!1},Ne=function(e,t){if(U("⇧⌘X",t)){var n=e.wysiwyg.popover.querySelector('[data-type="remove"]');return n&&n.click(),t.preventDefault(),!0}},Ye=function(e){clearTimeout(e.wysiwyg.hlToolbarTimeoutId),e.wysiwyg.hlToolbarTimeoutId=window.setTimeout(function(){if(e.wysiwyg.element.getAttribute("contenteditable")!=="false"&&(0,O.Gb)(e.wysiwyg.element)){_(e.toolbar.elements,M.g.EDIT_TOOLBARS),y(e.toolbar.elements,M.g.EDIT_TOOLBARS);var t=getSelection().getRangeAt(0),n=t.startContainer;t.startContainer.nodeType===3?n=t.startContainer.parentElement:n=n.childNodes[t.startOffset>=n.childNodes.length?n.childNodes.length-1:t.startOffset];var r=(0,s.a1)(n,"data-type","footnotes-block");if(r){e.wysiwyg.popover.innerHTML="",Fe(r,e),We(e,r);return}var a=(0,s.lG)(n,"LI");a?(a.classList.contains("vditor-task")?g(e.toolbar.elements,["check"]):a.parentElement.tagName==="OL"?g(e.toolbar.elements,["ordered-list"]):a.parentElement.tagName==="UL"&&g(e.toolbar.elements,["list"]),y(e.toolbar.elements,["outdent","indent"])):p(e.toolbar.elements,["outdent","indent"]),(0,s.lG)(n,"BLOCKQUOTE")&&g(e.toolbar.elements,["quote"]),((0,s.lG)(n,"B")||(0,s.lG)(n,"STRONG"))&&g(e.toolbar.elements,["bold"]),((0,s.lG)(n,"I")||(0,s.lG)(n,"EM"))&&g(e.toolbar.elements,["italic"]),((0,s.lG)(n,"STRIKE")||(0,s.lG)(n,"S"))&&g(e.toolbar.elements,["strike"]),e.wysiwyg.element.querySelectorAll(".vditor-comment--focus").forEach(function(te){te.classList.remove("vditor-comment--focus")});var o=(0,s.fb)(n,"vditor-comment");if(o){var l=o.getAttribute("data-cmtids").split(" ");if(l.length>1&&o.nextSibling.isSameNode(o.nextElementSibling)){var m=o.nextElementSibling.getAttribute("data-cmtids").split(" ");l.find(function(te){if(m.includes(te))return l=[te],!0})}e.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(te){te.getAttribute("data-cmtids").indexOf(l[0])>-1&&te.classList.add("vditor-comment--focus")})}var w=(0,s.lG)(n,"A");w&&g(e.toolbar.elements,["link"]);var h=(0,s.lG)(n,"TABLE"),A=(0,u.W)(n);(0,s.lG)(n,"CODE")?(0,s.lG)(n,"PRE")?(p(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","inline-code","upload","link","table","record"]),g(e.toolbar.elements,["code"])):(p(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","upload","link","table","record"]),g(e.toolbar.elements,["inline-code"])):A?(p(e.toolbar.elements,["bold"]),g(e.toolbar.elements,["headings"])):h&&p(e.toolbar.elements,["table"]);var P=(0,s.fb)(n,"vditor-toc");if(P){e.wysiwyg.popover.innerHTML="",Fe(P,e),We(e,P);return}var K=(0,u.S)(n,"BLOCKQUOTE");if(K&&(e.wysiwyg.popover.innerHTML="",tt(t,K,e),nt(t,K,e),Fe(K,e),We(e,K)),a&&(e.wysiwyg.popover.innerHTML="",tt(t,a,e),nt(t,a,e),Fe(a,e),We(e,a)),h){var W=e.options.lang,V=e.options;e.wysiwyg.popover.innerHTML="";var ee=function(){var te=h.rows.length,de=h.rows[0].cells.length,Qe=parseInt(ze.value,10)||te,qe=parseInt(Ue.value,10)||de;if(!(Qe===te&&de===qe)){if(de!==qe)for(var gt=qe-de,$e=0;$e<h.rows.length;$e++)if(gt>0)for(var Bn=0;Bn<gt;Bn++)$e===0?h.rows[$e].lastElementChild.insertAdjacentHTML("afterend","<th> </th>"):h.rows[$e].lastElementChild.insertAdjacentHTML("afterend","<td> </td>");else for(var Xt=de-1;Xt>=qe;Xt--)h.rows[$e].cells[Xt].remove();if(te!==Qe){var zn=Qe-te;if(zn>0){for(var Qt="<tr>",ot=0;ot<qe;ot++)Qt+="<td> </td>";for(var Un=0;Un<zn;Un++)h.querySelector("tbody")?h.querySelector("tbody").insertAdjacentHTML("beforeend",Qt):h.querySelector("thead").insertAdjacentHTML("afterend",Qt+"</tr>")}else for(var ot=te-1;ot>=Qe;ot--)h.rows[ot].remove(),h.rows.length===1&&h.querySelector("tbody").remove()}typeof e.options.input=="function"&&e.options.input(R(e))}},$=function(te){Ct(h,te),te==="right"?(ae.classList.remove("vditor-icon--current"),pe.classList.remove("vditor-icon--current"),fe.classList.add("vditor-icon--current")):te==="center"?(ae.classList.remove("vditor-icon--current"),fe.classList.remove("vditor-icon--current"),pe.classList.add("vditor-icon--current")):(pe.classList.remove("vditor-icon--current"),fe.classList.remove("vditor-icon--current"),ae.classList.add("vditor-icon--current")),(0,O.Hc)(t),we(e)},F=(0,s.lG)(n,"TD"),oe=(0,s.lG)(n,"TH"),ne="left";F?ne=F.getAttribute("align")||"left":oe&&(ne=oe.getAttribute("align")||"center");var ae=document.createElement("button");ae.setAttribute("type","button"),ae.setAttribute("aria-label",window.VditorI18n.alignLeft+"<"+(0,c.ns)("⇧⌘L")+">"),ae.setAttribute("data-type","left"),ae.innerHTML='<svg><use xlink:href="#vditor-icon-align-left"></use></svg>',ae.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+(ne==="left"?" vditor-icon--current":""),ae.onclick=function(){$("left")};var pe=document.createElement("button");pe.setAttribute("type","button"),pe.setAttribute("aria-label",window.VditorI18n.alignCenter+"<"+(0,c.ns)("⇧⌘C")+">"),pe.setAttribute("data-type","center"),pe.innerHTML='<svg><use xlink:href="#vditor-icon-align-center"></use></svg>',pe.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+(ne==="center"?" vditor-icon--current":""),pe.onclick=function(){$("center")};var fe=document.createElement("button");fe.setAttribute("type","button"),fe.setAttribute("aria-label",window.VditorI18n.alignRight+"<"+(0,c.ns)("⇧⌘R")+">"),fe.setAttribute("data-type","right"),fe.innerHTML='<svg><use xlink:href="#vditor-icon-align-right"></use></svg>',fe.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+(ne==="right"?" vditor-icon--current":""),fe.onclick=function(){$("right")};var ye=document.createElement("button");ye.setAttribute("type","button"),ye.setAttribute("aria-label",window.VditorI18n.insertRowBelow+"<"+(0,c.ns)("⌘=")+">"),ye.setAttribute("data-type","insertRow"),ye.innerHTML='<svg><use xlink:href="#vditor-icon-insert-row"></use></svg>',ye.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",ye.onclick=function(){var te=getSelection().getRangeAt(0).startContainer,de=(0,s.lG)(te,"TD")||(0,s.lG)(te,"TH");de&&yn(e,t,de)};var je=document.createElement("button");je.setAttribute("type","button"),je.setAttribute("aria-label",window.VditorI18n.insertRowAbove+"<"+(0,c.ns)("⇧⌘F")+">"),je.setAttribute("data-type","insertRow"),je.innerHTML='<svg><use xlink:href="#vditor-icon-insert-rowb"></use></svg>',je.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",je.onclick=function(){var te=getSelection().getRangeAt(0).startContainer,de=(0,s.lG)(te,"TD")||(0,s.lG)(te,"TH");de&&vn(e,t,de)};var Oe=document.createElement("button");Oe.setAttribute("type","button"),Oe.setAttribute("aria-label",window.VditorI18n.insertColumnRight+"<"+(0,c.ns)("⇧⌘=")+">"),Oe.setAttribute("data-type","insertColumn"),Oe.innerHTML='<svg><use xlink:href="#vditor-icon-insert-column"></use></svg>',Oe.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",Oe.onclick=function(){var te=getSelection().getRangeAt(0).startContainer,de=(0,s.lG)(te,"TD")||(0,s.lG)(te,"TH");de&&Mt(e,h,de)};var Re=document.createElement("button");Re.setAttribute("type","button"),Re.setAttribute("aria-label",window.VditorI18n.insertColumnLeft+"<"+(0,c.ns)("⇧⌘G")+">"),Re.setAttribute("data-type","insertColumn"),Re.innerHTML='<svg><use xlink:href="#vditor-icon-insert-columnb"></use></svg>',Re.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",Re.onclick=function(){var te=getSelection().getRangeAt(0).startContainer,de=(0,s.lG)(te,"TD")||(0,s.lG)(te,"TH");de&&Mt(e,h,de,"beforebegin")};var ce=document.createElement("button");ce.setAttribute("type","button"),ce.setAttribute("aria-label",window.VditorI18n["delete-row"]+"<"+(0,c.ns)("⌘-")+">"),ce.setAttribute("data-type","deleteRow"),ce.innerHTML='<svg><use xlink:href="#vditor-icon-delete-row"></use></svg>',ce.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",ce.onclick=function(){var te=getSelection().getRangeAt(0).startContainer,de=(0,s.lG)(te,"TD")||(0,s.lG)(te,"TH");de&&_n(e,t,de)};var Ie=document.createElement("button");Ie.setAttribute("type","button"),Ie.setAttribute("aria-label",window.VditorI18n["delete-column"]+"<"+(0,c.ns)("⇧⌘-")+">"),Ie.setAttribute("data-type","deleteColumn"),Ie.innerHTML='<svg><use xlink:href="#vditor-icon-delete-column"></use></svg>',Ie.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",Ie.onclick=function(){var te=getSelection().getRangeAt(0).startContainer,de=(0,s.lG)(te,"TD")||(0,s.lG)(te,"TH");de&&kn(e,t,h,de)};var xe=document.createElement("span");xe.setAttribute("aria-label",window.VditorI18n.row),xe.className="vditor-tooltipped vditor-tooltipped__n";var ze=document.createElement("input");xe.appendChild(ze),ze.type="number",ze.min="1",ze.className="vditor-input",ze.style.width="42px",ze.style.textAlign="center",ze.setAttribute("placeholder",window.VditorI18n.row),ze.value=h.rows.length.toString(),ze.oninput=function(){ee()},ze.onkeydown=function(te){if(!te.isComposing){if(te.key==="Tab"){Ue.focus(),Ue.select(),te.preventDefault();return}Ne(e,te)||Ge(te,t)}};var At=document.createElement("span");At.setAttribute("aria-label",window.VditorI18n.column),At.className="vditor-tooltipped vditor-tooltipped__n";var Ue=document.createElement("input");At.appendChild(Ue),Ue.type="number",Ue.min="1",Ue.className="vditor-input",Ue.style.width="42px",Ue.style.textAlign="center",Ue.setAttribute("placeholder",window.VditorI18n.column),Ue.value=h.rows[0].cells.length.toString(),Ue.oninput=function(){ee()},Ue.onkeydown=function(te){if(!te.isComposing){if(te.key==="Tab"){ze.focus(),ze.select(),te.preventDefault();return}Ne(e,te)||Ge(te,t)}},tt(t,h,e),nt(t,h,e),Fe(h,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",ae),e.wysiwyg.popover.insertAdjacentElement("beforeend",pe),e.wysiwyg.popover.insertAdjacentElement("beforeend",fe),e.wysiwyg.popover.insertAdjacentElement("beforeend",je),e.wysiwyg.popover.insertAdjacentElement("beforeend",ye),e.wysiwyg.popover.insertAdjacentElement("beforeend",Re),e.wysiwyg.popover.insertAdjacentElement("beforeend",Oe),e.wysiwyg.popover.insertAdjacentElement("beforeend",ce),e.wysiwyg.popover.insertAdjacentElement("beforeend",Ie),e.wysiwyg.popover.insertAdjacentElement("beforeend",xe),e.wysiwyg.popover.insertAdjacentHTML("beforeend"," x "),e.wysiwyg.popover.insertAdjacentElement("beforeend",At),We(e,h)}var Jt=(0,s.a1)(n,"data-type","link-ref");Jt&&an(e,Jt,t);var it=(0,s.a1)(n,"data-type","footnotes-ref");if(it){var W=e.options.lang,V=e.options;e.wysiwyg.popover.innerHTML="";var xe=document.createElement("span");xe.setAttribute("aria-label",window.VditorI18n.footnoteRef+"<"+(0,c.ns)("⌥Enter")+">"),xe.className="vditor-tooltipped vditor-tooltipped__n";var Je=document.createElement("input");xe.appendChild(Je),Je.className="vditor-input",Je.setAttribute("placeholder",window.VditorI18n.footnoteRef+"<"+(0,c.ns)("⌥Enter")+">"),Je.style.width="120px",Je.value=it.getAttribute("data-footnotes-label"),Je.oninput=function(){Je.value.trim()!==""&&it.setAttribute("data-footnotes-label",Je.value),typeof e.options.input=="function"&&e.options.input(R(e))},Je.onkeydown=function(qe){qe.isComposing||Ne(e,qe)||Ge(qe,t)},Fe(it,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",xe),We(e,it)}var He=(0,s.fb)(n,"vditor-wysiwyg__block"),In=He?He.getAttribute("data-type").indexOf("block")>-1:!1;if(e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview").forEach(function(te){if(!He||He&&In&&!He.contains(te)){var de=te.previousElementSibling;de.style.display="none"}}),He&&In){if(e.wysiwyg.popover.innerHTML="",tt(t,He,e),nt(t,He,e),Fe(He,e),He.getAttribute("data-type")==="code-block"){var Ht=document.createElement("span");Ht.setAttribute("aria-label",window.VditorI18n.language+"<"+(0,c.ns)("⌥Enter")+">"),Ht.className="vditor-tooltipped vditor-tooltipped__n";var Ke=document.createElement("input");Ht.appendChild(Ke);var mt=He.firstElementChild.firstElementChild;Ke.className="vditor-input",Ke.setAttribute("placeholder",window.VditorI18n.language+"<"+(0,c.ns)("⌥Enter")+">"),Ke.value=mt.className.indexOf("language-")>-1?mt.className.split("-")[1].split(" ")[0]:"",Ke.oninput=function(te){Ke.value.trim()!==""?mt.className="language-".concat(Ke.value):(mt.className="",e.hint.recentLanguage=""),He.lastElementChild.classList.contains("vditor-wysiwyg__preview")&&(He.lastElementChild.innerHTML=He.firstElementChild.innerHTML,X(He.lastElementChild,e)),we(e),te.detail===1&&(t.setStart(mt.firstChild,0),t.collapse(!0),(0,O.Hc)(t))},Ke.onkeydown=function(te){if(!te.isComposing&&!Ne(e,te)){if(te.key==="Escape"&&e.hint.element.style.display==="block"){e.hint.element.style.display="none",te.preventDefault();return}e.hint.select(te,e),Ge(te,t)}},Ke.onkeyup=function(te){var de,Qe;if(!(te.isComposing||te.key==="Enter"||te.key==="ArrowUp"||te.key==="Escape"||te.key==="ArrowDown")){var qe=[],gt=Ke.value.substring(0,Ke.selectionStart);(e.options.preview.hljs.langs||M.g.ALIAS_CODE_LANGUAGES.concat(((Qe=(de=window.hljs)===null||de===void 0?void 0:de.listLanguages())!==null&&Qe!==void 0?Qe:[]).sort())).forEach(function($e){$e.indexOf(gt.toLowerCase())>-1&&qe.push({html:$e,value:$e})}),e.hint.genHTML(qe,gt,e),te.preventDefault()}},e.wysiwyg.popover.insertAdjacentElement("beforeend",Ht)}We(e,He)}else He=void 0;if(A){e.wysiwyg.popover.innerHTML="";var xe=document.createElement("span");xe.setAttribute("aria-label","ID<"+(0,c.ns)("⌥Enter")+">"),xe.className="vditor-tooltipped vditor-tooltipped__n";var Xe=document.createElement("input");xe.appendChild(Xe),Xe.className="vditor-input",Xe.setAttribute("placeholder","ID<"+(0,c.ns)("⌥Enter")+">"),Xe.style.width="120px",Xe.value=A.getAttribute("data-id")||"",Xe.oninput=function(){A.setAttribute("data-id",Xe.value),typeof e.options.input=="function"&&e.options.input(R(e))},Xe.onkeydown=function(de){de.isComposing||Ne(e,de)||Ge(de,t)},tt(t,A,e),nt(t,A,e),Fe(A,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",xe),We(e,A)}if(w&&jt(e,w,t),!K&&!a&&!h&&!He&&!w&&!Jt&&!it&&!A&&!P){var st=(0,s.a1)(n,"data-block","0");st&&st.parentElement.isEqualNode(e.wysiwyg.element)?(e.wysiwyg.popover.innerHTML="",tt(t,st,e),nt(t,st,e),Fe(st,e),We(e,st)):e.wysiwyg.popover.style.display="none"}e.wysiwyg.element.querySelectorAll('span[data-type="backslash"] > span').forEach(function(te){te.style.display="none"});var xn=(0,s.a1)(t.startContainer,"data-type","backslash");xn&&(xn.querySelector("span").style.display="inline")}},200)},We=function(e,t){var n=t,r=(0,s.lG)(t,"TABLE");r&&(n=r),e.wysiwyg.popover.style.left="0",e.wysiwyg.popover.style.display="block",e.wysiwyg.popover.style.top=Math.max(-8,n.offsetTop-21-e.wysiwyg.element.scrollTop)+"px",e.wysiwyg.popover.style.left=Math.min(n.offsetLeft,e.wysiwyg.element.clientWidth-e.wysiwyg.popover.clientWidth)+"px",e.wysiwyg.popover.setAttribute("data-top",(n.offsetTop-21).toString())},an=function(e,t,n){n===void 0&&(n=getSelection().getRangeAt(0)),e.wysiwyg.popover.innerHTML="";var r=function(){o.value.trim()!==""&&(t.tagName==="IMG"?t.setAttribute("alt",o.value):t.textContent=o.value),m.value.trim()!==""&&t.setAttribute("data-link-label",m.value),typeof e.options.input=="function"&&e.options.input(R(e))},a=document.createElement("span");a.setAttribute("aria-label",window.VditorI18n.textIsNotEmpty),a.className="vditor-tooltipped vditor-tooltipped__n";var o=document.createElement("input");a.appendChild(o),o.className="vditor-input",o.setAttribute("placeholder",window.VditorI18n.textIsNotEmpty),o.style.width="120px",o.value=t.getAttribute("alt")||t.textContent,o.oninput=function(){r()},o.onkeydown=function(w){Ne(e,w)||Ge(w,n)||ct(e,t,w,m)};var l=document.createElement("span");l.setAttribute("aria-label",window.VditorI18n.linkRef),l.className="vditor-tooltipped vditor-tooltipped__n";var m=document.createElement("input");l.appendChild(m),m.className="vditor-input",m.setAttribute("placeholder",window.VditorI18n.linkRef),m.value=t.getAttribute("data-link-label"),m.oninput=function(){r()},m.onkeydown=function(w){Ne(e,w)||Ge(w,n)||ct(e,t,w,o)},Fe(t,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",a),e.wysiwyg.popover.insertAdjacentElement("beforeend",l),We(e,t)},tt=function(e,t,n){var r=t.previousElementSibling;if(!(!r||!t.parentElement.isEqualNode(n.wysiwyg.element)&&t.tagName!=="LI")){var a=document.createElement("button");a.setAttribute("type","button"),a.setAttribute("data-type","up"),a.setAttribute("aria-label",window.VditorI18n.up+"<"+(0,c.ns)("⇧⌘U")+">"),a.innerHTML='<svg><use xlink:href="#vditor-icon-up"></use></svg>',a.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",a.onclick=function(){e.insertNode(document.createElement("wbr")),r.insertAdjacentElement("beforebegin",t),(0,O.ib)(n.wysiwyg.element,e),we(n),Ye(n),Te(n)},n.wysiwyg.popover.insertAdjacentElement("beforeend",a)}},nt=function(e,t,n){var r=t.nextElementSibling;if(!(!r||!t.parentElement.isEqualNode(n.wysiwyg.element)&&t.tagName!=="LI")){var a=document.createElement("button");a.setAttribute("type","button"),a.setAttribute("data-type","down"),a.setAttribute("aria-label",window.VditorI18n.down+"<"+(0,c.ns)("⇧⌘D")+">"),a.innerHTML='<svg><use xlink:href="#vditor-icon-down"></use></svg>',a.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",a.onclick=function(){e.insertNode(document.createElement("wbr")),r.insertAdjacentElement("afterend",t),(0,O.ib)(n.wysiwyg.element,e),we(n),Ye(n),Te(n)},n.wysiwyg.popover.insertAdjacentElement("beforeend",a)}},Fe=function(e,t){var n=document.createElement("button");n.setAttribute("type","button"),n.setAttribute("data-type","remove"),n.setAttribute("aria-label",window.VditorI18n.remove+"<"+(0,c.ns)("⇧⌘X")+">"),n.innerHTML='<svg><use xlink:href="#vditor-icon-trashcan"></use></svg>',n.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",n.onclick=function(){var r=(0,O.zh)(t);r.setStartAfter(e),(0,O.Hc)(r),e.remove(),we(t),Ye(t),["H1","H2","H3","H4","H5","H6"].includes(e.tagName)&&ve(t)},t.wysiwyg.popover.insertAdjacentElement("beforeend",n)},ct=function(e,t,n,r){if(!n.isComposing){if(n.key==="Tab"){r.focus(),r.select(),n.preventDefault();return}if(!(0,c.yl)(n)&&!n.shiftKey&&n.altKey&&n.key==="Enter"){var a=(0,O.zh)(e);t.insertAdjacentHTML("afterend",M.g.ZWSP),a.setStartAfter(t.nextSibling),a.collapse(!0),(0,O.Hc)(a),n.preventDefault()}}},jt=function(e,t,n){e.wysiwyg.popover.innerHTML="";var r=function(){o.value.trim()!==""&&(t.innerHTML=o.value),t.setAttribute("href",m.value),t.setAttribute("title",h.value),we(e)};t.querySelectorAll("[data-marker]").forEach(function(A){A.removeAttribute("data-marker")});var a=document.createElement("span");a.setAttribute("aria-label",window.VditorI18n.textIsNotEmpty),a.className="vditor-tooltipped vditor-tooltipped__n";var o=document.createElement("input");a.appendChild(o),o.className="vditor-input",o.setAttribute("placeholder",window.VditorI18n.textIsNotEmpty),o.style.width="120px",o.value=t.innerHTML||"",o.oninput=function(){r()},o.onkeydown=function(A){Ne(e,A)||Ge(A,n)||ct(e,t,A,m)};var l=document.createElement("span");l.setAttribute("aria-label",window.VditorI18n.link),l.className="vditor-tooltipped vditor-tooltipped__n";var m=document.createElement("input");l.appendChild(m),m.className="vditor-input",m.setAttribute("placeholder",window.VditorI18n.link),m.value=t.getAttribute("href")||"",m.oninput=function(){r()},m.onkeydown=function(A){Ne(e,A)||Ge(A,n)||ct(e,t,A,h)};var w=document.createElement("span");w.setAttribute("aria-label",window.VditorI18n.tooltipText),w.className="vditor-tooltipped vditor-tooltipped__n";var h=document.createElement("input");w.appendChild(h),h.className="vditor-input",h.setAttribute("placeholder",window.VditorI18n.tooltipText),h.style.width="60px",h.value=t.getAttribute("title")||"",h.oninput=function(){r()},h.onkeydown=function(A){Ne(e,A)||Ge(A,n)||ct(e,t,A,o)},Fe(t,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",a),e.wysiwyg.popover.insertAdjacentElement("beforeend",l),e.wysiwyg.popover.insertAdjacentElement("beforeend",w),We(e,t)},dr=function(e,t){var n=e.target;t.wysiwyg.popover.innerHTML="";var r=function(){n.setAttribute("src",o.value),n.setAttribute("alt",m.value),n.setAttribute("title",h.value),typeof t.options.input=="function"&&t.options.input(R(t))},a=document.createElement("span");a.setAttribute("aria-label",window.VditorI18n.imageURL),a.className="vditor-tooltipped vditor-tooltipped__n";var o=document.createElement("input");a.appendChild(o),o.className="vditor-input",o.setAttribute("placeholder",window.VditorI18n.imageURL),o.value=n.getAttribute("src")||"",o.oninput=function(){r()},o.onkeydown=function(A){Ne(t,A)};var l=document.createElement("span");l.setAttribute("aria-label",window.VditorI18n.alternateText),l.className="vditor-tooltipped vditor-tooltipped__n";var m=document.createElement("input");l.appendChild(m),m.className="vditor-input",m.setAttribute("placeholder",window.VditorI18n.alternateText),m.style.width="52px",m.value=n.getAttribute("alt")||"",m.oninput=function(){r()},m.onkeydown=function(A){Ne(t,A)};var w=document.createElement("span");w.setAttribute("aria-label",window.VditorI18n.title),w.className="vditor-tooltipped vditor-tooltipped__n";var h=document.createElement("input");w.appendChild(h),h.className="vditor-input",h.setAttribute("placeholder",window.VditorI18n.title),h.value=n.getAttribute("title")||"",h.oninput=function(){r()},h.onkeydown=function(A){Ne(t,A)},Fe(n,t),t.wysiwyg.popover.insertAdjacentElement("beforeend",a),t.wysiwyg.popover.insertAdjacentElement("beforeend",l),t.wysiwyg.popover.insertAdjacentElement("beforeend",w),We(t,n)},Ge=function(e,t){if(!(0,c.yl)(e)&&!e.shiftKey&&e.key==="Enter"||e.key==="Escape")return t&&(0,O.Hc)(t),e.preventDefault(),e.stopPropagation(),!0},rt=function(e){e.currentMode==="wysiwyg"?Ye(e):e.currentMode==="ir"&&ut(e)},sn=function(e,t,n){n===void 0&&(n={enableAddUndoStack:!0,enableHint:!1,enableInput:!0});var r=e.wysiwyg.element;r.innerHTML=e.lute.Md2VditorDOM(t),r.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach(function(a){X(a,e),a.previousElementSibling.setAttribute("style","display:none")}),we(e,n)},fr=function(e,t,n){for(var r=e.startContainer.parentElement,a=!1,o="",l="",m=lr(e),w=m.beforeHTML,h=m.afterHTML;r&&!a;){var A=r.tagName;if(A==="STRIKE"&&(A="S"),A==="I"&&(A="EM"),A==="B"&&(A="STRONG"),A==="S"||A==="STRONG"||A==="EM"){var P="",K="",W="";r.parentElement.getAttribute("data-block")!=="0"&&(K=rn(r),W=nn(r)),(w||K)&&(P="".concat(K,"<").concat(A,">").concat(w,"</").concat(A,">"),w=P),(n==="bold"&&A==="STRONG"||n==="italic"&&A==="EM"||n==="strikeThrough"&&A==="S")&&(P+="".concat(o).concat(M.g.ZWSP,"<wbr>").concat(l),a=!0),(h||W)&&(h="<".concat(A,">").concat(h,"</").concat(A,">").concat(W),P+=h),r.parentElement.getAttribute("data-block")!=="0"?(r=r.parentElement,r.innerHTML=P):(r.outerHTML=P,r=r.parentElement),o="<".concat(A,">")+o,l="</".concat(A,">")+l}else a=!0}(0,O.ib)(t.wysiwyg.element,e)},pr=function(e,t,n){if(!(e.wysiwyg.composingLock&&n instanceof CustomEvent)){var r=!0,a=!0;e.wysiwyg.element.querySelector("wbr")&&e.wysiwyg.element.querySelector("wbr").remove();var o=(0,O.zh)(e),l=t.getAttribute("data-type");if(t.classList.contains("vditor-menu--current"))if(l==="strike"&&(l="strikeThrough"),l==="quote"){var m=(0,s.lG)(o.startContainer,"BLOCKQUOTE");m||(m=o.startContainer.childNodes[o.startOffset]),m&&(r=!1,t.classList.remove("vditor-menu--current"),o.insertNode(document.createElement("wbr")),m.outerHTML=m.innerHTML.trim()===""?'<p data-block="0">'.concat(m.innerHTML,"</p>"):m.innerHTML,(0,O.ib)(e.wysiwyg.element,o))}else if(l==="inline-code"){var w=(0,s.lG)(o.startContainer,"CODE");w||(w=o.startContainer.childNodes[o.startOffset]),w&&(w.outerHTML=w.innerHTML.replace(M.g.ZWSP,"")+"<wbr>",(0,O.ib)(e.wysiwyg.element,o))}else l==="link"?(o.collapsed&&o.selectNode(o.startContainer.parentElement),document.execCommand("unlink",!1,"")):l==="check"||l==="list"||l==="ordered-list"?(St(e,o,l),(0,O.ib)(e.wysiwyg.element,o),r=!1,t.classList.remove("vditor-menu--current")):(r=!1,t.classList.remove("vditor-menu--current"),o.toString()===""?fr(o,e,l):document.execCommand(l,!1,""));else{e.wysiwyg.element.childNodes.length===0&&(e.wysiwyg.element.innerHTML='<p data-block="0"><wbr></p>',(0,O.ib)(e.wysiwyg.element,o));var h=(0,s.F9)(o.startContainer);if(l==="quote"){if(h||(h=o.startContainer.childNodes[o.startOffset]),h){r=!1,t.classList.add("vditor-menu--current"),o.insertNode(document.createElement("wbr"));var A=(0,s.lG)(o.startContainer,"LI");A&&h.contains(A)?A.innerHTML='<blockquote data-block="0">'.concat(A.innerHTML,"</blockquote>"):h.outerHTML='<blockquote data-block="0">'.concat(h.outerHTML,"</blockquote>"),(0,O.ib)(e.wysiwyg.element,o)}}else if(l==="check"||l==="list"||l==="ordered-list")St(e,o,l,!1),(0,O.ib)(e.wysiwyg.element,o),r=!1,_(e.toolbar.elements,["check","list","ordered-list"]),t.classList.add("vditor-menu--current");else if(l==="inline-code"){if(o.toString()===""){var P=document.createElement("code");P.textContent=M.g.ZWSP,o.insertNode(P),o.setStart(P.firstChild,1),o.collapse(!0),(0,O.Hc)(o)}else if(o.startContainer.nodeType===3){var P=document.createElement("code");o.surroundContents(P),o.insertNode(P),(0,O.Hc)(o)}t.classList.add("vditor-menu--current")}else if(l==="code"){var P=document.createElement("div");P.className="vditor-wysiwyg__block",P.setAttribute("data-type","code-block"),P.setAttribute("data-block","0"),P.setAttribute("data-marker","```"),o.toString()===""?P.innerHTML=`<pre><code><wbr>
</code></pre>`:(P.innerHTML="<pre><code>".concat(o.toString(),"<wbr></code></pre>"),o.deleteContents()),o.insertNode(P),h&&(h.outerHTML=e.lute.SpinVditorDOM(h.outerHTML)),(0,O.ib)(e.wysiwyg.element,o),e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach(function(pe){X(pe,e)}),t.classList.add("vditor-menu--disabled")}else if(l==="link"){if(o.toString()===""){var K=document.createElement("a");K.innerText=M.g.ZWSP,o.insertNode(K),o.setStart(K.firstChild,1),o.collapse(!0),jt(e,K,o);var W=e.wysiwyg.popover.querySelector("input");W.value="",W.focus(),a=!1}else{var P=document.createElement("a");P.setAttribute("href",""),P.innerHTML=o.toString(),o.surroundContents(P),o.insertNode(P),(0,O.Hc)(o),jt(e,P,o);var V=e.wysiwyg.popover.querySelectorAll("input");V[0].value=P.innerText,V[1].focus()}r=!1,t.classList.add("vditor-menu--current")}else if(l==="table"){var ee='<table data-block="0"><thead><tr><th>col1<wbr></th><th>col2</th><th>col3</th></tr></thead><tbody><tr><td> </td><td> </td><td> </td></tr><tr><td> </td><td> </td><td> </td></tr></tbody></table>';if(o.toString().trim()==="")h&&h.innerHTML.trim().replace(M.g.ZWSP,"")===""?h.outerHTML=ee:document.execCommand("insertHTML",!1,ee),o.selectNode(e.wysiwyg.element.querySelector("wbr").previousSibling),e.wysiwyg.element.querySelector("wbr").remove(),(0,O.Hc)(o);else{ee='<table data-block="0"><thead><tr>';var $=o.toString().split(`
`),F=$[0].split(",").length>$[0].split("	").length?",":"	";$.forEach(function(ae,pe){pe===0?(ae.split(F).forEach(function(fe,ye){ye===0?ee+="<th>".concat(fe,"<wbr></th>"):ee+="<th>".concat(fe,"</th>")}),ee+="</tr></thead>"):(pe===1?ee+="<tbody><tr>":ee+="<tr>",ae.split(F).forEach(function(fe){ee+="<td>".concat(fe,"</td>")}),ee+="</tr>")}),ee+="</tbody></table>",document.execCommand("insertHTML",!1,ee),(0,O.ib)(e.wysiwyg.element,o)}r=!1,t.classList.add("vditor-menu--disabled")}else if(l==="line"){if(h){var oe=`<hr data-block="0"><p data-block="0"><wbr>
</p>`;h.innerHTML.trim()===""?h.outerHTML=oe:h.insertAdjacentHTML("afterend",oe),(0,O.ib)(e.wysiwyg.element,o)}}else if(r=!1,t.classList.add("vditor-menu--current"),l==="strike"&&(l="strikeThrough"),o.toString()===""&&(l==="bold"||l==="italic"||l==="strikeThrough")){var ne="strong";l==="italic"?ne="em":l==="strikeThrough"&&(ne="s");var P=document.createElement(ne);P.textContent=M.g.ZWSP,o.insertNode(P),P.previousSibling&&P.previousSibling.textContent===M.g.ZWSP&&(P.previousSibling.textContent=""),o.setStart(P.firstChild,1),o.collapse(!0),(0,O.Hc)(o)}else document.execCommand(l,!1,"")}r&&Ye(e),a&&we(e)}},Ce=function(){function e(t,n){var r,a=this;this.element=document.createElement("div"),n.className&&(r=this.element.classList).add.apply(r,n.className.split(" "));var o=n.hotkey?" <".concat((0,c.ns)(n.hotkey),">"):"";n.level===2&&(o=n.hotkey?" &lt;".concat((0,c.ns)(n.hotkey),"&gt;"):"");var l=n.tip?n.tip+o:"".concat(window.VditorI18n[n.name]).concat(o),m=n.name==="upload"?"div":"button";if(n.level===2)this.element.innerHTML="<".concat(m,' data-type="').concat(n.name,'">').concat(l,"</").concat(m,">");else{this.element.classList.add("vditor-toolbar__item");var w=document.createElement(m);w.setAttribute("data-type",n.name),w.className="vditor-tooltipped vditor-tooltipped__".concat(n.tipPosition),w.setAttribute("aria-label",l),w.innerHTML=n.icon,this.element.appendChild(w)}n.prefix&&this.element.children[0].addEventListener((0,c.Le)(),function(h){h.preventDefault(),!a.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)&&(t.currentMode==="wysiwyg"?pr(t,a.element.children[0],h):t.currentMode==="ir"?Tr(t,a.element.children[0],n.prefix||"",n.suffix||""):br(t,a.element.children[0],n.prefix||"",n.suffix||""))})}return e}(),hr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),et=function(e,t,n){var r;if(typeof n!="string"?(S(e,["subToolbar","hint"]),n.preventDefault(),r=R(e)):r=n,!(e.currentMode===t&&typeof n!="string")){if(e.devtools&&e.devtools.renderEchart(e),e.options.preview.mode==="both"&&t==="sv"?e.preview.element.style.display="block":e.preview.element.style.display="none",y(e.toolbar.elements,M.g.EDIT_TOOLBARS),_(e.toolbar.elements,M.g.EDIT_TOOLBARS),p(e.toolbar.elements,["outdent","indent"]),t==="ir")C(e.toolbar.elements,["both"]),L(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.sv.element.style.display="none",e.wysiwyg.element.parentElement.style.display="none",e.ir.element.parentElement.style.display="block",e.lute.SetVditorIR(!0),e.lute.SetVditorWYSIWYG(!1),e.lute.SetVditorSV(!1),e.currentMode="ir",e.ir.element.innerHTML=e.lute.Md2VditorIRDOM(r),Ze(e,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),Ve(e),e.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach(function(o){X(o,e)}),e.ir.element.querySelectorAll(".vditor-toc").forEach(function(o){(0,I.H)(o,{cdn:e.options.cdn,math:e.options.preview.math})});else if(t==="wysiwyg")C(e.toolbar.elements,["both"]),L(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.sv.element.style.display="none",e.wysiwyg.element.parentElement.style.display="block",e.ir.element.parentElement.style.display="none",e.lute.SetVditorIR(!1),e.lute.SetVditorWYSIWYG(!0),e.lute.SetVditorSV(!1),e.currentMode="wysiwyg",Ve(e),sn(e,r,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),e.wysiwyg.element.querySelectorAll(".vditor-toc").forEach(function(o){(0,I.H)(o,{cdn:e.options.cdn,math:e.options.preview.math})}),e.wysiwyg.popover.style.display="none";else if(t==="sv"){L(e.toolbar.elements,["both"]),C(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.wysiwyg.element.parentElement.style.display="none",e.ir.element.parentElement.style.display="none",(e.options.preview.mode==="both"||e.options.preview.mode==="editor")&&(e.sv.element.style.display="block"),e.lute.SetVditorIR(!1),e.lute.SetVditorWYSIWYG(!1),e.lute.SetVditorSV(!0),e.currentMode="sv";var a=un(r,e);a==="<div data-block='0'></div>"&&(a=""),e.sv.element.innerHTML=a,Se(e.sv.element),Ae(e,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),Ve(e)}e.undo.resetIcon(e),typeof n!="string"&&(e[e.currentMode].element.focus(),rt(e)),ve(e),vt(e),e.toolbar.elements["edit-mode"]&&(e.toolbar.elements["edit-mode"].querySelectorAll("button").forEach(function(o){o.classList.remove("vditor-menu--current")}),e.toolbar.elements["edit-mode"].querySelector('button[data-mode="'.concat(e.currentMode,'"]')).classList.add("vditor-menu--current")),e.outline.toggle(e,e.currentMode!=="sv"&&e.options.outline.enable,typeof n!="string")}},mr=function(e){hr(t,e);function t(n,r){var a=e.call(this,n,r)||this,o=document.createElement("div");return o.className="vditor-hint".concat(r.level===2?"":" vditor-panel--arrow"),o.innerHTML='<button data-mode="wysiwyg">'.concat(window.VditorI18n.wysiwyg," &lt;").concat((0,c.ns)("⌥⌘7"),`></button>
<button data-mode="ir">`).concat(window.VditorI18n.instantRendering," &lt;").concat((0,c.ns)("⌥⌘8"),`></button>
<button data-mode="sv">`).concat(window.VditorI18n.splitView," &lt;").concat((0,c.ns)("⌥⌘9"),"></button>"),a.element.appendChild(o),a._bindEvent(n,o,r),a}return t.prototype._bindEvent=function(n,r,a){var o=this.element.children[0];i(n,r,o,a.level),r.children.item(0).addEventListener((0,c.Le)(),function(l){et(n,"wysiwyg",l),l.preventDefault(),l.stopPropagation()}),r.children.item(1).addEventListener((0,c.Le)(),function(l){et(n,"ir",l),l.preventDefault(),l.stopPropagation()}),r.children.item(2).addEventListener((0,c.Le)(),function(l){et(n,"sv",l),l.preventDefault(),l.stopPropagation()})},t}(Ce),dt=function(e,t){return(0,O.Gb)(e,t)?getSelection().toString():""},Rt=function(e,t){t.addEventListener("focus",function(){e.options.focus&&e.options.focus(R(e)),S(e,["subToolbar","hint"])})},on=function(e,t){t.addEventListener("dblclick",function(n){n.target.tagName==="IMG"&&(e.options.image.preview?e.options.image.preview(n.target):e.options.image.isPreview&&(0,Y.E)(n.target,e.options.lang,e.options.theme))})},It=function(e,t){t.addEventListener("blur",function(n){if(e.currentMode==="ir"){var r=e.ir.element.querySelector(".vditor-ir__node--expand");r&&r.classList.remove("vditor-ir__node--expand")}else e.currentMode==="wysiwyg"&&!e.wysiwyg.selectPopover.contains(n.relatedTarget)&&e.wysiwyg.hideComment();e[e.currentMode].range=(0,O.zh)(e),e.options.blur&&e.options.blur(R(e))})},xt=function(e,t){t.addEventListener("dragstart",function(n){n.dataTransfer.setData(M.g.DROP_EDITOR,M.g.DROP_EDITOR)}),t.addEventListener("drop",function(n){n.dataTransfer.getData(M.g.DROP_EDITOR)?le(e):(n.dataTransfer.types.includes("Files")||n.dataTransfer.types.includes("text/html"))&&Lt(e,n,{pasteCode:function(r){document.execCommand("insertHTML",!1,r)}})})},Bt=function(e,t,n){t.addEventListener("copy",function(r){return n(r,e)})},zt=function(e,t,n){t.addEventListener("cut",function(r){n(r,e),e.options.comment.enable&&e.currentMode==="wysiwyg"&&e.wysiwyg.getComments(e),document.execCommand("delete")})},Te=function(e){if(e.currentMode==="wysiwyg"&&e.options.comment.enable&&e.options.comment.adjustTop(e.wysiwyg.getComments(e,!0)),!!e.options.typewriterMode){var t=e[e.currentMode].element,n=(0,O.Ny)(t).top;e.options.height==="auto"&&!e.element.classList.contains("vditor--fullscreen")&&window.scrollTo(window.scrollX,n+e.element.offsetTop+e.toolbar.element.offsetHeight-window.innerHeight/2+10),(e.options.height!=="auto"||e.element.classList.contains("vditor--fullscreen"))&&(t.scrollTop=n+t.scrollTop-t.clientHeight/2+10)}},Ut=function(e,t){t.addEventListener("keydown",function(n){if(!n.isComposing&&e.options.keydown&&e.options.keydown(n),!((e.options.hint.extend.length>1||e.toolbar.elements.emoji)&&e.hint.select(n,e))){if(e.options.comment.enable&&e.currentMode==="wysiwyg"&&(n.key==="Backspace"||U("⌘X",n))&&e.wysiwyg.getComments(e),e.currentMode==="sv"){if(De(e,n))return}else if(e.currentMode==="wysiwyg"){if(cr(e,n))return}else if(e.currentMode==="ir"&&be(e,n))return;if(e.options.ctrlEnter&&U("⌘Enter",n)){e.options.ctrlEnter(R(e)),n.preventDefault();return}if(U("⌘Z",n)&&!e.toolbar.elements.undo){e.undo.undo(e),n.preventDefault();return}if(U("⌘Y",n)&&!e.toolbar.elements.redo){e.undo.redo(e),n.preventDefault();return}if(n.key==="Escape"){e.hint.element.style.display==="block"?e.hint.element.style.display="none":e.options.esc&&!n.isComposing&&e.options.esc(R(e)),n.preventDefault();return}if((0,c.yl)(n)&&n.altKey&&!n.shiftKey&&/^Digit[1-6]$/.test(n.code)){if(e.currentMode==="wysiwyg"){var r=n.code.replace("Digit","H");(0,s.lG)(getSelection().getRangeAt(0).startContainer,r)?Nt(e):_t(e,r),we(e)}else e.currentMode==="sv"?ln(e,"#".repeat(parseInt(n.code.replace("Digit",""),10))+" "):e.currentMode==="ir"&&ht(e,"#".repeat(parseInt(n.code.replace("Digit",""),10))+" ");return n.preventDefault(),!0}if((0,c.yl)(n)&&n.altKey&&!n.shiftKey&&/^Digit[7-9]$/.test(n.code))return n.code==="Digit7"?et(e,"wysiwyg",n):n.code==="Digit8"?et(e,"ir",n):n.code==="Digit9"&&et(e,"sv",n),!0;e.options.toolbar.find(function(a){if(!a.hotkey||a.toolbar){if(a.toolbar){var o=a.toolbar.find(function(l){if(!l.hotkey)return!1;if(U(l.hotkey,n))return e.toolbar.elements[l.name].children[0].dispatchEvent(new CustomEvent((0,c.Le)())),n.preventDefault(),!0});return!!o}return!1}if(U(a.hotkey,n))return e.toolbar.elements[a.name].children[0].dispatchEvent(new CustomEvent((0,c.Le)())),n.preventDefault(),!0})}})},Vt=function(e,t){t.addEventListener("selectstart",function(n){t.onmouseup=function(){setTimeout(function(){var r=dt(e[e.currentMode].element);r.trim()?(e.currentMode==="wysiwyg"&&e.options.comment.enable&&(!(0,s.a1)(n.target,"data-type","footnotes-block")&&!(0,s.a1)(n.target,"data-type","link-ref-defs-block")?e.wysiwyg.showComment():e.wysiwyg.hideComment()),e.options.select&&e.options.select(r)):(e.currentMode==="wysiwyg"&&e.options.comment.enable&&e.wysiwyg.hideComment(),typeof e.options.unSelect=="function"&&e.options.unSelect())})}})},Wt=function(e,t){var n=(0,O.zh)(e);n.extractContents(),n.insertNode(document.createTextNode(Lute.Caret)),n.insertNode(document.createTextNode(t));var r=(0,s.a1)(n.startContainer,"data-block","0");r||(r=e.sv.element);var a=e.lute.SpinVditorSVDOM(r.textContent);a="<div data-block='0'>"+a.replace(/<span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span><span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span></g,`<span data-type="newline"><br /><span style="display: none">
</span></span><span data-type="newline"><br /><span style="display: none">
</span></span></div><div data-block="0"><`)+"</div>",r.isEqualNode(e.sv.element)?r.innerHTML=a:r.outerHTML=a,Se(e.sv.element),(0,O.ib)(e.sv.element,n),Te(e)},kt=function(e,t,n){n===void 0&&(n=!0);var r=e;for(r.nodeType===3&&(r=r.parentElement);r;){if(r.getAttribute("data-type")===t)return r;n?r=r.previousElementSibling:r=r.nextElementSibling}return!1},un=function(e,t){f("SpinVditorSVDOM",e,"argument",t.options.debugger);var n=t.lute.SpinVditorSVDOM(e);return e="<div data-block='0'>"+n.replace(/<span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span><span data-type="newline"><br \/><span style="display: none">\n<\/span><\/span></g,`<span data-type="newline"><br /><span style="display: none">
</span></span><span data-type="newline"><br /><span style="display: none">
</span></span></div><div data-block="0"><`)+"</div>",f("SpinVditorSVDOM",e,"result",t.options.debugger),e},gr=function(e){var t=e.getAttribute("data-type"),n=e.previousElementSibling,r=t&&t!=="text"&&t!=="table"&&t!=="heading-marker"&&t!=="newline"&&t!=="yaml-front-matter-open-marker"&&t!=="yaml-front-matter-close-marker"&&t!=="code-block-info"&&t!=="code-block-close-marker"&&t!=="code-block-open-marker"?e.textContent:"",a=!1;for(t==="newline"&&(a=!0);n&&!a;){var o=n.getAttribute("data-type");if(o==="li-marker"||o==="blockquote-marker"||o==="task-marker"||o==="padding"){var l=n.textContent;if(o==="li-marker"&&(t==="code-block-open-marker"||t==="code-block-info"))r=l.replace(/\S/g," ")+r;else if(t==="code-block-close-marker"&&n.nextElementSibling.isSameNode(e)){var m=kt(e,"code-block-open-marker");m&&m.previousElementSibling&&(n=m.previousElementSibling,r=l+r)}else r=l+r}else o==="newline"&&(a=!0);n=n.previousElementSibling}return r},Ae=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&e.hint.render(e),e.preview.render(e);var n=R(e);typeof e.options.input=="function"&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&(0,c.pK)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),clearTimeout(e.sv.processTimeoutId),e.sv.processTimeoutId=window.setTimeout(function(){t.enableAddUndoStack&&!e.sv.composingLock&&e.undo.addToUndoStack(e)},e.options.undoDelay)},ln=function(e,t){var n=(0,O.zh)(e),r=(0,u.S)(n.startContainer,"SPAN");r&&r.textContent.trim()!==""&&(t=`
`+t),n.collapse(!0),document.execCommand("insertHTML",!1,t)},br=function(e,t,n,r){var a=(0,O.zh)(e),o=t.getAttribute("data-type");e.sv.element.childNodes.length===0&&(e.sv.element.innerHTML=`<span data-type="p" data-block="0"><span data-type="text"><wbr></span></span><span data-type="newline"><br><span style="display: none">
</span></span>`,(0,O.ib)(e.sv.element,a));var l=(0,s.F9)(a.startContainer),m=(0,u.S)(a.startContainer,"SPAN");if(l){if(o==="link"){var w=void 0;a.toString()===""?w="".concat(n).concat(Lute.Caret).concat(r):w="".concat(n).concat(a.toString()).concat(r.replace(")",Lute.Caret+")")),document.execCommand("insertHTML",!1,w);return}else if(o==="italic"||o==="bold"||o==="strike"||o==="inline-code"||o==="code"||o==="table"||o==="line"){var w=void 0;a.toString()===""?w="".concat(n).concat(Lute.Caret).concat(o==="code"?"":r):w="".concat(n).concat(a.toString()).concat(Lute.Caret).concat(o==="code"?"":r),o==="table"||o==="code"&&m&&m.textContent!==""?w=`

`+w:o==="line"&&(w=`

`.concat(n,`
`).concat(Lute.Caret)),document.execCommand("insertHTML",!1,w);return}else if((o==="check"||o==="list"||o==="ordered-list"||o==="quote")&&m){var h="* ";o==="check"?h="* [ ] ":o==="ordered-list"?h="1. ":o==="quote"&&(h="> ");var A=kt(m,"newline");A?A.insertAdjacentText("afterend",h):l.insertAdjacentText("afterbegin",h),he(e);return}(0,O.ib)(e.sv.element,a),Ae(e)}},cn=function(e){switch(e.currentMode){case"ir":return e.ir.element;case"wysiwyg":return e.wysiwyg.element;case"sv":return e.sv.element}},dn=function(e,t){e.options.upload.setHeaders&&(e.options.upload.headers=e.options.upload.setHeaders()),e.options.upload.headers&&Object.keys(e.options.upload.headers).forEach(function(n){t.setRequestHeader(n,e.options.upload.headers[n])})},wr=function(e,t,n,r){function a(o){return o instanceof n?o:new n(function(l){l(o)})}return new(n||(n=Promise))(function(o,l){function m(A){try{h(r.next(A))}catch(P){l(P)}}function w(A){try{h(r.throw(A))}catch(P){l(P)}}function h(A){A.done?o(A.value):a(A.value).then(m,w)}h((r=r.apply(e,t||[])).next())})},yr=function(e,t){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,a,o,l;return l={next:m(0),throw:m(1),return:m(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function m(h){return function(A){return w([h,A])}}function w(h){if(r)throw new TypeError("Generator is already executing.");for(;l&&(l=0,h[0]&&(n=0)),n;)try{if(r=1,a&&(o=h[0]&2?a.return:h[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,h[1])).done)return o;switch(a=0,o&&(h=[h[0]&2,o.value]),h[0]){case 0:case 1:o=h;break;case 4:return n.label++,{value:h[1],done:!1};case 5:n.label++,a=h[1],h=[0];continue;case 7:h=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(h[0]===6||h[0]===2)){n=0;continue}if(h[0]===3&&(!o||h[1]>o[0]&&h[1]<o[3])){n.label=h[1];break}if(h[0]===6&&n.label<o[1]){n.label=o[1],o=h;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(h);break}o[2]&&n.ops.pop(),n.trys.pop();continue}h=t.call(e,n)}catch(A){h=[6,A],a=0}finally{r=o=0}if(h[0]&5)throw h[1];return{value:h[0]?h[1]:void 0,done:!0}}},vr=function(){function e(){this.isUploading=!1,this.element=document.createElement("div"),this.element.className="vditor-upload"}return e}(),_r=function(e,t){e.tip.hide();for(var n=[],r="",a="",o=e.options.lang,l=e.options,m=function(P,K){var W=t[K],V=!0;W.name||(r+="<li>".concat(window.VditorI18n.nameEmpty,"</li>"),V=!1),W.size>e.options.upload.max&&(r+="<li>".concat(W.name," ").concat(window.VditorI18n.over," ").concat(e.options.upload.max/1024/1024,"M</li>"),V=!1);var ee=W.name.lastIndexOf("."),$=W.name.substr(ee),F=e.options.upload.filename(W.name.substr(0,ee))+$;if(e.options.upload.accept){var oe=e.options.upload.accept.split(",").some(function(ne){var ae=ne.trim();if(ae.indexOf(".")===0){if($.toLowerCase()===ae.toLowerCase())return!0}else if(W.type.split("/")[0]===ae.split("/")[0])return!0;return!1});oe||(r+="<li>".concat(W.name," ").concat(window.VditorI18n.fileTypeError,"</li>"),V=!1)}V&&(n.push(W),a+="<li>".concat(F," ").concat(window.VditorI18n.uploading,' <a class="vditorCancelUpload" href="javascript:void(0)">').concat(window.VditorI18n.cancelUpload,"</a></li>"))},w=t.length,h=0;h<w;h++)m(w,h);if(e.tip.show("<ul>".concat(r).concat(a,"</ul>")),e.options.upload.cancel){var A=e.tip.element.querySelector(".vditorCancelUpload");A&&A.addEventListener("click",function(){e.options.upload.cancel(n),e.tip.hide(),e.upload.isUploading=!1})}return n},kr=function(e,t){var n=cn(t);n.focus();var r=JSON.parse(e),a="";r.code===1&&(a="".concat(r.msg)),r.data.errFiles&&r.data.errFiles.length>0&&(a="<ul><li>".concat(a,"</li>"),r.data.errFiles.forEach(function(l){var m=l.lastIndexOf("."),w=t.options.upload.filename(l.substr(0,m))+l.substr(m);a+="<li>".concat(w," ").concat(window.VditorI18n.uploadError,"</li>")}),a+="</ul>"),a?t.tip.show(a):t.tip.hide();var o="";Object.keys(r.data.succMap).forEach(function(l){var m=r.data.succMap[l],w=l.lastIndexOf("."),h=l.substr(w),A=t.options.upload.filename(l.substr(0,w))+h;h=h.toLowerCase(),h.indexOf(".wav")===0||h.indexOf(".mp3")===0||h.indexOf(".ogg")===0?t.currentMode==="wysiwyg"?o+=`<div class="vditor-wysiwyg__block" data-type="html-block"
 data-block="0"><pre><code>&lt;audio controls="controls" src="`.concat(m,'"&gt;&lt;/audio&gt;</code></pre><pre class="vditor-wysiwyg__preview" data-render="1"><audio controls="controls" src="').concat(m,`"></audio></pre></div>
`):t.currentMode==="ir"?o+='<audio controls="controls" src="'.concat(m,`"></audio>
`):o+="[".concat(A,"](").concat(m,`)
`):h.indexOf(".apng")===0||h.indexOf(".bmp")===0||h.indexOf(".gif")===0||h.indexOf(".ico")===0||h.indexOf(".cur")===0||h.indexOf(".jpg")===0||h.indexOf(".jpeg")===0||h.indexOf(".jfif")===0||h.indexOf(".pjp")===0||h.indexOf(".pjpeg")===0||h.indexOf(".png")===0||h.indexOf(".svg")===0||h.indexOf(".webp")===0?t.currentMode==="wysiwyg"?o+='<img alt="'.concat(A,'" src="').concat(m,`">
`):o+="![".concat(A,"](").concat(m,`)
`):t.currentMode==="wysiwyg"?o+='<a href="'.concat(m,'">').concat(A,`</a>
`):o+="[".concat(A,"](").concat(m,`)
`)}),(0,O.Hc)(t.upload.range),document.execCommand("insertHTML",!1,o),t.upload.range=getSelection().getRangeAt(0).cloneRange()},Ft=function(e,t,n){return wr(void 0,void 0,void 0,function(){var r,a,V,o,l,l,m,w,h,A,P,K,W,V,ee,$;return yr(this,function(F){switch(F.label){case 0:for(r=[],a=e.options.upload.multiple===!0?t.length:1,V=0;V<a;V++)o=t[V],o instanceof DataTransferItem&&(o=o.getAsFile()),r.push(o);return e.options.upload.handler?[4,e.options.upload.handler(r)]:[3,2];case 1:return l=F.sent(),n&&(n.value=""),typeof l=="string"?(e.tip.show(l),[2]):[2];case 2:return!e.options.upload.url||!e.upload?(n&&(n.value=""),e.tip.show("please config: options.upload.url"),[2]):e.options.upload.file?[4,e.options.upload.file(r)]:[3,4];case 3:r=F.sent(),F.label=4;case 4:if(e.options.upload.validate&&(l=e.options.upload.validate(r),typeof l=="string"))return e.tip.show(l),[2];if(m=cn(e),e.upload.range=(0,O.zh)(e),w=_r(e,r),w.length===0)return n&&(n.value=""),[2];for(h=new FormData,A=e.options.upload.extraData,P=0,K=Object.keys(A);P<K.length;P++)W=K[P],h.append(W,A[W]);for(V=0,ee=w.length;V<ee;V++)h.append(e.options.upload.fieldName,w[V]);return $=new XMLHttpRequest,e.upload.xhr=$,$.open("POST",e.options.upload.url),e.options.upload.token&&$.setRequestHeader("X-Upload-Token",e.options.upload.token),e.options.upload.withCredentials&&($.withCredentials=!0),dn(e,$),e.upload.isUploading=!0,m.setAttribute("contenteditable","false"),$.onreadystatechange=function(){if($.readyState===XMLHttpRequest.DONE){if(e.upload.isUploading=!1,m.setAttribute("contenteditable","true"),$.status>=200&&$.status<300)if(e.options.upload.success)e.options.upload.success(m,$.responseText);else{var oe=$.responseText;e.options.upload.format&&(oe=e.options.upload.format(t,$.responseText)),kr(oe,e)}else e.options.upload.error?e.options.upload.error($.responseText):e.tip.show($.responseText);n&&(n.value=""),e.upload.element.style.display="none",e.upload.xhr=void 0}},$.upload.onprogress=function(oe){if(oe.lengthComputable){var ne=oe.loaded/oe.total*100;e.upload.element.style.display="block";var ae=e.upload.element;ae.style.width=ne+"%"}},$.send(h),[2]}})})},Et=function(e,t,n){var r,a=(0,s.F9)(t.startContainer);if(a||(a=e.wysiwyg.element),n&&n.inputType!=="formatItalic"&&n.inputType!=="deleteByDrag"&&n.inputType!=="insertFromDrop"&&n.inputType!=="formatBold"&&n.inputType!=="formatRemove"&&n.inputType!=="formatStrikeThrough"&&n.inputType!=="insertUnorderedList"&&n.inputType!=="insertOrderedList"&&n.inputType!=="formatOutdent"&&n.inputType!=="formatIndent"&&n.inputType!==""||!n){var o=sr(t.startContainer);o&&o.remove(),e.wysiwyg.element.querySelectorAll("wbr").forEach(function(ne){ne.remove()}),t.insertNode(document.createElement("wbr")),a.querySelectorAll("[style]").forEach(function(ne){ne.removeAttribute("style")}),a.querySelectorAll(".vditor-comment").forEach(function(ne){ne.textContent.trim()===""&&(ne.classList.remove("vditor-comment","vditor-comment--focus"),ne.removeAttribute("data-cmtids"))}),(r=a.previousElementSibling)===null||r===void 0||r.querySelectorAll(".vditor-comment").forEach(function(ne){ne.textContent.trim()===""&&(ne.classList.remove("vditor-comment","vditor-comment--focus"),ne.removeAttribute("data-cmtids"))});var l="";a.getAttribute("data-type")==="link-ref-defs-block"&&(a=e.wysiwyg.element);var m=a.isEqualNode(e.wysiwyg.element),w=(0,s.a1)(a,"data-type","footnotes-block");if(m)l=a.innerHTML;else{var h=(0,s.O9)(t.startContainer);if(h&&!w){var A=(0,u.S)(t.startContainer,"BLOCKQUOTE");A?a=(0,s.F9)(t.startContainer)||a:a=h}if(w&&(a=w),l=a.outerHTML,a.tagName==="UL"||a.tagName==="OL"){var P=a.previousElementSibling,K=a.nextElementSibling;P&&(P.tagName==="UL"||P.tagName==="OL")&&(l=P.outerHTML+l,P.remove()),K&&(K.tagName==="UL"||K.tagName==="OL")&&(l=l+K.outerHTML,K.remove()),l=l.replace("<div><wbr><br></div>","<li><p><wbr><br></p></li>")}a.innerText.startsWith("```")||(e.wysiwyg.element.querySelectorAll("[data-type='link-ref-defs-block']").forEach(function(ne){ne&&!a.isEqualNode(ne)&&(l+=ne.outerHTML,ne.remove())}),e.wysiwyg.element.querySelectorAll("[data-type='footnotes-block']").forEach(function(ne){ne&&!a.isEqualNode(ne)&&(l+=ne.outerHTML,ne.remove())}))}if(l=l.replace(/<\/(strong|b)><strong data-marker="\W{2}">/g,"").replace(/<\/(em|i)><em data-marker="\W{1}">/g,"").replace(/<\/(s|strike)><s data-marker="~{1,2}">/g,""),l==='<p data-block="0">```<wbr></p>'&&e.hint.recentLanguage&&(l='<p data-block="0">```<wbr></p>'.replace("```","```"+e.hint.recentLanguage)),f("SpinVditorDOM",l,"argument",e.options.debugger),l=e.lute.SpinVditorDOM(l),f("SpinVditorDOM",l,"result",e.options.debugger),m)a.innerHTML=l;else if(a.outerHTML=l,w){var W=(0,s.E2)(e.wysiwyg.element.querySelector("wbr"),"LI");if(W){var V=e.wysiwyg.element.querySelector('sup[data-type="footnotes-ref"][data-footnotes-label="'.concat(W.getAttribute("data-marker"),'"]'));V&&V.setAttribute("aria-label",W.textContent.trim().substr(0,24))}}var ee,$=e.wysiwyg.element.querySelectorAll("[data-type='link-ref-defs-block']");$.forEach(function(ne,ae){ae===0?ee=ne:(ee.insertAdjacentHTML("beforeend",ne.innerHTML),ne.remove())}),$.length>0&&e.wysiwyg.element.insertAdjacentElement("beforeend",$[0]);var F,oe=e.wysiwyg.element.querySelectorAll("[data-type='footnotes-block']");oe.forEach(function(ne,ae){ae===0?F=ne:(F.insertAdjacentHTML("beforeend",ne.innerHTML),ne.remove())}),oe.length>0&&e.wysiwyg.element.insertAdjacentElement("beforeend",oe[0]),(0,O.ib)(e.wysiwyg.element,t),e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach(function(ne){X(ne,e)}),n&&(n.inputType==="deleteContentBackward"||n.inputType==="deleteContentForward")&&e.options.comment.enable&&(e.wysiwyg.triggerRemoveComment(e),e.options.comment.adjustTop(e.wysiwyg.getComments(e,!0)))}ve(e),we(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},Er=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},Sr=function(e,t,n,r){function a(o){return o instanceof n?o:new n(function(l){l(o)})}return new(n||(n=Promise))(function(o,l){function m(A){try{h(r.next(A))}catch(P){l(P)}}function w(A){try{h(r.throw(A))}catch(P){l(P)}}function h(A){A.done?o(A.value):a(A.value).then(m,w)}h((r=r.apply(e,t||[])).next())})},Cr=function(e,t){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,a,o,l;return l={next:m(0),throw:m(1),return:m(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function m(h){return function(A){return w([h,A])}}function w(h){if(r)throw new TypeError("Generator is already executing.");for(;l&&(l=0,h[0]&&(n=0)),n;)try{if(r=1,a&&(o=h[0]&2?a.return:h[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,h[1])).done)return o;switch(a=0,o&&(h=[h[0]&2,o.value]),h[0]){case 0:case 1:o=h;break;case 4:return n.label++,{value:h[1],done:!1};case 5:n.label++,a=h[1],h=[0];continue;case 7:h=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(h[0]===6||h[0]===2)){n=0;continue}if(h[0]===3&&(!o||h[1]>o[0]&&h[1]<o[3])){n.label=h[1];break}if(h[0]===6&&n.label<o[1]){n.label=o[1],o=h;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(h);break}o[2]&&n.ops.pop(),n.trys.pop();continue}h=t.call(e,n)}catch(A){h=[6,A],a=0}finally{r=o=0}if(h[0]&5)throw h[1];return{value:h[0]?h[1]:void 0,done:!0}}},fn=function(e,t,n){if(e.keyCode===229&&e.code===""&&e.key==="Unidentified"&&t.currentMode!=="sv"){var r=(0,s.F9)(n);if(r&&r.textContent.trim()==="")return t[t.currentMode].composingLock=!0,!1}return!0},pn=function(e,t,n){if(!(n.key==="Enter"||n.key==="Tab"||n.key==="Backspace"||n.key.indexOf("Arrow")>-1||(0,c.yl)(n)||n.key==="Escape"||n.shiftKey||n.altKey)){var r=(0,s.lG)(e.startContainer,"P")||(0,s.lG)(e.startContainer,"LI");if(r&&(0,O.im)(r,t[t.currentMode].element,e).start===0){r.nodeValue&&(r.nodeValue=r.nodeValue.replace(/\u2006/g,""));var a=document.createTextNode(M.g.ZWSP);e.insertNode(a),e.setStartAfter(a)}}},hn=function(e,t){if(t==="ArrowDown"||t==="ArrowUp"){var n=(0,s.a1)(e.startContainer,"data-type","math-inline")||(0,s.a1)(e.startContainer,"data-type","html-entity")||(0,s.a1)(e.startContainer,"data-type","html-inline");n&&(t==="ArrowDown"&&e.setStartAfter(n.parentElement),t==="ArrowUp"&&e.setStartBefore(n.parentElement))}},ft=function(e,t){var n=(0,O.zh)(e),r=(0,s.F9)(n.startContainer);r&&(r.insertAdjacentHTML(t,'<p data-block="0">'.concat(M.g.ZWSP,`<wbr>
</p>`)),(0,O.ib)(e[e.currentMode].element,n),rt(e),le(e))},Mr=function(e){var t=(0,s.lG)(e,"TABLE");return t&&t.rows[0].cells[0].isSameNode(e)?t:!1},Lr=function(e){var t=(0,s.lG)(e,"TABLE");return t&&t.lastElementChild.lastElementChild.lastElementChild.isSameNode(e)?t:!1},mn=function(e,t,n){n===void 0&&(n=!0);var r=e.previousElementSibling;return r||(e.parentElement.previousElementSibling?r=e.parentElement.previousElementSibling.lastElementChild:e.parentElement.parentElement.tagName==="TBODY"&&e.parentElement.parentElement.previousElementSibling?r=e.parentElement.parentElement.previousElementSibling.lastElementChild.lastElementChild:r=null),r&&(t.selectNodeContents(r),n||t.collapse(!1),(0,O.Hc)(t)),r},pt=function(e,t,n,r,a){var o=(0,O.im)(r,e[e.currentMode].element,n);if(t.key==="ArrowDown"&&r.textContent.trimRight().substr(o.start).indexOf(`
`)===-1||t.key==="ArrowRight"&&o.start>=r.textContent.trimRight().length){var l=a.nextElementSibling;return!l||l&&(l.tagName==="TABLE"||l.getAttribute("data-type"))?(a.insertAdjacentHTML("afterend",'<p data-block="0">'.concat(M.g.ZWSP,"<wbr></p>")),(0,O.ib)(e[e.currentMode].element,n)):(n.selectNodeContents(l),n.collapse(!0),(0,O.Hc)(n)),t.preventDefault(),!0}return!1},at=function(e,t,n,r,a){var o=(0,O.im)(r,e[e.currentMode].element,n);if(t.key==="ArrowUp"&&r.textContent.substr(0,o.start).indexOf(`
`)===-1||(t.key==="ArrowLeft"||t.key==="Backspace"&&n.toString()==="")&&o.start===0){var l=a.previousElementSibling;return!l||l&&(l.tagName==="TABLE"||l.getAttribute("data-type"))?(a.insertAdjacentHTML("beforebegin",'<p data-block="0">'.concat(M.g.ZWSP,"<wbr></p>")),(0,O.ib)(e[e.currentMode].element,n)):(n.selectNodeContents(l),n.collapse(!1),(0,O.Hc)(n)),t.preventDefault(),!0}return!1},St=function(e,t,n,r){r===void 0&&(r=!0);var a=(0,s.lG)(t.startContainer,"LI");if(e[e.currentMode].element.querySelectorAll("wbr").forEach(function(A){A.remove()}),t.insertNode(document.createElement("wbr")),r&&a){for(var o="",l=0;l<a.parentElement.childElementCount;l++){var m=a.parentElement.children[l].querySelector("input");m&&m.remove(),o+='<p data-block="0">'.concat(a.parentElement.children[l].innerHTML.trimLeft(),"</p>")}a.parentElement.insertAdjacentHTML("beforebegin",o),a.parentElement.remove()}else if(a)if(n==="check")a.parentElement.querySelectorAll("li").forEach(function(A){A.insertAdjacentHTML("afterbegin",'<input type="checkbox" />'.concat(A.textContent.indexOf(" ")===0?"":" ")),A.classList.add("vditor-task")});else{a.querySelector("input")&&a.parentElement.querySelectorAll("li").forEach(function(A){A.querySelector("input").remove(),A.classList.remove("vditor-task")});var h=void 0;n==="list"?(h=document.createElement("ul"),h.setAttribute("data-marker","*")):(h=document.createElement("ol"),h.setAttribute("data-marker","1.")),h.setAttribute("data-block","0"),h.setAttribute("data-tight",a.parentElement.getAttribute("data-tight")),h.innerHTML=a.parentElement.innerHTML,a.parentElement.parentNode.replaceChild(h,a.parentElement)}else{var w=(0,s.a1)(t.startContainer,"data-block","0");w||(e[e.currentMode].element.querySelector("wbr").remove(),w=e[e.currentMode].element.querySelector("p"),w.innerHTML="<wbr>"),n==="check"?(w.insertAdjacentHTML("beforebegin",'<ul data-block="0"><li class="vditor-task"><input type="checkbox" /> '.concat(w.innerHTML,"</li></ul>")),w.remove()):n==="list"?(w.insertAdjacentHTML("beforebegin",'<ul data-block="0"><li>'.concat(w.innerHTML,"</li></ul>")),w.remove()):n==="ordered-list"&&(w.insertAdjacentHTML("beforebegin",'<ol data-block="0"><li>'.concat(w.innerHTML,"</li></ol>")),w.remove())}},gn=function(e,t,n){var r=t.previousElementSibling;if(t&&r){var a=[t];Array.from(n.cloneContents().children).forEach(function(w,h){w.nodeType!==3&&t&&w.textContent.trim()!==""&&t.getAttribute("data-node-id")===w.getAttribute("data-node-id")&&(h!==0&&a.push(t),t=t.nextElementSibling)}),e[e.currentMode].element.querySelectorAll("wbr").forEach(function(w){w.remove()}),n.insertNode(document.createElement("wbr"));var o=r.parentElement,l="";a.forEach(function(w){var h=w.getAttribute("data-marker");h.length!==1&&(h="1".concat(h.slice(-1))),l+='<li data-node-id="'.concat(w.getAttribute("data-node-id"),'" data-marker="').concat(h,'">').concat(w.innerHTML,"</li>"),w.remove()}),r.insertAdjacentHTML("beforeend","<".concat(o.tagName,' data-block="0">').concat(l,"</").concat(o.tagName,">")),e.currentMode==="wysiwyg"?o.outerHTML=e.lute.SpinVditorDOM(o.outerHTML):o.outerHTML=e.lute.SpinVditorIRDOM(o.outerHTML),(0,O.ib)(e[e.currentMode].element,n);var m=(0,s.O9)(n.startContainer);m&&m.querySelectorAll(".vditor-".concat(e.currentMode,"__preview[data-render='2']")).forEach(function(w){X(w,e),e.currentMode==="wysiwyg"&&w.previousElementSibling.setAttribute("style","display:none")}),le(e),rt(e)}else e[e.currentMode].element.focus()},qt=function(e,t,n,r){var a=(0,s.lG)(t.parentElement,"LI");if(a){e[e.currentMode].element.querySelectorAll("wbr").forEach(function(P){P.remove()}),n.insertNode(document.createElement("wbr"));var o=t.parentElement,l=o.cloneNode(),m=[t];Array.from(n.cloneContents().children).forEach(function(P,K){P.nodeType!==3&&t&&P.textContent.trim()!==""&&t.getAttribute("data-node-id")===P.getAttribute("data-node-id")&&(K!==0&&m.push(t),t=t.nextElementSibling)});var w=!1,h="";o.querySelectorAll("li").forEach(function(P){w&&(h+=P.outerHTML,!P.nextElementSibling&&!P.previousElementSibling?P.parentElement.remove():P.remove()),P.isSameNode(m[m.length-1])&&(w=!0)}),m.reverse().forEach(function(P){a.insertAdjacentElement("afterend",P)}),h&&(l.innerHTML=h,m[0].insertAdjacentElement("beforeend",l)),e.currentMode==="wysiwyg"?r.outerHTML=e.lute.SpinVditorDOM(r.outerHTML):r.outerHTML=e.lute.SpinVditorIRDOM(r.outerHTML),(0,O.ib)(e[e.currentMode].element,n);var A=(0,s.O9)(n.startContainer);A&&A.querySelectorAll(".vditor-".concat(e.currentMode,"__preview[data-render='2']")).forEach(function(P){X(P,e),e.currentMode==="wysiwyg"&&P.previousElementSibling.setAttribute("style","display:none")}),le(e),rt(e)}else e[e.currentMode].element.focus()},Ct=function(e,t){for(var n=getSelection().getRangeAt(0).startContainer.parentElement,r=e.rows[0].cells.length,a=e.rows.length,o=0,l=0;l<a;l++)for(var m=0;m<r;m++)if(e.rows[l].cells[m].isSameNode(n)){o=m;break}for(var w=0;w<a;w++)e.rows[w].cells[o].setAttribute("align",t)},Kt=function(e){var t=e.trimRight().split(`
`).pop();return t===""?!1:(t.replace(/ |-/g,"")===""||t.replace(/ |_/g,"")===""||t.replace(/ |\*/g,"")==="")&&t.replace(/ /g,"").length>2?!(t.indexOf("-")>-1&&t.trimLeft().indexOf(" ")===-1&&e.trimRight().split(`
`).length>1||t.indexOf("    ")===0||t.indexOf("	")===0):!1},Gt=function(e){var t=e.trimRight().split(`
`);return e=t.pop(),e.indexOf("    ")===0||e.indexOf("	")===0||(e=e.trimLeft(),e===""||t.length===0)?!1:e.replace(/-/g,"")===""||e.replace(/=/g,"")===""},le=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),e.currentMode==="wysiwyg"?we(e,t):e.currentMode==="ir"?Ze(e,t):e.currentMode==="sv"&&Ae(e,t)},bn=function(e,t,n,r){var a,o=e.startContainer,l=(0,s.lG)(o,"LI");if(l){if(!(0,c.yl)(r)&&!r.altKey&&r.key==="Enter"&&!r.shiftKey&&n&&l.contains(n)&&n.nextElementSibling)return l&&!l.textContent.endsWith(`
`)&&l.insertAdjacentText("beforeend",`
`),e.insertNode(document.createTextNode(`

`)),e.collapse(!1),le(t),r.preventDefault(),!0;if(!(0,c.yl)(r)&&!r.shiftKey&&!r.altKey&&r.key==="Backspace"&&!l.previousElementSibling&&e.toString()===""&&(0,O.im)(l,t[t.currentMode].element,e).start===0)return l.nextElementSibling?(l.parentElement.insertAdjacentHTML("beforebegin",'<p data-block="0"><wbr>'.concat(l.innerHTML,"</p>")),l.remove()):l.parentElement.outerHTML='<p data-block="0"><wbr>'.concat(l.innerHTML,"</p>"),(0,O.ib)(t[t.currentMode].element,e),le(t),r.preventDefault(),!0;if(!(0,c.yl)(r)&&!r.shiftKey&&!r.altKey&&r.key==="Backspace"&&l.textContent.trim().replace(M.g.ZWSP,"")===""&&e.toString()===""&&((a=l.previousElementSibling)===null||a===void 0?void 0:a.tagName)==="LI")return l.previousElementSibling.insertAdjacentText("beforeend",`

`),e.selectNodeContents(l.previousElementSibling),e.collapse(!1),l.remove(),(0,O.ib)(t[t.currentMode].element,e),le(t),r.preventDefault(),!0;if(!(0,c.yl)(r)&&!r.altKey&&r.key==="Tab"){var m=!1;if((e.startOffset===0&&(o.nodeType===3&&!o.previousSibling||o.nodeType!==3&&o.nodeName==="LI")||l.classList.contains("vditor-task")&&e.startOffset===1&&o.previousSibling.nodeType!==3&&o.previousSibling.tagName==="INPUT")&&(m=!0),m||e.toString()!=="")return r.shiftKey?qt(t,l,e,l.parentElement):gn(t,l,e),r.preventDefault(),!0}}return!1},$t=function(e,t,n){if(e.options.tab&&n.key==="Tab")return n.shiftKey||(t.toString()===""?(t.insertNode(document.createTextNode(e.options.tab)),t.collapse(!1)):(t.extractContents(),t.insertNode(document.createTextNode(e.options.tab)),t.collapse(!1))),(0,O.Hc)(t),le(e),n.preventDefault(),!0},wn=function(e,t,n,r){if(n){if(!(0,c.yl)(e)&&!e.altKey&&e.key==="Enter"){var a=String.raw(Hn||(Hn=Er(["",""],["",""])),n.textContent).replace(/\\\|/g,"").trim(),o=a.split("|");if(a.startsWith("|")&&a.endsWith("|")&&o.length>3){var l=o.map(function(){return"---"}).join("|");return l=n.textContent+`
`+l.substring(3,l.length-3)+`
|<wbr>`,n.outerHTML=t.lute.SpinVditorDOM(l),(0,O.ib)(t[t.currentMode].element,r),le(t),Te(t),e.preventDefault(),!0}if(Kt(n.innerHTML)&&n.previousElementSibling){var m="",w=n.innerHTML.trimRight().split(`
`);return w.length>1&&(w.pop(),m='<p data-block="0">'.concat(w.join(`
`),"</p>")),n.insertAdjacentHTML("afterend","".concat(m,`<hr data-block="0"><p data-block="0"><wbr>
</p>`)),n.remove(),(0,O.ib)(t[t.currentMode].element,r),le(t),Te(t),e.preventDefault(),!0}if(Gt(n.innerHTML))return t.currentMode==="wysiwyg"?n.outerHTML=t.lute.SpinVditorDOM(n.innerHTML+`<p data-block="0"><wbr>
</p>`):n.outerHTML=t.lute.SpinVditorIRDOM(n.innerHTML+`<p data-block="0"><wbr>
</p>`),(0,O.ib)(t[t.currentMode].element,r),le(t),Te(t),e.preventDefault(),!0}if(r.collapsed&&n.previousElementSibling&&e.key==="Backspace"&&!(0,c.yl)(e)&&!e.altKey&&!e.shiftKey&&n.textContent.trimRight().split(`
`).length>1&&(0,O.im)(n,t[t.currentMode].element,r).start===0){var h=(0,s.DX)(n.previousElementSibling);return h.textContent.endsWith(`
`)||(h.textContent=h.textContent+`
`),h.parentElement.insertAdjacentHTML("beforeend","<wbr>".concat(n.innerHTML)),n.remove(),(0,O.ib)(t[t.currentMode].element,r),!1}return!1}},yn=function(e,t,n){for(var r="",a=0;a<n.parentElement.childElementCount;a++)r+='<td align="'.concat(n.parentElement.children[a].getAttribute("align"),'"> </td>');n.tagName==="TH"?n.parentElement.parentElement.insertAdjacentHTML("afterend","<tbody><tr>".concat(r,"</tr></tbody>")):n.parentElement.insertAdjacentHTML("afterend","<tr>".concat(r,"</tr>")),le(e)},vn=function(e,t,n){for(var r="",a=0;a<n.parentElement.childElementCount;a++)n.tagName==="TH"?r+='<th align="'.concat(n.parentElement.children[a].getAttribute("align"),'"> </th>'):r+='<td align="'.concat(n.parentElement.children[a].getAttribute("align"),'"> </td>');if(n.tagName==="TH"){n.parentElement.parentElement.insertAdjacentHTML("beforebegin","<thead><tr>".concat(r,"</tr></thead>")),t.insertNode(document.createElement("wbr"));var o=n.parentElement.innerHTML.replace(/<th>/g,"<td>").replace(/<\/th>/g,"</td>");n.parentElement.parentElement.nextElementSibling.insertAdjacentHTML("afterbegin",o),n.parentElement.parentElement.remove(),(0,O.ib)(e.ir.element,t)}else n.parentElement.insertAdjacentHTML("beforebegin","<tr>".concat(r,"</tr>"));le(e)},Mt=function(e,t,n,r){r===void 0&&(r="afterend");for(var a=0,o=n.previousElementSibling;o;)a++,o=o.previousElementSibling;for(var l=0;l<t.rows.length;l++)l===0?t.rows[l].cells[a].insertAdjacentHTML(r,"<th> </th>"):t.rows[l].cells[a].insertAdjacentHTML(r,"<td> </td>");le(e)},_n=function(e,t,n){if(n.tagName==="TD"){var r=n.parentElement.parentElement;n.parentElement.previousElementSibling?t.selectNodeContents(n.parentElement.previousElementSibling.lastElementChild):t.selectNodeContents(r.previousElementSibling.lastElementChild.lastElementChild),r.childElementCount===1?r.remove():n.parentElement.remove(),t.collapse(!1),(0,O.Hc)(t),le(e)}},kn=function(e,t,n,r){for(var a=0,o=r.previousElementSibling;o;)a++,o=o.previousElementSibling;(r.previousElementSibling||r.nextElementSibling)&&(t.selectNodeContents(r.previousElementSibling||r.nextElementSibling),t.collapse(!0));for(var l=0;l<n.rows.length;l++){var m=n.rows[l].cells;if(m.length===1){n.remove(),rt(e);break}m[a].remove()}(0,O.Hc)(t),le(e)},En=function(e,t,n){var r=n.startContainer,a=(0,s.lG)(r,"TD")||(0,s.lG)(r,"TH");if(a){if(!(0,c.yl)(t)&&!t.altKey&&t.key==="Enter"){(!a.lastElementChild||a.lastElementChild&&(!a.lastElementChild.isSameNode(a.lastChild)||a.lastElementChild.tagName!=="BR"))&&a.insertAdjacentHTML("beforeend","<br>");var o=document.createElement("br");return n.insertNode(o),n.setStartAfter(o),le(e),Te(e),t.preventDefault(),!0}if(t.key==="Tab"){if(t.shiftKey)return mn(a,n),t.preventDefault(),!0;var l=a.nextElementSibling;return l||(a.parentElement.nextElementSibling?l=a.parentElement.nextElementSibling.firstElementChild:a.parentElement.parentElement.tagName==="THEAD"&&a.parentElement.parentElement.nextElementSibling?l=a.parentElement.parentElement.nextElementSibling.firstElementChild.firstElementChild:l=null),l&&(n.selectNodeContents(l),(0,O.Hc)(n)),t.preventDefault(),!0}var m=a.parentElement.parentElement.parentElement;if(t.key==="ArrowUp"){if(t.preventDefault(),a.tagName==="TH")return m.previousElementSibling?(n.selectNodeContents(m.previousElementSibling),n.collapse(!1),(0,O.Hc)(n)):ft(e,"beforebegin"),!0;for(var w=0,h=a.parentElement;w<h.cells.length&&!h.cells[w].isSameNode(a);w++);var A=h.previousElementSibling;return A||(A=h.parentElement.previousElementSibling.firstChild),n.selectNodeContents(A.cells[w]),n.collapse(!1),(0,O.Hc)(n),!0}if(t.key==="ArrowDown"){t.preventDefault();var h=a.parentElement;if(!h.nextElementSibling&&a.tagName==="TD")return m.nextElementSibling?(n.selectNodeContents(m.nextElementSibling),n.collapse(!0),(0,O.Hc)(n)):ft(e,"afterend"),!0;for(var w=0;w<h.cells.length&&!h.cells[w].isSameNode(a);w++);var l=h.nextElementSibling;return l||(l=h.parentElement.nextElementSibling.firstChild),n.selectNodeContents(l.cells[w]),n.collapse(!0),(0,O.Hc)(n),!0}if(e.currentMode==="wysiwyg"&&!(0,c.yl)(t)&&t.key==="Enter"&&!t.shiftKey&&t.altKey){var P=e.wysiwyg.popover.querySelector(".vditor-input");return P.focus(),P.select(),t.preventDefault(),!0}if(!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&t.key==="Backspace"&&n.startOffset===0&&n.toString()===""){var K=mn(a,n,!1);return!K&&m&&(m.textContent.trim()===""?(m.outerHTML=`<p data-block="0"><wbr>
</p>`,(0,O.ib)(e[e.currentMode].element,n)):(n.setStartBefore(m),n.collapse(!0)),le(e)),t.preventDefault(),!0}if(U("⇧⌘F",t))return vn(e,n,a),t.preventDefault(),!0;if(U("⌘=",t))return yn(e,n,a),t.preventDefault(),!0;if(U("⇧⌘G",t))return Mt(e,m,a,"beforebegin"),t.preventDefault(),!0;if(U("⇧⌘=",t))return Mt(e,m,a),t.preventDefault(),!0;if(U("⌘-",t))return _n(e,n,a),t.preventDefault(),!0;if(U("⇧⌘-",t))return kn(e,n,m,a),t.preventDefault(),!0;if(U("⇧⌘L",t)){if(e.currentMode==="ir")return Ct(m,"left"),le(e),t.preventDefault(),!0;var W=e.wysiwyg.popover.querySelector('[data-type="left"]');if(W)return W.click(),t.preventDefault(),!0}if(U("⇧⌘C",t)){if(e.currentMode==="ir")return Ct(m,"center"),le(e),t.preventDefault(),!0;var W=e.wysiwyg.popover.querySelector('[data-type="center"]');if(W)return W.click(),t.preventDefault(),!0}if(U("⇧⌘R",t)){if(e.currentMode==="ir")return Ct(m,"right"),le(e),t.preventDefault(),!0;var W=e.wysiwyg.popover.querySelector('[data-type="right"]');if(W)return W.click(),t.preventDefault(),!0}}return!1},Sn=function(e,t,n,r){if(n.tagName==="PRE"&&U("⌘A",t))return r.selectNodeContents(n.firstElementChild),t.preventDefault(),!0;if(e.options.tab&&t.key==="Tab"&&!t.shiftKey&&r.toString()==="")return r.insertNode(document.createTextNode(e.options.tab)),r.collapse(!1),le(e),t.preventDefault(),!0;if(t.key==="Backspace"&&!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey){var a=(0,O.im)(n,e[e.currentMode].element,r);if((a.start===0||a.start===1&&n.innerText===`
`)&&r.toString()==="")return n.parentElement.outerHTML='<p data-block="0"><wbr>'.concat(n.firstElementChild.innerHTML,"</p>"),(0,O.ib)(e[e.currentMode].element,r),le(e),t.preventDefault(),!0}return!(0,c.yl)(t)&&!t.altKey&&t.key==="Enter"?(n.firstElementChild.textContent.endsWith(`
`)||n.firstElementChild.insertAdjacentText("beforeend",`
`),r.extractContents(),r.insertNode(document.createTextNode(`
`)),r.collapse(!1),(0,O.Hc)(r),(0,c.vU)()||(e.currentMode==="wysiwyg"?Et(e,r):_e(e,r)),Te(e),t.preventDefault(),!0):!1},Cn=function(e,t,n,r){var a=t.startContainer,o=(0,s.lG)(a,"BLOCKQUOTE");if(o&&t.toString()===""){if(n.key==="Backspace"&&!(0,c.yl)(n)&&!n.shiftKey&&!n.altKey&&(0,O.im)(o,e[e.currentMode].element,t).start===0)return t.insertNode(document.createElement("wbr")),o.outerHTML=o.innerHTML,(0,O.ib)(e[e.currentMode].element,t),le(e),n.preventDefault(),!0;if(r&&n.key==="Enter"&&!(0,c.yl)(n)&&!n.shiftKey&&!n.altKey&&r.parentElement.tagName==="BLOCKQUOTE"){var l=!1;if(r.innerHTML.replace(M.g.ZWSP,"")===`
`||r.innerHTML.replace(M.g.ZWSP,"")===""?(l=!0,r.remove()):r.innerHTML.endsWith(`

`)&&(0,O.im)(r,e[e.currentMode].element,t).start===r.textContent.length-1&&(r.innerHTML=r.innerHTML.substr(0,r.innerHTML.length-2),l=!0),l)return o.insertAdjacentHTML("afterend",'<p data-block="0">'.concat(M.g.ZWSP,`<wbr>
</p>`)),(0,O.ib)(e[e.currentMode].element,t),le(e),n.preventDefault(),!0}var m=(0,s.F9)(a);if(e.currentMode==="wysiwyg"&&m&&U("⇧⌘;",n))return t.insertNode(document.createElement("wbr")),m.outerHTML='<blockquote data-block="0">'.concat(m.outerHTML,"</blockquote>"),(0,O.ib)(e.wysiwyg.element,t),we(e),n.preventDefault(),!0;if(pt(e,n,t,o,o)||at(e,n,t,o,o))return!0}return!1},Mn=function(e,t,n){var r=t.startContainer,a=(0,s.lG)(r,"LI");if(a&&a.classList.contains("vditor-task")){if(U("⇧⌘J",n)){var o=a.firstElementChild;return o.checked?o.removeAttribute("checked"):o.setAttribute("checked","checked"),le(e),n.preventDefault(),!0}if(n.key==="Backspace"&&!(0,c.yl)(n)&&!n.shiftKey&&!n.altKey&&t.toString()===""&&t.startOffset===1&&(r.nodeType===3&&r.previousSibling&&r.previousSibling.tagName==="INPUT"||r.nodeType!==3)){var l=a.previousElementSibling;if(a.querySelector("input").remove(),l){var m=(0,s.DX)(l);m.parentElement.insertAdjacentHTML("beforeend","<wbr>"+a.innerHTML.trim()),a.remove()}else a.parentElement.insertAdjacentHTML("beforebegin",'<p data-block="0"><wbr>'.concat(a.innerHTML.trim()||`
`,"</p>")),a.nextElementSibling?a.remove():a.parentElement.remove();return(0,O.ib)(e[e.currentMode].element,t),le(e),n.preventDefault(),!0}if(n.key==="Enter"&&!(0,c.yl)(n)&&!n.shiftKey&&!n.altKey){if(a.textContent.trim()==="")if((0,s.fb)(a.parentElement,"vditor-task")){var w=(0,s.O9)(r);w&&qt(e,a,t,w)}else if(a.nextElementSibling){var h="",A="",P=!1;Array.from(a.parentElement.children).forEach(function(ee){a.isSameNode(ee)?P=!0:P?h+=ee.outerHTML:A+=ee.outerHTML});var K=a.parentElement.tagName,W=a.parentElement.tagName==="OL"?"":' data-marker="'.concat(a.parentElement.getAttribute("data-marker"),'"'),V="";A&&(V=a.parentElement.tagName==="UL"?"":' start="1"',A="<".concat(K,' data-tight="true"').concat(W,' data-block="0">').concat(A,"</").concat(K,">")),a.parentElement.outerHTML="".concat(A,`<p data-block="0"><wbr>
</p><`).concat(K,`
 data-tight="true"`).concat(W,' data-block="0"').concat(V,">").concat(h,"</").concat(K,">")}else a.parentElement.insertAdjacentHTML("afterend",`<p data-block="0"><wbr>
</p>`),a.parentElement.querySelectorAll("li").length===1?a.parentElement.remove():a.remove();else r.nodeType!==3&&t.startOffset===0&&r.firstChild.tagName==="INPUT"?t.setStart(r.childNodes[1],1):(t.setEndAfter(a.lastChild),a.insertAdjacentHTML("afterend",'<li class="vditor-task" data-marker="'.concat(a.getAttribute("data-marker"),'"><input type="checkbox"> <wbr></li>')),document.querySelector("wbr").after(t.extractContents()));return(0,O.ib)(e[e.currentMode].element,t),le(e),Te(e),n.preventDefault(),!0}}return!1},Ln=function(e,t,n,r){if(t.startContainer.nodeType!==3){var a=t.startContainer.children[t.startOffset];if(a&&a.tagName==="HR")return t.selectNodeContents(a.previousElementSibling),t.collapse(!1),n.preventDefault(),!0}if(r){var o=r.previousElementSibling;if(o&&(0,O.im)(r,e[e.currentMode].element,t).start===0&&((0,c.vU)()&&o.tagName==="HR"||o.tagName==="TABLE")){if(o.tagName==="TABLE"){var l=o.lastElementChild.lastElementChild.lastElementChild;l.innerHTML=l.innerHTML.trimLeft()+"<wbr>"+r.textContent.trim(),r.remove()}else o.remove();return(0,O.ib)(e[e.currentMode].element,t),le(e),n.preventDefault(),!0}}return!1},Tn=function(e){(0,c.vU)()&&e.startContainer.nodeType!==3&&e.startContainer.tagName==="HR"&&e.setStartBefore(e.startContainer)},An=function(e,t,n){var r,a;if(!(0,c.vU)())return!1;if(e.key==="ArrowUp"&&t&&((r=t.previousElementSibling)===null||r===void 0?void 0:r.tagName)==="TABLE"){var o=t.previousElementSibling;return n.selectNodeContents(o.rows[o.rows.length-1].lastElementChild),n.collapse(!1),e.preventDefault(),!0}return e.key==="ArrowDown"&&t&&((a=t.nextElementSibling)===null||a===void 0?void 0:a.tagName)==="TABLE"?(n.selectNodeContents(t.nextElementSibling.rows[0].cells[0]),n.collapse(!0),e.preventDefault(),!0):!1},Lt=function(e,t,n){return Sr(void 0,void 0,void 0,function(){var r,a,o,l,m,w,h,A,P,K,W,V,ee,F,$,F,oe;return Cr(this,function(ne){switch(ne.label){case 0:return e[e.currentMode].element.getAttribute("contenteditable")!=="true"?[2]:(t.stopPropagation(),t.preventDefault(),"clipboardData"in t?(r=t.clipboardData.getData("text/html"),a=t.clipboardData.getData("text/plain"),o=t.clipboardData.files):(r=t.dataTransfer.getData("text/html"),a=t.dataTransfer.getData("text/plain"),t.dataTransfer.types.includes("Files")&&(o=t.dataTransfer.items)),l={},m=function(ae,pe){if(!pe)return["",Lute.WalkContinue];if(e.options.upload.renderLinkDest)return e.options.upload.renderLinkDest(e,ae,pe);var fe=ae.TokensStr();if(ae.__internal_object__.Parent.Type===34&&fe&&fe.indexOf("file://")===-1&&e.options.upload.linkToImgUrl){var ye=new XMLHttpRequest;ye.open("POST",e.options.upload.linkToImgUrl),e.options.upload.token&&ye.setRequestHeader("X-Upload-Token",e.options.upload.token),e.options.upload.withCredentials&&(ye.withCredentials=!0),dn(e,ye),ye.setRequestHeader("Content-Type","application/json; charset=utf-8"),ye.onreadystatechange=function(){if(ye.readyState===XMLHttpRequest.DONE){if(ye.status===200){var je=ye.responseText;e.options.upload.linkToImgFormat&&(je=e.options.upload.linkToImgFormat(ye.responseText));var Oe=JSON.parse(je);if(Oe.code!==0){e.tip.show(Oe.msg);return}var Re=Oe.data.originalURL;if(e.currentMode==="sv")e.sv.element.querySelectorAll(".vditor-sv__marker--link").forEach(function(Ie){Ie.textContent===Re&&(Ie.textContent=Oe.data.url)});else{var ce=e[e.currentMode].element.querySelector('img[src="'.concat(Re,'"]'));ce.src=Oe.data.url,e.currentMode==="ir"&&(ce.previousElementSibling.previousElementSibling.innerHTML=Oe.data.url)}le(e)}else e.tip.show(ye.responseText);e.options.upload.linkToImgCallback&&e.options.upload.linkToImgCallback(ye.responseText)}},ye.send(JSON.stringify({url:fe}))}return e.currentMode==="ir"?['<span class="vditor-ir__marker vditor-ir__marker--link">'.concat(Lute.EscapeHTMLStr(fe),"</span>"),Lute.WalkContinue]:e.currentMode==="wysiwyg"?["",Lute.WalkContinue]:['<span class="vditor-sv__marker--link">'.concat(Lute.EscapeHTMLStr(fe),"</span>"),Lute.WalkContinue]},(r.replace(/&amp;/g,"&").replace(/<(|\/)(html|body|meta)[^>]*?>/ig,"").trim()==='<a href="'.concat(a,'">').concat(a,"</a>")||r.replace(/&amp;/g,"&").replace(/<(|\/)(html|body|meta)[^>]*?>/ig,"").trim()==='<!--StartFragment--><a href="'.concat(a,'">').concat(a,"</a><!--EndFragment-->"))&&(r=""),w=new DOMParser().parseFromString(r,"text/html"),w.body&&(r=w.body.innerHTML),r=Lute.Sanitize(r),e.wysiwyg.getComments(e),h=e[e.currentMode].element.scrollHeight,A=se(r,a,e.currentMode),P=e.currentMode==="sv"?(0,s.a1)(t.target,"data-type","code-block"):(0,s.lG)(t.target,"CODE"),P?(e.currentMode==="sv"?document.execCommand("insertHTML",!1,a.replace(/&/g,"&amp;").replace(/</g,"&lt;")):(K=(0,O.im)(t.target,e[e.currentMode].element),P.parentElement.tagName!=="PRE"&&(a+=M.g.ZWSP),P.textContent=P.textContent.substring(0,K.start)+a+P.textContent.substring(K.end),(0,O.$j)(K.start+a.length,K.start+a.length,P.parentElement),!((oe=P.parentElement)===null||oe===void 0)&&oe.nextElementSibling.classList.contains("vditor-".concat(e.currentMode,"__preview"))&&(P.parentElement.nextElementSibling.innerHTML=P.outerHTML,X(P.parentElement.nextElementSibling,e))),[3,8]):[3,1]);case 1:return A?(n.pasteCode(A),[3,8]):[3,2];case 2:return r.trim()===""?[3,3]:(W=document.createElement("div"),W.innerHTML=r,W.querySelectorAll("[style]").forEach(function(ae){ae.removeAttribute("style")}),W.querySelectorAll(".vditor-copy").forEach(function(ae){ae.remove()}),e.currentMode==="ir"?(l.HTML2VditorIRDOM={renderLinkDest:m},e.lute.SetJSRenderers({renderers:l}),(0,O.oC)(e.lute.HTML2VditorIRDOM(W.innerHTML),e)):e.currentMode==="wysiwyg"?(l.HTML2VditorDOM={renderLinkDest:m},e.lute.SetJSRenderers({renderers:l}),(0,O.oC)(e.lute.HTML2VditorDOM(W.innerHTML),e)):(l.Md2VditorSVDOM={renderLinkDest:m},e.lute.SetJSRenderers({renderers:l}),Wt(e,e.lute.HTML2Md(W.innerHTML).trimRight())),e.outline.render(e),[3,8]);case 3:return o.length>0?e.options.upload.url||e.options.upload.handler?[4,Ft(e,o)]:[3,5]:[3,7];case 4:return ne.sent(),[3,6];case 5:V=new FileReader,"clipboardData"in t?(o=t.clipboardData.files,ee=o[0]):t.dataTransfer.types.includes("Files")&&(o=t.dataTransfer.items,ee=o[0].getAsFile()),ee&&ee.type.startsWith("image")&&(V.readAsDataURL(ee),V.onload=function(){var ae="";e.currentMode==="wysiwyg"?ae+='<img alt="'.concat(ee.name,'" src="').concat(V.result.toString(),`">
`):ae+="![".concat(ee.name,"](").concat(V.result.toString(),`)
`),document.execCommand("insertHTML",!1,ae)}),ne.label=6;case 6:return[3,8];case 7:a.trim()!==""&&o.length===0&&(F=(0,O.zh)(e),F.toString()!==""&&e.lute.IsValidLinkDest(a)&&(a="[".concat(F.toString(),"](").concat(a,")")),e.currentMode==="ir"?(l.Md2VditorIRDOM={renderLinkDest:m},e.lute.SetJSRenderers({renderers:l}),(0,O.oC)(e.lute.Md2VditorIRDOM(a),e)):e.currentMode==="wysiwyg"?(l.Md2VditorDOM={renderLinkDest:m},e.lute.SetJSRenderers({renderers:l}),(0,O.oC)(e.lute.Md2VditorDOM(a),e)):(l.Md2VditorSVDOM={renderLinkDest:m},e.lute.SetJSRenderers({renderers:l}),Wt(e,a)),e.outline.render(e)),ne.label=8;case 8:return e.currentMode!=="sv"&&($=(0,s.F9)((0,O.zh)(e).startContainer),$&&(F=(0,O.zh)(e),e[e.currentMode].element.querySelectorAll("wbr").forEach(function(ae){ae.remove()}),F.insertNode(document.createElement("wbr")),e.currentMode==="wysiwyg"?$.outerHTML=e.lute.SpinVditorDOM($.outerHTML):$.outerHTML=e.lute.SpinVditorIRDOM($.outerHTML),(0,O.ib)(e[e.currentMode].element,F)),e[e.currentMode].element.querySelectorAll(".vditor-".concat(e.currentMode,"__preview[data-render='2']")).forEach(function(ae){X(ae,e)})),e.wysiwyg.triggerRemoveComment(e),le(e),e[e.currentMode].element.scrollHeight-h>Math.min(e[e.currentMode].element.clientHeight,window.innerHeight)/2&&Te(e),[2]}})})},Hn,Dn=function(e){var t,n;e.hint.render(e);var r=(0,O.zh)(e).startContainer,a=(0,s.a1)(r,"data-type","code-block-info");if(a)if(a.textContent.replace(M.g.ZWSP,"")===""&&e.hint.recentLanguage){a.textContent=M.g.ZWSP+e.hint.recentLanguage;var o=(0,O.zh)(e);o.selectNodeContents(a)}else{var l=[],m=a.textContent.substring(0,(0,O.im)(a,e.ir.element).start).replace(M.g.ZWSP,"");(e.options.preview.hljs.langs||M.g.ALIAS_CODE_LANGUAGES.concat(((n=(t=window.hljs)===null||t===void 0?void 0:t.listLanguages())!==null&&n!==void 0?n:[]).sort())).forEach(function(w){w.indexOf(m.toLowerCase())>-1&&l.push({html:w,value:w})}),e.hint.genHTML(l,m,e)}},Ze=function(e,t){t===void 0&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&Dn(e),clearTimeout(e.ir.processTimeoutId),e.ir.processTimeoutId=window.setTimeout(function(){if(!e.ir.composingLock){var n=R(e);typeof e.options.input=="function"&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&(0,c.pK)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),t.enableAddUndoStack&&e.undo.addToUndoStack(e)}},e.options.undoDelay)},ht=function(e,t){var n=(0,O.zh)(e),r=(0,s.F9)(n.startContainer)||n.startContainer;if(r){var a=r.querySelector(".vditor-ir__marker--heading");a?a.innerHTML=t:(r.insertAdjacentText("afterbegin",t),n.selectNodeContents(r),n.collapse(!1)),_e(e,n.cloneRange()),ut(e)}},Tt=function(e,t,n){var r=(0,s.a1)(e.startContainer,"data-type",n);if(r){r.firstElementChild.remove(),r.lastElementChild.remove(),e.insertNode(document.createElement("wbr"));var a=document.createElement("div");a.innerHTML=t.lute.SpinVditorIRDOM(r.outerHTML),r.outerHTML=a.firstElementChild.innerHTML.trim()}},Tr=function(e,t,n,r){var a=(0,O.zh)(e),o=t.getAttribute("data-type"),l=a.startContainer;l.nodeType===3&&(l=l.parentElement);var m=!0;if(t.classList.contains("vditor-menu--current"))if(o==="quote"){var w=(0,s.lG)(l,"BLOCKQUOTE");w&&(a.insertNode(document.createElement("wbr")),w.outerHTML=w.innerHTML.trim()===""?'<p data-block="0">'.concat(w.innerHTML,"</p>"):w.innerHTML)}else if(o==="link"){var h=(0,s.a1)(a.startContainer,"data-type","a");if(h){var A=(0,s.fb)(a.startContainer,"vditor-ir__link");A?(a.insertNode(document.createElement("wbr")),h.outerHTML=A.innerHTML):h.outerHTML=h.querySelector(".vditor-ir__link").innerHTML+"<wbr>"}}else o==="italic"?Tt(a,e,"em"):o==="bold"?Tt(a,e,"strong"):o==="strike"?Tt(a,e,"s"):o==="inline-code"?Tt(a,e,"code"):(o==="check"||o==="list"||o==="ordered-list")&&(St(e,a,o),m=!1,t.classList.remove("vditor-menu--current"));else{e.ir.element.childNodes.length===0&&(e.ir.element.innerHTML='<p data-block="0"><wbr></p>',(0,O.ib)(e.ir.element,a));var P=(0,s.F9)(a.startContainer);if(o==="line"){if(P){var K=`<hr data-block="0"><p data-block="0"><wbr>
</p>`;P.innerHTML.trim()===""?P.outerHTML=K:P.insertAdjacentHTML("afterend",K)}}else if(o==="quote")P&&(a.insertNode(document.createElement("wbr")),P.outerHTML='<blockquote data-block="0">'.concat(P.outerHTML,"</blockquote>"),m=!1,t.classList.add("vditor-menu--current"));else if(o==="link"){var W=void 0;a.toString()===""?W="".concat(n,"<wbr>").concat(r):W="".concat(n).concat(a.toString()).concat(r.replace(")","<wbr>)")),document.execCommand("insertHTML",!1,W),m=!1,t.classList.add("vditor-menu--current")}else if(o==="italic"||o==="bold"||o==="strike"||o==="inline-code"||o==="code"||o==="table"){var W=void 0;a.toString()===""?W="".concat(n,"<wbr>").concat(r):(o==="code"?W="".concat(n,`
`).concat(a.toString(),"<wbr>").concat(r):o==="table"?W="".concat(n).concat(a.toString(),"<wbr>").concat(r):W="".concat(n).concat(a.toString()).concat(r,"<wbr>"),a.deleteContents()),(o==="table"||o==="code")&&(W=`
`+W+`

`);var V=document.createElement("span");V.innerHTML=W,a.insertNode(V),_e(e,a),o==="table"&&(a.selectNodeContents(getSelection().getRangeAt(0).startContainer.parentElement),(0,O.Hc)(a))}else(o==="check"||o==="list"||o==="ordered-list")&&(St(e,a,o,!1),m=!1,_(e.toolbar.elements,["check","list","ordered-list"]),t.classList.add("vditor-menu--current"))}(0,O.ib)(e.ir.element,a),Ze(e),m&&ut(e)},Ar=function(e,t,n,r){function a(o){return o instanceof n?o:new n(function(l){l(o)})}return new(n||(n=Promise))(function(o,l){function m(A){try{h(r.next(A))}catch(P){l(P)}}function w(A){try{h(r.throw(A))}catch(P){l(P)}}function h(A){A.done?o(A.value):a(A.value).then(m,w)}h((r=r.apply(e,t||[])).next())})},Hr=function(e,t){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,a,o,l;return l={next:m(0),throw:m(1),return:m(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function m(h){return function(A){return w([h,A])}}function w(h){if(r)throw new TypeError("Generator is already executing.");for(;l&&(l=0,h[0]&&(n=0)),n;)try{if(r=1,a&&(o=h[0]&2?a.return:h[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,h[1])).done)return o;switch(a=0,o&&(h=[h[0]&2,o.value]),h[0]){case 0:case 1:o=h;break;case 4:return n.label++,{value:h[1],done:!1};case 5:n.label++,a=h[1],h=[0];continue;case 7:h=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(h[0]===6||h[0]===2)){n=0;continue}if(h[0]===3&&(!o||h[1]>o[0]&&h[1]<o[3])){n.label=h[1];break}if(h[0]===6&&n.label<o[1]){n.label=o[1],o=h;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(h);break}o[2]&&n.ops.pop(),n.trys.pop();continue}h=t.call(e,n)}catch(A){h=[6,A],a=0}finally{r=o=0}if(h[0]&5)throw h[1];return{value:h[0]?h[1]:void 0,done:!0}}},Dr=function(){function e(t){var n=this;this.splitChar="",this.lastIndex=-1,this.fillEmoji=function(r,a){n.element.style.display="none";var o=decodeURIComponent(r.getAttribute("data-value")),l=window.getSelection().getRangeAt(0);if(a.currentMode==="ir"){var m=(0,s.a1)(l.startContainer,"data-type","code-block-info");if(m){m.textContent=M.g.ZWSP+o.trimRight(),l.selectNodeContents(m),l.collapse(!1),Ze(a),m.parentElement.querySelectorAll("code").forEach(function(P){P.className="language-"+o.trimRight()}),X(m.parentElement.querySelector(".vditor-ir__preview"),a),n.recentLanguage=o.trimRight();return}}if(a.currentMode==="wysiwyg"&&l.startContainer.nodeType!==3){var w=l.startContainer,h=void 0;if(w.classList.contains("vditor-input")?h=w:h=w.firstElementChild,h&&h.classList.contains("vditor-input")){h.value=o.trimRight(),l.selectNodeContents(h),l.collapse(!1),h.dispatchEvent(new CustomEvent("input",{detail:1})),n.recentLanguage=o.trimRight();return}}if(l.setStart(l.startContainer,n.lastIndex),l.deleteContents(),a.options.hint.parse?a.currentMode==="sv"?(0,O.oC)(a.lute.SpinVditorSVDOM(o),a):a.currentMode==="wysiwyg"?(0,O.oC)(a.lute.SpinVditorDOM(o),a):(0,O.oC)(a.lute.SpinVditorIRDOM(o),a):(0,O.oC)(o,a),n.splitChar===":"&&o.indexOf(":")>-1&&a.currentMode!=="sv"&&l.insertNode(document.createTextNode(" ")),l.collapse(!1),(0,O.Hc)(l),a.currentMode==="wysiwyg"){var A=(0,s.fb)(l.startContainer,"vditor-wysiwyg__block");A&&A.lastElementChild.classList.contains("vditor-wysiwyg__preview")&&(A.lastElementChild.innerHTML=A.firstElementChild.innerHTML,X(A.lastElementChild,a))}else if(a.currentMode==="ir"){var A=(0,s.fb)(l.startContainer,"vditor-ir__marker--pre");A&&A.nextElementSibling.classList.contains("vditor-ir__preview")&&(A.nextElementSibling.innerHTML=A.innerHTML,X(A.nextElementSibling,a))}le(a)},this.timeId=-1,this.element=document.createElement("div"),this.element.className="vditor-hint",this.recentLanguage="",t.push({key:":"})}return e.prototype.render=function(t){var n=this;if(window.getSelection().focusNode){var r,a=getSelection().getRangeAt(0);r=a.startContainer.textContent.substring(0,a.startOffset)||"";var o=this.getKey(r,t.options.hint.extend);if(typeof o=="undefined")this.element.style.display="none",clearTimeout(this.timeId);else if(this.splitChar===":"){var l=o===""?t.options.hint.emoji:t.lute.GetEmojis(),m=[];Object.keys(l).forEach(function(w){w.indexOf(o.toLowerCase())===0&&(l[w].indexOf(".")>-1?m.push({html:'<img src="'.concat(l[w],'" title=":').concat(w,':"/> :').concat(w,":"),value:":".concat(w,":")}):m.push({html:'<span class="vditor-hint__emoji">'.concat(l[w],"</span>").concat(w),value:l[w]}))}),this.genHTML(m,o,t)}else t.options.hint.extend.forEach(function(w){w.key===n.splitChar&&(clearTimeout(n.timeId),n.timeId=window.setTimeout(function(){return Ar(n,void 0,void 0,function(){var h;return Hr(this,function(A){switch(A.label){case 0:return h=this.genHTML,[4,w.hint(o)];case 1:return h.apply(this,[A.sent(),o,t]),[2]}})})},t.options.hint.delay))})}},e.prototype.genHTML=function(t,n,r){var a=this;if(t.length===0){this.element.style.display="none";return}var o=r[r.currentMode].element,l=(0,O.Ny)(o),m=l.left+(r.options.outline.position==="left"?r.outline.element.offsetWidth:0),w=l.top,h="";t.forEach(function(P,K){if(!(K>7)){var W=P.html;if(n!==""){var V=W.lastIndexOf(">")+1,ee=W.substr(V),$=ee.toLowerCase().indexOf(n.toLowerCase());$>-1&&(ee=ee.substring(0,$)+"<b>"+ee.substring($,$+n.length)+"</b>"+ee.substring($+n.length),W=W.substr(0,V)+ee)}h+='<button type="button" data-value="'.concat(encodeURIComponent(P.value),` "
`).concat(K===0?"class='vditor-hint--current'":"","> ").concat(W,"</button>")}}),this.element.innerHTML=h;var A=parseInt(document.defaultView.getComputedStyle(o,null).getPropertyValue("line-height"),10);this.element.style.top="".concat(w+(A||22),"px"),this.element.style.left="".concat(m,"px"),this.element.style.display="block",this.element.style.right="auto",this.element.querySelectorAll("button").forEach(function(P){P.addEventListener("click",function(K){a.fillEmoji(P,r),K.preventDefault()})}),this.element.getBoundingClientRect().bottom>window.innerHeight&&(this.element.style.top="".concat(w-this.element.offsetHeight,"px")),this.element.getBoundingClientRect().right>window.innerWidth&&(this.element.style.left="auto",this.element.style.right="0")},e.prototype.select=function(t,n){if(this.element.querySelectorAll("button").length===0||this.element.style.display==="none")return!1;var r=this.element.querySelector(".vditor-hint--current");if(t.key==="ArrowDown")return t.preventDefault(),t.stopPropagation(),r.removeAttribute("class"),r.nextElementSibling?r.nextElementSibling.className="vditor-hint--current":this.element.children[0].className="vditor-hint--current",!0;if(t.key==="ArrowUp"){if(t.preventDefault(),t.stopPropagation(),r.removeAttribute("class"),r.previousElementSibling)r.previousElementSibling.className="vditor-hint--current";else{var a=this.element.children.length;this.element.children[a-1].className="vditor-hint--current"}return!0}else if(!(0,c.yl)(t)&&!t.shiftKey&&!t.altKey&&t.key==="Enter"&&!t.isComposing)return t.preventDefault(),t.stopPropagation(),this.fillEmoji(r,n),!0;return!1},e.prototype.getKey=function(t,n){var r=this;this.lastIndex=-1,this.splitChar="",n.forEach(function(h){var A=t.lastIndexOf(h.key);r.lastIndex<A&&(r.splitChar=h.key,r.lastIndex=A)});var a;if(this.lastIndex===-1)return a;var o=t.split(this.splitChar),l=o[o.length-1],m=32;if(o.length>1&&l.trim()===l)if(o.length===2&&o[0]===""&&o[1].length<m)a=o[1];else{var w=o[o.length-2].slice(-1);(0,D.X)(w)===" "&&l.length<m&&(a=l)}return a},e}(),Or=function(){function e(t){this.composingLock=!1;var n=document.createElement("div");n.className="vditor-ir",n.innerHTML='<pre class="vditor-reset" placeholder="'.concat(t.options.placeholder,`"
 contenteditable="true" spellcheck="false"></pre>`),this.element=n.firstElementChild,this.bindEvent(t),Rt(t,this.element),on(t,this.element),It(t,this.element),Ut(t,this.element),Vt(t,this.element),xt(t,this.element),Bt(t,this.element,this.copy),zt(t,this.element,this.copy)}return e.prototype.copy=function(t,n){var r=getSelection().getRangeAt(0);if(r.toString()!==""){t.stopPropagation(),t.preventDefault();var a=document.createElement("div");a.appendChild(r.cloneContents()),t.clipboardData.setData("text/plain",n.lute.VditorIRDOM2Md(a.innerHTML).trim()),t.clipboardData.setData("text/html","")}},e.prototype.bindEvent=function(t){var n=this;this.element.addEventListener("paste",function(r){Lt(t,r,{pasteCode:function(a){document.execCommand("insertHTML",!1,a)}})}),this.element.addEventListener("scroll",function(){S(t,["hint"])}),this.element.addEventListener("compositionstart",function(r){n.composingLock=!0}),this.element.addEventListener("compositionend",function(r){(0,c.vU)()||_e(t,getSelection().getRangeAt(0).cloneRange()),n.composingLock=!1}),this.element.addEventListener("input",function(r){if(!(r.inputType==="deleteByDrag"||r.inputType==="insertFromDrop")){if(n.preventInput){n.preventInput=!1,Ze(t,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0});return}n.composingLock||r.data==="‘"||r.data==="“"||r.data==="《"||_e(t,getSelection().getRangeAt(0).cloneRange(),!1,r)}}),this.element.addEventListener("click",function(r){if(r.target.tagName==="INPUT"){r.target.checked?r.target.setAttribute("checked","checked"):r.target.removeAttribute("checked"),n.preventInput=!0,Ze(t);return}var a=(0,O.zh)(t),o=(0,s.fb)(r.target,"vditor-ir__preview");if(o||(o=(0,s.fb)(a.startContainer,"vditor-ir__preview")),o&&(o.previousElementSibling.firstElementChild?a.selectNodeContents(o.previousElementSibling.firstElementChild):a.selectNodeContents(o.previousElementSibling),a.collapse(!0),(0,O.Hc)(a),Te(t)),r.target.tagName==="IMG"){var l=r.target.parentElement.querySelector(".vditor-ir__marker--link");l&&(a.selectNode(l),(0,O.Hc)(a))}var m=(0,s.a1)(r.target,"data-type","a");if(m&&!m.classList.contains("vditor-ir__node--expand")){t.options.link.click?t.options.link.click(m.querySelector(":scope > .vditor-ir__marker--link")):t.options.link.isOpen&&window.open(m.querySelector(":scope > .vditor-ir__marker--link").textContent);return}if(r.target.isEqualNode(n.element)&&n.element.lastElementChild&&a.collapsed){var w=n.element.lastElementChild.getBoundingClientRect();r.y>w.top+w.height&&(n.element.lastElementChild.tagName==="P"&&n.element.lastElementChild.textContent.trim().replace(M.g.ZWSP,"")===""?(a.selectNodeContents(n.element.lastElementChild),a.collapse(!1)):(n.element.insertAdjacentHTML("beforeend",'<p data-block="0">'.concat(M.g.ZWSP,"<wbr></p>")),(0,O.ib)(n.element,a)))}a.toString()===""?G(a,t):setTimeout(function(){G((0,O.zh)(t),t)}),Pe(r,t),ut(t)}),this.element.addEventListener("keyup",function(r){if(!(r.isComposing||(0,c.yl)(r))){if(r.key==="Enter"&&Te(t),ut(t),(r.key==="Backspace"||r.key==="Delete")&&t.ir.element.innerHTML!==""&&t.ir.element.childNodes.length===1&&t.ir.element.firstElementChild&&t.ir.element.firstElementChild.tagName==="P"&&t.ir.element.firstElementChild.childElementCount===0&&(t.ir.element.textContent===""||t.ir.element.textContent===`
`)){t.ir.element.innerHTML="";return}var a=(0,O.zh)(t);r.key==="Backspace"?((0,c.vU)()&&a.startContainer.textContent===`
`&&a.startOffset===1&&(a.startContainer.textContent="",G(a,t)),n.element.querySelectorAll(".language-math").forEach(function(l){var m=l.querySelector("br");m&&m.remove()})):r.key.indexOf("Arrow")>-1?((r.key==="ArrowLeft"||r.key==="ArrowRight")&&Dn(t),G(a,t)):r.keyCode===229&&r.code===""&&r.key==="Unidentified"&&G(a,t);var o=(0,s.fb)(a.startContainer,"vditor-ir__preview");if(o){if(r.key==="ArrowUp"||r.key==="ArrowLeft")return o.previousElementSibling.firstElementChild?a.selectNodeContents(o.previousElementSibling.firstElementChild):a.selectNodeContents(o.previousElementSibling),a.collapse(!1),r.preventDefault(),!0;if(o.tagName==="SPAN"&&(r.key==="ArrowDown"||r.key==="ArrowRight"))return o.parentElement.getAttribute("data-type")==="html-entity"?(o.parentElement.insertAdjacentText("afterend",M.g.ZWSP),a.setStart(o.parentElement.nextSibling,1)):a.selectNodeContents(o.parentElement.lastElementChild),a.collapse(!1),r.preventDefault(),!0}}})},e}(),On=function(e){if(e.currentMode==="sv")return e.lute.Md2HTML(R(e));if(e.currentMode==="wysiwyg")return e.lute.VditorDOM2HTML(e.wysiwyg.element.innerHTML);if(e.currentMode==="ir")return e.lute.VditorIRDOM2HTML(e.ir.element.innerHTML)},Pr=re(214),Pn=re(436),Nr=function(){function e(t){this.element=document.createElement("div"),this.element.className="vditor-outline",this.element.innerHTML='<div class="vditor-outline__title">'.concat(t,`</div>
<div class="vditor-outline__content"></div>`)}return e.prototype.render=function(t){var n="";return t.preview.element.style.display==="block"?n=(0,Pn.k)(t.preview.previewElement,this.element.lastElementChild,t):n=(0,Pn.k)(t[t.currentMode].element,this.element.lastElementChild,t),n},e.prototype.toggle=function(t,n,r){var a;n===void 0&&(n=!0),r===void 0&&(r=!0);var o=(a=t.toolbar.elements.outline)===null||a===void 0?void 0:a.firstElementChild;if(n&&window.innerWidth>=M.g.MOBILE_WIDTH?(this.element.style.display="block",this.render(t),o==null||o.classList.add("vditor-menu--current")):(this.element.style.display="none",o==null||o.classList.remove("vditor-menu--current")),r&&getSelection().rangeCount>0){var l=getSelection().getRangeAt(0);t[t.currentMode].element.contains(l.startContainer)&&(0,O.Hc)(l)}Ve(t)},e}(),jr=re(280),Rr=function(){function e(t){var n=this;this.element=document.createElement("div"),this.element.className="vditor-preview",this.previewElement=document.createElement("div"),this.previewElement.className="vditor-reset",t.options.classes.preview&&this.previewElement.classList.add(t.options.classes.preview),this.previewElement.style.maxWidth=t.options.preview.maxWidth+"px",this.previewElement.addEventListener("copy",function(w){if(w.target.tagName!=="TEXTAREA"){var h=document.createElement("div");h.className="vditor-reset",h.appendChild(getSelection().getRangeAt(0).cloneContents()),n.copyToX(t,h,"default"),w.preventDefault()}}),this.previewElement.addEventListener("click",function(w){var h=(0,s.lG)(w.target,"SPAN");if(h&&(0,s.fb)(h,"vditor-toc")){var A=n.previewElement.querySelector("#"+h.getAttribute("data-target-id"));A&&(n.element.scrollTop=A.offsetTop);return}if(w.target.tagName==="A"){t.options.link.click?t.options.link.click(w.target):t.options.link.isOpen&&window.open(w.target.getAttribute("href")),w.preventDefault();return}w.target.tagName==="IMG"&&(t.options.image.preview?t.options.image.preview(w.target):t.options.image.isPreview&&(0,Y.E)(w.target,t.options.lang,t.options.theme))}),this.element.appendChild(this.previewElement);var r=t.options.preview.actions;if(r.length!==0){var a=document.createElement("div");a.className="vditor-preview__action";for(var o=[],l=0;l<r.length;l++){var m=r[l];if(typeof m=="object"){o.push('<button type="button" data-type="'.concat(m.key,'" class="').concat(m.className,'"').concat(m.tooltip?' aria-label="'.concat(m.tooltip,'"'):"",'">').concat(m.text,"</button>"));continue}switch(m){case"desktop":o.push('<button type="button" class="vditor-preview__action--current" data-type="desktop">Desktop</button>');break;case"tablet":o.push('<button type="button" data-type="tablet">Tablet</button>');break;case"mobile":o.push('<button type="button" data-type="mobile">Mobile/Wechat</button>');break;case"mp-wechat":o.push('<button type="button" data-type="mp-wechat" class="vditor-tooltipped vditor-tooltipped__w" aria-label="复制到公众号"><svg><use xlink:href="#vditor-icon-mp-wechat"></use></svg></button>');break;case"zhihu":o.push('<button type="button" data-type="zhihu" class="vditor-tooltipped vditor-tooltipped__w" aria-label="复制到知乎"><svg><use xlink:href="#vditor-icon-zhihu"></use></svg></button>');break}}a.innerHTML=o.join(""),a.addEventListener((0,c.Le)(),function(w){var h=(0,u.S)(w.target,"BUTTON");if(h){var A=h.getAttribute("data-type"),P=r.find(function(K){return(K==null?void 0:K.key)===A});if(P){P.click(A);return}if(A==="mp-wechat"||A==="zhihu"){n.copyToX(t,n.previewElement.cloneNode(!0),A);return}A==="desktop"?n.previewElement.style.width="auto":A==="tablet"?n.previewElement.style.width="780px":n.previewElement.style.width="360px",n.previewElement.scrollWidth>n.previewElement.parentElement.clientWidth&&(n.previewElement.style.width="auto"),n.render(t),a.querySelectorAll("button").forEach(function(K){K.classList.remove("vditor-preview__action--current")}),h.classList.add("vditor-preview__action--current")}}),this.element.insertBefore(a,this.previewElement)}}return e.prototype.render=function(t,n){var r=this;if(clearTimeout(this.mdTimeoutId),this.element.style.display==="none"){this.element.getAttribute("data-type")==="renderPerformance"&&t.tip.hide();return}if(n){this.previewElement.innerHTML=n;return}if(R(t).replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")===""){this.previewElement.innerHTML="";return}var a=new Date().getTime(),o=R(t);this.mdTimeoutId=window.setTimeout(function(){if(t.options.preview.url){var l=new XMLHttpRequest;l.open("POST",t.options.preview.url),l.setRequestHeader("Content-Type","application/json;charset=UTF-8"),l.onreadystatechange=function(){if(l.readyState===XMLHttpRequest.DONE)if(l.status===200){var w=JSON.parse(l.responseText);if(w.code!==0){t.tip.show(w.msg);return}t.options.preview.transform&&(w.data=t.options.preview.transform(w.data)),r.previewElement.innerHTML=w.data,r.afterRender(t,a)}else{var h=t.lute.Md2HTML(o);t.options.preview.transform&&(h=t.options.preview.transform(h)),r.previewElement.innerHTML=h,r.afterRender(t,a)}},l.send(JSON.stringify({markdownText:o}))}else{var m=t.lute.Md2HTML(o);t.options.preview.transform&&(m=t.options.preview.transform(m)),r.previewElement.innerHTML=m,r.afterRender(t,a)}},t.options.preview.delay)},e.prototype.afterRender=function(t,n){t.options.preview.parse&&t.options.preview.parse(this.element);var r=new Date().getTime()-n;new Date().getTime()-n>2600?(t.tip.show(window.VditorI18n.performanceTip.replace("${x}",r.toString())),t.preview.element.setAttribute("data-type","renderPerformance")):t.preview.element.getAttribute("data-type")==="renderPerformance"&&(t.tip.hide(),t.preview.element.removeAttribute("data-type"));var a=t.preview.element.querySelector(".vditor-comment--focus");a&&a.classList.remove("vditor-comment--focus"),(0,k.O)(t.preview.previewElement,t.options.preview.hljs),(0,x.s)(t.options.preview.hljs,t.preview.previewElement,t.options.cdn),(0,z.i)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,T.K)(t.preview.previewElement,t.options.cdn),(0,J.J)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,H.P)(t.preview.previewElement,t.options.cdn),(0,j.v)(t.preview.previewElement,t.options.cdn),(0,b.p)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,B.P)(t.preview.previewElement,t.options.cdn,t.options.theme),(0,q.B)(t.preview.previewElement,t.options.cdn),(0,v.Q)(t.preview.previewElement,t.options.cdn),t.options.preview.render.media.enable&&(0,jr.Y)(t.preview.previewElement),t.options.customRenders.forEach(function(m){m.render(t.preview.previewElement,t)});var o=t.preview.element,l=t.outline.render(t);l===""&&(l="[ToC]"),o.querySelectorAll('[data-type="toc-block"]').forEach(function(m){m.innerHTML=l,(0,I.H)(m,{cdn:t.options.cdn,math:t.options.preview.math})}),(0,I.H)(t.preview.previewElement,{cdn:t.options.cdn,math:t.options.preview.math})},e.prototype.copyToX=function(t,n,r){r===void 0&&(r="mp-wechat"),r!=="zhihu"?n.querySelectorAll(".katex-html .base").forEach(function(o){o.style.display="initial"}):n.querySelectorAll(".language-math").forEach(function(o){o.outerHTML='<img class="Formula-image" data-eeimg="true" src="//www.zhihu.com/equation?tex=" alt="'.concat(o.getAttribute("data-math"),'\\" style="display: block; margin: 0 auto; max-width: 100%;">')}),n.style.backgroundColor="#fff",n.querySelectorAll("code").forEach(function(o){o.style.backgroundImage="none"}),this.element.append(n);var a=n.ownerDocument.createRange();a.selectNode(n),(0,O.Hc)(a),document.execCommand("copy"),n.remove(),t.tip.show(["zhihu","mp-wechat"].includes(r)?"已复制，可到".concat(r==="zhihu"?"知乎":"微信公众号平台","进行粘贴"):"已复制到剪切板")},e}(),Ir=function(){function e(t){this.element=document.createElement("div"),this.element.className="vditor-resize vditor-resize--".concat(t.options.resize.position),this.element.innerHTML='<div><svg><use xlink:href="#vditor-icon-resize"></use></svg></div>',this.bindEvent(t)}return e.prototype.bindEvent=function(t){var n=this;this.element.addEventListener("mousedown",function(r){var a=document,o=r.clientY,l=t.element.offsetHeight,m=63+t.element.querySelector(".vditor-toolbar").clientHeight;a.ondragstart=function(){return!1},window.captureEvents&&window.captureEvents(),n.element.classList.add("vditor-resize--selected"),a.onmousemove=function(w){t.options.resize.position==="top"?t.element.style.height=Math.max(m,l+(o-w.clientY))+"px":t.element.style.height=Math.max(m,l+(w.clientY-o))+"px",t.options.typewriterMode&&(t.sv.element.style.paddingBottom=t.sv.element.parentElement.offsetHeight/2+"px")},a.onmouseup=function(){t.options.resize.after&&t.options.resize.after(t.element.offsetHeight-l),window.captureEvents&&window.captureEvents(),a.onmousemove=null,a.onmouseup=null,a.ondragstart=null,a.onselectstart=null,a.onselect=null,n.element.classList.remove("vditor-resize--selected")}})},e}(),xr=function(){function e(t){this.composingLock=!1,this.element=document.createElement("pre"),this.element.className="vditor-sv vditor-reset",this.element.setAttribute("placeholder",t.options.placeholder),this.element.setAttribute("contenteditable","true"),this.element.setAttribute("spellcheck","false"),this.bindEvent(t),Rt(t,this.element),It(t,this.element),Ut(t,this.element),Vt(t,this.element),xt(t,this.element),Bt(t,this.element,this.copy),zt(t,this.element,this.copy)}return e.prototype.copy=function(t,n){t.stopPropagation(),t.preventDefault(),t.clipboardData.setData("text/plain",dt(n[n.currentMode].element))},e.prototype.bindEvent=function(t){var n=this;this.element.addEventListener("paste",function(r){Lt(t,r,{pasteCode:function(a){document.execCommand("insertHTML",!1,a)}})}),this.element.addEventListener("scroll",function(){if(t.preview.element.style.display==="block"){var r=n.element.scrollTop,a=n.element.clientHeight,o=n.element.scrollHeight-parseFloat(n.element.style.paddingBottom||"0"),l=t.preview.element;r/a>.5?l.scrollTop=(r+a)*l.scrollHeight/o-a:l.scrollTop=r*l.scrollHeight/o}}),this.element.addEventListener("compositionstart",function(r){n.composingLock=!0}),this.element.addEventListener("compositionend",function(r){(0,c.vU)()||he(t,r),n.composingLock=!1}),this.element.addEventListener("input",function(r){if(!(r.inputType==="deleteByDrag"||r.inputType==="insertFromDrop")&&!(n.composingLock||r.data==="‘"||r.data==="“"||r.data==="《")){if(n.preventInput){n.preventInput=!1,Ae(t,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0});return}he(t,r)}}),this.element.addEventListener("keyup",function(r){if(!(r.isComposing||(0,c.yl)(r))){if((r.key==="Backspace"||r.key==="Delete")&&t.sv.element.innerHTML!==""&&t.sv.element.childNodes.length===1&&t.sv.element.firstElementChild&&t.sv.element.firstElementChild.tagName==="DIV"&&t.sv.element.firstElementChild.childElementCount===2&&(t.sv.element.firstElementChild.textContent===""||t.sv.element.textContent===`
`)){t.sv.element.innerHTML="";return}r.key==="Enter"&&Te(t)}})},e}(),Nn=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-tip"}return e.prototype.show=function(t,n){var r=this;n===void 0&&(n=6e3),this.element.className="vditor-tip vditor-tip--show",n===0?(this.element.innerHTML='<div class="vditor-tip__content">'.concat(t,`
<div class="vditor-tip__close">X</div></div>`),this.element.querySelector(".vditor-tip__close").addEventListener("click",function(){r.hide()})):(this.element.innerHTML='<div class="vditor-tip__content">'.concat(t,"</div>"),setTimeout(function(){r.hide()},n)),this.element.removeAttribute("style"),setTimeout(function(){var a=r.element.getBoundingClientRect();a.top<46&&(r.element.style.position="fixed",r.element.style.top="46px")},150)},e.prototype.hide=function(){this.element.className="vditor-messageElementtip",this.element.innerHTML=""},e}(),Zt=function(e,t){if(t.options.preview.mode!==e){switch(t.options.preview.mode=e,e){case"both":t.sv.element.style.display="block",t.preview.element.style.display="block",t.preview.render(t),g(t.toolbar.elements,["both"]);break;case"editor":t.sv.element.style.display="block",t.preview.element.style.display="none",_(t.toolbar.elements,["both"]);break;default:break}t.devtools&&t.devtools.renderEchart(t)}},Br=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),zr=function(e){Br(t,e);function t(n,r){var a=e.call(this,n,r)||this;return n.options.preview.mode==="both"&&a.element.children[0].classList.add("vditor-menu--current"),a.element.children[0].addEventListener((0,c.Le)(),function(o){var l=a.element.firstElementChild;l.classList.contains(M.g.CLASS_MENU_DISABLED)||(o.preventDefault(),n.currentMode==="sv"&&(n.options.preview.mode==="both"?Zt("editor",n):Zt("both",n)))}),a}return t}(Ce),Ur=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-toolbar__br"}return e}(),jn=re(580),Vr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Wr=function(e){Vr(t,e);function t(n,r){var a=e.call(this,n,r)||this,o=a.element.children[0],l=document.createElement("div");l.className="vditor-hint".concat(r.level===2?"":" vditor-panel--arrow");var m="";return M.g.CODE_THEME.forEach(function(w){m+="<button>".concat(w,"</button>")}),l.innerHTML='<div style="overflow: auto;max-height:'.concat(window.innerHeight/2,'px">').concat(m,"</div>"),l.addEventListener((0,c.Le)(),function(w){w.target.tagName==="BUTTON"&&(S(n,["subToolbar"]),n.options.preview.hljs.style=w.target.textContent,(0,jn.Y)(w.target.textContent,n.options.cdn),w.preventDefault(),w.stopPropagation())}),a.element.appendChild(l),i(n,l,o,r.level),a}return t}(Ce),Fr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),qr=function(e){Fr(t,e);function t(n,r){var a=e.call(this,n,r)||this,o=a.element.children[0],l=document.createElement("div");l.className="vditor-hint".concat(r.level===2?"":" vditor-panel--arrow");var m="";return Object.keys(n.options.preview.theme.list).forEach(function(w){m+='<button data-type="'.concat(w,'">').concat(n.options.preview.theme.list[w],"</button>")}),l.innerHTML='<div style="overflow: auto;max-height:'.concat(window.innerHeight/2,'px">').concat(m,"</div>"),l.addEventListener((0,c.Le)(),function(w){w.target.tagName==="BUTTON"&&(S(n,["subToolbar"]),n.options.preview.theme.current=w.target.getAttribute("data-type"),(0,ie.Z)(n.options.preview.theme.current,n.options.preview.theme.path),w.preventDefault(),w.stopPropagation())}),a.element.appendChild(l),i(n,l,o,r.level),a}return t}(Ce),Kr=function(){function e(t){this.element=document.createElement("span"),this.element.className="vditor-counter vditor-tooltipped vditor-tooltipped__nw",this.render(t,"")}return e.prototype.render=function(t,n){var r=n.endsWith(`
`)?n.length-1:n.length;if(t.options.counter.type==="text"&&t[t.currentMode]){var a=t[t.currentMode].element.cloneNode(!0);a.querySelectorAll(".vditor-wysiwyg__preview").forEach(function(o){o.remove()}),r=a.textContent.length}typeof t.options.counter.max=="number"?(r>t.options.counter.max?this.element.className="vditor-counter vditor-counter--error":this.element.className="vditor-counter",this.element.innerHTML="".concat(r,"/").concat(t.options.counter.max)):this.element.innerHTML="".concat(r),this.element.setAttribute("aria-label",t.options.counter.type),t.options.counter.after&&t.options.counter.after(r,{enable:t.options.counter.enable,max:t.options.counter.max,type:t.options.counter.type})},e}(),Gr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),$r=function(e){Gr(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a.element.children[0].innerHTML=r.icon,a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!o.currentTarget.classList.contains(M.g.CLASS_MENU_DISABLED)&&r.click(o,n)}),a}return t}(Ce),Zr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Jr=function(e){Zr(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a.element.firstElementChild.addEventListener((0,c.Le)(),function(o){var l=a.element.firstElementChild;l.classList.contains(M.g.CLASS_MENU_DISABLED)||(o.preventDefault(),l.classList.contains("vditor-menu--current")?(l.classList.remove("vditor-menu--current"),n.devtools.element.style.display="none",Ve(n)):(l.classList.add("vditor-menu--current"),n.devtools.element.style.display="block",Ve(n),n.devtools.renderEchart(n)))}),a}return t}(Ce),Xr=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-toolbar__divider"}return e}(),Qr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Yr=function(e){Qr(t,e);function t(n,r){var a=e.call(this,n,r)||this,o=document.createElement("div");o.className="vditor-panel vditor-panel--arrow";var l="";return Object.keys(n.options.hint.emoji).forEach(function(m){var w=n.options.hint.emoji[m];w.indexOf(".")>-1?l+='<button data-value=":'.concat(m,': " data-key=":').concat(m,`:"><img
data-value=":`).concat(m,': " data-key=":').concat(m,':" class="vditor-emojis__icon" src="').concat(w,'"/></button>'):l+='<button data-value="'.concat(w,` "
 data-key="`).concat(m,'"><span class="vditor-emojis__icon">').concat(w,"</span></button>")}),o.innerHTML='<div class="vditor-emojis" style="max-height: '.concat(n.options.height==="auto"?"auto":n.options.height-80,'px">').concat(l,`</div><div class="vditor-emojis__tail">
    <span class="vditor-emojis__tip"></span><span>`).concat(n.options.hint.emojiTail||"",`</span>
</div>`),a.element.appendChild(o),i(n,o,a.element.firstElementChild,r.level),a.bindEvent(n),a}return t.prototype.bindEvent=function(n){var r=this;this.element.lastElementChild.addEventListener((0,c.Le)(),function(a){var o=(0,u.S)(a.target,"BUTTON");if(o){a.preventDefault();var l=o.getAttribute("data-value"),m=(0,O.zh)(n),w=l;if(n.currentMode==="wysiwyg"?w=n.lute.SpinVditorDOM(l):n.currentMode==="ir"&&(w=n.lute.SpinVditorIRDOM(l)),l.indexOf(":")>-1&&n.currentMode!=="sv"){var h=document.createElement("div");h.innerHTML=w,w=h.firstElementChild.firstElementChild.outerHTML+" ",(0,O.oC)(w,n)}else m.extractContents(),m.insertNode(document.createTextNode(l)),(0,s.F9)(m.startContainer)||Pt(n,m);m.collapse(!1),(0,O.Hc)(m),r.element.lastElementChild.style.display="none",le(n)}}),this.element.lastElementChild.addEventListener("mouseover",function(a){var o=(0,u.S)(a.target,"BUTTON");o&&(r.element.querySelector(".vditor-emojis__tip").innerHTML=o.getAttribute("data-key"))})},t}(Ce),Rn=function(e,t,n){var r=document.createElement("a");"download"in r?(r.download=n,r.style.display="none",r.href=URL.createObjectURL(new Blob([t])),document.body.appendChild(r),r.click(),r.remove()):e.tip.show(window.VditorI18n.downloadTip,0)},ea=function(e){var t=R(e);Rn(e,t,t.substr(0,10)+".md")},ta=function(e){e.tip.show(window.VditorI18n.generate,3800);var t=document.querySelector("#vditorExportIframe");t.contentDocument.open(),t.contentDocument.write('<link rel="stylesheet" href="'.concat(e.options.cdn,`/dist/index.css"/>
<script src="`).concat(e.options.cdn,`/dist/method.min.js"><\/script>
<div id="preview" style="width: 800px"></div>
<script>
window.addEventListener("message", (e) => {
  if(!e.data) {
    return;
  }
  Vditor.preview(document.getElementById('preview'), e.data, {
    cdn: "`).concat(e.options.cdn,`",
    markdown: {
      theme: `).concat(JSON.stringify(e.options.preview.theme),`
    },
    hljs: {
      style: "`).concat(e.options.preview.hljs.style,`"
    }
  });
  setTimeout(() => {
        window.print();
    }, 3600);
}, false);
<\/script>`)),t.contentDocument.close(),setTimeout(function(){t.contentWindow.postMessage(R(e),"*")},200)},na=function(e){var t=On(e),n='<html><head><link rel="stylesheet" type="text/css" href="'.concat(e.options.cdn,`/dist/index.css"/>
<script src="`).concat(e.options.cdn,"/dist/js/i18n/").concat(e.options.lang,`.js"><\/script>
<script src="`).concat(e.options.cdn,`/dist/method.min.js"><\/script></head>
<body><div class="vditor-reset" id="preview">`).concat(t,`</div>
<script>
    const previewElement = document.getElementById('preview')
    Vditor.setContentTheme('`).concat(e.options.preview.theme.current,"', '").concat(e.options.preview.theme.path,`');
    Vditor.codeRender(previewElement);
    Vditor.highlightRender(`).concat(JSON.stringify(e.options.preview.hljs),", previewElement, '").concat(e.options.cdn,`');
    Vditor.mathRender(previewElement, {
        cdn: '`).concat(e.options.cdn,`',
        math: `).concat(JSON.stringify(e.options.preview.math),`,
    });
    Vditor.mermaidRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.SMILESRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.markmapRender(previewElement, '`).concat(e.options.cdn,`');
    Vditor.flowchartRender(previewElement, '`).concat(e.options.cdn,`');
    Vditor.graphvizRender(previewElement, '`).concat(e.options.cdn,`');
    Vditor.chartRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.mindmapRender(previewElement, '`).concat(e.options.cdn,"', '").concat(e.options.theme,`');
    Vditor.abcRender(previewElement, '`).concat(e.options.cdn,`');
    `).concat(e.options.preview.render.media.enable?"Vditor.mediaRender(previewElement);":"",`
    Vditor.speechRender(previewElement);
<\/script>
<script src="`).concat(e.options.cdn,"/dist/js/icons/").concat(e.options.icon,'.js"><\/script></body></html>');Rn(e,n,t.substr(0,10)+".html")},ra=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),aa=function(e){ra(t,e);function t(n,r){var a=e.call(this,n,r)||this,o=a.element.children[0],l=document.createElement("div");return l.className="vditor-hint".concat(r.level===2?"":" vditor-panel--arrow"),l.innerHTML=`<button data-type="markdown">Markdown</button>
<button data-type="pdf">PDF</button>
<button data-type="html">HTML</button>`,l.addEventListener((0,c.Le)(),function(m){var w=m.target;if(w.tagName==="BUTTON"){switch(w.getAttribute("data-type")){case"markdown":ea(n);break;case"pdf":ta(n);break;case"html":na(n);break;default:break}S(n,["subToolbar"]),m.preventDefault(),m.stopPropagation()}}),a.element.appendChild(l),i(n,l,o,r.level),a}return t}(Ce),ia=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),sa=function(e){ia(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a._bindEvent(n,r),a}return t.prototype._bindEvent=function(n,r){this.element.children[0].addEventListener((0,c.Le)(),function(a){a.preventDefault(),n.element.className.includes("vditor--fullscreen")?(r.level||(this.innerHTML=r.icon),n.element.style.zIndex="",document.body.style.overflow="",n.element.classList.remove("vditor--fullscreen"),Object.keys(n.toolbar.elements).forEach(function(o){var l=n.toolbar.elements[o].firstChild;l&&(l.className=l.className.replace("__s","__n"),n.options.toolbar.forEach(function(m){typeof m!="string"&&m.tipPosition&&m.name===l.dataset.type&&(l.className="vditor-tooltipped vditor-tooltipped__".concat(m.tipPosition))}))}),n.counter&&(n.counter.element.className=n.counter.element.className.replace("__s","__n"))):(r.level||(this.innerHTML='<svg><use xlink:href="#vditor-icon-contract"></use></svg>'),n.element.style.zIndex=n.options.fullscreen.index.toString(),document.body.style.overflow="hidden",n.element.classList.add("vditor--fullscreen"),Object.keys(n.toolbar.elements).forEach(function(o){var l=n.toolbar.elements[o].firstChild;l&&(l.className=l.className.replace("__n","__s"))}),n.counter&&(n.counter.element.className=n.counter.element.className.replace("__n","__s"))),n.devtools&&n.devtools.renderEchart(n),r.click&&r.click(a,n),Ve(n),vt(n)})},t}(Ce),oa=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),ua=function(e){oa(t,e);function t(n,r){var a=e.call(this,n,r)||this,o=document.createElement("div");return o.className="vditor-hint vditor-panel--arrow",o.innerHTML='<button data-tag="h1" data-value="# ">'.concat(window.VditorI18n.heading1," ").concat((0,c.ns)("&lt;⌥⌘1>"),`</button>
<button data-tag="h2" data-value="## ">`).concat(window.VditorI18n.heading2," &lt;").concat((0,c.ns)("⌥⌘2"),`></button>
<button data-tag="h3" data-value="### ">`).concat(window.VditorI18n.heading3," &lt;").concat((0,c.ns)("⌥⌘3"),`></button>
<button data-tag="h4" data-value="#### ">`).concat(window.VditorI18n.heading4," &lt;").concat((0,c.ns)("⌥⌘4"),`></button>
<button data-tag="h5" data-value="##### ">`).concat(window.VditorI18n.heading5," &lt;").concat((0,c.ns)("⌥⌘5"),`></button>
<button data-tag="h6" data-value="###### ">`).concat(window.VditorI18n.heading6," &lt;").concat((0,c.ns)("⌥⌘6"),"></button>"),a.element.appendChild(o),a._bindEvent(n,o),a}return t.prototype._bindEvent=function(n,r){var a=this.element.children[0];a.addEventListener((0,c.Le)(),function(l){l.preventDefault(),clearTimeout(n.wysiwyg.afterRenderTimeoutId),clearTimeout(n.ir.processTimeoutId),clearTimeout(n.sv.processTimeoutId),!a.classList.contains(M.g.CLASS_MENU_DISABLED)&&(a.blur(),a.classList.contains("vditor-menu--current")?(n.currentMode==="wysiwyg"?(Nt(n),we(n)):n.currentMode==="ir"&&ht(n,""),a.classList.remove("vditor-menu--current")):(S(n,["subToolbar"]),r.style.display="block"))});for(var o=0;o<6;o++)r.children.item(o).addEventListener((0,c.Le)(),function(l){l.preventDefault(),n.currentMode==="wysiwyg"?(_t(n,l.target.getAttribute("data-tag")),we(n),a.classList.add("vditor-menu--current")):n.currentMode==="ir"?(ht(n,l.target.getAttribute("data-value")),a.classList.add("vditor-menu--current")):ln(n,l.target.getAttribute("data-value")),r.style.display="none"})},t}(Ce),la=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),ca=function(e){la(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),n.tip.show(`<div style="margin-bottom:14px;font-size: 14px;line-height: 22px;min-width:300px;max-width: 360px;display: flex;">
<div style="margin-top: 14px;flex: 1">
    <div>Markdown 使用指南</div>
    <ul style="list-style: none">
        <li><a href="https://ld246.com/article/1583308420519" target="_blank">语法速查手册</a></li>
        <li><a href="https://ld246.com/article/1583129520165" target="_blank">基础语法</a></li>
        <li><a href="https://ld246.com/article/1583305480675" target="_blank">扩展语法</a></li>
        <li><a href="https://ld246.com/article/1582778815353" target="_blank">键盘快捷键</a></li>
    </ul>
</div>
<div style="margin-top: 14px;flex: 1">
    <div>Vditor 支持</div>
    <ul style="list-style: none">
        <li><a href="https://github.com/Vanessa219/vditor/issues" target="_blank">Issues</a></li>
        <li><a href="https://ld246.com/tag/vditor" target="_blank">官方讨论区</a></li>
        <li><a href="https://ld246.com/article/1549638745630" target="_blank">开发手册</a></li>
        <li><a href="https://ld246.com/guide/markdown" target="_blank">演示地址</a></li>
    </ul>
</div></div>`,0)}),a}return t}(Ce),da=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),fa=function(e){da(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){if(o.preventDefault(),!(a.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)||n.currentMode==="sv")){var l=(0,O.zh)(n),m=(0,s.lG)(l.startContainer,"LI");m&&gn(n,m,l)}}),a}return t}(Ce),pa=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),ha=function(e){pa(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),n.tip.show(`<div style="max-width: 520px; font-size: 14px;line-height: 22px;margin-bottom: 14px;">
<p style="text-align: center;margin: 14px 0">
    <em>下一代的 Markdown 编辑器，为未来而构建</em>
</p>
<div style="display: flex;margin-bottom: 14px;flex-wrap: wrap;align-items: center">
    <img src="https://unpkg.com/vditor/dist/images/logo.png" style="margin: 0 auto;height: 68px"/>
    <div>&nbsp;&nbsp;</div>
    <div style="flex: 1;min-width: 250px">
        Vditor 是一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式。
        它使用 TypeScript 实现，支持原生 JavaScript 以及 Vue、React、Angular 和 Svelte 等框架。
    </div>
</div>
<div style="display: flex;flex-wrap: wrap;">
    <ul style="list-style: none;flex: 1;min-width:148px">
        <li>
        项目地址：<a href="https://b3log.org/vditor" target="_blank">b3log.org/vditor</a>
        </li>
        <li>
        开源协议：MIT
        </li>
    </ul>
    <ul style="list-style: none;margin-right: 18px">
        <li>
        组件版本：Vditor v`.concat(M.H," / Lute v").concat(Lute.Version,`
        </li>
        <li>
        赞助捐赠：<a href="https://ld246.com/sponsor" target="_blank">https://ld246.com/sponsor</a>
        </li>
    </ul>
</div>
</div>`),0)}),a}return t}(Ce),ma=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),ga=function(e){ma(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!(a.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)||n.currentMode==="sv")&&ft(n,"afterend")}),a}return t}(Ce),ba=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),wa=function(e){ba(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!(a.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)||n.currentMode==="sv")&&ft(n,"beforebegin")}),a}return t}(Ce),ya=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),va=function(e){ya(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a.element.children[0].addEventListener((0,c.Le)(),function(o){if(o.preventDefault(),!(a.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)||n.currentMode==="sv")){var l=(0,O.zh)(n),m=(0,s.lG)(l.startContainer,"LI");m&&qt(n,m,l,m.parentElement)}}),a}return t}(Ce),_a=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),ka=function(e){_a(t,e);function t(n,r){var a=e.call(this,n,r)||this;return n.options.outline&&a.element.firstElementChild.classList.add("vditor-menu--current"),a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault();var l=n.toolbar.elements.outline.firstElementChild;l.classList.contains(M.g.CLASS_MENU_DISABLED)||(n.options.outline.enable=!a.element.firstElementChild.classList.contains("vditor-menu--current"),n.outline.toggle(n,n.options.outline.enable))}),a}return t}(Ce),Ea=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Sa=function(e){Ea(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a._bindEvent(n),a}return t.prototype._bindEvent=function(n){var r=this;this.element.children[0].addEventListener((0,c.Le)(),function(a){a.preventDefault();var o=r.element.firstElementChild;if(!o.classList.contains(M.g.CLASS_MENU_DISABLED)){var l=M.g.EDIT_TOOLBARS.concat(["both","edit-mode","devtools"]);o.classList.contains("vditor-menu--current")?(o.classList.remove("vditor-menu--current"),n.currentMode==="sv"?(n.sv.element.style.display="block",n.options.preview.mode==="both"?n.preview.element.style.display="block":n.preview.element.style.display="none"):(n[n.currentMode].element.parentElement.style.display="block",n.preview.element.style.display="none"),y(n.toolbar.elements,l),n.outline.render(n)):(p(n.toolbar.elements,l),n.preview.element.style.display="block",n.currentMode==="sv"?n.sv.element.style.display="none":n[n.currentMode].element.parentElement.style.display="none",n.preview.render(n),o.classList.add("vditor-menu--current"),S(n,["subToolbar","hint","popover"]),setTimeout(function(){n.outline.render(n)},n.options.preview.delay+10)),Ve(n)}})},t}(Ce),Ca=function(){function e(t){this.SAMPLE_RATE=5e3,this.isRecording=!1,this.readyFlag=!1,this.leftChannel=[],this.rightChannel=[],this.recordingLength=0;var n;if(typeof AudioContext!="undefined")n=new AudioContext;else if(webkitAudioContext)n=new webkitAudioContext;else return;this.DEFAULT_SAMPLE_RATE=n.sampleRate;var r=n.createGain(),a=n.createMediaStreamSource(t);a.connect(r),this.recorder=n.createScriptProcessor(2048,2,1),this.recorder.onaudioprocess=null,r.connect(this.recorder),this.recorder.connect(n.destination),this.readyFlag=!0}return e.prototype.cloneChannelData=function(t,n){this.leftChannel.push(new Float32Array(t)),this.rightChannel.push(new Float32Array(n)),this.recordingLength+=2048},e.prototype.startRecordingNewWavFile=function(){this.readyFlag&&(this.isRecording=!0,this.leftChannel.length=this.rightChannel.length=0,this.recordingLength=0)},e.prototype.stopRecording=function(){this.isRecording=!1},e.prototype.buildWavFileBlob=function(){for(var t=this.mergeBuffers(this.leftChannel),n=this.mergeBuffers(this.rightChannel),r=new Float32Array(t.length),a=0;a<t.length;++a)r[a]=.5*(t[a]+n[a]);this.DEFAULT_SAMPLE_RATE>this.SAMPLE_RATE&&(r=this.downSampleBuffer(r,this.SAMPLE_RATE));var o=44+r.length*2,l=new ArrayBuffer(o),m=new DataView(l);this.writeUTFBytes(m,0,"RIFF"),m.setUint32(4,o,!0),this.writeUTFBytes(m,8,"WAVE"),this.writeUTFBytes(m,12,"fmt "),m.setUint32(16,16,!0),m.setUint16(20,1,!0),m.setUint16(22,1,!0),m.setUint32(24,this.SAMPLE_RATE,!0),m.setUint32(28,this.SAMPLE_RATE*2,!0),m.setUint16(32,2,!0),m.setUint16(34,16,!0);var w=r.length*2;this.writeUTFBytes(m,36,"data"),m.setUint32(40,w,!0);for(var h=r.length,A=44,P=1,K=0;K<h;K++)m.setInt16(A,r[K]*(32767*P),!0),A+=2;return new Blob([m],{type:"audio/wav"})},e.prototype.downSampleBuffer=function(t,n){if(n===this.DEFAULT_SAMPLE_RATE||n>this.DEFAULT_SAMPLE_RATE)return t;for(var r=this.DEFAULT_SAMPLE_RATE/n,a=Math.round(t.length/r),o=new Float32Array(a),l=0,m=0;l<o.length;){for(var w=Math.round((l+1)*r),h=0,A=0,P=m;P<w&&P<t.length;P++)h+=t[P],A++;o[l]=h/A,l++,m=w}return o},e.prototype.mergeBuffers=function(t){for(var n=new Float32Array(this.recordingLength),r=0,a=t.length,o=0;o<a;++o){var l=t[o];n.set(l,r),r+=l.length}return n},e.prototype.writeUTFBytes=function(t,n,r){for(var a=r.length,o=0;o<a;o++)t.setUint8(n+o,r.charCodeAt(o))},e}(),Ma=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),La=function(e){Ma(t,e);function t(n,r){var a=e.call(this,n,r)||this;return a._bindEvent(n),a}return t.prototype._bindEvent=function(n){var r=this,a;this.element.children[0].addEventListener((0,c.Le)(),function(o){if(o.preventDefault(),!r.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)){var l=n[n.currentMode].element;if(!a){navigator.mediaDevices.getUserMedia({audio:!0}).then(function(w){a=new Ca(w),a.recorder.onaudioprocess=function(h){if(a.isRecording){var A=h.inputBuffer.getChannelData(0),P=h.inputBuffer.getChannelData(1);a.cloneChannelData(A,P)}},a.startRecordingNewWavFile(),n.tip.show(window.VditorI18n.recording),l.setAttribute("contenteditable","false"),r.element.children[0].classList.add("vditor-menu--current")}).catch(function(){n.tip.show(window.VditorI18n["record-tip"])});return}if(a.isRecording){a.stopRecording(),n.tip.hide();var m=new File([a.buildWavFileBlob()],"record".concat(new Date().getTime(),".wav"),{type:"video/webm"});Ft(n,[m]),r.element.children[0].classList.remove("vditor-menu--current")}else n.tip.show(window.VditorI18n.recording),l.setAttribute("contenteditable","false"),a.startRecordingNewWavFile(),r.element.children[0].classList.add("vditor-menu--current")}})},t}(Ce),Ta=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Aa=function(e){Ta(t,e);function t(n,r){var a=e.call(this,n,r)||this;return p({redo:a.element},["redo"]),a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!a.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)&&n.undo.redo(n)}),a}return t}(Ce),Ha=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Da=function(e){Ha(t,e);function t(n,r){var a=e.call(this,n,r)||this;return p({undo:a.element},["undo"]),a.element.children[0].addEventListener((0,c.Le)(),function(o){o.preventDefault(),!a.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)&&n.undo.undo(n)}),a}return t}(Ce),Oa=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Pa=function(e){Oa(t,e);function t(n,r){var a=e.call(this,n,r)||this,o='<input type="file"';return n.options.upload.multiple&&(o+=' multiple="multiple"'),n.options.upload.accept&&(o+=' accept="'.concat(n.options.upload.accept,'"')),a.element.children[0].innerHTML="".concat(r.icon||'<svg><use xlink:href="#vditor-icon-upload"></use></svg>').concat(o,">"),a._bindEvent(n),a}return t.prototype._bindEvent=function(n){var r=this;this.element.children[0].addEventListener((0,c.Le)(),function(a){if(r.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)){a.stopPropagation(),a.preventDefault();return}}),this.element.querySelector("input").addEventListener("change",function(a){if(r.element.firstElementChild.classList.contains(M.g.CLASS_MENU_DISABLED)){a.stopPropagation(),a.preventDefault();return}a.target.files.length!==0&&Ft(n,a.target.files,a.target)})},t}(Ce),Na=function(){function e(t){var n=this,r=t.options;this.elements={},this.element=document.createElement("div"),this.element.className="vditor-toolbar",r.toolbar.forEach(function(a,o){var l=n.genItem(t,a,o);if(n.element.appendChild(l),a.toolbar){var m=document.createElement("div");m.className="vditor-hint vditor-panel--arrow",m.addEventListener((0,c.Le)(),function(w){m.style.display="none"}),a.toolbar.forEach(function(w,h){w.level=2,m.appendChild(n.genItem(t,w,o+h))}),l.appendChild(m),i(t,m,l.children[0],2)}}),t.options.toolbarConfig.hide&&this.element.classList.add("vditor-toolbar--hide"),t.options.toolbarConfig.pin&&this.element.classList.add("vditor-toolbar--pin"),t.options.counter.enable&&(t.counter=new Kr(t),this.element.appendChild(t.counter.element))}return e.prototype.updateConfig=function(t,n){t.options.toolbarConfig=Object.assign({hide:!1,pin:!1},n),t.options.toolbarConfig.hide?this.element.classList.add("vditor-toolbar--hide"):this.element.classList.remove("vditor-toolbar--hide"),t.options.toolbarConfig.pin?this.element.classList.add("vditor-toolbar--pin"):this.element.classList.remove("vditor-toolbar--pin")},e.prototype.genItem=function(t,n,r){var a;switch(n.name){case"bold":case"italic":case"more":case"strike":case"line":case"quote":case"list":case"ordered-list":case"check":case"code":case"inline-code":case"link":case"table":a=new Ce(t,n);break;case"emoji":a=new Yr(t,n);break;case"headings":a=new ua(t,n);break;case"|":a=new Xr;break;case"br":a=new Ur;break;case"undo":a=new Da(t,n);break;case"redo":a=new Aa(t,n);break;case"help":a=new ca(t,n);break;case"both":a=new zr(t,n);break;case"preview":a=new Sa(t,n);break;case"fullscreen":a=new sa(t,n);break;case"upload":a=new Pa(t,n);break;case"record":a=new La(t,n);break;case"info":a=new ha(t,n);break;case"edit-mode":a=new mr(t,n);break;case"devtools":a=new Jr(t,n);break;case"outdent":a=new va(t,n);break;case"indent":a=new fa(t,n);break;case"outline":a=new ka(t,n);break;case"insert-after":a=new ga(t,n);break;case"insert-before":a=new wa(t,n);break;case"code-theme":a=new Wr(t,n);break;case"content-theme":a=new qr(t,n);break;case"export":a=new aa(t,n);break;default:a=new $r(t,n);break}if(a){var o=n.name;return(o==="br"||o==="|")&&(o=o+r),this.elements[o]=a.element,a.element}},e}(),ja=re(173),Ra=function(){function e(){this.stackSize=50,this.resetStack(),this.dmp=new ja}return e.prototype.clearStack=function(t){this.resetStack(),this.resetIcon(t)},e.prototype.resetIcon=function(t){t.toolbar&&(this[t.currentMode].undoStack.length>1?y(t.toolbar.elements,["undo"]):p(t.toolbar.elements,["undo"]),this[t.currentMode].redoStack.length!==0?y(t.toolbar.elements,["redo"]):p(t.toolbar.elements,["redo"]))},e.prototype.undo=function(t){if(t[t.currentMode].element.getAttribute("contenteditable")!=="false"&&!(this[t.currentMode].undoStack.length<2)){var n=this[t.currentMode].undoStack.pop();n&&(this[t.currentMode].redoStack.push(n),this.renderDiff(n,t),this[t.currentMode].hasUndo=!0,S(t,["hint"]))}},e.prototype.redo=function(t){if(t[t.currentMode].element.getAttribute("contenteditable")!=="false"){var n=this[t.currentMode].redoStack.pop();n&&(this[t.currentMode].undoStack.push(n),this.renderDiff(n,t,!0))}},e.prototype.recordFirstPosition=function(t,n){if(getSelection().rangeCount!==0&&!(this[t.currentMode].undoStack.length!==1||this[t.currentMode].undoStack[0].length===0||this[t.currentMode].redoStack.length>0)&&!((0,c.vU)()&&n.key==="Backspace")&&!(0,c.G6)()){var r=this.addCaret(t);r.replace("<wbr>","").replace(" vditor-ir__node--expand","")===this[t.currentMode].undoStack[0][0].diffs[0][1].replace("<wbr>","")&&(this[t.currentMode].undoStack[0][0].diffs[0][1]=r,this[t.currentMode].lastText=r)}},e.prototype.addToUndoStack=function(t){var n=this.addCaret(t,!0),r=this.dmp.diff_main(n,this[t.currentMode].lastText,!0),a=this.dmp.patch_make(n,this[t.currentMode].lastText,r);a.length===0&&this[t.currentMode].undoStack.length>0||(this[t.currentMode].lastText=n,this[t.currentMode].undoStack.push(a),this[t.currentMode].undoStack.length>this.stackSize&&this[t.currentMode].undoStack.shift(),this[t.currentMode].hasUndo&&(this[t.currentMode].redoStack=[],this[t.currentMode].hasUndo=!1,p(t.toolbar.elements,["redo"])),this[t.currentMode].undoStack.length>1&&y(t.toolbar.elements,["undo"]))},e.prototype.renderDiff=function(t,n,r){r===void 0&&(r=!1);var a;if(r){var o=this.dmp.patch_deepCopy(t).reverse();o.forEach(function(m){m.diffs.forEach(function(w){w[0]=-w[0]})}),a=this.dmp.patch_apply(o,this[n.currentMode].lastText)[0]}else a=this.dmp.patch_apply(t,this[n.currentMode].lastText)[0];if(this[n.currentMode].lastText=a,n[n.currentMode].element.innerHTML=a,n.currentMode!=="sv"&&(n[n.currentMode].element.querySelectorAll(".vditor-".concat(n.currentMode,"__preview")).forEach(function(m){m.parentElement.querySelector(".language-echarts")&&(n.currentMode==="ir"?m.parentElement.outerHTML=n.lute.SpinVditorIRDOM(m.parentElement.outerHTML):m.parentElement.outerHTML=n.lute.SpinVditorDOM(m.parentElement.outerHTML))}),n[n.currentMode].element.querySelectorAll(".vditor-".concat(n.currentMode,"__preview[data-render='2']")).forEach(function(m){X(m,n)})),n[n.currentMode].element.querySelector("wbr"))(0,O.ib)(n[n.currentMode].element,n[n.currentMode].element.ownerDocument.createRange()),Te(n);else{var l=getSelection().getRangeAt(0);l.setEndBefore(n[n.currentMode].element),l.collapse(!1)}ve(n),le(n,{enableAddUndoStack:!1,enableHint:!1,enableInput:!0}),rt(n),n[n.currentMode].element.querySelectorAll(".vditor-".concat(n.currentMode,"__preview[data-render='2']")).forEach(function(m){X(m,n)}),this[n.currentMode].undoStack.length>1?y(n.toolbar.elements,["undo"]):p(n.toolbar.elements,["undo"]),this[n.currentMode].redoStack.length!==0?y(n.toolbar.elements,["redo"]):p(n.toolbar.elements,["redo"])},e.prototype.resetStack=function(){this.ir={hasUndo:!1,lastText:"",redoStack:[],undoStack:[]},this.sv={hasUndo:!1,lastText:"",redoStack:[],undoStack:[]},this.wysiwyg={hasUndo:!1,lastText:"",redoStack:[],undoStack:[]}},e.prototype.addCaret=function(t,n){n===void 0&&(n=!1);var r;if(getSelection().rangeCount!==0&&!t[t.currentMode].element.querySelector("wbr")){var a=getSelection().getRangeAt(0);if(t[t.currentMode].element.contains(a.startContainer)){r=a.cloneRange();var o=document.createElement("span");o.className="vditor-wbr",a.insertNode(o)}}var l=t[t.currentMode].element.cloneNode(!0);l.querySelectorAll(".vditor-".concat(t.currentMode,"__preview[data-render='1']")).forEach(function(w){w.firstElementChild&&(w.firstElementChild.classList.contains("language-echarts")||w.firstElementChild.classList.contains("language-plantuml")||w.firstElementChild.classList.contains("language-mindmap")?(w.firstElementChild.removeAttribute("_echarts_instance_"),w.firstElementChild.removeAttribute("data-processed"),w.firstElementChild.innerHTML=w.previousElementSibling.firstElementChild.innerHTML,w.setAttribute("data-render","2")):w.firstElementChild.classList.contains("language-math")&&(w.setAttribute("data-render","2"),w.firstElementChild.textContent=w.firstElementChild.getAttribute("data-math"),w.firstElementChild.removeAttribute("data-math")))});var m=l.innerHTML;return t[t.currentMode].element.querySelectorAll(".vditor-wbr").forEach(function(w){w.remove()}),n&&r&&(0,O.Hc)(r),m.replace('<span class="vditor-wbr"></span>',"<wbr>")},e}(),Ia=re(673),xa=function(){function e(t){this.defaultOptions={rtl:!1,after:void 0,cache:{enable:!0},cdn:M.g.CDN,classes:{preview:""},comment:{enable:!1},counter:{enable:!1,type:"markdown"},customRenders:[],debugger:!1,fullscreen:{index:90},height:"auto",hint:{delay:200,emoji:{"+1":"👍","-1":"👎",confused:"😕",eyes:"👀️",heart:"❤️",rocket:"🚀️",smile:"😄",tada:"🎉️"},emojiPath:"".concat(M.g.CDN,"/dist/images/emoji"),extend:[],parse:!0},icon:"ant",lang:"zh_CN",mode:"ir",outline:{enable:!1,position:"left"},placeholder:"",preview:{actions:["desktop","tablet","mobile","mp-wechat","zhihu"],delay:1e3,hljs:M.g.HLJS_OPTIONS,markdown:M.g.MARKDOWN_OPTIONS,math:M.g.MATH_OPTIONS,maxWidth:800,mode:"both",theme:M.g.THEME_OPTIONS,render:{media:{enable:!0}}},link:{isOpen:!0},image:{isPreview:!0},resize:{enable:!1,position:"bottom"},theme:"classic",toolbar:["emoji","headings","bold","italic","strike","link","|","list","ordered-list","check","outdent","indent","|","quote","line","code","inline-code","insert-before","insert-after","|","upload","record","table","|","undo","redo","|","fullscreen","edit-mode",{name:"more",toolbar:["both","code-theme","content-theme","export","outline","preview","devtools","info","help"]}],toolbarConfig:{hide:!1,pin:!1},typewriterMode:!1,undoDelay:800,upload:{extraData:{},fieldName:"file[]",filename:function(n){return n.replace(/\W/g,"")},linkToImgUrl:"",max:10*1024*1024,multiple:!0,url:"",withCredentials:!1},value:"",width:"auto"},this.options=t}return e.prototype.merge=function(){var t,n,r,a,o,l,m,w,h;this.options&&(this.options.toolbar?this.options.toolbar=this.mergeToolbar(this.options.toolbar):this.options.toolbar=this.mergeToolbar(this.defaultOptions.toolbar),!((n=(t=this.options.preview)===null||t===void 0?void 0:t.theme)===null||n===void 0)&&n.list&&(this.defaultOptions.preview.theme.list=this.options.preview.theme.list),!((o=(a=(r=this.options.preview)===null||r===void 0?void 0:r.render)===null||a===void 0?void 0:a.media)===null||o===void 0)&&o.enable&&(this.defaultOptions.preview.render.media.enable=this.options.preview.render.media.enable),!((l=this.options.hint)===null||l===void 0)&&l.emoji&&(this.defaultOptions.hint.emoji=this.options.hint.emoji),this.options.comment&&(this.defaultOptions.comment=this.options.comment),this.options.cdn&&(!((w=(m=this.options.preview)===null||m===void 0?void 0:m.theme)===null||w===void 0)&&w.path||(this.defaultOptions.preview.theme.path="".concat(this.options.cdn,"/dist/css/content-theme")),!((h=this.options.hint)===null||h===void 0)&&h.emojiPath||(this.defaultOptions.hint.emojiPath="".concat(this.options.cdn,"/dist/images/emoji"))));var A=(0,Ia.T)(this.defaultOptions,this.options);if(A.cache.enable&&!A.cache.id)throw new Error("need options.cache.id, see https://ld246.com/article/1549638745630#options");return A},e.prototype.mergeToolbar=function(t){var n=this,r=[{icon:'<svg><use xlink:href="#vditor-icon-export"></use></svg>',name:"export",tipPosition:"ne"},{hotkey:"⌘E",icon:'<svg><use xlink:href="#vditor-icon-emoji"></use></svg>',name:"emoji",tipPosition:"ne"},{hotkey:"⌘H",icon:'<svg><use xlink:href="#vditor-icon-headings"></use></svg>',name:"headings",tipPosition:"ne"},{hotkey:"⌘B",icon:'<svg><use xlink:href="#vditor-icon-bold"></use></svg>',name:"bold",prefix:"**",suffix:"**",tipPosition:"ne"},{hotkey:"⌘I",icon:'<svg><use xlink:href="#vditor-icon-italic"></use></svg>',name:"italic",prefix:"*",suffix:"*",tipPosition:"ne"},{hotkey:"⌘D",icon:'<svg><use xlink:href="#vditor-icon-strike"></use></svg>',name:"strike",prefix:"~~",suffix:"~~",tipPosition:"ne"},{hotkey:"⌘K",icon:'<svg><use xlink:href="#vditor-icon-link"></use></svg>',name:"link",prefix:"[",suffix:"](https://)",tipPosition:"n"},{name:"|"},{hotkey:"⌘L",icon:'<svg><use xlink:href="#vditor-icon-list"></use></svg>',name:"list",prefix:"* ",tipPosition:"n"},{hotkey:"⌘O",icon:'<svg><use xlink:href="#vditor-icon-ordered-list"></use></svg>',name:"ordered-list",prefix:"1. ",tipPosition:"n"},{hotkey:"⌘J",icon:'<svg><use xlink:href="#vditor-icon-check"></use></svg>',name:"check",prefix:"* [ ] ",tipPosition:"n"},{hotkey:"⇧⌘I",icon:'<svg><use xlink:href="#vditor-icon-outdent"></use></svg>',name:"outdent",tipPosition:"n"},{hotkey:"⇧⌘O",icon:'<svg><use xlink:href="#vditor-icon-indent"></use></svg>',name:"indent",tipPosition:"n"},{name:"|"},{hotkey:"⌘;",icon:'<svg><use xlink:href="#vditor-icon-quote"></use></svg>',name:"quote",prefix:"> ",tipPosition:"n"},{hotkey:"⇧⌘H",icon:'<svg><use xlink:href="#vditor-icon-line"></use></svg>',name:"line",prefix:"---",tipPosition:"n"},{hotkey:"⌘U",icon:'<svg><use xlink:href="#vditor-icon-code"></use></svg>',name:"code",prefix:"```",suffix:"\n```",tipPosition:"n"},{hotkey:"⌘G",icon:'<svg><use xlink:href="#vditor-icon-inline-code"></use></svg>',name:"inline-code",prefix:"`",suffix:"`",tipPosition:"n"},{hotkey:"⇧⌘B",icon:'<svg><use xlink:href="#vditor-icon-before"></use></svg>',name:"insert-before",tipPosition:"n"},{hotkey:"⇧⌘E",icon:'<svg><use xlink:href="#vditor-icon-after"></use></svg>',name:"insert-after",tipPosition:"n"},{name:"|"},{icon:'<svg><use xlink:href="#vditor-icon-upload"></use></svg>',name:"upload",tipPosition:"n"},{icon:'<svg><use xlink:href="#vditor-icon-record"></use></svg>',name:"record",tipPosition:"n"},{hotkey:"⌘M",icon:'<svg><use xlink:href="#vditor-icon-table"></use></svg>',name:"table",prefix:"| col1",suffix:` | col2 | col3 |
| --- | --- | --- |
|  |  |  |
|  |  |  |`,tipPosition:"n"},{name:"|"},{hotkey:"⌘Z",icon:'<svg><use xlink:href="#vditor-icon-undo"></use></svg>',name:"undo",tipPosition:"nw"},{hotkey:"⌘Y",icon:'<svg><use xlink:href="#vditor-icon-redo"></use></svg>',name:"redo",tipPosition:"nw"},{name:"|"},{icon:'<svg><use xlink:href="#vditor-icon-more"></use></svg>',name:"more",tipPosition:"e"},{hotkey:"⌘'",icon:'<svg><use xlink:href="#vditor-icon-fullscreen"></use></svg>',name:"fullscreen",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-edit"></use></svg>',name:"edit-mode",tipPosition:"nw"},{hotkey:"⌘P",icon:'<svg><use xlink:href="#vditor-icon-both"></use></svg>',name:"both",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-preview"></use></svg>',name:"preview",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-align-center"></use></svg>',name:"outline",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-theme"></use></svg>',name:"content-theme",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-code-theme"></use></svg>',name:"code-theme",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-bug"></use></svg>',name:"devtools",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-info"></use></svg>',name:"info",tipPosition:"nw"},{icon:'<svg><use xlink:href="#vditor-icon-help"></use></svg>',name:"help",tipPosition:"nw"},{name:"br"}],a=[];return t.forEach(function(o){var l=o;r.forEach(function(m){typeof o=="string"&&m.name===o&&(l=m),typeof o=="object"&&m.name===o.name&&(l=Object.assign({},m,o))}),o.toolbar&&(l.toolbar=n.mergeToolbar(o.toolbar)),a.push(l)}),a},e}(),Ba=function(){function e(t){var n=this;this.composingLock=!1,this.commentIds=[];var r=document.createElement("div");r.className="vditor-wysiwyg",r.innerHTML='<pre class="vditor-reset" placeholder="'.concat(t.options.placeholder,`"
 contenteditable="true" spellcheck="false"></pre>
<div class="vditor-panel vditor-panel--none"></div>
<div class="vditor-panel vditor-panel--none">
    <button type="button" aria-label="`).concat(window.VditorI18n.comment,`" class="vditor-icon vditor-tooltipped vditor-tooltipped__n">
        <svg><use xlink:href="#vditor-icon-comment"></use></svg>
    </button>
</div>`),this.element=r.firstElementChild,this.popover=r.firstElementChild.nextElementSibling,this.selectPopover=r.lastElementChild,this.bindEvent(t),Rt(t,this.element),on(t,this.element),It(t,this.element),Ut(t,this.element),Vt(t,this.element),xt(t,this.element),Bt(t,this.element,this.copy),zt(t,this.element,this.copy),t.options.comment.enable&&(this.selectPopover.querySelector("button").onclick=function(){var a=Lute.NewNodeID(),o=getSelection().getRangeAt(0),l=o.cloneRange(),m=o.extractContents(),w,h,A=!1,P=!1;m.childNodes.forEach(function(V,ee){var $=!1;if(V.nodeType===3?$=!0:V.classList.contains("vditor-comment")?V.classList.contains("vditor-comment")&&V.setAttribute("data-cmtids",V.getAttribute("data-cmtids")+" "+a):$=!0,$)if(V.nodeType!==3&&V.getAttribute("data-block")==="0"&&ee===0&&l.startOffset>0)V.innerHTML='<span class="vditor-comment" data-cmtids="'.concat(a,'">').concat(V.innerHTML,"</span>"),w=V;else if(V.nodeType!==3&&V.getAttribute("data-block")==="0"&&ee===m.childNodes.length-1&&l.endOffset<l.endContainer.textContent.length)V.innerHTML='<span class="vditor-comment" data-cmtids="'.concat(a,'">').concat(V.innerHTML,"</span>"),h=V;else if(V.nodeType!==3&&V.getAttribute("data-block")==="0")ee===0?A=!0:ee===m.childNodes.length-1&&(P=!0),V.innerHTML='<span class="vditor-comment" data-cmtids="'.concat(a,'">').concat(V.innerHTML,"</span>");else{var F=document.createElement("span");F.classList.add("vditor-comment"),F.setAttribute("data-cmtids",a),V.parentNode.insertBefore(F,V),F.appendChild(V)}});var K=(0,s.F9)(l.startContainer);K&&(w?(K.insertAdjacentHTML("beforeend",w.innerHTML),w.remove()):K.textContent.trim().replace(M.g.ZWSP,"")===""&&A&&K.remove());var W=(0,s.F9)(l.endContainer);W&&(h?(W.insertAdjacentHTML("afterbegin",h.innerHTML),h.remove()):W.textContent.trim().replace(M.g.ZWSP,"")===""&&P&&W.remove()),o.insertNode(m),t.options.comment.add(a,o.toString(),n.getComments(t,!0)),we(t,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),n.hideComment()})}return e.prototype.getComments=function(t,n){var r=this;if(n===void 0&&(n=!1),t.currentMode==="wysiwyg"&&t.options.comment.enable){this.commentIds=[],this.element.querySelectorAll(".vditor-comment").forEach(function(o){r.commentIds=r.commentIds.concat(o.getAttribute("data-cmtids").split(" "))}),this.commentIds=Array.from(new Set(this.commentIds));var a=[];if(n)return this.commentIds.forEach(function(o){a.push({id:o,top:r.element.querySelector('.vditor-comment[data-cmtids="'.concat(o,'"]')).offsetTop})}),a}else return[]},e.prototype.triggerRemoveComment=function(t){var n=function(o,l){var m=new Set(l);return o.filter(function(w){return!m.has(w)})};if(t.currentMode==="wysiwyg"&&t.options.comment.enable&&t.wysiwyg.commentIds.length>0){var r=JSON.parse(JSON.stringify(this.commentIds));this.getComments(t);var a=n(r,this.commentIds);a.length>0&&t.options.comment.remove(a)}},e.prototype.showComment=function(){var t=(0,O.Ny)(this.element);this.selectPopover.setAttribute("style","left:".concat(t.left,"px;display:block;top:").concat(Math.max(-8,t.top-21),"px"))},e.prototype.hideComment=function(){this.selectPopover.setAttribute("style","display:none")},e.prototype.unbindListener=function(){window.removeEventListener("scroll",this.scrollListener)},e.prototype.copy=function(t,n){var r=getSelection().getRangeAt(0);if(r.toString()!==""){t.stopPropagation(),t.preventDefault();var a=(0,s.lG)(r.startContainer,"CODE"),o=(0,s.lG)(r.endContainer,"CODE");if(a&&o&&o.isSameNode(a)){var l="";a.parentElement.tagName==="PRE"?l=r.toString():l="`"+r.toString()+"`",t.clipboardData.setData("text/plain",l),t.clipboardData.setData("text/html","");return}var m=(0,s.lG)(r.startContainer,"A"),w=(0,s.lG)(r.endContainer,"A");if(m&&w&&w.isSameNode(m)){var h=m.getAttribute("title")||"";h&&(h=' "'.concat(h,'"')),t.clipboardData.setData("text/plain","[".concat(r.toString(),"](").concat(m.getAttribute("href")).concat(h,")")),t.clipboardData.setData("text/html","");return}var A=document.createElement("div");A.appendChild(r.cloneContents()),t.clipboardData.setData("text/plain",n.lute.VditorDOM2Md(A.innerHTML).trim()),t.clipboardData.setData("text/html","")}},e.prototype.bindEvent=function(t){var n=this;this.unbindListener(),window.addEventListener("scroll",this.scrollListener=function(){if(S(t,["hint"]),!(n.popover.style.display!=="block"||n.selectPopover.style.display!=="block")){var r=parseInt(n.popover.getAttribute("data-top"),10);if(t.options.height!=="auto"){if(t.options.toolbarConfig.pin&&t.toolbar.element.getBoundingClientRect().top===0){var a=Math.max(window.scrollY-t.element.offsetTop-8,Math.min(r-t.wysiwyg.element.scrollTop,n.element.clientHeight-21))+"px";n.popover.style.display==="block"&&(n.popover.style.top=a),n.selectPopover.style.display==="block"&&(n.selectPopover.style.top=a)}return}else if(!t.options.toolbarConfig.pin)return;var o=Math.max(r,window.scrollY-t.element.offsetTop-8)+"px";n.popover.style.display==="block"&&(n.popover.style.top=o),n.selectPopover.style.display==="block"&&(n.selectPopover.style.top=o)}}),this.element.addEventListener("scroll",function(){if(S(t,["hint"]),t.options.comment&&t.options.comment.enable&&t.options.comment.scroll&&t.options.comment.scroll(t.wysiwyg.element.scrollTop),n.popover.style.display==="block"){var r=parseInt(n.popover.getAttribute("data-top"),10)-t.wysiwyg.element.scrollTop,a=-8;t.options.toolbarConfig.pin&&t.toolbar.element.getBoundingClientRect().top===0&&(a=window.scrollY-t.element.offsetTop+a);var o=Math.max(a,Math.min(r,n.element.clientHeight-21))+"px";n.popover.style.top=o,n.selectPopover.style.top=o}}),this.element.addEventListener("paste",function(r){Lt(t,r,{pasteCode:function(a){var o=(0,O.zh)(t),l=document.createElement("template");l.innerHTML=a,o.insertNode(l.content.cloneNode(!0));var m=(0,s.a1)(o.startContainer,"data-block","0");m?m.outerHTML=t.lute.SpinVditorDOM(m.outerHTML):t.wysiwyg.element.innerHTML=t.lute.SpinVditorDOM(t.wysiwyg.element.innerHTML),(0,O.ib)(t.wysiwyg.element,o)}})}),this.element.addEventListener("compositionstart",function(){n.composingLock=!0}),this.element.addEventListener("compositionend",function(r){var a=(0,u.W)(getSelection().getRangeAt(0).startContainer);if(a&&a.textContent===""){ve(t);return}(0,c.vU)()||Et(t,getSelection().getRangeAt(0).cloneRange(),r),n.composingLock=!1}),this.element.addEventListener("input",function(r){if(!(r.inputType==="deleteByDrag"||r.inputType==="insertFromDrop")){if(n.preventInput){n.preventInput=!1,we(t);return}if(n.composingLock||r.data==="‘"||r.data==="“"||r.data==="《"){we(t);return}var a=getSelection().getRangeAt(0),o=(0,s.F9)(a.startContainer);if(o||(Pt(t,a),o=(0,s.F9)(a.startContainer)),!!o){for(var l=(0,O.im)(o,t.wysiwyg.element,a).start,m=!0,w=l-1;w>o.textContent.substr(0,l).lastIndexOf(`
`);w--)if(o.textContent.charAt(w)!==" "&&o.textContent.charAt(w)!=="	"){m=!1;break}l===0&&(m=!1);for(var h=!0,w=l-1;w<o.textContent.length;w++)if(o.textContent.charAt(w)!==" "&&o.textContent.charAt(w)!==`
`){h=!1;break}h&&/^#{1,6} $/.test(o.textContent)&&(h=!1);var A=(0,u.W)(getSelection().getRangeAt(0).startContainer);if(A&&A.textContent===""&&(ve(t),A.remove()),m&&o.getAttribute("data-type")!=="code-block"||h||Gt(o.innerHTML)||Kt(o.innerHTML)&&o.previousElementSibling){typeof t.options.input=="function"&&t.options.input(R(t));return}r.inputType==="insertParagraph"&&n.element.innerHTML==="<p><br></p><p><br></p>"&&o.previousElementSibling.remove(),Et(t,a,r)}}}),this.element.addEventListener("click",function(r){if(r.target.tagName==="INPUT"){var a=r.target;a.checked?a.setAttribute("checked","checked"):a.removeAttribute("checked"),n.preventInput=!0,getSelection().rangeCount>0&&(0,O.Hc)(getSelection().getRangeAt(0)),we(t);return}if(r.target.tagName==="IMG"&&!r.target.parentElement.classList.contains("vditor-wysiwyg__preview")){r.target.getAttribute("data-type")==="link-ref"?an(t,r.target):dr(r,t);return}var o=(0,s.lG)(r.target,"A");if(o){t.options.link.click?t.options.link.click(o):t.options.link.isOpen&&window.open(o.getAttribute("href")),r.preventDefault();return}var l=(0,O.zh)(t);if(r.target.isEqualNode(n.element)&&n.element.lastElementChild&&l.collapsed){var m=n.element.lastElementChild.getBoundingClientRect();r.y>m.top+m.height&&(n.element.lastElementChild.tagName==="P"&&n.element.lastElementChild.textContent.trim().replace(M.g.ZWSP,"")===""?(l.selectNodeContents(n.element.lastElementChild),l.collapse(!1)):(n.element.insertAdjacentHTML("beforeend",'<p data-block="0">'.concat(M.g.ZWSP,"<wbr></p>")),(0,O.ib)(n.element,l)))}Ye(t);var w=(0,s.fb)(r.target,"vditor-wysiwyg__preview");w||(w=(0,s.fb)((0,O.zh)(t).startContainer,"vditor-wysiwyg__preview")),w&&lt(w,t),Pe(r,t)}),this.element.addEventListener("keyup",function(r){if(!(r.isComposing||(0,c.yl)(r))){r.key==="Enter"&&Te(t),(r.key==="Backspace"||r.key==="Delete")&&t.wysiwyg.element.innerHTML!==""&&t.wysiwyg.element.childNodes.length===1&&t.wysiwyg.element.firstElementChild&&t.wysiwyg.element.firstElementChild.tagName==="P"&&t.wysiwyg.element.firstElementChild.childElementCount===0&&(t.wysiwyg.element.textContent===""||t.wysiwyg.element.textContent===`
`)&&(t.wysiwyg.element.innerHTML="");var a=(0,O.zh)(t);if(r.key==="Backspace"&&(0,c.vU)()&&a.startContainer.textContent===`
`&&a.startOffset===1&&(a.startContainer.textContent=""),Pt(t,a),Ye(t),!(r.key!=="ArrowDown"&&r.key!=="ArrowRight"&&r.key!=="Backspace"&&r.key!=="ArrowLeft"&&r.key!=="ArrowUp")){(r.key==="ArrowLeft"||r.key==="ArrowRight")&&t.hint.render(t);var o=(0,s.fb)(a.startContainer,"vditor-wysiwyg__preview");if(!o&&a.startContainer.nodeType!==3&&a.startOffset>0){var l=a.startContainer;l.classList.contains("vditor-wysiwyg__block")&&(o=l.lastElementChild)}if(o){var m=o.previousElementSibling;if(m.style.display==="none"){r.key==="ArrowDown"||r.key==="ArrowRight"?lt(o,t):lt(o,t,!1);return}var w=o.previousElementSibling;if(w.tagName==="PRE"&&(w=w.firstElementChild),r.key==="ArrowDown"||r.key==="ArrowRight"){var l=o.parentElement,h=ur(l);if(h&&h.nodeType!==3){var A=h.querySelector(".vditor-wysiwyg__preview");if(A){lt(A,t);return}}if(h.nodeType===3){for(;h.textContent.length===0&&h.nextSibling;)h=h.nextSibling;a.setStart(h,1)}else a.setStart(h.firstChild,0)}else a.selectNodeContents(w),a.collapse(!1)}}}})},e}(),za=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(r[o]=a[o])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ua=function(e){za(t,e);function t(n,r){var a=e.call(this)||this;if(a.isDestroyed=!1,a.version=M.H,typeof n=="string"){if(r?r.cache?r.cache.id||(r.cache.id="vditor".concat(n)):r.cache={id:"vditor".concat(n)}:r={cache:{id:"vditor".concat(n)}},!document.getElementById(n))return a.showErrorTip("Failed to get element by id: ".concat(n)),a;n=document.getElementById(n)}var o=new xa(r),l=o.merge();if(l.i18n)window.VditorI18n=l.i18n,a.init(n,l);else if(["de_DE","en_US","fr_FR","pt_BR","ja_JP","ko_KR","ru_RU","sv_SE","zh_CN","zh_TW"].includes(l.lang)){var m="vditorI18nScript",w=m+l.lang;document.querySelectorAll('head script[id^="'.concat(m,'"]')).forEach(function(h){h.id!==w&&document.head.removeChild(h)}),(0,N.G)("".concat(l.cdn,"/dist/js/i18n/").concat(l.lang,".js"),w).then(function(){a.init(n,l)}).catch(function(h){a.showErrorTip("GET ".concat(l.cdn,"/dist/js/i18n/").concat(l.lang,".js net::ERR_ABORTED 404 (Not Found)"))})}else throw new Error("options.lang error, see https://ld246.com/article/1549638745630#options");return a}return t.prototype.showErrorTip=function(n){var r=new Nn;document.body.appendChild(r.element),r.show(n,0)},t.prototype.updateToolbarConfig=function(n){this.vditor.toolbar.updateConfig(this.vditor,n)},t.prototype.setTheme=function(n,r,a,o){this.vditor.options.theme=n,ke(this.vditor),r&&(this.vditor.options.preview.theme.current=r,(0,ie.Z)(r,o||this.vditor.options.preview.theme.path)),a&&(this.vditor.options.preview.hljs.style=a,(0,jn.Y)(a,this.vditor.options.cdn))},t.prototype.getValue=function(){return R(this.vditor)},t.prototype.getCurrentMode=function(){return this.vditor.currentMode},t.prototype.focus=function(){this.vditor.currentMode==="sv"?this.vditor.sv.element.focus():this.vditor.currentMode==="wysiwyg"?this.vditor.wysiwyg.element.focus():this.vditor.currentMode==="ir"&&this.vditor.ir.element.focus()},t.prototype.blur=function(){this.vditor.currentMode==="sv"?this.vditor.sv.element.blur():this.vditor.currentMode==="wysiwyg"?this.vditor.wysiwyg.element.blur():this.vditor.currentMode==="ir"&&this.vditor.ir.element.blur()},t.prototype.disabled=function(){S(this.vditor,["subToolbar","hint","popover"]),p(this.vditor.toolbar.elements,M.g.EDIT_TOOLBARS.concat(["undo","redo","fullscreen","edit-mode"])),this.vditor[this.vditor.currentMode].element.setAttribute("contenteditable","false")},t.prototype.enable=function(){y(this.vditor.toolbar.elements,M.g.EDIT_TOOLBARS.concat(["undo","redo","fullscreen","edit-mode"])),this.vditor.undo.resetIcon(this.vditor),this.vditor[this.vditor.currentMode].element.setAttribute("contenteditable","true")},t.prototype.getSelection=function(){if(this.vditor.currentMode==="wysiwyg")return dt(this.vditor.wysiwyg.element);if(this.vditor.currentMode==="sv")return dt(this.vditor.sv.element);if(this.vditor.currentMode==="ir")return dt(this.vditor.ir.element)},t.prototype.renderPreview=function(n){this.vditor.preview.render(this.vditor,n)},t.prototype.getCursorPosition=function(){return(0,O.Ny)(this.vditor[this.vditor.currentMode].element)},t.prototype.isUploading=function(){return this.vditor.upload.isUploading},t.prototype.clearCache=function(){this.vditor.options.cache.enable&&(0,c.pK)()&&localStorage.removeItem(this.vditor.options.cache.id)},t.prototype.disabledCache=function(){this.vditor.options.cache.enable=!1},t.prototype.enableCache=function(){if(!this.vditor.options.cache.id)throw new Error("need options.cache.id, see https://ld246.com/article/1549638745630#options");this.vditor.options.cache.enable=!0},t.prototype.html2md=function(n){return this.vditor.lute.HTML2Md(n)},t.prototype.exportJSON=function(n){return this.vditor.lute.RenderJSON(n)},t.prototype.getHTML=function(){return On(this.vditor)},t.prototype.tip=function(n,r){this.vditor.tip.show(n,r)},t.prototype.setPreviewMode=function(n){Zt(n,this.vditor)},t.prototype.deleteValue=function(){window.getSelection().isCollapsed||document.execCommand("delete",!1)},t.prototype.updateValue=function(n){document.execCommand("insertHTML",!1,n)},t.prototype.insertValue=function(n,r){r===void 0&&(r=!0);var a=(0,O.zh)(this.vditor);a.collapse(!0);var o=document.createElement("template");o.innerHTML=n,a.insertNode(o.content.cloneNode(!0)),a.collapse(!1),this.vditor.currentMode==="sv"?(this.vditor.sv.preventInput=!0,r&&he(this.vditor)):this.vditor.currentMode==="wysiwyg"?r&&Et(this.vditor,getSelection().getRangeAt(0)):this.vditor.currentMode==="ir"&&(this.vditor.ir.preventInput=!0,r&&_e(this.vditor,getSelection().getRangeAt(0),!0))},t.prototype.insertMD=function(n){this.vditor.currentMode==="ir"?(0,O.oC)(this.vditor.lute.Md2VditorIRDOM(n),this.vditor):this.vditor.currentMode==="wysiwyg"?(0,O.oC)(this.vditor.lute.Md2VditorDOM(n),this.vditor):Wt(this.vditor,n),this.vditor.outline.render(this.vditor),le(this.vditor)},t.prototype.setValue=function(n,r){var a=this;r===void 0&&(r=!1),this.vditor.currentMode==="sv"?(this.vditor.sv.element.innerHTML="<div data-block='0'>".concat(this.vditor.lute.SpinVditorSVDOM(n),"</div>"),Ae(this.vditor,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1})):this.vditor.currentMode==="wysiwyg"?sn(this.vditor,n,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}):(this.vditor.ir.element.innerHTML=this.vditor.lute.Md2VditorIRDOM(n),this.vditor.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach(function(o){X(o,a.vditor)}),Ze(this.vditor,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1})),this.vditor.outline.render(this.vditor),n||(S(this.vditor,["emoji","headings","submenu","hint"]),this.vditor.wysiwyg.popover&&(this.vditor.wysiwyg.popover.style.display="none"),this.clearCache()),r&&this.clearStack()},t.prototype.insertEmptyBlock=function(n){ft(this.vditor,n)},t.prototype.clearStack=function(){this.vditor.undo.clearStack(this.vditor),this.vditor.undo.addToUndoStack(this.vditor)},t.prototype.destroy=function(){this.vditor.element.innerHTML=this.vditor.originalInnerHTML,this.vditor.element.classList.remove("vditor"),this.vditor.element.removeAttribute("style");var n=document.getElementById("vditorIconScript");n&&n.remove(),this.clearCache(),tn(),this.vditor.wysiwyg.unbindListener(),this.vditor.options.after=void 0,this.isDestroyed=!0},t.prototype.getCommentIds=function(){return this.vditor.currentMode!=="wysiwyg"?[]:this.vditor.wysiwyg.getComments(this.vditor,!0)},t.prototype.hlCommentIds=function(n){if(this.vditor.currentMode==="wysiwyg"){var r=function(a){a.classList.remove("vditor-comment--hover"),n.forEach(function(o){a.getAttribute("data-cmtids").indexOf(o)>-1&&a.classList.add("vditor-comment--hover")})};this.vditor.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(a){r(a)}),this.vditor.preview.element.style.display!=="none"&&this.vditor.preview.element.querySelectorAll(".vditor-comment").forEach(function(a){r(a)})}},t.prototype.unHlCommentIds=function(n){if(this.vditor.currentMode==="wysiwyg"){var r=function(a){n.forEach(function(o){a.getAttribute("data-cmtids").indexOf(o)>-1&&a.classList.remove("vditor-comment--hover")})};this.vditor.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(a){r(a)}),this.vditor.preview.element.style.display!=="none"&&this.vditor.preview.element.querySelectorAll(".vditor-comment").forEach(function(a){r(a)})}},t.prototype.removeCommentIds=function(n){var r=this;if(this.vditor.currentMode==="wysiwyg"){var a=function(o,l){var m=o.getAttribute("data-cmtids").split(" ");m.find(function(w,h){if(w===l)return m.splice(h,1),!0}),m.length===0?(o.outerHTML=o.innerHTML,(0,O.zh)(r.vditor).collapse(!0)):o.setAttribute("data-cmtids",m.join(" "))};n.forEach(function(o){r.vditor.wysiwyg.element.querySelectorAll(".vditor-comment").forEach(function(l){a(l,o)}),r.vditor.preview.element.style.display!=="none"&&r.vditor.preview.element.querySelectorAll(".vditor-comment").forEach(function(l){a(l,o)})}),we(this.vditor,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1})}},t.prototype.init=function(n,r){var a=this;this.isDestroyed||(this.vditor={currentMode:r.mode,element:n,hint:new Dr(r.hint.extend),lute:void 0,options:r,originalInnerHTML:n.innerHTML,outline:new Nr(window.VditorI18n.outline),tip:new Nn},this.vditor.sv=new xr(this.vditor),this.vditor.undo=new Ra,this.vditor.wysiwyg=new Ba(this.vditor),this.vditor.ir=new Or(this.vditor),this.vditor.toolbar=new Na(this.vditor),r.resize.enable&&(this.vditor.resize=new Ir(this.vditor)),this.vditor.toolbar.elements.devtools&&(this.vditor.devtools=new d),(r.upload.url||r.upload.handler)&&(this.vditor.upload=new vr),(0,N.G)(r._lutePath||"".concat(r.cdn,"/dist/js/lute/lute.min.js"),"vditorLuteScript").then(function(){a.vditor.lute=(0,Pr.X)({autoSpace:a.vditor.options.preview.markdown.autoSpace,gfmAutoLink:a.vditor.options.preview.markdown.gfmAutoLink,codeBlockPreview:a.vditor.options.preview.markdown.codeBlockPreview,emojiSite:a.vditor.options.hint.emojiPath,emojis:a.vditor.options.hint.emoji,fixTermTypo:a.vditor.options.preview.markdown.fixTermTypo,footnotes:a.vditor.options.preview.markdown.footnotes,headingAnchor:!1,inlineMathDigit:a.vditor.options.preview.math.inlineDigit,linkBase:a.vditor.options.preview.markdown.linkBase,linkPrefix:a.vditor.options.preview.markdown.linkPrefix,listStyle:a.vditor.options.preview.markdown.listStyle,mark:a.vditor.options.preview.markdown.mark,mathBlockPreview:a.vditor.options.preview.markdown.mathBlockPreview,paragraphBeginningSpace:a.vditor.options.preview.markdown.paragraphBeginningSpace,sanitize:a.vditor.options.preview.markdown.sanitize,toc:a.vditor.options.preview.markdown.toc}),a.vditor.preview=new Rr(a.vditor),Be(a.vditor),r.after&&r.after(),r.icon&&(0,N.J)("".concat(r.cdn,"/dist/js/icons/").concat(r.icon,".js"),"vditorIconScript")}))},t}(Q.default);const Va=Ua})(),Me=Me.default,Me})()})}(yt,yt.exports)),yt.exports}var si=ii();const oi=ar(si),ui=er({inheritAttrs:!1,props:{height:{type:Number,default:360},value:{type:String,default:""}},emits:["change","get","update:value"],setup(Ee,{attrs:me,emit:ge}){const E=wt(null),re=wt(null),Me=wt(!1),Q=ri(),{getLocale:M}=$a(),{getDarkMode:D}=Xn(),R=wt(Ee.value||"");Yt([()=>D.value,()=>Me.value],([L,S])=>{var s;if(!S)return;const i=L==="dark"?"dark":"classic";(s=p.getVditor())==null||s.setTheme(i)},{immediate:!0,flush:"post"}),Yt(()=>Ee.value,L=>{var S;L!==R.value&&((S=p.getVditor())==null||S.setValue(L)),R.value=L});const N=tr(()=>{let L;switch(Dt(M)){case"en":L="en_US";break;case"ja":L="ja_JP";break;case"ko":L="ko_KR";break;default:L="zh_CN"}return L}),d=`${window._CONFIG.domianURL}/sys/common/upload`,c=Za(),_=Kn()?Kn():"0";function g(L,S){let i=JSON.parse(S),s=L[0].name,u={msg:"",code:0,data:{errFiles:[""],succMap:{}}};return i.success?(u.data.errFiles=[],u.data.succMap={[i.message]:Gn(i.message)}):(u.code=1,u.msg=i.message,u.data.errFiles=[s]),JSON.stringify(u)}function y(){const L=Dt(E);if(!L)return;const S=bt(bt({},me),Ee),i=new oi(L,Fn(bt({theme:D.value==="dark"?"dark":"classic",lang:Dt(N),toolbar:["emoji","headings","bold","italic","strike","link","|","list","ordered-list","check","outdent","indent","|","quote","line","code","inline-code","insert-before","insert-after","|","upload","table","|","undo","redo","|","fullscreen","edit-mode",{name:"more",toolbar:["both","code-theme","content-theme","export","outline","preview","devtools","info","help"]}],mode:"sv",cdn:"https://unpkg.com/vditor@3.10.1",fullscreen:{index:520},preview:{actions:[]},upload:{accept:"image/*",fieldName:"file",extraData:{biz:"markdown"},setHeaders(){return{"X-Access-Token":c,"X-Tenant-Id":_}},format(u,f){return g(u,f)},handler(u){return qn(this,null,function*(){const f=v=>{var b;v.success&&((b=re.value)==null||b.insertValue(`![${v.message}](${Gn(v.message)})`))};for(const v of u){let b={file:v,filename:v.name,data:{biz:"markdown"}};yield Ja(b,f)}})}},input:u=>{R.value=u,ge("update:value",u),ge("change",u)},after:()=>{Ya(()=>{var u;(u=Q==null?void 0:Q.redoModalHeight)==null||u.call(Q),i.setValue(R.value),re.value=i,Me.value=!0,ge("get",p)})},blur:()=>{}},S),{cache:{enable:!1}}))}const p={getVditor:()=>re.value};function C(){var S;const L=Dt(re);if(L){try{(S=L==null?void 0:L.destroy)==null||S.call(L)}catch(i){}re.value=null,Me.value=!1}}return Xa(y),ei(C),ti(C),bt({wrapRef:E},p)}}),li={ref:"wrapRef"};function ci(Ee,me,ge,E,re,Me){return rr(),nr("div",li,null,512)}const di=Qn(ui,[["render",ci],["__scopeId","data-v-b9d5a948"]]);var Ot={exports:{}},fi=Ot.exports,Jn;function pi(){return Jn||(Jn=1,function(Ee){/*! showdown v 2.1.0 - 21-04-2022 */(function(){function me(i){"use strict";var s={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:`Remove only spaces, ' and " from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids`,type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,describe:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,describe:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,describe:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,describe:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,describe:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",describe:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,describe:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,describe:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,describe:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,describe:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,describe:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},ellipsis:{defaultValue:!0,describe:"Replaces three dots with the ellipsis unicode character",type:"boolean"},completeHTMLDocument:{defaultValue:!1,describe:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,describe:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,describe:"Split adjacent blockquote blocks",type:"boolean"}};if(i===!1)return JSON.parse(JSON.stringify(s));var u={};for(var f in s)s.hasOwnProperty(f)&&(u[f]=s[f].defaultValue);return u}function ge(){"use strict";var i=me(!0),s={};for(var u in i)i.hasOwnProperty(u)&&(s[u]=!0);return s}var E={},re={},Me={},Q=me(!0),M="vanilla",D={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:me(!0),allOn:ge()};E.helper={},E.extensions={},E.setOption=function(i,s){"use strict";return Q[i]=s,this},E.getOption=function(i){"use strict";return Q[i]},E.getOptions=function(){"use strict";return Q},E.resetOptions=function(){"use strict";Q=me(!0)},E.setFlavor=function(i){"use strict";if(!D.hasOwnProperty(i))throw Error(i+" flavor was not found");E.resetOptions();var s=D[i];M=i;for(var u in s)s.hasOwnProperty(u)&&(Q[u]=s[u])},E.getFlavor=function(){"use strict";return M},E.getFlavorOptions=function(i){"use strict";if(D.hasOwnProperty(i))return D[i]},E.getDefaultOptions=function(i){"use strict";return me(i)},E.subParser=function(i,s){"use strict";if(E.helper.isString(i))if(typeof s!="undefined")re[i]=s;else{if(re.hasOwnProperty(i))return re[i];throw Error("SubParser named "+i+" not registered!")}},E.extension=function(i,s){"use strict";if(!E.helper.isString(i))throw Error("Extension 'name' must be a string");if(i=E.helper.stdExtName(i),E.helper.isUndefined(s)){if(!Me.hasOwnProperty(i))throw Error("Extension named "+i+" is not registered!");return Me[i]}else{typeof s=="function"&&(s=s()),E.helper.isArray(s)||(s=[s]);var u=R(s,i);if(u.valid)Me[i]=s;else throw Error(u.error)}},E.getAllExtensions=function(){"use strict";return Me},E.removeExtension=function(i){"use strict";delete Me[i]},E.resetExtensions=function(){"use strict";Me={}};function R(i,s){"use strict";var u=s?"Error in "+s+" extension->":"Error in unnamed extension",f={valid:!0,error:""};E.helper.isArray(i)||(i=[i]);for(var v=0;v<i.length;++v){var b=u+" sub-extension "+v+": ",k=i[v];if(typeof k!="object")return f.valid=!1,f.error=b+"must be an object, but "+typeof k+" given",f;if(!E.helper.isString(k.type))return f.valid=!1,f.error=b+'property "type" must be a string, but '+typeof k.type+" given",f;var H=k.type=k.type.toLowerCase();if(H==="language"&&(H=k.type="lang"),H==="html"&&(H=k.type="output"),H!=="lang"&&H!=="output"&&H!=="listener")return f.valid=!1,f.error=b+"type "+H+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',f;if(H==="listener"){if(E.helper.isUndefined(k.listeners))return f.valid=!1,f.error=b+'. Extensions of type "listener" must have a property called "listeners"',f}else if(E.helper.isUndefined(k.filter)&&E.helper.isUndefined(k.regex))return f.valid=!1,f.error=b+H+' extensions must define either a "regex" property or a "filter" method',f;if(k.listeners){if(typeof k.listeners!="object")return f.valid=!1,f.error=b+'"listeners" property must be an object but '+typeof k.listeners+" given",f;for(var j in k.listeners)if(k.listeners.hasOwnProperty(j)&&typeof k.listeners[j]!="function")return f.valid=!1,f.error=b+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+j+" must be a function but "+typeof k.listeners[j]+" given",f}if(k.filter){if(typeof k.filter!="function")return f.valid=!1,f.error=b+'"filter" must be a function, but '+typeof k.filter+" given",f}else if(k.regex){if(E.helper.isString(k.regex)&&(k.regex=new RegExp(k.regex,"g")),!(k.regex instanceof RegExp))return f.valid=!1,f.error=b+'"regex" property must either be a string or a RegExp object, but '+typeof k.regex+" given",f;if(E.helper.isUndefined(k.replace))return f.valid=!1,f.error=b+'"regex" extensions must implement a replace string or function',f}}return f}E.validateExtension=function(i){"use strict";var s=R(i,null);return!!s.valid},E.hasOwnProperty("helper")||(E.helper={}),E.helper.isString=function(i){"use strict";return typeof i=="string"||i instanceof String},E.helper.isFunction=function(i){"use strict";var s={};return i&&s.toString.call(i)==="[object Function]"},E.helper.isArray=function(i){"use strict";return Array.isArray(i)},E.helper.isUndefined=function(i){"use strict";return typeof i=="undefined"},E.helper.forEach=function(i,s){"use strict";if(E.helper.isUndefined(i))throw new Error("obj param is required");if(E.helper.isUndefined(s))throw new Error("callback param is required");if(!E.helper.isFunction(s))throw new Error("callback param must be a function/closure");if(typeof i.forEach=="function")i.forEach(s);else if(E.helper.isArray(i))for(var u=0;u<i.length;u++)s(i[u],u,i);else if(typeof i=="object")for(var f in i)i.hasOwnProperty(f)&&s(i[f],f,i);else throw new Error("obj does not seem to be an array or an iterable object")},E.helper.stdExtName=function(i){"use strict";return i.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()};function N(i,s){"use strict";var u=s.charCodeAt(0);return"¨E"+u+"E"}E.helper.escapeCharactersCallback=N,E.helper.escapeCharacters=function(i,s,u){"use strict";var f="(["+s.replace(/([\[\]\\])/g,"\\$1")+"])";u&&(f="\\\\"+f);var v=new RegExp(f,"g");return i=i.replace(v,N),i},E.helper.unescapeHTMLEntities=function(i){"use strict";return i.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};var d=function(i,s,u,f){"use strict";var v=f||"",b=v.indexOf("g")>-1,k=new RegExp(s+"|"+u,"g"+v.replace(/g/g,"")),H=new RegExp(s,v.replace(/g/g,"")),j=[],x,I,z,T,B;do for(x=0;z=k.exec(i);)if(H.test(z[0]))x++||(I=k.lastIndex,T=I-z[0].length);else if(x&&!--x){B=z.index+z[0].length;var q={left:{start:T,end:I},match:{start:I,end:z.index},right:{start:z.index,end:B},wholeMatch:{start:T,end:B}};if(j.push(q),!b)return j}while(x&&(k.lastIndex=I));return j};E.helper.matchRecursiveRegExp=function(i,s,u,f){"use strict";for(var v=d(i,s,u,f),b=[],k=0;k<v.length;++k)b.push([i.slice(v[k].wholeMatch.start,v[k].wholeMatch.end),i.slice(v[k].match.start,v[k].match.end),i.slice(v[k].left.start,v[k].left.end),i.slice(v[k].right.start,v[k].right.end)]);return b},E.helper.replaceRecursiveRegExp=function(i,s,u,f,v){"use strict";if(!E.helper.isFunction(s)){var b=s;s=function(){return b}}var k=d(i,u,f,v),H=i,j=k.length;if(j>0){var x=[];k[0].wholeMatch.start!==0&&x.push(i.slice(0,k[0].wholeMatch.start));for(var I=0;I<j;++I)x.push(s(i.slice(k[I].wholeMatch.start,k[I].wholeMatch.end),i.slice(k[I].match.start,k[I].match.end),i.slice(k[I].left.start,k[I].left.end),i.slice(k[I].right.start,k[I].right.end))),I<j-1&&x.push(i.slice(k[I].wholeMatch.end,k[I+1].wholeMatch.start));k[j-1].wholeMatch.end<i.length&&x.push(i.slice(k[j-1].wholeMatch.end)),H=x.join("")}return H},E.helper.regexIndexOf=function(i,s,u){"use strict";if(!E.helper.isString(i))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(!(s instanceof RegExp))throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var f=i.substring(u||0).search(s);return f>=0?f+(u||0):f},E.helper.splitAtIndex=function(i,s){"use strict";if(!E.helper.isString(i))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[i.substring(0,s),i.substring(s)]},E.helper.encodeEmailAddress=function(i){"use strict";var s=[function(u){return"&#"+u.charCodeAt(0)+";"},function(u){return"&#x"+u.charCodeAt(0).toString(16)+";"},function(u){return u}];return i=i.replace(/./g,function(u){if(u==="@")u=s[Math.floor(Math.random()*2)](u);else{var f=Math.random();u=f>.9?s[2](u):f>.45?s[1](u):s[0](u)}return u}),i},E.helper.padEnd=function(s,u,f){"use strict";return u=u>>0,f=String(f||" "),s.length>u?String(s):(u=u-s.length,u>f.length&&(f+=f.repeat(u/f.length)),String(s)+f.slice(0,u))},typeof console=="undefined"&&(console={warn:function(i){"use strict";alert(i)},log:function(i){"use strict";alert(i)},error:function(i){"use strict";throw i}}),E.helper.regexes={asteriskDashAndColon:/([*_:~])/g},E.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:`<span style="font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;">S</span>`},E.Converter=function(i){"use strict";var s={},u=[],f=[],v={},b=M,k={parsed:{},raw:"",format:""};H();function H(){i=i||{};for(var T in Q)Q.hasOwnProperty(T)&&(s[T]=Q[T]);if(typeof i=="object")for(var B in i)i.hasOwnProperty(B)&&(s[B]=i[B]);else throw Error("Converter expects the passed parameter to be an object, but "+typeof i+" was passed instead.");s.extensions&&E.helper.forEach(s.extensions,j)}function j(T,B){if(B=B||null,E.helper.isString(T))if(T=E.helper.stdExtName(T),B=T,E.extensions[T]){x(E.extensions[T],T);return}else if(!E.helper.isUndefined(Me[T]))T=Me[T];else throw Error('Extension "'+T+'" could not be loaded. It was either not found or is not a valid extension.');typeof T=="function"&&(T=T()),E.helper.isArray(T)||(T=[T]);var q=R(T,B);if(!q.valid)throw Error(q.error);for(var J=0;J<T.length;++J){switch(T[J].type){case"lang":u.push(T[J]);break;case"output":f.push(T[J]);break}if(T[J].hasOwnProperty("listeners"))for(var se in T[J].listeners)T[J].listeners.hasOwnProperty(se)&&I(se,T[J].listeners[se])}}function x(T,B){typeof T=="function"&&(T=T(new E.Converter)),E.helper.isArray(T)||(T=[T]);var q=R(T,B);if(!q.valid)throw Error(q.error);for(var J=0;J<T.length;++J)switch(T[J].type){case"lang":u.push(T[J]);break;case"output":f.push(T[J]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}function I(T,B){if(!E.helper.isString(T))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof T+" given");if(typeof B!="function")throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof B+" given");v.hasOwnProperty(T)||(v[T]=[]),v[T].push(B)}function z(T){var B=T.match(/^\s*/)[0].length,q=new RegExp("^\\s{0,"+B+"}","gm");return T.replace(q,"")}this._dispatch=function(B,q,J,se){if(v.hasOwnProperty(B))for(var X=0;X<v[B].length;++X){var O=v[B][X](B,q,this,J,se);O&&typeof O!="undefined"&&(q=O)}return q},this.listen=function(T,B){return I(T,B),this},this.makeHtml=function(T){if(!T)return T;var B={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:u,outputModifiers:f,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return T=T.replace(/¨/g,"¨T"),T=T.replace(/\$/g,"¨D"),T=T.replace(/\r\n/g,`
`),T=T.replace(/\r/g,`
`),T=T.replace(/\u00A0/g,"&nbsp;"),s.smartIndentationFix&&(T=z(T)),T=`

`+T+`

`,T=E.subParser("detab")(T,s,B),T=T.replace(/^[ \t]+$/mg,""),E.helper.forEach(u,function(q){T=E.subParser("runExtension")(q,T,s,B)}),T=E.subParser("metadata")(T,s,B),T=E.subParser("hashPreCodeTags")(T,s,B),T=E.subParser("githubCodeBlocks")(T,s,B),T=E.subParser("hashHTMLBlocks")(T,s,B),T=E.subParser("hashCodeTags")(T,s,B),T=E.subParser("stripLinkDefinitions")(T,s,B),T=E.subParser("blockGamut")(T,s,B),T=E.subParser("unhashHTMLSpans")(T,s,B),T=E.subParser("unescapeSpecialChars")(T,s,B),T=T.replace(/¨D/g,"$$"),T=T.replace(/¨T/g,"¨"),T=E.subParser("completeHTMLDocument")(T,s,B),E.helper.forEach(f,function(q){T=E.subParser("runExtension")(q,T,s,B)}),k=B.metadata,T},this.makeMarkdown=this.makeMd=function(T,B){if(T=T.replace(/\r\n/g,`
`),T=T.replace(/\r/g,`
`),T=T.replace(/>[ \t]+</,">¨NBSP;<"),!B)if(window&&window.document)B=window.document;else throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");var q=B.createElement("div");q.innerHTML=T;var J={preList:Pe(q)};ve(q);for(var se=q.childNodes,X="",O=0;O<se.length;O++)X+=E.subParser("makeMarkdown.node")(se[O],J);function ve(Le){for(var _e=0;_e<Le.childNodes.length;++_e){var U=Le.childNodes[_e];U.nodeType===3?!/\S/.test(U.nodeValue)&&!/^[ ]+$/.test(U.nodeValue)?(Le.removeChild(U),--_e):(U.nodeValue=U.nodeValue.split(`
`).join(" "),U.nodeValue=U.nodeValue.replace(/(\s)+/g,"$1")):U.nodeType===1&&ve(U)}}function Pe(Le){for(var _e=Le.querySelectorAll("pre"),U=[],ue=0;ue<_e.length;++ue)if(_e[ue].childElementCount===1&&_e[ue].firstChild.tagName.toLowerCase()==="code"){var Z=_e[ue].firstChild.innerHTML.trim(),G=_e[ue].firstChild.getAttribute("data-language")||"";if(G==="")for(var be=_e[ue].firstChild.className.split(" "),Y=0;Y<be.length;++Y){var Se=be[Y].match(/^language-(.+)$/);if(Se!==null){G=Se[1];break}}Z=E.helper.unescapeHTMLEntities(Z),U.push(Z),_e[ue].outerHTML='<precode language="'+G+'" precodenum="'+ue.toString()+'"></precode>'}else U.push(_e[ue].innerHTML),_e[ue].innerHTML="",_e[ue].setAttribute("prenum",ue.toString());return U}return X},this.setOption=function(T,B){s[T]=B},this.getOption=function(T){return s[T]},this.getOptions=function(){return s},this.addExtension=function(T,B){B=B||null,j(T,B)},this.useExtension=function(T){j(T)},this.setFlavor=function(T){if(!D.hasOwnProperty(T))throw Error(T+" flavor was not found");var B=D[T];b=T;for(var q in B)B.hasOwnProperty(q)&&(s[q]=B[q])},this.getFlavor=function(){return b},this.removeExtension=function(T){E.helper.isArray(T)||(T=[T]);for(var B=0;B<T.length;++B){for(var q=T[B],J=0;J<u.length;++J)u[J]===q&&u.splice(J,1);for(var se=0;se<f.length;++se)f[se]===q&&f.splice(se,1)}},this.getAllExtensions=function(){return{language:u,output:f}},this.getMetadata=function(T){return T?k.raw:k.parsed},this.getMetadataFormat=function(){return k.format},this._setMetadataPair=function(T,B){k.parsed[T]=B},this._setMetadataFormat=function(T){k.format=T},this._setMetadataRaw=function(T){k.raw=T}},E.subParser("anchors",function(i,s,u){"use strict";i=u.converter._dispatch("anchors.before",i,s,u);var f=function(v,b,k,H,j,x,I){if(E.helper.isUndefined(I)&&(I=""),k=k.toLowerCase(),v.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)H="";else if(!H)if(k||(k=b.toLowerCase().replace(/ ?\n/g," ")),H="#"+k,!E.helper.isUndefined(u.gUrls[k]))H=u.gUrls[k],E.helper.isUndefined(u.gTitles[k])||(I=u.gTitles[k]);else return v;H=H.replace(E.helper.regexes.asteriskDashAndColon,E.helper.escapeCharactersCallback);var z='<a href="'+H+'"';return I!==""&&I!==null&&(I=I.replace(/"/g,"&quot;"),I=I.replace(E.helper.regexes.asteriskDashAndColon,E.helper.escapeCharactersCallback),z+=' title="'+I+'"'),s.openLinksInNewWindow&&!/^#/.test(H)&&(z+=' rel="noopener noreferrer" target="¨E95Eblank"'),z+=">"+b+"</a>",z};return i=i.replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,f),i=i.replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,f),i=i.replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,f),i=i.replace(/\[([^\[\]]+)]()()()()()/g,f),s.ghMentions&&(i=i.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gmi,function(v,b,k,H,j){if(k==="\\")return b+H;if(!E.helper.isString(s.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var x=s.ghMentionsLink.replace(/\{u}/g,j),I="";return s.openLinksInNewWindow&&(I=' rel="noopener noreferrer" target="¨E95Eblank"'),b+'<a href="'+x+'"'+I+">"+H+"</a>"})),i=u.converter._dispatch("anchors.after",i,s,u),i});var c=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,_=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,g=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,y=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gmi,p=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi,C=function(i){"use strict";return function(s,u,f,v,b,k,H){f=f.replace(E.helper.regexes.asteriskDashAndColon,E.helper.escapeCharactersCallback);var j=f,x="",I="",z=u||"",T=H||"";return/^www\./i.test(f)&&(f=f.replace(/^www\./i,"http://www.")),i.excludeTrailingPunctuationFromURLs&&k&&(x=k),i.openLinksInNewWindow&&(I=' rel="noopener noreferrer" target="¨E95Eblank"'),z+'<a href="'+f+'"'+I+">"+j+"</a>"+x+T}},L=function(i,s){"use strict";return function(u,f,v){var b="mailto:";return f=f||"",v=E.subParser("unescapeSpecialChars")(v,i,s),i.encodeEmails?(b=E.helper.encodeEmailAddress(b+v),v=E.helper.encodeEmailAddress(v)):b=b+v,f+'<a href="'+b+'">'+v+"</a>"}};E.subParser("autoLinks",function(i,s,u){"use strict";return i=u.converter._dispatch("autoLinks.before",i,s,u),i=i.replace(g,C(s)),i=i.replace(p,L(s,u)),i=u.converter._dispatch("autoLinks.after",i,s,u),i}),E.subParser("simplifiedAutoLinks",function(i,s,u){"use strict";return s.simplifiedAutoLink&&(i=u.converter._dispatch("simplifiedAutoLinks.before",i,s,u),s.excludeTrailingPunctuationFromURLs?i=i.replace(_,C(s)):i=i.replace(c,C(s)),i=i.replace(y,L(s,u)),i=u.converter._dispatch("simplifiedAutoLinks.after",i,s,u)),i}),E.subParser("blockGamut",function(i,s,u){"use strict";return i=u.converter._dispatch("blockGamut.before",i,s,u),i=E.subParser("blockQuotes")(i,s,u),i=E.subParser("headers")(i,s,u),i=E.subParser("horizontalRule")(i,s,u),i=E.subParser("lists")(i,s,u),i=E.subParser("codeBlocks")(i,s,u),i=E.subParser("tables")(i,s,u),i=E.subParser("hashHTMLBlocks")(i,s,u),i=E.subParser("paragraphs")(i,s,u),i=u.converter._dispatch("blockGamut.after",i,s,u),i}),E.subParser("blockQuotes",function(i,s,u){"use strict";i=u.converter._dispatch("blockQuotes.before",i,s,u),i=i+`

`;var f=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return s.splitAdjacentBlockquotes&&(f=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),i=i.replace(f,function(v){return v=v.replace(/^[ \t]*>[ \t]?/gm,""),v=v.replace(/¨0/g,""),v=v.replace(/^[ \t]+$/gm,""),v=E.subParser("githubCodeBlocks")(v,s,u),v=E.subParser("blockGamut")(v,s,u),v=v.replace(/(^|\n)/g,"$1  "),v=v.replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,function(b,k){var H=k;return H=H.replace(/^  /mg,"¨0"),H=H.replace(/¨0/g,""),H}),E.subParser("hashBlock")(`<blockquote>
`+v+`
</blockquote>`,s,u)}),i=u.converter._dispatch("blockQuotes.after",i,s,u),i}),E.subParser("codeBlocks",function(i,s,u){"use strict";i=u.converter._dispatch("codeBlocks.before",i,s,u),i+="¨0";var f=/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g;return i=i.replace(f,function(v,b,k){var H=b,j=k,x=`
`;return H=E.subParser("outdent")(H,s,u),H=E.subParser("encodeCode")(H,s,u),H=E.subParser("detab")(H,s,u),H=H.replace(/^\n+/g,""),H=H.replace(/\n+$/g,""),s.omitExtraWLInCodeBlocks&&(x=""),H="<pre><code>"+H+x+"</code></pre>",E.subParser("hashBlock")(H,s,u)+j}),i=i.replace(/¨0/,""),i=u.converter._dispatch("codeBlocks.after",i,s,u),i}),E.subParser("codeSpans",function(i,s,u){"use strict";return i=u.converter._dispatch("codeSpans.before",i,s,u),typeof i=="undefined"&&(i=""),i=i.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,function(f,v,b,k){var H=k;return H=H.replace(/^([ \t]*)/g,""),H=H.replace(/[ \t]*$/g,""),H=E.subParser("encodeCode")(H,s,u),H=v+"<code>"+H+"</code>",H=E.subParser("hashHTMLSpans")(H,s,u),H}),i=u.converter._dispatch("codeSpans.after",i,s,u),i}),E.subParser("completeHTMLDocument",function(i,s,u){"use strict";if(!s.completeHTMLDocument)return i;i=u.converter._dispatch("completeHTMLDocument.before",i,s,u);var f="html",v=`<!DOCTYPE HTML>
`,b="",k=`<meta charset="utf-8">
`,H="",j="";typeof u.metadata.parsed.doctype!="undefined"&&(v="<!DOCTYPE "+u.metadata.parsed.doctype+`>
`,f=u.metadata.parsed.doctype.toString().toLowerCase(),(f==="html"||f==="html5")&&(k='<meta charset="utf-8">'));for(var x in u.metadata.parsed)if(u.metadata.parsed.hasOwnProperty(x))switch(x.toLowerCase()){case"doctype":break;case"title":b="<title>"+u.metadata.parsed.title+`</title>
`;break;case"charset":f==="html"||f==="html5"?k='<meta charset="'+u.metadata.parsed.charset+`">
`:k='<meta name="charset" content="'+u.metadata.parsed.charset+`">
`;break;case"language":case"lang":H=' lang="'+u.metadata.parsed[x]+'"',j+='<meta name="'+x+'" content="'+u.metadata.parsed[x]+`">
`;break;default:j+='<meta name="'+x+'" content="'+u.metadata.parsed[x]+`">
`}return i=v+"<html"+H+`>
<head>
`+b+k+j+`</head>
<body>
`+i.trim()+`
</body>
</html>`,i=u.converter._dispatch("completeHTMLDocument.after",i,s,u),i}),E.subParser("detab",function(i,s,u){"use strict";return i=u.converter._dispatch("detab.before",i,s,u),i=i.replace(/\t(?=\t)/g,"    "),i=i.replace(/\t/g,"¨A¨B"),i=i.replace(/¨B(.+?)¨A/g,function(f,v){for(var b=v,k=4-b.length%4,H=0;H<k;H++)b+=" ";return b}),i=i.replace(/¨A/g,"    "),i=i.replace(/¨B/g,""),i=u.converter._dispatch("detab.after",i,s,u),i}),E.subParser("ellipsis",function(i,s,u){"use strict";return s.ellipsis&&(i=u.converter._dispatch("ellipsis.before",i,s,u),i=i.replace(/\.\.\./g,"…"),i=u.converter._dispatch("ellipsis.after",i,s,u)),i}),E.subParser("emoji",function(i,s,u){"use strict";if(!s.emoji)return i;i=u.converter._dispatch("emoji.before",i,s,u);var f=/:([\S]+?):/g;return i=i.replace(f,function(v,b){return E.helper.emojis.hasOwnProperty(b)?E.helper.emojis[b]:v}),i=u.converter._dispatch("emoji.after",i,s,u),i}),E.subParser("encodeAmpsAndAngles",function(i,s,u){"use strict";return i=u.converter._dispatch("encodeAmpsAndAngles.before",i,s,u),i=i.replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;"),i=i.replace(/<(?![a-z\/?$!])/gi,"&lt;"),i=i.replace(/</g,"&lt;"),i=i.replace(/>/g,"&gt;"),i=u.converter._dispatch("encodeAmpsAndAngles.after",i,s,u),i}),E.subParser("encodeBackslashEscapes",function(i,s,u){"use strict";return i=u.converter._dispatch("encodeBackslashEscapes.before",i,s,u),i=i.replace(/\\(\\)/g,E.helper.escapeCharactersCallback),i=i.replace(/\\([`*_{}\[\]()>#+.!~=|:-])/g,E.helper.escapeCharactersCallback),i=u.converter._dispatch("encodeBackslashEscapes.after",i,s,u),i}),E.subParser("encodeCode",function(i,s,u){"use strict";return i=u.converter._dispatch("encodeCode.before",i,s,u),i=i.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,E.helper.escapeCharactersCallback),i=u.converter._dispatch("encodeCode.after",i,s,u),i}),E.subParser("escapeSpecialCharsWithinTagAttributes",function(i,s,u){"use strict";i=u.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",i,s,u);var f=/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,v=/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi;return i=i.replace(f,function(b){return b.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,E.helper.escapeCharactersCallback)}),i=i.replace(v,function(b){return b.replace(/([\\`*_~=|])/g,E.helper.escapeCharactersCallback)}),i=u.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",i,s,u),i}),E.subParser("githubCodeBlocks",function(i,s,u){"use strict";return s.ghCodeBlocks?(i=u.converter._dispatch("githubCodeBlocks.before",i,s,u),i+="¨0",i=i.replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,function(f,v,b,k){var H=s.omitExtraWLInCodeBlocks?"":`
`;return k=E.subParser("encodeCode")(k,s,u),k=E.subParser("detab")(k,s,u),k=k.replace(/^\n+/g,""),k=k.replace(/\n+$/g,""),k="<pre><code"+(b?' class="'+b+" language-"+b+'"':"")+">"+k+H+"</code></pre>",k=E.subParser("hashBlock")(k,s,u),`

¨G`+(u.ghCodeBlocks.push({text:f,codeblock:k})-1)+`G

`}),i=i.replace(/¨0/,""),u.converter._dispatch("githubCodeBlocks.after",i,s,u)):i}),E.subParser("hashBlock",function(i,s,u){"use strict";return i=u.converter._dispatch("hashBlock.before",i,s,u),i=i.replace(/(^\n+|\n+$)/g,""),i=`

¨K`+(u.gHtmlBlocks.push(i)-1)+`K

`,i=u.converter._dispatch("hashBlock.after",i,s,u),i}),E.subParser("hashCodeTags",function(i,s,u){"use strict";i=u.converter._dispatch("hashCodeTags.before",i,s,u);var f=function(v,b,k,H){var j=k+E.subParser("encodeCode")(b,s,u)+H;return"¨C"+(u.gHtmlSpans.push(j)-1)+"C"};return i=E.helper.replaceRecursiveRegExp(i,f,"<code\\b[^>]*>","</code>","gim"),i=u.converter._dispatch("hashCodeTags.after",i,s,u),i}),E.subParser("hashElement",function(i,s,u){"use strict";return function(f,v){var b=v;return b=b.replace(/\n\n/g,`
`),b=b.replace(/^\n/,""),b=b.replace(/\n+$/g,""),b=`

¨K`+(u.gHtmlBlocks.push(b)-1)+`K

`,b}}),E.subParser("hashHTMLBlocks",function(i,s,u){"use strict";i=u.converter._dispatch("hashHTMLBlocks.before",i,s,u);var f=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],v=function(T,B,q,J){var se=T;return q.search(/\bmarkdown\b/)!==-1&&(se=q+u.converter.makeHtml(B)+J),`

¨K`+(u.gHtmlBlocks.push(se)-1)+`K

`};s.backslashEscapesHTMLTags&&(i=i.replace(/\\<(\/?[^>]+?)>/g,function(T,B){return"&lt;"+B+"&gt;"}));for(var b=0;b<f.length;++b)for(var k,H=new RegExp("^ {0,3}(<"+f[b]+"\\b[^>]*>)","im"),j="<"+f[b]+"\\b[^>]*>",x="</"+f[b]+">";(k=E.helper.regexIndexOf(i,H))!==-1;){var I=E.helper.splitAtIndex(i,k),z=E.helper.replaceRecursiveRegExp(I[1],v,j,x,"im");if(z===I[1])break;i=I[0].concat(z)}return i=i.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,E.subParser("hashElement")(i,s,u)),i=E.helper.replaceRecursiveRegExp(i,function(T){return`

¨K`+(u.gHtmlBlocks.push(T)-1)+`K

`},"^ {0,3}<!--","-->","gm"),i=i.replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,E.subParser("hashElement")(i,s,u)),i=u.converter._dispatch("hashHTMLBlocks.after",i,s,u),i}),E.subParser("hashHTMLSpans",function(i,s,u){"use strict";i=u.converter._dispatch("hashHTMLSpans.before",i,s,u);function f(v){return"¨C"+(u.gHtmlSpans.push(v)-1)+"C"}return i=i.replace(/<[^>]+?\/>/gi,function(v){return f(v)}),i=i.replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,function(v){return f(v)}),i=i.replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,function(v){return f(v)}),i=i.replace(/<[^>]+?>/gi,function(v){return f(v)}),i=u.converter._dispatch("hashHTMLSpans.after",i,s,u),i}),E.subParser("unhashHTMLSpans",function(i,s,u){"use strict";i=u.converter._dispatch("unhashHTMLSpans.before",i,s,u);for(var f=0;f<u.gHtmlSpans.length;++f){for(var v=u.gHtmlSpans[f],b=0;/¨C(\d+)C/.test(v);){var k=RegExp.$1;if(v=v.replace("¨C"+k+"C",u.gHtmlSpans[k]),b===10)break;++b}i=i.replace("¨C"+f+"C",v)}return i=u.converter._dispatch("unhashHTMLSpans.after",i,s,u),i}),E.subParser("hashPreCodeTags",function(i,s,u){"use strict";i=u.converter._dispatch("hashPreCodeTags.before",i,s,u);var f=function(v,b,k,H){var j=k+E.subParser("encodeCode")(b,s,u)+H;return`

¨G`+(u.ghCodeBlocks.push({text:v,codeblock:j})-1)+`G

`};return i=E.helper.replaceRecursiveRegExp(i,f,"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),i=u.converter._dispatch("hashPreCodeTags.after",i,s,u),i}),E.subParser("headers",function(i,s,u){"use strict";i=u.converter._dispatch("headers.before",i,s,u);var f=isNaN(parseInt(s.headerLevelStart))?1:parseInt(s.headerLevelStart),v=s.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,b=s.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;i=i.replace(v,function(j,x){var I=E.subParser("spanGamut")(x,s,u),z=s.noHeaderId?"":' id="'+H(x)+'"',T=f,B="<h"+T+z+">"+I+"</h"+T+">";return E.subParser("hashBlock")(B,s,u)}),i=i.replace(b,function(j,x){var I=E.subParser("spanGamut")(x,s,u),z=s.noHeaderId?"":' id="'+H(x)+'"',T=f+1,B="<h"+T+z+">"+I+"</h"+T+">";return E.subParser("hashBlock")(B,s,u)});var k=s.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;i=i.replace(k,function(j,x,I){var z=I;s.customizedHeaderId&&(z=I.replace(/\s?\{([^{]+?)}\s*$/,""));var T=E.subParser("spanGamut")(z,s,u),B=s.noHeaderId?"":' id="'+H(I)+'"',q=f-1+x.length,J="<h"+q+B+">"+T+"</h"+q+">";return E.subParser("hashBlock")(J,s,u)});function H(j){var x,I;if(s.customizedHeaderId){var z=j.match(/\{([^{]+?)}\s*$/);z&&z[1]&&(j=z[1])}return x=j,E.helper.isString(s.prefixHeaderId)?I=s.prefixHeaderId:s.prefixHeaderId===!0?I="section-":I="",s.rawPrefixHeaderId||(x=I+x),s.ghCompatibleHeaderId?x=x.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():s.rawHeaderId?x=x.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-").toLowerCase():x=x.replace(/[^\w]/g,"").toLowerCase(),s.rawPrefixHeaderId&&(x=I+x),u.hashLinkCounts[x]?x=x+"-"+u.hashLinkCounts[x]++:u.hashLinkCounts[x]=1,x}return i=u.converter._dispatch("headers.after",i,s,u),i}),E.subParser("horizontalRule",function(i,s,u){"use strict";i=u.converter._dispatch("horizontalRule.before",i,s,u);var f=E.subParser("hashBlock")("<hr />",s,u);return i=i.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,f),i=i.replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,f),i=i.replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,f),i=u.converter._dispatch("horizontalRule.after",i,s,u),i}),E.subParser("images",function(i,s,u){"use strict";i=u.converter._dispatch("images.before",i,s,u);var f=/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,v=/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,b=/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,k=/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,H=/!\[([^\[\]]+)]()()()()()/g;function j(I,z,T,B,q,J,se,X){return B=B.replace(/\s/g,""),x(I,z,T,B,q,J,se,X)}function x(I,z,T,B,q,J,se,X){var O=u.gUrls,ve=u.gTitles,Pe=u.gDimensions;if(T=T.toLowerCase(),X||(X=""),I.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)B="";else if(B===""||B===null)if((T===""||T===null)&&(T=z.toLowerCase().replace(/ ?\n/g," ")),B="#"+T,!E.helper.isUndefined(O[T]))B=O[T],E.helper.isUndefined(ve[T])||(X=ve[T]),E.helper.isUndefined(Pe[T])||(q=Pe[T].width,J=Pe[T].height);else return I;z=z.replace(/"/g,"&quot;").replace(E.helper.regexes.asteriskDashAndColon,E.helper.escapeCharactersCallback),B=B.replace(E.helper.regexes.asteriskDashAndColon,E.helper.escapeCharactersCallback);var Le='<img src="'+B+'" alt="'+z+'"';return X&&E.helper.isString(X)&&(X=X.replace(/"/g,"&quot;").replace(E.helper.regexes.asteriskDashAndColon,E.helper.escapeCharactersCallback),Le+=' title="'+X+'"'),q&&J&&(q=q==="*"?"auto":q,J=J==="*"?"auto":J,Le+=' width="'+q+'"',Le+=' height="'+J+'"'),Le+=" />",Le}return i=i.replace(k,x),i=i.replace(b,j),i=i.replace(v,x),i=i.replace(f,x),i=i.replace(H,x),i=u.converter._dispatch("images.after",i,s,u),i}),E.subParser("italicsAndBold",function(i,s,u){"use strict";i=u.converter._dispatch("italicsAndBold.before",i,s,u);function f(v,b,k){return b+v+k}return s.literalMidWordUnderscores?(i=i.replace(/\b___(\S[\s\S]*?)___\b/g,function(v,b){return f(b,"<strong><em>","</em></strong>")}),i=i.replace(/\b__(\S[\s\S]*?)__\b/g,function(v,b){return f(b,"<strong>","</strong>")}),i=i.replace(/\b_(\S[\s\S]*?)_\b/g,function(v,b){return f(b,"<em>","</em>")})):(i=i.replace(/___(\S[\s\S]*?)___/g,function(v,b){return/\S$/.test(b)?f(b,"<strong><em>","</em></strong>"):v}),i=i.replace(/__(\S[\s\S]*?)__/g,function(v,b){return/\S$/.test(b)?f(b,"<strong>","</strong>"):v}),i=i.replace(/_([^\s_][\s\S]*?)_/g,function(v,b){return/\S$/.test(b)?f(b,"<em>","</em>"):v})),s.literalMidWordAsterisks?(i=i.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,function(v,b,k){return f(k,b+"<strong><em>","</em></strong>")}),i=i.replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,function(v,b,k){return f(k,b+"<strong>","</strong>")}),i=i.replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,function(v,b,k){return f(k,b+"<em>","</em>")})):(i=i.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,function(v,b){return/\S$/.test(b)?f(b,"<strong><em>","</em></strong>"):v}),i=i.replace(/\*\*(\S[\s\S]*?)\*\*/g,function(v,b){return/\S$/.test(b)?f(b,"<strong>","</strong>"):v}),i=i.replace(/\*([^\s*][\s\S]*?)\*/g,function(v,b){return/\S$/.test(b)?f(b,"<em>","</em>"):v})),i=u.converter._dispatch("italicsAndBold.after",i,s,u),i}),E.subParser("lists",function(i,s,u){"use strict";function f(k,H){u.gListLevel++,k=k.replace(/\n{2,}$/,`
`),k+="¨0";var j=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,x=/\n[ \t]*\n(?!¨0)/.test(k);return s.disableForced4SpacesIndentedSublists&&(j=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),k=k.replace(j,function(I,z,T,B,q,J,se){se=se&&se.trim()!=="";var X=E.subParser("outdent")(q,s,u),O="";return J&&s.tasklists&&(O=' class="task-list-item" style="list-style-type: none;"',X=X.replace(/^[ \t]*\[(x|X| )?]/m,function(){var ve='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return se&&(ve+=" checked"),ve+=">",ve})),X=X.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,function(ve){return"¨A"+ve}),z||X.search(/\n{2,}/)>-1?(X=E.subParser("githubCodeBlocks")(X,s,u),X=E.subParser("blockGamut")(X,s,u)):(X=E.subParser("lists")(X,s,u),X=X.replace(/\n$/,""),X=E.subParser("hashHTMLBlocks")(X,s,u),X=X.replace(/\n\n+/g,`

`),x?X=E.subParser("paragraphs")(X,s,u):X=E.subParser("spanGamut")(X,s,u)),X=X.replace("¨A",""),X="<li"+O+">"+X+`</li>
`,X}),k=k.replace(/¨0/g,""),u.gListLevel--,H&&(k=k.replace(/\s+$/,"")),k}function v(k,H){if(H==="ol"){var j=k.match(/^ *(\d+)\./);if(j&&j[1]!=="1")return' start="'+j[1]+'"'}return""}function b(k,H,j){var x=s.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,I=s.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,z=H==="ul"?x:I,T="";if(k.search(z)!==-1)(function q(J){var se=J.search(z),X=v(k,H);se!==-1?(T+=`

<`+H+X+`>
`+f(J.slice(0,se),!!j)+"</"+H+`>
`,H=H==="ul"?"ol":"ul",z=H==="ul"?x:I,q(J.slice(se))):T+=`

<`+H+X+`>
`+f(J,!!j)+"</"+H+`>
`})(k);else{var B=v(k,H);T=`

<`+H+B+`>
`+f(k,!!j)+"</"+H+`>
`}return T}return i=u.converter._dispatch("lists.before",i,s,u),i+="¨0",u.gListLevel?i=i.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(k,H,j){var x=j.search(/[*+-]/g)>-1?"ul":"ol";return b(H,x,!0)}):i=i.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(k,H,j,x){var I=x.search(/[*+-]/g)>-1?"ul":"ol";return b(j,I,!1)}),i=i.replace(/¨0/,""),i=u.converter._dispatch("lists.after",i,s,u),i}),E.subParser("metadata",function(i,s,u){"use strict";if(!s.metadata)return i;i=u.converter._dispatch("metadata.before",i,s,u);function f(v){u.metadata.raw=v,v=v.replace(/&/g,"&amp;").replace(/"/g,"&quot;"),v=v.replace(/\n {4}/g," "),v.replace(/^([\S ]+): +([\s\S]+?)$/gm,function(b,k,H){return u.metadata.parsed[k]=H,""})}return i=i.replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,function(v,b,k){return f(k),"¨M"}),i=i.replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,function(v,b,k){return b&&(u.metadata.format=b),f(k),"¨M"}),i=i.replace(/¨M/g,""),i=u.converter._dispatch("metadata.after",i,s,u),i}),E.subParser("outdent",function(i,s,u){"use strict";return i=u.converter._dispatch("outdent.before",i,s,u),i=i.replace(/^(\t|[ ]{1,4})/gm,"¨0"),i=i.replace(/¨0/g,""),i=u.converter._dispatch("outdent.after",i,s,u),i}),E.subParser("paragraphs",function(i,s,u){"use strict";i=u.converter._dispatch("paragraphs.before",i,s,u),i=i.replace(/^\n+/g,""),i=i.replace(/\n+$/g,"");for(var f=i.split(/\n{2,}/g),v=[],b=f.length,k=0;k<b;k++){var H=f[k];H.search(/¨(K|G)(\d+)\1/g)>=0?v.push(H):H.search(/\S/)>=0&&(H=E.subParser("spanGamut")(H,s,u),H=H.replace(/^([ \t]*)/g,"<p>"),H+="</p>",v.push(H))}for(b=v.length,k=0;k<b;k++){for(var j="",x=v[k],I=!1;/¨(K|G)(\d+)\1/.test(x);){var z=RegExp.$1,T=RegExp.$2;z==="K"?j=u.gHtmlBlocks[T]:I?j=E.subParser("encodeCode")(u.ghCodeBlocks[T].text,s,u):j=u.ghCodeBlocks[T].codeblock,j=j.replace(/\$/g,"$$$$"),x=x.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,j),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(x)&&(I=!0)}v[k]=x}return i=v.join(`
`),i=i.replace(/^\n+/g,""),i=i.replace(/\n+$/g,""),u.converter._dispatch("paragraphs.after",i,s,u)}),E.subParser("runExtension",function(i,s,u,f){"use strict";if(i.filter)s=i.filter(s,f.converter,u);else if(i.regex){var v=i.regex;v instanceof RegExp||(v=new RegExp(v,"g")),s=s.replace(v,i.replace)}return s}),E.subParser("spanGamut",function(i,s,u){"use strict";return i=u.converter._dispatch("spanGamut.before",i,s,u),i=E.subParser("codeSpans")(i,s,u),i=E.subParser("escapeSpecialCharsWithinTagAttributes")(i,s,u),i=E.subParser("encodeBackslashEscapes")(i,s,u),i=E.subParser("images")(i,s,u),i=E.subParser("anchors")(i,s,u),i=E.subParser("autoLinks")(i,s,u),i=E.subParser("simplifiedAutoLinks")(i,s,u),i=E.subParser("emoji")(i,s,u),i=E.subParser("underline")(i,s,u),i=E.subParser("italicsAndBold")(i,s,u),i=E.subParser("strikethrough")(i,s,u),i=E.subParser("ellipsis")(i,s,u),i=E.subParser("hashHTMLSpans")(i,s,u),i=E.subParser("encodeAmpsAndAngles")(i,s,u),s.simpleLineBreaks?/\n\n¨K/.test(i)||(i=i.replace(/\n+/g,`<br />
`)):i=i.replace(/  +\n/g,`<br />
`),i=u.converter._dispatch("spanGamut.after",i,s,u),i}),E.subParser("strikethrough",function(i,s,u){"use strict";function f(v){return s.simplifiedAutoLink&&(v=E.subParser("simplifiedAutoLinks")(v,s,u)),"<del>"+v+"</del>"}return s.strikethrough&&(i=u.converter._dispatch("strikethrough.before",i,s,u),i=i.replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,function(v,b){return f(b)}),i=u.converter._dispatch("strikethrough.after",i,s,u)),i}),E.subParser("stripLinkDefinitions",function(i,s,u){"use strict";var f=/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,v=/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm;i+="¨0";var b=function(k,H,j,x,I,z,T){return H=H.toLowerCase(),i.toLowerCase().split(H).length-1<2?k:(j.match(/^data:.+?\/.+?;base64,/)?u.gUrls[H]=j.replace(/\s/g,""):u.gUrls[H]=E.subParser("encodeAmpsAndAngles")(j,s,u),z?z+T:(T&&(u.gTitles[H]=T.replace(/"|'/g,"&quot;")),s.parseImgDimensions&&x&&I&&(u.gDimensions[H]={width:x,height:I}),""))};return i=i.replace(v,b),i=i.replace(f,b),i=i.replace(/¨0/,""),i}),E.subParser("tables",function(i,s,u){"use strict";if(!s.tables)return i;var f=/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,v=/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm;function b(I){return/^:[ \t]*--*$/.test(I)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(I)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(I)?' style="text-align:center;"':""}function k(I,z){var T="";return I=I.trim(),(s.tablesHeaderId||s.tableHeaderId)&&(T=' id="'+I.replace(/ /g,"_").toLowerCase()+'"'),I=E.subParser("spanGamut")(I,s,u),"<th"+T+z+">"+I+`</th>
`}function H(I,z){var T=E.subParser("spanGamut")(I,s,u);return"<td"+z+">"+T+`</td>
`}function j(I,z){for(var T=`<table>
<thead>
<tr>
`,B=I.length,q=0;q<B;++q)T+=I[q];for(T+=`</tr>
</thead>
<tbody>
`,q=0;q<z.length;++q){T+=`<tr>
`;for(var J=0;J<B;++J)T+=z[q][J];T+=`</tr>
`}return T+=`</tbody>
</table>
`,T}function x(I){var z,T=I.split(`
`);for(z=0;z<T.length;++z)/^ {0,3}\|/.test(T[z])&&(T[z]=T[z].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(T[z])&&(T[z]=T[z].replace(/\|[ \t]*$/,"")),T[z]=E.subParser("codeSpans")(T[z],s,u);var B=T[0].split("|").map(function(Le){return Le.trim()}),q=T[1].split("|").map(function(Le){return Le.trim()}),J=[],se=[],X=[],O=[];for(T.shift(),T.shift(),z=0;z<T.length;++z)T[z].trim()!==""&&J.push(T[z].split("|").map(function(Le){return Le.trim()}));if(B.length<q.length)return I;for(z=0;z<q.length;++z)X.push(b(q[z]));for(z=0;z<B.length;++z)E.helper.isUndefined(X[z])&&(X[z]=""),se.push(k(B[z],X[z]));for(z=0;z<J.length;++z){for(var ve=[],Pe=0;Pe<se.length;++Pe)E.helper.isUndefined(J[z][Pe]),ve.push(H(J[z][Pe],X[Pe]));O.push(ve)}return j(se,O)}return i=u.converter._dispatch("tables.before",i,s,u),i=i.replace(/\\(\|)/g,E.helper.escapeCharactersCallback),i=i.replace(f,x),i=i.replace(v,x),i=u.converter._dispatch("tables.after",i,s,u),i}),E.subParser("underline",function(i,s,u){"use strict";return s.underline&&(i=u.converter._dispatch("underline.before",i,s,u),s.literalMidWordUnderscores?(i=i.replace(/\b___(\S[\s\S]*?)___\b/g,function(f,v){return"<u>"+v+"</u>"}),i=i.replace(/\b__(\S[\s\S]*?)__\b/g,function(f,v){return"<u>"+v+"</u>"})):(i=i.replace(/___(\S[\s\S]*?)___/g,function(f,v){return/\S$/.test(v)?"<u>"+v+"</u>":f}),i=i.replace(/__(\S[\s\S]*?)__/g,function(f,v){return/\S$/.test(v)?"<u>"+v+"</u>":f})),i=i.replace(/(_)/g,E.helper.escapeCharactersCallback),i=u.converter._dispatch("underline.after",i,s,u)),i}),E.subParser("unescapeSpecialChars",function(i,s,u){"use strict";return i=u.converter._dispatch("unescapeSpecialChars.before",i,s,u),i=i.replace(/¨E(\d+)E/g,function(f,v){var b=parseInt(v);return String.fromCharCode(b)}),i=u.converter._dispatch("unescapeSpecialChars.after",i,s,u),i}),E.subParser("makeMarkdown.blockquote",function(i,s){"use strict";var u="";if(i.hasChildNodes())for(var f=i.childNodes,v=f.length,b=0;b<v;++b){var k=E.subParser("makeMarkdown.node")(f[b],s);k!==""&&(u+=k)}return u=u.trim(),u="> "+u.split(`
`).join(`
> `),u}),E.subParser("makeMarkdown.codeBlock",function(i,s){"use strict";var u=i.getAttribute("language"),f=i.getAttribute("precodenum");return"```"+u+`
`+s.preList[f]+"\n```"}),E.subParser("makeMarkdown.codeSpan",function(i){"use strict";return"`"+i.innerHTML+"`"}),E.subParser("makeMarkdown.emphasis",function(i,s){"use strict";var u="";if(i.hasChildNodes()){u+="*";for(var f=i.childNodes,v=f.length,b=0;b<v;++b)u+=E.subParser("makeMarkdown.node")(f[b],s);u+="*"}return u}),E.subParser("makeMarkdown.header",function(i,s,u){"use strict";var f=new Array(u+1).join("#"),v="";if(i.hasChildNodes()){v=f+" ";for(var b=i.childNodes,k=b.length,H=0;H<k;++H)v+=E.subParser("makeMarkdown.node")(b[H],s)}return v}),E.subParser("makeMarkdown.hr",function(){"use strict";return"---"}),E.subParser("makeMarkdown.image",function(i){"use strict";var s="";return i.hasAttribute("src")&&(s+="!["+i.getAttribute("alt")+"](",s+="<"+i.getAttribute("src")+">",i.hasAttribute("width")&&i.hasAttribute("height")&&(s+=" ="+i.getAttribute("width")+"x"+i.getAttribute("height")),i.hasAttribute("title")&&(s+=' "'+i.getAttribute("title")+'"'),s+=")"),s}),E.subParser("makeMarkdown.links",function(i,s){"use strict";var u="";if(i.hasChildNodes()&&i.hasAttribute("href")){var f=i.childNodes,v=f.length;u="[";for(var b=0;b<v;++b)u+=E.subParser("makeMarkdown.node")(f[b],s);u+="](",u+="<"+i.getAttribute("href")+">",i.hasAttribute("title")&&(u+=' "'+i.getAttribute("title")+'"'),u+=")"}return u}),E.subParser("makeMarkdown.list",function(i,s,u){"use strict";var f="";if(!i.hasChildNodes())return"";for(var v=i.childNodes,b=v.length,k=i.getAttribute("start")||1,H=0;H<b;++H)if(!(typeof v[H].tagName=="undefined"||v[H].tagName.toLowerCase()!=="li")){var j="";u==="ol"?j=k.toString()+". ":j="- ",f+=j+E.subParser("makeMarkdown.listItem")(v[H],s),++k}return f+=`
<!-- -->
`,f.trim()}),E.subParser("makeMarkdown.listItem",function(i,s){"use strict";for(var u="",f=i.childNodes,v=f.length,b=0;b<v;++b)u+=E.subParser("makeMarkdown.node")(f[b],s);return/\n$/.test(u)?u=u.split(`
`).join(`
    `).replace(/^ {4}$/gm,"").replace(/\n\n+/g,`

`):u+=`
`,u}),E.subParser("makeMarkdown.node",function(i,s,u){"use strict";u=u||!1;var f="";if(i.nodeType===3)return E.subParser("makeMarkdown.txt")(i,s);if(i.nodeType===8)return"<!--"+i.data+`-->

`;if(i.nodeType!==1)return"";var v=i.tagName.toLowerCase();switch(v){case"h1":u||(f=E.subParser("makeMarkdown.header")(i,s,1)+`

`);break;case"h2":u||(f=E.subParser("makeMarkdown.header")(i,s,2)+`

`);break;case"h3":u||(f=E.subParser("makeMarkdown.header")(i,s,3)+`

`);break;case"h4":u||(f=E.subParser("makeMarkdown.header")(i,s,4)+`

`);break;case"h5":u||(f=E.subParser("makeMarkdown.header")(i,s,5)+`

`);break;case"h6":u||(f=E.subParser("makeMarkdown.header")(i,s,6)+`

`);break;case"p":u||(f=E.subParser("makeMarkdown.paragraph")(i,s)+`

`);break;case"blockquote":u||(f=E.subParser("makeMarkdown.blockquote")(i,s)+`

`);break;case"hr":u||(f=E.subParser("makeMarkdown.hr")(i,s)+`

`);break;case"ol":u||(f=E.subParser("makeMarkdown.list")(i,s,"ol")+`

`);break;case"ul":u||(f=E.subParser("makeMarkdown.list")(i,s,"ul")+`

`);break;case"precode":u||(f=E.subParser("makeMarkdown.codeBlock")(i,s)+`

`);break;case"pre":u||(f=E.subParser("makeMarkdown.pre")(i,s)+`

`);break;case"table":u||(f=E.subParser("makeMarkdown.table")(i,s)+`

`);break;case"code":f=E.subParser("makeMarkdown.codeSpan")(i,s);break;case"em":case"i":f=E.subParser("makeMarkdown.emphasis")(i,s);break;case"strong":case"b":f=E.subParser("makeMarkdown.strong")(i,s);break;case"del":f=E.subParser("makeMarkdown.strikethrough")(i,s);break;case"a":f=E.subParser("makeMarkdown.links")(i,s);break;case"img":f=E.subParser("makeMarkdown.image")(i,s);break;default:f=i.outerHTML+`

`}return f}),E.subParser("makeMarkdown.paragraph",function(i,s){"use strict";var u="";if(i.hasChildNodes())for(var f=i.childNodes,v=f.length,b=0;b<v;++b)u+=E.subParser("makeMarkdown.node")(f[b],s);return u=u.trim(),u}),E.subParser("makeMarkdown.pre",function(i,s){"use strict";var u=i.getAttribute("prenum");return"<pre>"+s.preList[u]+"</pre>"}),E.subParser("makeMarkdown.strikethrough",function(i,s){"use strict";var u="";if(i.hasChildNodes()){u+="~~";for(var f=i.childNodes,v=f.length,b=0;b<v;++b)u+=E.subParser("makeMarkdown.node")(f[b],s);u+="~~"}return u}),E.subParser("makeMarkdown.strong",function(i,s){"use strict";var u="";if(i.hasChildNodes()){u+="**";for(var f=i.childNodes,v=f.length,b=0;b<v;++b)u+=E.subParser("makeMarkdown.node")(f[b],s);u+="**"}return u}),E.subParser("makeMarkdown.table",function(i,s){"use strict";var u="",f=[[],[]],v=i.querySelectorAll("thead>tr>th"),b=i.querySelectorAll("tbody>tr"),k,H;for(k=0;k<v.length;++k){var j=E.subParser("makeMarkdown.tableCell")(v[k],s),x="---";if(v[k].hasAttribute("style")){var I=v[k].getAttribute("style").toLowerCase().replace(/\s/g,"");switch(I){case"text-align:left;":x=":---";break;case"text-align:right;":x="---:";break;case"text-align:center;":x=":---:";break}}f[0][k]=j.trim(),f[1][k]=x}for(k=0;k<b.length;++k){var z=f.push([])-1,T=b[k].getElementsByTagName("td");for(H=0;H<v.length;++H){var B=" ";typeof T[H]!="undefined"&&(B=E.subParser("makeMarkdown.tableCell")(T[H],s)),f[z].push(B)}}var q=3;for(k=0;k<f.length;++k)for(H=0;H<f[k].length;++H){var J=f[k][H].length;J>q&&(q=J)}for(k=0;k<f.length;++k){for(H=0;H<f[k].length;++H)k===1?f[k][H].slice(-1)===":"?f[k][H]=E.helper.padEnd(f[k][H].slice(-1),q-1,"-")+":":f[k][H]=E.helper.padEnd(f[k][H],q,"-"):f[k][H]=E.helper.padEnd(f[k][H],q);u+="| "+f[k].join(" | ")+` |
`}return u.trim()}),E.subParser("makeMarkdown.tableCell",function(i,s){"use strict";var u="";if(!i.hasChildNodes())return"";for(var f=i.childNodes,v=f.length,b=0;b<v;++b)u+=E.subParser("makeMarkdown.node")(f[b],s,!0);return u.trim()}),E.subParser("makeMarkdown.txt",function(i){"use strict";var s=i.nodeValue;return s=s.replace(/ +/g," "),s=s.replace(/¨NBSP;/g," "),s=E.helper.unescapeHTMLEntities(s),s=s.replace(/([*_~|`])/g,"\\$1"),s=s.replace(/^(\s*)>/g,"\\$1>"),s=s.replace(/^#/gm,"\\#"),s=s.replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3"),s=s.replace(/^( {0,3}\d+)\./gm,"$1\\."),s=s.replace(/^( {0,3})([+-])/gm,"$1\\$2"),s=s.replace(/]([\s]*)\(/g,"\\]$1\\("),s=s.replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:"),s});var S=this;Ee.exports?Ee.exports=E:S.showdown=E}).call(fi)}(Ot)),Ot.exports}var hi=pi();const mi=ar(hi),gi=["innerHTML"],bi=er({__name:"MarkdownViewer",props:{value:{type:String},class:{type:String}},setup(Ee){const me=new mi.Converter;me.setOption("tables",!0),me.setOption("emoji",!0);const ge=Ee,E=tr(()=>me.makeHtml(ge.value||"")),re=wt(!1),{getDarkMode:Me}=Xn();return Yt(()=>Me.value,Q=>{re.value=Q===Qa.DARK},{immediate:!0}),(Q,M)=>(rr(),nr("div",{class:$n(["preview",[{preview_dark:re.value}]])},[ni("div",{innerHTML:E.value,class:$n([Q.$props.class,"markdown-viewer vditor-reset"])},null,10,gi)],2))}}),wi=Qn(bi,[["__scopeId","data-v-55510d41"]]),Ci=Yn(di),Mi=Yn(wi);export{Ci as M,Mi as a};
