import{d as c,ag as i,aB as d,ar as u,aD as r,k as p,G as n}from"./vue-vendor-dy9k-Yad.js";import{u as f}from"./index-BkGZ5fiW.js";import{getBasicColumns as _}from"./tableData-B4J3mkj4.js";import{P as g}from"./index-CtJ0w2CP.js";import{d as C}from"./table-BDFKJhHv.js";import b from"./BasicTable-xCEZpGLb.js";import{a as k}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const B=c({components:{BasicTable:b,PageWrapper:g},setup(){const[t,{reload:o}]=f({title:"远程加载示例",api:C,columns:_(),pagination:{pageSize:10}});function e(){o()}function m(){o({page:1})}return{registerTable:t,handleReloadCurrent:e,handleReload:m}}});function T(t,o,e,m,R,h){const a=i("a-button"),s=i("BasicTable"),l=i("PageWrapper");return u(),d(l,{contentBackground:"",contentClass:"flex",dense:"",contentFullHeight:"",fixedHeight:""},{default:r(()=>[p(s,{onRegister:t.registerTable},{toolbar:r(()=>[p(a,{type:"primary",onClick:t.handleReloadCurrent},{default:r(()=>o[0]||(o[0]=[n(" 刷新当前页 ")])),_:1,__:[0]},8,["onClick"]),p(a,{type:"primary",onClick:t.handleReload},{default:r(()=>o[1]||(o[1]=[n(" 刷新并返回第一页 ")])),_:1,__:[1]},8,["onClick"])]),_:1},8,["onRegister"])]),_:1})}const wo=k(B,[["render",T]]);export{wo as default};
