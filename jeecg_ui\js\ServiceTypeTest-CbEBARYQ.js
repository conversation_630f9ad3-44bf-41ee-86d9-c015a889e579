var m=(_,o,e)=>new Promise((f,y)=>{var c=r=>{try{d(e.next(r))}catch(s){y(s)}},v=r=>{try{d(e.throw(r))}catch(s){y(s)}},d=r=>r.done?f(r.value):Promise.resolve(r.value).then(c,v);d((e=e.apply(_,o)).next())});import{d as A,ap as I,f as T,ag as g,aq as P,ar as h,at as n,k as a,aD as l,G as p,au as S}from"./vue-vendor-dy9k-Yad.js";import{f as i}from"./antd-vue-vendor-me9YkNVC.js";import{q as E}from"./entrustBidding-7diNZrW8.js";import{q as k}from"./autonomouslyBidding-DdSjnKHD.js";import{a as q}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const C={class:"service-type-test"},O={class:"test-section"},B={class:"test-buttons"},b={class:"test-section"},x={class:"test-buttons"},N={class:"test-section"},$={class:"test-buttons"},w={class:"test-result"},V=A({__name:"ServiceTypeTest",setup(_){const o=I(),e=T("");function f(){return m(this,null,function*(){try{e.value="正在测试委托竞价API...";const s=yield E("test-id-001");e.value=`委托竞价API测试结果:
${JSON.stringify(s,null,2)}`,s&&s.hgyEntrustOrder&&s.hgyAuctionItemTemp?e.value+=`

✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyAuctionItemTemp`:e.value+=`

❌ 数据结构异常：缺少必要字段`,i.success("委托竞价API测试完成")}catch(s){e.value=`委托竞价API测试失败: ${s}`,i.error("委托竞价API测试失败")}})}function y(){return m(this,null,function*(){try{e.value="正在测试自主竞价API...";const s=yield k("test-id-002");e.value=`自主竞价API测试结果:
${JSON.stringify(s,null,2)}`,s&&s.hgyEntrustOrder&&s.hgyAuctionItemTemp?(e.value+=`

✅ 数据结构正确：包含 hgyEntrustOrder 和 hgyAuctionItemTemp`,s.hgyAuction&&(e.value+=`
✅ 包含 hgyAuction（自主委托特有）`)):e.value+=`

❌ 数据结构异常：缺少必要字段`,i.success("自主竞价API测试完成")}catch(s){e.value=`自主竞价API测试失败: ${s}`,i.error("自主竞价API测试失败")}})}function c(){e.value=`测试增值委托页面 - 服务类型1
跳转到增值委托页面...`,o.push({path:"/entrust/appreciationEntrust",query:{id:"test-appreciation-001",serviceType:1}}),i.info("跳转到增值委托页面 - 服务类型1")}function v(){e.value=`测试增值委托页面 - 服务类型2
跳转到增值委托页面...`,o.push({path:"/entrust/appreciationEntrust",query:{id:"test-appreciation-002",serviceType:2}}),i.info("跳转到增值委托页面 - 服务类型2")}function d(){e.value=`测试自主委托页面 - 服务类型1
跳转到自主委托页面...`,o.push({path:"/entrust/selfEntrust",query:{id:"test-self-001",serviceType:1}}),i.info("跳转到自主委托页面 - 服务类型1")}function r(){e.value=`测试自主委托页面 - 服务类型2
跳转到自主委托页面...`,o.push({path:"/entrust/selfEntrust",query:{id:"test-self-002",serviceType:2}}),i.info("跳转到自主委托页面 - 服务类型2")}return(s,t)=>{const u=g("a-button");return h(),P("div",C,[t[10]||(t[10]=n("h2",null,"服务类型接口测试",-1)),n("div",O,[t[2]||(t[2]=n("h3",null,"API接口测试",-1)),n("div",B,[a(u,{type:"primary",onClick:f},{default:l(()=>t[0]||(t[0]=[p(" 测试委托竞价API ")])),_:1,__:[0]}),a(u,{type:"primary",onClick:y},{default:l(()=>t[1]||(t[1]=[p(" 测试自主竞价API ")])),_:1,__:[1]})])]),n("div",b,[t[5]||(t[5]=n("h3",null,"增值委托页面测试",-1)),n("div",x,[a(u,{type:"primary",onClick:c},{default:l(()=>t[3]||(t[3]=[p(" 测试增值委托 - 服务类型1 ")])),_:1,__:[3]}),a(u,{type:"default",onClick:v},{default:l(()=>t[4]||(t[4]=[p(" 测试增值委托 - 服务类型2 ")])),_:1,__:[4]})])]),n("div",N,[t[8]||(t[8]=n("h3",null,"自主委托页面测试",-1)),n("div",$,[a(u,{type:"primary",onClick:d},{default:l(()=>t[6]||(t[6]=[p(" 测试自主委托 - 服务类型1 ")])),_:1,__:[6]}),a(u,{type:"default",onClick:r},{default:l(()=>t[7]||(t[7]=[p(" 测试自主委托 - 服务类型2 ")])),_:1,__:[7]})])]),n("div",w,[t[9]||(t[9]=n("h3",null,"测试结果",-1)),n("pre",null,S(e.value),1)])])}}}),H=q(V,[["__scopeId","data-v-40e4f837"]]);export{H as default};
