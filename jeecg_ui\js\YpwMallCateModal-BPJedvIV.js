var D=Object.defineProperty;var F=Object.getOwnPropertySymbols;var O=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var _=(s,t,o)=>t in s?D(s,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[t]=o,w=(s,t)=>{for(var o in t||(t={}))O.call(t,o)&&_(s,o,t[o]);if(F)for(var o of F(t))R.call(t,o)&&_(s,o,t[o]);return s};var v=(s,t,o)=>new Promise((l,c)=>{var n=p=>{try{m(o.next(p))}catch(f){c(f)}},u=p=>{try{m(o.throw(p))}catch(f){c(f)}},m=p=>p.done?l(p.value):Promise.resolve(p.value).then(n,u);m((o=o.apply(s,t)).next())});import{d as U,f as d,e as I,u as i,aB as L,ar as S,aD as T,k as Y,aE as j}from"./vue-vendor-dy9k-Yad.js";import{B as A}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{i as E,j as K,k as V}from"./YpwMallCate.api-BlqWuhnE.js";import{u as G}from"./useForm-CgkFTrrO.js";import{ac as H,a as N}from"./index-CCWaWN5g.js";import{B as $}from"./BasicForm-DBcXiHk0.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./renderUtils-D7XVOFwj.js";const q=U({__name:"YpwMallCateModal",emits:["register","success"],setup(s,{emit:t}){const o=t,l=d(!0),c=d(!1),n=d([]),u=d([]);let m=null;const[p,{setProps:f,resetFields:y,setFieldsValue:B,validate:M,updateSchema:z,scrollToField:k}]=G({schemas:K,showActionButtonGroup:!1,baseColProps:{span:24},labelCol:{xs:{span:24},sm:{span:4}},wrapperCol:{xs:{span:24},sm:{span:18}}}),[x,{setModalProps:h,closeModal:C}]=H(e=>v(null,null,function*(){yield y(),n.value=[],h({confirmLoading:!1,minHeight:80,showOkBtn:!(e!=null&&e.hideFooter)}),l.value=!!(e!=null&&e.isUpdate),c.value=!!(e!=null&&e.showFooter),e!=null&&e.record?(m=e.record,yield B(w({},e.record))):m=null,u.value=yield V({async:!1,pcode:""}),f({disabled:!!(e!=null&&e.hideFooter)})})),P=I(()=>i(l)?i(c)?"编辑":"详情":"新增");function g(e,r){if(e&&r&&r.length>0)for(let a=0;a<r.length;a++)r[a].key==e&&i(n).indexOf(e)<0?(n.value.push(r[a].key),g(r[a].parentId,i(u))):g(e,r[a].children)}function b(){return v(this,null,function*(){try{let e=yield M();h({confirmLoading:!0}),yield E(e,l.value),C(),yield g(e.pid,i(u)),o("success",{isUpdate:i(l),values:w({},e),expandedArr:i(n).reverse(),changeParent:m!=null&&m.pid!=e.pid})}catch({errorFields:e}){if(e){const r=e[0];r&&k(r.name,{behavior:"smooth",block:"center"})}return Promise.reject(e)}finally{h({confirmLoading:!1})}})}return(e,r)=>(S(),L(i(A),j(e.$attrs,{onRegister:i(x),destroyOnClose:"",width:800,title:P.value,onOk:b}),{default:T(()=>[Y(i($),{onRegister:i(p),name:"YpwMallCateForm"},null,8,["onRegister"])]),_:1},16,["onRegister","title"]))}}),Xe=N(q,[["__scopeId","data-v-1907a259"]]);export{Xe as default};
