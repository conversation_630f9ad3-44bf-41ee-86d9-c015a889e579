var n=(l,m,o)=>new Promise((p,e)=>{var a=t=>{try{i(o.next(t))}catch(r){e(r)}},s=t=>{try{i(o.throw(t))}catch(r){e(r)}},i=t=>t.done?p(t.value):Promise.resolve(t.value).then(a,s);i((o=o.apply(l,m)).next())});import{d as _,ag as h,aq as F,ar as b,at as x,k as c,aD as u,G as d,u as f,F as S}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as g}from"./useForm-CgkFTrrO.js";import{B}from"./BasicForm-DBcXiHk0.js";import{a as k}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const v={style:{margin:"20px auto","text-align":"center"}},w=_({__name:"BasicFormSchemas",setup(l){const m=[{field:"visitor",label:"来访人员",component:"Input",componentProps:{disabled:!0}},{field:"accessed",label:"来访日期",component:"DatePicker",ifShow:!1},{field:"phone",label:"来访人手机号",component:"Input",required:!0}],[o,{updateSchema:p,resetSchema:e}]=g({schemas:m,showActionButtonGroup:!1,labelWidth:"150px",autoFocusFirstItem:!0});function a(){return n(this,null,function*(){yield e([{field:"visitor",label:"来访人员",component:"Input"}])})}function s(){return n(this,null,function*(){yield p([{field:"visitor",componentProps:{disabled:!1}},{field:"accessed",ifShow:!0}])})}return(i,t)=>{const r=h("a-button");return b(),F(S,null,[x("div",v,[c(r,{onClick:s,class:"mr-2"},{default:u(()=>t[0]||(t[0]=[d(" 更新字段属性 ")])),_:1,__:[0]}),c(r,{onClick:a,class:"mr-2"},{default:u(()=>t[1]||(t[1]=[d(" 重置字段属性 ")])),_:1,__:[1]})]),c(f(B),{onRegister:f(o),style:{"margin-top":"20px"}},null,8,["onRegister"])],64)}}}),It=k(w,[["__scopeId","data-v-23698c6a"]]);export{It as default};
