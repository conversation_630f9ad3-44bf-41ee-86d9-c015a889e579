import{d as r,aB as n,ar as i,u as a}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{i as l}from"./antd-vue-vendor-me9YkNVC.js";import{u as p}from"./useForm-CgkFTrrO.js";import{B as m}from"./BasicForm-DBcXiHk0.js";import{a as c}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const s=[{label:"文本框",field:"name",component:"Input",componentProps:{prefix:"中文",showCount:!0},defaultValue:"张三"},{label:"密码",field:"password",component:"InputPassword",componentProps:{visibilityToggle:!0,prefix:"密码"}},{label:"搜索框",field:"searchBox",component:"InputSearch",componentProps:{onSearch:e=>{}}},{label:"文本域",field:"textArea",component:"InputTextArea",componentProps:{allowClear:!0,showCount:!0,autoSize:{minRows:2,maxRows:3}}},{label:"数值输入框",field:"number",component:"InputNumber",componentProps:{addonAfter:"保留两位小数",max:100,precision:2,step:.1}},{label:"下拉框",field:"jinputtype",component:"Select",componentProps:{options:[{value:"like",label:"模糊（like）"},{value:"ne",label:"不等于（ne）"},{value:"ge",label:"大于等于（ge）"},{value:"le",label:"小于等于（le)"}],mode:"multiple",showSearch:!0}},{field:"TreeSelect",label:"下拉树",component:"TreeSelect",componentProps:{treeCheckable:!0,title:"下拉树",treeData:[{label:"洗衣机",value:"0",children:[{label:"滚筒洗衣机",value:"0-1"}]},{label:"电视机",value:"1",children:[{label:"平板电视",value:"1-1",disabled:!0},{label:"CRT电视机",value:"1-2"},{label:"投影电视",value:"1-3"}]}]}},{label:"RadioButtonGroup组件",field:"status",component:"RadioButtonGroup",componentProps:{options:[{label:"有效",value:1},{label:"无效",value:0}]}},{label:"单选框",field:"radioSex",component:"RadioGroup",componentProps:{options:[{label:"男",value:1,disabled:!1},{label:"女",value:0}]}},{label:"多选框",field:"checkbox",component:"Checkbox",componentProps:{disabled:!1}},{label:"多选框组",field:"checkSex",component:"CheckboxGroup",componentProps:{name:"爱好",options:[{label:"运动",value:0,disabled:!0},{label:"听音乐",value:1},{label:"看书",value:2}]},defaultValue:[2]},{label:"自动完成组件",field:"AutoComplete",component:"AutoComplete",componentProps:{options:[{value:"Burns Bay Road"},{value:"Downing Street"},{value:"Wall Street"}]}},{label:"级联选择",field:"cascade",component:"Cascader",componentProps:{maxTagCount:2,placement:"bottomRight",showSearch:!0,options:[{label:"北京",value:"BeiJin",children:[{label:"海淀区",value:"HaiDian"}]},{label:"江苏省",value:"JiangSu",children:[{label:"南京",value:"Nanjing",children:[{label:"中华门",value:"ZhongHuaMen"}]}]}]}},{label:"日期选择",field:"dateSelect",component:"DatePicker",componentProps:{format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",showToday:!0,disabledDate:e=>{let o=l(e).format("YYYY-MM-DD"),t=l(new Date).format("YYYY-MM-DD");return o==t}}},{label:"月份选择",field:"monthSelect",component:"MonthPicker",componentProps:{disabledDate:e=>{let o=l(e).format("YYYY-MM"),t=l(new Date).format("YYYY-MM");return o==t}}},{label:"周选择",field:"weekSelect",component:"WeekPicker",componentProps:{size:"small"}},{label:"时间选择",field:"timeSelect",component:"TimePicker",componentProps:{size:"default",showNow:!0}},{label:"日期时间范围",field:"dateTimeRangeSelect",component:"RangePicker",componentProps:{showTime:!0,format:"YYYY/MM/DD HH:mm:ss",placeholder:["请选择开始日期时间","请选择结束日期时间"]}},{label:"日期范围",field:"dateRangeSelect",component:"RangeDate",componentProps:{format:"YYYY/MM/DD",placeholder:["请选择开始日期","请选择结束日期"]}},{label:"时间范围",field:"timeRangeSelect",component:"RangeTime",componentProps:{format:"HH/mm/ss",placeholder:["请选择开始时间","请选择结束时间"]}},{label:"开关",field:"switch",component:"Switch",componentProps:{size:"default",unCheckedChildren:"开启",unCheckedValue:"0",checkedChildren:"关闭",checkedValue:"1",disabled:!1}},{label:"滑动输入条",field:"slider",component:"Slider",componentProps:{min:-20,max:100,range:!0,marks:{"-20":"-20°C",0:"0°C",26:"26°C",37:"37°C",100:{style:{color:"#f50"},label:"100°C"}}}},{label:"评分",field:"rate",component:"Rate",componentProps:{allowHalf:!0,count:5,tooltips:["非常差","较差","正常","很好","非很好"]}},{label:"分割线",field:"divisionLine",component:"Divider",componentProps:{dashed:!1,orientation:"center",plain:!0,type:"horizontal"}}],u=r({__name:"BasicFormComponent",setup(e){const[o,{getFieldsValue:t,setFieldsValue:d,resetFields:b,validate:f}]=p({schemas:s,labelWidth:"150px",showActionButtonGroup:!1,autoFocusFirstItem:!0});return(h,v)=>(i(),n(a(m),{onRegister:a(o),style:{"margin-top":"20px"}},null,8,["onRegister"]))}}),Ye=c(u,[["__scopeId","data-v-43f0453f"]]);export{Ye as default};
