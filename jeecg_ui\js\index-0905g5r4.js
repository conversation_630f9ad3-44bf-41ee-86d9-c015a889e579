var v=Object.defineProperty,C=Object.defineProperties;var _=Object.getOwnPropertyDescriptors;var i=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable;var s=(t,e,o)=>e in t?v(t,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[e]=o,u=(t,e)=>{for(var o in e||(e={}))w.call(e,o)&&s(t,o,e[o]);if(i)for(var o of i(e))g.call(e,o)&&s(t,o,e[o]);return t},d=(t,e)=>C(t,_(e));import{d as T,r as b,I as B,ag as m,aB as P,ar as $,aD as a,k as l,at as r}from"./vue-vendor-dy9k-Yad.js";import{P as k}from"./index-CtJ0w2CP.js";import{T as W}from"./index-CXHeQyuE.js";import{C as c}from"./index-LCGLvkB3.js";import{a as D}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";const N=T({components:{PageWrapper:k,Time:W,CollapseContainer:c},setup(){const t=new Date().getTime(),e=b({time1:t-60*3*1e3,time2:t-86400*3*1e3});return d(u({},B(e)),{now:t})}});function V(t,e,o,y,I,R){const n=m("Time"),p=m("CollapseContainer"),f=m("PageWrapper");return $(),P(f,{title:"时间组件示例"},{default:a(()=>[l(p,{title:"基础示例"},{default:a(()=>[l(n,{value:t.time1},null,8,["value"]),e[0]||(e[0]=r("br",null,null,-1)),l(n,{value:t.time2},null,8,["value"])]),_:1,__:[0]}),l(p,{title:"定时更新",class:"my-4"},{default:a(()=>[l(n,{value:t.now,step:1},null,8,["value"]),e[1]||(e[1]=r("br",null,null,-1)),l(n,{value:t.now,step:5},null,8,["value"])]),_:1,__:[1]}),l(p,{title:"定时更新"},{default:a(()=>[l(n,{value:t.now,mode:"date"},null,8,["value"]),e[2]||(e[2]=r("br",null,null,-1)),l(n,{value:t.now,mode:"datetime"},null,8,["value"]),e[3]||(e[3]=r("br",null,null,-1)),l(n,{value:t.now},null,8,["value"])]),_:1,__:[2,3]})]),_:1})}const Z=D(N,[["render",V]]);export{Z as default};
