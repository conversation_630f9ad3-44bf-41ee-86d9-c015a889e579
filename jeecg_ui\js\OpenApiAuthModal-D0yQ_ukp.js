import{d as k,f as e,aB as b,ar as h,aD as _,k as x,n as r}from"./vue-vendor-dy9k-Yad.js";import B from"./OpenApiAuthForm-B8oZ9C_1.js";import{J as C}from"./JModal-CqNGp5k5.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./JFormContainer-BUU5aWZC.js";import"./JSearchSelect-c_lfTydU.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";const S=k({__name:"OpenApiAuthModal",emits:["register","success"],setup(O,{expose:n,emit:u}){const t=e(""),c=e(800),a=e(!1),o=e(!1),i=e(),m=u;function p(){t.value="新增",a.value=!0,r(()=>{i.value.add()})}function d(s){t.value=o.value?"详情":"编辑",a.value=!0,r(()=>{i.value.edit(s)})}function f(){i.value.submitForm()}function v(){l(),m("success")}function l(){a.value=!1}return n({add:p,edit:d,disableSubmit:o}),(s,w)=>(h(),b(C,{title:t.value,width:c.value,visible:a.value,onOk:f,okButtonProps:{class:{"jee-hidden":o.value}},onCancel:l,cancelText:"关闭"},{default:_(()=>[x(B,{ref_key:"registerForm",ref:i,onOk:v,title:t.value,formDisabled:o.value,formBpm:!1},null,8,["title","formDisabled"])]),_:1},8,["title","width","visible","okButtonProps"]))}});export{S as default};
