var k=(t,o,i)=>new Promise((c,f)=>{var g=l=>{try{s(i.next(l))}catch(u){f(u)}},m=l=>{try{s(i.throw(l))}catch(u){f(u)}},s=l=>l.done?c(l.value):Promise.resolve(l.value).then(g,m);s((i=i.apply(t,o)).next())});import{d as B,f as M,r as D,e as V,u as U,ag as r,aB as x,ar as O,aE as j,aD as a,k as e,G as y}from"./vue-vendor-dy9k-Yad.js";import{B as Y}from"./index-Diw57m_E.js";import{ac as A,a as E}from"./index-CCWaWN5g.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";const I=B({name:"VexTableModal",components:{BasicModal:Y},emits:["success","register"],setup(t,{emit:o}){const i=M(!0),c=M({}),f=M(""),g=M(),m=D({xs:{span:24},sm:{span:5}}),s=D({xs:{span:24},sm:{span:16}}),l=M([{label:"",value:""},{label:"男",value:"1"},{label:"女",value:"2"}]),u={orderCode:[{required:!0,message:"订单号不能为空",trigger:"blur"}]},v=D({id:null,orderCode:"",orderMoney:"",ctype:"",content:"",jeecgOrderCustomerList:[],jeecgOrderTicketList:[]}),[T,{setModalProps:_,closeModal:w}]=A(d=>k(null,null,function*(){v.orderCode="",_({confirmLoading:!1}),i.value=!!(d!=null&&d.isUpdate),U(i)&&(f.value=d.record.id,v.orderCode=d.record.orderCode)})),$=M([]),p=V(()=>U(i)?"编辑":"新增");function L(d){return k(this,null,function*(){const b=c.value,R={name:"",sex:"1",idcard:"",telphone:""},{row:n}=yield b.insertAt(R,d);yield b.setActiveCell(n,"sex")})}function C(){return k(this,null,function*(){g.value.validate().then(()=>{try{const d=c.value,{fullData:b}=d.getTableData();v.jeecgOrderCustomerList=b,_({confirmLoading:!0}),w(),o("success",{isUpdate:U(i),values:{id:f.value}})}finally{_({confirmLoading:!1})}}).catch(d=>{})})}return{xTable:c,tableData:$,sexList:l,formRef:g,validatorRules:u,orderMainModel:v,registerModal:T,getTitle:p,labelCol:m,wrapperCol:s,insertEvent:L,handleSubmit:C}}});function S(t,o,i,c,f,g){const m=r("a-input"),s=r("a-form-item"),l=r("a-col"),u=r("a-select-option"),v=r("a-select"),T=r("a-date-picker"),_=r("a-row"),w=r("vxe-button"),$=r("vxe-toolbar"),p=r("vxe-column"),L=r("vxe-table"),C=r("a-tab-pane"),d=r("a-tabs"),b=r("a-form"),R=r("BasicModal");return O(),x(R,j(t.$attrs,{onRegister:t.registerModal,title:t.getTitle,onOk:t.handleSubmit,width:"70%"}),{default:a(()=>[e(b,{ref:"formRef",model:t.orderMainModel,"label-col":t.labelCol,"wrapper-col":t.wrapperCol,rules:t.validatorRules},{default:a(()=>[e(_,{class:"form-row",gutter:16},{default:a(()=>[e(l,{lg:8},{default:a(()=>[e(s,{label:"订单号",name:"orderCode"},{default:a(()=>[e(m,{value:t.orderMainModel.orderCode,"onUpdate:value":o[0]||(o[0]=n=>t.orderMainModel.orderCode=n),placeholder:"请输入订单号"},null,8,["value"])]),_:1})]),_:1}),e(l,{lg:8},{default:a(()=>[e(s,{label:"订单类型"},{default:a(()=>[e(v,{placeholder:"请选择订单类型",value:t.orderMainModel.ctype,"onUpdate:value":o[1]||(o[1]=n=>t.orderMainModel.ctype=n)},{default:a(()=>[e(u,{value:"1"},{default:a(()=>o[7]||(o[7]=[y("国内订单")])),_:1,__:[7]}),e(u,{value:"2"},{default:a(()=>o[8]||(o[8]=[y("国际订单")])),_:1,__:[8]})]),_:1},8,["value"])]),_:1})]),_:1}),e(l,{lg:8},{default:a(()=>[e(s,{label:"订单日期"},{default:a(()=>[e(T,{showTime:"",valueFormat:"YYYY-MM-DD HH:mm:ss",value:t.orderMainModel.orderDate,"onUpdate:value":o[2]||(o[2]=n=>t.orderMainModel.orderDate=n)},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(_,{class:"form-row",gutter:16},{default:a(()=>[e(l,{lg:8},{default:a(()=>[e(s,{label:"订单金额"},{default:a(()=>[e(m,{value:t.orderMainModel.orderMoney,"onUpdate:value":o[3]||(o[3]=n=>t.orderMainModel.orderMoney=n),placeholder:"请输入订单金额"},null,8,["value"])]),_:1})]),_:1}),e(l,{lg:8},{default:a(()=>[e(s,{label:"订单备注"},{default:a(()=>[e(m,{value:t.orderMainModel.content,"onUpdate:value":o[4]||(o[4]=n=>t.orderMainModel.content=n),placeholder:"请输入订单备注"},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(d,{defaultActiveKey:"1"},{default:a(()=>[e(C,{tab:"客户信息",key:"1"},{default:a(()=>[e($,null,{buttons:a(()=>[e(w,{icon:"fa fa-plus",onClick:o[5]||(o[5]=n=>t.insertEvent())},{default:a(()=>o[9]||(o[9]=[y("新增")])),_:1,__:[9]}),e(w,{onClick:o[6]||(o[6]=n=>t.$refs.xTable.removeCheckboxRow())},{default:a(()=>o[10]||(o[10]=[y("删除选中")])),_:1,__:[10]})]),_:1}),e(L,{border:"","show-overflow":"","keep-source":"",ref:"xTable","max-height":"400",data:t.orderMainModel.jeecgOrderCustomerList,"edit-config":{trigger:"click",mode:"row",icon:"fa fa-pencil",showStatus:!0}},{default:a(()=>[e(p,{type:"checkbox",width:"60",align:"center"}),e(p,{type:"seq",width:"60",align:"center"}),e(p,{field:"name",title:"客户名",sortable:"","edit-render":{name:"input",defaultValue:""}}),e(p,{field:"sex",title:"性别","edit-render":{name:"$select",options:t.sexList}},null,8,["edit-render"]),e(p,{field:"idcard",title:"身份证",sortable:"","edit-render":{name:"input",defaultValue:""}}),e(p,{field:"telphone",title:"手机",sortable:"","edit-render":{name:"input",defaultValue:""}})]),_:1},8,["data"])]),_:1}),e(C,{tab:"机票信息",key:"2",forceRender:""})]),_:1})]),_:1},8,["model","label-col","wrapper-col","rules"])]),_:1},16,["onRegister","title","onOk"])}const te=E(I,[["render",S],["__scopeId","data-v-22eadcc5"]]);export{te as default};
