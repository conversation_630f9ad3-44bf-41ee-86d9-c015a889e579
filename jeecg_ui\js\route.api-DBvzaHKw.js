import{j as s}from"./index-CCWaWN5g.js";const u=e=>s.get({url:"/sys/gatewayRoute/list",params:e}),y=e=>s.get({url:"/sys/gatewayRoute/deleteList",params:e}),o=e=>s.post({url:"/sys/gatewayRoute/updateAll",params:e}),l=(e,t)=>s.delete({url:"/sys/gatewayRoute/delete",data:e},{joinParamsToUrl:!0}).then(()=>{t()}),r=(e,t)=>s.put({url:"/sys/gatewayRoute/putRecycleBin",params:e}).then(()=>{t()}),R=(e,t)=>s.delete({url:`/sys/gatewayRoute/deleteRecycleBin?ids=${e.ids}`}).then(()=>{t()}),c=(e,t)=>s.get({url:"/sys/gatewayRoute/copyRoute",params:e}).then(()=>{t()});export{R as a,l as b,c,y as d,u as g,r as p,o as s};
