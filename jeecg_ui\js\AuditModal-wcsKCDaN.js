var E=(P,T,s)=>new Promise((A,_)=>{var g=c=>{try{f(s.next(c))}catch(e){_(e)}},b=c=>{try{f(s.throw(c))}catch(e){_(e)}},f=c=>c.done?A(c.value):Promise.resolve(c.value).then(g,b);f((s=s.apply(P,T)).next())});import{d as O,e as Q,f as C,r as W,w as X,ag as h,v as Y,aB as y,ar as l,aD as t,q as Z,ah as w,aq as u,at as m,F as L,k as r,G as i,au as d,u as B,aC as z}from"./vue-vendor-dy9k-Yad.js";import{u as $,a as ee}from"./index-CCWaWN5g.js";import{g as te,a as ae,r as re,b as se}from"./certificationAudit-CpD5ybP4.js";import{C as oe}from"./CustomModal-BakuIxQv.js";import{an as N}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const le={key:0,class:"audit-content"},ne={class:"info-section"},ie={key:0,class:"image-preview"},ue={key:1},de={class:"info-section"},ce={key:0,class:"image-preview"},pe={key:1},fe={key:1,class:"info-section"},ve={key:0,class:"image-preview"},he={key:1},me={class:"audit-section"},_e=O({__name:"AuditModal",props:{open:{type:Boolean},record:{}},emits:["update:open","success"],setup(P,{emit:T}){const s=P,A=T,{createMessage:_}=$(),g=Q({get:()=>s.open,set:o=>A("update:open",o)}),b=C(!1),f=C(!1),c=C(),e=C(null),p=W({review:2,notes:""}),F={review:[{required:!0,message:"请选择审核结果",trigger:"change"}]};X(()=>s.open,o=>E(null,null,function*(){o&&(p.review=2,p.notes="",s.record&&(yield R()))}));function R(){return E(this,null,function*(){if(s.record)try{f.value=!0;let o;if(s.record.authType==="个人认证")o=yield te(s.record.id);else if(s.record.authType==="企业认证")o=yield ae(s.record.id);else{_.error("未知的认证类型");return}e.value=o}catch(o){}finally{f.value=!1}})}function M(o){switch(o){case 1:return"身份证";case 2:return"其他";default:return"-"}}function S(){return E(this,null,function*(){var o;try{if(yield(o=c.value)==null?void 0:o.validate(),!s.record){_.error("记录信息不存在");return}b.value=!0;const a={id:s.record.id,review:p.review,notes:p.notes||void 0};if(s.record.authType==="个人认证")yield re(a);else if(s.record.authType==="企业认证")yield se(a);else{_.error("未知的认证类型");return}A("success")}catch(a){}finally{b.value=!1}})}function G(){g.value=!1,e.value=null}return(o,a)=>{const n=h("a-descriptions-item"),x=h("a-descriptions"),q=h("a-radio"),j=h("a-radio-group"),I=h("a-form-item"),H=h("a-textarea"),J=h("a-form"),K=Y("loading");return l(),y(oe,{open:g.value,"onUpdate:open":a[2]||(a[2]=k=>g.value=k),title:"认证审核",width:"800px","show-footer":!0,"show-cancel-button":!0,"show-confirm-button":!0,"cancel-text":"取消","confirm-text":"确认审核",onConfirm:S,onCancel:G},{default:t(()=>{var k,U,V;return[o.record?Z((l(),u("div",le,[o.record.authType==="企业认证"?(l(),u(L,{key:0},[m("div",ne,[a[3]||(a[3]=m("span",{class:"section-title"},"企业资料",-1)),(k=e.value)!=null&&k.hgyEnterpriseAuth?(l(),y(x,{key:0,column:2,bordered:"",size:"small"},{default:t(()=>[r(n,{label:"企业名称"},{default:t(()=>[i(d(e.value.hgyEnterpriseAuth.enterpriseName||"-"),1)]),_:1}),r(n,{label:"统一社会信用代码"},{default:t(()=>[i(d(e.value.hgyEnterpriseAuth.creditCode||"-"),1)]),_:1}),r(n,{label:"业务联系人"},{default:t(()=>[i(d(e.value.hgyEnterpriseAuth.relationUser||"-"),1)]),_:1}),r(n,{label:"联系电话"},{default:t(()=>[i(d(e.value.hgyEnterpriseAuth.relationPhone||"-"),1)]),_:1}),r(n,{label:"营业执照照片",span:2},{default:t(()=>[e.value.hgyEnterpriseAuth.companyLogo?(l(),u("div",ie,[r(B(N),{src:e.value.hgyEnterpriseAuth.companyLogo,width:200,preview:!0},null,8,["src"])])):(l(),u("span",ue,"-"))]),_:1})]),_:1})):w("",!0)]),m("div",de,[a[4]||(a[4]=m("span",{class:"section-title"},"法人信息",-1)),(U=e.value)!=null&&U.hgyEnterpriseAuth?(l(),y(x,{key:0,column:2,bordered:"",size:"small"},{default:t(()=>[r(n,{label:"法人真实姓名"},{default:t(()=>[i(d(e.value.hgyEnterpriseAuth.legalName||"-"),1)]),_:1}),r(n,{label:"法人证件类型"},{default:t(()=>[i(d(M(e.value.hgyEnterpriseAuth.cartType)),1)]),_:1}),r(n,{label:"法人证件号",span:2},{default:t(()=>[i(d(e.value.hgyEnterpriseAuth.cartId||"-"),1)]),_:1}),r(n,{label:"身份证正反面照片",span:2},{default:t(()=>[e.value.hgyAttachmentList&&e.value.hgyAttachmentList.length>0?(l(),u("div",ce,[(l(!0),u(L,null,z(e.value.hgyAttachmentList,(v,D)=>(l(),y(B(N),{width:150,key:D,src:v.filePath,preview:!0},null,8,["src"]))),128))])):(l(),u("span",pe,"-"))]),_:1})]),_:1})):w("",!0)])],64)):o.record.authType==="个人认证"?(l(),u("div",fe,[a[5]||(a[5]=m("span",{class:"section-title"},"个人资料",-1)),(V=e.value)!=null&&V.hgyPersonalAuth?(l(),y(x,{key:0,column:2,bordered:"",size:"small"},{default:t(()=>[r(n,{label:"真实姓名"},{default:t(()=>[i(d(e.value.hgyPersonalAuth.name||"-"),1)]),_:1}),r(n,{label:"手机号"},{default:t(()=>[i(d(e.value.hgyPersonalAuth.phone||"-"),1)]),_:1}),r(n,{label:"证件类型"},{default:t(()=>[i(d(M(e.value.hgyPersonalAuth.cartType)),1)]),_:1}),r(n,{label:"证件号"},{default:t(()=>[i(d(e.value.hgyPersonalAuth.cartId||"-"),1)]),_:1}),r(n,{label:"身份证正反面照片",span:2},{default:t(()=>[e.value.hgyAttachmentList&&e.value.hgyAttachmentList.length>0?(l(),u("div",ve,[(l(!0),u(L,null,z(e.value.hgyAttachmentList,(v,D)=>(l(),y(B(N),{key:D,src:v.filePath,width:150,preview:!0,style:{"margin-right":"10px"}},null,8,["src"]))),128))])):(l(),u("span",he,"-"))]),_:1})]),_:1})):w("",!0)])):w("",!0),m("div",me,[a[8]||(a[8]=m("span",{class:"section-title"},"审核操作",-1)),r(J,{ref_key:"formRef",ref:c,model:p,rules:F,layout:"vertical"},{default:t(()=>[r(I,{label:"审核结果",name:"review",required:""},{default:t(()=>[r(j,{value:p.review,"onUpdate:value":a[0]||(a[0]=v=>p.review=v)},{default:t(()=>[r(q,{value:2},{default:t(()=>a[6]||(a[6]=[i("通过")])),_:1,__:[6]}),r(q,{value:3},{default:t(()=>a[7]||(a[7]=[i("拒绝")])),_:1,__:[7]})]),_:1},8,["value"])]),_:1}),r(I,{label:"审核意见",name:"notes"},{default:t(()=>[r(H,{value:p.notes,"onUpdate:value":a[1]||(a[1]=v=>p.notes=v),placeholder:"请输入审核意见（选填）",rows:4,maxlength:500,"show-count":""},null,8,["value"])]),_:1})]),_:1},8,["model"])])])),[[K,f.value]]):w("",!0)]}),_:1},8,["open"])}}}),Ce=ee(_e,[["__scopeId","data-v-e7ef24fb"]]);export{Ce as default};
