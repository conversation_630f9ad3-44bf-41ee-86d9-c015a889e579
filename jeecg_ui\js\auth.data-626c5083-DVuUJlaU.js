import{e as m}from"./vue-vendor-dy9k-Yad.js";import{ct as p}from"./index-CCWaWN5g.js";const f=[{title:"启用",dataIndex:"switch",width:100,align:"center",slots:{customRender:"switch"}},{title:"字段名称",width:200,dataIndex:"code"},{title:"字段描述",dataIndex:"title"},{title:"列表控制",dataIndex:"list",width:120,slots:{customRender:"list"}},{title:"表单控制",dataIndex:"form",width:180,slots:{customRender:"form"}}],v=[{title:"启用",dataIndex:"switch",width:80,slots:{customRender:"switch"}},{title:"名称",dataIndex:"title"},{title:"编码",dataIndex:"code"},{title:"权限控制",dataIndex:"control",width:180,slots:{customRender:"control"}}],w=[{code:"add",title:"新增",status:0},{code:"edit",title:"编辑",status:0},{code:"detail",title:"详情",status:0},{code:"delete",title:"删除",status:0},{code:"batch_delete",title:"批量删除",status:0},{code:"export",title:"导出",status:0},{code:"import",title:"导入",status:0},{code:"query",title:"查询",status:0},{code:"reset",title:"重置",status:0},{code:"bpm",title:"提交流程",status:0},{code:"super_query",title:"高级查询",status:0},{code:"form_confirm",title:"确定",status:0}],n="USE_SQL_RULES",y=[{title:"启用",dataIndex:"switch",width:80,slots:{customRender:"switch"}},{title:"规则名称",dataIndex:"ruleName",width:130},{title:"规则描述",dataIndex:"description",customRender({record:{ruleOperator:o,ruleValue:t,ruleColumn:e}}){return o==n?`自定义SQL: ${t}`:`${e} ${o} ${t}`}}];function S(o,t){return{formSchemas:m(()=>[{label:"规则名称",field:"ruleName",required:!0,component:"Input",componentProps:{onChange:t.onRuleNameChange}},{label:"规则字段",field:"ruleColumn",component:"JSearchSelect",componentProps:{dictOptions:o.authFields,getPopupContainer:()=>document.body,onChange:t.onRuleColumnChange},dynamicRules({model:e}){return[{required:e.ruleOperator!=n,message:"请输入规则字段"}]},show:({model:e})=>e.ruleOperator!=n},{label:"条件规则",field:"ruleOperator",required:!0,component:"JDictSelectTag",componentProps:{options:[],onChange:t.onRuleOperatorChange,getPopupContainer:()=>document.body},dynamicPropskey:"options",dynamicPropsVal:({model:e,field:h})=>{var d;const r=a=>{if(["BigDecimal","double","int"].includes(a))return"number"},{filterCondition:c}=p();if(e.ruleColumn){const a=(d=o.authFields.find(l=>l.value===e.ruleColumn))!=null?d:{},i=c({view:a.view,fieldType:r(a.dbType)}).map(l=>{var u,s;return{label:(u=l.title)!=null?u:l.label,value:(s=l.val)!=null?s:l.value}});return i.push({value:"USE_SQL_RULES",label:"自定义SQL"}),i}else return[{value:"USE_SQL_RULES",label:"自定义SQL"}]}},{label:"规则值",field:"ruleValue",required:!0,component:"JInputSelect",componentProps:{selectPlaceholder:"可选择系统变量",inputPlaceholder:"请输入",getPopupContainer:()=>document.body,selectWidth:"200px",options:[{label:"登录用户账号",value:"#{sys_user_code}"},{label:"登录用户名称",value:"#{sys_user_name}"},{label:"当前日期",value:"#{sys_date}"},{label:"当前时间",value:"#{sys_time}"},{label:"登录用户部门",value:"#{sys_org_code}"},{label:"用户拥有的部门",value:"#{sys_multi_org_code}"},{label:"登录用户租户",value:"#{tenant_id}"}]}},{label:"状态",field:"status",required:!0,component:"RadioButtonGroup",componentProps:{options:[{label:"有效",value:1},{label:"无效",value:0}]},defaultValue:1}])}}export{w as C,f as S,n,y as v,S as w,v as y};
