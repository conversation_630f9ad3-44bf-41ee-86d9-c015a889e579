var m=(o,a,r)=>new Promise((i,s)=>{var c=e=>{try{n(r.next(e))}catch(p){s(p)}},t=e=>{try{n(r.throw(e))}catch(p){s(p)}},n=e=>e.done?i(e.value):Promise.resolve(e.value).then(c,t);n((r=r.apply(o,a)).next())});import{d as u,f,e as l,u as d,ag as g,aB as w,ar as D,aE as B}from"./vue-vendor-dy9k-Yad.js";import{B as _,u as h}from"./index-JbqXEynz.js";import{a as $}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";const k=u({name:"tableDrawer",components:{BasicDrawer:_},emits:["success","register"],setup(o,{emit:a}){const r=f(!0),[i,{setDrawerProps:s,closeDrawer:c}]=h(e=>m(null,null,function*(){s({confirmLoading:!1}),r.value=!!(e!=null&&e.isUpdate)})),t=l(()=>d(r)?"编辑角色":"新增角色");function n(){return m(this,null,function*(){try{s({confirmLoading:!0}),c(),a("success")}finally{s({confirmLoading:!1})}})}return{registerDrawer:i,getTitle:t,handleSubmit:n}}});function y(o,a,r,i,s,c){const t=g("BasicDrawer");return D(),w(t,B(o.$attrs,{onRegister:o.registerDrawer,showFooter:"",title:o.getTitle,width:"500px",onOk:o.handleSubmit}),null,16,["onRegister","title","onOk"])}const I=$(k,[["render",y]]);export{I as default};
