var l=(t,a,e)=>new Promise((r,i)=>{var p=o=>{try{n(e.next(o))}catch(s){i(s)}},f=o=>{try{n(e.throw(o))}catch(s){i(s)}},n=o=>o.done?r(o.value):Promise.resolve(o.value).then(p,f);n((e=e.apply(t,a)).next())});import{_ as m,a as c}from"./index-CCWaWN5g.js";import{d,f as u,o as h,aq as _,ar as g,aA as y}from"./vue-vendor-dy9k-Yad.js";import{useECharts as b}from"./useECharts-BU6FzBZi.js";import{m as S}from"./data-A0kLGLbM.js";import"./index-C5ZumRS6.js";import{I as w}from"./renderers-CGMjx3X9.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useTimeout-CeTdFD_D.js";import"./echarts-D8q0NfgS.js";const v=d({props:{width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"}},setup(){const t=u(null),{setOptions:a}=b(t);return h(()=>l(null,null,function*(){const e=yield(yield m(()=>l(null,null,function*(){const{default:r}=yield import("./china-Dgrv2CBN.js");return{default:r}}),[])).default;w("china",e),a({visualMap:[{min:0,max:1e3,left:"left",top:"bottom",text:["高","低"],calculable:!1,orient:"horizontal",inRange:{color:["#e0ffff","#006edd"],symbolSize:[30,100]}}],tooltip:{trigger:"item",backgroundColor:"rgba(0, 0, 0, .6)",textStyle:{color:"#fff",fontSize:12}},series:[{name:"iphone4",type:"map",map:"china",label:{show:!0,color:"rgb(249, 249, 249)",fontSize:10},itemStyle:{areaColor:"#2f82ce",borderColor:"#0DAAC1"},data:S}]})})),{chartRef:t}}});function C(t,a,e,r,i,p){return g(),_("div",{ref:"chartRef",style:y({height:t.height,width:t.width})},null,4)}const O=c(v,[["render",C]]);export{O as default};
