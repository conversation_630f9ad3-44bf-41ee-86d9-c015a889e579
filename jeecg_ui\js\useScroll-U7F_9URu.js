import{f as u,o as c,w as m,b as d}from"./vue-vendor-dy9k-Yad.js";import{s as f,k as w,v as L}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function W(e,l){const o=u(0),s=u(0);let a=()=>{f(e.value)?(o.value=e.value.scrollX,s.value=e.value.scrollY):e.value&&(o.value=e.value.scrollLeft,s.value=e.value.scrollTop)};if(w(l)){let t=0;l.wait&&l.wait>0&&(t=l.wait,Reflect.deleteProperty(l,"wait")),a=L(a,t)}let r;c(()=>{r=m(e,(t,i,n)=>{t?t.addEventListener("scroll",a):i&&i.removeEventListener("scroll",a),n(()=>{o.value=s.value=0,t&&t.removeEventListener("scroll",a)})},{immediate:!0})}),d(()=>{e.value&&e.value.removeEventListener("scroll",a)});function v(){r&&r()}return{refX:o,refY:s,stop:v}}export{W as useScroll};
