import{d as P,aN as C,f as y,ag as s,aB as N,ar as i,aD as a,at as u,aq as f,ah as b,F as k,aC as _,au as g,k as n,G as B}from"./vue-vendor-dy9k-Yad.js";import{P as A}from"./index-CtJ0w2CP.js";import{a as D,bu as I,bt as V}from"./index-CCWaWN5g.js";import{a4 as v}from"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const $=P({name:"AccountDetail",components:{PageWrapper:A,ATabs:v,ATabPane:v.TabPane},setup(){var o;const e=C(),t=I(),p=y((o=e.params)==null?void 0:o.id),c=y("detail"),{setTitle:d}=V();d("详情：用户"+p.value);function m(){t("/system/account")}return{userId:p,currentKey:c,goBack:m}}}),W={class:"pt-4 m-4 desc-wrap"};function w(e,t,p,c,d,m){const o=s("a-button"),l=s("a-tab-pane"),T=s("a-tabs"),K=s("PageWrapper");return i(),N(K,{title:"用户"+e.userId+"的资料",content:"这是用户资料详情页面。本页面仅用于演示相同路由在tab中打开多个页面并且显示不同的数据",contentBackground:"",onBack:e.goBack},{extra:a(()=>[n(o,{type:"primary",danger:""},{default:a(()=>t[1]||(t[1]=[B(" 禁用账号 ")])),_:1,__:[1]}),n(o,{type:"primary"},{default:a(()=>t[2]||(t[2]=[B(" 修改密码 ")])),_:1,__:[2]})]),footer:a(()=>[n(T,{"default-active-key":"detail",activeKey:e.currentKey,"onUpdate:activeKey":t[0]||(t[0]=r=>e.currentKey=r)},{default:a(()=>[n(l,{key:"detail",tab:"用户资料"}),n(l,{key:"logs",tab:"操作日志"})]),_:1},8,["activeKey"])]),default:a(()=>[u("div",W,[e.currentKey=="detail"?(i(),f(k,{key:0},_(10,r=>u("div",{key:r},"这是用户"+g(e.userId)+"资料Tab",1)),64)):b("",!0),e.currentKey=="logs"?(i(),f(k,{key:1},_(10,r=>u("div",{key:r},"这是用户"+g(e.userId)+"操作日志Tab",1)),64)):b("",!0)])]),_:1},8,["title","onBack"])}const H=D($,[["render",w]]);export{H as default};
