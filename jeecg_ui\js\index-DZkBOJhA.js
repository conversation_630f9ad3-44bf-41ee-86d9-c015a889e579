import{d as x,f as u,ag as n,aB as C,ar as R,aD as o,k as a,G as l}from"./vue-vendor-dy9k-Yad.js";import{C as B}from"./index-Dsp-S-LZ.js";import{P as b}from"./index-CtJ0w2CP.js";import{a8 as j,R as c,M as k}from"./antd-vue-vendor-me9YkNVC.js";import{a as D}from"./index-CCWaWN5g.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./htmlmixed-CmvhkW5V.js";/* empty css             */import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const f='{"name":"BeJson","url":"http://www.xxx.com","page":88,"isNonProfit":true,"address":{"street":"科技园路.","city":"江苏苏州","country":"中国"},"links":[{"name":"Google","url":"http://www.xxx.com"},{"name":"Baidu","url":"http://www.xxx.com"},{"name":"SoSo","url":"http://www.xxx.com"}]}',y=`
      (() => {
        var htmlRoot = document.getElementById('htmlRoot');
        var theme = window.localStorage.getItem('__APP__DARK__MODE__');
        if (htmlRoot && theme) {
          htmlRoot.setAttribute('data-theme', theme);
          theme = htmlRoot = null;
        }
      })();
  `,P=`
     <!DOCTYPE html>
<html lang="en" id="htmlRoot">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
    />
    <title><%= title %></title>
    <link rel="icon" href="/favicon.ico" />
  </head>
  <body>
    <div id="app">
    </div>
  </body>
</html>
  `,E=x({components:{CodeEditor:B,PageWrapper:b,RadioButton:c.Button,RadioGroup:c.Group,ASpace:j},setup(){const e=u("application/json"),t=u(f);function d(s){const i=s.target.value;if(i==="application/json"){t.value=f;return}if(i==="htmlmixed"){t.value=P;return}if(i==="javascript"){t.value=y;return}}function p(){k.info({title:"编辑器当前值",content:t.value})}return{value:t,modeValue:e,handleModeChange:d,showData:p}}});function G(e,t,d,p,s,i){const h=n("a-button"),r=n("RadioButton"),v=n("RadioGroup"),_=n("a-space"),w=n("CodeEditor"),g=n("PageWrapper");return R(),C(g,{title:"代码编辑器组件示例",contentFullHeight:"",fixedHeight:"",contentBackground:""},{extra:o(()=>[a(_,{size:"middle"},{default:o(()=>[a(h,{onClick:e.showData,type:"primary"},{default:o(()=>t[2]||(t[2]=[l("获取数据")])),_:1,__:[2]},8,["onClick"]),a(v,{"button-style":"solid",value:e.modeValue,"onUpdate:value":t[0]||(t[0]=m=>e.modeValue=m),onChange:e.handleModeChange},{default:o(()=>[a(r,{value:"application/json"},{default:o(()=>t[3]||(t[3]=[l(" json数据 ")])),_:1,__:[3]}),a(r,{value:"htmlmixed"},{default:o(()=>t[4]||(t[4]=[l(" html代码 ")])),_:1,__:[4]}),a(r,{value:"javascript"},{default:o(()=>t[5]||(t[5]=[l(" javascript代码 ")])),_:1,__:[5]})]),_:1},8,["value","onChange"])]),_:1})]),default:o(()=>[a(w,{value:e.value,"onUpdate:value":t[1]||(t[1]=m=>e.value=m),mode:e.modeValue},null,8,["value","mode"])]),_:1})}const q=D(E,[["render",G]]);export{q as default};
