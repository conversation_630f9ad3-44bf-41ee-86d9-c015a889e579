import{d as I,f as T,e as D,ag as r,aB as j,ar as M,aE as _,aD as n,k as s,as as O,at as C,q as S,B}from"./vue-vendor-dy9k-Yad.js";import{cs as H,F as J,u as q,bx as p}from"./index-CCWaWN5g.js";import{L as A}from"./useTableSync-075826a1-CL-4GwR8.js";import{u as E}from"./index-BOX6--gq.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";var z=Object.defineProperty,K=Object.defineProperties,L=Object.getOwnPropertyDescriptors,R=Object.getOwnPropertySymbols,W=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable,N=(e,t,a)=>t in e?z(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,Y=(e,t)=>{for(var a in t||(t={}))W.call(t,a)&&N(e,a,t[a]);if(R)for(var a of R(t))X.call(t,a)&&N(e,a,t[a]);return e},Z=(e,t)=>K(e,L(t));const G=I({name:"CheckDictTable",components:{VNodes:(e,{attrs:t})=>t.vnodes},setup(){const{prefixCls:e}=J("cgform-check-dict-table"),{createMessage:t}=q(),a=T([{title:"字段名称",key:"dbFieldName",width:100},{title:"字段备注",key:"dbFieldTxt",width:100},{title:"字段Href",key:"fieldHref",width:130,type:p.textarea,defaultValue:""},{title:"验证规则",key:"fieldValidType",width:170,type:p.slot,slotName:"fieldValidType",allowInput:!0,defaultValue:"",placeholder:"空"},{title:"校验必填",key:"fieldMustInput",width:80,type:p.checkbox,align:"center",customValue:["1","0"],defaultChecked:!1},{title:"字典Table",key:"dictTable",width:280,type:p.textarea,defaultValue:""},{title:"字典Code",key:"dictField",width:280,type:p.input,defaultValue:"",validateRules:[{handler:d}]},{title:"字典Text",key:"dictText",width:280,type:p.input,defaultValue:"",validateRules:[{handler:d}]}]),v=A(a),{tableRef:w,tables:x}=v,m=T([{label:"空",value:""},{label:"唯一校验",value:"only"},{label:"6到16位数字",value:"n6-16"},{label:"6到18位字母",value:"s6-18"},{label:"6到16位任意字符",value:"*6-16"},{label:"网址",value:"url"},{label:"电子邮件",value:"e"},{label:"手机号码",value:"m"},{label:"邮政编码",value:"p"},{label:"字母",value:"s"},{label:"数字",value:"n"},{label:"整数",value:"z"},{label:"非空",value:"*"},{label:"金额",value:"money"}]),c=D(()=>m.value.map(l=>l.value)),{createJPrompt:u}=E();function f(l){return l!=null&&!c.value.includes(l)}function b(l){u({title:"自定义正则表达式",placeholder:"请输入正则表达式",rules:[{required:!0,message:"正则表达式不能为空！"},{validator:o}],onOk:i=>{l.triggerChange(i),t.success("添加成功")}})}function g(l){u({title:"修改自定义正则表达式",defaultValue:l.value,placeholder:"请输入正则表达式",rules:[{required:!0,message:"正则表达式不能为空！"},{validator:o}],onOk:i=>{l.triggerChange(i),i!==l.value&&t.success("修改成功")}})}function o(l,i){return f(i)?Promise.resolve():Promise.reject("当前校验已存在")}function h(l){l.dbIsNull==="0"&&w.value.setValues([{rowKey:l.id,values:{fieldMustInput:"1"}}])}function d({cellValue:l,row:i},y){const{dbFieldName:P}=i,k=x.pageTable.value.tableRef.getTableData(),V=k==null?void 0:k.find(F=>F.dbFieldName===P);(V==null?void 0:V.fieldShowType)==="popup_dict"?l.indexOf(",")==-1?y(!0):y(!1,"popup字典组件只允许填写一个字段"):y(!0)}return Z(Y({},v),{prefixCls:e,columns:a,isCustomRegexp:f,validTypeOptions:m,validTypeValues:c,onAddCustomRegexp:b,onChangeCustomRegexp:g,syncFieldMustInput:h})}}),Q={class:"menu"},U={class:"custom-option-list rc-virtual-list-holder-inner",style:{"border-top":"1px solid #dfdfdf"}},$=["onClick"];function ee(e,t,a,v,w,x){const m=r("VNodes"),c=r("a-select"),u=r("a-col"),f=r("a-button"),b=r("a-row"),g=r("JVxeTable");return M(),j(g,_({ref:"tableRef",rowNumber:"",keyboardEdit:"",class:[e.prefixCls],maxHeight:e.tableHeight.noToolbar,loading:e.loading,columns:e.columns,dataSource:e.dataSource,disabledRows:{dbFieldName:["id","has_child"]}},e.tableProps),{fieldValidType:n(o=>[s(b,{type:"flex",class:O(["row-valid-type",{full:!e.isCustomRegexp(o.value)}])},{default:n(()=>[s(u,{class:O(["left"])},{default:n(()=>[s(c,{value:o.value,options:e.validTypeOptions,placeholder:"空",style:{width:"100%"},onChange:o.triggerChange,virtual:!1},{dropdownRender:n(({menuNode:h})=>[C("div",Q,[s(m,{vnodes:h},null,8,["vnodes"])]),S(C("div",U,[C("div",{class:"ant-select-item ant-select-item-option",title:"使用自定义正则表达式作为校验规则",onClick:d=>e.onAddCustomRegexp(o),onMousedown:t[0]||(t[0]=d=>d.preventDefault())}," 正则表达式 ",40,$)],512),[[B,!e.isCustomRegexp(o.value)]])]),_:2},1032,["value","options","onChange"])]),_:2},1024),s(u,{class:"right",title:"修改自定义正则表达式"},{default:n(()=>[s(f,{preIcon:"ant-design:edit",onClick:()=>e.onChangeCustomRegexp(o)},null,8,["onClick"])]),_:2},1024)]),_:2},1032,["class"])]),_:1},16,["class","maxHeight","loading","columns","dataSource"])}const ot=H(G,[["render",ee],["__scopeId","data-v-272f2c52"]]);export{ot as default};
