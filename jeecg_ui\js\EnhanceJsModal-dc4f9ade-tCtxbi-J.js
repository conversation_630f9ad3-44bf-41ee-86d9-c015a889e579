import{d as q,f as m,r as M,ag as g,aB as C,ar as w,aD as o,k as l,ah as k,at as b,G as S}from"./vue-vendor-dy9k-Yad.js";import{B as W}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import{J as Q}from"./useOnlineTest-e4bd8be3-hnhsV9Hd.js";import{u as z,E as L}from"./EnhanceJsHistory-8ddb0657-CGGC_rnZ.js";import{q as X,b as Y}from"./enhance.api-138e6826-BOpOzAwu.js";import{cs as Z,u as ee,ac as te,ad as se}from"./index-CCWaWN5g.js";import{T as oe,c8 as ie}from"./antd-vue-vendor-me9YkNVC.js";import ae from"./JCodeEditor-B-WXz11X.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */var F=(e,t,h)=>new Promise((f,x)=>{var i=a=>{try{c(h.next(a))}catch(n){x(n)}},r=a=>{try{c(h.throw(a))}catch(n){x(n)}},c=a=>a.done?f(a.value):Promise.resolve(a.value).then(i,r);c((h=h.apply(e,t)).next())});const R={list:[{text:".acceptHrefParams",displayText:"acceptHrefParams",superiors:"this",desc:"获取地址栏上的条件"},{text:".currentPage",displayText:"currentPage",superiors:"this",desc:"获取当前页数，默认1"},{text:".currentTableName",displayText:"currentTableName",desc:"获取当前表名"},{text:".description",displayText:"description",superiors:"this",desc:"获取当前表描述"},{text:".hasChildrenField",displayText:"hasChildrenField",superiors:"this",desc:"如果是树形列表，获取是否有子节点字段名"},{text:".ID",displayText:"ID",superiors:"this",desc:"获取当前表的配置ID"},{text:".pageSize",displayText:"pageSize",superiors:"this",desc:"获取当前每页条数，默认10"},{text:".queryParam",displayText:"queryParam",superiors:"this",desc:"获取查询表单的查询条件"},{text:".selectedRowKeys",displayText:"selectedRowKeys",superiors:"this",desc:"获取选中行的id的数组"},{text:".selectedRows",displayText:"selectedRows",superiors:"this",desc:"获取选中行的数据数组"},{text:".sortField",displayText:"sortField",superiors:"this",desc:"获取排序字段，默认‘id’"},{text:".sortType",displayText:"sortType",superiors:"this",desc:"获取排序类型，默认升序‘asc’"},{text:".total",displayText:"total",superiors:"this",desc:"获取总条数"},{text:".loadData()",displayText:"loadData()",superiors:"this",desc:"加载数据"},{text:".clearSelectedRow()",displayText:"clearSelectedRow()",superiors:"this",desc:"清除选中的行"},{text:".getLoadDataParams()",displayText:"getLoadDataParams()",superiors:"this",desc:"获取所有的查询条件，返回一个对象，包括：查询表单，高级查询，地址栏参数，分页信息，排序信息等"},{text:".isTree()",displayText:"isTree()",superiors:"this",desc:"判断当前表是不是树，返回布尔值"},{text:`beforeEdit(row){
  return new Promise((resolve, reject) => {
    if(row.字段名 == '字段值'){
      reject('测试~');
    }else{
      resolve();
    }
  })     
}`,displayText:"beforeEdit(row){}",desc:"点击操作列下的编辑按钮触发，返回promise对象"},{text:`beforeDelete(row){
	return new Promise((resolve, reject) => {
  	if(row.字段名 == '字段值'){
    	reject('测试~');
    }else{
    	resolve();
    }
  })     
}`,displayText:"beforeDelete(row){}",desc:"点击操作列下的删除按钮触发，返回promise对象"},{text:"console.log()",displayText:"console.log()",desc:"打印日志"}],form:[{text:".loading",displayText:"loading",superiors:"this",desc:"是否加载中，返回的是一个ref对象"},{text:".isUpdate",displayText:"isUpdate",superiors:"this",desc:"是否是编辑页面，返回的是一个ref对象"},{text:".onlineFormRef",displayText:"onlineFormRef",superiors:"this",desc:"主表/单表表单的ref对象"},{text:".refMap",displayText:"refMap",superiors:"this",desc:"子表表单/子表table的ref对象map，key为子表表名"},{text:".subActiveKey",displayText:"subActiveKey",superiors:"this",desc:"子表的激活的tab索引值对应的字符串，从‘0’开始，返回的是一个ref对象"},{text:".sh",displayText:"sh",superiors:"this",desc:"单表/主表字段的显示隐藏状态"},{text:".submitFlowFlag",displayText:"submitFlowFlag",superiors:"this",desc:"是否提交表单后自动提交流程，返回一个ref对象"},{text:".subFormHeight",displayText:"subFormHeight",superiors:"this",desc:"一对一子表表单的高度，不需要设置，返回一个ref对象"},{text:".subTableHeight",displayText:"subTableHeight",superiors:"this",desc:"一对多子表table的高度，不需要设置，返回一个ref对象"},{text:".tableName",displayText:"tableName",superiors:"this",desc:"当前表名，返回的是一个ref对象"},{text:".$nextTick",displayText:"$nextTick",superiors:"this",desc:"调用的是vue3的nextTick"},{text:".字段名_load",displayText:"字段名_load",superiors:"this",desc:"控制字段的加载与否，设置为false表示当前字段不加载"},{text:".字段名_disabled",displayText:"字段名_disabled",superiors:"this",desc:"控制字段的禁用与否，设置为true表示当前字段禁用"},{text:".addSubRows(tableName, rows)",displayText:"addSubRows(tableName, rows)",superiors:"this",desc:"往一对多子表table里添加数据"},{text:".changeOptions(field, options)",texdisplayTextt:"changeOptions(field, options)",superiors:"this",desc:"改变单表/主笔 下拉控件的下拉选项"},{text:".clearSubRows(tableName)",displayText:"clearSubRows(tableName)",superiors:"this",desc:"清空一对多子表table的数据"},{text:".clearThenAddRows(tableName, rows)",displayText:"clearThenAddRows(tableName, rows)",superiors:"this",desc:"先清空一对多子表table的数据，再往里添加数据"},{text:".getFieldsValue()",displayText:"getFieldsValue()",superiors:"this",desc:"获取主表/单表 所有字段的值"},{text:".getSubTableInstance(tableName)",displayText:"getSubTableInstance(tableName)",superiors:"this",desc:"获取子表的实例对象，这个对象可以调用子表table的方法"},{text:".setFieldsValue(row)",displayText:"setFieldsValue(row)",superiors:"this",desc:"设置主表/单表 字段的值"},{text:".triggleChangeValues(values,id,target)",displayText:"triggleChangeValues(values,id,target)",superiors:"this",desc:"改变单表/主表/子表 字段的值，一般用于change事件，其中id，target需要通过change事件的内置参数获取，如果不传id，target的值，则改变的是主表的字段"},{text:".triggleChangeValue(field, value)",displayText:"triggleChangeValue(field, value)",superiors:"this",desc:"设置单表/主表 字段的值"},{text:".onlineFormValueChange(field, value, otherValus)",displayText:"onlineFormValueChange(field, value, otherValus)",superiors:"this",desc:"定义后，当表单值改变的时候会触发该方法（因js增强hook方式不支持原来的onlChange，所以定义此方法）"},{text:".changeSubTableOptions(tableName，field，options)",displayText:"changeSubTableOptions(tableName，field，options)",superiors:"this",desc:"改变一对一子表下拉框options"},{text:".changeSubFormbleOptions(tableName，field，options)",displayText:"changeSubFormbleOptions(tableName，field，options)",superiors:"this",desc:"改变一对多子表下拉框options"},{text:".changeRemoteOptions({ field, dict, label, type?, subTableName? })",displayText:"changeRemoteOptions({ field, dict, label, type?, subTableName? })",superiors:"this",desc:"改变动态下拉框options"},{text:".submitFormAndFlow()",displayText:"submitFormAndFlow()",superiors:"this",desc:"表单提交且发起流程"},{text:`beforeSubmit(row){
	return new Promise((resolve, reject)=>{
    //此处模拟等待时间，可能需要发起请求
    setTimeout(()=>{
      if(row.字段名 == '字段值'){
        // 当某个字段不满足要求的时候可以reject 
        reject('测试~');
      }else{
        resolve();
      }
    },3000)
  })
}`,displayText:"beforeSubmit(row){}",desc:"提交前置事件"},{text:`loaded(){
  this.$nextTick(()=>{
    // let text = '测试js增强设置默认值';
    // if(this.isUpdate.value === true){
    //   text = '测试js增强修改表单值';
    // }
    this.setFieldsValue({
      字段名: 修改的值
    })
  })
}`,displayText:"loaded(){}",desc:"表单加载事件"},{text:`onlChange(){
  return {
    字段名(){
      let value = event.value
      console.log(value)
      this.triggleChangeValues({'字段名': '修改后的值'})
    }
  }
 }`,displayText:"onlChange(){}",desc:"单表#表单值改变事件"},{text:`子表名_onlChange(){
  return {
    字段名(){
      let value = event.value;
      console.log(value);
      let row = {'字段名': '测试一对多值改变：'+value};
      this.triggleChangeValues(row, event.row.id, event.target)
  }
  }
}`,displayText:"子表名_onlChange(){}",desc:"子表#表单值改变事件"},{text:`子表名_onlChange(){
  return {
    子表字段01(){
      this.getSubTableInstance('子表名').getValues((err,values)=>{
        this.triggleChangeValues({'主表字段名': '修改后的值'})
      }) 
    },
  }
}
`,displayText:"子表名_onlChange(){}",desc:"子改主#表单值改变事件"},{text:`onlChange(){
  return {
    字段名01(){
      let value = event.value
      this.changeOptions('字段名02', '修改后的值');
    }
    字段名02(){
      let value = event.value
      this.changeOptions('字段名03', '修改后的值');
    }
  }
}`,displayText:"changeOptions()",desc:"js增强实现下拉联动"},{text:"console.log()",displayText:"console.log()",desc:"打印日志"}],common:[{text:`getAction('请求url', { 'key': 'value'}).then(res => {
  console.log(res)
})`,displayText:"getAction(url, param)",desc:"get请求"},{text:`postAction('请求url', { 'key': 'value'}).then(res => {
  console.log(res)
})`,displayText:"postAction(url, param)",desc:"post请求"},{text:`putAction('请求url', { 'key': 'value'}).then(res => {
  console.log(res)
})`,displayText:"putAction(url, param)",desc:"put请求"},{text:`deleteAction('请求url', { 'key': 'value'}).then(res => {
  console.log(res)
})`,displayText:"deleteAction(url, param)",desc:"delete请求"},{text:"this",displayText:"this",desc:"上下文"},{text:".openCustomModal({title,width,row,formComponent,requestUrl,hide,show})",displayText:"openCustomModal({title,width,row,formComponent,requestUrl,hide,show})",desc:"打开一个弹窗-参考 Js增强打开自定义弹窗"}]},le=q({name:"EnhanceJs",components:{BasicModal:W,JCodeEditor:ae,EnhanceJsHistory:L,QuestionCircleOutlined:ie,Tooltip:oe},emits:["register"],setup(){const{createMessage:e}=ee(),t=z(),h=m(),f=m(),x=M({form:{},list:{}}),i=m("list"),r=m(""),c=m(!1),a=m(!1),n=m(""),y=M({form:"",list:""}),p={form:!1,list:!1},T=m(!1),N=[...R.list,...R.common],V=[...R.form,...R.common],d=m("240px"),[H,{closeModal:E}]=te(s=>F(this,null,function*(){K(s.row)})),[D,O]=se(),{aiTestMode:P,genEnhanceJsData:A}=Q();function K(s){r.value=s.id,a.value=!1,n.value=s.tableName;let u=t.getEnhanceJs(r.value);(u==null?void 0:u.length)>0?(i.value=u[u.length-1].type,c.value=!0):c.value=!1,p.form=!1,p.list=!1,i.value?J(i.value):J("form"),T.value=!0,setTimeout(()=>T.value=!1,150)}function B(){return F(this,null,function*(){yield Promise.all([_("form"),_("list")]),E(),e.success("保存成功")})}function _(s){return F(this,null,function*(){let u=x[s],v={cgJs:y[s],cgJsType:s};if(!p[s]||u.cgJs===v.cgJs)return;let j=!!u.id;j&&(v=Object.assign({},u,v)),yield X(r.value,v,j),t.addEnhanceJs({code:r.value,str:v.cgJs,type:v.cgJsType,date:new Date().getTime()})})}function I(){E()}function J(s){return F(this,null,function*(){i.value=s;try{if(!p[s]){let u=yield Y(r.value,s);Object.assign(x[s],{id:null},u),y[s]=x[s].cgJs,p[s]=!0}}catch(u){}setTimeout(()=>{s=="list"?f.value.refresh():h.value.refresh()},150)})}function U(){O.openModal(!0,{code:r.value,type:i.value})}function $(s){y[i.value]!=s&&(a.value=!0,y[i.value]=s)}function G(){i.value==="form"?A(n.value,i.value,h.value):A(n.value,i.value,f.value)}return{formEditorRef:h,listEditorRef:f,reloading:T,enhanceValues:y,enhanceType:i,showHistory:c,aiTestMode:P,tableName:n,genEnhanceJsData:A,onGenTestData:G,onChangeType:J,onCodeChange:$,onShowHistory:U,onSubmit:B,onCancel:I,registerModal:H,registerEnhanceJsHistory:D,listKeyWords:N,formKeyWords:V,handleGo:s=>{window.open(`https://help.jeecg.com/java/online/enhanceJs/${s}`)},codeEditorHeight:d,handleFullScreenChange:s=>{s?d.value=`${document.documentElement.clientHeight-250}px`:d.value="240px"}}}}),re={class:"titleBox"},ne={class:"titleBox"};function de(e,t,h,f,x,i){const r=g("QuestionCircleOutlined"),c=g("Tooltip"),a=g("JCodeEditor"),n=g("a-tab-pane"),y=g("a-tabs"),p=g("a-button"),T=g("a-space"),N=g("EnhanceJsHistory"),V=g("BasicModal");return w(),C(V,{onRegister:e.registerModal,title:"JS增强",width:800,onFullScreen:e.handleFullScreenChange},{footer:o(()=>[l(T,null,{default:o(()=>[l(p,{onClick:e.onCancel},{default:o(()=>t[9]||(t[9]=[S("关闭")])),_:1},8,["onClick"]),l(p,{type:"primary",onClick:e.onSubmit},{default:o(()=>t[10]||(t[10]=[S("确定")])),_:1},8,["onClick"])]),_:1}),l(T,{style:{float:"left"}},{default:o(()=>[e.showHistory?(w(),C(p,{key:0,onClick:e.onShowHistory},{default:o(()=>t[11]||(t[11]=[S("查看历史版本")])),_:1},8,["onClick"])):k("",!0),e.aiTestMode?(w(),C(p,{key:1,onClick:e.onGenTestData},{default:o(()=>t[12]||(t[12]=[S("生成测试数据")])),_:1},8,["onClick"])):k("",!0)]),_:1})]),default:o(()=>[l(y,{activeKey:e.enhanceType,"onUpdate:activeKey":t[4]||(t[4]=d=>e.enhanceType=d),onChange:e.onChangeType},{default:o(()=>[l(n,{key:"form",forceRender:""},{tab:o(()=>[b("div",re,[t[6]||(t[6]=b("span",{class:"title"},"form",-1)),l(c,null,{title:o(()=>t[5]||(t[5]=[b("span",null,"表单js增强文档",-1)])),default:o(()=>[l(r,{onClick:t[0]||(t[0]=d=>e.handleGo("form"))})]),_:1})])]),default:o(()=>[!e.reloading&&e.enhanceType==="form"?(w(),C(a,{key:0,ref:"formEditorRef",value:e.enhanceValues.form,"onUpdate:value":t[1]||(t[1]=d=>e.enhanceValues.form=d),language:"javascript",fullScreen:!0,lineNumbers:!1,height:e.codeEditorHeight,"language-change":!1,onChange:e.onCodeChange,keywords:e.formKeyWords,placeholder:`代码提示技巧：
全局对象: this.调用属性或方法
事件方法：beforeSubmit、loaded、onlChange、getAction、postAction、putAction、deleteAction、deleteAction、openCustomModal等`},null,8,["value","height","onChange","keywords"])):k("",!0)]),_:1}),l(n,{key:"list",forceRender:""},{tab:o(()=>[b("div",ne,[t[8]||(t[8]=b("span",{class:"title"},"list",-1)),l(c,null,{title:o(()=>t[7]||(t[7]=[b("span",null,"列表js增强文档",-1)])),default:o(()=>[l(r,{onClick:t[2]||(t[2]=d=>e.handleGo("list"))})]),_:1})])]),default:o(()=>[!e.reloading&&e.enhanceType==="list"?(w(),C(a,{key:0,ref:"listEditorRef",value:e.enhanceValues.list,"onUpdate:value":t[3]||(t[3]=d=>e.enhanceValues.list=d),language:"javascript",fullScreen:!0,lineNumbers:!1,height:e.codeEditorHeight,"language-change":!1,onChange:e.onCodeChange,keywords:e.listKeyWords,placeholder:`代码提示技巧：
全局对象: this.调用属性或方法 
事件方法：beforeDelete、beforeEdit、getAction、postAction、putAction、deleteAction、deleteAction、openCustomModal等`},null,8,["value","height","onChange","keywords"])):k("",!0)]),_:1})]),_:1},8,["activeKey","onChange"]),l(N,{onRegister:e.registerEnhanceJsHistory},null,8,["onRegister"])]),_:1},8,["onRegister","onFullScreen"])}const ht=Z(le,[["render",de],["__scopeId","data-v-73c97e14"]]);export{ht as default};
