import{d as l,ap as m,e as d,ag as e,aB as f,ar as _,aD as a,k as c}from"./vue-vendor-dy9k-Yad.js";import{Z as g,T as E}from"./antd-vue-vendor-me9YkNVC.js";import{c as L,bL as C,N as h,bN as B,a as T}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const k=l({name:"ErrorAction",components:{Icon:L,Tooltip:E,Badge:g},setup(){const{t:o}=h(),{push:n}=m(),t=C(),r=d(()=>t.getErrorLogListCount);function s(){n(B.ERROR_LOG_PAGE).then(()=>{t.setErrorLogListCount(0)})}return{t:o,getCount:r,handleToErrorList:s}}});function b(o,n,t,r,s,I){const i=e("Icon"),u=e("Badge"),p=e("Tooltip");return _(),f(p,{title:o.t("layout.header.tooltipErrorLog"),placement:"bottom",mouseEnterDelay:.5,onClick:o.handleToErrorList},{default:a(()=>[c(u,{count:o.getCount,offset:[0,10],overflowCount:99},{default:a(()=>[c(i,{icon:"ion:bug-outline"})]),_:1},8,["count"])]),_:1},8,["title","onClick"])}const v=T(k,[["render",b]]);export{v as default};
