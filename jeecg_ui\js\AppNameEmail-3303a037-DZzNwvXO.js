import{d as P,r as S,f as d,e as f,u as a,J as $,ag as y,aq as j,ar as z,k as o,at as n,aD as w,as as _,au as G,G as J}from"./vue-vendor-dy9k-Yad.js";import W from"./AppLoginHeader-4432c584-CwU9kapN.js";import{cs as X,N as Y,u as Z,b_ as H}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";var T=(C,x,c)=>new Promise((r,i)=>{var g=l=>{try{m(c.next(l))}catch(p){i(p)}},h=l=>{try{m(c.throw(l))}catch(p){i(p)}},m=l=>l.done?r(l.value):Promise.resolve(l.value).then(g,h);m((c=c.apply(C,x)).next())});const K={class:"name-email-subject"},L={class:"name-email-content"},O=P({__name:"AppNameEmail",emits:["login-account","bind-third-account"],setup(C,{expose:x,emit:c}){const r=S({realname:"",email:""}),{t:i}=Y(),g=d(),h=d(),m=d(),l=f(()=>[{required:!0,message:"请填写邮箱",trigger:"change"},{type:"email",message:"请填写正确的邮箱"}]),p=f(()=>[{required:!0,message:"请填写昵称",trigger:"change"}]),D=f(()=>({realname:a(p),email:a(l)})),u=d(""),V=f(()=>r.realname||a(u)==="realname"?"current-active":""),q=f(()=>r.email!=""||a(u)==="email"?"current-active":""),s=d({}),N=c,{notification:k,createErrorModal:Q}=Z(),b=d(!1);function B(t){u.value=t,t==="realname"?g.value.focus():h.value.focus()}function E(){u.value=""}function A(){return T(this,null,function*(){m.value.validateFields().then(t=>T(this,null,function*(){if(a(s).bindThirdAccount)N("bind-third-account",{username:a(s).phone,email:t.email,realname:t.realname,password:a(s).password,phone:a(s).phone,smscode:a(s).smscode});else try{b.value=!0;const e=yield H($({username:a(s).phone,email:t.email,realname:t.realname,password:a(s).password,phone:a(s).phone,smscode:a(s).smscode}));e&&e.data.success?(N("login-account",{account:a(s).phone,password:a(s).password}),k.success({message:void 0,description:e.data.message||i("sys.api.registerMsg"),duration:3})):k.warning({message:i("sys.api.errorTip"),description:e.data.message||i("sys.api.networkExceptionMsg"),duration:3})}catch(e){k.error({message:i("sys.api.errorTip"),description:e.message||i("sys.api.networkExceptionMsg"),duration:3})}finally{b.value=!1}}))})}function I(t){s.value=t}return x({setRegisterData:I}),(t,e)=>{const M=y("a-input"),R=y("a-form-item"),U=y("a-button"),F=y("a-form");return z(),j("div",K,[o(W),e[5]||(e[5]=n("div",{class:"flex-row align-items-center margin-top40"},[n("div",{class:"register-title"}," 请填写昵称和邮箱 ")],-1)),e[6]||(e[6]=n("div",{class:"name-email-desc align-items-center"},[n("span",{style:{color:"#9e9e9e"}},"请填写昵称和邮箱，方便大家与您联系")],-1)),n("div",L,[o(F,{ref_key:"formRef",ref:m,model:r,rules:D.value},{default:w(()=>[n("div",{class:_(["content-item",V.value]),onClick:e[1]||(e[1]=v=>B("realname"))},[o(R,{name:"realname"},{default:w(()=>[o(M,{ref_key:"realNameRef",ref:g,value:r.realname,"onUpdate:value":e[0]||(e[0]=v=>r.realname=v),style:{height:"40px"},onBlur:E},null,8,["value"]),n("div",{class:_(["form-title",u.value==="username"?"active-title":""])}," 昵称 ",2)]),_:1})],2),n("div",{class:_(["content-item",q.value]),onClick:e[3]||(e[3]=v=>B("email"))},[o(R,{name:"email"},{default:w(()=>[o(M,{ref_key:"emailRef",ref:h,value:r.email,"onUpdate:value":e[2]||(e[2]=v=>r.email=v),style:{height:"40px"},onBlur:E},null,8,["value"]),n("div",{class:_(["form-title",u.value==="email"?"active-title":""])},G(a(i)("sys.login.email")),3)]),_:1})],2),n("div",{class:"pointer",onClick:A},[o(U,{type:"primary",class:"next-step",loading:b.value},{default:w(()=>e[4]||(e[4]=[J("完成")])),_:1},8,["loading"])])]),_:1},8,["model","rules"])])])}}}),re=X(O,[["__scopeId","data-v-f3b40873"]]);export{re as default};
