import{d as B,f as c,ag as s,aB as D,ar as I,aD as e,at as t,k as i,G as l,au as k,u as m}from"./vue-vendor-dy9k-Yad.js";import{P as N}from"./index-CtJ0w2CP.js";import{T as z}from"./index-ByPySmGo.js";import"./index-L3cSIXth.js";import{u as C,a as M}from"./index-CCWaWN5g.js";import{B as H}from"./BasicForm-DBcXiHk0.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const P={class:"p-4"},R={class:"mb-4"},S={class:"mt-4"},F=["innerHTML"],w=B({__name:"index",setup(L){const{createMessage:n}=C(),p=c(`
<h2>欢迎使用Tiptap富文本编辑器</h2>
<p>这是一个基于<strong>Tiptap</strong>的现代化富文本编辑器，具有以下特点：</p>
<ul>
  <li>轻量级且功能强大</li>
  <li>完全免费商用（MIT许可证）</li>
  <li>与Vue 3完美集成</li>
  <li>高度可定制</li>
</ul>
<p>您可以尝试以下功能：</p>
<ol>
  <li><strong>文本格式化</strong>：粗体、斜体、下划线、删除线</li>
  <li><em>标题设置</em>：支持H1-H6标题</li>
  <li><u>文本对齐</u>：左对齐、居中、右对齐、两端对齐</li>
  <li>列表功能：有序列表和无序列表</li>
  <li><span style="font-family: Microsoft YaHei; font-size: 18px; color: #ff0000;">字体选择</span>：支持多种字体和字号</li>
  <li><span style="color: #0066ff;">文字颜色</span>和<mark style="background-color: #ffff00;">背景高亮</mark></li>
  <li><strong>图片上传</strong>：点击图片按钮直接上传文件，无需输入URL</li>
</ol>
<h3>本地字体演示</h3>
<p style="font-family: PingFang Regular; font-size: 16px;">苹方常规：这是苹方常规字体的演示文本</p>
<p style="font-family: PingFang Medium; font-size: 16px; color: #1890ff;">苹方中等：这是苹方中等字体的演示文本</p>
<p style="font-family: PingFang Bold; font-size: 16px; color: #52c41a;">苹方加粗：这是苹方加粗字体的演示文本</p>
<p style="font-family: DIN Regular; font-size: 18px; color: #fa8c16;">DIN Regular: This is DIN Regular font demonstration</p>
<p style="font-family: DIN Bold; font-size: 18px; color: #eb2f96;">DIN Bold: This is DIN Bold font demonstration</p>
<p style="font-family: FZZongYi-M05S; font-size: 20px; color: #722ed1;">方正综艺：这是方正综艺字体的演示文本</p>
<p style="font-family: YouSheBiaoTiHei; font-size: 20px; color: #13c2c2;">优设标题黑：这是优设标题黑字体的演示文本</p>
<p style="text-align: center; font-family: KaiTi; font-size: 20px; color: #ff6600;">这是一段居中的楷体橙色文本</p>
<p style="text-align: right; font-family: SimHei; color: #6600ff;">这是一段右对齐的黑体紫色文本</p>
`),r=c(!1),d=()=>{n.info(`当前内容长度: ${p.value.length} 字符`)},g=()=>{p.value=`
<h3>这是通过代码设置的内容</h3>
<p>当前时间: ${new Date().toLocaleString()}</p>
<p>这段内容演示了如何通过代码动态设置编辑器内容。</p>
`,n.success("内容已设置")},_=()=>{p.value="",n.success("内容已清空")},y=()=>{r.value=!r.value,n.info(`编辑器已${r.value?"禁用":"启用"}`)},x=u=>{},h=[{field:"title",component:"Input",label:"标题",required:!0,defaultValue:"测试文章"},{field:"content",component:"JEditorTiptap",label:"内容",required:!0,defaultValue:"<p>这是表单中的富文本编辑器</p>",componentProps:{height:"300px",placeholder:"请输入文章内容..."}},{field:"summary",component:"InputTextArea",label:"摘要",componentProps:{rows:3,placeholder:"请输入文章摘要..."}}],v=u=>{n.success("表单提交成功")},b=()=>{n.info("表单已重置")};return(u,o)=>{const a=s("a-button"),T=s("a-button-group"),f=s("a-card");return I(),D(m(N),{title:"Tiptap富文本编辑器示例"},{default:e(()=>[t("div",P,[i(f,{title:"基础使用"},{default:e(()=>[t("div",R,[i(T,null,{default:e(()=>[i(a,{onClick:d},{default:e(()=>o[1]||(o[1]=[l("获取内容")])),_:1,__:[1]}),i(a,{onClick:g},{default:e(()=>o[2]||(o[2]=[l("设置内容")])),_:1,__:[2]}),i(a,{onClick:_},{default:e(()=>o[3]||(o[3]=[l("清空内容")])),_:1,__:[3]}),i(a,{onClick:y},{default:e(()=>[l(k(r.value?"启用":"禁用"),1)]),_:1})]),_:1})]),i(m(z),{modelValue:p.value,"onUpdate:modelValue":o[0]||(o[0]=V=>p.value=V),disabled:r.value,height:"400px",placeholder:"请输入内容，体验Tiptap富文本编辑器...",onChange:x},null,8,["modelValue","disabled"]),t("div",S,[o[4]||(o[4]=t("h4",null,"实时内容预览：",-1)),t("div",{class:"content-preview",innerHTML:p.value},null,8,F)]),o[5]||(o[5]=t("div",{class:"mt-4"},[t("h4",null,"新增功能说明："),t("ul",{class:"feature-list"},[t("li",null,[t("strong",null,"字体选择"),l("：支持系统字体、苹方字体系列、DIN字体系列、方正综艺、优设标题黑等专业字体")]),t("li",null,[t("strong",null,"字号设置"),l("：支持12px到36px的常用字号选择")]),t("li",null,[t("strong",null,"文字颜色"),l("：提供丰富的颜色选择，支持自定义颜色")]),t("li",null,[t("strong",null,"背景高亮"),l("：支持文字背景色设置，适合重点标记")]),t("li",null,[t("strong",null,"图片上传"),l("：直接点击图片按钮上传文件，自动插入到编辑器中")]),t("li",null,[t("strong",null,"工具栏优化"),l("：更直观的工具栏布局，下拉菜单点击触发，操作更精确")])])],-1))]),_:1,__:[5]}),i(f,{title:"表单集成示例",class:"mt-4"},{default:e(()=>[i(m(H),{schemas:h,labelWidth:120,onSubmit:v,onReset:b})]),_:1})])]),_:1})}}}),Wt=M(w,[["__scopeId","data-v-816bebbc"]]);export{Wt as default};
