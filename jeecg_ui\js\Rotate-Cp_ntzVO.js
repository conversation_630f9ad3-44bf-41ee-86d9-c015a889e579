import{d as a,ag as t,aB as s,ar as c,aD as n,at as p,k as i}from"./vue-vendor-dy9k-Yad.js";import{R as m}from"./index-Xq00qzUB.js";import{h as d}from"./header-OZa5fSDc.js";import{P as f}from"./index-CtJ0w2CP.js";import{a as _}from"./index-CCWaWN5g.js";import"./useTimeout-CeTdFD_D.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const l=a({components:{RotateDragVerify:m,PageWrapper:f},setup(){return{handleSuccess:()=>{},img:d}}}),u={class:"flex justify-center p-4 items-center bg-gray-700"};function g(e,h,y,R,S,V){const o=t("RotateDragVerify"),r=t("PageWrapper");return c(),s(r,{title:"旋转校验示例"},{default:n(()=>[p("div",u,[i(o,{src:e.img,ref:"el",onSuccess:e.handleSuccess},null,8,["src","onSuccess"])])]),_:1})}const w=_(l,[["render",g],["__scopeId","data-v-1cda40cc"]]);export{w as default};
