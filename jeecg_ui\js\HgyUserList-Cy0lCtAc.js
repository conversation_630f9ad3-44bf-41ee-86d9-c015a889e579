var W=Object.defineProperty,Y=Object.defineProperties;var Z=Object.getOwnPropertyDescriptors;var D=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,tt=Object.prototype.propertyIsEnumerable;var I=(r,t,o)=>t in r?W(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o,T=(r,t)=>{for(var o in t||(t={}))$.call(t,o)&&I(r,o,t[o]);if(D)for(var o of D(t))tt.call(t,o)&&I(r,o,t[o]);return r},A=(r,t)=>Y(r,Z(t));var x=(r,t,o)=>new Promise((U,g)=>{var l=a=>{try{c(o.next(a))}catch(d){g(d)}},k=a=>{try{c(o.throw(a))}catch(d){g(d)}},c=a=>a.done?U(a.value):Promise.resolve(a.value).then(l,k);c((o=o.apply(r,t)).next())});import{d as E,r as B,f as ot,ag as m,v as et,aq as nt,ar as u,k as s,aD as i,u as p,q as b,aB as _,ah as rt,G as f}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{useListPage as it}from"./useListPage-Soxgnx9a.js";import at from"./HgyUserModal-HYLDc5to.js";import{s as st,b as pt,g as mt,a as ut,c as lt,d as ct,l as dt,e as _t}from"./HgyUser.api-BDyvT4Fi.js";import{ah as ft,ad as gt,a as yt}from"./index-CCWaWN5g.js";import{Q as ht}from"./componentMap-Bkie1n3v.js";import bt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";const Ct=E({name:"auctionMan.auctionUserMan-hgyUser"}),Mt=E(A(T({},Ct),{setup(r){const t=B({}),o=ot([]),U=ft(),[g,{openModal:l}]=gt(),{prefixCls:k,tableContext:c,onExportXls:a,onImportXls:d}=it({tableProps:{title:"hgy_user",api:dt,columns:ct,canResize:!1,formConfig:{schemas:lt,autoSubmitOnEnter:!0,showAdvancedButton:!0,fieldMapToNumber:[],fieldMapToTime:[]},actionColumn:{width:120,fixed:"right"},beforeFetch:e=>Object.assign(e,t)},exportConfig:{name:"hgy_user",url:ut,params:t},importConfig:{url:mt,success:y}}),[R,{reload:v},{rowSelection:q,selectedRowKeys:C}]=c,F=B(st);function j(e){Object.keys(e).map(n=>{t[n]=e[n]}),v()}function N(){l(!0,{isUpdate:!1,showFooter:!0})}function O(e){l(!0,{record:e,isUpdate:!0,showFooter:!0})}function Q(e){l(!0,{record:e,isUpdate:!0,showFooter:!1})}function H(e){return x(this,null,function*(){yield _t({id:e.id},y)})}function K(){return x(this,null,function*(){yield pt({ids:C.value},y)})}function y(){(C.value=[])&&v()}function L(e){return[{label:"编辑",onClick:O.bind(null,e),auth:"auctionMan.auctionUserMan:hgy_user:edit"}]}function P(e){return[{label:"详情",onClick:Q.bind(null,e)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:H.bind(null,e),placement:"topLeft"},auth:"auctionMan.auctionUserMan:hgy_user:delete"}]}return(e,n)=>{const M=m("a-button"),V=m("j-upload-button"),S=m("Icon"),X=m("a-menu-item"),z=m("a-menu"),G=m("a-dropdown"),J=m("super-query"),h=et("auth");return u(),nt("div",null,[s(p(bt),{onRegister:p(R),rowSelection:p(q)},{tableTitle:i(()=>[b((u(),_(M,{type:"primary",onClick:N,preIcon:"ant-design:plus-outlined"},{default:i(()=>n[0]||(n[0]=[f(" 新增")])),_:1,__:[0]})),[[h,"auctionMan.auctionUserMan:hgy_user:add"]]),b((u(),_(M,{type:"primary",preIcon:"ant-design:export-outlined",onClick:p(a)},{default:i(()=>n[1]||(n[1]=[f(" 导出")])),_:1,__:[1]},8,["onClick"])),[[h,"auctionMan.auctionUserMan:hgy_user:exportXls"]]),b((u(),_(V,{type:"primary",preIcon:"ant-design:import-outlined",onClick:p(d)},{default:i(()=>n[2]||(n[2]=[f("导入")])),_:1,__:[2]},8,["onClick"])),[[h,"auctionMan.auctionUserMan:hgy_user:importExcel"]]),p(C).length>0?(u(),_(G,{key:0},{overlay:i(()=>[s(z,null,{default:i(()=>[s(X,{key:"1",onClick:K},{default:i(()=>[s(S,{icon:"ant-design:delete-outlined"}),n[3]||(n[3]=f(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:i(()=>[b((u(),_(M,null,{default:i(()=>[n[4]||(n[4]=f("批量操作 ")),s(S,{icon:"mdi:chevron-down"})]),_:1,__:[4]})),[[h,"auctionMan.auctionUserMan:hgy_user:deleteBatch"]])]),_:1})):rt("",!0),s(J,{config:F,onSearch:j},null,8,["config"])]),action:i(({record:w})=>[s(p(ht),{actions:L(w),dropDownActions:P(w)},null,8,["actions","dropDownActions"])]),bodyCell:i(({column:w,record:wt,index:xt,text:Ut})=>n[5]||(n[5]=[])),_:1},8,["onRegister","rowSelection"]),s(at,{onRegister:p(g),onSuccess:y},null,8,["onRegister"])])}}})),qo=yt(Mt,[["__scopeId","data-v-755b13bb"]]);export{qo as default};
