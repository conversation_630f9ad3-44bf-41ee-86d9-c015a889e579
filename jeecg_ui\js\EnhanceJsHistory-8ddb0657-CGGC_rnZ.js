import{cs as L,b7 as w,a1 as D,ac as I,Y as P}from"./index-CCWaWN5g.js";import{d as H,f as y,n as N,ag as n,aB as R,ar as T,aD as r,k as s,as as J,at as _,au as V,G as S}from"./vue-vendor-dy9k-Yad.js";import{B as F}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import z from"./JCodeEditor-B-WXz11X.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */var X=Object.defineProperty,E=Object.getOwnPropertySymbols,$=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable,M=(t,e,o)=>e in t?X(t,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[e]=o,k=(t,e)=>{for(var o in e||(e={}))$.call(e,o)&&M(t,o,e[o]);if(E)for(var o of E(e))G.call(e,o)&&M(t,o,e[o]);return t},K=(t,e,o)=>new Promise((p,d)=>{var f=i=>{try{c(o.next(i))}catch(m){d(m)}},g=i=>{try{c(o.throw(i))}catch(m){d(m)}},c=i=>i.done?p(i.value):Promise.resolve(i.value).then(f,g);c((o=o.apply(t,e)).next())});const j=D(),B="enhance_",Q=w({id:"online-cgform-enhance",state:()=>({enhanceJs:{}}),getters:{},actions:{getEnhanceJs(t){return this.enhanceJs[t]=j.get(B+t),this.enhanceJs[t]},addEnhanceJs(t){this.enhanceJs[t.code]?this.enhanceJs[t.code].push(k({},t)):this.enhanceJs[t.code]=[k({},t)];let e=this.enhanceJs[t.code];for(;e.length>16;)e.shift();j.set(B+t.code,e)}}}),U=H({name:"EnhanceJsHistory",components:{BasicModal:F,JCodeEditor:z},setup(){const t=Q(),e=y(),o=y(!1),p=y([]),d=y(""),f=y(0),[g,{closeModal:c}]=I(a=>K(this,null,function*(){i(a.code,a.type)}));function i(a,C){d.value="",p.value=[];let x=t.getEnhanceJs(a),l=[],u=0;for(let h of x)h.type===C&&(u++,l.push(Object.assign({},h,{index:u})));l&&l.length>0&&l.sort((h,O)=>O.date-h.date),p.value=[...l],N(()=>v(l[0]))}function m(){c()}function b(a){return P(a,"yyyy-MM-DD HH:mm:ss")}function v(a){f.value=a.index,e.value.setValue(a.str)}return{codeEditorRef:e,fullCode:v,registerModal:g,getFormatDate:b,onCancel:m,confirmLoading:o,dataList:p,jsStr:d,activeIndex:f}}}),W=["onClick"];function Y(t,e,o,p,d,f){const g=n("a-divider"),c=n("a-list-item"),i=n("a-list"),m=n("a-layout-sider"),b=n("JCodeEditor"),v=n("a-layout-content"),a=n("a-layout"),C=n("a-spin"),x=n("a-button"),l=n("BasicModal");return T(),R(l,{onRegister:t.registerModal,title:"JS增强历史记录",width:1200,maskClosable:!1,confirmLoading:t.confirmLoading,defaultFullscreen:"",onCancel:t.onCancel},{footer:r(()=>[s(x,{onClick:t.onCancel},{default:r(()=>e[1]||(e[1]=[S("关闭")])),_:1},8,["onClick"])]),default:r(()=>[s(C,{spinning:t.confirmLoading},{default:r(()=>[s(a,null,{default:r(()=>[s(m,{theme:"light"},{default:r(()=>[s(i,{bordered:"",dataSource:t.dataList,class:J("enhance-list")},{header:r(()=>[_("div",null,[s(g,{style:{margin:"0"}},{default:r(()=>e[0]||(e[0]=[S("保存时间")])),_:1})])]),renderItem:r(({item:u})=>[s(c,{class:J(t.activeIndex===u.index?"bg-blue":"")},{default:r(()=>[_("a",{onClick:h=>t.fullCode(u)},V(t.getFormatDate(u.date)),9,W)]),_:2},1032,["class"])]),_:1},8,["dataSource"])]),_:1}),s(a,null,{default:r(()=>[s(v,{style:{margin:"8px 8px",padding:"8px",background:"#fff",minHeight:"280px"}},{default:r(()=>[s(b,{ref:"codeEditorRef",language:"javascript",fullScreen:!0,lineNumbers:!1,"language-change":!1},null,512)]),_:1})]),_:1})]),_:1})]),_:1},8,["spinning"])]),_:1},8,["onRegister","confirmLoading","onCancel"])}const q=L(U,[["render",Y],["__scopeId","data-v-a2408ade"]]),At=Object.freeze(Object.defineProperty({__proto__:null,default:q},Symbol.toStringTag,{value:"Module"}));export{q as E,At as a,Q as u};
