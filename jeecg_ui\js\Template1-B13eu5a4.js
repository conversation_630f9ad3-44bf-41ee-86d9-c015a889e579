import{a as b,j as c,bx as a}from"./index-CCWaWN5g.js";import{ag as p,aB as w,ar as m,aD as g,k as s}from"./vue-vendor-dy9k-Yad.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const S={name:"Template1",data(){return{table1:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["10","20","30","100","200"],total:0},lastRow:null,selectedRows:[],dataSource:[],columns:[{key:"num",title:"序号",width:"80px"},{key:"ship_name",title:"船名",width:"180px",type:a.input,formatter({cellValue:e,row:l,column:i}){let n="";return l.company==="佧伒侾佯有限公司"&&(n+="-233"),e+n}},{key:"call",title:"呼叫",width:"80px",type:a.input},{key:"len",title:"长",width:"80px",type:a.inputNumber},{key:"ton",title:"吨",width:"120px",type:a.inputNumber},{key:"payer",title:"付款方",width:"120px",type:a.input},{key:"count",title:"数",width:"40px"},{key:"company",title:"公司",minWidth:"180px",type:a.input},{key:"trend",title:"动向",width:"120px",type:a.input}]},table2:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},lastRow:null,selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"180px",type:a.input},{key:"work_start_time",title:"作业开始时间",width:"180px",type:a.input},{key:"work_stop_time",title:"作业结束时间",width:"180px",type:a.input},{key:"type",title:"船舶分类",width:"120px",type:a.input},{key:"port_area",title:"所属港区",width:"120px",type:a.input}]},table3:{loading:!1,pagination:{current:1,pageSize:200,pageSizeOptions:["100","200"],total:0},selectedRows:[],dataSource:[],columns:[{key:"dd_num",title:"调度序号",width:"120px"},{key:"tug",title:"拖轮",width:"120px",type:a.input},{key:"power",title:"马力",width:"120px",type:a.input},{key:"nature",title:"性质",width:"120px",type:a.input},{key:"departure_time",title:"发船时间",width:"180px",type:a.input}]},url:{getData:"/mock/vxe/getData"}}},watch:{"table1.lastRow"(e){this.loadTable2Data()},"table2.lastRow"(){this.loadTable3Data()}},created(){this.loadTable1Data()},methods:{loadTable1Data(){let e={pageNo:this.table1.pagination.current,pageSize:this.table1.pagination.pageSize};this.table1.loading=!0,c.get({url:this.url.getData,params:e}).then(l=>{this.table1.pagination.total=l.total,this.table1.dataSource=l.records,this.table1.selectedRows=[]}).finally(()=>{this.table1.loading=!1})},handleTable1PageChange(e){this.table1.pagination.current=e.current,this.table1.pagination.pageSize=e.pageSize,this.loadTable1Data()},handleTable1SelectRowChange(e){this.handleTableSelectRowChange(this.table1,e)},loadTable2Data(){let e=this.table1.selectedRows;if(!e||e.length===0){this.table2.pagination.total=0,this.table2.dataSource=[],this.table2.selectedRows=[];return}else this.table1.lastRow==null&&(this.table1.lastRow=e[e.length-1]);let l={parentId:this.table1.lastRow.id,pageNo:this.table2.pagination.current,pageSize:this.table2.pagination.pageSize};this.table2.loading=!0,c.get({url:this.url.getData,params:l}).then(i=>{this.table2.pagination.total=i.total,this.table2.dataSource=i.records,this.table2.selectedRows=[]}).finally(()=>{this.table2.loading=!1})},handleTable2SelectRowChange(e){this.handleTableSelectRowChange(this.table2,e)},handleTable2PageChange(e){this.table2.pagination.current=e.current,this.table2.pagination.pageSize=e.pageSize,this.loadTable2Data()},loadTable3Data(){let e=this.table2.selectedRows;if(!e||e.length===0){this.table3.pagination.total=0,this.table3.dataSource=[],this.table3.selectedRows=[];return}else this.table2.lastRow==null&&(this.table2.lastRow=e[e.length-1]);let l={parentId:this.table2.lastRow.id,pageNo:this.table3.pagination.current,pageSize:this.table3.pagination.pageSize};this.table3.loading=!0,c.get({url:this.url.getData,params:l}).then(i=>{this.table3.pagination.total=i.total,this.table3.dataSource=i.records}).finally(()=>{this.table3.loading=!1})},handleTable3PageChange(e){this.table3.pagination.current=e.current,this.table3.pagination.pageSize=e.pageSize,this.loadTable3Data()},handleTableSelectRowChange(e,l){let{row:i,action:n,selectedRows:t,$table:o}=l,h=t[t.length-1];n==="selected"?e.lastRow=i:n==="selected-all"?t.length===0?e.lastRow=null:e.lastRow||(e.lastRow=h):n==="unselected"&&i===e.lastRow&&(e.lastRow=h),o.setCurrentRow(e.lastRow),e.selectedRows=t}}};function f(e,l,i,n,t,o){const h=p("JVxeTable"),r=p("a-col"),d=p("a-row"),u=p("a-card");return m(),w(u,{bordered:!1},{default:g(()=>[s(d,{gutter:8},{default:g(()=>[s(r,{span:24,style:{"margin-bottom":"4px"}},{default:g(()=>[s(h,{toolbar:"",rowNumber:"",rowSelection:"",clickSelectRow:"",highlightCurrentRow:"",radioConfig:{highlight:!1},checkboxConfig:{highlight:!1},height:340,loading:t.table1.loading,columns:t.table1.columns,dataSource:t.table1.dataSource,pagination:t.table1.pagination,onPageChange:o.handleTable1PageChange,onSelectRowChange:o.handleTable1SelectRowChange},null,8,["loading","columns","dataSource","pagination","onPageChange","onSelectRowChange"])]),_:1}),s(r,{span:12},{default:g(()=>[s(h,{toolbar:"",rowNumber:"",rowSelection:"",clickSelectRow:"",highlightCurrentRow:"",radioConfig:{highlight:!1},checkboxConfig:{highlight:!1},height:340,loading:t.table2.loading,columns:t.table2.columns,dataSource:t.table2.dataSource,pagination:t.table2.pagination,onPageChange:o.handleTable2PageChange,onSelectRowChange:o.handleTable2SelectRowChange},null,8,["loading","columns","dataSource","pagination","onPageChange","onSelectRowChange"])]),_:1}),s(r,{span:12},{default:g(()=>[s(h,{toolbar:"",rowNumber:"",rowSelection:"",height:340,loading:t.table3.loading,columns:t.table3.columns,dataSource:t.table3.dataSource,pagination:t.table3.pagination,onPageChange:o.handleTable3PageChange},null,8,["loading","columns","dataSource","pagination","onPageChange"])]),_:1})]),_:1})]),_:1})}const C=b(S,[["render",f]]);export{C as default};
