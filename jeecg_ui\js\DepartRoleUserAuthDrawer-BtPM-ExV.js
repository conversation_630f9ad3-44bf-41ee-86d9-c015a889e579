var w=(D,t,e)=>new Promise((c,r)=>{var l=a=>{try{d(e.next(a))}catch(m){r(m)}},o=a=>{try{d(e.throw(a))}catch(m){r(m)}},d=a=>a.done?c(a.value):Promise.resolve(a.value).then(l,o);d((e=e.apply(D,t)).next())});import{d as F,f as p,ag as n,aB as k,ar as _,u as g,aD as s,k as v,aq as x,F as B,at as b,aC as S,G as $,au as z}from"./vue-vendor-dy9k-Yad.js";import{u as A,B as E}from"./index-JbqXEynz.js";import{i as G,j as P,k as T}from"./depart.user.api-D_abnxSU.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";const H={style:{width:"100%","margin-top":"15px"}},ne=F({__name:"DepartRoleUserAuthDrawer",emits:["register"],setup(D){const t=p(!1),e=p(""),c=p(""),r=p(""),l=p([]),o=p([]),[d,{closeDrawer:a}]=A(u=>{e.value=g(u.userId),c.value=g(u.departId),m()});function m(){return w(this,null,function*(){try{t.value=!0;const u={departId:c.value,userId:e.value},[i,y]=yield Promise.all([G(u),P(u)]);l.value=i,o.value=y.map(I=>I.droleId),r.value=o.value.join(",")}finally{t.value=!1}})}function h(){return w(this,null,function*(){try{t.value=!0,yield T({userId:e.value,newRoleId:o.value.join(","),oldRoleId:r.value}),L()}finally{t.value=!1}})}function C(){R()}function L(){R(),a()}function R(){e.value="",c.value="",r.value="",l.value=[],o.value=[]}return(u,i)=>{const y=n("a-checkbox"),I=n("a-col"),N=n("a-row"),U=n("a-checkbox-group"),V=n("a-button"),j=n("a-empty"),q=n("a-spin");return _(),k(g(E),{title:"部门角色分配",width:365,onClose:C,onRegister:g(d)},{default:s(()=>[v(q,{spinning:t.value},{default:s(()=>[l.value.length>0?(_(),x(B,{key:0},[v(U,{value:o.value,"onUpdate:value":i[0]||(i[0]=f=>o.value=f)},{default:s(()=>[v(N,null,{default:s(()=>[(_(!0),x(B,null,S(l.value,f=>(_(),k(I,{span:24},{default:s(()=>[v(y,{value:f.id},{default:s(()=>[$(z(f.roleName),1)]),_:2},1032,["value"])]),_:2},1024))),256))]),_:1})]),_:1},8,["value"]),b("div",H,[v(V,{type:"primary",loading:t.value,size:"small",preIcon:"ant-design:save-filled",onClick:h},{default:s(()=>i[1]||(i[1]=[b("span",null,"点击保存",-1)])),_:1,__:[1]},8,["loading"])])],64)):(_(),k(j,{key:1,description:"无配置信息"}))]),_:1},8,["spinning"])]),_:1},8,["onRegister"])}}});export{ne as default};
