import{F as d,c as _,a as x,Q as w,R as v,a5 as C,a7 as $,w as m}from"./index-CCWaWN5g.js";import{d as y,e as i,aq as B,ar as u,as as f,k as a,u as p,aA as A,F as b,aM as T,aG as k,aB as I,ah as z}from"./vue-vendor-dy9k-Yad.js";import{T as g,aQ as M}from"./antd-vue-vendor-me9YkNVC.js";const F=y({__name:"BasicArrow",props:{expand:{type:Boolean},up:{type:Boolean},down:{type:Boolean},inset:{type:Boolean}},setup(e){const o=e,{prefixCls:t}=d("basic-arrow"),r=i(()=>{const{expand:s,up:l,down:n,inset:c}=o;return[t,{[`${t}--active`]:s,up:l,inset:c,down:n}]});return(s,l)=>(u(),B("span",{class:f(r.value)},[a(p(_),{icon:"ion:chevron-forward",style:A(s.$attrs.iconStyle)},null,8,["style"])],2))}}),N=x(F,[["__scopeId","data-v-3276be66"]]),O={maxWidth:{type:String,default:"600px"},showIndex:{type:Boolean},color:{type:String,default:"#ffffff"},fontSize:{type:String,default:"14px"},placement:{type:String,default:"right"},text:{type:[Array,String]}},S=y({name:"BasicHelp",components:{Tooltip:g},props:O,setup(e,{slots:o}){const{prefixCls:t}=d("basic-help"),r=i(()=>({color:e.color,fontSize:e.fontSize})),s=i(()=>({maxWidth:e.maxWidth}));function l(){const n=e.text;return w(n)?a("p",null,[n]):v(n)?n.map((c,h)=>a("p",{key:c},[a(b,null,[e.showIndex?`${h+1}. `:"",c])])):null}return()=>a(g,{overlayClassName:`${t}__wrap`,title:a("div",{style:p(r)},[l()]),autoAdjustOverflow:!0,overlayStyle:p(s),placement:e.placement,getPopupContainer:()=>C()},{default:()=>[a("span",{class:t},[$(o)||a(M,null,null)])]})}}),W=y({__name:"BasicTitle",props:{helpMessage:{type:[String,Array],default:""},span:{type:Boolean},normal:{type:Boolean}},setup(e){const o=e,{prefixCls:t}=d("basic-title"),r=T(),s=i(()=>[t,{[`${t}-show-span`]:o.span&&r.default},{[`${t}-normal`]:o.normal}]);return(l,n)=>(u(),B("span",{class:f(s.value)},[k(l.$slots,"default",{},void 0,!0),e.helpMessage?(u(),I(S,{key:0,class:f(`${p(t)}-help`),text:e.helpMessage},null,8,["class","text"])):z("",!0)],2))}}),H=x(W,[["__scopeId","data-v-c6f5f6d3"]]),j=m(N),q=m(H),D=m(S);export{q as B,S as _,D as a,j as b};
