var g=Object.defineProperty,_=Object.defineProperties;var S=Object.getOwnPropertyDescriptors;var n=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var a=(r,o,e)=>o in r?g(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,l=(r,o)=>{for(var e in o||(o={}))b.call(o,e)&&a(r,e,o[e]);if(n)for(var e of n(o))C.call(o,e)&&a(r,e,o[e]);return r},s=(r,o)=>_(r,S(o));import{d,aq as k,ar as x,k as c,aD as y,u as p}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import{useListPage as I}from"./useListPage-Soxgnx9a.js";import{Q as w}from"./componentMap-Bkie1n3v.js";import $ from"./BasicTable-xCEZpGLb.js";import"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./index-CImCetrx.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const P={class:"p-4"},R=d({name:"basic-table-demo"}),Dt=d(s(l({},R),{setup(r){const o=[{title:"名称",dataIndex:"name",customCell:(i,t,m)=>({colSpan:t<4?1:5}),customRender:({text:i,record:t,index:m,column:A})=>m<4?i:`${t.name}/${t.age}/${t.address}/${t.phone}`},{title:"年龄",dataIndex:"age",customCell:(i,t,m)=>{if(t==4)return{colSpan:0}}},{title:"家庭住址",dataIndex:"address",customCell:(i,t,m)=>{if(t==4)return{colSpan:0}}},{title:"联系电话",colSpan:2,dataIndex:"tel",customCell:(i,t,m)=>{if(t===2)return{rowSpan:2};if(t===3)return{rowSpan:0};if(t===4)return{colSpan:0}}},{title:"Phone",colSpan:0,dataIndex:"phone",customCell:(i,t,m)=>{if(t===4)return{colSpan:0}}}],{tableContext:e}=I({designScope:"basic-table-demo",tableProps:{title:"合并行列",dataSource:[{key:"1",name:"尹嘉乐",age:32,tel:"0319-5972018",phone:17600080009,address:"北京市昌平区"},{key:"2",name:"龙佳钰",tel:"0319-5972018",phone:17600060007,age:42,address:"北京市海淀区"},{key:"3",name:"贺泽惠",age:32,tel:"0319-5972018",phone:17600040005,address:"北京市门头沟区"},{key:"4",name:"沈勇",age:18,tel:"0319-5972018",phone:17600010003,address:"北京市朝阳区"},{key:"5",name:"白佳毅",age:18,tel:"0319-5972018",phone:17600010002,address:"北京市丰台区"}],columns:o,showActionColumn:!1,useSearchForm:!1}}),[u]=e;function f(i){return[{label:"编辑",onClick:h.bind(null,i)}]}function h(i){}return(i,t)=>(x(),k("div",P,[c(p($),{onRegister:p(u)},{action:y(({record:m})=>[c(p(w),{actions:f(m)},null,8,["actions"])]),_:1},8,["onRegister"])]))}}));export{Dt as default};
