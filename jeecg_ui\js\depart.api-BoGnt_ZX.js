import{u as n}from"./vue-vendor-dy9k-Yad.js";import{j as s,u as p}from"./index-CCWaWN5g.js";const{createConfirm:y}=p();var u=(e=>(e.queryDepartTreeSync="/sys/sysDepart/queryDepartTreeSync",e.save="/sys/sysDepart/add",e.edit="/sys/sysDepart/edit",e.delete="/sys/sysDepart/delete",e.deleteBatch="/sys/sysDepart/deleteBatch",e.exportXlsUrl="/sys/sysDepart/exportXls",e.importExcelUrl="/sys/sysDepart/importExcel",e.roleQueryTreeList="/sys/role/queryTreeList",e.queryDepartPermission="/sys/permission/queryDepartPermission",e.saveDepartPermission="/sys/permission/saveDepartPermission",e.dataRule="/sys/sysDepartPermission/datarule",e.getCurrentUserDeparts="/sys/user/getCurrentUserDeparts",e.selectDepart="/sys/selectDepart",e.getUpdateDepartInfo="/sys/user/getUpdateDepartInfo",e.doUpdateDepartInfo="/sys/user/doUpdateDepartInfo",e.changeDepartChargePerson="/sys/user/changeDepartChargePerson",e))(u||{});const c=e=>s.get({url:"/sys/sysDepart/queryDepartTreeSync",params:e}),d=(e,r)=>r?s.put({url:"/sys/sysDepart/edit",params:e}):s.post({url:"/sys/sysDepart/add",params:e}),i=(e,r=!1)=>new Promise((t,a)=>{const o=()=>{t(s.delete({url:"/sys/sysDepart/deleteBatch",params:e},{joinParamsToUrl:!0}))};r?y({iconType:"warning",title:"删除",content:"确定要删除吗？",onOk:()=>o(),onCancel:()=>a()}):o()}),m=e=>s.get({url:"/sys/role/queryTreeList",params:e}),g=e=>s.get({url:"/sys/permission/queryDepartPermission",params:e}),P=e=>s.post({url:"/sys/permission/saveDepartPermission",params:e}),f=(e,r,t)=>{let a=`/sys/sysDepartPermission/datarule/${n(e)}/${n(r)}`;return s.get({url:a,params:t})},U=e=>s.post({url:"/sys/sysDepartPermission/datarule",params:e}),h=e=>s.get({url:"/sys/user/getCurrentUserDeparts",params:e}),q=e=>s.put({url:"/sys/selectDepart",params:e});export{u as A,f as a,d as b,m as c,i as d,g as e,P as f,h as g,q as h,c as q,U as s};
