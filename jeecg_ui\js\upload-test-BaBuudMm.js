import{d as C,f as _,ag as g,aB as T,ar as r,aD as d,at as e,k as c,u as h,au as p,aq as u,ah as y,aC as L,as as k,F as V}from"./vue-vendor-dy9k-Yad.js";import{P as x}from"./index-CtJ0w2CP.js";import{T as B}from"./index-ByPySmGo.js";import{a as w}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const M={class:"p-4"},D={class:"mb-4"},H={class:"mt-4"},I={class:"content-html"},N={class:"mt-4"},b=["innerHTML"],E={class:"upload-log"},F={class:"log-time"},P={class:"log-message"},S={key:0,class:"no-logs"},U=C({__name:"upload-test",setup($){const o=_("<p>请测试图片上传功能</p>"),a=_([]),m=(n,t)=>{a.value.unshift({time:new Date().toLocaleTimeString(),type:n,message:t}),a.value.length>20&&(a.value=a.value.slice(0,20))},v=n=>{var i;const t=n.match(/<img[^>]+src="([^"]+)"[^>]*>/g);if(t){const l=t.length,s=((i=o.value.match(/<img[^>]+src="([^"]+)"[^>]*>/g))==null?void 0:i.length)||0;l>s&&m("success",`检测到新图片插入，当前共有 ${l} 张图片`)}m("info",`内容已更新，长度: ${n.length} 字符`)};return m("info","编辑器初始化完成"),(n,t)=>{const i=g("a-alert"),l=g("a-card");return r(),T(h(x),{title:"Tiptap图片上传测试"},{default:d(()=>[e("div",M,[c(l,{title:"图片上传功能测试"},{default:d(()=>[e("div",D,[c(i,{message:"测试说明",description:"点击编辑器工具栏中的图片按钮，选择图片文件进行上传测试。上传成功后图片会自动插入到编辑器中。",type:"info","show-icon":""})]),c(h(B),{modelValue:o.value,"onUpdate:modelValue":t[0]||(t[0]=s=>o.value=s),height:"400px",placeholder:"请点击工具栏中的图片按钮测试上传功能...",onChange:v},null,8,["modelValue"]),e("div",H,[t[1]||(t[1]=e("h4",null,"编辑器内容（HTML）：",-1)),e("pre",I,p(o.value),1)]),e("div",N,[t[2]||(t[2]=e("h4",null,"渲染效果：",-1)),e("div",{class:"content-preview",innerHTML:o.value},null,8,b)])]),_:1}),c(l,{title:"上传日志",class:"mt-4"},{default:d(()=>[e("div",E,[(r(!0),u(V,null,L(a.value,(s,f)=>(r(),u("div",{key:f,class:"log-item"},[e("span",F,p(s.time),1),e("span",{class:k(["log-type",s.type])},p(s.type.toUpperCase()),3),e("span",P,p(s.message),1)]))),128)),a.value.length===0?(r(),u("div",S," 暂无上传记录 ")):y("",!0)])]),_:1})])]),_:1})}}}),X=w(U,[["__scopeId","data-v-e6b60453"]]);export{X as default};
