var it=Object.defineProperty,rt=Object.defineProperties;var lt=Object.getOwnPropertyDescriptors;var I=Object.getOwnPropertySymbols;var ct=Object.prototype.hasOwnProperty,dt=Object.prototype.propertyIsEnumerable;var T=(l,s,o)=>s in l?it(l,s,{enumerable:!0,configurable:!0,writable:!0,value:o}):l[s]=o,z=(l,s)=>{for(var o in s||(s={}))ct.call(s,o)&&T(l,o,s[o]);if(I)for(var o of I(s))dt.call(s,o)&&T(l,o,s[o]);return l},S=(l,s)=>rt(l,lt(s));var D=(l,s,o)=>new Promise((k,y)=>{var u=p=>{try{i(o.next(p))}catch(g){y(g)}},m=p=>{try{i(o.throw(p))}catch(g){y(g)}},i=p=>p.done?k(p.value):Promise.resolve(p.value).then(u,m);i((o=o.apply(l,s)).next())});import{d as X,ap as ut,f as _,ag as L,aq as c,ar as d,at as t,au as r,k as h,aD as v,G as f,ah as C,as as x,F as M,aC as R,aA as B,u as P}from"./vue-vendor-dy9k-Yad.js";import{M as mt}from"./antd-vue-vendor-me9YkNVC.js";import{u as pt}from"./index-BkGZ5fiW.js";import{u as vt,aX as _t,a as ht}from"./index-CCWaWN5g.js";import ft from"./BasicTable-xCEZpGLb.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const gt={class:"account-overview"},bt={class:"cards-container"},kt={class:"account-card"},yt={class:"card-content"},Ct={class:"balance-amount"},xt={class:"integer-part"},Nt={class:"decimal-part"},wt={class:"card-actions"},It={class:"account-card"},Tt={class:"card-content"},zt={class:"balance-amount"},St={class:"integer-part"},Dt={class:"card-actions"},Lt={class:"account-card bank-card"},Mt={class:"card-header"},Rt={class:"card-title bank-card-title"},Bt={class:"card-actions-header"},Pt={class:"card-content"},Xt={class:"bank-card-display"},Ft={class:"card-header"},At={class:"bank-info"},Et={class:"bank-name"},Vt={class:"account-type"},Kt={class:"bank-logo"},Ot={class:"card-number"},qt={key:1,class:"cards-expanded"},$t={class:"cards-list"},jt=["onClick"],Gt={class:"card-header"},Wt={class:"bank-info"},Ht={class:"bank-name"},Jt={class:"account-type"},Qt={class:"bank-logo"},Ut={class:"card-number"},Yt={key:0,class:"selected-check"},Zt={class:"margin-table-container"},te={key:0},ee={key:1},ae=X({name:"AccountOverview"}),se=X(S(z({},ae),{setup(l){const{createMessage:s}=vt(),o=ut(),k=_(416546.2),y=_(16546),u=_([{id:1,bankName:"天瑞水泥集团有限公司",accountType:"对公账户",cardNumber:"6222 1234 1234 1234",bankLogo:"中国邮政储蓄银行",bgColor:"linear-gradient(180deg, #004C66 0%, #B3C9D1 100%)"},{id:2,bankName:"中国工商银行",accountType:"储蓄卡",cardNumber:"6222 5678 5678 5678",bankLogo:"中国工商银行",bgColor:"linear-gradient(135deg, #c0392b 0%, #e74c3c 100%)"},{id:3,bankName:"中国建设银行",accountType:"储蓄卡",cardNumber:"6222 9999 9999 9999",bankLogo:"中国建设银行",bgColor:"linear-gradient(135deg, #2980b9 0%, #3498db 100%)"}]),m=_(!1),i=_([]),p=[{title:"保证金编号",dataIndex:"marginId",width:160},{title:"资产名称",dataIndex:"assetName",width:200,ellipsis:!0},{title:"资产编号",dataIndex:"assetNo",width:180},{title:"保证金类型",dataIndex:"marginType",width:120},{title:"保证金金额（元）",dataIndex:"amount",width:150,slots:{customRender:"amount"}},{title:"缴费方式",dataIndex:"paymentMethod",width:120},{title:"缴费时间",dataIndex:"paymentTime",width:160,customRender:({text:a})=>a?_t(a):"-"},{title:"保证金状态",dataIndex:"status",width:120,slots:{customRender:"status"}},{title:"操作人",dataIndex:"operator",width:100},{title:"备注",dataIndex:"remark",width:150,ellipsis:!0}],g=[{key:"all",label:"全部保证金",icon:""},{key:"paid",label:"保证金缴纳",icon:""},{key:"frozen",label:"保证金冻结",icon:""},{key:"returned",label:"保证金退还",icon:""}],N=_("all"),F=_({});function A(a){N.value=a;let e={};switch(a){case"paid":e={status:1};break;case"frozen":e={status:2};break;case"returned":e={status:3};break;default:e={}}F.value=e,K()}function E(a){return D(this,null,function*(){const e={records:[{marginId:"123456789",assetName:"XX特钢",assetNo:"上海XX废旧物资有限公司",marginType:"上海市公司",amount:4645.5,paymentMethod:"银行汇款转账",paymentTime:"2025-05-02 13:00:59",status:1,operator:"张三",remark:"XX项目保证金"}],total:1};return Promise.resolve(e)})}const[V,{reload:K}]=pt({api:E,columns:p,striped:!1,useSearchForm:!1,showTableSetting:!1,bordered:!1,showIndexColumn:!1,canResize:!0,showNavigation:!0,navigationItems:g,activeNavigationKey:N.value,inset:!0,scroll:{y:400}});function O(a){return a.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})}function q(a){return a.toLocaleString("zh-CN")}function $(a){return Math.floor(a).toLocaleString("zh-CN")}function j(a){return(a%1).toFixed(2).substring(2)}function G(a){switch(a){case 1:return"green";case 2:return"orange";case 3:return"blue";default:return"default"}}function W(a){switch(a){case 1:return"已缴纳";case 2:return"已冻结";case 3:return"已退还";default:return"-"}}function H(){o.push("/account/inventory")}function J(){s.info("提现功能")}function Q(){s.info("充值功能")}function U(){o.push("/account/integral")}function Y(){s.info("积分兑换")}function Z(){s.info("添加银行卡")}function ne(){s.info("管理银行卡")}function tt(){m.value=!0,i.value=[]}function et(){m.value=!1,i.value=[]}function at(a){return a===0?{transform:"translate(0, 0)",zIndex:2,background:u.value[a].bgColor}:{display:"none"}}function st(a){const e=i.value.indexOf(a);e>-1?i.value.splice(e,1):i.value.push(a)}function nt(){i.value.length!==0&&mt.confirm({title:"确认删除",content:`确定要删除选中的 ${i.value.length} 张银行卡吗？`,onOk:()=>{u.value=u.value.filter(a=>!i.value.includes(a.id)),i.value=[],s.success("删除成功"),u.value.length===0&&(m.value=!1)}})}return(a,e)=>{const b=L("a-button"),ot=L("a-tag");return d(),c("div",gt,[t("div",bt,[t("div",kt,[e[5]||(e[5]=t("div",{class:"card-header"},[t("span",{class:"card-title"},"我的账户")],-1)),t("div",yt,[e[4]||(e[4]=t("div",{class:"balance-label"},"可用余额",-1)),t("div",Ct,[e[0]||(e[0]=t("span",{class:"currency-symbol"},"¥",-1)),t("span",xt,r($(k.value)),1),t("span",Nt,"."+r(j(k.value)),1)]),t("div",wt,[h(b,{type:"default",size:"small",onClick:H},{default:v(()=>e[1]||(e[1]=[f("流水")])),_:1,__:[1]}),h(b,{type:"default",size:"small",onClick:J},{default:v(()=>e[2]||(e[2]=[f("提现")])),_:1,__:[2]}),h(b,{type:"primary",size:"small",onClick:Q},{default:v(()=>e[3]||(e[3]=[f("充值")])),_:1,__:[3]})])])]),t("div",It,[e[9]||(e[9]=t("div",{class:"card-header"},[t("span",{class:"card-title"},"我的积分")],-1)),t("div",Tt,[e[8]||(e[8]=t("div",{class:"balance-label"},"可用积分",-1)),t("div",zt,[t("span",St,r(q(y.value)),1)]),t("div",Dt,[h(b,{type:"default",size:"small",onClick:U},{default:v(()=>e[6]||(e[6]=[f("积分明细")])),_:1,__:[6]}),h(b,{type:"primary",size:"small",onClick:Y},{default:v(()=>e[7]||(e[7]=[f("去使用")])),_:1,__:[7]})])])]),t("div",Lt,[t("div",Mt,[t("span",Rt,"我的银行卡 · "+r(u.value.length)+"张",1),t("div",Bt,[t("span",{class:"action-link",onClick:Z},"添加"),m.value&&i.value.length>0?(d(),c("span",{key:0,class:"action-link delete-btn",onClick:nt}," 删除("+r(i.value.length)+") ",1)):C("",!0),m.value?(d(),c("span",{key:1,class:"action-link collapse-btn",onClick:et},"收起")):C("",!0)])]),t("div",Pt,[t("div",{class:x(["bank-cards-container",{expanded:m.value}])},[m.value?(d(),c("div",qt,[t("div",$t,[(d(!0),c(M,null,R(u.value,n=>(d(),c("div",{key:n.id,class:x(["bank-card-item expanded",{selected:i.value.includes(n.id)}]),onClick:w=>st(n.id)},[t("div",{class:"bank-card-display",style:B({background:n.bgColor})},[t("div",Gt,[t("div",Wt,[t("div",Ht,r(n.bankName),1),t("div",Jt,r(n.accountType),1)]),t("div",Qt,r(n.bankLogo),1)]),t("div",Ut,r(n.cardNumber),1),i.value.includes(n.id)?(d(),c("div",Yt,e[10]||(e[10]=[t("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"},[t("circle",{cx:"12",cy:"12",r:"12",fill:"#52c41a"}),t("path",{d:"M9 12l2 2 4-4",stroke:"white","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)]))):C("",!0)],4)],10,jt))),128))])])):(d(),c("div",{key:0,class:x(["cards-stack",{"single-card":u.value.length===1}]),onClick:tt},[(d(!0),c(M,null,R(u.value,(n,w)=>(d(),c("div",{key:n.id,class:"bank-card-item stacked",style:B(at(w))},[t("div",Xt,[t("div",Ft,[t("div",At,[t("div",Et,r(n.bankName),1),t("div",Vt,r(n.accountType),1)]),t("div",Kt,r(n.bankLogo),1)]),t("div",Ot,r(n.cardNumber),1)])],4))),128))],2))],2)])])]),t("div",Zt,[e[11]||(e[11]=t("div",{class:"table-header"},[t("span",{class:"table-title"},"我的保证金")],-1)),h(P(ft),{onRegister:P(V),onNavigationChange:A},{status:v(({record:n})=>[h(ot,{color:G(n.status)},{default:v(()=>[f(r(W(n.status)),1)]),_:2},1032,["color"])]),amount:v(({text:n})=>[n?(d(),c("span",te,r(O(n)),1)):(d(),c("span",ee,"-"))]),_:1},8,["onRegister"])])])}}})),ca=ht(se,[["__scopeId","data-v-14b0ab3d"]]);export{ca as default};
