import{d as H,f as C,r as W,e as X,n as Y,ag as l,aB as F,ar as _,aD as i,k as n,ah as A,G as d,aJ as Z,aK as ee,aq as oe}from"./vue-vendor-dy9k-Yad.js";import{B as te}from"./index-Diw57m_E.js";import"./index-L3cSIXth.js";import"./index-BkGZ5fiW.js";import{useListPage as re}from"./useListPage-Soxgnx9a.js";import{J as ie}from"./useOnlineTest-e4bd8be3-hnhsV9Hd.js";import{c as ne,p as ae}from"./enhance.data-6601ff44-CPj6ao2j.js";import{M as le,C as P,D as ce}from"./enhance.api-138e6826-BOpOzAwu.js";import{cs as se,ac as me,ad as pe}from"./index-CCWaWN5g.js";import"./cgform.data-0ca62d09-CBB13rBO.js";import{u as ue}from"./useForm-CgkFTrrO.js";import{Q as de}from"./componentMap-Bkie1n3v.js";import{B as fe}from"./BasicForm-DBcXiHk0.js";import ge from"./BasicTable-xCEZpGLb.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";var be=Object.defineProperty,O=Object.getOwnPropertySymbols,ye=Object.prototype.hasOwnProperty,ve=Object.prototype.propertyIsEnumerable,j=(e,o,t)=>o in e?be(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,he=(e,o)=>{for(var t in o||(o={}))ye.call(o,t)&&j(e,t,o[t]);if(O)for(var t of O(o))ve.call(o,t)&&j(e,t,o[t]);return e},f=(e,o,t)=>new Promise((g,u)=>{var b=a=>{try{s(t.next(a))}catch(m){u(m)}},c=a=>{try{s(t.throw(a))}catch(m){u(m)}},s=a=>a.done?g(a.value):Promise.resolve(a.value).then(b,c);s((t=t.apply(e,o)).next())});const we=H({name:"EnhanceSqlModal",components:{BasicModal:te,BasicTable:ge,BasicForm:fe,TableAction:de},emits:["register"],setup(){const e=C(""),o=C(""),t=C([]),{columns:g}=ne(t),{doRequest:u,doDeleteRecord:b,tableContext:c}=re({tableProps:{api:r=>f(this,null,function*(){let{dataSource:w,btnList:Q}=yield le(e.value,r);return t.value=Q,w}),columns:g,canResize:!1,useSearchForm:!1,beforeFetch(r){return Object.assign(r,{column:"orderNum",order:"asc"})}}}),[s,{reload:a},{rowSelection:m,selectedRowKeys:y}]=c,[M,{closeModal:k}]=me(r=>f(this,null,function*(){e.value=r.row.id,o.value=r.row.tableName,a()})),{aiTestMode:B,genEnhanceSqlData:S}=ie(),[v,p]=pe(),h=C(!1),R=W({onRegister:v,title:X(()=>h!=null&&h.value?"修改":"新增"),width:800,confirmLoading:!1,onOk:U,onCancel:p.closeModal});let T={};const{formSchemas:x}=ae(t),[E,{resetFields:L,setFieldsValue:q,validate:K}]=ue({schemas:x,showActionButtonGroup:!1,labelWidth:80,labelCol:null,wrapperCol:null});function N(){k()}function D(r){return f(this,null,function*(){var w;h.value=r.isUpdate,T=he({},(w=r.record)!=null?w:{}),p.openModal(),yield Y(),yield L(),q(T)})}function G(){D({isUpdate:!1})}function I(r){D({isUpdate:!0,record:r})}function V(){S(e.value,o.value)}function $(){return f(this,null,function*(){u(()=>P(y.value))})}function U(){return f(this,null,function*(){try{R.confirmLoading=!0;let r=yield K();r=Object.assign({},T,r),yield ce(e.value,r,h.value),a(),p.closeModal()}finally{R.confirmLoading=!1}})}function z(r){return[{label:"编辑",onClick:()=>I(r)}]}function J(r){return[{label:"删除",popConfirm:{title:"确定删除吗？",placement:"left",confirm:()=>b(()=>P([r.id]))}}]}return{rowSelection:m,selectedRowKeys:y,aiTestMode:B,onCancel:N,onAdd:G,onGenEnhanceSqlData:V,onBatchDelete:$,getTableAction:z,getDropDownAction:J,formModalProps:R,registerModal:M,registerTable:s,registerForm:E}},computed:{tableScroll(){return{y:window.innerHeight-320}}}}),Ce={key:0,style:{float:"left"}};function Me(e,o,t,g,u,b){const c=l("a-button"),s=l("a-icon"),a=l("a-menu-item"),m=l("a-menu"),y=l("a-dropdown"),M=l("TableAction"),k=l("BasicTable"),B=l("BasicForm"),S=l("a-spin"),v=l("BasicModal");return _(),F(v,{onRegister:e.registerModal,title:"SQL增强",width:1200,defaultFullscreen:"",onCancel:e.onCancel},{footer:i(()=>[n(c,{onClick:e.onCancel},{default:i(()=>o[3]||(o[3]=[d("关闭")])),_:1},8,["onClick"]),e.aiTestMode?(_(),oe("div",Ce,[n(c,{onClick:e.onGenEnhanceSqlData},{default:i(()=>o[4]||(o[4]=[d("生成测试数据")])),_:1},8,["onClick"])])):A("",!0)]),default:i(()=>[n(k,{onRegister:e.registerTable,rowSelection:e.rowSelection},{tableTitle:i(()=>[n(c,{onClick:e.onAdd,type:"primary",preIcon:"ant-design:plus"},{default:i(()=>o[0]||(o[0]=[d("新增")])),_:1},8,["onClick"]),e.selectedRowKeys.length>0?(_(),F(y,{key:0},{overlay:i(()=>[n(m,null,{default:i(()=>[n(a,{key:"1",onClick:e.onBatchDelete},{default:i(()=>[n(s,{type:"delete"}),o[1]||(o[1]=d(" 删除 "))]),_:1},8,["onClick"])]),_:1})]),default:i(()=>[n(c,{style:{"margin-left":"8px"}},{default:i(()=>[o[2]||(o[2]=d(" 批量操作 ")),n(s,{type:"down"})]),_:1})]),_:1})):A("",!0)]),action:i(({record:p})=>[n(M,{actions:e.getTableAction(p),dropDownActions:e.getDropDownAction(p)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),n(v,Z(ee(e.formModalProps)),{default:i(()=>[n(S,{spinning:e.formModalProps.confirmLoading},{default:i(()=>[n(B,{class:"popupForm",onRegister:e.registerForm},null,8,["onRegister"])]),_:1},8,["spinning"])]),_:1},16)]),_:1},8,["onRegister","onCancel"])}const Po=se(we,[["render",Me],["__scopeId","data-v-0a46f791"]]);export{Po as default};
