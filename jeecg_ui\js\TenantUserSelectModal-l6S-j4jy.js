const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/BasicTable-xCEZpGLb.js","js/vue-vendor-dy9k-Yad.js","js/antd-vue-vendor-me9YkNVC.js","js/index-L3cSIXth.js","js/BasicForm-DBcXiHk0.js","js/componentMap-Bkie1n3v.js","js/index-CCWaWN5g.js","js/vxe-table-vendor-B22HppNm.js","assets/index-CEfKi2su.css","js/useFormItem-CHvpjy4o.js","js/index-Diw57m_E.js","js/BasicModal-BLFvpBuk.js","js/ModalHeader-BJG9dHtK.js","js/useTimeout-CeTdFD_D.js","js/index-CImCetrx.js","assets/index-BObJM2Lc.css","assets/ModalHeader-HwQKX-UU.css","js/useWindowSizeFn-DDbrQbks.js","js/index-LCGLvkB3.js","js/index-De_W6s5g.js","js/index-D6l0IxOU.js","js/useIntersectionObserver-C4LVxQJW.js","assets/index-zj-Vfn3Q.css","assets/BasicModal-ByeTDAzn.css","js/CustomModal-BakuIxQv.js","assets/CustomModal-DWxHZmza.css","assets/index-yRxe3SQ1.css","js/download-CZ-9H9a3.js","js/base64Conver-24EVOS6V.js","js/index-CBCjSSNZ.js","assets/index-NmxXH94f.css","js/index-DFrpKMGa.js","js/useCountdown-CCWNeb_r.js","js/useFormItemSingle-Cw668yj5.js","assets/index-BB9COjV3.css","js/JSelectUser-COkExGbu.js","js/props-CCT78mKr.js","js/JSelectBiz-jOYRdMJf.js","assets/JSelectBiz-CYw1rOZ6.css","assets/JSelectUser-CQvjZTEr.css","js/JAddInput-CxJ-JBK-.js","js/index-QxsVJqiT.js","js/index-BtIdS_Qz.js","js/bem-sRx7x0Ii.js","js/props-qAqCef5R.js","js/useContextMenu-BU2ycxls.js","assets/useContextMenu-DRJLeHo9.css","assets/index-D8VMPii6.css","js/depart.api-BoGnt_ZX.js","assets/JAddInput-i6a6KIoQ.css","js/JSelectDept-I-NqkbOH.js","assets/JSelectDept-WHP406xL.css","js/JAreaSelect-Db7Nhhc_.js","js/areaDataUtil-BXVjRArW.js","assets/JAreaSelect-Pwl_5U28.css","js/JEditorTiptap-BwAoWsi9.js","js/index-ByPySmGo.js","assets/index-BrdQT4ew.css","js/JPopup-CeU6ry6r.js","assets/JPopup-Dn0_YeSX.css","js/JEllipsis-BsXuWNHJ.js","js/JUpload-CRos0F1P.js","assets/JUpload-CsrjJkIs.css","js/JSearchSelect-c_lfTydU.js","js/index-CXHeQyuE.js","js/index-Dyko68ZT.js","assets/index-CTbO_Zqi.css","assets/componentMap-Degzw4_e.css","assets/BasicForm-DTEnYz8c.css","js/useForm-CgkFTrrO.js","js/JAreaLinkage-DFCdF3cr.js","js/JCodeEditor-B-WXz11X.js","js/htmlmixed-CmvhkW5V.js","js/vue-CAKGUkuE.js","assets/vue-DyVx2_Fd.css","assets/JCodeEditor-DaPRKM4Q.css","assets/idea-C3eFBO7g.css","js/EasyCronInput-BuvtO5dv.js","assets/EasyCronInput-BLbXuoBB.css","js/JUploadModal-C-iKhVFc.js","js/injectionKey-DPVn4AgL.js","assets/BasicTable-DcVosJye.css"])))=>i.map(i=>d[i]);
import{aj as x,ac as _,_ as h,a as T}from"./index-CCWaWN5g.js";import{d as b,f as n,ag as p,aq as I,ar as C,k as l,aE as S,aD as d}from"./vue-vendor-dy9k-Yad.js";import{B as y}from"./index-Diw57m_E.js";import{e as R}from"./tenant.api-CTNrRQ_d.js";import{useListPage as v}from"./useListPage-Soxgnx9a.js";import{u as B,a as O}from"./tenant.data-BEsk-IZ-.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-BkGZ5fiW.js";import"./BasicTable-xCEZpGLb.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";import"./renderUtils-D7XVOFwj.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";const k=b({name:"TenantUserSelectModal",components:{BasicModal:y,BasicTable:x(()=>h(()=>import("./BasicTable-xCEZpGLb.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81])),{loading:!0})},props:{modalTitle:{type:String,default:"选择用户"},tenantId:{type:Number,default:0},excludeUserIdList:{type:Array,default:[]}},emits:["register","on-select"],setup(t,{emit:e,refs:c}){const o=n({x:!1}),r=n(),[s,{closeModal:m}]=_(()=>{window.innerWidth<900?o.value={x:900}:o.value={x:!1},setTimeout(()=>{r.value&&r.value.setSelectedRowKeys([])},800)}),i=[{title:"账号",dataIndex:"username",width:40,align:"left"},{title:"姓名",dataIndex:"realname",width:40},{title:"性别",dataIndex:"sex_dictText",width:20},{title:"手机号码",dataIndex:"phone",width:30},{title:"邮箱",dataIndex:"email",width:40},{title:"状态",dataIndex:"status_dictText",width:20}],{prefixCls:P,tableContext:u}=v({designScope:"tenant-template",tableProps:{api:R,columns:O,scroll:{y:390},rowKey:"id",showActionColumn:!1,formConfig:{schemas:B,labelWidth:60,actionColOptions:{xs:24,sm:8,md:8,lg:8,xl:8,xxl:8}},beforeFetch:g=>Object.assign(g,{userTenantId:t.tenantId})}}),[f,{reload:$},{rowSelection:a,selectedRowKeys:A}]=u;function w(){e("on-select",a.selectedRows,a.selectedRowKeys)}return{handleOk:w,register:s,columns:i,rowSelection:a,tableScroll:o,tableRef:r,registerTable:f}}});function M(t,e,c,o,r,s){const m=p("BasicTable"),i=p("BasicModal");return C(),I("div",null,[l(i,S(t.$attrs,{onRegister:t.register,title:t.modalTitle,width:"900px",wrapClassName:"j-user-select-modal",onOk:t.handleOk,destroyOnClose:""}),{default:d(()=>[l(m,{ref:"tableRef",onRegister:t.registerTable,rowSelection:t.rowSelection},{tableTitle:d(()=>e[0]||(e[0]=[])),_:1},8,["onRegister","rowSelection"])]),_:1},16,["onRegister","title","onOk"])])}const Wt=T(k,[["render",M],["__scopeId","data-v-3efd8791"]]);export{Wt as default};
