import{d as I,f as s,ag as d,aB as F,ar as P,aD as n,k as u,at as T,G as h}from"./vue-vendor-dy9k-Yad.js";import{cs as j,u as q,F as D,ac as K,j as x}from"./index-CCWaWN5g.js";import"./index-BkGZ5fiW.js";import{B as V}from"./index-Diw57m_E.js";import{useListTable as z}from"./useListPage-Soxgnx9a.js";import A from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";const E=I({name:"TransDb2Online",components:{BasicModal:V,BasicTable:A},emits:["success","register"],setup(o,{emit:t}){const{createMessage:g}=q(),i=s("暂无数据"),p=s(!1),l=s(!1),a=s([]),r=s([]),{prefixCls:c}=D("online-db-import-form-modal"),f=c,v=document.documentElement.clientHeight,C=v-180,y=C>690?690:C,[L,{setPagination:M,getForm:B},{rowSelection:k,selectedRowKeys:m}]=z({bordered:!0,columns:[{title:"表名",align:"left",dataIndex:"id"}],dataSource:r,maxHeight:v-400,locale:{emptyText:i},pagination:{showQuickJumper:!1,showSizeChanger:!1},clickToRowSelect:!0,showIndexColumn:!0,showActionColumn:!1,formConfig:{schemas:[{label:"表名",field:"tableName",component:"Input",componentProps:{style:{width:"100%"},placeholder:"请输入表名以模糊筛选",onChange:e=>N(e.target.value)},disabledLabelWidth:!0,itemProps:{labelCol:{sm:0,md:0},wrapperCol:{sm:24,md:20}}}],baseColProps:{xs:24,sm:24,md:24,lg:24,xl:24,xxl:24},showActionButtonGroup:!1}}),[R,{closeModal:w}]=K(()=>{var e;(e=B())==null||e.resetFields(),l.value=!1,i.value="暂无数据",m.value=[],b()});function b(){return p.value=!0,x.get({url:"/online/cgform/head/queryTables"},{errorMessageMode:"none"}).then(e=>(r.value=e,a.value=[...e],e),e=>{e.message=="noadminauth"&&(i.value="非admin用户无权限操作！",g.warn(i.value)),r.value=[],a.value=[]}).finally(()=>{p.value=!1})}function N(e){a.value.length!==0&&(e?(r.value=a.value.filter(H=>H.id.toLowerCase().includes(e.toLowerCase())),i.value=r.value.length===0?"无筛选结果":"暂无数据"):r.value=[...a.value],M({current:1}))}function S(){w()}function _(){if(!m.value||m.value.length==0){g.warning("请选择一张表");return}else{l.value=!0;let e=m.value.join(",");x.post({url:"/online/cgform/head/transTables/"+e},{errorMessageMode:"modal"}).then(()=>{w()}).finally(()=>{l.value=!1,t("success")})}}return{emptyText:i,confirmLoading:p,btnLoading:l,metaSource:a,handleTrans:_,handleCancel:S,queryTables:b,registerModal:R,registerTable:L,rowSelection:k,selectedRowKeys:m,wrapClassName:f,modalHeight:y}}});function G(o,t,g,i,p,l){const a=d("BasicTable"),r=d("a-spin"),c=d("a-button"),f=d("BasicModal");return P(),F(f,{onRegister:o.registerModal,height:o.modalHeight,width:500,title:"从数据库导入表单",confirmLoading:o.confirmLoading,onCancel:o.handleCancel,wrapClassName:o.wrapClassName},{footer:n(()=>[u(c,{onClick:o.handleCancel},{default:n(()=>t[1]||(t[1]=[h("关闭")])),_:1},8,["onClick"]),u(c,{onClick:o.handleTrans,type:"primary",preIcon:"ant-design:swap",loading:o.confirmLoading||o.btnLoading},{default:n(()=>t[2]||(t[2]=[h(" 生成表单 ")])),_:1},8,["onClick","loading"])]),default:n(()=>[u(r,{spinning:o.confirmLoading},{default:n(()=>[u(a,{onRegister:o.registerTable,rowSelection:o.rowSelection,onTableRedo:o.queryTables},{tableTitle:n(()=>t[0]||(t[0]=[T("div",null,[h(" 注：导入表会排除配置前缀表 "),T("a",{href:"http://doc.jeecg.com/2043924",target:"_blank"}," 参考文档")],-1)])),_:1},8,["onRegister","rowSelection","onTableRedo"])]),_:1},8,["spinning"])]),_:1},8,["onRegister","height","confirmLoading","onCancel","wrapClassName"])}const Ue=j(E,[["render",G]]);export{Ue as default};
