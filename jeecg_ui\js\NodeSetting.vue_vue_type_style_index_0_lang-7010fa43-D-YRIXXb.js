import{d as O,f as d,w as k,n as P,e as x,ag as v,aq as c,ar as p,F as j,ah as m,as as C,u as y,aB as h,aJ as N,aE as S,k as q}from"./vue-vendor-dy9k-Yad.js";import{F as B}from"./index-CCWaWN5g.js";import{t as E}from"./index-9c51646a-BviQbLw-.js";var F=Object.defineProperty,g=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable,f=(n,e,r)=>e in n?F(n,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[e]=r,V=(n,e)=>{for(var r in e||(e={}))J.call(e,r)&&f(n,r,e[r]);if(g)for(var r of g(e))z.call(e,r)&&f(n,r,e[r]);return n},_=(n,e,r)=>new Promise((s,o)=>{var l=a=>{try{t(r.next(a))}catch(i){o(i)}},u=a=>{try{t(r.throw(a))}catch(i){o(i)}},t=a=>a.done?s(a.value):Promise.resolve(a.value).then(l,u);t((r=r.apply(n,e)).next())});const $={key:1,style:{"margin-top":"100px"}},T=O({__name:"NodeSetting",props:{type:{type:String,required:!0},node:{type:Object,required:!0},properties:{type:Object,required:!0},setProperties:{type:Function,required:!0}},setup(n){const e=n,{prefixCls:r}=B("airag-node-setting-container");let s=null;const o=d(!0),l=d(!1);k(()=>{var t;return(t=e.node)==null?void 0:t.id},()=>_(this,null,function*(){l.value=!0,s=e.type?E.get(e.type):null,o.value=s==null,yield P(),l.value=!1}),{immediate:!0});const u=x({get:()=>{if(!e.properties)return"";const t=V({},e.properties);return delete t.width,delete t.height,JSON.stringify(t,null,2)},set:t=>{const a=JSON.parse(t);a.width=e.properties.width,a.height=e.properties.height,e.setProperties(a)}});return(t,a)=>{const i=v("a-empty"),w=v("a-textarea");return p(),c(j,null,[l.value?m("",!0):(p(),c("div",{key:0,class:C([y(r)])},[o.value?(p(),h(i,{key:0,description:"暂无配置"})):(p(),h(y(s),N(S({key:1},t.$props)),null,16))],2)),o.value?(p(),c("div",$,[q(w,{value:u.value,"onUpdate:value":a[0]||(a[0]=b=>u.value=b),style:{width:"100%",height:"30vh"}},null,8,["value"])])):m("",!0)],64)}}});export{T as A};
