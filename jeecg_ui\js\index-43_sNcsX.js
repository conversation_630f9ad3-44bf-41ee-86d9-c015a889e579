import{d as m,ag as i,aB as p,ar as _,aD as s,k as t,at as n,G as d}from"./vue-vendor-dy9k-Yad.js";import{P as f}from"./index-CtJ0w2CP.js";import{a7 as g,a6 as y,J as k}from"./antd-vue-vendor-me9YkNVC.js";import{a as b}from"./index-CCWaWN5g.js";import"./useContentHeight-bZ7VSBAL.js";import"./useWindowSizeFn-DDbrQbks.js";import"./vxe-table-vendor-B22HppNm.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const w=m({components:{PageWrapper:f,ACard:k,ARow:y,ACol:g}}),v={class:"my-2"},x={class:"py-2"},B={class:"my-2"},C={class:"my-2"},P={class:"my-2"},$={class:"my-2"},A={class:"my-2"},N={class:"my-2"},V={class:"my-2"},W={class:"bg-gray-400 py-2"};function I(R,l,T,D,F,G){const o=i("a-button"),a=i("a-card"),r=i("a-col"),e=i("a-row"),u=i("PageWrapper");return _(),p(u,{contentFullHeight:"",title:"基础组件",content:" 基础组件依赖于ant-design-vue,组件库已有的基础组件,项目中不会再次进行demo展示（二次封装组件除外）"},{default:s(()=>[t(e,{gutter:[20,20]},{default:s(()=>[t(r,{xl:10,lg:24},{default:s(()=>[t(a,{title:"BasicButton Color"},{default:s(()=>[n("div",v,[l[6]||(l[6]=n("h3",null,"success",-1)),n("div",x,[t(o,{color:"success"},{default:s(()=>l[0]||(l[0]=[d(" 成功 ")])),_:1,__:[0]}),t(o,{color:"success",class:"ml-2",disabled:""},{default:s(()=>l[1]||(l[1]=[d(" 禁用 ")])),_:1,__:[1]}),t(o,{color:"success",class:"ml-2",loading:""},{default:s(()=>l[2]||(l[2]=[d(" loading ")])),_:1,__:[2]}),t(o,{color:"success",type:"link",class:"ml-2"},{default:s(()=>l[3]||(l[3]=[d(" link ")])),_:1,__:[3]}),t(o,{color:"success",type:"link",class:"ml-2",loading:""},{default:s(()=>l[4]||(l[4]=[d(" loading link ")])),_:1,__:[4]}),t(o,{color:"success",type:"link",class:"ml-2",disabled:""},{default:s(()=>l[5]||(l[5]=[d(" disabled link ")])),_:1,__:[5]})])]),n("div",B,[l[13]||(l[13]=n("h3",null,"warning",-1)),t(o,{color:"warning"},{default:s(()=>l[7]||(l[7]=[d(" 警告 ")])),_:1,__:[7]}),t(o,{color:"warning",class:"ml-2",disabled:""},{default:s(()=>l[8]||(l[8]=[d(" 禁用 ")])),_:1,__:[8]}),t(o,{color:"warning",class:"ml-2",loading:""},{default:s(()=>l[9]||(l[9]=[d(" loading ")])),_:1,__:[9]}),t(o,{color:"warning",type:"link",class:"ml-2"},{default:s(()=>l[10]||(l[10]=[d(" link ")])),_:1,__:[10]}),t(o,{color:"warning",type:"link",class:"ml-2",loading:""},{default:s(()=>l[11]||(l[11]=[d(" loading link ")])),_:1,__:[11]}),t(o,{color:"warning",type:"link",class:"ml-2",disabled:""},{default:s(()=>l[12]||(l[12]=[d(" disabled link ")])),_:1,__:[12]})]),n("div",C,[l[20]||(l[20]=n("h3",null,"error",-1)),t(o,{color:"error"},{default:s(()=>l[14]||(l[14]=[d(" 错误 ")])),_:1,__:[14]}),t(o,{color:"error",class:"ml-2",disabled:""},{default:s(()=>l[15]||(l[15]=[d(" 禁用 ")])),_:1,__:[15]}),t(o,{color:"error",class:"ml-2",loading:""},{default:s(()=>l[16]||(l[16]=[d(" loading ")])),_:1,__:[16]}),t(o,{color:"error",type:"link",class:"ml-2"},{default:s(()=>l[17]||(l[17]=[d(" link ")])),_:1,__:[17]}),t(o,{color:"error",type:"link",class:"ml-2",loading:""},{default:s(()=>l[18]||(l[18]=[d(" loading link ")])),_:1,__:[18]}),t(o,{color:"error",type:"link",class:"ml-2",disabled:""},{default:s(()=>l[19]||(l[19]=[d(" disabled link ")])),_:1,__:[19]})]),n("div",P,[l[26]||(l[26]=n("h3",null,"ghost",-1)),t(o,{ghost:"",color:"success",class:"ml-2"},{default:s(()=>l[21]||(l[21]=[d(" 幽灵成功 ")])),_:1,__:[21]}),t(o,{ghost:"",color:"warning",class:"ml-2"},{default:s(()=>l[22]||(l[22]=[d(" 幽灵警告 ")])),_:1,__:[22]}),t(o,{ghost:"",color:"error",class:"ml-2"},{default:s(()=>l[23]||(l[23]=[d(" 幽灵错误 ")])),_:1,__:[23]}),t(o,{ghost:"",type:"dashed",color:"warning",class:"ml-2"},{default:s(()=>l[24]||(l[24]=[d(" 幽灵警告dashed ")])),_:1,__:[24]}),t(o,{ghost:"",danger:"",class:"ml-2"},{default:s(()=>l[25]||(l[25]=[d(" 幽灵危险 ")])),_:1,__:[25]})])]),_:1})]),_:1}),t(r,{xl:14,lg:24},{default:s(()=>[t(a,{title:"BasicButton Types"},{default:s(()=>[n("div",$,[l[34]||(l[34]=n("h3",null,"primary",-1)),t(o,{type:"primary",preIcon:"mdi:page-next-outline"},{default:s(()=>l[27]||(l[27]=[d(" 主按钮 ")])),_:1,__:[27]}),t(o,{type:"primary",class:"ml-2",disabled:""},{default:s(()=>l[28]||(l[28]=[d(" 禁用 ")])),_:1,__:[28]}),t(o,{type:"primary",class:"ml-2",danger:"",preIcon:"mdi:page-next-outline"},{default:s(()=>l[29]||(l[29]=[d(" 危险 ")])),_:1,__:[29]}),t(o,{type:"primary",class:"ml-2",loading:""},{default:s(()=>l[30]||(l[30]=[d(" loading ")])),_:1,__:[30]}),t(o,{type:"link",class:"ml-2"},{default:s(()=>l[31]||(l[31]=[d(" link ")])),_:1,__:[31]}),t(o,{type:"link",class:"ml-2",loading:""},{default:s(()=>l[32]||(l[32]=[d(" loading link ")])),_:1,__:[32]}),t(o,{type:"link",class:"ml-2",disabled:""},{default:s(()=>l[33]||(l[33]=[d(" disabled link ")])),_:1,__:[33]})]),n("div",A,[l[42]||(l[42]=n("h3",null,"default",-1)),t(o,{type:"default"},{default:s(()=>l[35]||(l[35]=[d(" 默认 ")])),_:1,__:[35]}),t(o,{type:"default",class:"ml-2",disabled:""},{default:s(()=>l[36]||(l[36]=[d(" 禁用 ")])),_:1,__:[36]}),t(o,{type:"default",class:"ml-2",danger:""},{default:s(()=>l[37]||(l[37]=[d(" 危险 ")])),_:1,__:[37]}),t(o,{type:"default",class:"ml-2",loading:""},{default:s(()=>l[38]||(l[38]=[d(" loading ")])),_:1,__:[38]}),t(o,{type:"link",class:"ml-2"},{default:s(()=>l[39]||(l[39]=[d(" link ")])),_:1,__:[39]}),t(o,{type:"link",class:"ml-2",loading:""},{default:s(()=>l[40]||(l[40]=[d(" loading link ")])),_:1,__:[40]}),t(o,{type:"link",class:"ml-2",disabled:""},{default:s(()=>l[41]||(l[41]=[d(" disabled link ")])),_:1,__:[41]})]),n("div",N,[l[47]||(l[47]=n("h3",null,"dashed",-1)),t(o,{type:"dashed"},{default:s(()=>l[43]||(l[43]=[d(" dashed ")])),_:1,__:[43]}),t(o,{type:"dashed",class:"ml-2",disabled:""},{default:s(()=>l[44]||(l[44]=[d(" 禁用 ")])),_:1,__:[44]}),t(o,{type:"dashed",class:"ml-2",danger:""},{default:s(()=>l[45]||(l[45]=[d(" 危险 ")])),_:1,__:[45]}),t(o,{type:"dashed",class:"ml-2",loading:""},{default:s(()=>l[46]||(l[46]=[d(" loading ")])),_:1,__:[46]})]),n("div",V,[l[53]||(l[53]=n("h3",null,"ghost 常规幽灵按钮通常用于有色背景下",-1)),n("div",W,[t(o,{ghost:"",type:"primary",class:"ml-2"},{default:s(()=>l[48]||(l[48]=[d(" 幽灵主要 ")])),_:1,__:[48]}),t(o,{ghost:"",type:"default",class:"ml-2"},{default:s(()=>l[49]||(l[49]=[d(" 幽灵默认 ")])),_:1,__:[49]}),t(o,{ghost:"",type:"dashed",class:"ml-2"},{default:s(()=>l[50]||(l[50]=[d(" 幽灵dashed ")])),_:1,__:[50]}),t(o,{ghost:"",type:"primary",class:"ml-2",disabled:""},{default:s(()=>l[51]||(l[51]=[d(" 禁用 ")])),_:1,__:[51]}),t(o,{ghost:"",type:"primary",class:"ml-2",loading:""},{default:s(()=>l[52]||(l[52]=[d(" loading ")])),_:1,__:[52]})])])]),_:1})]),_:1})]),_:1})]),_:1})}const Q=b(w,[["render",I]]);export{Q as default};
