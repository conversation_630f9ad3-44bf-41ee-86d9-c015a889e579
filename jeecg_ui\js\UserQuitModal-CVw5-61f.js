var j=Object.defineProperty,D=Object.defineProperties;var L=Object.getOwnPropertyDescriptors;var b=Object.getOwnPropertySymbols;var q=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var k=(e,o,t)=>o in e?j(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,x=(e,o)=>{for(var t in o||(o={}))q.call(o,t)&&k(e,t,o[t]);if(b)for(var t of b(o))z.call(o,t)&&k(e,t,o[t]);return e},y=(e,o)=>D(e,L(o));var R=(e,o,t)=>new Promise((f,u)=>{var _=r=>{try{p(t.next(r))}catch(l){u(l)}},g=r=>{try{p(t.throw(r))}catch(l){u(l)}},p=r=>r.done?f(r.value):Promise.resolve(r.value).then(_,g);p((t=t.apply(e,o)).next())});import{d as B,f as A,ag as c,aB as h,ar as v,aD as n,k as s,u as m,ah as E,G as M,aE as F,J as G}from"./vue-vendor-dy9k-Yad.js";import{B as H}from"./index-Diw57m_E.js";import"./index-BkGZ5fiW.js";import{r as J}from"./user.data-CLRTqTDz.js";import{i as U,p as $}from"./user.api-mLAlJze4.js";import{ac as W,u as X,a as Y}from"./index-CCWaWN5g.js";import{useListPage as Z}from"./useListPage-Soxgnx9a.js";import{M as oo}from"./antd-vue-vendor-me9YkNVC.js";import to from"./BasicTable-xCEZpGLb.js";import{Q as eo}from"./componentMap-Bkie1n3v.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./validator-B_KkcUnu.js";import"./renderUtils-D7XVOFwj.js";import"./index-QxsVJqiT.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const ro=B({name:"user-quit-modal"}),io=B(y(x({},ro),{emits:["success","register"],setup(e,{emit:o}){const{createConfirm:t}=X(),f=o,u=A([]),[_]=W(()=>{u.value=[]}),{prefixCls:g,tableContext:p}=Z({tableProps:{api:U,columns:J,rowKey:"id",canResize:!1,useSearchForm:!1,actionColumn:{width:120}}}),[r,{reload:l},{rowSelection:T,selectedRowKeys:w,selectedRows:I}]=p;function C(i){return R(this,null,function*(){yield $({userIds:i.id,usernames:i.username},l),f("success")})}function Q(){oo.confirm({title:"取消离职",content:"取消离职交接人也会清空",okText:"确认",cancelText:"取消",onOk:()=>{let i=I.value,a=[];for(const d of i)a.push(d.username);C({id:G(m(w)).join(","),username:a.join(",")})}})}function K(i){return[{label:"取消离职",icon:"ant-design:redo-outlined",popConfirm:{title:"是否取消离职,取消离职交接人也会清空",confirm:C.bind(null,i)}}]}return(i,a)=>{const d=c("Icon"),S=c("a-menu-item"),V=c("a-menu"),N=c("a-button"),O=c("a-dropdown");return v(),h(m(H),F(i.$attrs,{onRegister:m(_),title:"离职人员信息",showOkBtn:!1,width:"1000px",destroyOnClose:""}),{default:n(()=>[s(m(to),{onRegister:m(r),rowSelection:m(T)},{tableTitle:n(()=>[m(w).length>0?(v(),h(O,{key:0},{overlay:n(()=>[s(V,null,{default:n(()=>[s(S,{key:"1",onClick:Q},{default:n(()=>[s(d,{icon:"ant-design:redo-outlined"}),a[0]||(a[0]=M(" 批量取消 "))]),_:1,__:[0]})]),_:1})]),default:n(()=>[s(N,null,{default:n(()=>[a[1]||(a[1]=M("批量操作 ")),s(d,{icon:"ant-design:down-outlined"})]),_:1,__:[1]})]),_:1})):E("",!0)]),action:n(({record:P})=>[s(m(eo),{actions:K(P)},null,8,["actions"])]),_:1},8,["onRegister","rowSelection"])]),_:1},16,["onRegister"])}}})),_t=Y(io,[["__scopeId","data-v-7bfaefd9"]]);export{_t as default};
