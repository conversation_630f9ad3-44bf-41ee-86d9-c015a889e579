var _=(e,t,p)=>new Promise((s,a)=>{var r=o=>{try{i(p.next(o))}catch(n){a(n)}},m=o=>{try{i(p.throw(o))}catch(n){a(n)}},i=o=>o.done?s(o.value):Promise.resolve(o.value).then(r,m);i((p=p.apply(e,t)).next())});import{d as $,f as P,ag as u,aB as c,ar as f,aD as l,k as C,ah as b,G as B}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{C as k}from"./index-LCGLvkB3.js";import{j as v}from"./antd-vue-vendor-me9YkNVC.js";import{P as g}from"./index-CtJ0w2CP.js";import{ar as F,a as I}from"./index-CCWaWN5g.js";import{B as h}from"./BasicForm-DBcXiHk0.js";import{u as y}from"./useForm-CgkFTrrO.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./useContentHeight-bZ7VSBAL.js";import"./useContentViewHeight-Md7r1NIg.js";import"./usePageContext-CxiNGbPs.js";import"./injectionKey-DPVn4AgL.js";const S=$({components:{BasicForm:h,CollapseContainer:k,PageWrapper:g,[v.name]:v,Button:F},setup(){const[e,{appendSchemaByField:t,removeSchemaByFiled:p,validate:s}]=y({schemas:[{field:"field0a",component:"Input",label:"字段0",colProps:{span:8},required:!0},{field:"field0b",component:"Input",label:"字段0",colProps:{span:8},required:!0},{field:"0",component:"Input",label:" ",colProps:{span:8},slot:"add"}],labelWidth:100,actionColOptions:{span:24}});function a(){return _(this,null,function*(){try{const o=yield s()}catch(o){}})}const r=P(1);function m(){t({field:`field${r.value}a`,component:"Input",label:"字段"+r.value,colProps:{span:8},required:!0},""),t({field:`field${r.value}b`,component:"Input",label:"字段"+r.value,colProps:{span:8},required:!0},""),t({field:`${r.value}`,component:"Input",label:" ",colProps:{span:8},slot:"add"},""),r.value++}function i(o){p([`field${o}a`,`field${o}b`,`${o}`]),r.value--}return{register:e,handleSubmit:a,add:m,del:i}}});function q(e,t,p,s,a,r){const m=u("Button"),i=u("BasicForm"),o=u("CollapseContainer"),n=u("PageWrapper");return f(),c(n,{title:"表单增删示例"},{default:l(()=>[C(o,{title:"表单增删"},{default:l(()=>[C(i,{onRegister:e.register,onSubmit:e.handleSubmit},{add:l(({field:d})=>[Number(d)===0?(f(),c(m,{key:0,onClick:e.add},{default:l(()=>t[0]||(t[0]=[B("+")])),_:1,__:[0]},8,["onClick"])):b("",!0),d>0?(f(),c(m,{key:1,onClick:N=>e.del(d)},{default:l(()=>t[1]||(t[1]=[B("-")])),_:2,__:[1]},1032,["onClick"])):b("",!0)]),_:1},8,["onRegister","onSubmit"])]),_:1})]),_:1})}const Do=I(S,[["render",q]]);export{Do as default};
