var h=Object.defineProperty,f=Object.defineProperties;var C=Object.getOwnPropertyDescriptors;var p=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,v=Object.prototype.propertyIsEnumerable;var i=(e,a,s)=>a in e?h(e,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[a]=s,u=(e,a)=>{for(var s in a||(a={}))g.call(a,s)&&i(e,s,a[s]);if(p)for(var s of p(a))v.call(a,s)&&i(e,s,a[s]);return e},m=(e,a)=>f(e,C(a));import{d as _,e as d,ag as P,aB as V,ar as x,aE as k}from"./vue-vendor-dy9k-Yad.js";import{c as J,a as $}from"./areaDataUtil-BXVjRArW.js";import{cj as w,ck as B,cl as E,a as b}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const j=_({name:"JVxePcaCell",props:w(),setup(e){const{innerValue:a,cellProps:s,handleChangeCommon:c}=E(e),n=d(()=>{let t=a.value;return t?J(t,3):[]}),l=d(()=>m(u({},s.value),{options:$,showOverflow:!1,value:n.value}));function o(t){let r="";t&&t.length==3&&(r=t[2]),c(r)}return{handleChange:o,selectedValue:n,getProps:l}},enhanced:{switches:{visible:!0},translate:{enabled:!1},aopEvents:{editActived({$event:e}){B({$event:e,props:this.props,className:".ant-select .ant-select-selection-search-input",isClick:!0})}}}});function A(e,a,s,c,n,l){const o=P("a-cascader");return x(),V(o,k(e.getProps,{class:"pca-select",onChange:e.handleChange}),null,16,["onChange"])}const z=b(j,[["render",A]]);export{z as default};
