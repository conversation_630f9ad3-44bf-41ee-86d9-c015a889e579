var l=(n,a,t)=>new Promise((r,i)=>{var p=o=>{try{e(t.next(o))}catch(s){i(s)}},m=o=>{try{e(t.throw(o))}catch(s){i(s)}},e=o=>o.done?r(o.value):Promise.resolve(o.value).then(p,m);e((t=t.apply(n,a)).next())});import{f as c,ag as d,aB as f,ar as u,aD as M,k as _}from"./vue-vendor-dy9k-Yad.js";import{I as y}from"./BasicModal-BLFvpBuk.js";import"./index-Diw57m_E.js";import{B as h}from"./BasicForm-DBcXiHk0.js";import{a as g}from"./index-mbACBRQ9.js";import k from"./AiModelSeniorForm-DwUUzhZR.js";import{ac as C,a as B}from"./index-CCWaWN5g.js";import"./ModalHeader-BJG9dHtK.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";const O={name:"AiAppParamsSettingModal",components:{MarkdownViewer:g,BasicForm:h,BasicModal:y,AiModelSeniorForm:k},emits:["ok","register"],setup(n,{emit:a}){let t=c();const r=c(""),[i,{closeModal:p}]=C(o=>l(null,null,function*(){r.value=o.type,o.type==="model"?o.metadata.hasOwnProperty("temperature")||(o.metadata.temperature=.7):(o.metadata.hasOwnProperty("topNumber")||(o.metadata.topNumber=4),o.metadata.hasOwnProperty("similarity")||(o.metadata.similarity=.76)),setTimeout(()=>{t.value.setModalParams(o.metadata)})}));function m(){let o=t.value.emitChange();a("ok",o),e()}function e(){p()}return{registerModal:i,handleOk:m,handleCancel:e,type:r,aiModelSeniorFormRef:t}}};function w(n,a,t,r,i,p){const m=d("AiModelSeniorForm"),e=d("BasicModal");return u(),f(e,{title:"参数设置",destroyOnClose:"",onRegister:r.registerModal,canFullscreen:!1,width:"560px",onOk:r.handleOk,onCancel:r.handleCancel},{default:M(()=>[_(m,{ref:"aiModelSeniorFormRef",type:r.type},null,8,["type"])]),_:1},8,["onRegister","onOk","onCancel"])}const ko=B(O,[["render",w],["__scopeId","data-v-ba06ef4d"]]);export{ko as default};
