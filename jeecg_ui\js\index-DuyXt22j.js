import{d as n,f as a,r as s,o as i,aq as c,ar as p,at as m}from"./vue-vendor-dy9k-Yad.js";import{C as d}from"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";import{a as f}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const u=n({components:{},setup(){const t=window.CodeMirror||d,e=a(null),o=s({tabSize:2,theme:"cobalt",lineNumbers:!0,line:!0});i(()=>{r()});function r(){t.fromTextArea(e.value,o)}return{textarea:e}}}),l={ref:"textarea"};function _(t,e,o,r,x,C){return p(),c("div",null,[m("textarea",l,`白日依山尽，黄河入海流。
欲穷千里目，更上一层楼。
        `,512)])}const k=f(u,[["render",_]]);export{k as default};
