var Z=Object.defineProperty,tt=Object.defineProperties;var ot=Object.getOwnPropertyDescriptors;var x=Object.getOwnPropertySymbols;var et=Object.prototype.hasOwnProperty,nt=Object.prototype.propertyIsEnumerable;var D=(i,o,e)=>o in i?Z(i,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[o]=e,v=(i,o)=>{for(var e in o||(o={}))et.call(o,e)&&D(i,e,o[e]);if(x)for(var e of x(o))nt.call(o,e)&&D(i,e,o[e]);return i},B=(i,o)=>tt(i,ot(o));var d=(i,o,e)=>new Promise((_,f)=>{var g=s=>{try{p(e.next(s))}catch(c){f(c)}},w=s=>{try{p(e.throw(s))}catch(c){f(c)}},p=s=>s.done?_(s.value):Promise.resolve(s.value).then(g,w);p((e=e.apply(i,o)).next())});import{d as U,e as it,ag as u,aq as rt,ar as R,k as r,aD as l,u as a,aB as at,ah as st,G as y,at as M,as as lt,J as pt}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{useListPage as mt}from"./useListPage-Soxgnx9a.js";import ut from"./TemplateModal-9IG_6Lrb.js";import ct from"./TemplateTestModal-D82Hfsu4.js";import{s as A,A as I,c as dt,d as ft,l as _t,e as gt}from"./template.api-Bv33NMH9.js";import{ad as T,u as wt}from"./index-CCWaWN5g.js";import{Q as Ct}from"./componentMap-Bkie1n3v.js";import bt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";import"./validator-B_KkcUnu.js";import"./user.api-mLAlJze4.js";const yt=U({name:"message-template"}),To=U(B(v({},yt),{setup(i){const{createMessage:o}=wt(),{prefixCls:e,onExportXls:_,onImportXls:f,tableContext:g}=mt({designScope:"message-template",tableProps:{title:"消息中心模板列表数据",api:_t,columns:ft,formConfig:{schemas:dt}},exportConfig:{url:I.exportXls,name:"消息中心模板列表"},importConfig:{url:I.importXls,success:()=>p()}}),[w,{reload:p,setLoading:s},{rowSelection:c,selectedRowKeys:C,selectedRows:N}]=g,[F,{openModal:b}]=T(),[V,X]=T(),j=it(()=>C.value.length>0);function E(){b(!0,{title:"新增消息模板",isUpdate:!1,showFooter:!0,record:{}})}function L(t){if(t.useStatus==="1"){o.warning("此模板已被应用，禁止编辑!");return}b(!0,{title:"修改消息模板",isUpdate:!0,record:t,showFooter:!0})}function K(t){if(t){if(t.useStatus=="1"){o.warning("该模板已被应用禁止删除!");return}k([t.id],!1)}}function k(t,n=!0){return d(this,null,function*(){const m=a(t);if(m.length>0)try{s(!0),yield gt({ids:m.join(",")},n),yield p()}finally{s(!1)}})}function P(){return d(this,null,function*(){try{if(pt(N.value).filter(m=>m.useStatus=="1").length>0){o.warning("选中的模板已被应用禁止删除!");return}yield k(C),C.value=[]}finally{}})}function $(t){X.openModal(!0,{record:t})}function q(t){return[{label:"查看",onClick:O.bind(null,t)},{label:"编辑",onClick:L.bind(null,t)}]}function z(t){return[{label:"应用",onClick:G.bind(null,t)},{label:"停用",onClick:J.bind(null,t)},{label:"发送测试",onClick:$.bind(null,t)},{label:"删除",color:"error",popConfirm:{title:"确认要删除吗？",confirm:K.bind(null,t)}}]}function G(t){return d(this,null,function*(){let n={id:t.id,useStatus:"1"};yield A(n,!0),yield p()})}function J(t){return d(this,null,function*(){let n={id:t.id,useStatus:"0"};yield A(n,!0),yield p()})}function O(t){b(!0,{title:"消息模板详情",isUpdate:!0,showFooter:!1,record:t})}return(t,n)=>{const m=u("a-button"),Q=u("j-upload-button"),S=u("Icon"),H=u("a-menu-item"),W=u("a-menu"),Y=u("a-dropdown");return R(),rt("div",{class:lt(a(e))},[r(a(bt),{onRegister:a(w),rowSelection:a(c)},{tableTitle:l(()=>[r(m,{type:"primary",preIcon:"ant-design:plus-outlined",onClick:E},{default:l(()=>n[0]||(n[0]=[y("新增")])),_:1,__:[0]}),r(m,{type:"primary",preIcon:"ant-design:export-outlined",onClick:a(_)},{default:l(()=>n[1]||(n[1]=[y(" 导出")])),_:1,__:[1]},8,["onClick"]),r(Q,{type:"primary",preIcon:"ant-design:import-outlined",onClick:a(f)},{default:l(()=>n[2]||(n[2]=[y("导入")])),_:1,__:[2]},8,["onClick"]),j.value?(R(),at(Y,{key:0},{overlay:l(()=>[r(W,null,{default:l(()=>[r(H,{key:"1",onClick:P},{default:l(()=>[r(S,{icon:"ant-design:delete-outlined"}),n[3]||(n[3]=M("span",null,"删除",-1))]),_:1,__:[3]})]),_:1})]),default:l(()=>[r(m,null,{default:l(()=>[n[4]||(n[4]=M("span",null,"批量操作",-1)),r(S,{icon:"mdi:chevron-down"})]),_:1,__:[4]})]),_:1})):st("",!0)]),action:l(({record:h})=>[r(a(Ct),{actions:q(h),dropDownActions:z(h)},null,8,["actions","dropDownActions"])]),_:1},8,["onRegister","rowSelection"]),r(ut,{onRegister:a(F),onSuccess:a(p)},null,8,["onRegister","onSuccess"]),r(ct,{onRegister:a(V)},null,8,["onRegister"])],2)}}}));export{To as default};
