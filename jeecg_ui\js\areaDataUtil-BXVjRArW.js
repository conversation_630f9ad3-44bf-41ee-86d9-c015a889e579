import{aY as c}from"./index-CCWaWN5g.js";import{h as g}from"./antd-vue-vendor-me9YkNVC.js";const d={},p={},s=c[86],a=[];let x=[];d[""]="全部";for(const e in s)a.push({value:e,label:s[e]}),d[e]=s[e],p[s[e]]={code:e},p[s[e]].全部={code:""};for(let e=0,l=a.length;e<l;e++){const t=a[e].value,n=a[e].label,i=[];for(const o in c[t])i.push({value:o,label:c[t][o]}),d[o]=c[t][o],p[n][c[t][o]]={code:o},p[n][c[t][o]].全部={code:""};i.length&&(a[e].children=i)}x=g(a);for(let e=0,l=a.length;e<l;e++){const t=a[e].children,n=a[e].label;if(t)for(let i=0,o=t.length;i<o;i++){const h=t[i].value,j=t[i].label,u=[];for(const r in c[h])u.push({value:r,label:c[h][r]}),d[r]=c[h][r],p[n][j][c[h][r]]={code:r};u.length&&(t[i].children=u)}}const b=g(x);b.unshift({value:"",label:"全部"});for(let e=0,l=b.length;e<l;e++){const t=b[e].children;if(t&&t.length){t.unshift({value:"",label:"全部"});for(let n=0,i=t.length;n<i;n++){const o=t[n].children;o&&o.length&&o.unshift({value:"",label:"全部"})}}}const v=g(a);v.unshift({value:"",label:"全部"});for(let e=0,l=v.length;e<l;e++){const t=v[e].children;if(t&&t.length){t.unshift({value:"",label:"全部"});for(let n=0,i=t.length;n<i;n++){const o=t[n].children;o&&o.length&&o.unshift({value:"",label:"全部"})}}}const y=[];for(const e in s)y.push({value:e,label:s[e]});function T(e){let l=[];for(const t in c[e])l.push({value:t,label:c[e][t]});return l}const f=[];Object.keys(s).map(e=>{f.push({id:e,text:s[e],pid:"86",index:1});const l=c[e];Object.keys(l).map(t=>{f.push({id:t,text:l[t],pid:e,index:2});const n=c[t];n&&Object.keys(n).map(i=>{f.push({id:i,text:n[i],pid:t,index:3})})})});function D(e,l){let t=[];return C(e,t,l),t}function C(e,l,t){for(let n of f)n.id===e&&n.index==t&&(l.unshift(e),n.pid!="86"&&C(n.pid,l,--t))}export{a,b,D as c,y as d,T as g,x as p,v as r};
