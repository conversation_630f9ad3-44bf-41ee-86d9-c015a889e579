import{d as F,f as O,e as d,n as R,ag as j,aq as h,ar as u,aA as I,as as N,ah as U,u as V,F as $,aB as B,aJ as v,aE as C,aD as k,k as z,G as T}from"./vue-vendor-dy9k-Yad.js";import{cP as A}from"./index-CCWaWN5g.js";import{S as H}from"./shareStore-7de6c7a6-BGigCHd8.js";import J from"./OnlineAutoModal-95f46901-Co1FfZLa.js";import{d as W,m as q}from"./useListButton-98908683-Bu4g4Tt-.js";import{a as G,u as Q}from"./useExtendComponent-bb98e568-B7LlULaY.js";import Z from"./OnlineDetailModal-5b412bb9-C14CjZpZ.js";var w=(r,f,t)=>new Promise((m,l)=>{var s=a=>{try{n(t.next(a))}catch(c){l(c)}},g=a=>{try{n(t.throw(a))}catch(c){l(c)}},n=a=>a.done?m(a.value):Promise.resolve(a.value).then(s,g);n((t=t.apply(r,f)).next())});const ne=F({__name:"SingleView",props:{ID:String,formName:String,buttonSwitch:Object,cgBIBtnMap:Object,confirmBtnCfg:Object,isUpdate:Boolean,isDetail:Boolean,isMobile:Boolean},emits:["ok","goEdit"],setup(r,{emit:f}){const t=r,m=f,l=H(),s=O(),g=d(()=>{let o=20,e=100;return window.innerWidth<1200&&(o=0,e=0),{padding:`${o}px ${e}px`}}),n=d(()=>{const o=t.isDetail?"详情":t.isUpdate?"修改":"新增",e={id:t.ID,title:o+` [${t.formName}]`,onRegister:M};return t.isDetail||Object.assign(e,{cgBIBtnMap:t.cgBIBtnMap,buttonSwitch:t.buttonSwitch,confirmBtnCfg:t.confirmBtnCfg,onSuccess:P,onFormConfig:y}),Object.assign(e,{cancelBtnCfg:{enabled:!1},width:1200,getContainer:()=>s.value,mask:!1,maskClosable:!1,bodyStyle:{height:a.value},draggable:!1,canFullscreen:!1,closeFunc:E}),e}),a=d(()=>{var o,e;let i=140;return t.isMobile&&(i=110),((e=(o=s.value)==null?void 0:o.offsetHeight)!=null?e:window.innerHeight)-i+"px"}),{loading:c,handleFormConfig:y,onlineTableContext:p,onlineExtConfigJson:b}=W(),{registerModal:x,handleAdd:S,handleEdit:D}=q(p,b);function M(o,e){return w(this,null,function*(){if(x(o,e),yield R(),t.isUpdate){const i=yield _();D(i)}else S({})})}function _(){return w(this,null,function*(){return yield A(d(()=>l.getDataRecord))})}G(p,b),Q(p);function P(o){let e;t.isUpdate?e=l.getDataRecord.id:e=o.flow_submit_id,e&&m("ok",e)}function E(){return alert("不允许关闭"),!1}return(o,e)=>{const i=j("a-button");return u(),h("div",{ref_key:"boxRef",ref:s,class:N(["view-box",{"is-mobile":r.isMobile}]),style:I(g.value)},[V(c)?U("",!0):(u(),h($,{key:0},[r.isDetail?(u(),B(Z,v(C({key:0},n.value)),{footerBtn:k(()=>[z(i,{onClick:e[0]||(e[0]=K=>m("goEdit"))},{default:k(()=>e[1]||(e[1]=[T("编辑该数据")])),_:1})]),_:1},16)):(u(),B(J,v(C({key:1},n.value)),null,16))],64))],6)}}});export{ne as a};
