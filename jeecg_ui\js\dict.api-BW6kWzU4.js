import{j as s}from"./index-CCWaWN5g.js";import{M as l}from"./antd-vue-vendor-me9YkNVC.js";const r="/sys/dict/exportXls",a="/sys/dict/importExcel",n=e=>s.get({url:"/sys/dict/list",params:e}),y=(e,t)=>s.delete({url:"/sys/dict/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),o=(e,t)=>{l.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>s.delete({url:"/sys/dict/deleteBatch",data:e},{joinParamsToUrl:!0}).then(()=>{t()})})},u=(e,t)=>{let c=t?"/sys/dict/edit":"/sys/dict/add";return s.post({url:c,params:e})};const m=e=>s.get({url:"/sys/dict/deleteList",params:e}),h=(e,t)=>s.put({url:"/sys/dict/putRecycleBin",params:e}).then(()=>{t()}),I=(e,t)=>s.put({url:`/sys/dict/back/${e}`}).then(()=>{t()}),f=(e,t)=>s.delete({url:`/sys/dict/deleteRecycleBin?ids=${e.ids}`}).then(()=>{t()}),B=(e,t)=>s.delete({url:`/sys/dict/deletePhysic/${e}`}).then(()=>{t()}),R=e=>s.get({url:"/sys/dictItem/list",params:e}),k=(e,t)=>s.delete({url:"/sys/dictItem/delete",params:e},{joinParamsToUrl:!0}).then(()=>{t()}),p=(e,t)=>{let c=t?"/sys/dictItem/edit":"/sys/dictItem/add";return s.post({url:c,params:e})},D=e=>s.get({url:"/sys/dictItem/dictItemCheck",params:e},{isTransformResponse:!1}),g=()=>s.get({url:"/sys/dict/refleshCache"},{isTransformResponse:!1}),x=()=>s.get({url:"/sys/dict/queryAllDictItems"},{isTransformResponse:!1});export{D as a,u as b,B as c,k as d,h as e,f,m as g,o as h,R as i,a as j,r as k,n as l,y as m,I as p,x as q,g as r,p as s};
