var b=(_,g,l)=>new Promise((h,d)=>{var i=r=>{try{c(l.next(r))}catch(u){d(u)}},o=r=>{try{c(l.throw(r))}catch(u){d(u)}},c=r=>r.done?h(r.value):Promise.resolve(r.value).then(i,o);c((l=l.apply(_,g)).next())});import{d as V,f as I,e as q,ag as p,aq as w,ar as x,k as a,aD as v,at as n}from"./vue-vendor-dy9k-Yad.js";import{i as Y}from"./antd-vue-vendor-me9YkNVC.js";import{a as k}from"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";const N={class:"step-panel"},z={class:"form-section"},H={class:"form-row"},M={class:"form-section"},P={class:"form-row"},B=V({__name:"Step2",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{expose:g,emit:l}){const h=_,d=l,i=I(),o=q({get:()=>h.modelValue,set:e=>d("update:modelValue",e)}),c={validity:{validDate:[{required:!0,message:"请选择有效期时间",trigger:"change"}]},contactInfo:{contactName:[{required:!0,message:"请输入联系姓名",trigger:"blur"},{min:2,max:20,message:"联系姓名长度应在2-20个字符之间",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]}},r=e=>e&&e<Y().startOf("day");return g({validateForm:()=>b(null,null,function*(){var e;try{return yield(e=i.value)==null?void 0:e.validate(),!0}catch(t){const f=document.querySelector(".ant-form-item-has-error");return f&&f.scrollIntoView({behavior:"smooth",block:"center"}),!1}}),clearValidate:()=>{var e;(e=i.value)==null||e.clearValidate()}}),(e,t)=>{const f=p("a-date-picker"),s=p("a-form-item"),y=p("a-input"),D=p("a-form");return x(),w("div",N,[a(D,{ref_key:"formRef",ref:i,layout:"horizontal",model:o.value,rules:c,"label-col":{span:4,style:{textAlign:"left"}},"wrapper-col":{span:20},"scroll-to-first-error":!0},{default:v(()=>[n("div",z,[t[3]||(t[3]=n("h3",{class:"section-title"},"信息有效性",-1)),n("div",H,[a(s,{label:"时间信息",name:["validity","validDate"],required:"",class:"form-item-third"},{default:v(()=>[a(f,{value:o.value.validity.validDate,"onUpdate:value":t[0]||(t[0]=m=>o.value.validity.validDate=m),placeholder:"请选择有效期时间",style:{width:"100%"},size:"large","show-time":"",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss","disabled-date":r},null,8,["value"])]),_:1}),a(s,{class:"form-item-third"}),a(s,{class:"form-item-third"})])]),n("div",M,[t[4]||(t[4]=n("h3",{class:"section-title"},"联系人信息",-1)),n("div",P,[a(s,{label:"联系姓名",name:["contactInfo","contactName"],required:"",class:"form-item-third"},{default:v(()=>[a(y,{value:o.value.contactInfo.contactName,"onUpdate:value":t[1]||(t[1]=m=>o.value.contactInfo.contactName=m),placeholder:"请输入联系姓名",size:"large"},null,8,["value"])]),_:1}),a(s,{label:"联系电话",name:["contactInfo","contactPhone"],required:"",class:"form-item-third"},{default:v(()=>[a(y,{value:o.value.contactInfo.contactPhone,"onUpdate:value":t[2]||(t[2]=m=>o.value.contactInfo.contactPhone=m),placeholder:"请输入联系电话",maxlength:"11",size:"large"},null,8,["value"])]),_:1}),a(s,{class:"form-item-third"})])])]),_:1},8,["model"])])}}}),j=k(B,[["__scopeId","data-v-02cc1c70"]]);export{j as default};
