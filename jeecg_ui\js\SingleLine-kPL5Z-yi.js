import{d,e as l,ag as y,aq as f,ar as p,G as g,at as c,au as u,as as _,k as x,f as b,r as v,h as S,aA as $}from"./vue-vendor-dy9k-Yad.js";import{a as h}from"./index-CCWaWN5g.js";import{useECharts as C}from"./useECharts-BU6FzBZi.js";import{h as w}from"./antd-vue-vendor-me9YkNVC.js";const D=d({name:"Trend",props:{term:{type:String,default:"",required:!0},percentage:{type:Number,default:null},type:{type:Boolean,default:null},target:{type:Number,default:0},value:{type:Number,default:0},fixed:{type:Number,default:2}},setup(e){const a=l(()=>(e.type===null?e.value>=e.target:e.type)?"up":"down"),r=l(()=>(e.percentage===null?Math.abs(e.value-e.target)*100/e.target:e.percentage).toFixed(e.fixed));return{trend:a,rate:r}}}),N={class:"chart-trend"};function A(e,a,r,n,t,s){const o=y("Icon");return p(),f("div",N,[g(u(e.term)+" ",1),c("span",null,u(e.rate)+"%",1),c("span",{class:_(["trend-icon",e.trend])},[x(o,{icon:"ant-design:caret-"+e.trend+"-outlined"},null,8,["icon"])],2)])}const V=h(D,[["render",A],["__scopeId","data-v-620ec0b8"]]),k=d({name:"single-line",props:{chartData:{type:Array,default:()=>[]},option:{type:Object,default:()=>({})},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"},seriesColor:{type:String,default:"#1890ff"}},setup(e){const a=b(null),{setOptions:r,echarts:n}=C(a),t=v({tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0,backgroundColor:"#333"}}},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{type:"line",showSymbol:!1,smooth:!0,areaStyle:{},data:[],color:e.seriesColor}]});S(()=>{e.chartData&&s()});function s(){e.option&&Object.assign(t,w(e.option));let o=e.chartData.map(i=>i.value),m=e.chartData.map(i=>i.name);t.series[0].data=o,t.series[0].color=e.seriesColor,t.xAxis.data=m,r(t)}return{chartRef:a}}});function B(e,a,r,n,t,s){return p(),f("div",{ref:"chartRef",style:$({height:e.height,width:e.width})},null,4)}const j=h(k,[["render",B]]);export{j as S,V as T};
