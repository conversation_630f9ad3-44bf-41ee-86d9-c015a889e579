import P from"./LeftRole-b0e0b496-Qa0mbQLH.js";import F from"./LeftDepart-52cb6743-Cn71KUoa.js";import U from"./LeftUser-dd4b10e2-CznvoKNw.js";import j from"./AuthFieldTree-5cc0da05-BA2QWlHM.js";import x from"./AuthButtonTree-b0bd6c40-Dgxz95FG.js";import L from"./AuthDataTree-f14a98d9-BhZVszsI.js";import{d as N,f as u,e as V,n as _,ag as f,aB as M,ar as A,aD as i,k as o,ah as I,aq as E,F as $}from"./vue-vendor-dy9k-Yad.js";import{B as q}from"./index-Diw57m_E.js";import{cs as X,ac as z}from"./index-CCWaWN5g.js";import"./index-BkGZ5fiW.js";import"./useListPage-Soxgnx9a.js";import"./auth.api-53df4c33-CWNFk1-w.js";import"./antd-vue-vendor-me9YkNVC.js";import"./auth.data-626c5083-DVuUJlaU.js";import"./BasicTable-xCEZpGLb.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./componentMap-Bkie1n3v.js";import"./useFormItem-CHvpjy4o.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-CImCetrx.js";import"./index-D6l0IxOU.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useWindowSizeFn-DDbrQbks.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";import"./injectionKey-DPVn4AgL.js";import"./CustomModal-BakuIxQv.js";var G=Object.defineProperty,H=Object.defineProperties,J=Object.getOwnPropertyDescriptors,O=Object.getOwnPropertySymbols,Q=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable,B=(e,t,r)=>t in e?G(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Y=(e,t)=>{for(var r in t||(t={}))Q.call(t,r)&&B(e,r,t[r]);if(O)for(var r of O(t))W.call(t,r)&&B(e,r,t[r]);return e},Z=(e,t)=>H(e,J(t)),D=(e,t,r)=>new Promise((d,l)=>{var a=c=>{try{p(r.next(c))}catch(v){l(v)}},m=c=>{try{p(r.throw(c))}catch(v){l(v)}},p=c=>c.done?d(c.value):Promise.resolve(c.value).then(a,m);p((r=r.apply(e,t)).next())});const ee=N({name:"AuthSetterModal",components:{BasicModal:q,LeftRole:P,LeftDepart:F,LeftUser:U,AuthFieldTree:j,AuthButtonTree:x,AuthDataTree:L},props:{tableType:{type:Number,default:1}},setup(e){const t=u(""),r=u(!1),d=u("field"),l=u("role"),a={fieldRef:u(),buttonRef:u(),dataRef:u(),roleRef:u(),userRef:u(),departRef:u()},m=u(""),p=V(()=>e.tableType==1||e.tableType==2),c=u(!0),[v,{closeModal:g}]=z(n=>{d.value="field",l.value="role",t.value=n.cgformId,b()});function h(n=d.value){var T;return(T=a[n+"Ref"])==null?void 0:T.value}function b(){return D(this,null,function*(){yield _(),R(),h().clear()})}function S(n){m.value=n,s(d.value),y()}function C(n){m.value=n,s(d.value),y()}function k(n){m.value=n,s(d.value),y()}function y(){l.value=="role"?(a.departRef.value.clearSelected(),a.userRef.value.clearSelected()):l.value=="depart"?(a.roleRef.value.clearSelected(),a.userRef.value.clearSelected()):l.value=="user"&&(a.departRef.value.clearSelected(),a.roleRef.value.clearSelected())}function R(){l.value=="role"?a.roleRef.value.clearSelected():l.value=="depart"?a.departRef.value.clearSelected():l.value=="user"&&a.userRef.value.clearSelected(),h().clearChecked(),m.value=""}function s(n){return D(this,null,function*(){yield _(),m.value&&h(n).loadChecked(m.value,l.value)})}function w(){R()}const K=n=>{c.value=n};return Z(Y({},a),{cgformId:t,loading:r,activeKey:d,hasDataAuth:p,authMode:l,onAuthModeChange:w,onAuthTypeChange:s,closeModal:g,onSelectRole:S,onSelectDepart:C,onSelectUser:k,registerModal:v,hanldeOpenChange:K,contentShow:c})}});function te(e,t,r,d,l,a){const m=f("LeftRole"),p=f("a-tab-pane"),c=f("LeftDepart"),v=f("LeftUser"),g=f("a-tabs"),h=f("a-col"),b=f("AuthFieldTree"),S=f("AuthButtonTree"),C=f("AuthDataTree"),k=f("a-row"),y=f("a-spin"),R=f("BasicModal");return A(),M(R,{title:"Online权限授权",width:900,maskClosable:!1,defaultFullscreen:"",okButtonProps:{style:{display:"none"}},cancelText:"关闭",onCancel:e.closeModal,onRegister:e.registerModal,onOpenChange:e.hanldeOpenChange},{default:i(()=>[o(y,{wrapperClassName:"authsetting-container",spinning:e.loading},{default:i(()=>[e.contentShow?(A(),M(k,{key:0},{default:i(()=>[o(h,{span:12},{default:i(()=>[o(g,{activeKey:e.authMode,"onUpdate:activeKey":t[0]||(t[0]=s=>e.authMode=s),onChange:e.onAuthModeChange},{default:i(()=>[o(p,{tab:"角色授权",key:"role",forceRender:""},{default:i(()=>[o(m,{ref:"roleRef",onSelect:e.onSelectRole},null,8,["onSelect"])]),_:1}),o(p,{tab:"部门授权",key:"depart",forceRender:""},{default:i(()=>[o(c,{ref:"departRef",onSelect:e.onSelectDepart},null,8,["onSelect"])]),_:1}),o(p,{tab:"人员授权",key:"user",forceRender:""},{default:i(()=>[o(v,{ref:"userRef",onSelect:e.onSelectUser},null,8,["onSelect"])]),_:1})]),_:1},8,["activeKey","onChange"])]),_:1}),o(h,{span:1}),o(h,{span:11},{default:i(()=>[o(g,{activeKey:e.activeKey,"onUpdate:activeKey":t[1]||(t[1]=s=>e.activeKey=s),onChange:e.onAuthTypeChange},{default:i(()=>[o(p,{tab:"字段权限",key:"field",forceRender:""},{default:i(()=>[o(b,{class:"authFieldTree",ref:"fieldRef",cgformId:e.cgformId},null,8,["cgformId"])]),_:1}),e.hasDataAuth?(A(),E($,{key:0},[o(p,{tab:"按钮权限",key:"button",forceRender:""},{default:i(()=>[o(S,{class:"authButtonTree",ref:"buttonRef",cgformId:e.cgformId},null,8,["cgformId"])]),_:1}),o(p,{tab:"数据权限",key:"data",forceRender:""},{default:i(()=>[o(C,{class:"authDataTree",ref:"dataRef",cgformId:e.cgformId},null,8,["cgformId"])]),_:1})],64)):I("",!0)]),_:1},8,["activeKey","onChange"])]),_:1})]),_:1})):I("",!0)]),_:1},8,["spinning"])]),_:1},8,["onCancel","onRegister","onOpenChange"])}const st=X(ee,[["render",te],["__scopeId","data-v-e2407ec1"]]);export{st as default};
