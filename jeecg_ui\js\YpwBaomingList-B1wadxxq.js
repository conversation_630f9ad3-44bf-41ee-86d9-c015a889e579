var J=Object.defineProperty,W=Object.defineProperties;var Z=Object.getOwnPropertyDescriptors;var I=Object.getOwnPropertySymbols;var $=Object.prototype.hasOwnProperty,tt=Object.prototype.propertyIsEnumerable;var T=(r,t,o)=>t in r?J(r,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[t]=o,A=(r,t)=>{for(var o in t||(t={}))$.call(t,o)&&T(r,o,t[o]);if(I)for(var o of I(t))tt.call(t,o)&&T(r,o,t[o]);return r},E=(r,t)=>W(r,Z(t));var k=(r,t,o)=>new Promise((v,g)=>{var u=p=>{try{c(o.next(p))}catch(d){g(d)}},S=p=>{try{c(o.throw(p))}catch(d){g(d)}},c=p=>p.done?v(p.value):Promise.resolve(p.value).then(u,S);c((o=o.apply(r,t)).next())});import{d as R,r as M,f as ot,ag as s,v as et,aq as nt,ar as l,k as a,aD as i,u as m,q as b,aB as f,ah as rt,G as _}from"./vue-vendor-dy9k-Yad.js";import"./index-BkGZ5fiW.js";import"./index-Diw57m_E.js";import{useListPage as it}from"./useListPage-Soxgnx9a.js";import pt from"./YpwBaomingModal-D6F3GJiT.js";import{s as at,b as mt,g as st,a as lt,c as ut,d as ct,l as dt,e as ft}from"./YpwBaoming.api-BixF3mjv.js";import{ah as _t,ad as gt,a as wt}from"./index-CCWaWN5g.js";import{Q as yt}from"./componentMap-Bkie1n3v.js";import bt from"./BasicTable-xCEZpGLb.js";import"./antd-vue-vendor-me9YkNVC.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./index-L3cSIXth.js";import"./BasicForm-DBcXiHk0.js";import"./useForm-CgkFTrrO.js";import"./JAreaLinkage-DFCdF3cr.js";import"./areaDataUtil-BXVjRArW.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JSelectDept-I-NqkbOH.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./JCodeEditor-B-WXz11X.js";import"./useFormItem-CHvpjy4o.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./depart.api-BoGnt_ZX.js";import"./JPopup-CeU6ry6r.js";import"./JAreaSelect-Db7Nhhc_.js";import"./EasyCronInput-BuvtO5dv.js";import"./JEllipsis-BsXuWNHJ.js";import"./JSearchSelect-c_lfTydU.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JUpload-CRos0F1P.js";import"./JUploadModal-C-iKhVFc.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./injectionKey-DPVn4AgL.js";const ht=R({name:"ypw-ypwBaoming"}),Ct=R(E(A({},ht),{setup(r){const t=M({}),o=ot([]),v=_t(),[g,{openModal:u}]=gt(),{prefixCls:S,tableContext:c,onExportXls:p,onImportXls:d}=it({tableProps:{title:"报名信息",api:dt,columns:ct,canResize:!1,formConfig:{schemas:ut,autoSubmitOnEnter:!0,showAdvancedButton:!0,fieldMapToNumber:[],fieldMapToTime:[]},actionColumn:{width:120,fixed:"right"},beforeFetch:e=>Object.assign(e,t)},exportConfig:{name:"报名信息",url:lt,params:t},importConfig:{url:st,success:w}}),[U,{reload:D},{rowSelection:q,selectedRowKeys:h}]=c,F=M(at);function j(e){Object.keys(e).map(n=>{t[n]=e[n]}),D()}function N(){u(!0,{isUpdate:!1,showFooter:!0})}function O(e){u(!0,{record:e,isUpdate:!0,showFooter:!0})}function Q(e){u(!0,{record:e,isUpdate:!0,showFooter:!1})}function K(e){return k(this,null,function*(){yield ft({id:e.id},w)})}function L(){return k(this,null,function*(){yield mt({ids:h.value},w)})}function w(){(h.value=[])&&D()}function P(e){return[{label:"编辑",onClick:O.bind(null,e),auth:"ypw:ypw_baoming:edit"}]}function V(e){return[{label:"详情",onClick:Q.bind(null,e)},{label:"删除",popConfirm:{title:"是否确认删除",confirm:K.bind(null,e),placement:"topLeft"},auth:"ypw:ypw_baoming:delete"}]}return(e,n)=>{const C=s("a-button"),X=s("j-upload-button"),B=s("Icon"),Y=s("a-menu-item"),z=s("a-menu"),G=s("a-dropdown"),H=s("super-query"),y=et("auth");return l(),nt("div",null,[a(m(bt),{onRegister:m(U),rowSelection:m(q)},{tableTitle:i(()=>[b((l(),f(C,{type:"primary",onClick:N,preIcon:"ant-design:plus-outlined"},{default:i(()=>n[0]||(n[0]=[_(" 新增")])),_:1,__:[0]})),[[y,"ypw:ypw_baoming:add"]]),b((l(),f(C,{type:"primary",preIcon:"ant-design:export-outlined",onClick:m(p)},{default:i(()=>n[1]||(n[1]=[_(" 导出")])),_:1,__:[1]},8,["onClick"])),[[y,"ypw:ypw_baoming:exportXls"]]),b((l(),f(X,{type:"primary",preIcon:"ant-design:import-outlined",onClick:m(d)},{default:i(()=>n[2]||(n[2]=[_("导入")])),_:1,__:[2]},8,["onClick"])),[[y,"ypw:ypw_baoming:importExcel"]]),m(h).length>0?(l(),f(G,{key:0},{overlay:i(()=>[a(z,null,{default:i(()=>[a(Y,{key:"1",onClick:L},{default:i(()=>[a(B,{icon:"ant-design:delete-outlined"}),n[3]||(n[3]=_(" 删除 "))]),_:1,__:[3]})]),_:1})]),default:i(()=>[b((l(),f(C,null,{default:i(()=>[n[4]||(n[4]=_("批量操作 ")),a(B,{icon:"mdi:chevron-down"})]),_:1,__:[4]})),[[y,"ypw:ypw_baoming:deleteBatch"]])]),_:1})):rt("",!0),a(H,{config:F,onSearch:j},null,8,["config"])]),action:i(({record:x})=>[a(m(yt),{actions:P(x),dropDownActions:V(x)},null,8,["actions","dropDownActions"])]),bodyCell:i(({column:x,record:xt,index:kt,text:vt})=>n[5]||(n[5]=[])),_:1},8,["onRegister","rowSelection"]),a(pt,{onRegister:m(g),onSuccess:w},null,8,["onRegister"])])}}})),Ro=wt(Ct,[["__scopeId","data-v-e5f871cf"]]);export{Ro as default};
