import{aN as g,ap as f,aq as m,ar as v,aT as _}from"./vue-vendor-dy9k-Yad.js";import{u as k,N as y,ah as S,a as T}from"./index-CCWaWN5g.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const I={name:"TokenLogin",setup(){const d=g();let e=f();const{createMessage:t,notification:s}=k(),{t:i}=y(),o=d.query;o||t.warning("参数无效");const c=o.loginToken;c||t.warning("token无效"),S().ThirdLogin({token:c,thirdType:"email",goHome:!1}).then(a=>{a&&a.userInfo?u(a):l(a)});function l(a){s.error({message:"登录失败",description:((a.response||{}).data||{}).message||a.message||"请求出现错误，请稍后再试",duration:4})}function u(a){let p=o.info;if(p){let n=JSON.parse(p),r="";n.isLowApp===1?r="/myapps/personalOffice/myTodo":r="/task/handle/"+n.taskId,e.replace({path:r,query:n}),s.success({message:i("sys.login.loginSuccessTitle"),description:`${i("sys.login.loginSuccessDesc")}: ${a.userInfo.realname}`,duration:3})}else s.error({message:"参数失效",description:"页面跳转参数丢失，请查看日志",duration:4})}}},h="/resource/img/logo.png",$={class:"app-loading"};function q(d,e,t,s,i,o){return v(),m("div",$,e[0]||(e[0]=[_('<div class="app-loading-wrap" data-v-0d7a6c75><img src="'+h+'" class="app-loading-logo" alt="Logo" data-v-0d7a6c75><div class="app-loading-dots" data-v-0d7a6c75><span class="dot dot-spin" data-v-0d7a6c75><i data-v-0d7a6c75></i><i data-v-0d7a6c75></i><i data-v-0d7a6c75></i><i data-v-0d7a6c75></i></span></div><div class="app-loading-title" data-v-0d7a6c75>JeecgBoot 企业级低代码平台</div></div>',1)]))}const M=T(I,[["render",q],["__scopeId","data-v-0d7a6c75"]]);export{M as default};
