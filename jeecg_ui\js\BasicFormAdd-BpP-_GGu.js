var b=(f,n,p)=>new Promise((e,i)=>{var o=t=>{try{m(p.next(t))}catch(r){i(r)}},s=t=>{try{m(p.throw(t))}catch(r){i(r)}},m=t=>t.done?e(t.value):Promise.resolve(t.value).then(o,s);m((p=p.apply(f,n)).next())});import{d as B,f as h,ag as y,aB as u,ar as c,aD as d,ah as v,G as x,u as F}from"./vue-vendor-dy9k-Yad.js";import"./index-L3cSIXth.js";import{u as C}from"./useForm-CgkFTrrO.js";import{B as I}from"./BasicForm-DBcXiHk0.js";import{a as k}from"./index-CCWaWN5g.js";import"./componentMap-Bkie1n3v.js";import"./antd-vue-vendor-me9YkNVC.js";import"./useFormItem-CHvpjy4o.js";import"./index-Diw57m_E.js";import"./BasicModal-BLFvpBuk.js";import"./ModalHeader-BJG9dHtK.js";import"./useTimeout-CeTdFD_D.js";import"./vxe-table-vendor-B22HppNm.js";import"./index-CImCetrx.js";import"./useWindowSizeFn-DDbrQbks.js";import"./index-LCGLvkB3.js";import"./index-De_W6s5g.js";import"./index-D6l0IxOU.js";import"./useIntersectionObserver-C4LVxQJW.js";import"./CustomModal-BakuIxQv.js";import"./download-CZ-9H9a3.js";import"./base64Conver-24EVOS6V.js";import"./index-CBCjSSNZ.js";import"./index-DFrpKMGa.js";import"./useCountdown-CCWNeb_r.js";import"./useFormItemSingle-Cw668yj5.js";import"./JSelectUser-COkExGbu.js";import"./props-CCT78mKr.js";import"./JSelectBiz-jOYRdMJf.js";import"./JAddInput-CxJ-JBK-.js";import"./index-QxsVJqiT.js";import"./index-BtIdS_Qz.js";import"./bem-sRx7x0Ii.js";import"./props-qAqCef5R.js";import"./useContextMenu-BU2ycxls.js";import"./depart.api-BoGnt_ZX.js";import"./JSelectDept-I-NqkbOH.js";import"./JAreaSelect-Db7Nhhc_.js";import"./areaDataUtil-BXVjRArW.js";import"./JEditorTiptap-BwAoWsi9.js";import"./index-ByPySmGo.js";import"./JPopup-CeU6ry6r.js";import"./JEllipsis-BsXuWNHJ.js";import"./JUpload-CRos0F1P.js";import"./JSearchSelect-c_lfTydU.js";import"./index-CXHeQyuE.js";import"./index-Dyko68ZT.js";import"./JAreaLinkage-DFCdF3cr.js";import"./JCodeEditor-B-WXz11X.js";import"./htmlmixed-CmvhkW5V.js";import"./vue-CAKGUkuE.js";/* empty css             */import"./EasyCronInput-BuvtO5dv.js";import"./JUploadModal-C-iKhVFc.js";const w=B({__name:"BasicFormAdd",setup(f){const n=[{field:"name1",label:"姓名1",component:"Input",colProps:{span:8}},{field:"age1",label:"年龄1",component:"InputNumber",colProps:{span:8}},{field:"0",component:"Input",label:" ",colProps:{span:8},slot:"addForm"}],[p,{appendSchemaByField:e,removeSchemaByFiled:i}]=C({schemas:n,showResetButton:!1,labelWidth:"150px",submitButtonOptions:{text:"提交",preIcon:""},actionColOptions:{span:17}});let o=h(2);function s(){return b(this,null,function*(){yield e({field:`name${o.value}`,component:"Input",label:"字段"+o.value,colProps:{span:8}},""),yield e({field:`sex${o.value}`,component:"InputNumber",label:"字段"+o.value,colProps:{span:8}},""),yield e({field:`${o.value}`,component:"Input",label:" ",colProps:{span:8},slot:"addForm"},""),o.value++})}function m(r){i([`name${r}`,`sex${r}`,`${r}`]),o.value--}function t(r){}return(r,a)=>{const _=y("a-button");return c(),u(F(I),{onRegister:F(p),style:{"margin-top":"20px"},onSubmit:t},{addForm:d(({field:l})=>[Number(l)===0?(c(),u(_,{key:0,onClick:s,style:{width:"50px"}},{default:d(()=>a[0]||(a[0]=[x("+")])),_:1,__:[0]})):v("",!0),Number(l)>0?(c(),u(_,{key:1,onClick:$=>m(l),style:{width:"50px"}},{default:d(()=>a[1]||(a[1]=[x("-")])),_:2,__:[1]},1032,["onClick"])):v("",!0)]),_:1},8,["onRegister"])}}}),Po=k(w,[["__scopeId","data-v-d25c53ae"]]);export{Po as default};
