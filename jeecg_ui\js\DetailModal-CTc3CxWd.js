var D=(A,k,o)=>new Promise((T,b)=>{var g=c=>{try{h(o.next(c))}catch(_){b(_)}},e=c=>{try{h(o.throw(c))}catch(_){b(_)}},h=c=>c.done?T(c.value):Promise.resolve(c.value).then(g,e);h((o=o.apply(A,k)).next())});import{d as G,e as X,f as V,w as H,ag as P,v as J,aB as m,ar as l,aD as a,q as K,ah as y,aq as d,at as p,F as L,k as s,G as i,au as n,u as v,aC as x}from"./vue-vendor-dy9k-Yad.js";import{u as O,aX as S,a as Q}from"./index-CCWaWN5g.js";import{g as W,a as Y}from"./certificationAudit-CpD5ybP4.js";import{C as Z}from"./CustomModal-BakuIxQv.js";import{an as N,i as U}from"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";const $={key:0,class:"detail-content"},ee={class:"info-section"},te={key:0,class:"image-preview"},ae={key:1},re={class:"info-section"},se={key:0,class:"image-preview"},le={key:1},ne={key:1,class:"info-section"},oe={key:0,class:"image-preview"},ie={key:1},ue={class:"audit-section"},de={class:"notes-content"},ce=G({__name:"DetailModal",props:{open:{type:Boolean},record:{}},emits:["update:open"],setup(A,{emit:k}){const o=A,T=k,{createMessage:b}=O(),g=X({get:()=>o.open,set:t=>T("update:open",t)}),e=V(null),h=V(!1);H(()=>o.open,t=>D(null,null,function*(){t&&o.record&&(yield c())}));function c(){return D(this,null,function*(){if(o.record)try{h.value=!0;let t;if(o.record.authType==="个人认证")t=yield W(o.record.id);else if(o.record.authType==="企业认证")t=yield Y(o.record.id);else{b.error("未知的认证类型");return}e.value=t}catch(t){}finally{h.value=!1}})}function _(){g.value=!1,e.value=null}function q(t){switch(t){case 1:return"orange";case 2:return"green";case 3:return"red";default:return"default"}}function F(t){switch(t){case 1:return"未审核";case 2:return"已通过";case 3:return"未通过";default:return"-"}}function B(t){switch(t){case 1:return"身份证";case 2:return"其他";default:return"-"}}function M(){var t,u,r,f;return e.value?((t=o.record)==null?void 0:t.authType)==="企业认证"?((u=e.value.hgyEnterpriseAuth)==null?void 0:u.notes)||"":((r=o.record)==null?void 0:r.authType)==="个人认证"&&((f=e.value.hgyPersonalAuth)==null?void 0:f.notes)||"":""}return(t,u)=>{const r=P("a-descriptions-item"),f=P("a-descriptions"),R=P("a-tag"),j=J("loading");return l(),m(Z,{open:g.value,"onUpdate:open":u[0]||(u[0]=w=>g.value=w),title:"认证详情",width:"800px","show-footer":!0,"show-cancel-button":!1,"show-confirm-button":!0,"confirm-text":"关闭",onConfirm:_},{default:a(()=>{var w,z,I;return[t.record?K((l(),d("div",$,[t.record.authType==="企业认证"?(l(),d(L,{key:0},[p("div",ee,[u[1]||(u[1]=p("span",{class:"section-title"},"企业资料",-1)),(w=e.value)!=null&&w.hgyEnterpriseAuth?(l(),m(f,{key:0,column:2,bordered:"",size:"small"},{default:a(()=>[s(r,{label:"企业名称"},{default:a(()=>[i(n(e.value.hgyEnterpriseAuth.enterpriseName||"-"),1)]),_:1}),s(r,{label:"统一社会信用代码"},{default:a(()=>[i(n(e.value.hgyEnterpriseAuth.creditCode||"-"),1)]),_:1}),s(r,{label:"业务联系人"},{default:a(()=>[i(n(e.value.hgyEnterpriseAuth.relationUser||"-"),1)]),_:1}),s(r,{label:"联系电话"},{default:a(()=>[i(n(e.value.hgyEnterpriseAuth.relationPhone||"-"),1)]),_:1}),s(r,{label:"营业执照照片",span:2},{default:a(()=>[e.value.hgyEnterpriseAuth.companyLogo?(l(),d("div",te,[s(v(N),{src:e.value.hgyEnterpriseAuth.companyLogo,width:200,preview:!0},null,8,["src"])])):(l(),d("span",ae,"-"))]),_:1})]),_:1})):y("",!0)]),p("div",re,[u[2]||(u[2]=p("span",{class:"section-title"},"法人信息",-1)),(z=e.value)!=null&&z.hgyEnterpriseAuth?(l(),m(f,{key:0,column:2,bordered:"",size:"small"},{default:a(()=>[s(r,{label:"法人真实姓名"},{default:a(()=>[i(n(e.value.hgyEnterpriseAuth.legalName||"-"),1)]),_:1}),s(r,{label:"法人证件类型"},{default:a(()=>[i(n(B(e.value.hgyEnterpriseAuth.cartType)),1)]),_:1}),s(r,{label:"法人证件号",span:2},{default:a(()=>[i(n(e.value.hgyEnterpriseAuth.cartId||"-"),1)]),_:1}),s(r,{label:"身份证正反面照片",span:2},{default:a(()=>[e.value.hgyAttachmentList&&e.value.hgyAttachmentList.length>0?(l(),d("div",se,[(l(!0),d(L,null,x(e.value.hgyAttachmentList,(E,C)=>(l(),m(v(N),{key:C,src:E.filePath,width:150,preview:!0,style:{"margin-right":"10px"}},null,8,["src"]))),128))])):(l(),d("span",le,"-"))]),_:1})]),_:1})):y("",!0)])],64)):t.record.authType==="个人认证"?(l(),d("div",ne,[u[3]||(u[3]=p("span",{class:"section-title"},"个人资料",-1)),(I=e.value)!=null&&I.hgyPersonalAuth?(l(),m(f,{key:0,column:2,bordered:"",size:"small"},{default:a(()=>[s(r,{label:"真实姓名"},{default:a(()=>[i(n(e.value.hgyPersonalAuth.name||"-"),1)]),_:1}),s(r,{label:"手机号"},{default:a(()=>[i(n(e.value.hgyPersonalAuth.phone||"-"),1)]),_:1}),s(r,{label:"证件类型"},{default:a(()=>[i(n(B(e.value.hgyPersonalAuth.cartType)),1)]),_:1}),s(r,{label:"证件号"},{default:a(()=>[i(n(e.value.hgyPersonalAuth.cartId||"-"),1)]),_:1}),s(r,{label:"身份证正反面照片",span:2},{default:a(()=>[e.value.hgyAttachmentList&&e.value.hgyAttachmentList.length>0?(l(),d("div",oe,[(l(!0),d(L,null,x(e.value.hgyAttachmentList,(E,C)=>(l(),m(v(N),{key:C,src:E.filePath,width:150,preview:!0,style:{"margin-right":"10px"}},null,8,["src"]))),128))])):(l(),d("span",ie,"-"))]),_:1})]),_:1})):y("",!0)])):y("",!0),p("div",ue,[u[4]||(u[4]=p("span",{class:"section-title"},"审核信息",-1)),s(f,{column:2,bordered:"",size:"small"},{default:a(()=>[s(r,{label:"审核状态"},{default:a(()=>[s(R,{color:q(t.record.review)},{default:a(()=>[i(n(F(t.record.review)),1)]),_:1},8,["color"])]),_:1}),s(r,{label:"审核人"},{default:a(()=>[i(n(t.record.reviewUser||"-"),1)]),_:1}),s(r,{label:"审核时间"},{default:a(()=>[i(n(v(S)(v(U)(t.record.reviewTime))||"-"),1)]),_:1}),s(r,{label:"提交时间"},{default:a(()=>[i(n(v(S)(v(U)(t.record.submitTime))||"-"),1)]),_:1}),M()?(l(),m(r,{key:0,label:"审核说明",span:2},{default:a(()=>[p("div",de,n(M()),1)]),_:1})):y("",!0)]),_:1})])])),[[j,h.value]]):y("",!0)]}),_:1},8,["open"])}}}),_e=Q(ce,[["__scopeId","data-v-b8bcdfbe"]]);export{_e as default};
