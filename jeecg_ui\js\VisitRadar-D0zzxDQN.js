import{d as r,f as i,w as n,aB as l,ar as s,u as d,aD as m,at as f,aA as c}from"./vue-vendor-dy9k-Yad.js";import{J as h}from"./antd-vue-vendor-me9YkNVC.js";import{useECharts as p}from"./useECharts-BU6FzBZi.js";import"./useTimeout-CeTdFD_D.js";import"./index-CCWaWN5g.js";import"./vxe-table-vendor-B22HppNm.js";import"./echarts-D8q0NfgS.js";import"./renderers-CGMjx3X9.js";const v=r({__name:"VisitRadar",props:{loading:Boolean,width:{type:String,default:"100%"},height:{type:String,default:"300px"}},setup(a){const e=a,t=i(null),{setOptions:o}=p(t);return n(()=>e.loading,()=>{e.loading||o({legend:{bottom:0,data:["访问","购买"]},tooltip:{},radar:{radius:"60%",splitNumber:8,indicator:[{name:"电脑"},{name:"充电器"},{name:"耳机"},{name:"手机"},{name:"Ipad"},{name:"耳机"}]},series:[{type:"radar",symbolSize:0,areaStyle:{shadowBlur:0,shadowColor:"rgba(0,0,0,.2)",shadowOffsetX:0,shadowOffsetY:10,opacity:1},data:[{value:[90,50,86,40,50,20],name:"访问",itemStyle:{color:"#b6a2de"}},{value:[70,75,70,76,20,85],name:"购买",itemStyle:{color:"#5ab1ef"}}]}]})},{immediate:!0}),(u,g)=>(s(),l(d(h),{title:"转化率",loading:a.loading},{default:m(()=>[f("div",{ref_key:"chartRef",ref:t,style:c({width:a.width,height:a.height})},null,4)]),_:1},8,["loading"]))}});export{v as default};
