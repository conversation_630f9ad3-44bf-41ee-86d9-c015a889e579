var y=(d,s,a)=>new Promise((i,m)=>{var f=t=>{try{n(a.next(t))}catch(l){m(l)}},r=t=>{try{n(a.throw(t))}catch(l){m(l)}},n=t=>t.done?i(t.value):Promise.resolve(t.value).then(f,r);n((a=a.apply(d,s)).next())});import{j as T}from"./index-CCWaWN5g.js";import{f as F,u as g}from"./vue-vendor-dy9k-Yad.js";import{validateFormModelAndTables as D,VALIDATE_FAILED as j}from"./vxeUtils-B1NxCh07.js";import"./antd-vue-vendor-me9YkNVC.js";import"./vxe-table-vendor-B22HppNm.js";function k(d,s,a,i,m,f){const r=F();function n(e,o,c,p){c.loading=!0,T.get({url:e,params:o},{isTransformResponse:!1}).then(A=>{let{result:v}=A;A.success&&v&&(Array.isArray(v)?c.dataSource=v:Array.isArray(v.records)&&(c.dataSource=v.records)),typeof p=="function"&&p(A)}).finally(()=>{c.loading=!1})}function t(e){var o,c;(c=(o=a[e])==null?void 0:o.value)==null||c.resetScrollTop(0)}function l(){let e=Object.values(a);return Promise.all(e)}function h(){l().then(e=>{let o=r.value.getFieldsValue();return D(r.value.validate,o,e,r.value.getProps,!1)}).then(e=>f&&typeof f=="function"?f(e):u(e)).then(e=>{if(typeof s!="function")throw b("classifyIntoFormData");let o=s(e);return d(o)}).catch(e=>{if(e.error===j){if(e.paneKey?i.value=e.paneKey:i.value=e.subIndex==null?e.index==null?g(i):m.value[e.index]:Object.keys(a)[e.subIndex],e!=null&&e.errorFields){const o=e.errorFields[0];o&&r.value.scrollToField(o.name,{behavior:"smooth",block:"end"})}return Promise.reject(e==null?void 0:e.errorFields)}})}function u(e){return new Promise(o=>{o(e)})}function b(e){return`${e} 未定义或不是一个函数`}function w(e){return`${e} 未定义或不是一个数组`}return[t,h,n,r]}function E(d,s){function a(){return y(this,null,function*(){let r={},n=Object.keys(s),t="";for(let l=0;l<n.length;l++){t=n[l];let h=s[t].value;if(h.isForm){let u=yield f(h,t);u&&(r[t+"List"]=[u])}else{let u=yield m(h,t);u&&u.length>0&&(r[t+"List"]=u)}}return r})}function i(r){return r&&Object.keys(r).map(n=>{r[n]instanceof Array&&(r[n]=r[n].join(","))}),r}function m(r,n){return y(this,null,function*(){return(yield r.validateTable())?(d.value=n,r.resetScrollTop(0),Promise.reject(1)):r.getTableData()})}function f(r,n){return y(this,null,function*(){try{let t=yield r.getFormData();return i(t),t}catch(t){return d.value=n,Promise.reject(t)}})}return{getSubFormAndTableData:a,transformData:i}}export{k as useJvxeMethod,E as useValidateAntFormAndTable};
